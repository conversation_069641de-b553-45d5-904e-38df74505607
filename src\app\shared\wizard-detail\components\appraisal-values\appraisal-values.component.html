<h4 class="section-heading">{{'UBZ.SITE_CONTENT.100000100011'| translate }}</h4>
<table class="uc-table">
  <thead>
    <tr>
      <th scope="col" style="text-align: center">{{'UBZ.SITE_CONTENT.100000011101' | translate }} (€)</th>
      <th scope="col" style="text-align: center">{{'UBZ.SITE_CONTENT.100000011110'| translate }} (€)</th>
      <th scope="col" style="text-align: center">{{'UBZ.SITE_CONTENT.100000011111'| translate }} (€)</th>
      <th scope="col" style="text-align: center">{{'UBZ.SITE_CONTENT.100000100000'| translate }} (€)</th>
      <th scope="col" style="text-align: center">{{'UBZ.SITE_CONTENT.100000100001'| translate }} (€)</th>
      <th scope="col" style="text-align: center">{{'UBZ.SITE_CONTENT.100000100010'| translate }} (€)</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td style="text-align: center">{{dataValue.bookValue === null ? '-' : (dataValue.bookValue
        |currency:'EUR':true:'1.2-2')}}</td>
      <td style="text-align: center">{{dataValue.pledgeValue === null ? '-' : (dataValue.pledgeValue
        |currency:'EUR':true:'1.2-2')}}</td>
      <td style="text-align: center">{{dataValue.marketValue === null ? '-' : (dataValue.marketValue
        |currency:'EUR':true:'1.2-2')}}</td>
      <td style="text-align: center">{{dataValue.insuranceValue === null ? '-' : (dataValue.insuranceValue
        |currency:'EUR':true:'1.2-2')}}</td>
      <td style="text-align: center">{{dataValue.actualValue === null ? '-' : (dataValue.actualValue
        |currency:'EUR':true:'1.2-2')}}</td>
      <td style="text-align: center">{{dataValue.propertyValue === null ? '-' : (dataValue.propertyValue
        |currency:'EUR':true:'1.2-2')}}</td>
    </tr>
  </tbody>
</table>