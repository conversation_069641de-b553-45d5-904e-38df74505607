<div *ngIf="isOpen" class="modal fade" id="file-associati" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #fileAssociati="bs-modal" (onHidden)="closeModal(false)" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.11000000' | translate }}</h2>
        <button type="button" class="close pull-right" aria-label="Close" (click)="closeModal(false)">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <h4>{{'UBZ.SITE_CONTENT.11000001' | translate }}</h4> {{'UBZ.SITE_CONTENT.11000010' | translate }}
            <div class="input-group">
              <input type="text" class="form-control" readonly [value]="fileName">
              <label class="input-group-btn">
                <span class="btn btn-primary">
                  {{'UBZ.SITE_CONTENT.11000011' | translate }}&hellip; <input #fileToUpload type="file" style="display: none;" multiple (change)="setFile()">
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
          <button type="button" class="btn btn-primary waves-effect" data-dismiss="modal" (click)="uploadFile()">{{'UBZ.SITE_CONTENT.11000100' | translate }}</button>
      </div>
    </div>
  </div>
</div>
