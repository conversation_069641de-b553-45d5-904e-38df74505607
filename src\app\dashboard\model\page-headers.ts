export class PageHeaders {
  ALL = [
    new Head<PERSON>ield('UBZ.SITE_CONTENT.1101100', 'id', null),
    new Head<PERSON>ield('UBZ.SITE_CONTENT.1001110', 'ndg', null),
    new Head<PERSON>ield('UBZ.SITE_CONTENT.1101101', 'heading', null),
    new Head<PERSON>ield('UBZ.SITE_CONTENT.10000001', 'phase', null),
    new Head<PERSON><PERSON>('UBZ.SITE_CONTENT.1111100', 'inChargeUser', null),
    new Head<PERSON>ield('UBZ.SITE_CONTENT.1111110', 'insertDate', null),
    new HeadField('UBZ.SITE_CONTENT.10111110', 'creationBranch', null)
  ];
  REQ = [
    new HeadField('UBZ.SITE_CONTENT.1101100', 'id', null),
    new HeadField('UBZ.SITE_CONTENT.1001110', 'ndg', null),
    new Head<PERSON>ield('UBZ.SITE_CONTENT.1101101', 'heading', null),
    new Head<PERSON><PERSON>('UBZ.SITE_CONTENT.10000001', 'phase', null),
    new Head<PERSON>ield(
      'UBZ.SITE_CONTENT.1111100',
      'inChargeUser',
      null,
      "this.searchTypes === 'STRUCT'"
    ),
    new HeadField('UBZ.SITE_CONTENT.1111110', 'insertDate', null),
    new HeadField('UBZ.SITE_CONTENT.1111101', 'creationBranch', null),
    new HeadField('UBZ.SITE_CONTENT.1110110001', 'conclusionChangeDate' , null , 'this.selectedCounterId === \'200RIC-CON\''),
    new HeadField('UBZ.SITE_CONTENT.1110110010', 'annulmentChangeDate' , null , 'this.selectedCounterId === \'200RIC-ANN\'')
  ];
  SIM = [
    new HeadField('UBZ.SITE_CONTENT.10010', 'id', null),
    new HeadField('UBZ.SITE_CONTENT.1001110', 'ndg', null),
    new HeadField('UBZ.SITE_CONTENT.1101101', 'heading', null),
    new HeadField(
      'UBZ.SITE_CONTENT.1111100',
      'inChargeUser',
      null,
      "this.searchTypes === 'STRUCT'"
    ),
    new HeadField('UBZ.SITE_CONTENT.1111110', 'insertDate', null),
    new HeadField('UBZ.SITE_CONTENT.1111101', 'creationBranch', null)
  ];
  LAA = [
    new HeadField('UBZ.SITE_CONTENT.10100101', '', null),
    new HeadField('UBZ.SITE_CONTENT.10010001', 'appraisalId', null),
    new HeadField('UBZ.SITE_CONTENT.1001110', '', null), // non ordinabile causa umf
    new HeadField('UBZ.SITE_CONTENT.1101101', '', null), // non ordinabile causa umf
    new HeadField('UBZ.SITE_CONTENT.10011', '', null), // non ordinabile causa umf
    new HeadField(
      'UBZ.SITE_CONTENT.1111100',
      'inChargeUser',
      null,
      "this.searchTypes === 'STRUCT'"
    ),
    new HeadField('UBZ.SITE_CONTENT.1111110', 'insertDate', null),
    new HeadField('UBZ.SITE_CONTENT.10111110', '', null) // non ordinabile causa umf
  ];
  LAPS = [
    new HeadField('UBZ.SITE_CONTENT.10100101', 'id', null),
    new HeadField('UBZ.SITE_CONTENT.1001110', 'ndg', null),
    new HeadField('UBZ.SITE_CONTENT.1101101', 'heading', null),
    new HeadField('UBZ.SITE_CONTENT.10011', 'phase', null),
    new HeadField('UBZ.SITE_CONTENT.10111101', 'task', null),
    new HeadField(
      'UBZ.SITE_CONTENT.1111100',
      'inChargeUser',
      null,
      "this.searchTypes === 'STRUCT'"
    ),
    new HeadField('UBZ.SITE_CONTENT.1111110', 'insertDate', null),
    new HeadField('UBZ.SITE_CONTENT.1111101', 'creationBranch', null)
  ];
  // 'LASC' = [];
  // 'LASS' = [];
  // 'PCM' = [];
  // 'WAM' = [];
}

class HeadField {
  label: string;
  orderBy: string;
  orderDesc: boolean;
  condition: string;

  public constructor(
    label: string,
    orderBy: string,
    orderDesc: boolean,
    condition?: string
  ) {
    this.label = label;
    this.orderBy = orderBy;
    this.orderDesc = orderDesc;
    if (condition) {
      this.condition = condition;
    } else {
      this.condition = 'true';
    }
  }
}
