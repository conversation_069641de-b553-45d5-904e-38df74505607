import { Injectable } from '@angular/core';
import { Http } from '@angular/http';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class QteService {
  constructor(private _http: Http) {}

  public getData(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = '/UBZ-ESA-RS/service/qtefinplan/' + positionId;
    return this._http.get(url).map(res => res.json());
  }

  public saveData(positionId: string, toBeSaved: any): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const objToSave = JSON.parse(JSON.stringify(toBeSaved));
    objToSave.qteDate = Date.parse(objToSave.qteDate);
    objToSave.qteUpdateDate = Date.parse(objToSave.qteUpdateDate);
    objToSave.totalCostUpdateDate = Date.parse(objToSave.totalCostUpdateDate);
    const url = '/UBZ-ESA-RS/service/qtefinplan/' + positionId;
    return this._http.post(url, objToSave).map(res => res.text());
  }
}
