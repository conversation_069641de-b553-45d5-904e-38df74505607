import {
  Component,
  OnChanges,
  Input,
  <PERSON><PERSON><PERSON>roy,
  SimpleChanges
} from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { Subscription } from 'rxjs/Subscription';
import { InsurancePolicy } from '../../../../model/registry.models';
import { RegistryService } from '../../../../service/registry.service';

@Component({
  selector: 'app-insurance-policy',
  templateUrl: './insurance-policy.component.html',
  styleUrls: ['./insurance-policy.component.css']
})
export class InsurancePolicyComponent implements OnChanges, OnDestroy {
  @Input() idAnag: string;
  @Input() subjectType: string;
  public insuranceList: any[] = [];
  public modifyMod = false;
  public anInsuranceExists: boolean;
  private _subscriptions: Subscription[] = [];

  constructor(private _registryService: RegistryService) { }

  ngOnChanges(changes: SimpleChanges) {
    if (changes && this.idAnag) {
      this._subscriptions[0] = this.refreshInsuranceList().subscribe(() => { });
    }
  }

  ngOnDestroy() {
    for (const subscription of this._subscriptions) {
      if (subscription && !subscription.closed) {
        subscription.unsubscribe();
      }
    }
  }

  private refreshInsuranceList(): Observable<any> {
    return this._registryService
      .getInsurancePolicyList(this.idAnag, this.subjectType)
      .switchMap(res => {
        this.insuranceList = res;
        for (const item of this.insuranceList) {
          item.endPolicy = new Date(item.endPolicy);
        }
        this.checkInsurance();
        return Observable.of(true);
      });
  }

  private checkInsurance() {
    if (this.insuranceList.length > 0) {
      this.anInsuranceExists = true;
    } else {
      this.anInsuranceExists = false;
    }
  }

  public abortAdding(event: any): void {
    this.stopPropagation(event);
    this.modifyMod = false;
  }

  public saveAddedData(event: any): void {
    this.stopPropagation(event);
    const toSave = JSON.parse(JSON.stringify(this.insuranceList));
    for (const item of toSave) {
      item.endPolicy = Date.parse(item.endPolicy);
    }
    this._subscriptions[1] = this._registryService
      .saveInsurancePolicyList(this.idAnag, toSave, this.subjectType)
      .subscribe(() => {
        this.modifyMod = false;
      });
  }

  startModify(event: any) {
    this.stopPropagation(event);
    this.refreshInsuranceList().subscribe(() => {
      if (!this.anInsuranceExists) {
        this.createNewInsurance();
      }
      this.modifyMod = true;
    });
  }

  public createNewInsurance(): void {
    this.insuranceList.push(new InsurancePolicy());
    this.anInsuranceExists = true;
  }

  private stopPropagation(event: any): void {
    event.stopPropagation();
  }
}
