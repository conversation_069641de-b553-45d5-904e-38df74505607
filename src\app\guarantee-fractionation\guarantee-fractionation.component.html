<app-position-header
  *ngIf="guaranteeFractionationService.frazLoaded"
  [staticHeaderArray]="guaranteeFractionationService.staticHeaderArray" 
  [lockingUser]="guaranteeFractionationService.lockingUser"
  [familyAsset]="guaranteeFractionationService.familyAsset"  
  (positionLockedFrg)="guaranteeFractionationService.positionLockedFrg()"
  >
</app-position-header>

<div class="row section-headline">
  <h1>{{'UBZ.SITE_CONTENT.1101110000' | translate}} {{'UBZ.SITE_CONTENT.10100100' | translate}}</h1>
</div>

<app-guarantee-fractionation-wizard 
  [ndg]="guaranteeFractionationService.ndg"
  [familyAsset]="guaranteeFractionationService.familyAsset" 
  [stepNum]="guaranteeFractionationService.wizardStep"
  [stepActive]="guaranteeFractionationService.wizardStepActive">
</app-guarantee-fractionation-wizard>

<router-outlet></router-outlet>