import { Component, OnInit, Inject } from "@angular/core";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Params, Router } from "@angular/router";
import { Observable } from "rxjs/Observable";
import * as FileSaver from "file-saver";
import { DomainService } from "../../../shared/domain/domain.service";
import { Domain } from "../../../shared/domain/domain";
import { GenericInfoService } from "../../services/generic-info/generic-info.service";
import { Simulation } from "../../model/simulation";
import { LandingService } from "../../services/landing/landing.service";
import { MenuService } from "../../../shared/menu/services/menu.service";
import { WizardService } from "../../../shared/wizard/services/wizard.service";
import { APP_CONSTANTS, IAppConstants } from "../../../app.constants";
import { PositionService } from "../../../shared/position/position.service";
import { WizardDetailService } from "../../../wizard-detail/services/wizard-detail.service";

@Component({
  selector: "app-summary",
  templateUrl: "./summary.component.html",
  styleUrls: ["./summary.component.css"],
})
export class SummaryComponent implements OnInit {
  wizardCode: string;
  positionId: string;
  simulation = new Simulation();
  documents: any[] = [];
  currentTask: string;
  domainStructureType: any = {};
  domainScopeType: any = {};
  domainAppraisalType: any = {};
  domainMacroProcess: any = {};
  domainAssetType: any = {};
  saveIsEnable = false;
  accessPoint: any;
  appraisalOwner: any;
  posSegment: any;
  macroProcess: any;
  forcingOwner: any;
  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private domainService: DomainService,
    private genericInfoService: GenericInfoService,
    public landingService: LandingService,
    public menuService: MenuService,
    private wizardService: WizardService,
    private wizardDetailService: WizardDetailService,
    private service: PositionService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) {}

  ngOnInit() {
    this.retrieveData();
  }

  retrieveData() {
    this.activatedRoute.parent.params
      .switchMap((params: Params) => {
        this.wizardCode = params["wizardCode"];
        this.positionId = params["positionId"];
        this.currentTask =
          this.wizardCode === "WSIM" ? "UBZ-SIM-SUM" : "UBZ-REQ-SUM";
        return this.genericInfoService.getGenericInfo(
          params["positionId"],
          this.wizardCode
        );
      })
      .switchMap((res) => {
        this.accessPoint = res.accessPoint;
        this.simulation = res;
       this.forcingOwner = res.forcingOwner;
// Imposta originationProcess nel landingService e scatena il metodo di controllo task
        this.landingService.originationProcess =
          this.simulation.originationProcess;
        this.landingService.checkIfCompletedTask(
          this.positionId,
          this.currentTask
        );
        return Observable.forkJoin(
          this.domainService.getSingleDomainItem(
            "UBZ_DOM_STRUCTURE_TYPE",
            this.simulation.appraisalStruct
          ),
          this.domainService.getSingleDomainItem(
            "UBZ_DOM_SCOPE_TYPE",
            this.simulation.appraisalScope
          ),
          this.domainService.getSingleDomainItem(
            "UBZ_DOM_APPRAISAL_TYPE",
            this.simulation.appraisalType
          ),
          this.domainService.getSingleDomainItem(
            "UBZ_DOM_MACRO_PROCESS",
            this.simulation.macroProcess
          ),
          this.domainService.getSingleDomainItem(
            "UBZ_DOM_RESITEM_TYPE",
            this.simulation.assetType
          ),
          this.genericInfoService.retrieveSummaryDocuments(
            this.simulation.positionId
          ),
          this.wizardDetailService.getRequestData(
            this.positionId,
            this.wizardCode
          ),
          this.service.getPositionDetail(this.positionId)
        );
      })
      .subscribe((res) => {
        this.domainStructureType = res[0];
        this.domainScopeType = res[1];
        this.domainAppraisalType = res[2];
        this.domainMacroProcess = res[3];
        this.domainAssetType = res[4];
        this.documents = res[5];
        this.appraisalOwner = res[6].appraisalOwner;
        this.macroProcess = res[6].macroProcess;
        this.posSegment = res[7].posSegment;
       
      });
  }

  confirm() {
    this.landingService.goNextPage(
      this.positionId,
      this.currentTask,
      this.wizardCode,
      this.activatedRoute
    );
  }

  exit() {
    this.router.navigate(["/"]);
  }

  previous() {
    this.landingService.goToPreviousTask(
      this.positionId,
      this.wizardCode,
      this.activatedRoute
    );
  }

  downloadPdf(docType: string) {
    this.genericInfoService
      .printDocument(this.simulation.positionId, docType)
      .subscribe((res) => FileSaver.saveAs(res, "document.pdf"));
  }

  openPdf(docType: string) {
    const url = this.genericInfoService.getPrintServiceUrl(
      this.simulation.positionId,
      docType
    );
    window.open(url);
  }

  cancelPosition() {
    this.landingService
      .cancelPosition(this.simulation.positionId, this.wizardCode)
      .subscribe((res) => this.router.navigate(["/"]));
  }

  setSaving(save) {
    this.saveIsEnable = save;
  }
}
