<ng-container *ngIf="ameoriList && ameoriList.length > 0">
  <table class="uc-table">
      <caption style="color: white">
        <h4>Record trovati</h4>
      </caption>
    <thead>
      <tr>
        <th scope="col" style="text-align: left">{{ 'UBZ.SITE_CONTENT.11101101100' | translate }}</th>
        <th scope="col" style="text-align: left">{{ 'UBZ.SITE_CONTENT.11101101101' | translate }}  </th>
        <th scope="col" style="text-align: left">{{'UBZ.SITE_CONTENT.11101101110' | translate }}</th>
        <th scope="col" style="text-align: left">{{'UBZ.SITE_CONTENT.11101101111' | translate }}</th>
        <th scope="col" style="text-align: left">{{ 'UBZ.SITE_CONTENT.11101110000' | translate }}</th>
        <th scope="col" style="text-align: left">{{ 'UBZ.SITE_CONTENT.***********' | translate }}</th>
        <th scope="col" style="text-align: left">{{ 'UBZ.SITE_CONTENT.***********' | translate }}</th>
        <th scope="col" style="text-align: left"></th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let ameori of ameoriList">
        <td style="text-align: left">{{ ameori.newAppraisalId }}</td>
        <td style="text-align: left">{{ ameori.originProcess }}</td>
        <td style="text-align: left">{{ ameori.accountType }}</td>
        <td style="text-align: left">{{ ameori.accountNumber }}</td>
        <td style="text-align: left">{{ ameori.ndg }}</td>
        <td style="text-align: left">{{ ameori.collatProg }}</td>
        <td style="text-align: center">{{ ameori.appValue ? (ameori.appValue | currency:'EUR':true:'1.2-2') : '' }}</td>
        <td style="text-align: left">
            <a role="button" (click)="openPopup(ameori)"><i class="icon-angle-double-right"></i></a>
        </td>
      </tr>
    </tbody>
  </table>
</ng-container>
