import { Component, OnInit } from '@angular/core';
import { GuaranteeTransferService } from '../../services/guarantee-transfer.service';

@Component({
  selector: 'app-guarantee-transfer-confirm',
  templateUrl: './guarantee-transfer-confirm.component.html'
})
export class GuaranteeTransferConfirmComponent implements OnInit {

  constructor(
    public guaranteeTransferService: GuaranteeTransferService
  ) {}

  ngOnInit() {
    // Se l'array non è popolato forza uscita dal wizard
    if (!this.guaranteeTransferService.appraisalList.length) {
      return this.guaranteeTransferService.cancelRequest();
    }
    setTimeout(() => (this.guaranteeTransferService.wizardStep = 4), 10);
  }
}
