<div *ngIf="isOpen" class="modal fade" id="invalidate" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #stateBox="bs-modal" (onHidden)="closeModal(false)" [config]="{show: 'true'}">
	<div class="modal-dialog modal-lg" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h2>{{'UBZ.SITE_CONTENT.110011101' | translate }}</h2>
				<button type="button" class="close pull-right" data-dismiss="modal" aria-label="Close" (click)="closeModal(false)">
					<i class="icon-close"></i>
				</button>
			</div>
			<div class="modal-body">
				<div class="row">
					<div class="col-sm-12">
						<h4>{{'UBZ.SITE_CONTENT.11000110' | translate }}</h4>
					</div>
					<div *ngIf="section" class="col-sm-3 form-group">
						<label>
								{{'UBZ.SITE_CONTENT.11010000' | translate }} {{ (section.entityType) ? (section.entityType.translationCod | translate) : ''}}
							</label>
							{{ (section.resItemType) ? (section.resItemType.translationCod | translate) : '' }} 
							{{ (section.resItemCategory) ? ('-' + (section.resItemCategory.translationCod | translate)) : '' }}
					</div>
					<div class="col-sm-3 form-group">
						<label>{{'UBZ.SITE_CONTENT.11001000' | translate }}</label>
						{{document.lastUpload.userId}}
					</div>
					<div class="col-sm-3 form-group">
						<label>{{'UBZ.SITE_CONTENT.1111110' | translate }}</label>
						{{document.lastUpload.uploadDate | date: 'dd-MM-y'}} {{document.lastUpload.uploadDate | customTime}}
					</div>
					<div class="col-sm-3 form-group">
						<label>{{'UBZ.SITE_CONTENT.10000001' | translate }}</label>
						<span [class]="isValidClass[document.acquired]"></span>
					</div>
				</div>
		
				<div class="row">
					<div class="col-sm-12 form-group">
						<h4>{{'UBZ.SITE_CONTENT.110011110' | translate }}</h4>
						<label>{{'UBZ.SITE_CONTENT.110011111' | translate }}*</label>
						<div class="custom-select">
							<select class="form-control" [(ngModel)]="reasonOfInvalidation">
								<option value="" selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
								<option *ngFor="let el of (reasonsDomain | domainMapToDomainArray)" value="{{el.domCode}}">{{el.translationCod | translate}}</option>
							</select>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-sm-12 form-group">
						<textarea class="form-control" placeholder="{{'UBZ.SITE_CONTENT.110100010' | translate }}..." [(ngModel)]="notes" required></textarea>
					</div>
				</div>
			</div>
			<div class="modal-footer">
				<button data-dismiss="modal" id="invalid-doc" type="button" class="btn btn-primary waves-effect" (click)="invalidateDocument()" [disabled]="reasonOfInvalidation === '' || notes === ''">
					{{'UBZ.SITE_CONTENT.110100011' | translate }}
				</button>
			</div>
		</div>
	</div>
</div>
