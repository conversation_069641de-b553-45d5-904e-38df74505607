import { Component, OnInit, On<PERSON><PERSON>roy, Inject } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { DragulaService } from 'ng2-dragula/ng2-dragula';
import { LandingService } from '../../../simulation/services/landing/landing.service';
import { WizardService } from '../../../shared/wizard/services/wizard.service';
import { RestrictionsService } from './service/restrictions.service';
import { DomainService } from '../../../shared/domain/domain.service';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';

@Component({
  selector: 'app-restrictions',
  templateUrl: './restrictions.component.html',
  styleUrls: ['./restrictions.component.css'],
  providers: [RestrictionsService]
})
export class RestrictionsComponent implements OnInit, OnDestroy {
  private positionId: string;
  private wizardCode: string;
  private bpmTaskId: string;
  private bpmTaskCod: string;
  public currentTask = 'UBZ-PER-RES';

  public limitationObject: any = { residualAssets: [], alienAssets: [] };
  public unitTypes: any = {};

  activeClassEnable: boolean[] = [false, false];
  commercialValue: number[] = [0, 0];
  cautionalValue: number[] = [0, 0];
  insuranceValue: number[] = [0, 0];

  private _initSubscription;
  private _overSubscription;
  private _outSubscription;
  private _dropModelSubscription;
  saveDraftCallback = this.saveDraft.bind(this);

  constructor(
    private dragulaService: DragulaService,
    private _activatedRoute: ActivatedRoute,
    public _landingService: LandingService,
    private _wizardService: WizardService,
    private _router: Router,
    private _restrictionService: RestrictionsService,
    private _domainService: DomainService,
    @Inject(APP_CONSTANTS) public constants: IAppConstants
  ) {
    this._overSubscription = this.dragulaService.over.subscribe(args => {
      this.handleActiveClass(args[2].id, true);
    });
    this._outSubscription = this.dragulaService.out.subscribe(args => {
      this.handleActiveClass(args[2].id, false);
    });
    this._dropModelSubscription = this.dragulaService.dropModel.subscribe(
      args => {
        this.calculateTotals();
      }
    );
    dragulaService.setOptions('first-bag', {
      revertOnSpill: true,
      moves: (el, source, handle, sibling) => {
        return !(
          el.className === 'no-record' ||
          _landingService.isLockedTask[this.currentTask]
        );
      }
    });
  }

  ngOnInit() {
    this._initSubscription = this._activatedRoute.params
      .switchMap((params: Params) => {
        this.positionId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        this.bpmTaskId = params['taskId'];
        this.bpmTaskCod = params['taskCod'];
        this._landingService.checkIfCompletedTask(
          this.positionId,
          this.currentTask
        );
        return Observable.forkJoin(
          this._restrictionService.getLimitationObjects(this.positionId),
          this._domainService.newGetDomain('UBZ_DOM_CATEGORY_TYPE','-',true)
        );
      })
      .subscribe(res => {
        this.limitationObject = res[0];
        this.unitTypes = res[1];
        setTimeout(() => {
          this.calculateTotals();
        }, 0);
      });
  }

  ngOnDestroy() {
    this._initSubscription.unsubscribe();
    this._overSubscription.unsubscribe();
    this._outSubscription.unsubscribe();
    this._dropModelSubscription.unsubscribe();
    this.dragulaService.destroy('first-bag');
  }

  private handleActiveClass(id: string, value: boolean) {
    if (id === 'firstBagId') {
      this.activeClassEnable[0] = value;
    } else {
      this.activeClassEnable[1] = value;
    }
  }

  private calculateTotals() {
    this.commercialValue = [0, 0];
    this.cautionalValue = [0, 0];
    this.insuranceValue = [0, 0];
    for (const asset of this.limitationObject.residualAssets) {
      this.commercialValue[0] += asset.bookValue;
      this.cautionalValue[0] += asset.pledgedValue;
      this.insuranceValue[0] += asset.insuranceValue;
    }
    for (const asset of this.limitationObject.alienAssets) {
      this.commercialValue[1] += asset.bookValue;
      this.cautionalValue[1] += asset.pledgedValue;
      this.insuranceValue[1] += asset.insuranceValue;
    }
  }

  saveData() {
    this.saveDraft().subscribe(res => {
      this._landingService.goNextPage(
        this.positionId,
        this.currentTask,
        this.wizardCode,
        this._activatedRoute
      );
    });
  }

  private saveDraft(): Observable<any> {
    return this._restrictionService.saveLimitationObjects(
      this.positionId,
      this.limitationObject
    );
  }

  goToGenericTask() {
    this._landingService.goToGenericTask(this.positionId, this.bpmTaskId, this.bpmTaskCod);
  }

  goToPreviousTask() {
    this._landingService.goToPreviousTask(this.positionId, this.wizardCode, this._activatedRoute);
  }
}
