import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { NgForm } from '@angular/forms';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { AgrarianAgencyModel } from '../../model/agrarian-agency.models';
import { Domain } from '../../../../../shared/domain/domain';
import { DomainService } from '../../../../../shared/domain/domain.service';

@Component({
  selector: 'app-salary-calculation',
  templateUrl: './salary-calculation.component.html',
  styleUrls: ['./salary-calculation.component.css']
})
export class SalaryCalculationComponent implements OnInit {
  @ViewChild(NgForm) private form: NgForm;
  @Input() model: AgrarianAgencyModel = new AgrarianAgencyModel();
  expenseTypes: Domain[] = [];
  partialTotal = 0;

  constructor(
    public _accordionAPFService: AccordionAPFService,
    public _domainService: DomainService
  ) {}

  ngOnInit() {
    this._domainService
      .newGetDomain('UBZ_DOM_EXPENSE_TYPE', 'FPS')
      .subscribe(res => {
        this.expenseTypes = res;
      });
  }

  calculateTotal(domCode, newValue) {
    this.model.appFarmLoss[domCode].lossAmount = newValue;
    this.model.appFarmLoss['ALE'].lossAmount = 0;
    this.model.appFarmLoss['ALE'].lossAmount +=  (this.model.appFarmLoss['IEC'].lossAmount ?
    Number(this.model.appFarmLoss['IEC'].lossAmount) : 0 );

    this.model.appFarmLoss['ALE'].lossAmount += ( this.model.appFarmLoss['ICS'].lossAmount ?
     Number(this.model.appFarmLoss['ICS'].lossAmount) : 0);

    this.model.appFarmLoss['ALE'].lossAmount += (this.model.appFarmLoss['AMM'].lossAmount ?
    Number(this.model.appFarmLoss['AMM'].lossAmount) : 0);

    this.model.appFarmLoss['ALE'].lossAmount += (this.model.appFarmLoss['ASA'].lossAmount ?
    Number(this.model.appFarmLoss['ASA'].lossAmount) : 0);

    this.model.appFarmLoss['ALE'].lossAmount += (this.model.appFarmLoss['QDA'].lossAmount ?
    Number(this.model.appFarmLoss['QDA'].lossAmount) : 0);

    this.model.appFarmLoss['ALE'].lossAmount += (this.model.appFarmLoss['SAR'].lossAmount ?
    Number(this.model.appFarmLoss['SAR'].lossAmount) : 0);

    this.model.appFarmLoss['ALE'].lossAmount += (this.model.appFarmLoss['STI'].lossAmount ?
    Number(this.model.appFarmLoss['STI'].lossAmount) : 0);

    this.model.appFarmLoss['ALE'].lossAmount += (this.model.appFarmLoss['MDA'].lossAmount ?
    Number(this.model.appFarmLoss['MDA'].lossAmount) : 0);
  }

  isValid(): boolean {
    return this.form && this.form.valid;
  }

  public retrieveModel() {
    return this.model;
  }
}
