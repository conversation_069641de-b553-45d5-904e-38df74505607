import {
  Component,
  OnChanges,
  Input,
  On<PERSON><PERSON>roy,
  SimpleChanges
} from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { Subscription } from 'rxjs/Subscription';
import { DomainService } from '../../../../../shared/domain/domain.service';
import { RegistryService } from '../../../../service/registry.service';
import { AssociatedExpert } from '../../../../model/registry.models';

@Component({
  selector: 'app-associated-expert',
  templateUrl: './associated-expert.component.html',
  styleUrls: ['./associated-expert.component.css']
})
export class AssociatedExpertComponent implements OnChanges, OnDestroy {
  public addExpert = false;
  @Input() public anagId: string;
  public expertList: any[] = [];
  public anagSubjectTypes: any[] = [];
  public anagStatus: any = [];
  public anExpertPresent: boolean;
  private _subscription: Subscription;

  constructor(
    private _domainService: DomainService,
    private _registryService: RegistryService,
    private _router: Router
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes && this.anagId) {
      this._subscription = Observable.forkJoin(
        this._domainService.newGetDomain('UBZ_DOM_EXPERT_ROLE'),
        this._domainService.newGetDomain('UBZ_DOM_ANAG_STATUS')
      ).subscribe(res => {
        this.anagSubjectTypes = res[0];
        this.anagStatus = res[1];
        this.refreshExpertList();
      });
    }
  }

  ngOnDestroy() {
    if (this._subscription && !this._subscription.closed) {
      this._subscription.unsubscribe();
    }
  }

  private checkExpertList() {
    this.anExpertPresent = this.expertList.length > 0 ? true : false;
  }

  public abortAdding(event: any): void {
    event.stopPropagation();
    this.addExpert = false;
    this.refreshExpertList();
  }

  public saveAddedData(event: any): void {
    event.stopPropagation();
    this._registryService
      .saveExpSocAssociatedExperts(this.expertList, this.anagId)
      .subscribe(res => {
        this.refreshExpertList();
        this.addExpert = false;
      });
  }

  public createNewExpert(): void {
    this.expertList.push(new AssociatedExpert());
  }

  public addAnExpert(event: any, addExpert: boolean = false) {
    event.stopPropagation();
    this.refreshExpertList(addExpert);
    this.addExpert = true;
  }

  private refreshExpertList(addExpert: boolean = false) {
    return this._registryService
      .getExpSocAssociatedExperts(this.anagId)
      .subscribe(res => {
        this.expertList = res;
        if (addExpert) {
          this.createNewExpert();
        }
        this.checkExpertList();
      });
  }

  public goToDetail(idAnag: string) {
    this._registryService.fromExpertFirm = true;
    this._registryService.lastExpertFirm = this.anagId;
    this._router.navigateByUrl(`registry/PER/${idAnag}`);
  }
}
