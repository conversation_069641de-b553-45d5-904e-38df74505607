<!-- FIXME - TOGLIERE CODICE COMMENTATO -->
<fieldset [disabled]="!landingService.positionLocked">
<form #f="ngForm" (ngSubmit)="saveGenericInfo()" novalidate>
  <!-- Per richieste TGP i campi devono essere disabilitati e prevalorizzati con i valori inseriti nel link di richiesta -->
    <fieldset *ngIf="contentLoaded" [disabled]="isFieldSetDisabled()">
    <div class="row">
      <div class="col-sm-12">
        <h3>{{'UBZ.SITE_CONTENT.1010000' | translate }}</h3>
      </div>
      <div class="form-group" [ngClass]="{'col-sm-3': !isSurroga(), 'col-sm-4': isSurroga()}">
        <label>{{'UBZ.SITE_CONTENT.1010001' | translate }}</label>
        <div class="custom-select">
            <select class="form-control" name="macroprocess" [(ngModel)]="simulation.macroProcess"
              [disabled]="this.macroprocessesCount <= 1" (ngModelChange)="changeMacroProcess()" required>
            <option value="">{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <ng-container *ngFor="let row of (domainMacroProcess | domainMapToDomainArray)">
                <option *ngIf="row.domCode !== 'AGG' || isUpdateMacroprocessAvailable" value="{{row.domCode}}">
                  {{row.translationCod | translate}}</option>
            </ng-container>
          </select>
        </div>
      </div>
      <div class="form-group" [ngClass]="{'col-sm-3': !isSurroga(), 'col-sm-4': isSurroga()}">
        <label>{{'UBZ.SITE_CONTENT.1010010' | translate }}</label>
        <div class="custom-select">
          <select class="form-control" name="struct" [(ngModel)]="simulation.appraisalStruct" disabled required>
            <option [ngValue]="undefined">{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
              <option *ngFor="let row of (domainStructureType | domainMapToDomainArray)" value="{{row.domCode}}">
                {{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
      <!-- finalità operazione -->
      <div class="form-group" [ngClass]="{'col-sm-3': !isSurroga(), 'col-sm-4': isSurroga()}">
        <label>{{'UBZ.SITE_CONTENT.1010011' | translate }}*</label>
         <div class="custom-select">
          <select class="form-control required" [(ngModel)]="simulation.appraisalScope" data-placement="bottom" name="appraisalScope" #appraisalScope="ngModel"
              required [ngClass]="(f.submitted && !appraisalScope.valid && simulation.macroProcess !== 'AGG') ? 'error' : 'valid'">
            <option [ngValue]="undefined">{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <!-- Valore CTU per finalita operazione visibile sse macroprocesso === 'WOR' -->
              <option
                *ngFor="let row of (domainScopeType | domainMapToDomainArray | removeFromDomain: simulation.macroProcess : isSurroga() : isTecnicaOperETL())"
                value="{{row.domCode}}">
              {{row.translationCod | translate}}
            </option>
          </select>
        </div>
          <div *ngIf="f.submitted && !appraisalScope.valid && simulation.macroProcess !== 'AGG'"
            class="tooltip fade bottom in" role="tooltip" style="top: 67px; left: 127px;">
          <div class="tooltip-arrow" style="left: 50%;"></div>
          <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
        </div>
      </div>
      <div class="col-sm-3 form-group" *ngIf="wizardCode === 'WSIM'">
        <label>{{'UBZ.SITE_CONTENT.1010101' | translate }}*</label>
        <div class="custom-select">
            <select class="form-control required" [(ngModel)]="simulation.collatTecForm" name="collatTecForm"
              #collatTecForm="ngModel" required [ngClass]="(f.submitted && !collatTecForm.valid) ? 'error' : 'valid'">
            <option [ngValue]="undefined">{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
              <option *ngFor="let row of (domainCollateralTecForm | domainMapToDomainArray)" value="{{row.domCode}}">
                {{row.translationCod | translate}}</option>
          </select>
        </div>
          <div *ngIf="f.submitted && !collatTecForm.valid" class="tooltip fade bottom in" role="tooltip"
            style="top: 67px; left: 127px;">
          <div class="tooltip-arrow" style="left: 50%;"></div>
          <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="form-group" [ngClass]="{'col-sm-3': !isSurroga(), 'col-sm-4': isSurroga()}">
        <label></label>
          <div class="custom-checkbox">
            <input type="checkbox" id="perizia-da-terzi" name="perizia-da-terzi"
              [checked]="simulation.flagPeriziaTerzi || isTecnicaUsmOper() === true || accessPoint === 'PT'"
              (change)="changePeriziaDaTerzi()" class="checkbox"
              [disabled]="simulation.flagTipoPoolCapofila || simulation.macroProcess === 'AGG' || isTecnicaUsmOper() === true || isUbzRstProfile() || accessPoint === 'PT' ">
            <label for="perizia-da-terzi">{{'UBZ.SITE_CONTENT.1010110' | translate }}</label>
          </div>
      </div>
      <!-- tipologia operazione -->
      <div class="form-group" [ngClass]="{'col-sm-3': !isSurroga(), 'col-sm-4': isSurroga()}">
        <label *ngIf="!simulation.flagPeriziaTerzi">{{'UBZ.SITE_CONTENT.1010111' | translate }}</label>
        <label *ngIf="simulation.flagPeriziaTerzi">{{'UBZ.SITE_CONTENT.1010111' | translate }}*</label>
        <div class="custom-select">
            <select class="form-control" [(ngModel)]="simulation.operationType" name="operationType"
              #operationType="ngModel" [disabled]="isSurroga() || !simulation.flagPeriziaTerzi"
              [required]="simulation.flagPeriziaTerzi"
              [ngClass]="(!isSurroga() && simulation.flagPeriziaTerzi && f.submitted && !operationType.valid) ? 'error' : 'valid'"
              (change)="changeOperationType()">
            <option [ngValue]="undefined">{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <!-- <option *ngFor="let row of (domainOperationType | domainMapToDomainArray | filterFields: fOperType)" value="{{row.domCode}}">{{row.translationCod | translate}}</option> -->
              <option *ngFor="let row of (domainOperationType | domainMapToDomainArray )" value="{{row.domCode}}">
                {{row.translationCod | translate}}</option>
          </select>
        </div>
          <div *ngIf="!isSurroga() && simulation.flagPeriziaTerzi && f.submitted && !operationType.valid"
            class="tooltip fade bottom in" role="tooltip" style="top: 67px; left: 127px;">
          <div class="tooltip-arrow" style="left: 50%;"></div>
          <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
        </div>
      </div>
      
      <!-- tipologia perizia -->
      <div class="form-group" [ngClass]="{'col-sm-3': !isSurroga(), 'col-sm-4': isSurroga()}">
        <label>{{'UBZ.SITE_CONTENT.1011001' | translate }}*</label>
        <div class="custom-select">
            <select class="form-control required" data-placement="bottom" [(ngModel)]="simulation.appraisalType"
              name="appraisalType" #appraisalType="ngModel" required="!isSurroga()"
              [ngClass]="(!isSurroga() && f.submitted && !appraisalType.valid && simulation.macroProcess !== 'AGG') ? 'error' : 'valid'"
              [disabled]="isSurroga() || !(simulation.appraisalScope) || simulation.appraisalScope === '' || simulation.macroProcess === 'AGG'">
            <option [ngValue]="undefined">{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <ng-container *ngIf="wizardCode === 'WSIM' || !isSurroga()">
                <option *ngFor="let row of (domainAppraisalType | domainMapToDomainArray | filterFields: fApprType)"
                  value="{{row.domCode}}">{{row.translationCod | translate}}</option>
            </ng-container>
            <ng-container *ngIf="isSurroga()">
                <option *ngFor="let row of (domainAppraisalType | domainMapToDomainArray)" value="{{row.domCode}}">
                  {{row.translationCod | translate}}</option>
            </ng-container>
          </select>
        </div>
          <div *ngIf="!isSurroga() && f.submitted && !appraisalType.valid && simulation.macroProcess !== 'AGG'"
            class="tooltip fade bottom in" role="tooltip" style="top: 67px; left: 127px;">
          <div class="tooltip-arrow" style="left: 50%;"></div>
          <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="form-group col-sm-3" *ngIf="simulation.operationType === 'REL' && !simulation.fromaSim">
        <label>{{'UBZ.SITE_CONTENT.1010111000' | translate }}</label>
        <div class="custom-select" *ngIf="simulation.macroProcess !== 'CTE'">
          <select class="form-control valid" name="expertName" [(ngModel)]="simulation.expertName" required>
            <option [ngValue]="undefined">{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let expert of agencyList" value="{{expert.idSocPer}}">{{expert.heading}}</option>
            <option *ngFor="let expert of expertList" value="{{expert.idExpert}}">{{expert.heading}}</option>
          </select>
        </div>
        <div *ngIf="simulation.macroProcess === 'CTE'">
            <input type="text" class="form-control required" value="{{simulation.expertName}}"
              [ngModel]="simulation.expertName" name="expertName" required disabled />
        </div>
      </div>

        <div class="form-group" [ngClass]="{'col-sm-3': !isSurroga(), 'col-sm-4': isSurroga()}"
          *ngIf="simulation.flagPeriziaTerzi && isTecnicaUsmOper() === false">
        <label>{{'UBZ.SITE_CONTENT.1000101110' | translate }}*</label>
          <app-calendario [(ngModel)]="simulation.surveyOutcome" [name]="'surveyOutcome'"
            [placeholder]="'UBZ.SITE_CONTENT.1001000000' | translate" [disabled]="simulation.macroProcess === 'AGG'"
          [required]="true">
        </app-calendario>
          <div *ngIf="f.submitted && !simulation.surveyOutcome" class="tooltip fade bottom in" role="tooltip"
            style="top: 67px; left: 127px;">
        <!-- <div *ngIf="f.submitted && !f.controls['surveyOutcome'].valid" class="tooltip fade bottom in" role="tooltip" style="top: 67px; left: 127px;"> -->
          <div class="tooltip-arrow" style="left: 50%;"></div>
          <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
        </div>
      </div>

      <div class="col-sm-4 form-group" *ngIf="isSurroga()">
        <label>{{'UBZ.SITE_CONTENT.11000101' | translate }}</label>
          <input type="text" value="{{ simulation.note }}" class="form-control" data-placement="bottom"
            [(ngModel)]="simulation.note" name="note" />
      </div>
    </div>

    <div class="row" *ngIf="accessPoint === 'PT'   && !isTecnicaUsmOper() ||( simulation.flagPeriziaTerzi && isTecnicaUsmOper() === false)">
      <div class="form-group col-sm-3">
        <label></label>
          <div class="custom-checkbox" *ngIf="accessPoint == 'PT'">
            <input type="checkbox" id="flag-forcing-owner" name="flag-forcing-owner" [checked]="flagForcingOwnerBool"
              (change)="changeFlagForcingOwner()" class="checkbox" [disabled]="isFlagForcingOwnerDisabled">
            <label for="flag-forcing-owner">{{'UBZ.SITE_CONTENT.10101011000' | translate }}</label>
          </div>
      </div>
      <!-- fatturazione in carico -->
      <div class="form-group col-sm-3" *ngIf="flagForcingOwnerBool  " [ngClass]="{'active':accessPoint === 'RP' && simulation.macroProcess === 'ITL','nactive':simulation.operationType === objkey}">
        <label>{{'UBZ.SITE_CONTENT.10101011001' | translate }}*</label>
        <div class="custom-select" >
            <select class="form-control" [(ngModel)]="simulation.forcingOwner" name="forcingOwner"
              [required]="flagForcingOwnerBool">
            <option [ngValue]="null">{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
              <option *ngFor="let row of (appraisalOwnerDomain | domainMapToDomainArray )" value="{{row.domCode}}">
                {{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
      <!-- nota fatturazione -->
      <div class="form-group col-sm-3" *ngIf="flagForcingOwnerBool"[ngClass]="{'active':accessPoint === 'RP' && simulation.macroProcess === 'ITL','nactive':simulation.operationType === objkey}">
        <label >{{'UBZ.SITE_CONTENT.10101011010' | translate }}*</label>
        <div class="custom-select">
            <textarea class="form-control" [(ngModel)]="simulation.forcingNote" name="forcingNote"
              [required]="flagForcingOwnerBool"></textarea>
        </div>
      </div>
    </div> 
 <!-- Tab Assegnazione -->
    <div role="tabpanel" id="assegnazione" *ngIf="isTecnicaOper() === true">
        <app-expert-table #appExpertTable [positionId]="positionId" [isSecondOpinion]="false"
          [isOperCTEAppraisalRequest]="true" [idExpert]="simulation.idExpert" (isCompiled)="onIsCompiledExpert($event)"
        (isNotCompiled)="onIsNotCompiledExpert($event)">
      </app-expert-table>
    </div>
    <!-- Fine tab Assegnazione -->
    
  </fieldset>

    <app-navigation-footer showSaveDraft="true" showPrevious="true" [footerClass]="menuService.footerProperty"
      [saveIsEnable]="isSaveButtonEnabled" (closeDraftButtonClick)="exit()" (previousButtonClick)="previous()"
      (cancelButtonClick)="cancelPosition()" [saveDraftCallback]="draftButtonCallback" [activeTaskCode]="currentTask"
    cancelButtonString="{{wizardCode === constants.wizardCodes.SIM ? ('UBZ.SITE_CONTENT.1001010100' | translate) : ('UBZ.SITE_CONTENT.1010' | translate)}}">
  </app-navigation-footer>
</form>
</fieldset>