<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="unicreditlight" horiz-adv-x="1048" >
<font-face units-per-em="2048" ascent="1536" descent="-512" />
<missing-glyph horiz-adv-x="462" />
<glyph unicode="&#xfb01;" horiz-adv-x="980" d="M45 934v39q0 10 12 14l129 17v75q0 68 8.5 111t28.5 82q41 78 122 105.5t179 27.5q66 0 129.5 -11.5t102.5 -27.5q12 -4 15 -9t1 -16l-6 -32q-2 -20 -23 -15q-47 14 -103 22.5t-114 8.5q-70 0 -111.5 -9t-70.5 -32q-43 -33 -56.5 -82t-13.5 -135v-63h510q16 0 17 -17 v-971q0 -16 -17 -16h-57q-14 0 -14 16v906h-439v-906q0 -16 -16 -16h-57q-14 0 -15 16v906h-129q-12 0 -12 12z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1042" d="M45 936v39q0 10 12 14l129 17v75q0 68 8.5 111t28.5 82q41 76 119 104.5t170 28.5q57 0 113.5 -10.5t97.5 -22.5l68 15q16 0 16 -17v-1143q0 -90 22.5 -117.5t81.5 -27.5h54q14 0 14 -14v-52q0 -16 -16 -16h-48q-109 0 -152.5 46t-43.5 171v1073q-43 16 -96.5 25.5 t-108.5 9.5q-66 0 -104.5 -9t-65.5 -32q-41 -35 -55.5 -83t-14.5 -134v-63h224q16 0 16 -15v-53q0 -14 -16 -14h-224v-906q0 -16 -16 -16h-57q-14 0 -15 16v906h-129q-12 0 -12 12z" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="462" />
<glyph unicode=" "  horiz-adv-x="462" />
<glyph unicode="&#x09;" horiz-adv-x="462" />
<glyph unicode="&#xa0;" horiz-adv-x="462" />
<glyph unicode="!" horiz-adv-x="442" d="M143 53v41q0 35 14.5 54.5t51.5 19.5h24q37 0 51.5 -19.5t14.5 -54.5v-41q0 -33 -14.5 -52t-51.5 -19h-24q-37 0 -51.5 19t-14.5 52zM172 1296q0 20 21 21h55q20 0 20 -21l-8 -989q0 -20 -16 -20h-47q-16 0 -17 20z" />
<glyph unicode="&#x22;" horiz-adv-x="610" d="M100 1380q-2 18 6.5 23.5t24.5 5.5h47q16 0 23.5 -5t7.5 -24l-14 -430q0 -25 -23 -24h-35q-23 0 -22 24zM403 1380q-2 18 6.5 23.5t24.5 5.5h47q16 0 23.5 -5t7.5 -24l-14 -430q0 -25 -23 -24h-35q-23 0 -22 24z" />
<glyph unicode="#" horiz-adv-x="1218" d="M2 422l12 49q4 14 29 14h256l80 361h-273q-27 0 -20 18l12 49q4 14 29 15h266l82 375q4 20 21 20h57q18 0 12 -20l-80 -375h342l82 375q4 20 21 20h53q18 0 14 -20l-79 -375h278q27 0 21 -19l-13 -49q-4 -14 -28 -14h-275l-78 -361h289q27 0 21 -18l-13 -49 q-4 -14 -28 -15h-285l-82 -383q-6 -20 -25 -20h-47q-18 0 -16 20l80 383h-342l-82 -383q-6 -20 -23 -20h-53q-16 0 -14 20l80 383h-260q-27 0 -21 19zM391 483h342l80 363h-344z" />
<glyph unicode="$" d="M137 94l15 47q6 20 28 11q59 -27 141 -43.5t171 -16.5h16v555l-66 17q-145 37 -224 107.5t-79 197.5v35q0 145 92.5 226t264.5 91v184q0 25 20 25h39q20 0 20 -25v-182q74 -4 141.5 -13.5t121.5 -27.5q12 -4 16 -9t0 -22l-8 -41q-6 -20 -33 -12q-98 31 -238 37v-508 l33 -8q88 -23 147.5 -49.5t96.5 -64.5t52.5 -89t15.5 -121v-37q0 -164 -91.5 -247.5t-242.5 -102.5v-201q0 -25 -25 -24h-29q-25 0 -24 24v197h-14q-111 0 -196 19.5t-148 44.5q-20 8 -13 26zM244 983q0 -59 19.5 -99t53 -66.5t79.5 -44t100 -30.5v492q-252 -16 -252 -231 v-21zM586 98q117 18 174 83t57 169v23q0 59 -13 101t-41 71.5t-72 49t-105 34.5v-531z" />
<glyph unicode="%" horiz-adv-x="1497" d="M102 973v24q0 88 15.5 165t62.5 124q53 57 168 57t168 -57q47 -47 62.5 -124t15.5 -165v-24q0 -88 -15.5 -165t-62.5 -124q-53 -57 -168 -57t-168 57q-47 47 -62.5 124t-15.5 165zM188 975q0 -82 11.5 -135.5t32 -85t50 -44t66.5 -12.5t66.5 12.5t50 44t32 85t11.5 135.5 v20q0 82 -11.5 135.5t-32 85t-50 44t-66.5 12.5t-66.5 -12.5t-50 -44t-32 -85t-11.5 -135.5v-20zM334 -2l725 1333q4 8 8 10t14 2h68q18 0 10 -16l-725 -1333q-4 -8 -8 -10t-14 -2h-68q-18 0 -10 16zM903 328v24q0 88 15.5 165t62.5 124q53 57 168 57t168 -57 q47 -47 62.5 -124t15.5 -165v-24q0 -88 -15.5 -165t-62.5 -124q-53 -57 -168 -57t-168 57q-47 47 -62.5 124t-15.5 165zM989 330q0 -82 11.5 -135.5t32 -85t50 -44t66.5 -12.5t66.5 12.5t50 44t32 85t11.5 135.5v20q0 82 -11.5 135.5t-32 85t-50 44t-66.5 12.5t-66.5 -12.5 t-50 -44t-32 -85t-11.5 -135.5v-20z" />
<glyph unicode="&#x26;" horiz-adv-x="1243" d="M100 313v31q0 141 66.5 232.5t191.5 146.5q-90 90 -125.5 168t-35.5 160v8q0 129 79.5 206.5t225.5 77.5q76 0 132 -21.5t94 -58t56.5 -88t18.5 -110.5v-21q0 -57 -11.5 -110t-39 -101.5t-74.5 -93.5t-119 -84l352 -350q12 39 18.5 89t6.5 108v149q0 20 22 21h50 q20 0 20 -23v-137q0 -92 -9 -156.5t-32 -124.5l184 -180q16 -16 0 -33l-30 -28q-10 -10 -17.5 -9t-21.5 13l-158 153q-53 -82 -146 -123.5t-237 -41.5h-82q-193 0 -286 89t-93 242zM203 322q0 -123 67.5 -189.5t221.5 -66.5h49q129 0 212 34.5t124 114.5l-463 455 q-119 -59 -165 -135t-46 -191v-22zM299 1042q0 -35 8 -67.5t28.5 -69.5t55.5 -79t88 -93l25 -24q57 33 96 67.5t62.5 74.5t33 88t9.5 110v12q0 104 -57.5 152.5t-141.5 48.5q-111 0 -159 -53.5t-48 -157.5v-9z" />
<glyph unicode="'" horiz-adv-x="307" d="M100 1380q-2 18 6.5 23.5t24.5 5.5h47q16 0 23.5 -5t7.5 -24l-14 -430q0 -25 -23 -24h-35q-23 0 -22 24z" />
<glyph unicode="(" horiz-adv-x="557" d="M154 590q0 221 70.5 431t217.5 404q12 14 29 5l33 -21q18 -10 6 -27q-72 -96 -121 -196.5t-78.5 -199.5t-42 -199.5t-12.5 -196.5q0 -98 12.5 -198.5t42 -200t78.5 -198.5t121 -196q12 -16 -6 -26l-33 -21q-16 -10 -29 4q-147 193 -217.5 404t-70.5 432z" />
<glyph unicode=")" horiz-adv-x="557" d="M47 -203q143 193 198.5 393.5t55.5 399.5q0 96 -12.5 196.5t-42 199.5t-78.5 199.5t-121 196.5q-12 16 6 27l33 21q16 10 29 -5q147 -195 217.5 -404.5t70.5 -430.5t-70.5 -432t-217.5 -404q-12 -14 -29 -4l-33 21q-18 10 -6 26z" />
<glyph unicode="*" horiz-adv-x="675" d="M57 1245l25 60q8 27 35 12l196 -105l-32 240q-4 18 2 24.5t22 6.5h66q16 0 22 -6.5t2 -24.5l-33 -240l197 105q27 14 35 -12l24 -60q10 -25 -20 -31l-219 -55l174 -186q20 -23 0 -37l-55 -37q-25 -16 -39 10l-121 211l-121 -211q-14 -27 -39 -10l-55 37q-23 14 0 37 l174 186l-219 55q-31 6 -21 31z" />
<glyph unicode="+" d="M109 504v61q0 14 12 15h358v356q0 14 15 14h61q12 0 12 -14v-356h359q12 0 12 -13v-63q0 -12 -12 -12h-359v-359q0 -14 -12 -14h-61q-14 0 -15 14v359h-358q-12 0 -12 12z" />
<glyph unicode="," horiz-adv-x="366" d="M109 43v68q0 68 65 67h27q39 0 59.5 -18.5t20.5 -67.5v-41q0 -78 -27 -154.5t-80 -154.5q-8 -14 -22 -6l-39 22q-12 6 -2 21q27 41 46 82t29 76q6 27 -16 36l-25 11q-37 14 -36 59z" />
<glyph unicode="-" horiz-adv-x="563" d="M72 504v61q0 14 14 15h391q14 0 15 -15v-61q0 -14 -15 -15h-391q-14 0 -14 15z" />
<glyph unicode="." horiz-adv-x="378" d="M109 49v55q0 68 69 68h21q41 0 56 -16.5t15 -51.5v-55q0 -35 -15 -51t-56 -16h-21q-70 0 -69 67z" />
<glyph unicode="/" horiz-adv-x="630" d="M53 -33l428 1428q6 18 23 18h57q20 0 17 -20l-428 -1428q-6 -18 -23 -18h-57q-23 0 -17 20z" />
<glyph unicode="0" d="M127 635v55q0 190 27.5 316.5t80 201t126 105t163.5 30.5t164 -30.5t126 -105t80 -200.5t28 -317v-55q0 -190 -28 -316t-80 -201t-126 -105.5t-164 -30.5t-163.5 30.5t-126 105.5t-80 200.5t-27.5 316.5zM229 649q0 -135 12.5 -243.5t45 -183.5t90 -115.5t147.5 -40.5 t147.5 40.5t90.5 115.5t45 183.5t12 243.5v27q0 135 -12 243.5t-45 183.5t-90.5 116t-147.5 41t-147.5 -41t-90 -116t-45 -183.5t-12.5 -243.5v-27z" />
<glyph unicode="1" d="M164 16v45q0 18 18 19h316v1151h-9l-239 -148q-14 -8 -23 4l-26 48q-8 14 8 24l240 150q16 10 29.5 13t39.5 3h41q33 0 33 -31v-1214h313q18 0 19 -19v-45q0 -16 -19 -16h-723q-18 0 -18 16z" />
<glyph unicode="2" d="M166 35v190q0 80 10 142.5t38 111.5t77 89t129 73l147 61q66 27 108 56.5t66.5 63.5t34.5 75t10 92v15q0 147 -68.5 200t-207.5 53q-66 0 -144.5 -12t-144.5 -35q-25 -8 -31 17l-8 37q-6 23 17 28q31 10 70.5 19.5t82.5 16.5t84 11t76 4q90 0 159.5 -16t118 -54 t74 -102.5t25.5 -160.5v-23q0 -72 -15.5 -124t-49 -94t-85 -74.5t-125.5 -65.5l-145 -64q-68 -31 -110 -59.5t-63.5 -68.5t-28.5 -96t-7 -142v-119h588q18 0 18 -19v-45q0 -16 -18 -16h-647q-20 0 -27.5 6t-7.5 29z" />
<glyph unicode="3" d="M156 57l8 35q2 8 7 13.5t22 0.5q53 -16 129.5 -30.5t156.5 -14.5q66 0 121 15.5t95 49.5t61.5 87t21.5 127v20q0 94 -25.5 153.5t-74.5 91.5t-119 43t-156 11h-55q-20 0 -20 19v53q0 18 20 19h55q195 0 273 63.5t78 194.5v20q0 119 -66.5 174t-183.5 55q-72 0 -138.5 -12 t-121.5 -31q-23 -8 -29 15l-8 37q-4 20 16 28q57 23 133 36t152 13q178 0 263 -81.5t85 -225.5v-28q0 -109 -49 -186t-162 -111v-9q49 -10 91 -34.5t75 -64.5t51.5 -97t18.5 -131v-23q0 -98 -29 -168.5t-80 -115.5t-122.5 -65.5t-160.5 -20.5q-92 0 -171.5 13t-145.5 34 q-12 4 -15 11t-1 17z" />
<glyph unicode="4" d="M76 389v21q0 16 7 31.5t23 43.5l492 799q14 23 29.5 32t48.5 9h69q39 0 39 -37v-852h146q18 0 18 -18v-45q0 -16 -18 -17h-146v-333q0 -23 -18 -23h-57q-18 0 -19 23v333h-584q-16 0 -23 6.5t-7 26.5zM178 436h512v807h-16z" />
<glyph unicode="5" d="M143 76l13 39q6 20 32 8q66 -29 145 -43t150 -14q154 0 238 79q43 41 62.5 107.5t19.5 150.5v17q0 78 -15.5 134t-52.5 103q-66 80 -203 80q-74 0 -142 -20.5t-124 -54.5q-20 -10 -35 -9l-28 9q-16 6 -15 26l41 610q2 27 31 27h531q23 0 22 -22v-41q0 -25 -22 -25h-459 q-18 0 -19 -16l-32 -467q109 70 260 69q88 0 151.5 -22.5t112.5 -71.5q53 -53 76.5 -130t23.5 -181v-17q0 -102 -26.5 -183t-85.5 -134q-113 -102 -322 -102q-74 0 -162 17t-149 46q-23 10 -17 31z" />
<glyph unicode="6" d="M133 612v19q0 199 43 343t111 226q57 70 143 106.5t207 36.5q45 0 97 -7t89 -19q23 -8 17 -29l-9 -33q-4 -10 -8 -15t-20 -1q-35 8 -81 14.5t-95 6.5q-102 0 -173 -40t-117 -114t-69.5 -176.5t-29.5 -224.5q57 51 135 84.5t186 33.5q168 0 265.5 -101t97.5 -282v-39 q0 -96 -30 -173.5t-82 -132t-120.5 -84t-146.5 -29.5q-217 0 -313.5 164.5t-96.5 465.5zM236 600q0 -123 17 -221t54 -167t96.5 -106.5t143.5 -37.5q125 0 198.5 90t73.5 245v27q0 158 -73.5 232.5t-202.5 74.5q-92 0 -170 -35.5t-137 -101.5z" />
<glyph unicode="7" d="M135 1262v41q0 23 23 22h749q18 0 24.5 -6t6.5 -23v-26q0 -27 -11.5 -49.5t-31.5 -63.5l-573 -1134q-6 -12 -13.5 -17.5t-23.5 -5.5h-52q-33 0 -20 25l623 1212h-678q-23 0 -23 25z" />
<glyph unicode="8" d="M129 338v8q0 117 61.5 207t171.5 141q-109 49 -155.5 131t-46.5 166v27q0 143 97 234t267 91q90 0 157 -24.5t111 -65.5t65.5 -98t21.5 -121v-26q0 -106 -59.5 -193.5t-160.5 -146.5q131 -47 195 -128t64 -186v-18q0 -88 -30 -154.5t-82 -110.5t-124 -66.5t-158 -22.5 t-157.5 19t-125 62t-83 111t-29.5 164zM233 326q0 -133 83 -198t208 -65q129 0 208 69t79 202v12q0 98 -61.5 159.5t-188.5 108.5l-117 43q-102 -47 -156.5 -125.5t-54.5 -189.5v-16zM268 997q0 -104 58.5 -164.5t173.5 -103.5l75 -29q100 61 148.5 132t48.5 176v14 q0 104 -60.5 172t-187.5 68q-125 0 -190.5 -68t-65.5 -178v-19z" />
<glyph unicode="9" d="M119 891v39q0 88 26.5 163.5t76.5 131t124 87t168 31.5q117 0 194.5 -47t124 -132t64.5 -200.5t18 -250.5v-21q0 -154 -23.5 -283.5t-79.5 -225t-148.5 -148.5t-229.5 -53q-59 0 -120.5 8t-102.5 22q-14 4 -16 10.5t0 16.5l8 37q6 23 30 16q45 -10 96.5 -19.5t104.5 -9.5 q113 0 185.5 49.5t114.5 129.5t59.5 183t17.5 214q-53 -57 -142 -97t-194 -40q-170 0 -263 107.5t-93 281.5zM225 897q0 -158 70 -233.5t199 -75.5q78 0 161.5 35.5t155.5 105.5q0 256 -70.5 395.5t-222.5 139.5q-150 0 -221.5 -92.5t-71.5 -251.5v-23z" />
<glyph unicode=":" horiz-adv-x="378" d="M109 49v55q0 68 69 68h21q41 0 56 -16.5t15 -51.5v-55q0 -35 -15 -51t-56 -16h-21q-70 0 -69 67zM109 829v55q0 68 69 68h21q41 0 56 -16.5t15 -51.5v-55q0 -35 -15 -51t-56 -16h-21q-70 0 -69 67z" />
<glyph unicode=";" horiz-adv-x="366" d="M109 43v68q0 68 65 67h27q39 0 59.5 -18.5t20.5 -67.5v-41q0 -78 -27 -154.5t-80 -154.5q-8 -14 -22 -6l-39 22q-12 6 -2 21q27 41 46 82t29 76q6 27 -16 36l-25 11q-37 14 -36 59zM111 829v55q0 68 69 68h21q41 0 56 -16.5t15 -51.5v-55q0 -35 -15 -51t-56 -16h-21 q-70 0 -69 67z" />
<glyph unicode="&#x3c;" d="M174 516v37q0 12 10 18l631 342q20 12 31 -4l22 -43q12 -20 -8 -30l-559 -299l559 -299q20 -10 8 -31l-22 -43q-10 -16 -31 -4l-631 338q-10 4 -10 18z" />
<glyph unicode="=" d="M109 322v61q0 14 12 14h805q12 0 12 -12v-63q0 -12 -12 -13h-805q-12 0 -12 13zM109 686v62q0 14 12 14h805q12 0 12 -12v-64q0 -12 -12 -12h-805q-12 0 -12 12z" />
<glyph unicode="&#x3e;" d="M180 207q-12 20 8 31l560 299l-560 299q-20 10 -8 30l23 43q10 16 30 4l631 -342q10 -6 10 -18v-37q0 -14 -10 -18l-631 -338q-20 -12 -30 4z" />
<glyph unicode="?" horiz-adv-x="907" d="M92 1268q-2 16 10 22q59 23 145.5 38t174.5 15q203 0 293 -86t90 -233v-25q0 -96 -45 -172.5t-152 -148.5l-102 -70q-43 -31 -69.5 -54.5t-41 -51t-19.5 -64.5t-5 -90v-57q0 -23 -19 -23h-57q-18 0 -19 23v57q0 61 5.5 105.5t23 80t48 66.5t81.5 66l101 67 q47 33 79.5 59.5t53 56.5t30 65.5t9.5 86.5v15q0 129 -75 188.5t-218 59.5q-78 0 -153 -14.5t-138 -37.5q-18 -6 -21 13zM248 53v29q0 35 14 54.5t51 19.5h25q37 0 51 -19.5t14 -54.5v-29q0 -33 -14 -52t-51 -19h-25q-37 0 -51 19t-14 52z" />
<glyph unicode="@" horiz-adv-x="1593" d="M106 625q0 172 52.5 311t147.5 239.5t231.5 154.5t302.5 54q139 0 259 -42t208 -120.5t138 -193.5t50 -258q0 -121 -25.5 -211t-71.5 -150.5t-107.5 -90t-131.5 -29.5q-94 0 -152.5 43t-68.5 98q-27 -51 -83 -90t-132 -39q-96 0 -154.5 61.5t-58.5 186.5q0 53 10 129 t25 129q27 100 92 157.5t164 57.5q66 0 113 -28.5t69 -73.5l12 53q4 14 9.5 17t19.5 3h29q14 0 20 -4t4 -20l-45 -348q-4 -33 -8 -68t-4 -51q0 -61 33.5 -98t109.5 -37q61 0 107.5 28.5t76 80.5t45 125t15.5 161q0 119 -39 217t-111.5 169t-179 110.5t-241.5 39.5 q-139 0 -256 -45t-201 -131t-131 -208.5t-47 -278.5q0 -154 47 -268.5t132 -190.5t202.5 -112.5t259.5 -36.5q111 0 213 21.5t178 53.5q10 4 17 3t12 -13l10 -29q8 -23 -15 -32q-80 -39 -196.5 -62.5t-233.5 -23.5q-170 0 -304 47t-226 135t-141.5 214t-49.5 284zM598 551 q0 -170 135 -170q86 0 140.5 58.5t68.5 164.5l12 101q6 49 7 75q0 70 -34 117t-118 47q-72 0 -118 -42t-68 -140q-10 -47 -17.5 -104.5t-7.5 -106.5z" />
<glyph unicode="A" d="M57 23q41 162 87 333.5t95.5 337.5t98.5 317.5t94 274.5q10 27 21.5 33t33.5 6h72q23 0 35 -6t22 -33q45 -123 93.5 -274.5t97.5 -317.5t96 -338t88 -333q4 -23 -12 -23h-68q-18 0 -20 16q-27 109 -57.5 223.5t-59.5 217.5h-500q-29 -102 -59.5 -217t-56.5 -224 q-2 -16 -21 -16h-67q-16 0 -13 23zM297 543h455q-23 76 -47.5 163t-52 177t-56.5 182t-57 178h-29q-29 -86 -57.5 -178t-56 -182t-52.5 -177.5t-47 -162.5z" />
<glyph unicode="B" horiz-adv-x="1024" d="M147 51v1215q0 33 35 43q25 8 58.5 14t70.5 11t74 7t70 2q197 0 296 -73.5t99 -233.5v-30q0 -121 -51 -199t-150 -100v-7q137 -14 210 -89.5t73 -213.5v-28q0 -104 -33 -175t-94.5 -114t-150 -61.5t-199.5 -18.5q-86 0 -152.5 2t-116.5 8q-39 6 -39 41zM242 119 q0 -25 24 -27q51 -6 183 -6q72 0 140 8t121.5 37t86 84t32.5 150v22q0 78 -22.5 129t-69.5 81t-122.5 41t-182.5 11h-190v-530zM242 735h170q76 0 137 11.5t105 42t69 85t25 140.5v18q0 129 -78 177t-221 48q-51 0 -102.5 -4t-86.5 -14q-18 -6 -18 -25v-479z" />
<glyph unicode="C" horiz-adv-x="1007" d="M115 604v119q0 109 7 191.5t23.5 146t46 111t72.5 83.5q55 47 133 67.5t176 20.5q197 0 340 -83q16 -10 9 -31l-17 -37q-10 -18 -31 -6q-70 35 -140 53t-159 18q-80 0 -143 -16t-110 -63q-66 -66 -87.5 -180.5t-21.5 -301.5v-65q0 -106 4 -180t14.5 -126.5t29 -91 t46.5 -73.5q41 -49 105.5 -70.5t179.5 -21.5q76 0 153.5 16t141.5 39q23 8 28 -14l11 -43q4 -12 0 -16.5t-13 -8.5q-66 -27 -152.5 -43t-178.5 -16q-143 0 -229.5 32.5t-139.5 100.5q-29 35 -47.5 75.5t-29.5 96t-16 132.5t-5 185z" />
<glyph unicode="D" horiz-adv-x="1163" d="M147 37v1259q0 16 7.5 26.5t29.5 14.5q31 2 57.5 4t56.5 4.5t64.5 2.5h77.5q150 0 263.5 -26t190.5 -95.5t116 -190.5t39 -313v-96q0 -162 -29 -281t-93.5 -195.5t-170 -113.5t-258.5 -37h-310q-41 0 -41 37zM242 104q0 -18 16 -18h188q135 0 222.5 18.5t146.5 71.5 q68 59 101.5 167t33.5 300v55q0 94 -8 164t-23.5 122t-39 91t-52.5 72q-63 70 -159 92.5t-219 22.5h-88.5t-94.5 -5q-25 -2 -24 -28v-1125z" />
<glyph unicode="E" horiz-adv-x="931" d="M129 604v117q0 98 3 206.5t9 200.5q4 49 14.5 84t32.5 60q51 53 172 53h476q23 0 22 -22v-41q0 -23 -22 -23h-469q-41 0 -62.5 -6t-34.5 -19q-14 -14 -20 -36.5t-10 -63.5q-6 -86 -9.5 -174t-3.5 -211h500q23 0 23 -22v-41q0 -23 -23 -23h-500q0 -82 1 -143.5t3 -112.5 t4.5 -93t4.5 -83q4 -41 10 -63.5t20 -36.5q12 -12 34 -18.5t63 -6.5h469q23 0 22 -23v-40q0 -23 -22 -23h-476q-61 0 -102 10t-68 39q-25 27 -35 63t-14 85q-10 96 -11 207.5t-1 199.5z" />
<glyph unicode="F" horiz-adv-x="876" d="M147 23v1091q0 127 56 170q29 23 68.5 32t97.5 9h419q23 0 23 -22v-39q0 -25 -23 -25h-411q-47 0 -70.5 -6t-38.5 -21q-16 -16 -21 -39.5t-5 -70.5v-367h473q23 0 22 -22v-41q0 -23 -22 -23h-473v-626q0 -23 -19 -23h-57q-18 0 -19 23z" />
<glyph unicode="G" horiz-adv-x="1124" d="M115 602v119q0 317 117.5 469.5t392.5 152.5q86 0 173 -13t156 -36q14 -4 19.5 -9t1.5 -19l-8 -39q-6 -23 -33 -15q-59 16 -139 30.5t-162 14.5q-109 0 -187.5 -25.5t-131 -90t-77 -173t-24.5 -274.5v-61q0 -154 21.5 -261.5t70.5 -175t130 -98t200 -30.5q61 0 125.5 5 t113.5 15q31 6 31 37v477h-229q-23 0 -23 23v41q0 23 23 22h303q20 0 20 -20v-602q0 -39 -38 -48q-61 -16 -152.5 -26t-177.5 -10q-147 0 -245.5 37.5t-159 114.5t-86 193.5t-25.5 274.5z" />
<glyph unicode="H" horiz-adv-x="1181" d="M147 23v1280q0 23 19 22h57q18 0 19 -22v-572h698v572q0 23 18 22h58q18 0 18 -22v-1280q0 -23 -18 -23h-58q-18 0 -18 23v622h-698v-622q0 -23 -19 -23h-57q-18 0 -19 23z" />
<glyph unicode="I" horiz-adv-x="413" d="M160 23v1280q0 23 18 22h58q18 0 18 -22v-1280q0 -23 -18 -23h-58q-18 0 -18 23z" />
<glyph unicode="J" horiz-adv-x="575" d="M41 23v40q0 23 22 23h76q139 0 178 78q20 41 20.5 97t0.5 128v914q0 23 18 22h58q18 0 18 -22v-910v-133t-24 -129q-27 -66 -89.5 -98.5t-179.5 -32.5h-76q-23 0 -22 23z" />
<glyph unicode="K" horiz-adv-x="974" d="M147 23v1280q0 23 19 22h57q18 0 19 -22v-578h164q27 0 39 6t24 25l379 557q8 12 29 12h63q23 0 10 -18l-387 -574q-16 -25 -16 -37q0 -10 16 -39l379 -630q14 -27 -14 -27h-62q-20 0 -30 16l-357 598q-10 18 -23.5 21.5t-41.5 3.5h-172v-616q0 -23 -19 -23h-57 q-18 0 -19 23z" />
<glyph unicode="L" horiz-adv-x="727" d="M147 160v1143q0 23 19 22h57q18 0 19 -22v-1125q0 -57 18.5 -74.5t71.5 -17.5h336q23 0 22 -23v-40q0 -23 -22 -23h-342q-96 0 -136 33q-43 37 -43 127z" />
<glyph unicode="M" horiz-adv-x="1486" d="M104 23q12 317 33 628.5t49 620.5q4 29 16.5 41t43.5 12h61q31 0 44.5 -10t25.5 -43q39 -111 85 -236t94 -252t94 -249.5t89 -229.5h9q43 106 89 229t94 250t93 252t86 236q12 33 25.5 43t44.5 10h61q31 0 43 -12.5t16 -40.5q29 -309 49.5 -620.5t32.5 -628.5 q0 -23 -16 -23h-61q-20 0 -21 16q-14 334 -31.5 632t-44.5 595h-12q-43 -121 -89 -248t-93 -250.5t-91 -238.5t-83 -211q-16 -39 -30.5 -52.5t-43.5 -13.5h-47q-29 0 -42 13.5t-30 52.5q-39 96 -83 211t-91 238.5t-93 250.5t-89 248h-12q-27 -297 -44.5 -595t-31.5 -632 q0 -16 -21 -16h-61q-16 0 -17 23z" />
<glyph unicode="N" horiz-adv-x="1220" d="M147 23v1237q0 39 14.5 52t49.5 13h41q41 0 58.5 -12.5t35.5 -48.5l623 -1182h12v1221q0 23 18 22h54q20 0 20 -22v-1242q0 -33 -12 -47t-43 -14h-55q-49 0 -72 43l-639 1200h-12v-1220q0 -23 -19 -23h-53q-20 0 -21 23z" />
<glyph unicode="O" horiz-adv-x="1163" d="M115 604v115q0 100 6 177t20.5 137.5t37 106.5t57.5 83q53 61 141 90.5t205 29.5t204.5 -29.5t141.5 -90.5q33 -37 56.5 -83t37.5 -106.5t20.5 -137.5t6.5 -177v-115q0 -98 -6.5 -175t-20.5 -137.5t-37.5 -106.5t-56.5 -83q-53 -61 -141 -90.5t-205 -29.5t-205 29.5 t-141 90.5q-35 37 -57.5 83t-37 106.5t-20.5 137.5t-6 175zM213 633q0 -176 19.5 -282.5t74.5 -178.5q41 -53 115 -78.5t160 -25.5t159.5 25.5t114.5 78.5q55 72 74.5 178.5t19.5 282.5v59q0 94 -4 165t-15 125t-29.5 96t-45.5 77q-41 53 -114.5 77.5t-159.5 24.5 t-160 -24.5t-115 -77.5q-29 -35 -46 -77t-28.5 -96t-15.5 -125t-4 -165v-59z" />
<glyph unicode="P" horiz-adv-x="991" d="M147 23v1253q0 37 31 43q57 10 126 17t140 7q127 0 217.5 -21.5t147.5 -69.5t84 -122.5t27 -179.5v-24q0 -209 -116 -306.5t-343 -97.5h-105.5t-113.5 2v-501q0 -23 -19 -23h-57q-18 0 -19 23zM242 614q98 -6 209 -6q94 0 163.5 15.5t115.5 52.5t68.5 98.5t22.5 153.5v18 q0 160 -84.5 235.5t-283.5 75.5q-53 0 -99.5 -4t-83.5 -10q-29 -6 -28 -37v-592z" />
<glyph unicode="Q" horiz-adv-x="1163" d="M115 604v115q0 100 6 177t20.5 137.5t37 106.5t57.5 83q53 61 141 90.5t205 29.5t204.5 -29.5t141.5 -90.5q33 -37 56.5 -83t37.5 -106.5t20.5 -137.5t6.5 -177v-115q0 -98 -6.5 -175t-20.5 -137.5t-37.5 -106.5t-56.5 -83q-47 -53 -118 -80.5t-163 -35.5 q47 -72 111.5 -132.5t165.5 -125.5q23 -14 -2 -33l-33 -25q-10 -8 -19.5 -9t-27.5 9q-84 51 -163 133t-142 181q-100 4 -176 32.5t-125 85.5q-35 37 -57.5 83t-37 106.5t-20.5 137.5t-6 175zM213 633q0 -176 19.5 -285.5t74.5 -181.5q41 -53 115 -79t160 -26t159.5 26 t114.5 79q55 72 74.5 181.5t19.5 285.5v59q0 94 -4 166t-15 127t-29.5 98t-45.5 78q-41 53 -114.5 78t-159.5 25t-160 -25t-115 -78q-29 -35 -46 -78t-28.5 -98t-15.5 -127t-4 -166v-59z" />
<glyph unicode="R" horiz-adv-x="1030" d="M147 23v1253q0 37 31 43q57 10 130 17t136 7q127 0 217.5 -19t147.5 -62t84 -114t27 -173v-23q0 -164 -71 -255t-208 -117l324 -555q14 -25 -15 -25h-65q-25 0 -35 18l-309 547q-20 -2 -41 -2h-43h-105.5t-109.5 4v-544q0 -23 -19 -23h-57q-18 0 -19 23zM242 655 q98 -6 202 -6q94 0 165 13.5t118 49.5t70.5 95t23.5 151v19q0 154 -84.5 217t-283.5 63q-53 0 -99.5 -4t-83.5 -10q-29 -6 -28 -37v-551z" />
<glyph unicode="S" horiz-adv-x="915" d="M88 74l14 47q6 20 27 10q59 -29 136 -46t163 -17q162 0 235.5 72.5t73.5 197.5v24q0 63 -11 107.5t-39 75t-74 51t-115 37.5l-117 29q-141 35 -216 109.5t-75 205.5v37q0 160 100.5 244.5t286.5 84.5q82 0 146.5 -10t117.5 -26q23 -6 17 -31l-8 -41q-6 -20 -31 -12 q-55 16 -119.5 25t-128.5 9q-139 0 -211 -61t-72 -182v-23q0 -61 18.5 -102t51.5 -68.5t76 -44t92 -28.5l115 -29q86 -23 142 -49.5t90 -65.5t48.5 -92t14.5 -125v-39q0 -96 -30 -165.5t-83 -113.5t-128 -65.5t-165 -21.5q-104 0 -185 19t-145 46q-20 8 -12 27z" />
<glyph unicode="T" horiz-adv-x="978" d="M47 1260v45q0 20 25 20h835q25 0 25 -20v-45q0 -20 -25 -21h-370v-1216q0 -23 -19 -23h-57q-18 0 -19 23v1216h-370q-25 0 -25 21z" />
<glyph unicode="U" horiz-adv-x="1167" d="M143 422v881q0 23 19 22h57q18 0 19 -22v-871q0 -100 16 -166.5t59 -113.5q41 -47 106.5 -70t164.5 -23q98 0 163.5 22.5t106.5 70.5q43 47 59.5 113.5t16.5 166.5v871q0 23 18 22h58q18 0 18 -22v-881q0 -111 -24.5 -196t-79.5 -138q-53 -53 -135 -79.5t-201 -26.5 t-201 26.5t-135 79.5q-55 53 -80 138t-25 196z" />
<glyph unicode="V" horiz-adv-x="1069" d="M57 1303q-4 23 13 22h67q14 0 21 -16q86 -307 176 -616.5t188 -610.5h25q98 301 188 610t174 617q6 16 21 16h67q20 0 13 -22q-51 -180 -97.5 -341t-92.5 -313.5t-94 -302t-99 -307.5q-10 -27 -26.5 -33t-39.5 -6h-55q-23 0 -38 6t-26 33q-53 158 -102 307.5t-95 302 t-92.5 313t-95.5 341.5z" />
<glyph unicode="W" horiz-adv-x="1609" d="M53 1303q-4 23 13 22h65q18 0 21 -20q23 -139 53.5 -295t64 -315.5t71.5 -316.5t77 -296h22q74 285 140.5 562.5t130.5 567.5q6 31 20 41.5t47 10.5h53q31 0 46.5 -10.5t21.5 -41.5q63 -291 131 -568t139 -562h23q39 139 77 296t71.5 316.5t64.5 315t53 295.5q2 20 21 20 h65q16 0 12 -22q-27 -154 -58.5 -315t-67 -320.5t-74.5 -315t-78 -299.5q-10 -35 -25.5 -44t-44.5 -9h-51q-31 0 -46 11.5t-26 45.5q-41 156 -77.5 302.5t-69.5 284.5t-62.5 272.5t-60.5 267.5h-20q-31 -133 -60.5 -267.5t-62.5 -272.5t-70 -284.5t-78 -302.5 q-10 -35 -25.5 -46t-45.5 -11h-52q-29 0 -44 9t-25 44q-41 143 -79 299t-74 315.5t-67.5 320.5t-58.5 315z" />
<glyph unicode="X" horiz-adv-x="1052" d="M57 18l295 633q4 10 8.5 18.5t4.5 16.5q0 12 -13 37l-272 584q-4 6 0 12t14 6h58q14 0 20 -4t14 -21l252 -538q10 -23 20.5 -30t28.5 -7h78q18 0 28.5 7t20.5 30l252 538q8 16 14.5 20.5t20.5 4.5h57q10 0 14.5 -6t0.5 -12l-273 -584q-12 -25 -12 -37q0 -8 4 -16t8 -19 l295 -633q8 -18 -14 -18h-70q-14 0 -22 16l-260 578q-14 31 -31.5 40t-50.5 9h-41q-33 0 -50.5 -9t-31.5 -40l-260 -578q-8 -16 -23 -16h-69q-25 0 -15 18z" />
<glyph unicode="Y" horiz-adv-x="954" d="M57 1303q-8 23 13 22h67q14 0 21 -16q139 -422 311 -734h16q172 311 312 734q6 16 20 16h68q20 0 12 -22q-45 -135 -87 -244t-85 -207t-92 -192.5t-109 -200.5v-436q0 -23 -18 -23h-57q-18 0 -19 23v436q-59 106 -108.5 200.5t-92.5 192.5t-85 206.5t-87 244.5z" />
<glyph unicode="Z" horiz-adv-x="968" d="M74 23v20q0 29 11 51.5t30 50.5l673 1102h-665q-16 0 -17 19v41q0 18 17 18h735q25 0 25 -25v-20q0 -16 -2 -30.5t-10.5 -31t-21.5 -40t-34 -58.5l-639 -1042h690q16 0 17 -17v-43q0 -6 -5.5 -12t-11.5 -6h-766q-27 0 -26 23z" />
<glyph unicode="[" horiz-adv-x="499" d="M143 -174v1556q0 14 15 15h295q14 0 14 -15v-55q0 -14 -14 -14h-211v-1417h211q14 0 14 -15v-55q0 -14 -14 -14h-295q-14 0 -15 14z" />
<glyph unicode="\" horiz-adv-x="630" d="M53 1393l428 -1428q6 -18 23 -18h57q23 0 17 20l-428 1428q-6 18 -23 18h-57q-20 0 -17 -20z" />
<glyph unicode="]" horiz-adv-x="499" d="M33 -119q0 14 14 15h211v1417h-211q-14 0 -14 14v55q0 14 14 15h295q14 0 14 -15v-1556q0 -14 -14 -14h-295q-14 0 -14 14v55z" />
<glyph unicode="^" d="M137 745l307 474q10 14 18.5 18t26.5 4h72q18 0 26.5 -4t18.5 -18l307 -474q12 -16 -6 -28l-49 -23q-18 -12 -31 6l-292 435h-19l-291 -435q-12 -18 -30 -6l-52 23q-18 12 -6 28z" />
<glyph unicode="_" horiz-adv-x="956" d="M41 -131q0 12 12 12h850q12 0 12 -12v-39q0 -12 -12 -12h-850q-12 0 -12 12v39z" />
<glyph unicode="`" horiz-adv-x="569" d="M70 1391l26 53q14 31 45 16l346 -194q16 -10 9 -33l-15 -33q-6 -12 -14 -14t-31 8l-340 154q-43 14 -26 43z" />
<glyph unicode="a" horiz-adv-x="927" d="M86 266v25q0 72 15.5 126t57.5 90t112.5 54.5t181.5 18.5h102q37 0 75 -2.5t72 -6.5v121q0 147 -62 198.5t-187 51.5q-68 0 -133.5 -11t-112.5 -26q-27 -8 -33 10l-8 37q-4 14 0 19.5t16 9.5q53 16 124 28.5t157 12.5q178 0 253 -83t75 -267v-654q0 -18 -19 -18h-45 q-18 0 -20 20l-7 82h-4q-35 -59 -97 -89.5t-179 -30.5h-41q-162 0 -227.5 65.5t-65.5 218.5zM176 252q0 -104 45 -147.5t141 -43.5h80q135 0 197.5 60.5t62.5 183.5v187q-29 4 -65.5 6t-71.5 2h-129q-141 0 -200.5 -44t-59.5 -153v-51z" />
<glyph unicode="b" horiz-adv-x="997" d="M135 340v1030q0 16 15 16h57q16 0 16 -16v-471h4q37 53 110 88t189 35q193 0 281 -112.5t88 -360.5v-96q0 -133 -25.5 -223.5t-75 -145.5t-122 -78.5t-168.5 -23.5q-201 0 -285 89t-84 269zM223 346q0 -156 72 -219.5t211 -63.5q82 0 139 20.5t92 68t51.5 125t16.5 192.5 v72q0 219 -70.5 309t-218.5 90q-160 0 -226.5 -76t-66.5 -217v-301z" />
<glyph unicode="c" horiz-adv-x="831" d="M102 475v70q0 154 27 245t84 144q90 88 270 88q90 0 162 -23.5t119 -58.5q16 -10 6 -27l-27 -39q-8 -12 -26 0q-100 66 -236 66q-74 0 -122 -16.5t-80 -46.5q-31 -31 -48.5 -70t-25.5 -84t-10 -95t-2 -104v-35q0 -125 17 -208.5t69 -143.5q29 -35 88 -54.5t133 -19.5 q47 0 106.5 10.5t108.5 30.5q20 8 28 -14l9 -27q6 -16 2 -22t-19 -12q-47 -20 -112.5 -33.5t-122.5 -13.5q-186 0 -283 90q-63 59 -89 160.5t-26 242.5z" />
<glyph unicode="d" horiz-adv-x="993" d="M102 449v100q0 244 84 358.5t291 114.5q104 0 178 -36t111 -95h4v479q0 16 14 16h58q16 0 16 -16v-1044q0 -78 -20.5 -141.5t-64.5 -108.5t-113.5 -69.5t-167.5 -24.5t-172 21.5t-122 75.5t-72 144t-24 226zM193 465q0 -115 17 -192.5t53 -124t92.5 -66t133.5 -19.5 q70 0 122.5 15.5t87 49.5t53 89t18.5 137v281q0 143 -74.5 224t-220.5 81q-74 0 -128 -20.5t-88 -67.5t-50 -125t-16 -190v-72z" />
<glyph unicode="e" horiz-adv-x="917" d="M102 455v92q0 248 85 361.5t282 113.5q342 0 342 -371v-123q0 -47 -18.5 -66.5t-65.5 -19.5h-534q2 -109 18 -181.5t52 -116.5t90.5 -62.5t133.5 -18.5q53 0 123 12.5t123 35.5q20 8 27 -9l12 -32q4 -14 2 -20.5t-18 -14.5q-55 -25 -129 -39t-140 -14q-106 0 -180 26.5 t-119 82.5t-65.5 146.5t-20.5 217.5zM195 528h499q16 0 22.5 4.5t6.5 28.5v64q0 172 -61.5 243.5t-192.5 71.5q-72 0 -123 -18.5t-84 -65.5t-49 -127t-18 -201z" />
<glyph unicode="f" horiz-adv-x="540" d="M45 934v39q0 10 12 14l129 17v75q0 68 8.5 111t28.5 82t50 64.5t67 41t77 21.5t79 6q41 0 81.5 -5t75.5 -16q16 -4 15 -26l-6 -35q-2 -12 -7.5 -14t-15.5 0q-29 6 -63.5 11t-69.5 5q-113 0 -162 -41q-41 -35 -55.5 -83t-14.5 -134v-63h224q16 0 16 -15v-53q0 -14 -16 -14 h-224v-906q0 -16 -16 -16h-57q-14 0 -15 16v906h-129q-12 0 -12 12z" />
<glyph unicode="g" horiz-adv-x="944" d="M69 -282.5q-3 6.5 1 18.5l16 39q8 16 29 6q72 -37 162 -59.5t182 -22.5q139 0 216 51t77 184v21q0 102 -53.5 165.5t-139.5 106.5q-29 -4 -54.5 -6t-55.5 -2q-188 0 -270.5 87t-82.5 284v65q0 104 22.5 175t66.5 113t111 60.5t153 18.5q23 0 43 -1t40 -3 q35 70 85.5 103.5t127.5 33.5h46q16 0 16 -14v-56q0 -16 -16 -16h-58q-43 0 -69.5 -16.5t-42.5 -55.5q45 -14 79.5 -37.5t58 -63.5t35 -98.5t11.5 -142.5v-65q0 -139 -36 -217t-107 -117q86 -53 133 -120.5t47 -172.5v-35q0 -90 -29 -149.5t-81 -95t-123.5 -51t-157.5 -15.5 q-51 0 -102.5 7t-100.5 19.5t-92 29t-76 34.5q-8 4 -11 10.5zM186 596q0 -168 63.5 -231.5t199.5 -63.5q70 0 119.5 12.5t82.5 45t48.5 91t15.5 150.5v47q0 88 -16.5 145.5t-50.5 90t-84 45t-115 12.5q-66 0 -115 -12.5t-82 -45t-49.5 -90t-16.5 -145.5v-51z" />
<glyph unicode="h" horiz-adv-x="1019" d="M150 16v1354q0 16 14 16h57q16 0 17 -16v-485q35 61 110.5 100t179.5 39q100 0 168 -25.5t109 -76t59.5 -125t18.5 -172.5v-609q0 -16 -17 -16h-57q-14 0 -14 16v600q0 170 -62.5 247t-216.5 77q-129 0 -203.5 -71.5t-74.5 -223.5v-629q0 -16 -17 -16h-57q-14 0 -14 16z " />
<glyph unicode="i" horiz-adv-x="448" d="M147 1266v12q0 43 20.5 60.5t53.5 17.5h8q31 0 49.5 -17.5t18.5 -60.5v-12q0 -43 -18.5 -61.5t-49.5 -18.5h-8q-41 0 -57.5 18.5t-16.5 61.5zM180 16v971q0 16 15 17h57q16 0 16 -17v-971q0 -16 -16 -16h-57q-14 0 -15 16z" />
<glyph unicode="j" horiz-adv-x="450" d="M-121 -299q0 16 17 16h63q135 0 184 82q27 45 32 94.5t5 100.5v993q0 16 15 17h57q16 0 16 -17v-985q0 -66 -7 -118t-28 -97q-39 -84 -105.5 -118t-158.5 -34h-71q-18 0 -19 19v47zM147 1266v12q0 43 20.5 60.5t53.5 17.5h8q31 0 49.5 -17.5t18.5 -60.5v-12 q0 -43 -18.5 -61.5t-49.5 -18.5h-8q-41 0 -57.5 18.5t-16.5 61.5z" />
<glyph unicode="k" horiz-adv-x="827" d="M150 16v1354q0 16 14 16h57q16 0 17 -16v-795h108q23 0 39 8.5t33 34.5l270 373q8 12 27 13h57q27 0 12 -23l-317 -434q-6 -8 -5 -12.5t5 -12.5l315 -499q14 -23 -12 -23h-55q-18 0 -29 16l-276 441q-12 23 -24.5 30t-35.5 7h-112v-478q0 -16 -17 -16h-57q-14 0 -14 16z " />
<glyph unicode="l" horiz-adv-x="479" d="M156 217v1153q0 16 14 16h57q16 0 17 -16v-1143q0 -90 22.5 -117.5t81.5 -27.5h53q14 0 15 -14v-52q0 -16 -17 -16h-47q-109 0 -152.5 46t-43.5 171z" />
<glyph unicode="m" horiz-adv-x="1642" d="M150 16v973q0 14 14 15h45q18 0 18 -17l6 -98h5q35 61 106.5 97t175.5 36q117 0 188.5 -35t116.5 -113h4q41 59 121 103.5t207 44.5q223 0 303 -143q27 -47 36 -107.5t9 -146.5v-609q0 -16 -16 -16h-57q-14 0 -15 16v600q0 121 -24.5 185.5t-77.5 97.5q-31 18 -71 29.5 t-97 11.5q-100 0 -170 -36t-119 -101q14 -78 14 -178v-609q0 -16 -16 -16h-57q-14 0 -15 16v615q0 170 -65.5 239.5t-206.5 69.5q-135 0 -204.5 -72.5t-69.5 -214.5v-637q0 -16 -17 -16h-57q-14 0 -14 16z" />
<glyph unicode="n" horiz-adv-x="1019" d="M150 16v973q0 14 14 15h45q18 0 18 -17l6 -94h3q37 59 109.5 94t182.5 35q229 0 310 -143q27 -47 36 -107.5t9 -146.5v-609q0 -16 -17 -16h-57q-14 0 -14 16v600q0 121 -25 185.5t-78 97.5q-31 18 -70.5 28.5t-93.5 12.5h-15q-59 0 -107 -14q-54 -16 -90.5 -50t-57 -91.5 t-20.5 -139.5v-629q0 -16 -17 -16h-57q-14 0 -14 16z" />
<glyph unicode="o" horiz-adv-x="962" d="M102 457v90q0 127 21.5 229.5t89.5 161.5q43 37 106.5 60.5t161.5 23.5t163 -22.5t106 -61.5q66 -59 88 -161.5t22 -229.5v-90q0 -127 -22.5 -229.5t-87.5 -161.5q-41 -39 -105.5 -61.5t-163.5 -22.5q-98 0 -162.5 22.5t-105.5 61.5q-66 59 -88.5 161.5t-22.5 229.5z M193 481q0 -127 12 -196.5t43 -116.5q70 -104 233 -105q162 0 234 105q31 47 43 116.5t12 196.5v41q0 127 -12.5 196.5t-42.5 117.5q-72 104 -234 104q-164 0 -233 -104q-31 -47 -43 -117t-12 -197v-41z" />
<glyph unicode="p" horiz-adv-x="1013" d="M150 -348v1337q0 14 14 15h45q18 0 18 -17l6 -104h5q41 66 112.5 102.5t194.5 36.5q109 0 179.5 -29.5t112.5 -86t58 -141.5t16 -196v-118q0 -113 -16 -200t-57 -147.5t-111 -91t-174 -30.5q-68 0 -118 9t-86 26.5t-61.5 42t-43.5 55.5h-6v-463q0 -16 -17 -17h-57 q-14 0 -14 17zM238 346q0 -135 81.5 -209t225.5 -74q74 0 126 16.5t86 61.5t49 123t15 201v86q0 94 -11 167t-42 122t-86 74.5t-143 25.5q-158 0 -229.5 -73.5t-71.5 -209.5v-311z" />
<glyph unicode="q" horiz-adv-x="1015" d="M102 432v107q0 137 22.5 230t69 149.5t118 80t171.5 23.5q209 0 291 -127h4l8 88q0 20 21 21h39q10 0 15 -2.5t5 -16.5v-1333q0 -16 -16 -17h-57q-14 0 -15 17v457h-6q-47 -61 -115.5 -94t-173.5 -33q-201 0 -291 105.5t-90 344.5zM193 442q0 -102 15 -173.5t50 -117 t93.5 -67t142.5 -21.5q129 0 206.5 69t77.5 198v323q0 143 -80 215t-219 72q-76 0 -130 -20.5t-89 -69.5t-51 -130t-16 -202v-76z" />
<glyph unicode="r" horiz-adv-x="581" d="M150 16v973q0 14 14 15h41q20 0 22 -17l13 -100h2q29 53 82 85t141 32h63q16 0 17 -17v-47q0 -18 -19 -18h-61q-70 0 -114 -20.5t-68 -61.5q-29 -45 -37 -89t-8 -92v-643q0 -16 -17 -16h-57q-14 0 -14 16z" />
<glyph unicode="s" horiz-adv-x="782" d="M83 42q-3 5 1 17l10 35q6 18 31 10q59 -20 119.5 -32.5t130.5 -12.5q74 0 121 13.5t72.5 39t34.5 63.5t9 87v10q0 53 -7 88t-28.5 58.5t-58.5 37t-98 23.5l-92 17q-141 25 -190.5 89t-49.5 169v8q0 135 82 197.5t236 62.5q59 0 118.5 -9t100.5 -20q23 -6 16 -26l-8 -33 q-4 -18 -29 -12q-47 10 -98 16t-107 6q-119 0 -171 -45t-52 -135v-8q0 -45 10.5 -77t35 -53.5t64.5 -34.5t99 -24l92 -16q59 -10 102.5 -29.5t70 -50.5t38.5 -77t12 -109v-19q0 -156 -79.5 -220t-253.5 -64q-76 0 -151 16t-120 35q-10 4 -13 9z" />
<glyph unicode="t" horiz-adv-x="602" d="M18 934v39q0 10 13 14l143 17v239q0 16 16 17h56q16 0 16 -17v-239h250q16 0 16 -15v-53q0 -14 -16 -14h-250v-596q0 -72 5 -117t34 -78q23 -25 64 -39t110 -14h62q16 0 16 -17v-45q0 -16 -16 -16h-74q-92 0 -149.5 23.5t-86.5 58.5q-35 45 -44 96t-9 137v607h-143 q-12 0 -13 12z" />
<glyph unicode="u" horiz-adv-x="987" d="M139 301v686q0 16 17 17h55q16 0 16 -17v-674q0 -80 13.5 -125t42.5 -71q35 -31 81 -44.5t130 -13.5t130 13.5t81 44.5q29 27 42 71.5t13 124.5v674q0 16 16 17h55q16 0 17 -17v-686q0 -86 -17.5 -144.5t-62.5 -99.5q-41 -37 -104.5 -56t-169.5 -19t-170 19t-105 56 q-45 41 -62.5 99.5t-17.5 144.5z" />
<glyph unicode="v" horiz-adv-x="862" d="M49 983q-6 20 14 21h52q20 0 24 -17q129 -518 281 -901h22q152 383 281 901q4 16 25 17h51q20 0 14 -21q-68 -281 -142.5 -507t-152.5 -423q-10 -27 -24.5 -40t-42.5 -13h-39q-29 0 -43.5 13.5t-24.5 39.5q-78 197 -152.5 423t-142.5 507z" />
<glyph unicode="w" horiz-adv-x="1343" d="M49 983q-4 16 1 18.5t20 2.5h49q10 0 14 -2.5t8 -14.5q41 -229 94.5 -454.5t116.5 -446.5h23q53 193 108.5 406t106.5 419q6 27 21.5 35t45.5 8h29q31 0 46.5 -8t21.5 -35q51 -207 106 -419.5t109 -405.5h22q63 221 117.5 446.5t95.5 454.5q2 16 21 17h49q23 0 20 -21 q-45 -236 -99 -468t-124 -456q-10 -35 -26.5 -47t-49.5 -12h-30q-37 0 -51.5 15.5t-24.5 43.5q-27 90 -54.5 192.5t-55 207t-52.5 206t-45 191.5h-20q-20 -90 -45 -191.5t-52.5 -206t-55.5 -206.5t-54 -193q-10 -29 -24.5 -44t-51.5 -15h-31q-33 0 -49 12.5t-27 46.5 q-70 223 -124 456t-99 468z" />
<glyph unicode="x" horiz-adv-x="835" d="M53 18l232 471q8 14 9 24.5t-7 25.5l-217 444q-10 20 10 21h57q16 0 25 -13l188 -401q10 -18 18.5 -24.5t24.5 -6.5h45q16 0 24.5 6t18.5 25l195 401q8 12 24 13h58q20 0 10 -21l-223 -444q-8 -14 -7 -24.5t9 -25.5l235 -469q10 -20 -10 -20h-61q-20 0 -25 12l-209 437 q-10 18 -18 23t-29 5h-31q-23 0 -30 -5t-15 -23l-202 -437q-4 -12 -25 -12h-64q-20 0 -10 18z" />
<glyph unicode="y" horiz-adv-x="831" d="M49 983q-6 20 14 21h54q18 0 22 -19q12 -53 30.5 -132t44.5 -177t58.5 -210t69.5 -228q12 -37 22.5 -62.5t21.5 -40t26.5 -20.5t38.5 -6h10q66 174 126 389t107 489q2 16 21 17h53q20 0 14 -21q-57 -264 -107 -454.5t-99.5 -342t-102.5 -281.5t-117 -274q-8 -20 -26 -12 l-47 21q-16 8 -9 24q45 90 84 179t74 184q-53 2 -86 26.5t-59 93.5q-33 90 -62.5 180.5t-57.5 190.5t-56.5 214t-61.5 251z" />
<glyph unicode="z" horiz-adv-x="790" d="M82 14v25q0 16 7 33.5t28 50.5l497 799h-497q-16 0 -17 14v53q0 14 17 15h592q16 0 16 -15v-20q0 -16 -7 -35t-30 -57l-498 -795h504q14 0 15 -14v-54q0 -14 -15 -14h-596q-16 0 -16 14z" />
<glyph unicode="{" horiz-adv-x="569" d="M102 580v51q0 18 15 20q90 20 129 65.5t39 145.5v348q0 102 46 144.5t134 42.5h74q14 0 14 -15v-55q0 -14 -14 -14h-66q-53 0 -74.5 -24.5t-21.5 -117.5v-292q0 -125 -42 -189.5t-145 -81.5v-6q102 -29 144.5 -92.5t42.5 -192.5v-280q0 -92 21.5 -116.5t74.5 -24.5h66 q14 0 14 -15v-55q0 -14 -14 -14h-74q-88 0 -134 42t-46 144v330q0 106 -39 157.5t-129 73.5q-14 2 -15 21z" />
<glyph unicode="|" horiz-adv-x="370" d="M143 -29v1428q0 18 17 18h51q16 0 16 -18v-1428q0 -18 -16 -18h-51q-16 0 -17 18z" />
<glyph unicode="}" horiz-adv-x="569" d="M16 -119q0 14 15 15h65q53 0 75 24.5t22 116.5v280q0 129 41.5 192.5t144.5 92.5v6q-102 16 -144 81t-42 190v292q0 92 -21.5 117t-75.5 25h-65q-14 0 -15 14v55q0 14 15 15h73q88 0 134.5 -42t46.5 -145v-348q0 -100 38.5 -145t129.5 -66q14 -2 14 -20v-51 q0 -18 -14 -21q-90 -23 -129 -74t-39 -157v-330q0 -102 -46 -144t-135 -42h-73q-14 0 -15 14v55z" />
<glyph unicode="~" d="M50 570q-5 5 1 22q53 127 116.5 179t143.5 52q45 0 86 -17.5t86 -47.5l117 -80q41 -27 70.5 -40t64.5 -13q51 0 96.5 40t88.5 126q8 16 26 4l41 -25q12 -8 13 -14t-3 -17q-55 -121 -126.5 -169t-131.5 -48q-59 0 -100 16.5t-96 53.5l-109 74q-39 29 -67.5 42t-57.5 13 q-20 0 -39.5 -6t-41 -24.5t-43 -52.5t-46.5 -89q-6 -14 -14 -14t-23 6l-43 20z" />
<glyph unicode="&#xa1;" horiz-adv-x="442" d="M143 909q0 -35 14.5 -54.5t51.5 -19.5h24q37 0 51.5 19.5t14.5 54.5v41q0 33 -14.5 52t-51.5 19h-24q-37 0 -51.5 -19t-14.5 -52v-41zM172 -293q0 -20 21 -21h55q20 0 20 21l-8 989q0 20 -16 20h-47q-16 0 -17 -20z" />
<glyph unicode="&#xa2;" d="M184 475v70q0 152 27 239.5t84 141.5q84 78 235 84v204q0 18 19 19h41q20 0 20 -19v-204q72 -6 135.5 -27.5t104.5 -50.5q18 -12 8 -29l-22 -37q-8 -14 -29 -2q-41 25 -93 42.5t-104 23.5v-858q45 2 95.5 13t93.5 28q20 8 28 -15l9 -26q4 -16 1 -21.5t-18 -11.5 q-41 -18 -98 -30.5t-111 -16.5v-191q0 -18 -20 -18h-41q-18 0 -19 18v193q-147 8 -231 86q-63 59 -89 158.5t-26 236.5zM274 489q0 -123 17.5 -204t68.5 -140q23 -29 68 -47t102 -24v856q-59 -4 -100 -19.5t-70 -40.5q-61 -59 -73.5 -150t-12.5 -196v-35z" />
<glyph unicode="&#xa3;" d="M133 23v16q0 16 5 25.5t30 29.5q80 66 114.5 155t34.5 224v178h-114q-20 0 -21 17v32q0 20 21 23l114 18v236q0 98 19.5 164.5t64.5 111.5q90 90 267 90q68 0 135 -11t115 -27q23 -8 16 -27l-12 -39q-4 -10 -9.5 -13t-21.5 1q-53 16 -107.5 24.5t-109.5 8.5 q-72 0 -124 -15.5t-81 -52.5q-29 -35 -41 -90t-12 -148v-215h383q18 0 18 -16v-55q0 -16 -18 -17h-385v-162q0 -129 -29 -227t-106 -168v-6h636q23 0 23 -22v-41q0 -25 -23 -25h-759q-23 0 -23 23z" />
<glyph unicode="&#xa4;" d="M78 172q-16 14 0 31l155 153q-35 45 -53 99.5t-18 117.5t18.5 117.5t52.5 100.5l-155 153q-16 14 0 31l35 35q16 16 32 0l152 -150q98 76 231 76q127 0 224 -74l151 148q16 16 33 0l35 -35q14 -16 0 -31l-154 -149q35 -47 55.5 -103t20.5 -119t-20.5 -118.5t-55.5 -102.5 l154 -149q14 -16 0 -31l-35 -35q-16 -16 -33 0l-151 148q-96 -74 -224 -74q-133 0 -231 76l-152 -150q-16 -16 -32 0zM260 573q0 -59 21.5 -110t57.5 -88t85 -57.5t104 -20.5t103.5 20.5t84.5 57.5t57.5 88t21.5 110t-21.5 110.5t-57.5 88.5t-84 57.5t-104 20.5 q-55 0 -104 -20.5t-85 -57.5t-57.5 -88t-21.5 -111z" />
<glyph unicode="&#xa5;" d="M104 1303q-8 23 13 22h67q14 0 21 -16q70 -211 149.5 -407.5t163.5 -356.5h10q82 160 164 356.5t152 407.5q6 16 20 16h68q20 0 12 -22q-41 -121 -80 -223.5t-79 -193.5t-80.5 -174t-86.5 -167h224q14 0 14 -13v-61q0 -12 -14 -12h-271v-121h271q14 0 14 -12v-62 q0 -12 -14 -12h-271v-229q0 -23 -18 -23h-57q-18 0 -19 23v229h-274q-14 0 -15 12v62q0 12 15 12h274v121h-274q-14 0 -15 12v61q0 12 15 13h225q-45 84 -86 167t-80 174t-78 193.5t-80 223.5z" />
<glyph unicode="&#xa6;" horiz-adv-x="370" d="M143 -29v484q0 18 17 18h51q16 0 16 -18v-484q0 -18 -16 -18h-51q-16 0 -17 18zM143 913v486q0 18 17 18h51q16 0 16 -18v-486q0 -18 -16 -18h-51q-16 0 -17 18z" />
<glyph unicode="&#xa7;" horiz-adv-x="880" d="M139 674v20q0 72 38 125t110 76q-145 43 -146 209v18q0 63 26 111.5t69 75.5q76 47 213 47q57 0 112 -9.5t96 -21.5q14 -4 16.5 -10t0.5 -17l-8 -34q-4 -20 -29 -13q-41 12 -93 19.5t-98 7.5q-33 0 -73.5 -3t-77.5 -24q-66 -35 -66 -131v-12q0 -72 42 -108.5t145 -57.5 l112 -22q117 -23 164 -73t47 -153v-18q0 -66 -36.5 -120t-104.5 -83q74 -23 106.5 -75t32.5 -144v-18q0 -74 -23.5 -122t-66.5 -78t-102.5 -42t-128.5 -12q-68 0 -140.5 14t-119.5 33q-10 4 -14.5 9t-0.5 17l11 39q4 14 11 15.5t21 -3.5q47 -16 108.5 -29.5t123.5 -13.5 q66 0 110.5 10.5t73.5 35.5q27 20 38 52t11 72v13q0 39 -6 66.5t-25.5 48t-54.5 35t-90 24.5l-115 22q-127 25 -173 78t-46 154zM227 680q0 -35 6.5 -60.5t23.5 -44t49 -32t83 -23.5l121 -24q76 29 108.5 74.5t32.5 105.5v12q0 35 -7 59.5t-25.5 43t-52.5 32t-85 23.5 l-104 20q-76 -12 -113 -57t-37 -117v-12z" />
<glyph unicode="&#xa8;" horiz-adv-x="663" d="M86 1296v43q0 39 15.5 52.5t54.5 13.5h20q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-20q-41 0 -55.5 13.5t-14.5 49.5zM416 1296v43q0 39 15 52.5t54 13.5h21q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-21q-41 0 -55 13.5t-14 49.5z " />
<glyph unicode="&#xa9;" horiz-adv-x="1630" d="M82 713q0 174 57.5 312t156.5 233.5t232.5 145.5t286.5 50q154 0 287 -50t232.5 -145.5t156.5 -233.5t57 -312t-57 -312.5t-156.5 -233.5t-232.5 -145.5t-287 -50.5t-287 50.5t-232 145.5t-156.5 233.5t-57.5 312.5zM176 713q0 -152 49 -273t135 -204.5t203 -129 t252 -45.5t252 45.5t203 129t135 204.5t49 273t-49 272.5t-135 204.5t-203 129t-252 45t-252 -45t-203 -129t-135 -205t-49 -272zM502 713v24q0 129 27.5 217t78.5 142q82 84 234 84q55 0 107 -13.5t104 -38.5q18 -10 8 -30l-12 -29q-8 -18 -25 -10q-41 18 -86 29.5 t-92 11.5q-139 0 -195.5 -86t-56.5 -285t54 -289t194 -90q51 0 97 12.5t89 30.5q18 8 25 -10l10 -37q6 -18 -12 -27q-49 -23 -103.5 -36t-109.5 -13q-74 0 -134.5 20.5t-101.5 63.5q-100 102 -100 359z" />
<glyph unicode="&#xaa;" horiz-adv-x="608" d="M74 784v21q0 88 52 130t181 42h41q23 0 48.5 -1t43.5 -3v61q0 68 -32.5 94.5t-104.5 26.5q-43 0 -84 -7t-76 -18q-18 -4 -20 11l-6 37q-2 12 10 18q35 12 86 20.5t100 8.5q113 0 161 -45t48 -156v-389q0 -14 -14 -14h-37q-16 0 -18 14l-7 51h-4q-39 -76 -151 -76h-39 q-86 0 -132 38t-46 136zM160 774q0 -96 96 -96h47q78 0 107.5 31.5t29.5 85.5v110q-12 2 -29.5 3t-39.5 1h-68q-82 0 -112.5 -23.5t-30.5 -78.5v-33z" />
<glyph unicode="&#xab;" horiz-adv-x="915" d="M92 457v35q0 14 10 24l279 234q14 10 25 0l32 -33q10 -12 -2 -25l-233 -217l233 -221q4 -4 6 -11t-4 -14l-32 -32q-10 -10 -25 0l-279 235q-10 10 -10 25zM450 457v35q0 14 10 24l279 234q14 10 25 0l32 -33q10 -12 -2 -25l-233 -217l233 -221q4 -4 6 -11t-4 -14l-32 -32 q-10 -10 -25 0l-279 235q-10 10 -10 25z" />
<glyph unicode="&#xac;" d="M109 504v61q0 14 12 15h805q12 0 12 -13v-463q0 -12 -12 -12h-60q-14 0 -14 12v388h-731q-12 0 -12 12z" />
<glyph unicode="&#xad;" horiz-adv-x="563" d="M72 504v61q0 14 14 15h391q14 0 15 -15v-61q0 -14 -15 -15h-391q-14 0 -14 15z" />
<glyph unicode="&#xae;" horiz-adv-x="1630" d="M82 713q0 174 57.5 312t156.5 233.5t232.5 145.5t286.5 50q154 0 287 -50t232.5 -145.5t156.5 -233.5t57 -312t-57 -312.5t-156.5 -233.5t-232.5 -145.5t-287 -50.5t-287 50.5t-232 145.5t-156.5 233.5t-57.5 312.5zM176 713q0 -152 49 -273t135 -204.5t203 -129 t252 -45.5t252 45.5t203 129t135 204.5t49 273t-49 272.5t-135 204.5t-203 129t-252 45t-252 -45t-203 -129t-135 -205t-49 -272zM559 303v807q0 18 6 29.5t31 15.5q29 6 83 11t105 5q154 0 228 -67q68 -66 67 -189v-12q0 -51 -14 -99t-49 -83q-43 -45 -121 -64l178 -348 q12 -25 -14 -24h-43q-16 0 -22.5 3t-14.5 19l-174 344h-160v-348q0 -18 -20 -18h-45q-20 0 -21 18zM645 727h137q66 0 103 12.5t61 34.5q45 45 45 135v4q0 96 -47 137.5t-158 41.5q-35 0 -68.5 -2.5t-49.5 -6.5t-19.5 -9t-3.5 -21v-326z" />
<glyph unicode="&#xaf;" horiz-adv-x="657" d="M72 1296v43q0 25 22 25h469q23 0 23 -25v-43q0 -23 -23 -22h-469q-23 0 -22 22z" />
<glyph unicode="&#xb0;" horiz-adv-x="602" d="M70 1229q0 96 63.5 160.5t167.5 64.5t167.5 -64.5t63.5 -160.5q0 -47 -16 -89t-47 -73t-74 -48.5t-94 -17.5q-53 0 -95 17.5t-73 48.5t-47 73t-16 89zM158 1229q0 -66 40 -109t103 -43t103 43t40 109t-39.5 107.5t-103.5 41.5q-63 0 -103 -42t-40 -107z" />
<glyph unicode="&#xb1;" d="M109 12v62q0 14 12 14h805q12 0 12 -12v-64q0 -12 -12 -12h-805q-12 0 -12 12zM109 698v66q0 10 12 10h358v356q0 14 15 15h61q12 0 12 -15v-356h359q12 0 12 -12v-64q0 -12 -12 -12h-359v-358q0 -14 -12 -15h-61q-14 0 -15 15v358h-358q-12 0 -12 12z" />
<glyph unicode="&#xb2;" horiz-adv-x="618" d="M84 948v37q0 98 28.5 155.5t104.5 94.5l109 53q55 29 75.5 57.5t20.5 77.5v13q0 66 -33 85t-100 19q-37 0 -78 -7t-74 -17q-18 -6 -20 8l-8 39q-2 14 10 20q37 12 87 20.5t91 8.5q104 0 159.5 -37t55.5 -129v-12q0 -74 -25.5 -119t-103.5 -82l-104 -51q-66 -31 -86.5 -68 t-20.5 -98v-15h336q12 0 12 -12v-45q0 -12 -12 -12h-408q-16 0 -16 16z" />
<glyph unicode="&#xb3;" horiz-adv-x="618" d="M100 961l9 43q4 14 16 8q29 -10 67.5 -18.5t79.5 -8.5q82 0 119 28.5t37 84.5v14q0 63 -38 93t-126 30h-41q-12 0 -12 14v41q0 16 14 17h39q86 0 116 30.5t30 89.5v15q0 51 -28 74.5t-93 23.5q-35 0 -73 -6t-66 -16q-18 -6 -21 8l-8 37q-2 14 10 20q31 10 79 19.5t95 9.5 q92 0 143.5 -40t51.5 -124v-16q0 -61 -29 -102.5t-80 -55.5v-4q53 -4 90 -44t37 -106v-20q0 -92 -64.5 -139.5t-164.5 -47.5q-49 0 -92 7.5t-91 23.5q-10 4 -6 17z" />
<glyph unicode="&#xb4;" horiz-adv-x="569" d="M66 1233q-8 23 8 33l346 194q31 14 45 -16l27 -53q16 -29 -27 -43l-340 -154q-23 -10 -31 -8t-14 14z" />
<glyph unicode="&#xb5;" horiz-adv-x="1103" d="M150 -348v1335q0 16 16 17h55q16 0 17 -17v-700q0 -129 62 -178.5t192 -49.5q55 0 105 15.5t89 51.5t61.5 92t22.5 140v629q0 16 16 17h56q16 0 16 -17v-825q0 -63 18.5 -83t53.5 -20q18 0 41.5 5.5t36.5 9.5q12 6 18 -11l8 -32q4 -14 -8 -23q-47 -27 -115 -26 q-63 0 -98 36.5t-37 102.5h-6q-35 -61 -101.5 -100t-183.5 -39q-98 0 -156.5 23.5t-90.5 66.5v-420q0 -16 -17 -17h-55q-16 0 -16 17z" />
<glyph unicode="&#xb6;" horiz-adv-x="1140" d="M98 1061q0 76 28 142.5t78 116.5t121.5 78.5t159.5 28.5h545q27 0 27 -24v-39q0 -25 -27 -25h-158v-1564q0 -23 -22 -23h-51q-25 0 -25 23v1564h-211v-1564q0 -23 -22 -23h-52q-25 0 -24 23v921q-70 0 -136.5 23.5t-117.5 70t-82 114t-31 157.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="378" d="M109 571v55q0 68 69 68h21q41 0 56 -16.5t15 -51.5v-55q0 -35 -15 -51t-56 -16h-21q-70 0 -69 67z" />
<glyph unicode="&#xb8;" horiz-adv-x="458" d="M55 -426l8 45q4 16 21 10q29 -8 64.5 -12t64.5 -4q59 0 89 21.5t30 76.5q0 76 -80 76q-23 0 -44.5 -3t-35.5 -7q-14 -2 -22 2l-15 8q-6 4 -8 7t0 13l25 179q2 12 14 12h57q14 0 13 -14l-17 -127q14 4 31.5 6t38.5 2q59 0 97 -35t38 -113q0 -184 -215 -184q-37 0 -78 6 t-63 15q-16 4 -13 20z" />
<glyph unicode="&#xb9;" horiz-adv-x="618" d="M74 1460q-8 10 6 21l121 86q23 16 35 21t30 5h62q27 0 26 -22v-570h170q12 0 13 -12v-45q0 -12 -13 -12h-432q-12 0 -12 12v45q0 12 12 12h172v517h-6l-135 -99q-12 -10 -21 2z" />
<glyph unicode="&#xba;" horiz-adv-x="641" d="M74 893v49q0 162 59.5 222.5t185.5 60.5q127 0 187.5 -60.5t60.5 -222.5v-49q0 -82 -15 -136.5t-46 -87t-77 -46t-110 -13.5q-63 0 -109 13.5t-77 46t-45 87t-14 136.5zM160 909q0 -70 8 -114.5t27.5 -70.5t50 -36t73.5 -10q45 0 76 10t50.5 36t27.5 71t8 114v13 q0 137 -35.5 186t-126.5 49q-88 0 -123.5 -49t-35.5 -186v-13z" />
<glyph unicode="&#xbb;" horiz-adv-x="923" d="M115 243q-2 -7 4 -14l32 -32q10 -10 25 0l279 235q10 10 10 25v35q0 14 -10 24l-279 234q-14 10 -25 0l-32 -33q-10 -12 2 -25l233 -217l-233 -221q-4 -4 -6 -11zM482 243q-2 -7 4 -14l32 -32q10 -10 25 0l279 235q10 10 10 25v35q0 14 -10 24l-279 234q-14 10 -25 0 l-32 -33q-10 -12 2 -25l233 -217l-233 -221q-4 -4 -6 -11z" />
<glyph unicode="&#xbc;" horiz-adv-x="1564" d="M117 1192q-8 10 6 20l121 86q23 16 35 21.5t30 5.5h62q27 0 26 -22v-570h170q12 0 13 -12v-45q0 -12 -13 -12h-432q-12 0 -12 12v45q0 12 12 12h172v516h-6l-135 -98q-12 -10 -21 2zM336 -14l770 1343q6 14 22 14h62q16 0 8 -18l-770 -1343q-8 -14 -22 -15h-62 q-20 0 -8 19zM860 193v47q0 12 17 34l258 371q8 10 13 14.5t19 4.5h90q16 0 17 -17v-397h78q12 0 12 -10v-50q0 -10 -12 -10h-78v-166q0 -14 -12 -14h-62q-16 0 -16 14v166h-307q-16 0 -17 13zM948 250h236v332h-8z" />
<glyph unicode="&#xbd;" horiz-adv-x="1564" d="M117 1194q-8 10 6 21l121 86q23 16 35 21t30 5h62q27 0 26 -22v-570h170q12 0 13 -12v-45q0 -12 -13 -12h-432q-12 0 -12 12v45q0 12 12 12h172v517h-6l-135 -99q-12 -10 -21 2zM336 -14l770 1343q6 14 23 14h61q16 0 8 -18l-770 -1343q-8 -14 -22 -15h-62q-20 0 -8 19z M989 18v37q0 98 28.5 155.5t104.5 94.5l109 53q55 29 75.5 57.5t20.5 77.5v13q0 66 -33 85t-100 19q-37 0 -78 -7t-74 -17q-18 -6 -20 8l-8 39q-2 14 10 20q37 12 87 20.5t91 8.5q104 0 159.5 -37t55.5 -129v-12q0 -74 -25.5 -119t-103.5 -82l-104 -51q-66 -31 -86.5 -68 t-20.5 -98v-15h336q12 0 12 -12v-45q0 -12 -12 -12h-408q-16 0 -16 16z" />
<glyph unicode="&#xbe;" horiz-adv-x="1564" d="M168 692l8 43q4 14 17 8q29 -10 67.5 -18t79.5 -8q82 0 119 28.5t37 83.5v15q0 63 -38 93t-126 30h-41q-12 0 -12 14v41q0 16 14 16h39q86 0 115.5 31t29.5 90v15q0 51 -27.5 74.5t-93.5 23.5q-35 0 -72.5 -6t-66.5 -17q-18 -6 -20 8l-9 37q-2 14 11 21q31 10 79 19t95 9 q92 0 143 -39.5t51 -123.5v-17q0 -61 -28.5 -102t-79.5 -55v-5q53 -4 90 -43.5t37 -105.5v-21q0 -92 -64.5 -139t-165.5 -47q-49 0 -92 7t-90 24q-10 4 -6 16zM336 -14l770 1343q6 14 22 14h62q16 0 8 -18l-770 -1343q-8 -14 -22 -15h-62q-20 0 -8 19zM860 193v47 q0 12 17 34l258 371q8 10 13 14.5t19 4.5h90q16 0 17 -17v-397h78q12 0 12 -10v-50q0 -10 -12 -10h-78v-166q0 -14 -12 -14h-62q-16 0 -16 14v166h-307q-16 0 -17 13zM948 250h236v332h-8z" />
<glyph unicode="&#xbf;" horiz-adv-x="907" d="M102 4q0 96 45 172.5t152 148.5l102 70q43 31 69.5 54.5t41 51t19.5 64.5t5 90v57q0 23 19 23h57q18 0 19 -23v-57q0 -61 -5.5 -105.5t-23 -80t-48 -66.5t-81.5 -66l-101 -67q-47 -33 -79.5 -59.5t-53 -56.5t-30 -65.5t-9.5 -86.5v-15q0 -129 75 -188.5t218 -59.5 q78 0 153 14.5t138 37.5q18 6 21 -13l10 -43q2 -16 -10 -22q-59 -23 -145.5 -38t-174.5 -15q-203 0 -293 86t-90 233v25zM504 921v29q0 33 14 52t51 19h25q37 0 51 -19t14 -52v-29q0 -35 -14 -54.5t-51 -19.5h-25q-37 0 -51 19.5t-14 54.5z" />
<glyph unicode="&#xc0;" d="M57 23q41 162 87 333.5t95.5 337.5t98.5 317.5t94 274.5q10 27 21.5 33t33.5 6h72q23 0 35 -6t22 -33q45 -123 93.5 -274.5t97.5 -317.5t96 -338t88 -333q4 -23 -12 -23h-68q-18 0 -20 16q-27 109 -57.5 223.5t-59.5 217.5h-500q-29 -102 -59.5 -217t-56.5 -224 q-2 -16 -21 -16h-67q-16 0 -13 23zM233 1618l21 56q12 33 43 20l367 -152q18 -8 12 -30l-12 -35q-4 -14 -12.5 -17.5t-30.5 5.5l-357 112q-43 12 -31 41zM297 543h455q-23 76 -47.5 163t-52 177t-56.5 182t-57 178h-29q-29 -86 -57.5 -178t-56 -182t-52.5 -177.5t-47 -162.5 z" />
<glyph unicode="&#xc1;" d="M57 23q41 162 87 333.5t95.5 337.5t98.5 317.5t94 274.5q10 27 21.5 33t33.5 6h72q23 0 35 -6t22 -33q45 -123 93.5 -274.5t97.5 -317.5t96 -338t88 -333q4 -23 -12 -23h-68q-18 0 -20 16q-27 109 -57.5 223.5t-59.5 217.5h-500q-29 -102 -59.5 -217t-56.5 -224 q-2 -16 -21 -16h-67q-16 0 -13 23zM297 543h455q-23 76 -47.5 163t-52 177t-56.5 182t-57 178h-29q-29 -86 -57.5 -178t-56 -182t-52.5 -177.5t-47 -162.5zM380 1512q-6 23 13 30l366 152q31 12 43 -20l21 -56q12 -29 -31 -41l-356 -112q-23 -8 -31 -5t-12 17z" />
<glyph unicode="&#xc2;" d="M57 23q41 162 87 333.5t95.5 337.5t98.5 317.5t94 274.5q10 27 21.5 33t33.5 6h72q23 0 35 -6t22 -33q45 -123 93.5 -274.5t97.5 -317.5t96 -338t88 -333q4 -23 -12 -23h-68q-18 0 -20 16q-27 109 -57.5 223.5t-59.5 217.5h-500q-29 -102 -59.5 -217t-56.5 -224 q-2 -16 -21 -16h-67q-16 0 -13 23zM239 1495q-16 12 2 31l228 250q12 14 30 14h50q18 0 30 -14l228 -250q16 -16 2 -31l-39 -32q-16 -12 -35 6l-205 211h-12l-205 -211q-20 -20 -35 -6zM297 543h455q-23 76 -47.5 163t-52 177t-56.5 182t-57 178h-29q-29 -86 -57.5 -178 t-56 -182t-52.5 -177.5t-47 -162.5z" />
<glyph unicode="&#xc3;" d="M57 23q41 162 87 333.5t95.5 337.5t98.5 317.5t94 274.5q10 27 21.5 33t33.5 6h72q23 0 35 -6t22 -33q45 -123 93.5 -274.5t97.5 -317.5t96 -338t88 -333q4 -23 -12 -23h-68q-18 0 -20 16q-27 109 -57.5 223.5t-59.5 217.5h-500q-29 -102 -59.5 -217t-56.5 -224 q-2 -16 -21 -16h-67q-16 0 -13 23zM206 1561q35 74 87.5 111.5t111.5 37.5q33 0 61.5 -12t59.5 -35l55 -43q23 -16 38 -23t38 -7q31 0 63.5 31.5t57.5 72.5q8 10 18 6l43 -29q16 -10 6 -26q-35 -66 -88 -110t-112 -44q-33 0 -60.5 10.5t-58.5 34.5l-53 41q-23 16 -40.5 25.5 t-39.5 9.5q-59 0 -117 -102q-8 -14 -25 -4l-41 26q-16 10 -4 29zM297 543h455q-23 76 -47.5 163t-52 177t-56.5 182t-57 178h-29q-29 -86 -57.5 -178t-56 -182t-52.5 -177.5t-47 -162.5z" />
<glyph unicode="&#xc4;" d="M57 23q41 162 87 333.5t95.5 337.5t98.5 317.5t94 274.5q10 27 21.5 33t33.5 6h72q23 0 35 -6t22 -33q45 -123 93.5 -274.5t97.5 -317.5t96 -338t88 -333q4 -23 -12 -23h-68q-18 0 -20 16q-27 109 -57.5 223.5t-59.5 217.5h-500q-29 -102 -59.5 -217t-56.5 -224 q-2 -16 -21 -16h-67q-16 0 -13 23zM279 1577v43q0 39 15.5 52.5t54.5 13.5h20q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-20q-41 0 -55.5 13t-14.5 50zM297 543h455q-23 76 -47.5 163t-52 177t-56.5 182t-57 178h-29q-29 -86 -57.5 -178t-56 -182 t-52.5 -177.5t-47 -162.5zM609 1577v43q0 39 15 52.5t54 13.5h21q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-21q-41 0 -55 13t-14 50z" />
<glyph unicode="&#xc5;" d="M57 23q41 162 87 333.5t95.5 337.5t98.5 317.5t94 274.5q10 27 21.5 33t33.5 6h72q23 0 35 -6t22 -33q45 -123 93.5 -274.5t97.5 -317.5t96 -338t88 -333q4 -23 -12 -23h-68q-18 0 -20 16q-27 109 -57.5 223.5t-59.5 217.5h-500q-29 -102 -59.5 -217t-56.5 -224 q-2 -16 -21 -16h-67q-16 0 -13 23zM297 543h455q-23 76 -47.5 163t-52 177t-56.5 182t-57 178h-29q-29 -86 -57.5 -178t-56 -182t-52.5 -177.5t-47 -162.5zM338 1630q0 90 55.5 136t130.5 46q78 0 132.5 -46t54.5 -136t-54.5 -136t-132.5 -46q-76 0 -131 46t-55 136z M426 1630q0 -49 28.5 -74.5t69.5 -25.5q43 0 71 25.5t28 74.5t-28 75t-71 26q-41 0 -69.5 -26t-28.5 -75z" />
<glyph unicode="&#xc6;" horiz-adv-x="1579" d="M59 27q98 207 181.5 376.5t157 316t141 277.5t139.5 263q18 35 37.5 50t64.5 15h703q23 0 22 -22v-41q0 -23 -22 -23h-604v-510h522q23 0 22 -22v-41q0 -23 -22 -23h-522v-434q0 -70 24.5 -96.5t100.5 -26.5h469q23 0 22 -23v-40q0 -23 -22 -23h-480q-111 0 -161 46 t-50 151v260h-417l-201 -432q-10 -25 -27 -25h-65q-27 0 -13 27zM410 543h372v696h-16q-61 -117 -106.5 -203t-85 -162.5t-78.5 -153.5t-86 -177z" />
<glyph unicode="&#xc7;" horiz-adv-x="1007" d="M115 604v119q0 109 7 191.5t23.5 146t46 111t72.5 83.5q55 47 133 67.5t176 20.5q197 0 340 -83q16 -10 9 -31l-17 -37q-10 -18 -31 -6q-70 35 -140.5 53t-158.5 18q-80 0 -143 -16t-110 -63q-66 -66 -87.5 -180.5t-21.5 -301.5v-65q0 -106 4 -180t14.5 -126.5t29 -91 t46.5 -73.5q41 -49 105.5 -70.5t179.5 -21.5q76 0 154.5 15t142.5 42q20 8 26 -10l11 -49q6 -16 -13 -25q-137 -57 -317 -59l-16 -125q14 4 31.5 6t37.5 2q59 0 97 -35t38 -113q0 -184 -215 -184q-37 0 -77.5 6t-63.5 15q-16 4 -12 20l8 45q4 16 20 10q29 -8 65 -12t64 -4 q59 0 89 21.5t30 76.5q0 76 -80 76q-23 0 -44 -3t-36 -7q-14 -2 -22 2l-14 8q-6 4 -8.5 7t-0.5 13l25 177q-111 8 -181.5 39.5t-117.5 91.5q-29 35 -47.5 75.5t-29.5 96t-16 132.5t-5 185z" />
<glyph unicode="&#xc8;" horiz-adv-x="931" d="M129 604v117q0 98 3 206.5t9 200.5q4 49 14.5 84t32.5 60q51 53 172 53h476q23 0 22 -22v-41q0 -23 -22 -23h-469q-41 0 -62.5 -6t-34.5 -19q-14 -14 -20 -36.5t-10 -63.5q-6 -86 -9.5 -174t-3.5 -211h500q23 0 23 -22v-41q0 -23 -23 -23h-500q0 -82 1 -143.5t3 -112.5 t4.5 -93t4.5 -83q4 -41 10 -63.5t20 -36.5q12 -12 34 -18.5t63 -6.5h469q23 0 22 -23v-40q0 -23 -22 -23h-476q-61 0 -102 10t-68 39q-25 27 -35 63t-14 85q-10 96 -11 207.5t-1 199.5zM288 1589l21 56q12 33 43 20l367 -152q18 -8 12 -30l-12 -35q-4 -14 -12.5 -17.5 t-30.5 5.5l-357 112q-43 12 -31 41z" />
<glyph unicode="&#xc9;" horiz-adv-x="931" d="M129 604v117q0 98 3 206.5t9 200.5q4 49 14.5 84t32.5 60q51 53 172 53h476q23 0 22 -22v-41q0 -23 -22 -23h-469q-41 0 -62.5 -6t-34.5 -19q-14 -14 -20 -36.5t-10 -63.5q-6 -86 -9.5 -174t-3.5 -211h500q23 0 23 -22v-41q0 -23 -23 -23h-500q0 -82 1 -143.5t3 -112.5 t4.5 -93t4.5 -83q4 -41 10 -63.5t20 -36.5q12 -12 34 -18.5t63 -6.5h469q23 0 22 -23v-40q0 -23 -22 -23h-476q-61 0 -102 10t-68 39q-25 27 -35 63t-14 85q-10 96 -11 207.5t-1 199.5zM352 1483q-6 23 13 30l366 152q31 12 43 -20l21 -56q12 -29 -31 -41l-356 -112 q-23 -8 -31 -5t-12 17z" />
<glyph unicode="&#xca;" horiz-adv-x="931" d="M129 604v117q0 98 3 206.5t9 200.5q4 49 14.5 84t32.5 60q51 53 172 53h476q23 0 22 -22v-41q0 -23 -22 -23h-469q-41 0 -62.5 -6t-34.5 -19q-14 -14 -20 -36.5t-10 -63.5q-6 -86 -9.5 -174t-3.5 -211h500q23 0 23 -22v-41q0 -23 -23 -23h-500q0 -82 1 -143.5t3 -112.5 t4.5 -93t4.5 -83q4 -41 10 -63.5t20 -36.5q12 -12 34 -18.5t63 -6.5h469q23 0 22 -23v-40q0 -23 -22 -23h-476q-61 0 -102 10t-68 39q-25 27 -35 63t-14 85q-10 96 -11 207.5t-1 199.5zM237 1466q-16 12 2 31l228 250q12 14 30 14h50q18 0 30 -14l228 -250q16 -16 2 -31 l-39 -32q-16 -12 -35 6l-205 211h-12l-205 -211q-20 -20 -35 -6z" />
<glyph unicode="&#xcb;" horiz-adv-x="931" d="M129 604v117q0 98 3 206.5t9 200.5q4 49 14.5 84t32.5 60q51 53 172 53h476q23 0 22 -22v-41q0 -23 -22 -23h-469q-41 0 -62.5 -6t-34.5 -19q-14 -14 -20 -36.5t-10 -63.5q-6 -86 -9.5 -174t-3.5 -211h500q23 0 23 -22v-41q0 -23 -23 -23h-500q0 -82 1 -143.5t3 -112.5 t4.5 -93t4.5 -83q4 -41 10 -63.5t20 -36.5q12 -12 34 -18.5t63 -6.5h469q23 0 22 -23v-40q0 -23 -22 -23h-476q-61 0 -102 10t-68 39q-25 27 -35 63t-14 85q-10 96 -11 207.5t-1 199.5zM276 1548v43q0 39 15.5 52.5t54.5 13.5h20q41 0 56.5 -13.5t15.5 -52.5v-43 q0 -37 -15.5 -50t-56.5 -13h-20q-41 0 -55.5 13t-14.5 50zM606 1548v43q0 39 15 52.5t54 13.5h21q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-21q-41 0 -55 13t-14 50z" />
<glyph unicode="&#xcc;" horiz-adv-x="413" d="M-82 1589l21 56q12 33 43 20l367 -152q18 -8 12 -30l-12 -35q-4 -14 -12.5 -17.5t-30.5 5.5l-357 112q-43 12 -31 41zM160 23v1280q0 23 18 22h58q18 0 18 -22v-1280q0 -23 -18 -23h-58q-18 0 -18 23z" />
<glyph unicode="&#xcd;" horiz-adv-x="413" d="M57 1483q-6 23 13 30l366 152q31 12 43 -20l21 -56q12 -29 -31 -41l-356 -112q-23 -8 -31 -5t-12 17zM160 23v1280q0 23 18 22h58q18 0 18 -22v-1280q0 -23 -18 -23h-58q-18 0 -18 23z" />
<glyph unicode="&#xce;" horiz-adv-x="413" d="M-76 1466q-16 12 2 31l228 250q12 14 30 14h50q18 0 30 -14l228 -250q16 -16 2 -31l-39 -32q-16 -12 -35 6l-205 211h-12l-205 -211q-20 -20 -35 -6zM160 23v1280q0 23 18 22h58q18 0 18 -22v-1280q0 -23 -18 -23h-58q-18 0 -18 23z" />
<glyph unicode="&#xcf;" horiz-adv-x="413" d="M-33 1548v43q0 39 15.5 52.5t54.5 13.5h20q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-20q-41 0 -55.5 13t-14.5 50zM160 23v1280q0 23 18 22h58q18 0 18 -22v-1280q0 -23 -18 -23h-58q-18 0 -18 23zM297 1548v43q0 39 15 52.5t54 13.5h21 q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-21q-41 0 -55 13t-14 50z" />
<glyph unicode="&#xd0;" horiz-adv-x="1202" d="M20 662v45q0 16 21 16h145v573q0 16 7.5 26.5t29.5 14.5q31 2 57.5 4t56.5 4.5t64.5 2.5h77.5q150 0 263.5 -26t190.5 -95.5t115.5 -190.5t38.5 -313v-96q0 -162 -28.5 -281t-93 -195.5t-170 -113.5t-258.5 -37h-310q-41 -4 -41 37v608h-145q-20 0 -21 17zM281 104 q0 -18 16 -18h188q135 0 222.5 18.5t146.5 71.5q68 59 99.5 167t31.5 300v55q0 94 -7 164t-22.5 122t-38 91t-51.5 72q-63 70 -159.5 92.5t-219.5 22.5h-88t-94 -5q-25 -2 -24 -28v-506h313q20 0 20 -16v-45q0 -16 -20 -17h-313v-541z" />
<glyph unicode="&#xd1;" horiz-adv-x="1220" d="M147 23v1237q0 39 14.5 52t49.5 13h41q41 0 58.5 -12.5t35.5 -48.5l623 -1182h12v1221q0 23 18 22h54q20 0 20 -22v-1242q0 -33 -12 -47t-43 -14h-55q-49 0 -72 43l-639 1200h-12v-1220q0 -23 -19 -23h-53q-20 0 -21 23zM286 1532q35 74 87.5 111.5t111.5 37.5 q33 0 61.5 -12t59.5 -35l55 -43q23 -16 38 -23t38 -7q31 0 63.5 31.5t57.5 72.5q8 10 18 6l43 -29q16 -10 6 -26q-35 -66 -88 -110t-112 -44q-33 0 -60.5 10.5t-58.5 34.5l-53 41q-23 16 -40.5 25.5t-39.5 9.5q-59 0 -117 -102q-8 -14 -25 -4l-41 26q-16 10 -4 29z" />
<glyph unicode="&#xd2;" horiz-adv-x="1163" d="M115 604v115q0 100 6 177t20.5 137.5t37 106.5t57.5 83q53 61 141 90.5t205 29.5t204.5 -29.5t141.5 -90.5q33 -37 56.5 -83t37.5 -106.5t20.5 -137.5t6.5 -177v-115q0 -98 -6.5 -175t-20.5 -137.5t-37.5 -106.5t-56.5 -83q-53 -61 -141 -90.5t-205 -29.5t-205 29.5 t-141 90.5q-35 37 -57.5 83t-37 106.5t-20.5 137.5t-6 175zM213 633q0 -176 19.5 -282.5t74.5 -178.5q41 -53 115 -78.5t160 -25.5t159.5 25.5t114.5 78.5q55 72 74.5 178.5t19.5 282.5v59q0 94 -4 165t-15 125t-29.5 96t-45.5 77q-41 53 -114.5 77.5t-159.5 24.5 t-160 -24.5t-115 -77.5q-29 -35 -46 -77t-28.5 -96t-15.5 -125t-4 -165v-59zM350 1589l21 56q12 33 43 20l367 -152q18 -8 12 -30l-12 -35q-4 -14 -12.5 -17.5t-30.5 5.5l-357 112q-43 12 -31 41z" />
<glyph unicode="&#xd3;" horiz-adv-x="1163" d="M115 604v115q0 100 6 177t20.5 137.5t37 106.5t57.5 83q53 61 141 90.5t205 29.5t204.5 -29.5t141.5 -90.5q33 -37 56.5 -83t37.5 -106.5t20.5 -137.5t6.5 -177v-115q0 -98 -6.5 -175t-20.5 -137.5t-37.5 -106.5t-56.5 -83q-53 -61 -141 -90.5t-205 -29.5t-205 29.5 t-141 90.5q-35 37 -57.5 83t-37 106.5t-20.5 137.5t-6 175zM213 633q0 -176 19.5 -282.5t74.5 -178.5q41 -53 115 -78.5t160 -25.5t159.5 25.5t114.5 78.5q55 72 74.5 178.5t19.5 282.5v59q0 94 -4 165t-15 125t-29.5 96t-45.5 77q-41 53 -114.5 77.5t-159.5 24.5 t-160 -24.5t-115 -77.5q-29 -35 -46 -77t-28.5 -96t-15.5 -125t-4 -165v-59zM423 1483q-6 23 13 30l366 152q31 12 43 -20l21 -56q12 -29 -31 -41l-356 -112q-23 -8 -31 -5t-12 17z" />
<glyph unicode="&#xd4;" horiz-adv-x="1163" d="M115 604v115q0 100 6 177t20.5 137.5t37 106.5t57.5 83q53 61 141 90.5t205 29.5t204.5 -29.5t141.5 -90.5q33 -37 56.5 -83t37.5 -106.5t20.5 -137.5t6.5 -177v-115q0 -98 -6.5 -175t-20.5 -137.5t-37.5 -106.5t-56.5 -83q-53 -61 -141 -90.5t-205 -29.5t-205 29.5 t-141 90.5q-35 37 -57.5 83t-37 106.5t-20.5 137.5t-6 175zM213 633q0 -176 19.5 -282.5t74.5 -178.5q41 -53 115 -78.5t160 -25.5t159.5 25.5t114.5 78.5q55 72 74.5 178.5t19.5 282.5v59q0 94 -4 165t-15 125t-29.5 96t-45.5 77q-41 53 -114.5 77.5t-159.5 24.5 t-160 -24.5t-115 -77.5q-29 -35 -46 -77t-28.5 -96t-15.5 -125t-4 -165v-59zM301 1466q-16 12 2 31l228 250q12 14 30 14h50q18 0 30 -14l228 -250q16 -16 2 -31l-39 -32q-16 -12 -35 6l-205 211h-12l-205 -211q-20 -20 -35 -6z" />
<glyph unicode="&#xd5;" horiz-adv-x="1163" d="M115 604v115q0 100 6 177t20.5 137.5t37 106.5t57.5 83q53 61 141 90.5t205 29.5t204.5 -29.5t141.5 -90.5q33 -37 56.5 -83t37.5 -106.5t20.5 -137.5t6.5 -177v-115q0 -98 -6.5 -175t-20.5 -137.5t-37.5 -106.5t-56.5 -83q-53 -61 -141 -90.5t-205 -29.5t-205 29.5 t-141 90.5q-35 37 -57.5 83t-37 106.5t-20.5 137.5t-6 175zM213 633q0 -176 19.5 -282.5t74.5 -178.5q41 -53 115 -78.5t160 -25.5t159.5 25.5t114.5 78.5q55 72 74.5 178.5t19.5 282.5v59q0 94 -4 165t-15 125t-29.5 96t-45.5 77q-41 53 -114.5 77.5t-159.5 24.5 t-160 -24.5t-115 -77.5q-29 -35 -46 -77t-28.5 -96t-15.5 -125t-4 -165v-59zM274 1532q35 74 87.5 111.5t111.5 37.5q33 0 61.5 -12t59.5 -35l55 -43q23 -16 38 -23t38 -7q31 0 63.5 31.5t57.5 72.5q8 10 18 6l43 -29q16 -10 6 -26q-35 -66 -88 -110t-112 -44 q-33 0 -60.5 10.5t-58.5 34.5l-53 41q-23 16 -40.5 25.5t-39.5 9.5q-59 0 -117 -102q-8 -14 -25 -4l-41 26q-16 10 -4 29z" />
<glyph unicode="&#xd6;" horiz-adv-x="1163" d="M115 604v115q0 100 6 177t20.5 137.5t37 106.5t57.5 83q53 61 141 90.5t205 29.5t204.5 -29.5t141.5 -90.5q33 -37 56.5 -83t37.5 -106.5t20.5 -137.5t6.5 -177v-115q0 -98 -6.5 -175t-20.5 -137.5t-37.5 -106.5t-56.5 -83q-53 -61 -141 -90.5t-205 -29.5t-205 29.5 t-141 90.5q-35 37 -57.5 83t-37 106.5t-20.5 137.5t-6 175zM213 633q0 -176 19.5 -282.5t74.5 -178.5q41 -53 115 -78.5t160 -25.5t159.5 25.5t114.5 78.5q55 72 74.5 178.5t19.5 282.5v59q0 94 -4 165t-15 125t-29.5 96t-45.5 77q-41 53 -114.5 77.5t-159.5 24.5 t-160 -24.5t-115 -77.5q-29 -35 -46 -77t-28.5 -96t-15.5 -125t-4 -165v-59zM342 1548v43q0 39 15.5 52.5t54.5 13.5h20q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-20q-41 0 -55.5 13t-14.5 50zM672 1548v43q0 39 15 52.5t54 13.5h21q41 0 56.5 -13.5 t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-21q-41 0 -55 13t-14 50z" />
<glyph unicode="&#xd7;" d="M207 283l254 252l-252 251q-12 12 0 21l43 45q10 6 18 -2l252 -252l254 254q10 6 17 0l45 -45q8 -10 0 -19l-252 -253l252 -252q10 -12 2 -21l-43 -43q-12 -8 -21 0l-254 254l-254 -254q-6 -10 -16 0l-43 43q-12 10 -2 21z" />
<glyph unicode="&#xd8;" horiz-adv-x="1163" d="M115 604v115q0 100 6 177t20.5 137.5t37 106.5t57.5 83q53 61 141 90.5t205 29.5q86 0 156.5 -15t123.5 -50l66 127q8 12 24 4l39 -20q14 -8 6 -23l-71 -141q35 -37 58.5 -83t37.5 -106.5t20.5 -138.5t6.5 -178v-115q0 -98 -6.5 -175t-20.5 -137.5t-37.5 -106.5 t-56.5 -83q-53 -61 -141 -90.5t-205 -29.5q-158 0 -258 51l-64 -123q-8 -12 -24 -4l-39 20q-14 8 -7 23l66 131q-8 6 -20 22q-35 37 -57.5 83t-37 106.5t-20.5 137.5t-6 175zM213 633q0 -174 19.5 -280.5t72.5 -176.5l514 1014q-43 35 -105.5 51t-131.5 16q-86 0 -160 -24.5 t-115 -77.5q-29 -35 -46 -77t-28.5 -96t-15.5 -125t-4 -165v-59zM367 119q41 -27 97.5 -39t117.5 -12q86 0 159.5 25.5t114.5 78.5q55 72 74.5 178.5t19.5 282.5v59q0 168 -16 266.5t-60 165.5z" />
<glyph unicode="&#xd9;" horiz-adv-x="1167" d="M143 422v881q0 23 19 22h57q18 0 19 -22v-871q0 -100 16 -166.5t59 -113.5q41 -47 106.5 -70t164.5 -23q98 0 163.5 22.5t106.5 70.5q43 47 59.5 113.5t16.5 166.5v871q0 23 18 22h58q18 0 18 -22v-881q0 -111 -24.5 -196t-79.5 -138q-53 -53 -135 -79.5t-201 -26.5 t-201 26.5t-135 79.5q-55 53 -80 138t-25 196zM346 1589l21 56q12 33 43 20l367 -152q18 -8 12 -30l-12 -35q-4 -14 -12.5 -17.5t-30.5 5.5l-357 112q-43 12 -31 41z" />
<glyph unicode="&#xda;" horiz-adv-x="1167" d="M143 422v881q0 23 19 22h57q18 0 19 -22v-871q0 -100 16 -166.5t59 -113.5q41 -47 106.5 -70t164.5 -23q98 0 163.5 22.5t106.5 70.5q43 47 59.5 113.5t16.5 166.5v871q0 23 18 22h58q18 0 18 -22v-881q0 -111 -24.5 -196t-79.5 -138q-53 -53 -135 -79.5t-201 -26.5 t-201 26.5t-135 79.5q-55 53 -80 138t-25 196zM370 1483q-6 23 13 30l366 152q31 12 43 -20l21 -56q12 -29 -31 -41l-356 -112q-23 -8 -31 -5t-12 17z" />
<glyph unicode="&#xdb;" horiz-adv-x="1167" d="M143 422v881q0 23 19 22h57q18 0 19 -22v-871q0 -100 16 -166.5t59 -113.5q41 -47 106.5 -70t164.5 -23q98 0 163.5 22.5t106.5 70.5q43 47 59.5 113.5t16.5 166.5v871q0 23 18 22h58q18 0 18 -22v-881q0 -111 -24.5 -196t-79.5 -138q-53 -53 -135 -79.5t-201 -26.5 t-201 26.5t-135 79.5q-55 53 -80 138t-25 196zM303 1466q-16 12 2 31l228 250q12 14 30 14h50q18 0 30 -14l228 -250q16 -16 2 -31l-39 -32q-16 -12 -35 6l-205 211h-12l-205 -211q-20 -20 -35 -6z" />
<glyph unicode="&#xdc;" horiz-adv-x="1167" d="M143 422v881q0 23 19 22h57q18 0 19 -22v-871q0 -100 16 -166.5t59 -113.5q41 -47 106.5 -70t164.5 -23q98 0 163.5 22.5t106.5 70.5q43 47 59.5 113.5t16.5 166.5v871q0 23 18 22h58q18 0 18 -22v-881q0 -111 -24.5 -196t-79.5 -138q-53 -53 -135 -79.5t-201 -26.5 t-201 26.5t-135 79.5q-55 53 -80 138t-25 196zM336 1548v43q0 39 15.5 52.5t54.5 13.5h20q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-20q-41 0 -55.5 13t-14.5 50zM666 1548v43q0 39 15 52.5t54 13.5h21q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50 t-56.5 -13h-21q-41 0 -55 13t-14 50z" />
<glyph unicode="&#xdd;" horiz-adv-x="954" d="M57 1303q-8 23 13 22h67q14 0 21 -16q139 -422 311 -734h16q172 311 312 734q6 16 20 16h68q20 0 12 -22q-45 -135 -87 -244t-85 -207t-92 -192.5t-109 -200.5v-436q0 -23 -18 -23h-57q-18 0 -19 23v436q-59 106 -108.5 200.5t-92.5 192.5t-85 206.5t-87 244.5zM296 1483 q-6 23 13 30l366 152q31 12 43 -20l21 -56q12 -29 -31 -41l-356 -112q-23 -8 -31 -5t-12 17z" />
<glyph unicode="&#xde;" horiz-adv-x="991" d="M147 23v1306q0 23 19 23h57q18 0 19 -23v-205q47 8 98 12.5t104 4.5q127 0 217.5 -22.5t147.5 -70t84 -123t27 -180.5v-22q0 -209 -116 -306.5t-343 -97.5h-101.5t-117.5 5v-301q0 -23 -19 -23h-57q-18 0 -19 23zM242 412q98 -6 209 -6q94 0 162.5 15t113.5 52t67.5 98.5 t22.5 153.5v27q0 160 -82.5 231.5t-281.5 71.5q-47 0 -106.5 -5.5t-104.5 -13.5v-624z" />
<glyph unicode="&#xdf;" horiz-adv-x="999" d="M150 -98v1155q0 348 342 348q72 0 133 -18.5t107 -56.5t72.5 -98.5t26.5 -141.5v-29q0 -113 -49 -198t-149 -115v-7q147 -14 210.5 -100t63.5 -240v-22q0 -104 -17.5 -179t-59.5 -123t-112.5 -71.5t-174.5 -23.5q-53 0 -109.5 8t-99.5 22q-10 4 -12.5 8.5t0.5 14.5l8 41 q2 16 20 12q43 -12 93.5 -18.5t103.5 -6.5q80 0 132 17.5t83 55.5t43 98.5t12 146.5v10q0 168 -76.5 234.5t-246.5 66.5h-70q-23 0 -23 21v37q0 25 23 24h47q72 0 123 23.5t84 63.5t48 91.5t15 106.5v18q0 123 -64.5 184.5t-187.5 61.5q-135 0 -193 -69.5t-58 -239.5v-1112 q0 -16 -17 -17h-57q-14 0 -14 17z" />
<glyph unicode="&#xe0;" horiz-adv-x="927" d="M86 266v25q0 72 15.5 126t57.5 90t112.5 54.5t181.5 18.5h102q37 0 75 -2.5t72 -6.5v121q0 147 -62 198.5t-187 51.5q-68 0 -133.5 -11t-112.5 -26q-27 -8 -33 10l-8 37q-4 14 0 19.5t16 9.5q53 16 124 28.5t157 12.5q178 0 253 -83t75 -267v-654q0 -18 -19 -18h-45 q-18 0 -20 20l-7 82h-4q-35 -59 -97 -89.5t-179 -30.5h-41q-162 0 -227.5 65.5t-65.5 218.5zM176 252q0 -104 45 -147.5t141 -43.5h80q135 0 197.5 60.5t62.5 183.5v187q-29 4 -65.5 6t-71.5 2h-129q-141 0 -200.5 -44t-59.5 -153v-51zM244 1391l26 53q14 31 45 16l346 -194 q16 -10 9 -33l-15 -33q-6 -12 -14 -14t-31 8l-340 154q-43 14 -26 43z" />
<glyph unicode="&#xe1;" horiz-adv-x="927" d="M86 266v25q0 72 15.5 126t57.5 90t112.5 54.5t181.5 18.5h102q37 0 75 -2.5t72 -6.5v121q0 147 -62 198.5t-187 51.5q-68 0 -133.5 -11t-112.5 -26q-27 -8 -33 10l-8 37q-4 14 0 19.5t16 9.5q53 16 124 28.5t157 12.5q178 0 253 -83t75 -267v-654q0 -18 -19 -18h-45 q-18 0 -20 20l-7 82h-4q-35 -59 -97 -89.5t-179 -30.5h-41q-162 0 -227.5 65.5t-65.5 218.5zM176 252q0 -104 45 -147.5t141 -43.5h80q135 0 197.5 60.5t62.5 183.5v187q-29 4 -65.5 6t-71.5 2h-129q-141 0 -200.5 -44t-59.5 -153v-51zM299 1233q-8 23 8 33l346 194 q31 14 45 -16l27 -53q16 -29 -27 -43l-340 -154q-23 -10 -31 -8t-14 14z" />
<glyph unicode="&#xe2;" horiz-adv-x="927" d="M86 266v25q0 72 15.5 126t57.5 90t112.5 54.5t181.5 18.5h102q37 0 75 -2.5t72 -6.5v121q0 147 -62 198.5t-187 51.5q-68 0 -133.5 -11t-112.5 -26q-27 -8 -33 10l-8 37q-4 14 0 19.5t16 9.5q53 16 124 28.5t157 12.5q178 0 253 -83t75 -267v-654q0 -18 -19 -18h-45 q-18 0 -20 20l-7 82h-4q-35 -59 -97 -89.5t-179 -30.5h-41q-162 0 -227.5 65.5t-65.5 218.5zM176 252q0 -104 45 -147.5t141 -43.5h80q135 0 197.5 60.5t62.5 183.5v187q-29 4 -65.5 6t-71.5 2h-129q-141 0 -200.5 -44t-59.5 -153v-51zM194 1204q-16 12 2 31l228 250 q12 14 30 14h50q18 0 30 -14l228 -250q16 -16 2 -31l-39 -33q-16 -12 -35 7l-205 211h-12l-205 -211q-20 -20 -35 -7z" />
<glyph unicode="&#xe3;" horiz-adv-x="927" d="M86 266v25q0 72 15.5 126t57.5 90t112.5 54.5t181.5 18.5h102q37 0 75 -2.5t72 -6.5v121q0 147 -62 198.5t-187 51.5q-68 0 -133.5 -11t-112.5 -26q-27 -8 -33 10l-8 37q-4 14 0 19.5t16 9.5q53 16 124 28.5t157 12.5q178 0 253 -83t75 -267v-654q0 -18 -19 -18h-45 q-18 0 -20 20l-7 82h-4q-35 -59 -97 -89.5t-179 -30.5h-41q-162 0 -227.5 65.5t-65.5 218.5zM153 1276q35 74 87.5 111.5t111.5 37.5q33 0 61.5 -12t59.5 -35l55 -43q23 -16 38 -23t38 -7q31 0 63.5 31.5t57.5 72.5q8 10 18 6l43 -29q16 -10 6 -26q-35 -66 -88 -110 t-112 -44q-33 0 -60.5 10.5t-58.5 34.5l-53 41q-23 16 -40.5 25.5t-39.5 9.5q-59 0 -117 -102q-8 -14 -25 -4l-41 26q-16 10 -4 29zM176 252q0 -104 45 -147.5t141 -43.5h80q135 0 197.5 60.5t62.5 183.5v187q-29 4 -65.5 6t-71.5 2h-129q-141 0 -200.5 -44t-59.5 -153v-51z " />
<glyph unicode="&#xe4;" horiz-adv-x="927" d="M86 266v25q0 72 15.5 126t57.5 90t112.5 54.5t181.5 18.5h102q37 0 75 -2.5t72 -6.5v121q0 147 -62 198.5t-187 51.5q-68 0 -133.5 -11t-112.5 -26q-27 -8 -33 10l-8 37q-4 14 0 19.5t16 9.5q53 16 124 28.5t157 12.5q178 0 253 -83t75 -267v-654q0 -18 -19 -18h-45 q-18 0 -20 20l-7 82h-4q-35 -59 -97 -89.5t-179 -30.5h-41q-162 0 -227.5 65.5t-65.5 218.5zM176 252q0 -104 45 -147.5t141 -43.5h80q135 0 197.5 60.5t62.5 183.5v187q-29 4 -65.5 6t-71.5 2h-129q-141 0 -200.5 -44t-59.5 -153v-51zM227 1296v43q0 39 15.5 52.5 t54.5 13.5h20q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-20q-41 0 -55.5 13.5t-14.5 49.5zM557 1296v43q0 39 15 52.5t54 13.5h21q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-21q-41 0 -55 13.5t-14 49.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="927" d="M86 266v25q0 72 15.5 126t57.5 90t112.5 54.5t181.5 18.5h102q37 0 75 -2.5t72 -6.5v121q0 147 -62 198.5t-187 51.5q-68 0 -133.5 -11t-112.5 -26q-27 -8 -33 10l-8 37q-4 14 0 19.5t16 9.5q53 16 124 28.5t157 12.5q178 0 253 -83t75 -267v-654q0 -18 -19 -18h-45 q-18 0 -20 20l-7 82h-4q-35 -59 -97 -89.5t-179 -30.5h-41q-162 0 -227.5 65.5t-65.5 218.5zM176 252q0 -104 45 -147.5t141 -43.5h80q135 0 197.5 60.5t62.5 183.5v187q-29 4 -65.5 6t-71.5 2h-129q-141 0 -200.5 -44t-59.5 -153v-51zM301 1309q0 90 55 136t131 46 q78 0 132.5 -46t54.5 -136t-54.5 -136.5t-132.5 -46.5q-76 0 -131 46.5t-55 136.5zM389 1309q0 -49 28.5 -75t69.5 -26q43 0 71 26t28 75t-28 74.5t-71 25.5q-41 0 -69.5 -25.5t-28.5 -74.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1517" d="M86 266v25q0 72 15.5 126t57.5 90t112.5 54.5t181.5 18.5h102q37 0 75 -2.5t72 -6.5v121q0 147 -62 198.5t-187 51.5q-68 0 -133.5 -10t-112.5 -27q-27 -8 -33 10l-8 37q-4 14 0 19.5t16 9.5q53 16 126 28.5t159 12.5q119 0 192.5 -38t102.5 -124q41 84 113.5 123 t193.5 39q170 0 256 -82t86 -281v-131q0 -47 -18.5 -66.5t-65.5 -19.5h-534q2 -109 18 -181.5t52 -116.5t90.5 -62.5t133.5 -18.5q53 0 123 12.5t123 35.5q18 8 27 -9l14 -32q4 -10 1 -17.5t-19 -15.5q-55 -25 -129 -40t-140 -15q-135 0 -223 48t-110 136 q-45 -184 -334 -184h-41q-158 0 -225.5 65.5t-67.5 218.5zM176 252q0 -104 44 -147.5t142 -43.5h99q125 0 183 63.5t58 180.5v125v25v37q-29 4 -65.5 6t-71.5 2h-129q-141 0 -200.5 -44t-59.5 -153v-51zM795 528h495q16 0 22.5 4.5t6.5 28.5v72q0 172 -59.5 239.5 t-190.5 67.5q-72 0 -123 -18.5t-85 -64.5t-49 -126t-17 -203z" />
<glyph unicode="&#xe7;" horiz-adv-x="817" d="M102 475v70q0 154 27 245t84 144q90 88 270 88q90 0 162 -23.5t119 -58.5q16 -10 6 -27l-27 -39q-8 -12 -26 0q-101 66 -236 66q-74 0 -122 -16.5t-80 -46.5q-31 -31 -48.5 -70t-25.5 -84t-10 -95t-2 -104v-35q0 -125 17 -208.5t69 -143.5q29 -35 88 -54.5t133 -19.5 q47 0 106.5 10.5t108.5 30.5q20 8 28 -14l9 -27q6 -16 2 -22t-19 -12q-47 -20 -112.5 -33.5t-122.5 -13.5h-4l-17 -125q14 4 31.5 6t38.5 2q59 0 97 -35t38 -113q0 -184 -215 -184q-37 0 -78 6t-63 15q-16 4 -13 20l9 45q4 16 20 10q29 -8 64.5 -12t64.5 -4q59 0 89 21.5 t30 76.5q0 76 -80 76q-23 0 -44.5 -3t-35.5 -7q-14 -2 -22 2l-15 8q-6 4 -8 7t0 13l25 179v2q-127 18 -195 84q-63 59 -89 160.5t-26 242.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="917" d="M102 455v92q0 248 85 361.5t282 113.5q342 0 342 -371v-123q0 -47 -18.5 -66.5t-65.5 -19.5h-534q2 -109 18 -181.5t52 -116.5t90.5 -62.5t133.5 -18.5q53 0 123 12.5t123 35.5q20 8 27 -9l12 -32q4 -14 2 -20.5t-18 -14.5q-55 -25 -129 -39t-140 -14q-106 0 -180 26.5 t-119 82.5t-65.5 146.5t-20.5 217.5zM195 528h499q16 0 22.5 4.5t6.5 28.5v64q0 172 -61.5 243.5t-192.5 71.5q-72 0 -123 -18.5t-84 -65.5t-49 -127t-18 -201zM240 1391l26 53q14 31 45 16l346 -194q16 -10 9 -33l-15 -33q-6 -12 -14 -14t-31 8l-340 154q-43 14 -26 43z " />
<glyph unicode="&#xe9;" horiz-adv-x="917" d="M102 455v92q0 248 85 361.5t282 113.5q342 0 342 -371v-123q0 -47 -18.5 -66.5t-65.5 -19.5h-534q2 -109 18 -181.5t52 -116.5t90.5 -62.5t133.5 -18.5q53 0 123 12.5t123 35.5q20 8 27 -9l12 -32q4 -14 2 -20.5t-18 -14.5q-55 -25 -129 -39t-140 -14q-106 0 -180 26.5 t-119 82.5t-65.5 146.5t-20.5 217.5zM195 528h499q16 0 22.5 4.5t6.5 28.5v64q0 172 -61.5 243.5t-192.5 71.5q-72 0 -123 -18.5t-84 -65.5t-49 -127t-18 -201zM271 1233q-8 23 8 33l346 194q31 14 45 -16l27 -53q16 -29 -27 -43l-340 -154q-23 -10 -31 -8t-14 14z" />
<glyph unicode="&#xea;" horiz-adv-x="917" d="M102 455v92q0 248 85 361.5t282 113.5q342 0 342 -371v-123q0 -47 -18.5 -66.5t-65.5 -19.5h-534q2 -109 18 -181.5t52 -116.5t90.5 -62.5t133.5 -18.5q53 0 123 12.5t123 35.5q20 8 27 -9l12 -32q4 -14 2 -20.5t-18 -14.5q-55 -25 -129 -39t-140 -14q-106 0 -180 26.5 t-119 82.5t-65.5 146.5t-20.5 217.5zM188 1204q-16 12 2 31l228 250q12 14 30 14h50q18 0 30 -14l228 -250q16 -16 2 -31l-39 -33q-16 -12 -35 7l-205 211h-12l-205 -211q-20 -20 -35 -7zM195 528h499q16 0 22.5 4.5t6.5 28.5v64q0 172 -61.5 243.5t-192.5 71.5 q-72 0 -123 -18.5t-84 -65.5t-49 -127t-18 -201z" />
<glyph unicode="&#xeb;" horiz-adv-x="917" d="M102 455v92q0 248 85 361.5t282 113.5q342 0 342 -371v-123q0 -47 -18.5 -66.5t-65.5 -19.5h-534q2 -109 18 -181.5t52 -116.5t90.5 -62.5t133.5 -18.5q53 0 123 12.5t123 35.5q20 8 27 -9l12 -32q4 -14 2 -20.5t-18 -14.5q-55 -25 -129 -39t-140 -14q-106 0 -180 26.5 t-119 82.5t-65.5 146.5t-20.5 217.5zM195 528h499q16 0 22.5 4.5t6.5 28.5v64q0 172 -61.5 243.5t-192.5 71.5q-72 0 -123 -18.5t-84 -65.5t-49 -127t-18 -201zM231 1296v43q0 39 15.5 52.5t54.5 13.5h20q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-20 q-41 0 -55.5 13.5t-14.5 49.5zM561 1296v43q0 39 15 52.5t54 13.5h21q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-21q-41 0 -55 13.5t-14 49.5z" />
<glyph unicode="&#xec;" horiz-adv-x="448" d="M-36 1393l26 53q14 31 45 16l346 -194q16 -10 9 -33l-15 -33q-6 -12 -14 -14t-31 8l-340 154q-43 14 -26 43zM180 16v971q0 16 15 17h57q16 0 16 -17v-971q0 -16 -16 -16h-57q-14 0 -15 16z" />
<glyph unicode="&#xed;" horiz-adv-x="448" d="M105 1233q-8 23 8 33l346 194q31 14 45 -16l27 -53q16 -29 -27 -43l-340 -154q-23 -10 -31 -8t-14 14zM180 16v971q0 16 15 17h57q16 0 16 -17v-971q0 -16 -16 -16h-57q-14 0 -15 16z" />
<glyph unicode="&#xee;" horiz-adv-x="448" d="M-58 1204q-16 12 2 31l228 250q12 14 30 14h50q18 0 30 -14l228 -250q16 -16 2 -31l-39 -33q-16 -12 -35 7l-205 211h-12l-205 -211q-20 -20 -35 -7zM180 16v971q0 16 15 17h57q16 0 16 -17v-971q0 -16 -16 -16h-57q-14 0 -15 16z" />
<glyph unicode="&#xef;" horiz-adv-x="448" d="M-18 1296v43q0 39 15.5 52.5t54.5 13.5h20q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-20q-41 0 -55.5 13.5t-14.5 49.5zM180 16v971q0 16 15 17h57q16 0 16 -17v-971q0 -16 -16 -16h-57q-14 0 -15 16zM312 1296v43q0 39 15 52.5t54 13.5h21 q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-21q-41 0 -55 13.5t-14 49.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1026" d="M102 428v61q0 102 24 186.5t73 145t123.5 94t177.5 33.5q82 0 147.5 -24.5t110.5 -67.5l4 4q-37 109 -100.5 202t-147.5 167l-190 -139q-14 -10 -27 8l-18 24q-10 16 4 29l172 123q-80 57 -172 96q-12 4 -14.5 8t1.5 17l17 39q8 18 24 12q59 -25 113.5 -54.5t101.5 -64.5 l162 117q16 12 27 -2l22 -29q8 -14 -4 -24l-149 -109q170 -143 252 -341t82 -427v-57q0 -111 -30 -199t-83 -149.5t-129 -93t-168 -31.5q-102 0 -178 33.5t-126 94t-76 141.5t-26 177zM193 451q0 -193 80.5 -289t238.5 -96q152 0 233.5 103t81.5 302v23q0 98 -23.5 168.5 t-65.5 115.5t-101 65.5t-131 20.5q-86 0 -146.5 -28.5t-97.5 -79.5t-53 -123t-16 -160v-22z" />
<glyph unicode="&#xf1;" horiz-adv-x="1019" d="M150 16v973q0 14 14 15h45q18 0 18 -17l6 -94h3q37 59 109.5 94t182.5 35q229 0 310 -143q27 -47 36 -107.5t9 -146.5v-609q0 -16 -17 -16h-57q-14 0 -14 16v600q0 121 -25 185.5t-78 97.5q-31 18 -70.5 28.5t-93.5 12.5q-68 2 -122 -13.5t-90.5 -50t-57 -92 t-20.5 -139.5v-629q0 -16 -17 -16h-57q-14 0 -14 16zM196 1276q35 74 87.5 111.5t111.5 37.5q33 0 61.5 -12t59.5 -35l55 -43q23 -16 38 -23t38 -7q31 0 63.5 31.5t57.5 72.5q8 10 18 6l43 -29q16 -10 6 -26q-35 -66 -88 -110t-112 -44q-33 0 -60.5 10.5t-58.5 34.5l-53 41 q-23 16 -40.5 25.5t-39.5 9.5q-59 0 -117 -102q-8 -14 -25 -4l-41 26q-16 10 -4 29z" />
<glyph unicode="&#xf2;" horiz-adv-x="962" d="M102 457v90q0 127 21.5 229.5t89.5 161.5q43 37 106.5 60.5t161.5 23.5t163 -22.5t106 -61.5q66 -59 88 -161.5t22 -229.5v-90q0 -127 -22.5 -229.5t-87.5 -161.5q-41 -39 -105.5 -61.5t-163.5 -22.5q-98 0 -162.5 22.5t-105.5 61.5q-66 59 -88.5 161.5t-22.5 229.5z M193 481q0 -127 12 -196.5t43 -116.5q70 -104 233 -105q162 0 234 105q31 47 43 116.5t12 196.5v41q0 127 -12.5 196.5t-42.5 117.5q-72 104 -234 104q-164 0 -233 -104q-31 -47 -43 -117t-12 -197v-41zM242 1391l26 53q14 31 45 16l346 -194q16 -10 9 -33l-15 -33 q-6 -12 -14 -14t-31 8l-340 154q-43 14 -26 43z" />
<glyph unicode="&#xf3;" horiz-adv-x="962" d="M102 457v90q0 127 21.5 229.5t89.5 161.5q43 37 106.5 60.5t161.5 23.5t163 -22.5t106 -61.5q66 -59 88 -161.5t22 -229.5v-90q0 -127 -22.5 -229.5t-87.5 -161.5q-41 -39 -105.5 -61.5t-163.5 -22.5q-98 0 -162.5 22.5t-105.5 61.5q-66 59 -88.5 161.5t-22.5 229.5z M193 481q0 -127 12 -196.5t43 -116.5q70 -104 233 -105q162 0 234 105q31 47 43 116.5t12 196.5v41q0 127 -12.5 196.5t-42.5 117.5q-72 104 -234 104q-164 0 -233 -104q-31 -47 -43 -117t-12 -197v-41zM281 1233q-8 23 8 33l346 194q31 14 45 -16l27 -53q16 -29 -27 -43 l-340 -154q-23 -10 -31 -8t-14 14z" />
<glyph unicode="&#xf4;" horiz-adv-x="962" d="M102 457v90q0 127 21.5 229.5t89.5 161.5q43 37 106.5 60.5t161.5 23.5t163 -22.5t106 -61.5q66 -59 88 -161.5t22 -229.5v-90q0 -127 -22.5 -229.5t-87.5 -161.5q-41 -39 -105.5 -61.5t-163.5 -22.5q-98 0 -162.5 22.5t-105.5 61.5q-66 59 -88.5 161.5t-22.5 229.5z M193 481q0 -127 12 -196.5t43 -116.5q70 -104 233 -105q162 0 234 105q31 47 43 116.5t12 196.5v41q0 127 -12.5 196.5t-42.5 117.5q-72 104 -234 104q-164 0 -233 -104q-31 -47 -43 -117t-12 -197v-41zM202 1204q-16 12 2 31l228 250q12 14 30 14h50q18 0 30 -14l228 -250 q16 -16 2 -31l-39 -33q-16 -12 -35 7l-205 211h-12l-205 -211q-20 -20 -35 -7z" />
<glyph unicode="&#xf5;" horiz-adv-x="962" d="M102 457v90q0 127 21.5 229.5t89.5 161.5q43 37 106.5 60.5t161.5 23.5t163 -22.5t106 -61.5q66 -59 88 -161.5t22 -229.5v-90q0 -127 -22.5 -229.5t-87.5 -161.5q-41 -39 -105.5 -61.5t-163.5 -22.5q-98 0 -162.5 22.5t-105.5 61.5q-66 59 -88.5 161.5t-22.5 229.5z M166 1276q35 74 87.5 111.5t111.5 37.5q33 0 61.5 -12t59.5 -35l55 -43q23 -16 38 -23t38 -7q31 0 63.5 31.5t57.5 72.5q8 10 18 6l43 -29q16 -10 6 -26q-35 -66 -88 -110t-112 -44q-33 0 -60.5 10.5t-58.5 34.5l-53 41q-23 16 -40.5 25.5t-39.5 9.5q-59 0 -117 -102 q-8 -14 -25 -4l-41 26q-16 10 -4 29zM193 481q0 -127 12 -196.5t43 -116.5q70 -104 233 -105q162 0 234 105q31 47 43 116.5t12 196.5v41q0 127 -12.5 196.5t-42.5 117.5q-72 104 -234 104q-164 0 -233 -104q-31 -47 -43 -117t-12 -197v-41z" />
<glyph unicode="&#xf6;" horiz-adv-x="962" d="M102 457v90q0 127 21.5 229.5t89.5 161.5q43 37 106.5 60.5t161.5 23.5t163 -22.5t106 -61.5q66 -59 88 -161.5t22 -229.5v-90q0 -127 -22.5 -229.5t-87.5 -161.5q-41 -39 -105.5 -61.5t-163.5 -22.5q-98 0 -162.5 22.5t-105.5 61.5q-66 59 -88.5 161.5t-22.5 229.5z M193 481q0 -127 12 -196.5t43 -116.5q70 -104 233 -105q162 0 234 105q31 47 43 116.5t12 196.5v41q0 127 -12.5 196.5t-42.5 117.5q-72 104 -234 104q-164 0 -233 -104q-31 -47 -43 -117t-12 -197v-41zM238 1296v43q0 39 15.5 52.5t54.5 13.5h20q41 0 56.5 -13.5 t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-20q-41 0 -55.5 13.5t-14.5 49.5zM568 1296v43q0 39 15 52.5t54 13.5h21q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-21q-41 0 -55 13.5t-14 49.5z" />
<glyph unicode="&#xf7;" d="M109 504v61q0 14 12 15h805q12 0 12 -13v-63q0 -12 -12 -12h-805q-12 0 -12 12zM428 152v57q0 68 76 67h24q47 0 62.5 -16t15.5 -51v-57q0 -35 -15.5 -51.5t-62.5 -16.5h-24q-76 0 -76 68zM428 862v58q0 68 76 67h24q47 0 62.5 -16.5t15.5 -50.5v-58q0 -35 -15.5 -51 t-62.5 -16h-24q-76 0 -76 67z" />
<glyph unicode="&#xf8;" horiz-adv-x="962" d="M102 457v90q0 127 21.5 229.5t89.5 161.5q43 37 106.5 60.5t161.5 23.5q61 0 109.5 -10.5t87.5 -26.5l61 123q6 14 25 6l33 -16q16 -10 8 -25l-66 -127l11 -8q66 -59 88 -161.5t22 -229.5v-90q0 -127 -22.5 -229.5t-87.5 -161.5q-41 -39 -105.5 -61.5t-163.5 -22.5 q-68 0 -119 11t-90 30l-59 -119q-6 -16 -25 -6l-32 16q-16 10 -9 25l64 127q-66 59 -87.5 161.5t-21.5 229.5zM193 481q0 -127 12 -196.5t43 -116.5l8 -12l381 749q-63 35 -156 35q-164 0 -233 -104q-31 -47 -43 -117t-12 -197v-41zM313 104q66 -41 168 -41q162 0 234 105 q31 47 43 116.5t12 196.5v41q0 127 -12.5 196.5t-42.5 117.5l-19 22z" />
<glyph unicode="&#xf9;" horiz-adv-x="987" d="M139 301v686q0 16 17 17h55q16 0 16 -17v-674q0 -80 13.5 -125t42.5 -71q35 -31 81 -44.5t130 -13.5t130 13.5t81 44.5q29 27 42 71.5t13 124.5v674q0 16 16 17h55q16 0 17 -17v-686q0 -86 -17.5 -144.5t-62.5 -99.5q-41 -37 -104.5 -56t-169.5 -19t-170 19t-105 56 q-45 41 -62.5 99.5t-17.5 144.5zM246 1391l26 53q14 31 45 16l346 -194q16 -10 9 -33l-15 -33q-6 -12 -14 -14t-31 8l-340 154q-43 14 -26 43z" />
<glyph unicode="&#xfa;" horiz-adv-x="987" d="M139 301v686q0 16 17 17h55q16 0 16 -17v-674q0 -80 13.5 -125t42.5 -71q35 -31 81 -44.5t130 -13.5t130 13.5t81 44.5q29 27 42 71.5t13 124.5v674q0 16 16 17h55q16 0 17 -17v-686q0 -86 -17.5 -144.5t-62.5 -99.5q-41 -37 -104.5 -56t-169.5 -19t-170 19t-105 56 q-45 41 -62.5 99.5t-17.5 144.5zM318 1233q-8 23 8 33l346 194q31 14 45 -16l27 -53q16 -29 -27 -43l-340 -154q-23 -10 -31 -8t-14 14z" />
<glyph unicode="&#xfb;" horiz-adv-x="987" d="M139 301v686q0 16 17 17h55q16 0 16 -17v-674q0 -80 13.5 -125t42.5 -71q35 -31 81 -44.5t130 -13.5t130 13.5t81 44.5q29 27 42 71.5t13 124.5v674q0 16 16 17h55q16 0 17 -17v-686q0 -86 -17.5 -144.5t-62.5 -99.5q-41 -37 -104.5 -56t-169.5 -19t-170 19t-105 56 q-45 41 -62.5 99.5t-17.5 144.5zM211 1204q-16 12 2 31l228 250q12 14 30 14h50q18 0 30 -14l228 -250q16 -16 2 -31l-39 -33q-16 -12 -35 7l-205 211h-12l-205 -211q-20 -20 -35 -7z" />
<glyph unicode="&#xfc;" horiz-adv-x="987" d="M139 301v686q0 16 17 17h55q16 0 16 -17v-674q0 -80 13.5 -125t42.5 -71q35 -31 81 -44.5t130 -13.5t130 13.5t81 44.5q29 27 42 71.5t13 124.5v674q0 16 16 17h55q16 0 17 -17v-686q0 -86 -17.5 -144.5t-62.5 -99.5q-41 -37 -104.5 -56t-169.5 -19t-170 19t-105 56 q-45 41 -62.5 99.5t-17.5 144.5zM252 1296v43q0 39 15.5 52.5t54.5 13.5h20q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-20q-41 0 -55.5 13.5t-14.5 49.5zM582 1296v43q0 39 15 52.5t54 13.5h21q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13 h-21q-41 0 -55 13.5t-14 49.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="831" d="M49 983q-6 20 14 21h54q18 0 22 -19q12 -53 30.5 -132t44.5 -177t58.5 -210t69.5 -228q12 -37 22.5 -62.5t21.5 -40t26.5 -20.5t38.5 -6h10q66 174 126 389t107 489q2 16 21 17h53q20 0 14 -21q-57 -264 -107 -454.5t-99.5 -342t-102.5 -281.5t-117 -274q-8 -20 -26 -12 l-47 21q-16 8 -9 24q45 90 84 179t74 184q-53 2 -86 26.5t-59 93.5q-33 90 -62.5 180.5t-57.5 190.5t-56.5 214t-61.5 251zM269 1233q-8 23 8 33l346 194q31 14 45 -16l27 -53q16 -29 -27 -43l-340 -154q-23 -10 -31 -8t-14 14z" />
<glyph unicode="&#xfe;" horiz-adv-x="1013" d="M150 -348v1718q0 16 14 16h57q16 0 17 -16v-487h4q41 68 107.5 103.5t195.5 35.5q109 0 179.5 -29.5t112.5 -86t58 -141.5t16 -196v-118q0 -113 -16 -200t-57 -147.5t-111 -91t-174 -30.5q-68 0 -118 9t-86 26.5t-61.5 42t-43.5 55.5h-6v-463q0 -16 -17 -17h-57 q-14 0 -14 17zM238 346q0 -135 81.5 -209t225.5 -74q74 0 126 16.5t86 61.5t49 123t15 201v86q0 94 -11 167t-42 122t-86 74.5t-143 25.5q-158 0 -229.5 -73.5t-71.5 -209.5v-311z" />
<glyph unicode="&#xff;" horiz-adv-x="831" d="M49 983q-6 20 14 21h54q18 0 22 -19q12 -53 30.5 -132t44.5 -177t58.5 -210t69.5 -228q12 -37 22.5 -62.5t21.5 -40t26.5 -20.5t38.5 -6h10q66 174 126 389t107 489q2 16 21 17h53q20 0 14 -21q-57 -264 -107 -454.5t-99.5 -342t-102.5 -281.5t-117 -274q-8 -20 -26 -12 l-47 21q-16 8 -9 24q45 90 84 179t74 184q-53 2 -86 26.5t-59 93.5q-33 90 -62.5 180.5t-57.5 190.5t-56.5 214t-61.5 251zM180 1296v43q0 39 15.5 52.5t54.5 13.5h20q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-20q-41 0 -55.5 13.5t-14.5 49.5zM510 1296 v43q0 39 15 52.5t54 13.5h21q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-21q-41 0 -55 13.5t-14 49.5z" />
<glyph unicode="&#x152;" horiz-adv-x="1697" d="M115 604v115q0 100 6 177t20.5 137.5t37 106.5t57.5 83q53 61 141 90.5t205 29.5q113 0 196.5 -27.5t151.5 -80.5q6 12 10 19.5t14 17.5q51 53 172 53h476q23 0 22 -22v-41q0 -23 -22 -23h-469q-41 0 -62.5 -6t-34.5 -19q-14 -14 -20 -36.5t-10 -63.5q-6 -86 -7.5 -174 t-1.5 -211h496q23 0 23 -22v-41q0 -23 -23 -23h-496v-143.5t1 -112.5t3.5 -93t4.5 -83q4 -41 10 -63.5t20 -36.5q12 -12 34 -18.5t63 -6.5h469q23 0 22 -23v-40q0 -23 -22 -23h-476q-61 0 -102 10t-68 39q-12 12 -20 24.5t-12 30.5q-66 -66 -147 -94t-195 -28 q-117 0 -205 29.5t-141 90.5q-35 37 -57.5 83t-37 106.5t-20.5 137.5t-6 175zM213 633q0 -176 20.5 -282.5t75.5 -178.5q41 -53 114 -79.5t159 -26.5q115 0 196.5 35.5t128.5 95.5q-10 96 -11 207.5t-1 199.5v117q0 98 3 209.5t9 220.5q-66 55 -143.5 82t-181.5 27 q-86 0 -159 -26t-114 -79q-29 -35 -47 -77t-28.5 -96t-15.5 -125t-5 -165v-59z" />
<glyph unicode="&#x153;" horiz-adv-x="1585" d="M102 457v90q0 127 21.5 229.5t89.5 161.5q43 37 106.5 60.5t161.5 23.5q125 0 209 -45t131 -152q35 102 111 149.5t205 47.5q170 0 256 -82t86 -281v-131q0 -47 -18.5 -66.5t-65.5 -19.5h-535q2 -109 18.5 -181.5t52.5 -116.5t90 -62.5t134 -18.5q53 0 123 12.5t123 35.5 q18 8 26 -9l15 -32q4 -10 1 -17.5t-20 -15.5q-55 -25 -128.5 -40t-139.5 -15q-147 0 -230 49t-112 143q-31 -86 -113 -139t-219 -53q-98 0 -162.5 22.5t-105.5 61.5q-66 59 -88.5 161.5t-22.5 229.5zM193 481q0 -127 12 -196.5t43 -116.5q70 -104 233 -105q162 0 234 105 q31 47 43 116.5t12 196.5v66v22q-4 104 -15.5 165t-39.5 102q-72 104 -234 104q-164 0 -233 -104q-31 -47 -43 -117t-12 -197v-41zM862 528h496q16 0 22 4.5t6 28.5v72q0 172 -59 239.5t-190 67.5q-68 0 -119 -18.5t-85 -64.5t-51.5 -126t-19.5 -203z" />
<glyph unicode="&#x178;" horiz-adv-x="954" d="M57 1303q-8 23 13 22h67q14 0 21 -16q139 -422 311 -734h16q172 311 312 734q6 16 20 16h68q20 0 12 -22q-45 -135 -87 -244t-85 -207t-92 -192.5t-109 -200.5v-436q0 -23 -18 -23h-57q-18 0 -19 23v436q-59 106 -108.5 200.5t-92.5 192.5t-85 206.5t-87 244.5zM233 1548 v43q0 39 15.5 52.5t54.5 13.5h20q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-20q-41 0 -55.5 13t-14.5 50zM563 1548v43q0 39 15 52.5t54 13.5h21q41 0 56.5 -13.5t15.5 -52.5v-43q0 -37 -15.5 -50t-56.5 -13h-21q-41 0 -55 13t-14 50z" />
<glyph unicode="&#x2c6;" horiz-adv-x="763" d="M98 1204q-16 12 2 31l228 250q12 14 30 14h50q18 0 30 -14l228 -250q16 -16 2 -31l-39 -33q-16 -12 -35 7l-205 211h-12l-205 -211q-20 -20 -35 -7z" />
<glyph unicode="&#x2dc;" horiz-adv-x="851" d="M100 1276q35 74 87.5 111.5t111.5 37.5q33 0 61.5 -12t59.5 -35l55 -43q23 -16 38 -23t38 -7q31 0 63.5 31.5t57.5 72.5q8 10 18 6l43 -29q16 -10 6 -26q-35 -66 -88 -110t-112 -44q-33 0 -60.5 10.5t-58.5 34.5l-53 41q-23 16 -40.5 25.5t-39.5 9.5q-59 0 -117 -102 q-8 -14 -25 -4l-41 26q-16 10 -4 29z" />
<glyph unicode="&#x2000;" horiz-adv-x="906" />
<glyph unicode="&#x2001;" horiz-adv-x="1812" />
<glyph unicode="&#x2002;" horiz-adv-x="906" />
<glyph unicode="&#x2003;" horiz-adv-x="1812" />
<glyph unicode="&#x2004;" horiz-adv-x="604" />
<glyph unicode="&#x2005;" horiz-adv-x="453" />
<glyph unicode="&#x2006;" horiz-adv-x="302" />
<glyph unicode="&#x2007;" horiz-adv-x="302" />
<glyph unicode="&#x2008;" horiz-adv-x="226" />
<glyph unicode="&#x2009;" horiz-adv-x="362" />
<glyph unicode="&#x200a;" horiz-adv-x="100" />
<glyph unicode="&#x2010;" horiz-adv-x="563" d="M72 504v61q0 14 14 15h391q14 0 15 -15v-61q0 -14 -15 -15h-391q-14 0 -14 15z" />
<glyph unicode="&#x2011;" horiz-adv-x="563" d="M72 504v61q0 14 14 15h391q14 0 15 -15v-61q0 -14 -15 -15h-391q-14 0 -14 15z" />
<glyph unicode="&#x2012;" horiz-adv-x="563" d="M72 504v61q0 14 14 15h391q14 0 15 -15v-61q0 -14 -15 -15h-391q-14 0 -14 15z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 504v61q0 14 14 15h996q14 0 14 -15v-61q0 -14 -14 -15h-996q-14 0 -14 15z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M0 504v61q0 14 14 15h2020q14 0 14 -15v-61q0 -14 -14 -15h-2020q-14 0 -14 15z" />
<glyph unicode="&#x2018;" horiz-adv-x="417" d="M143 1043v41q0 78 27 154.5t80 154.5q8 14 22 6l39 -22q12 -6 2 -21q-27 -41 -46 -82t-29 -76q-6 -27 16 -36l25 -11q37 -14 36 -59v-68q0 -68 -65 -67h-27q-39 0 -59.5 18.5t-20.5 67.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="417" d="M105 1270v68q0 68 65 67h27q39 0 59.5 -18.5t20.5 -67.5v-41q0 -78 -27 -154.5t-80 -154.5q-8 -14 -22 -6l-39 22q-12 6 -2 21q27 41 46 82t29 76q6 27 -16 36l-25 11q-37 14 -36 59z" />
<glyph unicode="&#x201a;" horiz-adv-x="417" d="M105 43v68q0 68 65 67h27q39 0 59.5 -18.5t20.5 -67.5v-41q0 -78 -27 -154.5t-80 -154.5q-8 -14 -22 -6l-39 22q-12 6 -2 21q27 41 46 82t29 76q6 27 -16 36l-25 11q-37 14 -36 59z" />
<glyph unicode="&#x201c;" horiz-adv-x="692" d="M143 1045v41q0 78 27 154.5t80 154.5q8 14 22 6l39 -22q12 -6 2 -21q-27 -41 -46 -82t-29 -76q-6 -27 16 -36l25 -11q37 -14 36 -59v-68q0 -68 -65 -67h-27q-39 0 -59.5 18.5t-20.5 67.5zM417 1045v41q0 78 27 154.5t80 154.5q8 14 22 6l39 -22q12 -6 2 -21 q-27 -41 -46 -82t-29 -76q-6 -27 16 -36l25 -11q37 -14 36 -59v-68q0 -68 -65 -67h-27q-39 0 -59.5 18.5t-20.5 67.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="694" d="M105 1270v68q0 68 65 67h27q39 0 59.5 -18.5t20.5 -67.5v-41q0 -78 -27 -154.5t-80 -154.5q-8 -14 -22 -6l-39 22q-12 6 -2 21q27 41 46 82t29 76q6 27 -16 36l-25 11q-37 14 -36 59zM379 1270v68q0 68 65 67h27q39 0 59.5 -18.5t20.5 -67.5v-41q0 -78 -27 -154.5 t-80 -154.5q-8 -14 -22 -6l-39 22q-12 6 -2 21q27 41 46 82t29 76q6 27 -16 36l-25 11q-37 14 -36 59z" />
<glyph unicode="&#x201e;" horiz-adv-x="692" d="M105 43v68q0 68 65 67h27q39 0 59.5 -18.5t20.5 -67.5v-41q0 -78 -27 -154.5t-80 -154.5q-8 -14 -22 -6l-39 22q-12 6 -2 21q27 41 46 82t29 76q6 27 -16 36l-25 11q-37 14 -36 59zM377 43v68q0 68 65 67h27q39 0 59.5 -18.5t20.5 -67.5v-41q0 -78 -27 -154.5t-80 -154.5 q-8 -14 -22 -6l-39 22q-12 6 -2 21q27 41 46 82t29 76q6 27 -16 36l-25 11q-37 14 -36 59z" />
<glyph unicode="&#x2022;" horiz-adv-x="698" d="M127 535q0 47 17.5 88t47 70.5t69.5 47t87 17.5t88 -17.5t71 -47t47 -70.5t17 -88t-17 -87t-47 -70t-71 -47.5t-88 -17.5q-94 0 -157.5 64t-63.5 158z" />
<glyph unicode="&#x2026;" horiz-adv-x="1136" d="M109 49v55q0 68 69 68h21q41 0 56 -16.5t15 -51.5v-55q0 -35 -15 -51t-56 -16h-21q-70 0 -69 67zM488 49v55q0 68 69 68h21q41 0 56 -16.5t15 -51.5v-55q0 -35 -15 -51t-56 -16h-21q-70 0 -69 67zM867 49v55q0 68 69 68h21q41 0 56 -16.5t15 -51.5v-55q0 -35 -15 -51 t-56 -16h-21q-70 0 -69 67z" />
<glyph unicode="&#x202f;" horiz-adv-x="362" />
<glyph unicode="&#x2039;" horiz-adv-x="557" d="M92 457v35q0 14 10 24l279 234q14 10 25 0l32 -33q10 -12 -2 -25l-233 -217l233 -221q4 -4 6 -11t-4 -14l-32 -32q-10 -10 -25 0l-279 235q-10 10 -10 25z" />
<glyph unicode="&#x203a;" horiz-adv-x="557" d="M115 242.5q2 7.5 6 11.5l233 221l-233 217q-4 4 -6 11.5t4 13.5l33 33q10 10 24 0l279 -234q10 -10 10 -24v-35q0 -14 -10 -25l-279 -235q-14 -10 -24 0l-33 32q-6 6 -4 13.5z" />
<glyph unicode="&#x205f;" horiz-adv-x="453" />
<glyph unicode="&#x20ac;" d="M39 479v62q0 12 14 12h150v145h-150q-14 0 -14 13v61q0 12 14 12h150q2 100 10 174t24.5 128.5t43 94.5t63.5 70q111 92 299 92q86 0 169 -19t155 -56q12 -6 16 -13.5t-4 -23.5l-14 -31q-8 -20 -33 -8q-68 31 -138.5 48t-148.5 17q-84 0 -140 -17t-104 -62 q-55 -53 -74.5 -144.5t-21.5 -249.5h457q14 0 14 -12v-61q0 -12 -14 -13h-457v-2v-143h457q14 0 14 -12v-62q0 -12 -14 -12h-457q2 -100 17.5 -175t54.5 -125t107.5 -75.5t179.5 -25.5q68 0 145.5 15t130.5 36q23 8 29 -11l12 -38q4 -14 0 -18.5t-12 -8.5q-66 -29 -150 -45 t-172 -16q-125 0 -208 28.5t-134 88t-73.5 151.5t-28.5 219h-150q-14 0 -14 12z" />
<glyph unicode="&#x2122;" horiz-adv-x="1316" d="M53 1272v41q0 12 13 12h407q10 0 10 -12v-41q0 -14 -10 -15h-166v-532q0 -14 -14 -14h-47q-12 0 -14.5 2t-2.5 12v532h-163q-12 0 -13 15zM569 727q2 78 6.5 147.5t8.5 138t9 139.5t11 151q0 23 25 22h76q25 0 30 -22q33 -125 64.5 -240t60.5 -199h8q27 82 58.5 191.5 t68.5 247.5q8 23 33 22h76q27 0 26 -22q6 -80 11.5 -151t9.5 -139.5t7 -138t7 -147.5q0 -16 -18 -16h-45q-14 0 -15 14q-4 123 -10 252t-16 258h-6q-35 -117 -65.5 -218t-65.5 -210q-6 -27 -35 -27h-51q-27 0 -35 27q-35 109 -64.5 210t-62.5 218h-6q-10 -129 -16.5 -258 t-10.5 -252q0 -14 -14 -14h-43q-16 0 -17 16z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1003" d="M0 0v1004h1004v-1004h-1004z" />
<hkern u1="&#x26;" u2="&#x178;" k="61" />
<hkern u1="&#x26;" u2="&#xdd;" k="61" />
<hkern u1="&#x26;" u2="Z" k="-31" />
<hkern u1="&#x26;" u2="Y" k="61" />
<hkern u1="&#x26;" u2="T" k="113" />
<hkern u1="&#x26;" u2="V" k="41" />
<hkern u1="&#x26;" u2="J" k="-61" />
<hkern u1="&#x28;" u2="&#x178;" k="-49" />
<hkern u1="&#x28;" u2="&#xdd;" k="-49" />
<hkern u1="&#x28;" u2="j" k="-244" />
<hkern u1="&#x28;" u2="g" k="-84" />
<hkern u1="&#x28;" u2="Y" k="-49" />
<hkern u1="&#x28;" u2="W" k="-61" />
<hkern u1="&#x28;" u2="T" k="-82" />
<hkern u1="&#x28;" u2="X" k="-51" />
<hkern u1="&#x28;" u2="V" k="-82" />
<hkern u1="&#x28;" u2="J" k="-49" />
<hkern u1="&#x2a;" u2="&#x178;" k="-49" />
<hkern u1="&#x2a;" u2="&#xdd;" k="-49" />
<hkern u1="&#x2a;" u2="&#xc5;" k="143" />
<hkern u1="&#x2a;" u2="&#xc4;" k="143" />
<hkern u1="&#x2a;" u2="&#xc3;" k="143" />
<hkern u1="&#x2a;" u2="&#xc2;" k="143" />
<hkern u1="&#x2a;" u2="&#xc1;" k="143" />
<hkern u1="&#x2a;" u2="&#xc0;" k="143" />
<hkern u1="&#x2a;" u2="Y" k="-49" />
<hkern u1="&#x2a;" u2="W" k="-49" />
<hkern u1="&#x2a;" u2="T" k="-61" />
<hkern u1="&#x2a;" u2="A" k="143" />
<hkern u1="&#x2a;" u2="V" k="-49" />
<hkern u1="&#x2c;" u2="v" k="113" />
<hkern u1="&#x2c;" u2="V" k="164" />
<hkern u1="&#x2c;" u2="J" k="-49" />
<hkern u1="&#x2e;" u2="v" k="113" />
<hkern u1="&#x2e;" u2="V" k="164" />
<hkern u1="&#x2e;" u2="J" k="-49" />
<hkern u1="&#x2f;" g2="uniFB02" k="-31" />
<hkern u1="&#x2f;" g2="uniFB01" k="-31" />
<hkern u1="&#x2f;" u2="&#x178;" k="-72" />
<hkern u1="&#x2f;" u2="&#x153;" k="31" />
<hkern u1="&#x2f;" u2="&#xf8;" k="31" />
<hkern u1="&#x2f;" u2="&#xf6;" k="31" />
<hkern u1="&#x2f;" u2="&#xf5;" k="31" />
<hkern u1="&#x2f;" u2="&#xf4;" k="31" />
<hkern u1="&#x2f;" u2="&#xf3;" k="31" />
<hkern u1="&#x2f;" u2="&#xf2;" k="31" />
<hkern u1="&#x2f;" u2="&#xf0;" k="31" />
<hkern u1="&#x2f;" u2="&#xdd;" k="-72" />
<hkern u1="&#x2f;" u2="o" k="31" />
<hkern u1="&#x2f;" u2="g" k="72" />
<hkern u1="&#x2f;" u2="f" k="-31" />
<hkern u1="&#x2f;" u2="Y" k="-72" />
<hkern u1="&#x2f;" u2="W" k="-49" />
<hkern u1="&#x2f;" u2="T" k="-98" />
<hkern u1="&#x2f;" u2="V" k="-51" />
<hkern u1="A" u2="&#x2122;" k="123" />
<hkern u1="A" u2="&#x203a;" k="-31" />
<hkern u1="A" u2="&#xbb;" k="-31" />
<hkern u1="A" u2="v" k="82" />
<hkern u1="A" u2="q" k="8" />
<hkern u1="A" u2="b" k="20" />
<hkern u1="A" u2="\" k="143" />
<hkern u1="A" u2="V" k="78" />
<hkern u1="A" u2="Q" k="20" />
<hkern u1="A" u2="J" k="-51" />
<hkern u1="A" u2="E" k="10" />
<hkern u1="A" u2="&#x3f;" k="51" />
<hkern u1="A" u2="&#x2a;" k="143" />
<hkern u1="B" u2="T" k="61" />
<hkern u1="B" u2="&#x2122;" k="92" />
<hkern u1="B" u2="&#x29;" k="31" />
<hkern u1="C" u2="q" k="20" />
<hkern u1="C" u2="X" k="-20" />
<hkern u1="C" u2="V" k="-31" />
<hkern u1="C" u2="J" k="-51" />
<hkern u1="C" u2="&#x29;" k="-72" />
<hkern u1="D" u2="&#x2122;" k="59" />
<hkern u1="D" u2="v" k="-31" />
<hkern u1="D" u2="X" k="41" />
<hkern u1="E" u2="V" k="-10" />
<hkern u1="E" u2="Q" k="10" />
<hkern u1="E" u2="J" k="-20" />
<hkern u1="E" u2="B" k="10" />
<hkern u1="F" u2="&#x2026;" k="252" />
<hkern u1="F" u2="&#x201e;" k="252" />
<hkern u1="F" u2="&#x201a;" k="252" />
<hkern u1="F" u2="&#x178;" k="-31" />
<hkern u1="F" u2="&#x153;" k="25" />
<hkern u1="F" u2="&#x152;" k="33" />
<hkern u1="F" u2="&#xf8;" k="25" />
<hkern u1="F" u2="&#xf6;" k="25" />
<hkern u1="F" u2="&#xf5;" k="25" />
<hkern u1="F" u2="&#xf4;" k="25" />
<hkern u1="F" u2="&#xf3;" k="25" />
<hkern u1="F" u2="&#xf2;" k="25" />
<hkern u1="F" u2="&#xf1;" k="20" />
<hkern u1="F" u2="&#xf0;" k="25" />
<hkern u1="F" u2="&#xeb;" k="25" />
<hkern u1="F" u2="&#xea;" k="25" />
<hkern u1="F" u2="&#xe9;" k="25" />
<hkern u1="F" u2="&#xe8;" k="25" />
<hkern u1="F" u2="&#xdd;" k="-31" />
<hkern u1="F" u2="&#xd8;" k="33" />
<hkern u1="F" u2="&#xd6;" k="33" />
<hkern u1="F" u2="&#xd5;" k="33" />
<hkern u1="F" u2="&#xd4;" k="33" />
<hkern u1="F" u2="&#xd3;" k="33" />
<hkern u1="F" u2="&#xd2;" k="33" />
<hkern u1="F" u2="&#xc7;" k="33" />
<hkern u1="F" u2="&#xc6;" k="195" />
<hkern u1="F" u2="&#xc5;" k="115" />
<hkern u1="F" u2="&#xc4;" k="115" />
<hkern u1="F" u2="&#xc3;" k="115" />
<hkern u1="F" u2="&#xc2;" k="115" />
<hkern u1="F" u2="&#xc1;" k="115" />
<hkern u1="F" u2="&#xc0;" k="115" />
<hkern u1="F" u2="r" k="20" />
<hkern u1="F" u2="o" k="25" />
<hkern u1="F" u2="n" k="20" />
<hkern u1="F" u2="g" k="16" />
<hkern u1="F" u2="e" k="25" />
<hkern u1="F" u2="d" k="25" />
<hkern u1="F" u2="Y" k="-31" />
<hkern u1="F" u2="W" k="-31" />
<hkern u1="F" u2="T" k="-20" />
<hkern u1="F" u2="O" k="33" />
<hkern u1="F" u2="G" k="33" />
<hkern u1="F" u2="C" k="33" />
<hkern u1="F" u2="A" k="115" />
<hkern u1="F" u2="&#x2e;" k="252" />
<hkern u1="F" u2="&#x2c;" k="252" />
<hkern u1="F" u2="&#xef;" k="-29" />
<hkern u1="F" u2="&#xee;" k="-51" />
<hkern u1="F" u2="&#x7d;" k="-74" />
<hkern u1="F" u2="q" k="20" />
<hkern u1="F" u2="p" k="20" />
<hkern u1="F" u2="m" k="20" />
<hkern u1="F" u2="]" k="-86" />
<hkern u1="F" u2="V" k="-31" />
<hkern u1="F" u2="Q" k="33" />
<hkern u1="F" u2="J" k="98" />
<hkern u1="F" u2="&#x3f;" k="-16" />
<hkern u1="F" u2="&#x2f;" k="111" />
<hkern u1="F" u2="&#x29;" k="-86" />
<hkern u1="G" u2="J" k="-20" />
<hkern u1="G" u2="&#x29;" k="-72" />
<hkern u1="K" u2="&#x7d;" k="-96" />
<hkern u1="K" u2="v" k="41" />
<hkern u1="K" u2="q" k="25" />
<hkern u1="K" u2="X" k="-20" />
<hkern u1="K" u2="V" k="-31" />
<hkern u1="K" u2="J" k="-78" />
<hkern u1="K" u2="&#x2f;" k="-49" />
<hkern u1="K" u2="&#x29;" k="-121" />
<hkern u1="L" u2="&#x2122;" k="164" />
<hkern u1="L" u2="x" k="-25" />
<hkern u1="L" u2="v" k="45" />
<hkern u1="L" u2="]" k="-96" />
<hkern u1="L" u2="\" k="113" />
<hkern u1="L" u2="X" k="-31" />
<hkern u1="L" u2="V" k="92" />
<hkern u1="L" u2="J" k="-78" />
<hkern u1="L" u2="&#x3f;" k="51" />
<hkern u1="L" u2="&#x2f;" k="-61" />
<hkern u1="L" u2="&#x2a;" k="143" />
<hkern u1="L" u2="&#x26;" k="-10" />
<hkern u1="M" u2="&#x178;" k="51" />
<hkern u1="M" u2="&#xdd;" k="51" />
<hkern u1="M" u2="Y" k="51" />
<hkern u1="M" u2="T" k="33" />
<hkern u1="O" u2="X" k="41" />
<hkern u1="O" u2="V" k="31" />
<hkern u1="P" u2="&#x2026;" k="307" />
<hkern u1="P" u2="&#x201e;" k="307" />
<hkern u1="P" u2="&#x201a;" k="307" />
<hkern u1="P" u2="&#x153;" k="31" />
<hkern u1="P" u2="&#xff;" k="-16" />
<hkern u1="P" u2="&#xfd;" k="-16" />
<hkern u1="P" u2="&#xf8;" k="31" />
<hkern u1="P" u2="&#xf6;" k="31" />
<hkern u1="P" u2="&#xf5;" k="31" />
<hkern u1="P" u2="&#xf4;" k="31" />
<hkern u1="P" u2="&#xf3;" k="31" />
<hkern u1="P" u2="&#xf2;" k="31" />
<hkern u1="P" u2="&#xf0;" k="31" />
<hkern u1="P" u2="&#xe6;" k="45" />
<hkern u1="P" u2="&#xe5;" k="45" />
<hkern u1="P" u2="&#xe4;" k="45" />
<hkern u1="P" u2="&#xe3;" k="45" />
<hkern u1="P" u2="&#xe2;" k="45" />
<hkern u1="P" u2="&#xe1;" k="45" />
<hkern u1="P" u2="&#xe0;" k="45" />
<hkern u1="P" u2="&#xc6;" k="231" />
<hkern u1="P" u2="&#xc5;" k="115" />
<hkern u1="P" u2="&#xc4;" k="115" />
<hkern u1="P" u2="&#xc3;" k="115" />
<hkern u1="P" u2="&#xc2;" k="115" />
<hkern u1="P" u2="&#xc1;" k="115" />
<hkern u1="P" u2="&#xc0;" k="115" />
<hkern u1="P" u2="y" k="-16" />
<hkern u1="P" u2="w" k="-16" />
<hkern u1="P" u2="t" k="-20" />
<hkern u1="P" u2="s" k="25" />
<hkern u1="P" u2="o" k="31" />
<hkern u1="P" u2="g" k="25" />
<hkern u1="P" u2="a" k="45" />
<hkern u1="P" u2="Z" k="41" />
<hkern u1="P" u2="A" k="115" />
<hkern u1="P" u2="&#x2e;" k="307" />
<hkern u1="P" u2="&#x2c;" k="307" />
<hkern u1="P" u2="v" k="-16" />
<hkern u1="P" u2="X" k="41" />
<hkern u1="P" u2="J" k="92" />
<hkern u1="P" u2="&#x2f;" k="133" />
<hkern u1="P" u2="&#x29;" k="-10" />
<hkern u1="P" u2="&#x26;" k="66" />
<hkern u1="Q" u2="&#x178;" k="37" />
<hkern u1="Q" u2="&#xdd;" k="37" />
<hkern u1="Q" u2="&#xc6;" k="61" />
<hkern u1="Q" u2="&#xc5;" k="20" />
<hkern u1="Q" u2="&#xc4;" k="20" />
<hkern u1="Q" u2="&#xc3;" k="20" />
<hkern u1="Q" u2="&#xc2;" k="20" />
<hkern u1="Q" u2="&#xc1;" k="20" />
<hkern u1="Q" u2="&#xc0;" k="20" />
<hkern u1="Q" u2="j" k="-18" />
<hkern u1="Q" u2="Y" k="37" />
<hkern u1="Q" u2="T" k="61" />
<hkern u1="Q" u2="A" k="20" />
<hkern u1="Q" u2="X" k="41" />
<hkern u1="Q" u2="V" k="31" />
<hkern u1="R" u2="&#x2122;" k="61" />
<hkern u1="R" u2="x" k="-20" />
<hkern u1="R" u2="\" k="41" />
<hkern u1="R" u2="X" k="-41" />
<hkern u1="R" u2="J" k="-51" />
<hkern u1="R" u2="&#x29;" k="-20" />
<hkern u1="S" u2="J" k="-16" />
<hkern u1="T" u2="&#xef;" k="-31" />
<hkern u1="T" u2="&#xee;" k="-59" />
<hkern u1="T" u2="&#xec;" k="-23" />
<hkern u1="T" u2="&#xe3;" k="119" />
<hkern u1="T" u2="&#x7d;" k="-96" />
<hkern u1="T" u2="x" k="113" />
<hkern u1="T" u2="v" k="113" />
<hkern u1="T" u2="q" k="147" />
<hkern u1="T" u2="p" k="133" />
<hkern u1="T" u2="m" k="82" />
<hkern u1="T" u2="h" k="41" />
<hkern u1="T" u2="b" k="20" />
<hkern u1="T" u2="]" k="-96" />
<hkern u1="T" u2="\" k="-109" />
<hkern u1="T" u2="V" k="-31" />
<hkern u1="T" u2="Q" k="61" />
<hkern u1="T" u2="M" k="33" />
<hkern u1="T" u2="J" k="82" />
<hkern u1="T" u2="F" k="20" />
<hkern u1="T" u2="E" k="31" />
<hkern u1="T" u2="&#x40;" k="41" />
<hkern u1="T" u2="&#x2f;" k="86" />
<hkern u1="T" u2="&#x2a;" k="-61" />
<hkern u1="T" u2="&#x29;" k="-82" />
<hkern u1="V" u2="&#x2026;" k="164" />
<hkern u1="V" u2="&#x201e;" k="164" />
<hkern u1="V" u2="&#x201a;" k="164" />
<hkern u1="V" u2="&#x178;" k="-20" />
<hkern u1="V" u2="&#x153;" k="66" />
<hkern u1="V" u2="&#x152;" k="31" />
<hkern u1="V" u2="&#xfc;" k="16" />
<hkern u1="V" u2="&#xfb;" k="16" />
<hkern u1="V" u2="&#xfa;" k="16" />
<hkern u1="V" u2="&#xf9;" k="16" />
<hkern u1="V" u2="&#xf8;" k="66" />
<hkern u1="V" u2="&#xf6;" k="66" />
<hkern u1="V" u2="&#xf5;" k="66" />
<hkern u1="V" u2="&#xf4;" k="66" />
<hkern u1="V" u2="&#xf3;" k="66" />
<hkern u1="V" u2="&#xf2;" k="66" />
<hkern u1="V" u2="&#xf1;" k="74" />
<hkern u1="V" u2="&#xf0;" k="66" />
<hkern u1="V" u2="&#xe6;" k="61" />
<hkern u1="V" u2="&#xe5;" k="61" />
<hkern u1="V" u2="&#xe4;" k="61" />
<hkern u1="V" u2="&#xe3;" k="61" />
<hkern u1="V" u2="&#xe2;" k="61" />
<hkern u1="V" u2="&#xe1;" k="61" />
<hkern u1="V" u2="&#xe0;" k="61" />
<hkern u1="V" u2="&#xdd;" k="-20" />
<hkern u1="V" u2="&#xd8;" k="31" />
<hkern u1="V" u2="&#xd6;" k="31" />
<hkern u1="V" u2="&#xd5;" k="31" />
<hkern u1="V" u2="&#xd4;" k="31" />
<hkern u1="V" u2="&#xd3;" k="31" />
<hkern u1="V" u2="&#xd2;" k="31" />
<hkern u1="V" u2="&#xc6;" k="195" />
<hkern u1="V" u2="&#xc5;" k="78" />
<hkern u1="V" u2="&#xc4;" k="78" />
<hkern u1="V" u2="&#xc3;" k="78" />
<hkern u1="V" u2="&#xc2;" k="78" />
<hkern u1="V" u2="&#xc1;" k="78" />
<hkern u1="V" u2="&#xc0;" k="78" />
<hkern u1="V" u2="z" k="20" />
<hkern u1="V" u2="u" k="16" />
<hkern u1="V" u2="t" k="-16" />
<hkern u1="V" u2="s" k="51" />
<hkern u1="V" u2="r" k="74" />
<hkern u1="V" u2="o" k="66" />
<hkern u1="V" u2="n" k="74" />
<hkern u1="V" u2="g" k="82" />
<hkern u1="V" u2="a" k="61" />
<hkern u1="V" u2="Y" k="-20" />
<hkern u1="V" u2="W" k="-20" />
<hkern u1="V" u2="T" k="-31" />
<hkern u1="V" u2="O" k="31" />
<hkern u1="V" u2="A" k="78" />
<hkern u1="V" u2="&#x3b;" k="72" />
<hkern u1="V" u2="&#x3a;" k="72" />
<hkern u1="V" u2="&#x2e;" k="164" />
<hkern u1="V" u2="&#x2c;" k="164" />
<hkern u1="V" u2="&#xef;" k="-29" />
<hkern u1="V" u2="&#xee;" k="-61" />
<hkern u1="V" u2="&#xec;" k="-29" />
<hkern u1="V" u2="&#xbb;" k="20" />
<hkern u1="V" u2="&#x7d;" k="-61" />
<hkern u1="V" u2="x" k="20" />
<hkern u1="V" u2="p" k="41" />
<hkern u1="V" u2="m" k="74" />
<hkern u1="V" u2="b" k="16" />
<hkern u1="V" u2="]" k="-74" />
<hkern u1="V" u2="\" k="-74" />
<hkern u1="V" u2="V" k="-20" />
<hkern u1="V" u2="Q" k="31" />
<hkern u1="V" u2="J" k="68" />
<hkern u1="V" u2="F" k="20" />
<hkern u1="V" u2="&#x40;" k="41" />
<hkern u1="V" u2="&#x2f;" k="147" />
<hkern u1="V" u2="&#x2a;" k="-49" />
<hkern u1="V" u2="&#x29;" k="-82" />
<hkern u1="V" u2="&#x26;" k="31" />
<hkern u1="W" u2="&#xef;" k="-45" />
<hkern u1="W" u2="&#x7d;" k="-74" />
<hkern u1="W" u2="p" k="25" />
<hkern u1="W" u2="b" k="8" />
<hkern u1="W" u2="]" k="-86" />
<hkern u1="W" u2="\" k="-72" />
<hkern u1="W" u2="V" k="-20" />
<hkern u1="W" u2="J" k="55" />
<hkern u1="W" u2="F" k="10" />
<hkern u1="W" u2="&#x40;" k="31" />
<hkern u1="W" u2="&#x2f;" k="102" />
<hkern u1="W" u2="&#x2a;" k="-49" />
<hkern u1="W" u2="&#x29;" k="-61" />
<hkern u1="W" u2="&#x26;" k="51" />
<hkern u1="X" g2="uniFB02" k="6" />
<hkern u1="X" g2="uniFB01" k="6" />
<hkern u1="X" u2="&#x153;" k="31" />
<hkern u1="X" u2="&#x152;" k="41" />
<hkern u1="X" u2="&#xff;" k="45" />
<hkern u1="X" u2="&#xfd;" k="45" />
<hkern u1="X" u2="&#xf8;" k="31" />
<hkern u1="X" u2="&#xf6;" k="31" />
<hkern u1="X" u2="&#xf5;" k="31" />
<hkern u1="X" u2="&#xf4;" k="31" />
<hkern u1="X" u2="&#xf3;" k="31" />
<hkern u1="X" u2="&#xf2;" k="31" />
<hkern u1="X" u2="&#xf1;" k="10" />
<hkern u1="X" u2="&#xf0;" k="31" />
<hkern u1="X" u2="&#xeb;" k="31" />
<hkern u1="X" u2="&#xea;" k="31" />
<hkern u1="X" u2="&#xe9;" k="31" />
<hkern u1="X" u2="&#xe8;" k="31" />
<hkern u1="X" u2="&#xe7;" k="31" />
<hkern u1="X" u2="&#xd8;" k="41" />
<hkern u1="X" u2="&#xd6;" k="41" />
<hkern u1="X" u2="&#xd5;" k="41" />
<hkern u1="X" u2="&#xd4;" k="41" />
<hkern u1="X" u2="&#xd3;" k="41" />
<hkern u1="X" u2="&#xd2;" k="41" />
<hkern u1="X" u2="&#xc7;" k="41" />
<hkern u1="X" u2="z" k="-41" />
<hkern u1="X" u2="y" k="45" />
<hkern u1="X" u2="w" k="31" />
<hkern u1="X" u2="t" k="33" />
<hkern u1="X" u2="o" k="31" />
<hkern u1="X" u2="n" k="10" />
<hkern u1="X" u2="g" k="57" />
<hkern u1="X" u2="f" k="6" />
<hkern u1="X" u2="e" k="31" />
<hkern u1="X" u2="d" k="31" />
<hkern u1="X" u2="c" k="31" />
<hkern u1="X" u2="O" k="41" />
<hkern u1="X" u2="G" k="41" />
<hkern u1="X" u2="C" k="41" />
<hkern u1="X" u2="&#xbb;" k="20" />
<hkern u1="X" u2="v" k="39" />
<hkern u1="X" u2="q" k="31" />
<hkern u1="X" u2="Q" k="41" />
<hkern u1="X" u2="J" k="-51" />
<hkern u1="X" u2="&#x29;" k="-61" />
<hkern u1="Y" u2="&#xef;" k="-31" />
<hkern u1="Y" u2="&#xee;" k="-39" />
<hkern u1="Y" u2="&#xbb;" k="20" />
<hkern u1="Y" u2="&#x7d;" k="-59" />
<hkern u1="Y" u2="x" k="20" />
<hkern u1="Y" u2="q" k="102" />
<hkern u1="Y" u2="p" k="78" />
<hkern u1="Y" u2="h" k="31" />
<hkern u1="Y" u2="b" k="31" />
<hkern u1="Y" u2="]" k="-61" />
<hkern u1="Y" u2="\" k="-98" />
<hkern u1="Y" u2="V" k="-20" />
<hkern u1="Y" u2="Q" k="37" />
<hkern u1="Y" u2="M" k="51" />
<hkern u1="Y" u2="J" k="92" />
<hkern u1="Y" u2="E" k="20" />
<hkern u1="Y" u2="&#x40;" k="41" />
<hkern u1="Y" u2="&#x2f;" k="160" />
<hkern u1="Y" u2="&#x2a;" k="-49" />
<hkern u1="Y" u2="&#x29;" k="-59" />
<hkern u1="Y" u2="&#x26;" k="72" />
<hkern u1="Z" u2="&#x7d;" k="-59" />
<hkern u1="Z" u2="v" k="25" />
<hkern u1="Z" u2="p" k="6" />
<hkern u1="Z" u2="]" k="-72" />
<hkern u1="Z" u2="V" k="-20" />
<hkern u1="Z" u2="J" k="-41" />
<hkern u1="Z" u2="&#x40;" k="20" />
<hkern u1="Z" u2="&#x26;" k="51" />
<hkern u1="[" u2="&#x178;" k="-61" />
<hkern u1="[" u2="&#xdd;" k="-61" />
<hkern u1="[" u2="z" k="-31" />
<hkern u1="[" u2="j" k="-207" />
<hkern u1="[" u2="Z" k="-61" />
<hkern u1="[" u2="Y" k="-61" />
<hkern u1="[" u2="W" k="-74" />
<hkern u1="[" u2="T" k="-96" />
<hkern u1="[" u2="V" k="-74" />
<hkern u1="[" u2="J" k="-86" />
<hkern u1="\" u2="&#x178;" k="86" />
<hkern u1="\" u2="&#xff;" k="51" />
<hkern u1="\" u2="&#xfd;" k="51" />
<hkern u1="\" u2="&#xdd;" k="86" />
<hkern u1="\" u2="y" k="51" />
<hkern u1="\" u2="Y" k="86" />
<hkern u1="\" u2="W" k="61" />
<hkern u1="\" u2="T" k="109" />
<hkern u1="\" u2="v" k="82" />
<hkern u1="\" u2="V" k="109" />
<hkern u1="a" u2="&#x2122;" k="82" />
<hkern u1="a" u2="v" k="31" />
<hkern u1="a" u2="\" k="61" />
<hkern u1="a" u2="&#x2a;" k="51" />
<hkern u1="b" u2="&#x2122;" k="92" />
<hkern u1="c" u2="v" k="-37" />
<hkern u1="c" u2="\" k="20" />
<hkern u1="e" u2="&#x2122;" k="61" />
<hkern u1="e" u2="]" k="-20" />
<hkern u1="e" u2="\" k="82" />
<hkern u1="f" g2="uniFB02" k="-20" />
<hkern u1="f" g2="uniFB01" k="-20" />
<hkern u1="f" u2="&#x2026;" k="113" />
<hkern u1="f" u2="&#x201e;" k="113" />
<hkern u1="f" u2="&#x201d;" k="-113" />
<hkern u1="f" u2="&#x201a;" k="113" />
<hkern u1="f" u2="&#x2019;" k="-113" />
<hkern u1="f" u2="&#xff;" k="-20" />
<hkern u1="f" u2="&#xfd;" k="-20" />
<hkern u1="f" u2="y" k="-20" />
<hkern u1="f" u2="w" k="-37" />
<hkern u1="f" u2="t" k="-20" />
<hkern u1="f" u2="l" k="-49" />
<hkern u1="f" u2="j" k="-49" />
<hkern u1="f" u2="i" k="-61" />
<hkern u1="f" u2="f" k="-20" />
<hkern u1="f" u2="&#x2e;" k="113" />
<hkern u1="f" u2="&#x2c;" k="113" />
<hkern u1="f" u2="&#x27;" k="-123" />
<hkern u1="f" u2="&#x22;" k="-123" />
<hkern u1="f" u2="&#x2122;" k="-133" />
<hkern u1="f" u2="&#xef;" k="-215" />
<hkern u1="f" u2="&#xee;" k="-188" />
<hkern u1="f" u2="&#xed;" k="-27" />
<hkern u1="f" u2="&#xec;" k="-195" />
<hkern u1="f" u2="v" k="-33" />
<hkern u1="f" u2="k" k="-53" />
<hkern u1="f" u2="h" k="-49" />
<hkern u1="f" u2="b" k="-72" />
<hkern u1="f" u2="]" k="-219" />
<hkern u1="f" u2="\" k="-178" />
<hkern u1="f" u2="&#x3f;" k="-154" />
<hkern u1="f" u2="&#x2a;" k="-143" />
<hkern u1="f" u2="&#x29;" k="-197" />
<hkern u1="g" u2="&#x2122;" k="41" />
<hkern u1="g" u2="&#x7d;" k="-59" />
<hkern u1="g" u2="]" k="-61" />
<hkern u1="g" u2="\" k="31" />
<hkern u1="g" u2="&#x29;" k="-82" />
<hkern u1="i" u2="&#x2f;" k="20" />
<hkern u1="i" u2="&#x2a;" k="-10" />
<hkern u1="j" u2="\" k="25" />
<hkern u1="j" u2="&#x2f;" k="20" />
<hkern u1="k" u2="v" k="-16" />
<hkern u1="l" u2="v" k="31" />
<hkern u1="l" u2="&#x2a;" k="33" />
<hkern u1="n" u2="&#x2122;" k="113" />
<hkern u1="n" u2="v" k="31" />
<hkern u1="n" u2="]" k="-20" />
<hkern u1="n" u2="\" k="70" />
<hkern u1="n" u2="&#x2a;" k="82" />
<hkern u1="o" u2="&#x2122;" k="92" />
<hkern u1="o" u2="x" k="6" />
<hkern u1="o" u2="v" k="6" />
<hkern u1="o" u2="\" k="90" />
<hkern u1="o" u2="&#x2a;" k="72" />
<hkern u1="p" u2="&#x2122;" k="92" />
<hkern u1="q" u2="j" k="-135" />
<hkern u1="q" u2="&#x2122;" k="61" />
<hkern u1="q" u2="]" k="-51" />
<hkern u1="q" u2="\" k="31" />
<hkern u1="q" u2="&#x2a;" k="31" />
<hkern u1="r" u2="v" k="-29" />
<hkern u1="r" u2="q" k="16" />
<hkern u1="r" u2="\" k="35" />
<hkern u1="s" u2="&#x2122;" k="61" />
<hkern u1="s" u2="\" k="51" />
<hkern u1="s" u2="&#x2a;" k="31" />
<hkern u1="t" u2="q" k="-8" />
<hkern u1="t" u2="\" k="20" />
<hkern u1="t" u2="&#x2f;" k="-74" />
<hkern u1="u" u2="&#x2122;" k="37" />
<hkern u1="u" u2="\" k="20" />
<hkern u1="u" u2="&#x2a;" k="20" />
<hkern u1="v" g2="uniFB02" k="-31" />
<hkern u1="v" g2="uniFB01" k="-31" />
<hkern u1="v" u2="&#x2026;" k="113" />
<hkern u1="v" u2="&#x201e;" k="113" />
<hkern u1="v" u2="&#x201a;" k="113" />
<hkern u1="v" u2="&#x153;" k="6" />
<hkern u1="v" u2="&#xff;" k="-6" />
<hkern u1="v" u2="&#xfd;" k="-6" />
<hkern u1="v" u2="&#xf8;" k="6" />
<hkern u1="v" u2="&#xf6;" k="6" />
<hkern u1="v" u2="&#xf5;" k="6" />
<hkern u1="v" u2="&#xf4;" k="6" />
<hkern u1="v" u2="&#xf3;" k="6" />
<hkern u1="v" u2="&#xf2;" k="6" />
<hkern u1="v" u2="&#xf0;" k="6" />
<hkern u1="v" u2="&#xe6;" k="10" />
<hkern u1="v" u2="&#xe5;" k="10" />
<hkern u1="v" u2="&#xe4;" k="10" />
<hkern u1="v" u2="&#xe3;" k="10" />
<hkern u1="v" u2="&#xe2;" k="10" />
<hkern u1="v" u2="&#xe1;" k="10" />
<hkern u1="v" u2="&#xe0;" k="10" />
<hkern u1="v" u2="y" k="-6" />
<hkern u1="v" u2="w" k="-16" />
<hkern u1="v" u2="t" k="-37" />
<hkern u1="v" u2="o" k="6" />
<hkern u1="v" u2="f" k="-31" />
<hkern u1="v" u2="a" k="10" />
<hkern u1="v" u2="&#x3b;" k="41" />
<hkern u1="v" u2="&#x3a;" k="41" />
<hkern u1="v" u2="&#x2e;" k="113" />
<hkern u1="v" u2="&#x2c;" k="113" />
<hkern u1="v" u2="v" k="-16" />
<hkern u1="w" u2="&#x2122;" k="31" />
<hkern u1="w" u2="v" k="-16" />
<hkern u1="x" u2="&#x153;" k="6" />
<hkern u1="x" u2="&#xf8;" k="6" />
<hkern u1="x" u2="&#xf6;" k="6" />
<hkern u1="x" u2="&#xf5;" k="6" />
<hkern u1="x" u2="&#xf4;" k="6" />
<hkern u1="x" u2="&#xf3;" k="6" />
<hkern u1="x" u2="&#xf2;" k="6" />
<hkern u1="x" u2="&#xf0;" k="6" />
<hkern u1="x" u2="o" k="6" />
<hkern u1="x" u2="&#x2122;" k="41" />
<hkern u1="x" u2="\" k="41" />
<hkern u1="y" u2="&#x2122;" k="31" />
<hkern u1="y" u2="v" k="-6" />
<hkern u1="z" u2="&#x2122;" k="51" />
<hkern u1="z" u2="v" k="-16" />
<hkern u1="z" u2="]" k="-31" />
<hkern u1="&#x7b;" u2="&#x178;" k="-59" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-59" />
<hkern u1="&#x7b;" u2="j" k="-207" />
<hkern u1="&#x7b;" u2="g" k="-59" />
<hkern u1="&#x7b;" u2="Y" k="-59" />
<hkern u1="&#x7b;" u2="W" k="-59" />
<hkern u1="&#x7b;" u2="T" k="-72" />
<hkern u1="&#x7b;" u2="V" k="-72" />
<hkern u1="&#x7b;" u2="J" k="-96" />
<hkern u1="&#xab;" u2="&#xc5;" k="-31" />
<hkern u1="&#xab;" u2="&#xc4;" k="-31" />
<hkern u1="&#xab;" u2="&#xc3;" k="-31" />
<hkern u1="&#xab;" u2="&#xc2;" k="-31" />
<hkern u1="&#xab;" u2="&#xc1;" k="-31" />
<hkern u1="&#xab;" u2="&#xc0;" k="-31" />
<hkern u1="&#xab;" u2="A" k="-31" />
<hkern u1="&#xab;" u2="X" k="20" />
<hkern u1="&#xab;" u2="V" k="20" />
<hkern u1="&#xbf;" u2="j" k="-164" />
<hkern u1="&#xbf;" u2="T" k="109" />
<hkern u1="&#xc0;" u2="&#x2122;" k="123" />
<hkern u1="&#xc0;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc0;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc0;" u2="v" k="82" />
<hkern u1="&#xc0;" u2="q" k="8" />
<hkern u1="&#xc0;" u2="b" k="20" />
<hkern u1="&#xc0;" u2="\" k="143" />
<hkern u1="&#xc0;" u2="V" k="78" />
<hkern u1="&#xc0;" u2="Q" k="20" />
<hkern u1="&#xc0;" u2="J" k="-51" />
<hkern u1="&#xc0;" u2="E" k="10" />
<hkern u1="&#xc0;" u2="&#x3f;" k="51" />
<hkern u1="&#xc0;" u2="&#x2a;" k="143" />
<hkern u1="&#xc1;" u2="&#x2122;" k="123" />
<hkern u1="&#xc1;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc1;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc1;" u2="v" k="82" />
<hkern u1="&#xc1;" u2="q" k="8" />
<hkern u1="&#xc1;" u2="b" k="20" />
<hkern u1="&#xc1;" u2="\" k="143" />
<hkern u1="&#xc1;" u2="V" k="78" />
<hkern u1="&#xc1;" u2="Q" k="20" />
<hkern u1="&#xc1;" u2="J" k="-51" />
<hkern u1="&#xc1;" u2="E" k="10" />
<hkern u1="&#xc1;" u2="&#x3f;" k="51" />
<hkern u1="&#xc1;" u2="&#x2a;" k="143" />
<hkern u1="&#xc2;" u2="&#x2122;" k="123" />
<hkern u1="&#xc2;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc2;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc2;" u2="v" k="82" />
<hkern u1="&#xc2;" u2="q" k="8" />
<hkern u1="&#xc2;" u2="b" k="20" />
<hkern u1="&#xc2;" u2="\" k="143" />
<hkern u1="&#xc2;" u2="V" k="78" />
<hkern u1="&#xc2;" u2="Q" k="20" />
<hkern u1="&#xc2;" u2="J" k="-51" />
<hkern u1="&#xc2;" u2="E" k="10" />
<hkern u1="&#xc2;" u2="&#x3f;" k="51" />
<hkern u1="&#xc2;" u2="&#x2a;" k="143" />
<hkern u1="&#xc3;" u2="&#x2122;" k="123" />
<hkern u1="&#xc3;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc3;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc3;" u2="v" k="82" />
<hkern u1="&#xc3;" u2="q" k="8" />
<hkern u1="&#xc3;" u2="b" k="20" />
<hkern u1="&#xc3;" u2="\" k="143" />
<hkern u1="&#xc3;" u2="V" k="78" />
<hkern u1="&#xc3;" u2="Q" k="20" />
<hkern u1="&#xc3;" u2="J" k="-51" />
<hkern u1="&#xc3;" u2="E" k="10" />
<hkern u1="&#xc3;" u2="&#x3f;" k="51" />
<hkern u1="&#xc3;" u2="&#x2a;" k="143" />
<hkern u1="&#xc4;" u2="&#x2122;" k="123" />
<hkern u1="&#xc4;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc4;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc4;" u2="v" k="82" />
<hkern u1="&#xc4;" u2="q" k="8" />
<hkern u1="&#xc4;" u2="b" k="20" />
<hkern u1="&#xc4;" u2="\" k="143" />
<hkern u1="&#xc4;" u2="V" k="78" />
<hkern u1="&#xc4;" u2="Q" k="20" />
<hkern u1="&#xc4;" u2="J" k="-51" />
<hkern u1="&#xc4;" u2="E" k="10" />
<hkern u1="&#xc4;" u2="&#x3f;" k="51" />
<hkern u1="&#xc4;" u2="&#x2a;" k="143" />
<hkern u1="&#xc5;" u2="&#x2122;" k="123" />
<hkern u1="&#xc5;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc5;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc5;" u2="v" k="82" />
<hkern u1="&#xc5;" u2="q" k="8" />
<hkern u1="&#xc5;" u2="b" k="20" />
<hkern u1="&#xc5;" u2="\" k="143" />
<hkern u1="&#xc5;" u2="V" k="78" />
<hkern u1="&#xc5;" u2="Q" k="20" />
<hkern u1="&#xc5;" u2="J" k="-51" />
<hkern u1="&#xc5;" u2="E" k="10" />
<hkern u1="&#xc5;" u2="&#x3f;" k="51" />
<hkern u1="&#xc5;" u2="&#x2a;" k="143" />
<hkern u1="&#xc6;" u2="V" k="-10" />
<hkern u1="&#xc6;" u2="Q" k="10" />
<hkern u1="&#xc6;" u2="J" k="-20" />
<hkern u1="&#xc6;" u2="B" k="10" />
<hkern u1="&#xc7;" u2="q" k="20" />
<hkern u1="&#xc7;" u2="X" k="-20" />
<hkern u1="&#xc7;" u2="V" k="-31" />
<hkern u1="&#xc7;" u2="J" k="-51" />
<hkern u1="&#xc7;" u2="&#x29;" k="-72" />
<hkern u1="&#xc8;" u2="V" k="-10" />
<hkern u1="&#xc8;" u2="Q" k="10" />
<hkern u1="&#xc8;" u2="J" k="-20" />
<hkern u1="&#xc8;" u2="B" k="10" />
<hkern u1="&#xc9;" u2="V" k="-10" />
<hkern u1="&#xc9;" u2="Q" k="10" />
<hkern u1="&#xc9;" u2="J" k="-20" />
<hkern u1="&#xc9;" u2="B" k="10" />
<hkern u1="&#xca;" u2="V" k="-10" />
<hkern u1="&#xca;" u2="Q" k="10" />
<hkern u1="&#xca;" u2="J" k="-20" />
<hkern u1="&#xca;" u2="B" k="10" />
<hkern u1="&#xcb;" u2="V" k="-10" />
<hkern u1="&#xcb;" u2="Q" k="10" />
<hkern u1="&#xcb;" u2="J" k="-20" />
<hkern u1="&#xcb;" u2="B" k="10" />
<hkern u1="&#xd0;" u2="&#x2122;" k="59" />
<hkern u1="&#xd0;" u2="v" k="-31" />
<hkern u1="&#xd0;" u2="X" k="41" />
<hkern u1="&#xd2;" u2="X" k="41" />
<hkern u1="&#xd2;" u2="V" k="31" />
<hkern u1="&#xd3;" u2="X" k="41" />
<hkern u1="&#xd3;" u2="V" k="31" />
<hkern u1="&#xd4;" u2="X" k="41" />
<hkern u1="&#xd4;" u2="V" k="31" />
<hkern u1="&#xd5;" u2="X" k="41" />
<hkern u1="&#xd5;" u2="V" k="31" />
<hkern u1="&#xd6;" u2="X" k="41" />
<hkern u1="&#xd6;" u2="V" k="31" />
<hkern u1="&#xd8;" u2="X" k="41" />
<hkern u1="&#xd8;" u2="V" k="31" />
<hkern u1="&#xdd;" u2="&#xef;" k="-31" />
<hkern u1="&#xdd;" u2="&#xee;" k="-39" />
<hkern u1="&#xdd;" u2="&#xbb;" k="20" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-59" />
<hkern u1="&#xdd;" u2="x" k="20" />
<hkern u1="&#xdd;" u2="q" k="102" />
<hkern u1="&#xdd;" u2="p" k="78" />
<hkern u1="&#xdd;" u2="h" k="31" />
<hkern u1="&#xdd;" u2="b" k="31" />
<hkern u1="&#xdd;" u2="]" k="-61" />
<hkern u1="&#xdd;" u2="\" k="-98" />
<hkern u1="&#xdd;" u2="V" k="-20" />
<hkern u1="&#xdd;" u2="Q" k="37" />
<hkern u1="&#xdd;" u2="M" k="51" />
<hkern u1="&#xdd;" u2="J" k="92" />
<hkern u1="&#xdd;" u2="E" k="20" />
<hkern u1="&#xdd;" u2="&#x40;" k="41" />
<hkern u1="&#xdd;" u2="&#x2f;" k="160" />
<hkern u1="&#xdd;" u2="&#x2a;" k="-49" />
<hkern u1="&#xdd;" u2="&#x29;" k="-59" />
<hkern u1="&#xdd;" u2="&#x26;" k="72" />
<hkern u1="&#xdf;" u2="&#xff;" k="20" />
<hkern u1="&#xdf;" u2="&#xfd;" k="20" />
<hkern u1="&#xdf;" u2="y" k="20" />
<hkern u1="&#xdf;" u2="x" k="20" />
<hkern u1="&#xdf;" u2="v" k="20" />
<hkern u1="&#xe0;" u2="&#x2122;" k="82" />
<hkern u1="&#xe0;" u2="v" k="31" />
<hkern u1="&#xe0;" u2="\" k="61" />
<hkern u1="&#xe0;" u2="&#x2a;" k="51" />
<hkern u1="&#xe1;" u2="&#x2122;" k="82" />
<hkern u1="&#xe1;" u2="v" k="31" />
<hkern u1="&#xe1;" u2="\" k="61" />
<hkern u1="&#xe1;" u2="&#x2a;" k="51" />
<hkern u1="&#xe2;" u2="&#x2122;" k="82" />
<hkern u1="&#xe2;" u2="v" k="31" />
<hkern u1="&#xe2;" u2="\" k="61" />
<hkern u1="&#xe2;" u2="&#x2a;" k="51" />
<hkern u1="&#xe3;" u2="&#x2122;" k="82" />
<hkern u1="&#xe3;" u2="v" k="31" />
<hkern u1="&#xe3;" u2="\" k="61" />
<hkern u1="&#xe3;" u2="&#x2a;" k="51" />
<hkern u1="&#xe4;" u2="&#x2122;" k="82" />
<hkern u1="&#xe4;" u2="v" k="31" />
<hkern u1="&#xe4;" u2="\" k="61" />
<hkern u1="&#xe4;" u2="&#x2a;" k="51" />
<hkern u1="&#xe5;" u2="&#x2122;" k="82" />
<hkern u1="&#xe5;" u2="v" k="31" />
<hkern u1="&#xe5;" u2="\" k="61" />
<hkern u1="&#xe5;" u2="&#x2a;" k="51" />
<hkern u1="&#xe6;" u2="&#x2122;" k="61" />
<hkern u1="&#xe6;" u2="]" k="-20" />
<hkern u1="&#xe6;" u2="\" k="82" />
<hkern u1="&#xe7;" u2="v" k="-37" />
<hkern u1="&#xe7;" u2="\" k="20" />
<hkern u1="&#xe8;" u2="&#x2122;" k="61" />
<hkern u1="&#xe8;" u2="]" k="-20" />
<hkern u1="&#xe8;" u2="\" k="82" />
<hkern u1="&#xe9;" u2="&#x2122;" k="61" />
<hkern u1="&#xe9;" u2="]" k="-20" />
<hkern u1="&#xe9;" u2="\" k="82" />
<hkern u1="&#xea;" u2="&#x2122;" k="61" />
<hkern u1="&#xea;" u2="]" k="-20" />
<hkern u1="&#xea;" u2="\" k="82" />
<hkern u1="&#xeb;" u2="&#x2122;" k="61" />
<hkern u1="&#xeb;" u2="]" k="-20" />
<hkern u1="&#xeb;" u2="\" k="82" />
<hkern u1="&#xec;" u2="&#x2f;" k="20" />
<hkern u1="&#xec;" u2="&#x2a;" k="-10" />
<hkern u1="&#xed;" u2="&#x2f;" k="20" />
<hkern u1="&#xed;" u2="&#x2a;" k="-10" />
<hkern u1="&#xee;" u2="&#x2f;" k="20" />
<hkern u1="&#xee;" u2="&#x2a;" k="-10" />
<hkern u1="&#xef;" u2="&#x2f;" k="20" />
<hkern u1="&#xef;" u2="&#x2a;" k="-10" />
<hkern u1="&#xf1;" u2="&#x2122;" k="113" />
<hkern u1="&#xf1;" u2="v" k="31" />
<hkern u1="&#xf1;" u2="]" k="-20" />
<hkern u1="&#xf1;" u2="\" k="70" />
<hkern u1="&#xf1;" u2="&#x2a;" k="82" />
<hkern u1="&#xf2;" u2="&#x2122;" k="92" />
<hkern u1="&#xf2;" u2="x" k="6" />
<hkern u1="&#xf2;" u2="v" k="6" />
<hkern u1="&#xf2;" u2="\" k="90" />
<hkern u1="&#xf2;" u2="&#x2a;" k="72" />
<hkern u1="&#xf3;" u2="&#x2122;" k="92" />
<hkern u1="&#xf3;" u2="x" k="6" />
<hkern u1="&#xf3;" u2="v" k="6" />
<hkern u1="&#xf3;" u2="\" k="90" />
<hkern u1="&#xf3;" u2="&#x2a;" k="72" />
<hkern u1="&#xf4;" u2="&#x2122;" k="92" />
<hkern u1="&#xf4;" u2="x" k="6" />
<hkern u1="&#xf4;" u2="v" k="6" />
<hkern u1="&#xf4;" u2="\" k="90" />
<hkern u1="&#xf4;" u2="&#x2a;" k="72" />
<hkern u1="&#xf5;" u2="&#x2122;" k="92" />
<hkern u1="&#xf5;" u2="x" k="6" />
<hkern u1="&#xf5;" u2="v" k="6" />
<hkern u1="&#xf5;" u2="\" k="90" />
<hkern u1="&#xf5;" u2="&#x2a;" k="72" />
<hkern u1="&#xf6;" u2="&#x2122;" k="92" />
<hkern u1="&#xf6;" u2="x" k="6" />
<hkern u1="&#xf6;" u2="v" k="6" />
<hkern u1="&#xf6;" u2="\" k="90" />
<hkern u1="&#xf6;" u2="&#x2a;" k="72" />
<hkern u1="&#xf8;" u2="&#x2122;" k="92" />
<hkern u1="&#xf8;" u2="x" k="6" />
<hkern u1="&#xf8;" u2="v" k="6" />
<hkern u1="&#xf8;" u2="\" k="90" />
<hkern u1="&#xf8;" u2="&#x2a;" k="72" />
<hkern u1="&#xf9;" u2="&#x2122;" k="37" />
<hkern u1="&#xf9;" u2="\" k="20" />
<hkern u1="&#xf9;" u2="&#x2a;" k="20" />
<hkern u1="&#xfa;" u2="&#x2122;" k="37" />
<hkern u1="&#xfa;" u2="\" k="20" />
<hkern u1="&#xfa;" u2="&#x2a;" k="20" />
<hkern u1="&#xfb;" u2="&#x2122;" k="37" />
<hkern u1="&#xfb;" u2="\" k="20" />
<hkern u1="&#xfb;" u2="&#x2a;" k="20" />
<hkern u1="&#xfc;" u2="&#x2122;" k="37" />
<hkern u1="&#xfc;" u2="\" k="20" />
<hkern u1="&#xfc;" u2="&#x2a;" k="20" />
<hkern u1="&#xfd;" u2="&#x2122;" k="31" />
<hkern u1="&#xfd;" u2="v" k="-6" />
<hkern u1="&#xff;" u2="&#x2122;" k="31" />
<hkern u1="&#xff;" u2="v" k="-6" />
<hkern u1="&#x152;" u2="V" k="-10" />
<hkern u1="&#x152;" u2="Q" k="10" />
<hkern u1="&#x152;" u2="J" k="-20" />
<hkern u1="&#x152;" u2="B" k="10" />
<hkern u1="&#x153;" u2="&#x2122;" k="61" />
<hkern u1="&#x153;" u2="]" k="-20" />
<hkern u1="&#x153;" u2="\" k="82" />
<hkern u1="&#x178;" u2="&#xef;" k="-31" />
<hkern u1="&#x178;" u2="&#xee;" k="-39" />
<hkern u1="&#x178;" u2="&#xbb;" k="20" />
<hkern u1="&#x178;" u2="&#x7d;" k="-59" />
<hkern u1="&#x178;" u2="x" k="20" />
<hkern u1="&#x178;" u2="q" k="102" />
<hkern u1="&#x178;" u2="p" k="78" />
<hkern u1="&#x178;" u2="h" k="31" />
<hkern u1="&#x178;" u2="b" k="31" />
<hkern u1="&#x178;" u2="]" k="-61" />
<hkern u1="&#x178;" u2="\" k="-98" />
<hkern u1="&#x178;" u2="V" k="-20" />
<hkern u1="&#x178;" u2="Q" k="37" />
<hkern u1="&#x178;" u2="M" k="51" />
<hkern u1="&#x178;" u2="J" k="92" />
<hkern u1="&#x178;" u2="E" k="20" />
<hkern u1="&#x178;" u2="&#x40;" k="41" />
<hkern u1="&#x178;" u2="&#x2f;" k="160" />
<hkern u1="&#x178;" u2="&#x2a;" k="-49" />
<hkern u1="&#x178;" u2="&#x29;" k="-59" />
<hkern u1="&#x178;" u2="&#x26;" k="72" />
<hkern u1="&#x201a;" u2="v" k="113" />
<hkern u1="&#x201a;" u2="V" k="164" />
<hkern u1="&#x201a;" u2="J" k="-49" />
<hkern u1="&#x201e;" u2="v" k="113" />
<hkern u1="&#x201e;" u2="V" k="164" />
<hkern u1="&#x201e;" u2="J" k="-49" />
<hkern u1="&#x2026;" u2="v" k="113" />
<hkern u1="&#x2026;" u2="V" k="164" />
<hkern u1="&#x2026;" u2="J" k="-49" />
<hkern u1="&#x2039;" u2="&#xc5;" k="-31" />
<hkern u1="&#x2039;" u2="&#xc4;" k="-31" />
<hkern u1="&#x2039;" u2="&#xc3;" k="-31" />
<hkern u1="&#x2039;" u2="&#xc2;" k="-31" />
<hkern u1="&#x2039;" u2="&#xc1;" k="-31" />
<hkern u1="&#x2039;" u2="&#xc0;" k="-31" />
<hkern u1="&#x2039;" u2="A" k="-31" />
<hkern g1="uniFB01" u2="&#x2f;" k="20" />
<hkern g1="uniFB01" u2="&#x2a;" k="-10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="g" 	k="25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="49" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="76" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="143" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="94" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="AE" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,ccedilla" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="d" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="G" 	k="25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="121" />
<hkern g1="C,Ccedilla" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="25" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="-8" />
<hkern g1="C,Ccedilla" 	g2="W" 	k="-51" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="C,Ccedilla" 	g2="c,ccedilla" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="d" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="-31" />
<hkern g1="C,Ccedilla" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-31" />
<hkern g1="D,Eth" 	g2="w" 	k="-31" />
<hkern g1="D,Eth" 	g2="y,yacute,ydieresis" 	k="-31" />
<hkern g1="D,Eth" 	g2="T" 	k="61" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="47" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="25" />
<hkern g1="D,Eth" 	g2="AE" 	k="109" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="T" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="W" 	k="-10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Y,Yacute,Ydieresis" 	k="-10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Z" 	k="-20" />
<hkern g1="K" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="K" 	g2="g" 	k="53" />
<hkern g1="K" 	g2="w" 	k="41" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="K" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="K" 	g2="T" 	k="-41" />
<hkern g1="K" 	g2="W" 	k="-31" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="-31" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-41" />
<hkern g1="K" 	g2="c,ccedilla" 	k="25" />
<hkern g1="K" 	g2="d" 	k="25" />
<hkern g1="K" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="25" />
<hkern g1="K" 	g2="S" 	k="-16" />
<hkern g1="L" 	g2="g" 	k="41" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="47" />
<hkern g1="L" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="L" 	g2="T" 	k="154" />
<hkern g1="L" 	g2="W" 	k="61" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="113" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-51" />
<hkern g1="L" 	g2="AE" 	k="-25" />
<hkern g1="L" 	g2="Z" 	k="-31" />
<hkern g1="L" 	g2="S" 	k="-31" />
<hkern g1="L" 	g2="j" 	k="-51" />
<hkern g1="L" 	g2="s" 	k="-14" />
<hkern g1="L" 	g2="z" 	k="-29" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="37" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="92" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="t" 	k="-10" />
<hkern g1="R" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="R" 	g2="g" 	k="33" />
<hkern g1="R" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="R" 	g2="T" 	k="10" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="37" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="R" 	g2="Z" 	k="-31" />
<hkern g1="R" 	g2="s" 	k="12" />
<hkern g1="S" 	g2="AE" 	k="53" />
<hkern g1="T" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="147" />
<hkern g1="T" 	g2="g" 	k="154" />
<hkern g1="T" 	g2="w" 	k="113" />
<hkern g1="T" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="T" 	g2="T" 	k="-51" />
<hkern g1="T" 	g2="W" 	k="-31" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-31" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="143" />
<hkern g1="T" 	g2="AE" 	k="209" />
<hkern g1="T" 	g2="c,ccedilla" 	k="147" />
<hkern g1="T" 	g2="d" 	k="147" />
<hkern g1="T" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="147" />
<hkern g1="T" 	g2="t" 	k="41" />
<hkern g1="T" 	g2="G" 	k="61" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="215" />
<hkern g1="T" 	g2="s" 	k="127" />
<hkern g1="T" 	g2="z" 	k="102" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="154" />
<hkern g1="T" 	g2="f,uniFB01,uniFB02" 	k="74" />
<hkern g1="T" 	g2="l" 	k="37" />
<hkern g1="T" 	g2="n,ntilde" 	k="133" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="133" />
<hkern g1="T" 	g2="colon,semicolon" 	k="31" />
<hkern g1="T" 	g2="r" 	k="82" />
<hkern g1="T" 	g2="C,Ccedilla" 	k="61" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="78" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="t" 	k="-10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="W" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="W" 	g2="g" 	k="14" />
<hkern g1="W" 	g2="T" 	k="-31" />
<hkern g1="W" 	g2="W" 	k="-20" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="W" 	g2="AE" 	k="145" />
<hkern g1="W" 	g2="t" 	k="-35" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="143" />
<hkern g1="W" 	g2="s" 	k="23" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="W" 	g2="n,ntilde" 	k="12" />
<hkern g1="W" 	g2="colon,semicolon" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="94" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="219" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,ccedilla" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="174" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="n,ntilde" 	k="78" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="72" />
<hkern g1="Z" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="Z" 	g2="g" 	k="45" />
<hkern g1="Z" 	g2="w" 	k="25" />
<hkern g1="Z" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="Z" 	g2="W" 	k="-20" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="Z" 	g2="s" 	k="20" />
<hkern g1="Z" 	g2="z" 	k="-10" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="8" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-37" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="-37" />
<hkern g1="c,ccedilla" 	g2="t" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-10" />
<hkern g1="g" 	g2="j" 	k="-86" />
<hkern g1="j" 	g2="j" 	k="-51" />
<hkern g1="k" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="k" 	g2="g" 	k="20" />
<hkern g1="k" 	g2="w" 	k="-16" />
<hkern g1="k" 	g2="c,ccedilla" 	k="16" />
<hkern g1="k" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="16" />
<hkern g1="k" 	g2="t" 	k="-31" />
<hkern g1="l" 	g2="g" 	k="41" />
<hkern g1="l" 	g2="w" 	k="18" />
<hkern g1="l" 	g2="quoteright,quotedblright" 	k="86" />
<hkern g1="n,ntilde" 	g2="w" 	k="10" />
<hkern g1="r" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="r" 	g2="g" 	k="10" />
<hkern g1="r" 	g2="w" 	k="-29" />
<hkern g1="r" 	g2="c,ccedilla" 	k="16" />
<hkern g1="r" 	g2="d" 	k="16" />
<hkern g1="r" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="16" />
<hkern g1="r" 	g2="t" 	k="-31" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="r" 	g2="f,uniFB01,uniFB02" 	k="-23" />
<hkern g1="s" 	g2="g" 	k="12" />
<hkern g1="t" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="-8" />
<hkern g1="t" 	g2="g" 	k="4" />
<hkern g1="t" 	g2="c,ccedilla" 	k="-8" />
<hkern g1="t" 	g2="d" 	k="-8" />
<hkern g1="t" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="-8" />
<hkern g1="t" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-8" />
<hkern g1="w" 	g2="w" 	k="-20" />
<hkern g1="w" 	g2="y,yacute,ydieresis" 	k="-16" />
<hkern g1="w" 	g2="t" 	k="-37" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="8" />
<hkern g1="w" 	g2="f,uniFB01,uniFB02" 	k="-37" />
<hkern g1="w" 	g2="colon,semicolon" 	k="16" />
<hkern g1="y,yacute,ydieresis" 	g2="w" 	k="-16" />
<hkern g1="y,yacute,ydieresis" 	g2="t" 	k="-37" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="70" />
<hkern g1="y,yacute,ydieresis" 	g2="f,uniFB01,uniFB02" 	k="-20" />
<hkern g1="z" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="y,yacute,ydieresis" 	k="119" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="215" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W" 	k="143" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="174" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-98" />
<hkern g1="colon,semicolon" 	g2="j" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="123" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="367" />
<hkern g1="J" 	g2="AE" 	k="86" />
</font>
</defs></svg> 