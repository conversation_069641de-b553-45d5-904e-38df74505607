import { Component, OnInit, ViewChild, Inject } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Location } from '@angular/common';
import { AppraisalCtuService } from '../service/appraisal-ctu.service';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { DOCUMENT } from '@angular/platform-browser';

@Component({
  selector: 'app-appraisal-ctu',
  templateUrl: './appraisal-ctu.component.html',
  styleUrls: ['./appraisal-ctu.component.css'],
  providers: [AppraisalCtuService]
})
export class AppraisalCtuComponent implements OnInit {
  appraisalId: string;
  appraisal: any = {};
  assetsType: string[] = [];
  ctuDetailAsset: any;
  isTavolare: boolean;
  taskLockingUser: string;
  wizardCode: string;
  @ViewChild('assetAssociatedListModal') assetAssociatedListModal: ModalDirective;
  


  constructor(
    private appraisalCtuService: AppraisalCtuService,
    private activatedRoute: ActivatedRoute,
    private location: Location,
    private router: Router,
    @Inject(DOCUMENT) private document: any
  ) { }

  ngOnInit() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.appraisalId = params['appraisalId'];
      this.wizardCode = params['wizardCode'];
      if (params['taskLockingUser'] !== '-') {
        this.taskLockingUser = params['taskLockingUser'];
      }
      this.appraisalCtuService
        .getCtuDetail(this.appraisalId)
        .subscribe(res => {
           this.appraisal = res;
        });
    });
  }

  /**
   * @function
   * @name showListAssetAssociated
   * @description Apre la modale che mostra l'elenco degli asset associati ad un lotto
   * @params ctuDetailAsset che rappresenta la lista degli asset associati a quel lotto
   */
  showListAssetAssociated(ctuDetailAsset: any) {
    this.assetAssociatedListModal.show();
    this.ctuDetailAsset = ctuDetailAsset;
    this.setIsTavolare(ctuDetailAsset);
    this.checkAssetsType();
  }

  /**
   * @function
   * @name navigateBack
   * @description torna un passo indietro
   */
  navigateBack() {
    this.router.navigateByUrl(`generic-task/${this.appraisalId}/-/-`);
  }

  /**
   * @function
   * @name goToAssetPage
   * @description vai alla pagina dell asset
   * @params idAsset id dell asset corrispondente
   */
  goToAssetPage(idAsset: any) {
    this.document.location.href = `/UAM-EFA-PF/UAM/#/detail/false/${idAsset}`;
  }

  checkAssetsType() {
    if (this.ctuDetailAsset && this.ctuDetailAsset.length > 0) {
      this.ctuDetailAsset.forEach(asset => {
          if (!(this.assetsType.indexOf(asset.resItemType) > -1)) {
            this.assetsType.push(asset.resItemType);
          }
      });
    }
  }

  /**
   * @function
   * @name setTavolareOnModal
   * @description sets the view of the table inside the modal
   * @params ctuDetailAsset list that contains assets to be shown on modal
   */
  private setIsTavolare(ctuDetailAsset: any) {
    if (ctuDetailAsset && ctuDetailAsset.length > 0) {
      this.isTavolare = ctuDetailAsset[0].tavolare;
    } else {
      this.isTavolare = null;
    }
  }
}
