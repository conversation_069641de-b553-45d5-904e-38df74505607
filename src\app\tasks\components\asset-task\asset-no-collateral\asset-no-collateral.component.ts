import {
  Component,
  OnInit,
  Input,
  AfterViewChecked,
  Output,
  EventEmitter
} from '@angular/core';
import { AccordionAPFService } from '../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';

@Component({
  selector: 'app-asset-no-collateral',
  templateUrl: './asset-no-collateral.component.html',
  styleUrls: ['./asset-no-collateral.component.css']
})

export class AssetNoCollateralComponent implements OnInit, AfterViewChecked {
  @Input()
  positionId: string;
  @Input()
  assetsWithNoCollateral: any[];
  @Input()
  collateralList: any[];
  @Input()
  semaforo: string;
  jointCod: string[] = [];
  selectedCollateral: any[] = [null];
  @Output()
  saveEvent = new EventEmitter<any>();
  assignmentList: any[] = [];
  isSaveAllEnable: boolean = false;
  isSingleSaveEnable: boolean[] = [false];
  constructor(public _accordionAPFService: AccordionAPFService) {}

  ngOnInit() {
    for (const e of this.assetsWithNoCollateral){
      this.isSingleSaveEnable.push(false);
      this.jointCod.push("");
      this.selectedCollateral.push(null);
    }
  }

  ngAfterViewChecked() {
  }

  assign(asset:any, index: number){
    this.getSelectedCollateral(index);
    for(const a of this.assetsWithNoCollateral){
      if(a.objectCod === asset.objectCod){
        if(this.assignmentList.indexOf(a)!=-1){
          this.assignmentList.splice(this.assignmentList.indexOf(a),1);
        }
        a.jointCod      = this.selectedCollateral[index]==null?  null : this.selectedCollateral[index].progCollateral;
        a.collatTecForm = this.selectedCollateral[index]==null?  null : this.selectedCollateral[index].collateralTecForm;
        a.collatDesc    = this.selectedCollateral[index]==null?  null : this.selectedCollateral[index].collateralDesc;
        a.collatAmount  = this.selectedCollateral[index]==null?  null : this.selectedCollateral[index].collateralAmmount;
        a.deletionFlag  = this.selectedCollateral[index]==null?  null : this.selectedCollateral[index].deletionFlag;
        a.jointType     = this.selectedCollateral[index]==null?  null : this.selectedCollateral[index].garantType;

        if(a.jointCod != null){
          this.assignmentList.push(a);
          this.isSingleSaveEnable[index] = true;
        }
        else{
          this.isSingleSaveEnable = [];
          this.selectedCollateral = [];
          this.jointCod = [];
          this.isSaveAllEnable = false;
          for (const e of this.assetsWithNoCollateral){
            this.isSingleSaveEnable.push(false);
            this.selectedCollateral.push(null);
            this.jointCod.push(null);
          }
        }
        break;
      }
    }
    this.isSaveAllEnable = this.isSaveEnable();
  }

  isSaveEnable(){
    if(this.assignmentList.length>0){
      return true;
    }
    return false;
  }

  getSelectedCollateral(index: number){
    this.selectedCollateral[index] = null;
    for(const el of this.collateralList){
      if(el.progCollateral === this.jointCod[index]){
        this.selectedCollateral[index] = el;
        break;
      }
    }
  }

  saveSingleAsset(asset: any, index: number){
      this.saveEvent.emit([asset]);
      if(this.assignmentList.indexOf(asset)!=-1){
        this.assignmentList.splice(this.assignmentList.indexOf(asset),1);
      }
      this.isSingleSaveEnable = [];
      this.selectedCollateral = [];
      this.jointCod = [];
      this.isSaveAllEnable = false;
      for (const e of this.assetsWithNoCollateral){
        this.isSingleSaveEnable.push(false);
        this.selectedCollateral.push(null);
        this.jointCod.push(null);
      }
  }

  saveAllAsset(){
    this.saveEvent.emit(this.assignmentList);
    this.isSingleSaveEnable = [];
    this.selectedCollateral = [];
    this.jointCod = [];
    this.isSaveAllEnable = false;
    for (const e of this.assetsWithNoCollateral){
      this.isSingleSaveEnable.push(false);
      this.selectedCollateral.push(null);
      this.jointCod.push(null);
    }
  }

}
