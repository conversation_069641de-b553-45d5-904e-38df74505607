<accordion class="panel-group" id="accordion">
  <div *ngFor="let accord of accordions; let i = index">
    <div class="col-sm-12">
      <div class="custom-radio" style="width: 50px ; position: absolute; margin-top: 15px">
        <input [(ngModel)]="guaranteeId" value="{{accord.jointCod}}" type="radio" name="radioGuarantee" id="{{accord.jointCod}}" class="radio"
          (click)="onSelectionChange( i )">
        <label for="{{accord.jointCod}}" style="width: auto"></label>
      </div>
      <accordion-group  #group class="panel" style="width: 95% !important ; float: right">
        <div accordion-heading class="acc-note-headline" role="tab">
          <h4 class="panel-title">
            <a role="button">
              <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
              <span>{{accord.jointCod +' - '+ accord.description}}</span>
            </a>
          </h4>
        </div>
        <div class="panel-collapse collapse in" role="tabpanel">
          <div class="panel-body">
            <div class="row">
              <div class="col-sm-12">
                <table class="uc-table">
                  <thead>
                    <tr>
                      <th scope="col" class="col-sm-2" style="text-align : center">{{'UBZ.SITE_CONTENT.100100' | translate }}</th>
                      <th scope="col" class="col-sm-4" style="text-align : center">{{'UBZ.SITE_CONTENT.111100' | translate }}</th>
                      <th scope="col" class="col-sm-2" style="text-align : center">{{'UBZ.SITE_CONTENT.101000' | translate }}</th>
                      <th scope="col" class="col-sm-2" style="text-align : center">{{'UBZ.SITE_CONTENT.101100111' | translate }}</th>
                      <th scope="col" class="col-sm-2" style="text-align : center">{{'UBZ.SITE_CONTENT.10000000010' | translate }}</th>
                      <th scope="col" class="col-sm-1" style="text-align : center">{{'UBZ.SITE_CONTENT.1110101' | translate }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let row of accord.assets; let i2 = index" >
                      <td attr.data-label = "ID Asset" style="text-align : center">{{row.assetId}}</td>
                      <td attr.data-label = "ID Asset" style="text-align : center">{{row.assetDescription}}</td>
                      <td attr.data-label = "ID Asset" style="text-align : center">{{row.province}}</td>
                      <td attr.data-label = "Perizia" style="text-align : center">{{row.appraisalId}}</td>
                      <td attr.data-label = "Data" style="text-align : center">{{row.lastChange | date:'dd/MM/yyyy'}}</td>
                      <button type="button" class="btn btn-empty pull-right" data-toggle="modal" data-target="#details" (click)="openAssetDetails(row, assetModal)">
                        <i class="icon-detail_page"></i>
                        {{'UBZ.SITE_CONTENT.1000000' | translate }}
                      </button>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </accordion-group>
     </div>
  </div>
</accordion>
  
<app-navigation-footer 
  [showPrevious]="true"
  [showCancelButton]="true" 
  [saveIsEnable]="guaranteeId" 
  (cancelButtonClick)="guaranteeTransfer.cancelRequest()"
  (previousButtonClick)="openModal()"
  (saveButtonClick)="save()">
</app-navigation-footer>

<div *ngIf="modalIsOpen" class="modal fade" id="nuovo-asset" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #assetModal="bs-modal" (onHidden)="closeAssetModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.10000000011' | translate }}</h2>
        <button type="button" role="button" class="close pull-right" aria-label="Close" (click)="closeAssetModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <app-application-form 
          [idCode]="asset.assetId" 
          [page]="'INFO_ASSET_UAM_RIC'" 
          [drivers]="{'DD_AST_TIPO': asset.assetType}" 
          [setReadOnly]=true 
          [positionId]="asset.appraisalId">
        </app-application-form>
      </div>
    </div>
  </div>
</div>

<div *ngIf="previousModalIsOpen" class="modal fade" id="nuovo-asset" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #modal="bs-modal" (onHidden)="closeModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.1100010' | translate }}</h2>
        <button type="button" role="button" class="close pull-right" aria-label="Close" (click)="closeModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <p>{{'UBZ.SITE_CONTENT.10000000100' | translate }}.</p>
          <p>{{'UBZ.SITE_CONTENT.1111110110' | translate }}?</p>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" (click)="confirmUndo()">
          {{'UBZ.SITE_CONTENT.110101000' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>