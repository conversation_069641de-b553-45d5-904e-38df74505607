<h3>{{ 'UBZ.SITE_CONTENT.1101011001' | translate }}</h3>

<ng-container *ngIf="!ndgAppraisals.length">
  <h4>Nessuna perizia presente</h4>
</ng-container>
<accordion class="panel-group" id="accordion">
  <div class="row step-navigation">
    <div class="col-sm-12">
      <div class="custom-checkbox">
        <input class="checkbox" id="selectall" type="checkbox" (change)="selectAll()" [checked]="allSelected">
        <label for="selectall">{{'UBZ.SITE_CONTENT.111010' | translate }}</label>
      </div>
    </div>
  </div>
  <div class="col-sm-12" *ngFor="let appraisal of ndgAppraisals; let i=index">
    <div class="custom-checkbox" style="width: 50px ; position: absolute; margin-top: 15px">
      <input id="{{appraisal.appraisalId}}" type="checkbox" [(ngModel)]="appraisalsCheckArray[i]" (ngModelChange)="setSaveIsEnable()"
        name="{{appraisal.appraisalId}}" class="checkbox">
      <label for="{{appraisal.appraisalId}}"></label>
    </div>
    <accordion-group #group class="panel" style="width: 95% !important ; float: right">
      <div accordion-heading class="acc-note-headline" role="tab">
        <h4 class="panel-title">
          <a role="button">
            <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
            <span>{{ 'UBZ.SITE_CONTENT.10010001' | translate }}: {{appraisal.appraisalId}}</span>
          </a>
        </h4>
      </div>
      <div class="panel-collapse collapse in" role="tabpanel">
        <div class="panel-body">
          <div class="row">
            <div class="col-sm-12">
              <table class="uc-table">
                <thead>
                  <tr>
                    <th scope="col" class="col-sm-2" style="text-align : center">{{ 'UBZ.SITE_CONTENT.10010100' | translate }}</th>
                    <th scope="col" class="col-sm-2" style="text-align : center">{{ 'UBZ.SITE_CONTENT.10001110011' | translate }}</th>
                    <th scope="col" class="col-sm-2" style="text-align : center">{{ 'UBZ.SITE_CONTENT.100100' | translate }}</th>
                    <th scope="col" class="col-sm-2" style="text-align : center">{{ 'UBZ.SITE_CONTENT.111100' | translate }}</th>
                    <th scope="col" class="col-sm-2" style="text-align : center">{{ 'UBZ.SITE_CONTENT.10010111010' | translate }}</th>
                    <th scope="col" class="col-sm-2" style="text-align : center">{{ 'UBZ.SITE_CONTENT.101000' | translate }}</th>
                  </tr>
                </thead>
                <tbody>
                  <ng-container *ngFor="let guarantee of appraisal.collaterals">
                    <tr *ngFor="let asset of guarantee.assets">
                      <td style="text-align : center">{{guarantee.jointCod}}</td>
                      <td style="text-align : center">{{guarantee.collatTecForm}}</td>
                      <td style="text-align : center">{{asset.assetId}}</td>
                      <td style="text-align : center">{{asset.assetDescription}}</td>
                      <td style="text-align : center">{{asset.assetType}}</td>
                      <td style="text-align : center">{{asset.province}}</td>
                    </tr>
                  </ng-container>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </accordion-group>
  </div>
</accordion>

<!-- FIXME - CENTRALIZZARE FOOTER NEL COMPONENTE PADRE CONTENITORE -->
<app-navigation-footer (cancelButtonClick)="cancelRequest()" (saveButtonClick)="sendAppraisalList()" [saveIsEnable]="saveIsEnable">
</app-navigation-footer>