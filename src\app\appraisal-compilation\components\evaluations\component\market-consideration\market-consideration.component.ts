import {
  Component,
  OnInit,
  Input,
  ViewChild
} from '@angular/core';
import { NgForm } from '@angular/forms';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';

@Component({
  selector: 'app-market-consideration',
  templateUrl: './market-consideration.component.html',
  styleUrls: ['./market-consideration.component.css']
})
export class MarketConsiderationComponent implements OnInit {
  @ViewChild('f') form: NgForm;
  @Input() marketConsideration: string;

  constructor(public accordionAPFService: AccordionAPFService) {}

  // L'evento change del form viene intercettato dal componente padre EvaluationsComponent e
  // viene scatenato il metodo del padre checkSaveEnable() per validare lo step del wizard
  ngOnInit() {}

  public isValid(): boolean {
    return this.form && this.form.valid;
  }

  // Invocato dal componente padre, restituisce il dato di consideration per aggiungerlo all'oggetto da salvare
  getData() {
    return {markConsideration: this.marketConsideration};
  }
}