import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { Observable } from 'rxjs/Observable';

@Component({
  selector: 'app-task-button',
  templateUrl: './task-button.component.html',
  styleUrls: ['./task-button.component.css']
})
export class TaskButtonComponent implements OnInit {
  @Input() eventCod: string;
  @Input() buttonLabel: string;
  @Input() customButton: boolean;
  @Input() before: () => Observable<boolean>;
  @Input() disabled: boolean;

  @Output() buttonClick = new EventEmitter<string>();
  @Output() success = new EventEmitter();
  @Output() error = new EventEmitter();

  _visible = true;

  constructor() {}

  ngOnInit() {}

  @Input()
  set visible(visible: boolean) {
    this._visible = visible;
  }

  get visible(): boolean {
    return this._visible;
  }
}
