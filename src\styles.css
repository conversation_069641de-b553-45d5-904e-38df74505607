@charset "UTF-8";
@font-face {
  font-family: 'unicreditbold_italic';
  src: url('./assets/fonts/unicredit-bold_italic-webfont.woff2') format('woff2'),
    url('./assets/fonts/unicredit-bold_italic-webfont.woff') format('woff'),
    url('./assets/fonts/unicredit-bold_italic-webfont.svg#unicreditbold_italic')
      format('svg');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'unicreditbold';
  src: url('./assets/fonts/unicredit-bold-webfont.woff2') format('woff2'),
    url('./assets/fonts/unicredit-bold-webfont.woff') format('woff'),
    url('./assets/fonts/unicredit-bold-webfont.svg#unicreditbold') format('svg');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'unicreditheavy_italic';
  src: url('./assets/fonts/unicredit-heavy_italic-webfont.woff2')
      format('woff2'),
    url('./assets/fonts/unicredit-heavy_italic-webfont.woff') format('woff'),
    url('./assets/fonts/unicredit-heavy_italic-webfont.svg#unicreditheavy_italic')
      format('svg');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'unicreditheavy';
  src: url('./assets/fonts/unicredit-heavy-webfont.woff2') format('woff2'),
    url('./assets/fonts/unicredit-heavy-webfont.woff') format('woff'),
    url('./assets/fonts/unicredit-heavy-webfont.svg#unicreditheavy')
      format('svg');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'unicredititalic';
  src: url('./assets/fonts/unicredit-italic-webfont.woff2') format('woff2'),
    url('./assets/fonts/unicredit-italic-webfont.woff') format('woff'),
    url('./assets/fonts/unicredit-italic-webfont.svg#unicredititalic')
      format('svg');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'unicreditlight_italic';
  src: url('./assets/fonts/unicredit-light_italic-webfont.woff2')
      format('woff2'),
    url('./assets/fonts/unicredit-light_italic-webfont.woff') format('woff'),
    url('./assets/fonts/unicredit-light_italic-webfont.svg#unicreditlight_italic')
      format('svg');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'unicreditlight';
  src: url('./assets/fonts/unicredit-light-webfont.woff2') format('woff2'),
    url('./assets/fonts/unicredit-light-webfont.woff') format('woff'),
    url('./assets/fonts/unicredit-light-webfont.svg#unicreditlight')
      format('svg');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'unicreditmedium_italic';
  src: url('./assets/fonts/unicredit-medium_italic-webfont.woff2')
      format('woff2'),
    url('./assets/fonts/unicredit-medium_italic-webfont.woff') format('woff'),
    url('./assets/fonts/unicredit-medium_italic-webfont.svg#unicreditmedium_italic')
      format('svg');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'unicreditmedium';
  src: url('./assets/fonts/unicredit-medium-webfont.woff2') format('woff2'),
    url('./assets/fonts/unicredit-medium-webfont.woff') format('woff'),
    url('./assets/fonts/unicredit-medium-webfont.svg#unicreditmedium')
      format('svg');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'unicreditregular';
  src: url('./assets/fonts/unicredit-regular-webfont.woff2') format('woff2'),
    url('./assets/fonts/unicredit-regular-webfont.woff') format('woff'),
    url('./assets/fonts/unicredit-regular-webfont.svg#unicreditregular')
      format('svg');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'unicredit-icons';
  src: url('./assets/fonts/unicredit-icons.eot?60434660');
  src: url('./assets/fonts/unicredit-icons.eot?60434660#iefix')
      format('embedded-opentype'),
    url('./assets/fonts/unicredit-icons.woff2?60434660') format('woff2'),
    url('./assets/fonts/unicredit-icons.woff?60434660') format('woff'),
    url('./assets/fonts/unicredit-icons.ttf?60434660') format('truetype'),
    url('./assets/fonts/unicredit-icons.svg?60434660#sintra-test') format('svg');
  font-weight: normal;
  font-style: normal;
}
[class^='icon-']:before,
[class*=' icon-']:before {
  font-family: 'unicredit-icons';
  font-style: normal;
  font-weight: normal;
  speak: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-folder:before {
  content: '\e800';
}

/* '' */
.icon-logout:before {
  content: '\e801';
}

/* '' */
.icon-nuova_simulazione:before {
  content: '\e802';
}

/* '' */
.icon-report:before {
  content: '\e803';
}

/* '' */
.icon-richiesta_perizia:before {
  content: '\e804';
}

/* '' */
.icon-admin_panel:before {
  content: '\e805';
}

/* '' */
.icon-arrow_down:before {
  content: '\e806';
}

/* '' */
.icon-arrow_up:before {
  content: '\e807';
}

/* '' */
.icon-asset:before {
  content: '\e808';
}

/* '' */
.icon-assign_to:before {
  content: '\e809';
}

/* '' */
.icon-dashboard:before {
  content: '\e80a';
}

/* '' */
.icon-add:before {
  content: '\e80b';
}

/* '' */
.icon-close:before {
  content: '\e80c';
}

/* '' */
.icon-check:before {
  content: '\e80d';
}

/* '' */
.icon-clock:before {
  content: '\e80e';
}

/* '' */
.icon-info:before {
  content: '\e80f';
}

/* '' */
.icon-invalidate:before {
  content: '\e810';
}

/* '' */
.icon-note:before {
  content: '\e811';
}

/* '' */
.icon-plus_open_lightbox:before {
  content: '\e812';
}

/* '' */
.icon-print_checklist:before {
  content: '\e813';
}

/* '' */
.icon-remove_to:before {
  content: '\e814';
}

/* '' */
.icon-upload:before {
  content: '\e815';
}

/* '' */
.icon-menu:before {
  content: '\e816';
}

/* '' */
.icon-printer:before {
  content: '\e817';
}

/* '' */
.icon-search:before {
  content: '\e818';
}

/* '' */
.icon-calendar:before {
  content: '\e819';
}

/* '' */
.icon-settings:before {
  content: '\e81a';
}

/* '' */
.icon-plus:before {
  content: '\e81b';
}

/* '' */
.icon-clock-1:before {
  content: '\e81c';
}

/* '' */
.icon-icon-arrow-down:before {
  content: '\e81d';
}

/* '' */
.icon-icon-arrow-right:before {
  content: '\e81e';
}

/* '' */
.icon-download:before {
  content: '\e825';
}

/* '' */
.icon-sort:before {
  content: '\f0dc';
}

/* '' */
.icon-angle-double-right:before {
  content: '\f101';
}

/* '' */
.icon-angle-left:before {
  content: '\f104';
}

/* '' */
.icon-angle-right:before {
  content: '\f105';
}

/* '' */
.icon-left:before {
  content: '\f177';
}

/* '' */
.icon-database:before {
  content: '\f1c0';
}

/* '' */
.icon-drag:before {
  content: '\e826';
}

/* '' */
.icon-detail_page:before {
  content: '\e827';
}

/* '' */
.icon-placeholder_note:before {
  content: '\e828';
}

/* '' */
.icon-placeholder_storico:before {
  content: '\e829';
}

/* '' */
.icon-surroga:before {
  content: '\e82a';
}

/* '' */
.icon-template_perizia:before {
  content: '\e82b';
}

/* '' */
.icon-close_message:before {
  content: '\e82c';
}

/* '' */
.icon-error:before {
  content: '\e82d';
}

/* '' */
.icon-info_message:before {
  content: '\e82e';
}

/* '' */
.icon-success:before {
  content: '\e82f';
}

/* '' */
.icon-warning:before {
  content: '\e830';
}

/* '' */
.icon-sort:before {
  content: '\f0dc';
}

/* '' */
.icon-angle-double-right:before {
  content: '\f101';
}

/* '' */
.icon-angle-left:before {
  content: '\f104';
}

/* '' */
.icon-angle-right:before {
  content: '\f105';
}

/* '' */
.icon-left:before {
  content: '\f177';
}

/* '' */
.icon-database:before {
  content: '\f1c0';
}

/* '' */
.icon-appuntamento:before {
  content: '\e831';
}

/* '' */
.icon-incarico:before {
  content: '\e832';
}

/* '' */
.icon-sopralluogo:before {
  content: '\e833';
}

/* '' */
.icon-round-done-button:before {
  content: '\e834';
}

/* '' */
.icon-algoritmi:before {
  content: '\e835';
}

/* '' */
.icon-database_asset:before {
  content: '\e836';
}

/* '' */
.icon-filter:before {
  content: '\e837';
}

/* '' */
.icon-monitoraggio:before {
  content: '\e838';
}

/* '' */
.icon-ricerca-servicing:before {
  content: '\e838';
}

/* '' */
.icon-rendiconto_annuale:before {
  content: '\e839';
}

/* '' */
.icon-report_errori:before {
  content: '\e83a';
}

/* '' */
.icon-sorveglianza:before {
  content: '\e83b';
}

/* '' */
.icon-xls_export:before {
  content: '\e83c';
}

/* '' */
.icon-switch:before {
  content: '\e83e';
}

/* '' */
.icon-lightblue {
  color: #00afd0;
}

.sort-top {
  background: transparent;
  /* Old browsers */
  background: -moz-linear-gradient(
    top,
    #00afd0 1%,
    #00afd0 50%,
    #94e7f2 50%,
    #94e7f2 100%
  );
  /* FF3.6-15 */
  background: -webkit-linear-gradient(
    top,
    #00afd0 1%,
    #00afd0 50%,
    #94e7f2 50%,
    #94e7f2 100%
  );
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(
    to bottom,
    #00afd0 1%,
    #00afd0 50%,
    #94e7f2 50%,
    #94e7f2 100%
  );
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient(
      startColorstr='#00afd0',
      endColorstr='#94e7f2',
      GradientType=0
    );
  /* IE6-9 */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.sort-bottom {
  background: transparent;
  /* Old browsers */
  background: -moz-linear-gradient(
    top,
    #94e7f2 0%,
    #94e7f2 50%,
    #00afd0 50%,
    #00afd0 99%
  );
  /* FF3.6-15 */
  background: -webkit-linear-gradient(
    top,
    #94e7f2 0%,
    #94e7f2 50%,
    #00afd0 50%,
    #00afd0 99%
  );
  /* Chrome10-25,Safari5.1-6 */
  background: linear-gradient(
    to bottom,
    #94e7f2 0%,
    #94e7f2 50%,
    #00afd0 50%,
    #00afd0 99%
  );
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient(
      startColorstr='#94e7f2',
      endColorstr='#00afd0',
      GradientType=0
    );
  /* IE6-9 */
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .sort-top,
  .sort-bottom {
    filter: none;
    background: transparent;
  }
}
html,
body {
  font-family: 'unicreditregular';
  color: #333333;
  font-size: 16px;
  padding-bottom: 0px;
  min-height: 100vh;
}
body {
  padding-top: 80px;
}
h1,
h2,
h3,
h4,
h5 {
  color: #333333;
}
a {
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
  transition: all 0.2s ease;
}
h1 i {
  color: #bebebe;
}
h2 {
  margin-top: 0;
  font-size: 26px;
}
h3 {
  padding-bottom: 20px;
  padding-top: 10px;
  text-transform: uppercase;
}
h3 .alert-warning {
  font-size: 14px;
  text-transform: none;
  display: inline-block;
  float: right;
  margin-bottom: 0;
  padding: 8px 12px;
}
h4.table-title {
  padding: 5px 20px;
  text-transform: uppercase;
}
h4.section-heading {
  text-transform: uppercase;
  padding-top: 30px;
}
.collapsing {
  width: 100%;
}
b,
strong {
  font-family: 'unicreditbold';
}
.inactive {
  pointer-events: none;
  cursor: default;
}
a:active,
a:focus {
  outline: none;
  ie-dummy: expression(this.hideFocus=true);
}
button:focus {
  outline: 0 !important;
}
@media (max-width: 992px) {
  .text-center-sm {
    text-align: center;
  }
}
.eq-height {
  display: flex;
}
@media (max-width: 992px) {
  .eq-height {
    display: inherit;
  }
}
.top-bottom-spacing {
  padding-top: 15px;
  padding-bottom: 15px;
  overflow: hidden;
}
.badge.circular {
  width: 35px;
  height: 35px;
  line-height: 30px;
  border-radius: 50%;
  padding: 0;
  margin: 0;
  background: transparent;
  color: #333;
  border: 2px solid #333;
}
.badge.search-tag {
  background: #e5eff2;
  color: #00afd0;
  font-weight: normal;
  font-size: 14px;
  padding: 5px 12px;
  font-family: 'unicreditmedium';
  border-radius: 60px;
  margin-right: 5px;
}
.sep {
  min-height: 5px;
  width: 100%;
  background: #ccc;
  display: inline-block;
  position: absolute;
  top: 15px;
  z-index: 1;
  left: 38px;
}
.navbar {
  background: #e2001a;
  min-height: 80px;
  max-height: 80px;
  color: #fff;
  border-radius: 0px;
  border: 0;
  margin-bottom: 0;
}
.navbar #menu-toggle {
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  display: block;
  float: left;
  background: #262626;
  text-decoration: none;
}
.navbar #menu-toggle i {
  color: #fff;
  font-size: 26px;
}
.navbar .navbar-nav .user {
  text-align: left;
  padding: 0;
  height: 80px;
  background: #e2001a;
  width: 242px;
  font-size: 18px;
  text-transform: none;
}
@media (max-width: 992px) {
  .navbar .navbar-nav .user {
    width: 80px;
    position: static;
  }
}
.navbar .navbar-nav .user .userInfo {
  float: left;
  width: calc(100% - 48px);
  padding: 0px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: right;
}
@media (min-width: 992px) {
  .navbar .navbar-nav .user .userInfo {
    height: 80px;
  }
}
@media (max-width: 992px) {
  .navbar .navbar-nav .user .userInfo {
    width: 100%;
    z-index: 9999;
    position: static;
    left: 0;
    right: 0;
    top: 80px;
  }
}
.navbar .navbar-nav .user .userInfo .uc-dropdown {
  min-width: 260px;
  box-shadow: none;
  border: 0px;
  background: #e2001a;
  padding: 0px;
  margin-top: 0;
  top: 80px;
  border-radius: 0;
}
@media (max-width: 992px) {
  .navbar .navbar-nav .user .userInfo .uc-dropdown {
    z-index: 9999;
    position: absolute;
    right: 0;
  }
}
.navbar .navbar-nav .user .userInfo .uc-dropdown .menu-info {
  margin: 5px 0px 8px 0px;
  font-size: 15px;
}
.navbar .navbar-nav .user .userInfo .uc-dropdown .menu-info span {
  display: block;
  width: 100%;
  padding: 1px 20px;
}
.navbar .navbar-nav .user .userInfo .uc-dropdown .menu-info span strong {
  min-width: 60px;
  display: inline-block;
  text-align: left;
}
.navbar .navbar-nav .user .userInfo .uc-dropdown li a {
  color: #fff;
  font-size: 16px;
  text-transform: uppercase;
  padding: 15px 20px 15px 50px;
  position: relative;
}
.navbar .navbar-nav .user .userInfo .uc-dropdown li a i {
  position: absolute;
  left: 20px;
  top: 12px;
  font-size: 20px;
}
.navbar .navbar-nav .user .userInfo .uc-dropdown li a:hover {
  background: #aa1c0d;
}
.navbar .navbar-nav .user .userInfo .uc-dropdown li:last-child {
  background: #aa1c0d;
}
.navbar .navbar-nav .user .userInfo > i {
  position: absolute;
  font-size: 12px;
  right: 12px;
  top: 30px;
}
.navbar .navbar-nav .user .userInfo .btn {
  background: transparent;
  border: 0px;
  text-align: right;
  float: left;
  padding: 22px 5px 22px 5px;
  width: 100%;
}
@media (max-width: 992px) {
  .navbar .navbar-nav .user .userInfo .btn {
    position: absolute;
    top: 0;
    width: 80px;
    height: 80px;
    right: 0;
  }
  .navbar .navbar-nav .user .userInfo .btn span {
    display: none;
  }
}
.navbar .navbar-nav .user .userInfo .btn .icon-arrow_down {
  position: absolute;
  right: 12px;
  top: 30px;
}
@media (max-width: 768px) {
  .navbar .navbar-nav .user .userInfo .btn .icon-arrow_down {
    right: 8px;
  }
}
@media (max-width: 768px) {
  .navbar .navbar-nav .user .userInfo .btn .icon-arrow_down {
    right: 6px;
  }
}
.navbar .navbar-nav .user .userInfo .btn:hover,
.navbar .navbar-nav .user .userInfo .btn:active,
.navbar .navbar-nav .user .userInfo .btn:focus {
  color: #fff;
}
.navbar .navbar-nav .user .userInfo span {
  display: block;
  font-size: 14px;
  line-height: 18px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 150px;
}
.navbar .navbar-nav .user .userInfo span strong {
  text-align: center;
}
.navbar .navbar-nav .user .userContainer {
  float: left;
  width: 48px;
  position: relative;
  padding: 18px 0px;
}
@media (max-width: 992px) {
  .navbar .navbar-nav .user .userContainer {
    padding: 18px 20px;
    width: 80px;
  }
  .navbar .navbar-nav .user .userContainer img {
    display: block;
    margin: 0 auto;
  }
}
.navbar .navbar-nav .user .userContainer .userPicture {
  font-size: 24px;
  overflow: hidden;
  width: 44px;
  height: 44px;
  line-height: 44px;
  float: left;
  color: #999999;
  background: #f6f6f6;
  border-radius: 50%;
  text-align: center;
  position: relative;
}
@media (max-width: 992px) {
  .navbar .navbar-nav .user .userContainer .userPicture {
    margin-left: 0px;
    display: block;
    float: none;
  }
}
@media (max-width: 992px) {
  .navbar .navbar-nav .user .userContainer .userPicture {
    margin-left: -14px;
  }
}
.navbar .navbar-nav .user .userContainer .userPicture img {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
.navbar .navbar-nav .user .userContainer .message-badge {
  background: #fff;
  color: #e2001a;
  border: 3px solid #e2001a;
  width: 24px;
  height: 24px;
  line-height: 18px;
  border-radius: 50%;
  position: absolute;
  top: 13px;
  right: -8px;
  text-align: center;
  font-size: 12px;
  font-family: 'unicreditregular';
}
@media (max-width: 992px) {
  .navbar .navbar-nav .user .userContainer .message-badge {
    right: 10px;
  }
}
@media (max-width: 768px) {
  .navbar .navbar-nav .user .userContainer .message-badge {
    right: 15px;
    top: 7px;
  }
}
.navbar .navbar-nav > li {
  display: table;
}
.navbar .navbar-nav > li > a {
  display: table-cell;
  vertical-align: middle;
  color: #fff;
  height: 80px;
  text-transform: uppercase;
  border-bottom: 3px solid transparent;
  font-size: 14px;
  min-width: 90px;
  text-align: center;
  padding: 5px 0px 0px 0px;
}
.navbar .navbar-nav > li > a:hover {
  background: #aa1c0d;
}
.navbar .navbar-nav > li > a i {
  font-size: 27px;
  display: block;
  text-align: center;
  padding-bottom: 10px;
}
.navbar .navbar-nav > li > a:hover,
.navbar .navbar-nav > li > a:focus,
.navbar .navbar-nav > li > a:active {
  color: #fff;
}
.navbar .navbar-nav > .active > a {
  color: #fff;
  background: #aa1c0d;
  border-bottom: 3px solid #fff;
}
.navbar .navbar-nav > .active > a:hover,
.navbar .navbar-nav > .active > a:focus,
.navbar .navbar-nav > .active > a:active {
  background: #aa1c0d;
  color: #fff;
  border-bottom: 3px solid #fff;
}
.navbar .navbar-nav > .active > a:before {
  position: absolute;
  content: '';
  margin-left: -6px;
  left: 50%;
  bottom: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 6px 6px 6px;
  border-color: transparent transparent #fff transparent;
}
.navbar .navbar-nav.navbar-logout {
  width: 80px;
  background: #aa1c0d;
  margin-right: 0px !important;
}
.navbar .navbar-nav.navbar-logout .logout {
  text-align: center;
}
@media (max-width: 992px) {
  .navbar .navbar-nav.navbar-logout .logout {
    display: none;
  }
}
.navbar .navbar-nav.navbar-logout .logout a {
  height: 80px;
  background: #aa1c0d;
  padding: 5px 0 0 0px;
  text-align: center;
  min-width: 80px;
}
.navbar .navbar-nav.navbar-logout .logout a:focus,
.navbar .navbar-nav.navbar-logout .logout a:active,
.navbar .navbar-nav.navbar-logout .logout a:hover,
.navbar .navbar-nav.navbar-logout .logout a:focus:active {
  color: #fff;
}
.navbar .navbar-brand {
  background: #fff;
  padding: 0;
  height: 80px;
}
@media (max-width: 992px) {
  .navbar .navbar-brand {
    width: calc(100% - 80px);
    text-align: center;
  }
  .navbar .navbar-brand img {
    margin: 0 auto;
    display: block;
  }
}
.navbar .navbar-brand:hover,
.navbar .navbar-brand:focus,
.navbar .navbar-brand:active {
  background: #fff;
}
.navbar .navbar-user {
  background: #aa1c0d;
  text-transform: none;
  height: 80px;
  text-align: right;
}
.navbar .navbar-user > .open > a,
.navbar .navbar-user > .open > a:hover,
.navbar .navbar-user > .open > a:focus {
  background-color: #fff;
}
.navbar .navbar-user > li > a {
  padding: 26px 35px 13px 25px;
  border-bottom: 0;
}
.navbar .navbar-user > li > a:hover,
.navbar .navbar-user > li > a:active,
.navbar .navbar-user > li > a:focus {
  background-color: #e2001a;
  color: #fff;
  border-bottom: 0;
}
.toggled .drop-switch {
  display: none !important;
}
.navbar-collapse {
  padding-right: 0;
}
@media (max-width: 992px) {
  .navbar-collapse {
    width: 80px;
    float: right;
    padding-right: 0;
    margin-right: 0;
    padding-left: 0;
  }
  .navbar-header {
    width: calc(100% - 80px);
    float: left;
  }
  .navbar-nav {
    margin: 0;
  }
}
.dropdown-backdrop {
  position: static;
}
.dropdown-menu > li > a:focus,
.dropdown-menu > li > a:hover {
  background-color: #aa1c0d;
}
body {
  overflow-x: hidden;
}
#wrapper {
  -webkit-transition: all 0s ease;
  -moz-transition: all 0s ease;
  -o-transition: all 0s ease;
  transition: all 0s ease;
  min-height: calc(100vh - 80px);
  background: #333;
  padding-left: 80px;
}
#wrapper.toggled {
  padding-left: 80px;
}
@media (max-width: 992px) {
  #wrapper {
    padding-left: 0;
  }
}
#sidebar-wrapper {
  z-index: 1000;
  position: fixed;
  left: 250px;
  height: calc(100% - 80px);
  margin-left: -250px;
  overflow-y: auto;
  padding-top: 80px;
  background: #333333;
  -webkit-transition: all 0s ease;
  -moz-transition: all 0s ease;
  -o-transition: all 0s ease;
  transition: all 0s ease;
  width: 280px;
}
@media (max-width: 992px) {
  #sidebar-wrapper {
    position: fixed;
    width: 100%;
    height: 100vh;
  }
}
@media (max-width: 992px) {
  #wrapper.toggled {
    padding-left: 0px;
  }
}
#wrapper.toggled #sidebar-wrapper {
  width: 80px;
}
@media (max-width: 992px) {
  #wrapper.toggled #sidebar-wrapper {
    display: none;
  }
}
#wrapper.toggled #page-content-wrapper {
  position: relative;
  margin-right: 0;
}
#page-content-wrapper {
  width: 100%;
  position: relative;
  padding: 20px;
  background: #fcfcfc;
  min-height: calc(100vh - 80px);
  padding-bottom: 100px !important;
}
/* Sidebar Styles */
.sidebar-nav {
  position: fixed;
  top: 80px;
  margin: 0;
  padding: 0;
  list-style: none;
}
.sidebar-nav .search {
  margin: 0;
  height: 80px;
}
.sidebar-nav .search input {
  padding-left: 23px;
  color: #ccc;
}
.sidebar-nav .search span,
.sidebar-nav .search input {
  height: 80px;
  background: transparent;
  border: 0px;
}
.sidebar-nav .search span > .btn,
.sidebar-nav .search input > .btn {
  height: 80px;
  background: transparent;
  border: 0px;
  font-size: 20px;
  color: #ccc;
  padding-right: 20px;
}
.sidebar-nav li {
  width: 100%;
  display: block;
  position: relative;
}
@media (max-width: 992px) {
  .sidebar-nav li {
    display: block;
    width: 100%;
  }
}
.sidebar-nav li ul {
  padding-left: 0;
  padding-top: 10px;
  padding-bottom: 5px;
}
.sidebar-nav li ul li a {
  padding: 3px 50px;
  width: 100%;
  height: auto;
  display: block;
  color: #bebebe;
  text-transform: none;
  font-size: 15px;
  border-left: 0;
}
.sidebar-nav li ul li a:hover,
.sidebar-nav li ul li a:active,
.sidebar-nav li ul li a:focus {
  border-left: 0;
  background: transparent;
  color: #fff;
}
.sidebar-nav li ul li.active a {
  color: #fff !important;
}
.sidebar-nav li.active a {
  color: #fff;
  background: #262626;
  font-family: 'unicreditmedium';
  border-left: 3px solid #e2001a;
}
.sidebar-nav li.active a:active,
.sidebar-nav li.active a:focus {
  text-decoration: none;
}
.sidebar-nav li.active.drop-main-menu.active a {
  background: #e2001a;
  border-left: 3px solid #e2001a;
}
.sidebar-nav li.active ul li a {
  padding: 3px 50px;
  width: 100%;
  height: auto;
  display: block;
  color: #bebebe;
  text-transform: none;
  border: 0;
  background: transparent;
}
.sidebar-nav li.active ul li a:hover,
.sidebar-nav li.active ul li a:active,
.sidebar-nav li.active ul li a:focus {
  border-left: 0;
  background: transparent;
  color: #fff;
}
.sidebar-nav li a {
  width: 280px;
  display: table-cell;
  vertical-align: middle;
  height: 80px;
  padding: 0px 20px;
  color: #fff;
  cursor: pointer;
  text-align: left;
  border-bottom: 1px solid #373737;
  border-left: 3px solid #333333;
  font-size: 18px;
  text-transform: uppercase;
  text-decoration: none;
  color: #bebebe;
  -webkit-transition: all 0s ease;
  -moz-transition: all 0s ease;
  -o-transition: all 0s ease;
  transition: all 0s ease;
}
@media (max-width: 992px) {
  .sidebar-nav li a {
    width: 100%;
    line-height: 80px;
    display: block;
  }
}
.sidebar-nav li a .switch {
  position: Absolute;
  right: 10px;
  font-size: 12px;
  top: 32px;
}
@media (max-width: 992px) {
  .sidebar-nav li a .switch {
    top: 0;
  }
}
.sidebar-nav li a:hover {
  background-color: #262626;
  -webkit-transition: background-color 200ms linear;
  -ms-transition: background-color 200ms linear;
  transition: background-color 200ms linear;
  border-left: 3px solid #fff;
}
.sidebar-nav li a i {
  font-size: 24px;
  padding-right: 15px;
}
.toggled {
  transition: none;
}
.toggled .sidebar-nav li ul {
  display: none;
}
.toggled .sidebar-nav li ul li a {
  color: #fff;
}
.toggled .sidebar-nav li a {
  width: 80px;
  height: 80px;
  text-align: center;
  border-bottom: 1px solid #373737;
  border-left: 3px solid #333333;
  padding: 0;
  text-decoration: none;
  color: #999999;
  font-size: 12px;
  line-height: 12px;
  text-transform: uppercase;
}
.toggled .sidebar-nav li a .switch {
  display: none;
}
.toggled .sidebar-nav li a i {
  display: block;
  font-size: 26px;
  width: 100%;
  text-align: center;
  padding: 0 0 13px 0;
}
.toggled .sidebar-nav li a:hover {
  border-left: 3px solid #fff;
}
.toggled .sidebar-nav li.active a {
  color: #fff;
  background: #262626;
  border-left: 3px solid #e2001a;
  font-family: 'unicreditmedium';
}
.toggled .sidebar-nav li.active a:active,
.toggled .sidebar-nav li.active a:focus {
  text-decoration: none;
}
.toggled > .sidebar-brand {
  height: 65px;
  font-size: 18px;
  line-height: 60px;
}
.toggled > .sidebar-brand a {
  color: #999999;
}
.toggled > .sidebar-brand a:hover {
  color: #fff;
  background: none;
}
.drop-main-menu a i {
  position: absolute;
  right: 8px;
  font-size: 17px !important;
}
.drop-switch a {
  line-height: 24px !important;
}
label {
  font-weight: normal;
  min-height: 22px;
  display: block;
  font-family: 'unicreditmedium';
  color: #333;
}
.form-control {
  margin-bottom: 0px;
  border-radius: 3px;
  border: 1px solid #ccc;
  box-shadow: none;
  height: 40px;
  font-family: 'unicreditmedium';
  font-size: 16px;
  color: #333333;
}
.form-control:focus {
  box-shadow: none;
  border-color: #00afd0;
  background-color: #fff;
}
.form-control.error {
  border: 2px solid #ed5c49;
  background-color: transparent;
}
.form-group {
  margin-bottom: 1rem;
}
/* @media (min-width: 800px) {
  .form-group {
    margin-bottom: 3px;
  } 
}*/
.radio-buttons {
  padding-left: 0;
  list-style: none;
}
.radio-buttons li {
  display: inline-block;
}
.checkbox-buttons .custom-checkbox,
.checkbox-buttons .custom-radio,
.radio-buttons .custom-checkbox,
.radio-buttons .custom-radio {
  display: inline-block;
  margin-right: 10px;
}
.custom-checkbox [type='checkbox']:not(:checked),
.custom-checkbox [type='checkbox']:checked {
  position: absolute;
  left: -9999px;
}
.custom-checkbox [type='checkbox']:not(:checked) + label,
.custom-checkbox [type='checkbox']:checked + label {
  position: relative;
  padding-left: 1.95em;
  cursor: pointer;
  font-weight: 400;
  line-height: 18px;
}
.custom-checkbox [type='checkbox']:not(:checked) + label:hover:before,
.custom-checkbox [type='checkbox']:checked + label:hover:before {
  border: 1px solid #00afd0;
}
.custom-checkbox [type='checkbox']:not(:checked) + label:before,
.custom-checkbox [type='checkbox']:checked + label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 1.25em;
  height: 1.25em;
  border: 1px solid #999;
  background: #fff;
  border-radius: 0px;
}
.custom-checkbox [type='checkbox']:not(:checked) + label:after,
.custom-checkbox [type='checkbox']:checked + label:after {
  font-family: 'FontAwesome';
  content: '\f00c';
  position: absolute;
  top: 4px;
  left: 2px;
  font-size: 15px;
  line-height: 0.8;
  color: #00afd0;
  transition: all 0.2s;
}
.custom-checkbox [type='checkbox']:not(:checked) + label:after {
  opacity: 0;
  transform: scale(0);
}
.custom-checkbox [type='checkbox']:checked + label:after {
  opacity: 1;
  transform: scale(1);
}
.custom-checkbox [type='checkbox']:disabled:not(:checked) + label:before,
.custom-checkbox [type='checkbox']:disabled:checked + label:before {
  box-shadow: none;
  border-color: #ccc;
  background-color: #e5e5e5;
}
.custom-checkbox [type='checkbox']:checked:focus + label:before,
.custom-checkbox [type='checkbox']:not(:checked):focus + label:before {
  border: 1px solid #00afd0;
}

.custom-checkbox.center-aligned [type='checkbox']:not(:checked) + label:before,
.custom-checkbox.center-aligned [type='checkbox']:checked + label:before,
.custom-checkbox.center-aligned [type='checkbox']:not(:checked) + label:after,
.custom-checkbox.center-aligned [type='checkbox']:checked + label:after,
.uc-table tr td .custom-checkbox.center-aligned [type='checkbox']:checked + label:after{
  left: auto;
  right: 0;
}
.custom-radio label {
  display: inline-block;
  cursor: pointer;
  position: relative;
  padding-left: 28px !important;
  margin-right: 15px;
  font-size: 15px;
  font-weight: normal;
}
.custom-radio label:before {
  content: '';
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 10px;
  position: absolute;
  left: 0;
  bottom: 1px;
  background-color: #fff;
  border-radius: 50%;
  border: 1px solid #999;
}
.custom-radio label:hover:before {
  border: 1px solid #00afd0 !important;
}
.custom-radio input[type='radio'] {
  display: none;
}
.custom-radio input[type='radio']:checked + label:before {
  font-family: 'FontAwesome';
  content: '\f111';
  color: #00afd0;
  border: 1px solid #00afd0;
  font-size: 17px;
  text-align: center;
  line-height: 18px;
}
.custom-radio [type='radio']:disabled:not(:checked) + label:before,
.custom-radio [type='radio']:disabled:checked + label:before {
  background-color: #e5e5e5;
  border: 1px solid #ccc;
}
.custom-radio [type='radio']:disabled:not(:checked) + label:hover:before,
.custom-radio [type='radio']:disabled:checked + label:hover:before {
  border: 1px solid #ccc !important;
}
.custom-select {
  overflow: hidden;
  background: white url('./assets/img/select.png') no-repeat right top;
}
.custom-select select {
  box-shadow: none;
  background-color: transparent;
  background-image: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}
.custom-select select::-ms-expand {
  display: none;
}
.custom-select select:focus {
  outline: none;
  background: white url('./assets/img/select.png') no-repeat right top;
}
.custom-select select[disabled] {
  background: #eeeeee url('./assets/img/select.png') no-repeat right top;
}
.calendar {
  background: white url(./assets/img/calendar.png) no-repeat right 10px center;
}
.select-all {
  padding: 7px 5px;
}
.panel-group .panel + .panel {
  margin-top: 2px;
}
.newAsset {
  display: none;
}
.newAsset.exist {
  display: block !important;
}
.panel-default > .panel-heading {
  padding: 0;
  text-transform: uppercase;
  background: #fff;
  position: relative;
}
.panel-default > .panel-heading .btn-set {
  display: inline-block;
  float: right;
  padding: 8px 10px;
}
.panel-default > .panel-heading .state {
  position: absolute;
  right: 15px;
  top: 12px;
}
.panel-default > .panel-heading .panel-title a {
  text-decoration: none;
  font-family: 'unicreditmedium';
  text-transform: uppercase;
  padding: 15px 15px 15px 18px;
  height: 47px;
  display: inline-block;
}
.panel-default > .panel-heading .panel-title a i {
  margin-right: 10px;
  color: #00afd0;
}
.panel-default .panel-body {
  padding: 0px;
}
.panel-default .panel-body .panel-box {
  padding: 15px;
}
.tooltip.in {
  opacity: 1;
  z-index: 100;
}
.tooltip > .tooltip-inner {
  background-color: #fff;
  color: #333;
  font-size: 14px;
  font-family: 'unicreditregular';
  border-radius: 3px;
  padding: 10px 12px;
  max-width: 850px;
  border: 1px solid;
  text-align: left;
  /* border-color: #e2001a; */
}

.tooltip.bottom .tooltip-arrow {
  /* border-bottom-color: #e2001a !important; */
}
.tooltip.right .tooltip-arrow {
  /* border-right-color: #e2001a; */
}
.tooltip.left .tooltip-arrow {
  /* border-left-color: #e2001a; */
}
.tooltip-inner {
  min-width: 100px;
  max-width: 100%;
}
.tooltip-warning > div {
  position: static;
}
.tooltip-col-support {
  position: static;
}
.tooltip-arrow,
.blue-tooltip + .tooltip > .tooltip-inner {
  background-color: #fff;
  color: #999;
}
.blue-tooltip + .tooltip.top > .tooltip-arrow {
  background-color: transparent;
  border-top-color: #fff;
}
.blue-tooltip + .tooltip.right > .tooltip-arrow {
  background-color: #fff;
  border-right-color: #fbfbfb !important;
}
.blue-tooltip + .tooltip > .tooltip-inner {
  box-shadow: 2px 2px 2px #e3e3e3;
  min-width: 150px;
}
.tooltip-arrow,
.regular-tooltip + .tooltip > .tooltip-inner {
  background-color: #fff;
  color: #333;
}
.regular-tooltip + .tooltip.top > .tooltip-arrow {
  background-color: transparent;
  border-top-color: #fff;
}
.regular-tooltip + .tooltip.right > .tooltip-arrow {
  border-right-color: #ccc !important;
}
.regular-tooltip + .tooltip.left > .tooltip-arrow {
  border-left-color: #ccc !important;
}
.regular-tooltip + .tooltip > .tooltip-inner {
  box-shadow: 2px 2px 2px #e3e3e3;
  min-width: 150px;
}
.alert {
  position: absolute;
  left: 0;
  right: 0;
  /* top: 0; */
  bottom: 25px; /* margin-left: 0; */
  margin-top: 0px;
  margin-bottom: 0;
  margin: 0 auto; /* margin-right: 0; */
  width: 500px;
  text-align: center;
  font-size: 14px;
  display: inline-block;
  padding: 7px;
}
label {
  width: 100%;
}
.input-field label {
  font-size: 0.8rem;
  -webkit-transform: translateY(-140%);
  -moz-transform: translateY(-140%);
  -ms-transform: translateY(-140%);
  -o-transform: translateY(-140%);
  transform: translateY(-140%);
}
.uc-datatabs {
  padding: 20px 0px;
}
.uc-datatabs .nav-tabs > li:first-child {
  margin-left: 10px;
}
.uc-datatabs .nav-tabs > li > a {
  color: #333333;
  font-family: 'unicreditmedium';
}
.uc-datatabs .nav > li > a:focus,
.uc-datatabs .nav > li > a:hover {
  color: #00afd0;
  background: transparent;
}
.uc-datatabs .nav > li > a:hover {
  border-color: transparent;
}
.uc-datatabs .nav > li.active > a:hover {
  border-color: transparent;
  border: 1px solid #ddd;
  border-bottom-color: transparent;
}
.uc-datatabs .nav-tabs > li.active > a,
.uc-datatabs .nav-tabs > li.active > a:focus,
.uc-datatabs .nav-tabs > li.active > a:hover {
  color: #00afd0;
  font-family: 'unicreditmedium';
  background: #fcfcfc;
}
.datepicker {
  padding: 10px 5px;
  border: 0;
  border-radius: 1px;
}
.datepicker td,
.datepicker th {
  width: 42px;
  height: 38px;
  font-weight: normal;
  font-family: 'unicreditmedium';
}
.datepicker .datepicker-switch,
.datepicker .prev,
.datepicker .next {
  background: #ccecea;
  border: 0;
  border-radius: 0;
}
.datepicker table tr td.active,
.datepicker table tr td.active:hover,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active:hover,
.datepicker table tr td.active:hover:hover,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active.disabled:hover:hover,
.datepicker table tr td.active:active,
.datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active.disabled:hover.active,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active:hover.disabled,
.datepicker table tr td.active.disabled.disabled,
.datepicker table tr td.active.disabled:hover.disabled,
.datepicker table tr td.active[disabled],
.datepicker table tr td.active:hover[disabled],
.datepicker table tr td.active.disabled[disabled],
.datepicker table tr td.active.disabled:hover[disabled],
.datepicker table tr td span.active:hover,
.datepicker table tr td span.active:hover:hover,
.datepicker table tr td span.active.disabled:hover,
.datepicker table tr td span.active.disabled:hover:hover,
.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover:active,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active.active,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled:hover.active,
.datepicker table tr td span.active.disabled,
.datepicker table tr td span.active:hover.disabled,
.datepicker table tr td span.active.disabled.disabled,
.datepicker table tr td span.active.disabled:hover.disabled,
.datepicker table tr td span.active[disabled],
.datepicker table tr td span.active:hover[disabled],
.datepicker table tr td span.active.disabled[disabled],
.datepicker table tr td span.active.disabled:hover[disabled] {
  background-image: none;
  background: #00afd0;
}
.datepicker .datepicker-switch:hover,
.datepicker .prev:hover,
.datepicker .next:hover,
.datepicker tfoot tr th:hover {
  background: #87dfda;
}
.checklist-tab-wrap {
  margin: 20px 0px;
}
.tabs-with-select {
  overflow: hidden;
}
.tabs-with-select .nav-tabs {
  float: left;
  width: calc(100% - 150px);
}
.tabs-with-select .custom-select {
  width: 150px;
  float: left;
  border-bottom: 1px solid #ddd;
  padding-bottom: 3px;
}
.controls {
  margin-top: 10px;
}
.controls .btn {
  float: right;
}
.controls .separator {
  display: inline-block;
  height: 30px;
  width: 1px;
  float: right;
  background: #e0e0e0;
  margin-top: 4px;
}
.inline-status {
  display: inline-block;
  padding: 6px 12px;
  float: right;
}
.inline-status i {
  float: right;
  margin-left: 5px;
  padding: 2px;
}
.text {
  display: inline-block;
  float: right;
  margin-left: 10px;
  color: #999;
}
.icon-info {
  color: #666;
}
.icon-info:hover {
  color: #00afd0;
}
.input-group {
  margin-top: 10px;
  margin-bottom: 20px;
}
.input-group-btn > .btn {
  height: 40px;
  border-radius: 0px 4px 4px 0px;
  border-left: 0;
  color: #fff;
  background: #00afd0;
  border: 1px solid #00afd0;
}
.input-group-btn > .btn:hover,
.input-group-btn > .btn:active,
.input-group-btn > .btn:focus {
  background: #009dbb;
  border: 1px solid #009dbb;
}
.input-group-btn > .btn.btn-primary {
  background: #009dbb;
  border: 1px solid #009dbb;
}
.expand-filters .form-control {
  margin-bottom: 0;
}
.editable-field .btn {
  width: calc(100% - 40px);
}
.editable-field .icon-info {
  width: 40px;
  margin: 10px 0px;
}
.lock {
  background: #00afd0;
  color: #fff;
  font-size: 13px !important;
  width: 25px;
  height: 25px;
  line-height: 25px;
  border-radius: 50%;
  float: right;
  margin-left: 10px;
  margin-top: 7px;
  text-align: center;
}
.btn {
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
  transition: all 0.2s ease;
}
.btn-set .btn {
  margin-left: 15px;
}
.btn-empty {
  background: transparent;
  font-size: 15px;
  color: #666;
  font-family: 'unicreditmedium';
}
.btn-empty:hover,
.btn-empty:focus,
.btn-empty:active {
  color: #00afd0;
}
.btn-clean {
  background: transparent;
  padding: 5px 8px;
  display: inline-block;
}
.btn-clean:hover {
  background-color: #fff;
  border-color: #ccc;
}
.btn-clean:focus,
.btn-clean:active {
  background-color: #fff;
  border-color: #ccc;
  color: #222;
}
.btn-clean.return {
  border-right: 1px solid #e4e4e4;
  border-radius: 0px;
  margin: 4px;
  color: #00afd0;
  padding: 4px 13px;
}
.btn-clean.return:hover {
  border-color: transparent;
  border-right: 1px solid #e4e4e4;
  color: #009dbb;
}
.btn-clean.return:focus,
.btn-clean.return:active {
  color: #009dbb;
  border-color: transparent;
}
.btn-clean.return i {
  margin-right: 10px;
}
.btn-primary {
  background: #00afd0;
  color: #fff;
  border: 1px solid #009dbb;
  text-transform: uppercase;
  font-family: 'unicreditmedium';
  border-radius: 3px;
  padding: 9px 20px;
}
.btn-primary:hover {
  background-color: #009dbb;
  border-color: #009dbb;
}
.btn-primary:focus,
.btn-primary:active {
  background-color: #007a91;
  border-color: #007a91;
}
.btn-secondary {
  background: transparent;
  border: 1px solid #999;
  padding: 9px 20px;
  font-size: 14px;
  font-family: 'unicreditmedium';
  text-transform: uppercase;
  border-radius: 3px;
  color: #666;
}
.btn-secondary:hover {
  background-color: #fff;
  border-color: #333;
}
.btn-secondary:focus,
.btn-secondary:active {
  background-color: #fff;
  border-color: #e4e4e4;
  color: #333;
}
.btn-secondary.search-btn:hover,
.btn-secondary.search-btn:focus,
.btn-secondary.search-btn:active {
  color: #00afd0;
}
.btn-secondary.filters-btn {
  color: #00afd0;
  text-decoration: underline;
  border: 0;
}
.btn-secondary.filters-btn:hover,
.btn-secondary.filters-btn:focus,
.btn-secondary.filters-btn:active {
  color: #009dbb;
}
.btn-tertiary {
  background: transparent;
  border: 1px solid #00afd0;
  padding: 9px 20px;
  font-size: 14px;
  font-family: 'unicreditmedium';
  text-transform: uppercase;
  border-radius: 3px;
  color: #00afd0;
}
.btn-tertiary:hover {
  background-color: #fff;
  color: #009dbb;
  border-color: #009dbb;
}
.btn-tertiary:focus,
.btn-tertiary:active {
  background-color: #fff;
  color: #009dbb;
  border-color: #009dbb;
}
.btn-action {
  background: #fef1cc;
  border: 1px solid #fbb800;
  width: 100%;
  height: 40px;
  text-align: left;
  font-size: 15px;
}
.btn-action.text-center {
  text-align: center !important;
}
.btn-action.active {
  background: #ebf6ef;
  border: 1px solid #2fa358;
  box-shadow: none;
}
.close-search {
  position: absolute;
  right: 45px;
  height: 40px;
  width: 40px;
  z-index: 999;
  top: 0px;
  color: #666;
  background: transparent;
  border: 0;
}
.close-search:hover {
  color: #00afd0;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.waves-effect {
  position: relative;
  cursor: pointer;
  display: inline-block;
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  vertical-align: middle;
  z-index: 1;
  transition: 0.3s ease-out;
}
.waves-effect .waves-ripple {
  position: absolute;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  margin-top: -10px;
  margin-left: -10px;
  opacity: 0;
  background: rgba(0, 0, 0, 0.2);
  transition: all 0.7s ease-out;
  transition-property: opacity, -webkit-transform;
  transition-property: transform, opacity;
  transition-property: transform, opacity, -webkit-transform;
  -webkit-transform: scale(0);
  transform: scale(0);
  pointer-events: none;
}
.waves-effect input[type='button'],
.waves-effect input[type='reset'],
.waves-effect input[type='submit'] {
  border: 0;
  font-style: normal;
  font-size: inherit;
  text-transform: inherit;
  background: none;
}
.waves-effect img {
  position: relative;
  z-index: -1;
}
.waves-notransition {
  transition: none !important;
}
.waves-circle {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);
}
.waves-input-wrapper {
  border-radius: 0.2em;
  vertical-align: bottom;
}
.waves-input-wrapper .waves-button-input {
  position: relative;
  top: 0;
  left: 0;
  z-index: 1;
}
.waves-circle {
  text-align: center;
  width: 2.5em;
  height: 2.5em;
  line-height: 2.5em;
  border-radius: 50%;
  -webkit-mask-image: none;
}
.waves-block {
  display: block;
}
.waves-effect .waves-ripple {
  background-color: #08bbdd;
}
.waves-effect.waves-secondary .waves-ripple {
  background-color: #e5eff2;
}
.waves-effect.waves-progress .waves-ripple {
  background-color: #99d9d5;
}
/* Firefox Bug: link not triggered */
.waves-effect .waves-ripple {
  z-index: -1;
}
.import-btn.inactive {
  opacity: 0.5;
}
.uc-table,
.table-fixed {
  border: 1px solid #ccc;
  border-collapse: collapse;
  margin: 0;
  padding: 0;
  width: 100%;
  table-layout: fixed;
  position: relative;
}
.uc-table caption,
.table-fixed caption {
  font-size: 20px;
  padding: 13px 15px !important;
  margin: 0px;
  background: #eee;
}
.uc-table tbody,
.table-fixed tbody {
  position: relative;
  overflow: auto;
}
.uc-table tbody tr,
.table-fixed tbody tr {
  border: 0;
}
.uc-table tbody tr:nth-child(even),
.table-fixed tbody tr:nth-child(even) {
  background: #f9f9f9;
}
.uc-table tbody tr:nth-child(odd),
.table-fixed tbody tr:nth-child(odd) {
  background: #fff;
}
.uc-table tr,
.table-fixed tr {
  border: 0;
}
.uc-table tr th,
.uc-table tr td,
.table-fixed tr th,
.table-fixed tr td {
  text-align: left;
  font-weight: normal;
  padding: 7px 15px !important;
  vertical-align: middle;
}
.uc-table tr th .custom-checkbox label,
.uc-table tr td .custom-checkbox label,
.table-fixed tr th .custom-checkbox label,
.table-fixed tr td .custom-checkbox label {
  margin-bottom: 0px;
}
@media (max-width: 992px) {
  .uc-table
    tr
    th
    .custom-checkbox
    [type='checkbox']:not(:checked)
    + label:after,
  .uc-table tr th .custom-checkbox [type='checkbox']:checked + label:after,
  .uc-table
    tr
    td
    .custom-checkbox
    [type='checkbox']:not(:checked)
    + label:after,
  .uc-table tr td .custom-checkbox [type='checkbox']:checked + label:after,
  .table-fixed
    tr
    th
    .custom-checkbox
    [type='checkbox']:not(:checked)
    + label:after,
  .table-fixed tr th .custom-checkbox [type='checkbox']:checked + label:after,
  .table-fixed
    tr
    td
    .custom-checkbox
    [type='checkbox']:not(:checked)
    + label:after,
  .table-fixed tr td .custom-checkbox [type='checkbox']:checked + label:after {
    top: 2px;
    left: 1px;
  }
}
@media (min-width: 993px) {
  .custom-checkbox.center-aligned [type='checkbox']:not(:checked) + label:before,
  .custom-checkbox.center-aligned [type='checkbox']:checked + label:before {
    left: 50%;
    margin-left: -10px;
    right: auto;
  }
  .custom-checkbox.center-aligned [type='checkbox']:not(:checked) + label:after,
  .custom-checkbox.center-aligned [type='checkbox']:checked + label:after,
  .uc-table tr td .custom-checkbox.center-aligned [type='checkbox']:checked + label:after {
    left: 50%;
    margin-left: -8px;
    right: auto;
  }
}
.uc-table tr th .custom-radio label,
.uc-table tr td .custom-radio label,
.table-fixed tr th .custom-radio label,
.table-fixed tr td .custom-radio label {
  margin-top: 6px;
  margin-bottom: 0px;
}
.uc-table tr th table,
.uc-table tr td table,
.table-fixed tr th table,
.table-fixed tr td table {
  margin: 10px 0px;
}
.uc-table tr th table th:first-child,
.uc-table tr td table th:first-child,
.table-fixed tr th table th:first-child,
.table-fixed tr td table th:first-child {
  border-right: 1px solid #c6c6c6;
}
.uc-table tr th.table-controls,
.uc-table tr td.table-controls,
.table-fixed tr th.table-controls,
.table-fixed tr td.table-controls {
  text-align: left;
}
.uc-table tr th.table-controls .additional-description,
.uc-table tr td.table-controls .additional-description,
.table-fixed tr th.table-controls .additional-description,
.table-fixed tr td.table-controls .additional-description {
  float: left;
  margin-right: 15px;
  line-height: 35px;
}
.uc-table tr th.table-controls .custom-select,
.uc-table tr td.table-controls .custom-select,
.table-fixed tr th.table-controls .custom-select,
.table-fixed tr td.table-controls .custom-select {
  max-width: 200px;
}
.uc-table tr th,
.table-fixed tr th {
  letter-spacing: 0;
  font-family: 'unicreditmedium';
  background: #e5eff2;
  padding: 13px 15px !important;
  border-right: 1px solid #c6c6c6;
  border-bottom: 1px solid #ccc;
  color: #333;
}
.uc-table tr th.checkbox-col,
.table-fixed tr th.checkbox-col {
  width: 35px;
}
.uc-table tr th:last-child,
.uc-table tr th:first-child,
.table-fixed tr th:last-child,
.table-fixed tr th:first-child {
  border-right: 0;
}
.uc-table tr .table-btn-dropdown,
.table-fixed tr .table-btn-dropdown {
  max-width: 41px;
  float: right;
}
.uc-table tr .table-btn-dropdown .dropdown-menu,
.table-fixed tr .table-btn-dropdown .dropdown-menu {
  min-width: 120px;
  padding: 0px 10px;
}
.uc-table tr .table-btn-dropdown .dropdown-menu .btn,
.table-fixed tr .table-btn-dropdown .dropdown-menu .btn {
  float: right;
  text-align: left;
  min-width: 127px;
}
.uc-table tr .table-btn-dropdown .dropdown-menu .btn i,
.table-fixed tr .table-btn-dropdown .dropdown-menu .btn i {
  color: #00afd0;
  padding-right: 10px;
}
@media (min-width: 992px) {
  .uc-table tr td,
  .uc-table tr th,
  .table-fixed tr td,
  .table-fixed tr th {
    text-align: center;
  }
  .uc-table tr td:nth-child(-n + 2),
  .uc-table tr th:nth-child(-n + 2),
  .table-fixed tr td:nth-child(-n + 2),
  .table-fixed tr th:nth-child(-n + 2) {
    text-align: left;
  }
  .uc-table tr td:last-child,
  .uc-table tr th:last-child,
  .table-fixed tr td:last-child,
  .table-fixed tr th:last-child {
    text-align: right;
  }
}
@media (min-width: 992px) {
  .uc-table.uc-multirow tr td,
  .uc-table.uc-multirow tr th,
  .table-fixed.uc-multirow tr td,
  .table-fixed.uc-multirow tr th {
    text-align: center;
  }
  .uc-table.uc-multirow tr td:last-child,
  .uc-table.uc-multirow tr th:last-child,
  .table-fixed.uc-multirow tr td:last-child,
  .table-fixed.uc-multirow tr th:last-child {
    text-align: center;
  }
  .uc-table.uc-multirow tr td:last-child.text-left,
  .uc-table.uc-multirow tr th:last-child.text-left,
  .table-fixed.uc-multirow tr td:last-child.text-left,
  .table-fixed.uc-multirow tr th:last-child.text-left {
    text-align: left !important;
  }
  .uc-table.uc-multirow tr td:first-child,
  .uc-table.uc-multirow tr th:first-child,
  .table-fixed.uc-multirow tr td:first-child,
  .table-fixed.uc-multirow tr th:first-child {
    text-align: left;
  }
}
.uc-table.uc-multirow tbody tr td,
.table-fixed.uc-multirow tbody tr td {
  position: relative;
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.uc-table.uc-multirow tbody tr td input,
.table-fixed.uc-multirow tbody tr td input {
  max-width: calc(100% - 20px);
  margin: 0 auto;
  float: none;
}
.uc-table.uc-multirow tbody tr td input.form-tooltip,
.table-fixed.uc-multirow tbody tr td input.form-tooltip {
  max-width: calc(100% - 80px);
}
.uc-table.uc-multirow tbody tr td:first-child span,
.table-fixed.uc-multirow tbody tr td:first-child span {
  padding-left: 15px;
}
.uc-table.uc-multirow tbody tr td tr td:first-child span,
.table-fixed.uc-multirow tbody tr td tr td:first-child span {
  padding-left: 30px;
}
.uc-table.uc-multirow tbody tr td tr td tr td:first-child span,
.table-fixed.uc-multirow tbody tr td tr td tr td:first-child span {
  padding-left: 45px;
}
.uc-table.uc-multirow tbody tr td .td-tooltip,
.table-fixed.uc-multirow tbody tr td .td-tooltip {
  position: absolute;
  right: 20px;
  top: 20px;
}
.uc-table tfoot,
.table-fixed tfoot {
  background: #f2f2f2;
}
.uc-table thead th,
.table-fixed thead th {
  border-right: 1px solid #c6c6c6;
  border-bottom: 1px solid #ccc;
  word-break: break-word;
}
.uc-table thead th.checkbox-col,
.table-fixed thead th.checkbox-col {
  width: 35px;
}
.uc-table thead th:first-child,
.table-fixed thead th:first-child {
  border-right: 1px solid #c6c6c6;
}
.uc-table thead th.text-center,
.table-fixed thead th.text-center {
  text-align: center !important;
}
.uc-table.uc-table-clean,
.table-fixed.uc-table-clean {
  border: 0;
}
.uc-table.uc-table-clean tbody tr,
.table-fixed.uc-table-clean tbody tr {
  background: transparent;
  border-bottom: 1px solid #c6c6c6;
}
.uc-table.uc-table-clean tbody tr:first-child,
.table-fixed.uc-table-clean tbody tr:first-child {
  border-top: 1px solid #c6c6c6;
}
.uc-table.uc-table-clean tbody tr td,
.table-fixed.uc-table-clean tbody tr td {
  background: transparent;
}
.uc-table.uc-table-clean tbody tr td strong,
.table-fixed.uc-table-clean tbody tr td strong {
  margin-right: 15px;
}
.uc-table.uc-table-clean tbody tr td span,
.table-fixed.uc-table-clean tbody tr td span {
  font-size: 14px;
  margin-left: 15px;
}
.panel-body .uc-table {
  border: 0;
}
.table thead tr th {
  color: #00afd0;
  font-family: 'unicreditmedium';
  border-bottom: 0;
  font-weight: normal;
}
.table tr td {
  border-top: 0;
  border-bottom: 1px solid #ccc;
  vertical-align: middle;
}
.table tr td:last-child,
.table tr th:last-child {
  text-align: right;
}
.table a {
  color: #00afd0;
  text-decoration: none;
}
.table > tbody > tr > td,
.table > tbody > tr > th,
.table > tfoot > tr > td,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > thead > tr > th {
  vertical-align: middle;
}
@media (max-width: 992px) {
  .uc-table,
  .table {
    border: 0;
  }
  .uc-table thead,
  .table thead {
    border: none;
    clip: rect(0 0 0 0);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
  }
  .uc-table tr,
  .table tr {
    border-bottom: 3px solid #ddd;
    display: block;
    margin-bottom: 0.625em;
  }
  .uc-table tr td,
  .table tr td {
    border-bottom: 1px solid #ddd;
    display: block;
    font-size: 0.8em;
    text-align: right;
    min-height: 32px;
  }
  .uc-table tr td:before,
  .table tr td:before {
    content: attr(data-label);
    float: left;
    font-weight: normal;
    font-family: 'unicreditmedium';
    text-transform: uppercase;
    padding-right:20px;
  }
  .uc-table tr td:last-child,
  .table tr td:last-child {
    border-bottom: 0;
  }
}
.table-display {
  display: table;
  width: 100%;
  min-height: 30px;
}
.table-display .vertical-align {
  display: table-cell;
  vertical-align: middle;
  font-size: 14px;
}
.table-display .vertical-align label {
  float: left;
  font-family: 'unicreditmedium';
  word-break: break-all;
  padding-bottom: 0;
  margin-bottom: 0;
  margin-right: 5px;
  width: auto;
}
.table-display .vertical-align p {
  float: left;
  margin-bottom: 0;
  word-break: break-all;
}
.table-display .vertical-align p span {
  font-size: 11px;
  display: block;
}
.table-fixed-wrap {
  overflow-x: scroll;
  margin-left: 133px;
}
.table-fixed-wrap .table-fixed {
  position: inherit;
  table-layout: auto;
  border: 0;
}
.table-fixed-wrap .table-fixed thead tr th {
  border: 0;
  text-align: center !important;
}
.table-fixed-wrap .table-fixed thead tr td {
  background: #e5eff2;
}
.table-fixed-wrap td,
.table-fixed-wrap th {
  min-width: 80px;
  height: 54px;
}
.table-fixed-wrap tbody tr:nth-child(even) {
  background: #fff;
}
.table-fixed-wrap tbody tr:nth-child(n + 4):nth-child(-n + 5) {
  background: #f9f9f9;
}
.table-fixed-wrap tbody tr:nth-child(n + 4):nth-child(-n + 5) .fixed {
  background: #f9f9f9;
}
.table-fixed-wrap tbody tr:nth-child(1) td,
.table-fixed-wrap tbody tr:nth-child(4) td,
.table-fixed-wrap tbody tr:nth-child(6) td {
  font-family: 'unicreditbold';
}
.table-fixed-wrap tbody tr:nth-child(1) td.fixed,
.table-fixed-wrap tbody tr:nth-child(4) td.fixed,
.table-fixed-wrap tbody tr:nth-child(6) td.fixed {
  padding-left: 15px !important;
}
.table-fixed-wrap .fixed {
  position: absolute;
  left: 0;
  width: 150px;
  line-height: 40px;
  background: #fff;
  border-right: 1px solid #c6c6c6;
  padding-left: 35px !important;
}
.table-fixed-wrap .fixed i {
  position: absolute;
  right: 20px;
  font-weight: normal;
  top: 17px;
}
.table-fixed-wrap .fixed.fixed-two-rows {
  top: 0;
  line-height: normal;
  height: 113px;
  vertical-align: middle;
  bottom: 0;
  padding: 45px 12px !important;
}
.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -ms-overflow-style: -ms-autohiding-scrollbar;
}
.table-responsive.table-bordered {
  border: 0;
}
.modal-content {
  background: #fff;
  box-shadow: none;
  border-radius: 2px;
  border: 0;
}
.modal-header {
  padding: 0;
}
.modal-header h2 {
  padding: 25px 20px;
  margin-bottom: 0;
}
.modal-header .close {
  padding: 25px 20px;
  position: absolute;
  right: 0px;
  top: 5px;
}
.modal-header .close img {
  max-width: 25px;
}
.modal-body {
  border: 0;
  padding: 20px;
}
.modal-footer {
  text-align: center;
  border: 0;
  padding: 25px 20px;
}
.modal-backdrop.in {
  filter: alpha(opacity=80);
  opacity: 0.8;
}
.step-navigation {
  margin-left: -35px;
  margin-right: -35px;
  background: #f5f5f5;
  padding: 10px 20px;
  line-height: 30px;
  min-height: 56px;
}
.step-navigation label {
  margin-bottom: 0;
}
.step-navigation .custom-checkbox {
  padding: 8px 14px 3px 17px;
  max-width: 180px;
  float: left;
}
.step-navigation .btn {
  font-size: 16px;
}
.box-wrap {
  position: relative;
  margin-bottom: 25px;
}
.box-wrap .box-layout {
  background: #fff;
  border: 1px solid #e7e7e7;
  padding: 10px 15px;
  overflow: hidden;
  width: 100%;
}
.box-wrap .box-layout .box-col {
  width: 20%;
  float: left;
}
@media (max-width: 992px) {
  .box-wrap .box-layout .box-col {
    width: 33%;
    margin-top: 10px;
  }
}
.box-wrap .box-layout .state-id {
  color: #ccc;
  font-size: 14px;
  float: right;
  margin-top: 14px;
  margin-right: 14px;
}
.box-wrap .box-layout .btn {
  margin-left: 5px;
}
.box-wrap .box-toggle {
  background: #fff;
  overflow: hidden;
  border: 1px solid #e7e7e7;
  border-top: 0;
  border-radius: 0px 0px 4px 4px;
  text-align: center;
  position: absolute;
  width: 50px;
  color: #ccc;
  height: 25px;
  bottom: -24px;
  left: 0;
  right: 0;
  margin: 0 auto;
}
.box-wrap .box-toggle .btn {
  padding: 0px;
  display: block;
  width: 100%;
  transition: none;
  border: 0;
  font-size: 15px;
  -webkit-transform: translateZ(0);
}
.box-wrap .box-toggle .btn:hover,
.box-wrap .box-toggle .btn:active,
.box-wrap .box-toggle .btn:focus,
.box-wrap .box-toggle .btn:focus:hover {
  border: 0;
  padding: 0;
}
.success-confirmation {
  max-width: 500px;
  margin: 0 auto;
  text-align: -webkit-center;
  padding: 20px 0px;
}
.success-confirmation .icon-check {
  color: #00afd0;
  border: 3px solid #00afd0;
  width: 40px;
  height: 40px;
  text-align: center;
  border-radius: 50%;
  display: inline-block;
  line-height: 35px;
  font-size: 20px;
}
@media (max-width: 992px) {
  .status-check {
    text-align: left;
  }
}
.status-check .icon-check {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: #00afd0;
  text-align: center;
  color: #fff;
  line-height: 20px;
  font-size: 11px;
  border-radius: 50%;
  float: left;
  margin-top: 5px;
}
.status-check .simulation {
  float: left;
  width: calc(100% - 30px);
  margin-left: 10px;
}
.status-check .simulation .sim-label {
  display: block;
  font-family: 'unicreditbold';
  text-transform: uppercase;
  color: #00afd0;
}
.status-check .simulation .sim-description {
  font-size: 13px;
  margin-bottom: 20px;
  display: block;
}
.status-check .data-stamp {
  font-size: 13px;
  margin-bottom: 20px;
  display: block;
}
.status-check .data-stamp strong {
  margin-left: 8px;
  font-family: 'unicreditbold';
}
.status-check .data-label {
  color: #00afd0;
  display: block;
}
.status-check.inactive .icon-check {
  background: #cbebea;
}
.status-check.inactive .sim-label {
  color: #cbebea;
}
.status-check.inactive .data-label {
  color: #cbebea;
}
.status-wrap {
  position: relative;
}
.status-wrap .info-message,
.status-wrap .info-message-del {
  position: absolute !important;
  right: 15px;
  top: 0;
}
.status-wrap .info-message i,
.status-wrap .info-message-del i {
  color: #ccc;
}
.status-wrap .info-message i:hover,
.status-wrap .info-message-del i:hover {
  color: #00afd0;
}
.state {
  height: 23px;
  width: 23px;
  display: inline-block;
  border-radius: 50%;
}
.state.red {
  background: #f1978d;
}
.state.green {
  background: #a5d6a7;
}
.state.yellow {
  background: #ffcc80;
}
.state.grey {
  background: #B7B7B7;
}
.empty {
  padding: 0;
  margin: 0;
}
.results span {
  width: 140px;
  float: left;
  display: inline-block;
  line-height: 40px;
  padding-right: 10px;
}
.results .custom-select {
  width: 120px;
  float: left;
}
.box {
  background: #fff;
  border: 1px solid #ddd;
  padding: 20px;
  margin: 10px 0px;
  height: 100%;
  word-wrap: break-word;
}
.box h4 {
  margin-top: 0;
  border-bottom: 1px solid #ddd;
  padding-bottom: 10px;
}
.acc-note-headline a {
  width: 100%;
}
.acc-note-headline .acc-sub-description {
  float: right;
  font-size: 12px;
  line-height: 18px;
}
.acc-note-headline .acc-note-state {
  font-size: 14px;
  text-transform: none;
}
.border-row {
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
  padding: 10px 0px;
}
.border-row span {
  display: inline-block;
  margin-left: 10px;
}
.search-menu {
  cursor: pointer;
}
.close-filters {
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  display: block;
  float: left;
  background: #fff;
  text-decoration: none;
  cursor: pointer;
  text-decoration: none;
}
.close-filters i {
  color: #ccc;
  font-size: 26px;
}
.close-filters:hover {
  text-decoration: none;
}
.close-filters:hover i {
  color: #000;
}
.search-headline {
  line-height: 80px;
  font-size: 25px;
  font-family: 'unicreditmedium';
  color: #000;
}
#sidebar-wrapper .search-content {
  background: #e5e5e5;
  padding: 20px 0px;
  position: fixed;
  top: 80px;
}
#sidebar-wrapper .search-content .search-buttons .btn {
  margin-left: 10px;
  margin-bottom: 5px;
}
#sidebar-wrapper .sidebar-search {
  overflow: hidden;
}
#sidebar-wrapper .sidebar-search .search-wrap {
  overflow: hidden;
  float: left;
  width: 100%;
  padding: 15px;
  max-width: calc(100% - 80px);
  float: left;
  height: 80px;
}
#sidebar-wrapper .sidebar-search .search {
  background: #000;
  height: 50px;
}
#sidebar-wrapper .sidebar-search .search input,
#sidebar-wrapper .sidebar-search .search .input-group-btn {
  height: 50px;
}
#sidebar-wrapper .sidebar-search .search input .btn,
#sidebar-wrapper .sidebar-search .search .input-group-btn .btn {
  height: 50px;
}
#sidebar-wrapper .sidebar-search .btn-filters {
  width: 80px;
  float: left;
  background: transparent;
  border: 0px;
  font-size: 22px;
  line-height: 60px;
  color: #ccc;
  height: 80px;
}
#sidebar-wrapper .sidebar-search .btn-filters:focus,
#sidebar-wrapper .sidebar-search .btn-filters:active,
#sidebar-wrapper .sidebar-search .btn-filters:focus:hover,
#sidebar-wrapper .sidebar-search .btn-filters:hover {
  background: #000;
}
.search-overlay {
  background: rgba(0, 0, 0, 0.63);
  z-index: 10;
  height: calc(100vh - 80px);
  width: 100%;
  position: fixed;
  top: 80px;
  left: 0;
  bottom: 0;
  right: 0;
  left: 80px;
  z-index: 99;
}

.search-form.scrollable {
  max-height: calc(100vh - 80px);
  overflow-y:auto;
}

.search-form {
  width: calc(100% - 80px);
  position: fixed;
  margin-left: 80px;
  z-index: 100;
}
.search-form .search-buttons {
  overflow: hidden;
}
.search-form .search-buttons .btn {
  margin-left: 15px;
}
.search-form .search-content {
  padding: 15px;
  background: whitesmoke;
}
.search-form .search-heading {
  padding: 5px 15px;
  border-bottom: 1px solid #cccccc;
  border-top: 1px solid #cccccc;
}
.search-form .search-heading .btn {
  text-align: left;
  width: 100%;
  font-family: 'unicreditbold';
  font-size: 17px;
  border: 0;
  padding: 0;
  position: relative;
}
.search-form .search-heading .btn:hover,
.search-form .search-heading .btn:active,
.search-form .search-heading .btn:focus,
.search-form .search-heading .btn:focus:hover {
  border: 0;
}
.search-form .search-heading .btn i {
  font-size: 14px;
  position: absolute;
  right: 0;
  color: #00afd0;
}
.search-form .search-form-wrapper {
  background: #fff;
  padding: 0px;
}
.search-form .search-form-wrapper .search-input {
  width: calc(100% - 280px);
  margin-left: 200px;
  position: relative;
}
.search-form .search-form-wrapper .search-input .btn {
  position: absolute;
  right: 6px;
  top: 15px;
}
.search-form .search-form-wrapper .search-input input {
  width: 100%;
  border: 0px;
  font-size: 30px;
  padding: 10px 15px;
}
.search-form .search-form-results {
  width: calc(100% - 280px);
  margin-left: 200px;
  background: #fff;
}
.search-results-amount {
  display: block;
  font-size: 15px;
  padding: 14px 0px;
  color: #333333;
}
.pagination {
  margin: 0;
}
.pagination > li > a,
.pagination > li > span {
  border: 0;
  background: transparent;
  color: #333;
}
.pagination > .active > a,
.pagination > .active > a:focus,
.pagination > .active > a:hover,
.pagination > .active > span,
.pagination > .active > span:focus,
.pagination > .active > span:hover {
  color: #00afd0;
  background: transparent;
}
.pagination > .disabled > a,
.pagination > .disabled > a:focus,
.pagination > .disabled > a:hover,
.pagination > .disabled > span,
.pagination > .disabled > span:focus,
.pagination > .disabled > span:hover {
  color: #ccc;
  background: transparent;
}
.pagination > li > a:focus,
.pagination > li > a:hover,
.pagination > li > span:focus,
.pagination > li > span:hover {
  color: #00afd0;
  background: transparent;
}
.breadcrumbs {
  padding: 10px 0px;
  font-size: 14px;
}
.breadcrumbs a {
  color: #00afd0;
  text-decoration: none;
}
.breadcrumbs a:hover,
.breadcrumbs a:focus {
  color: #009dbb;
}
.breadcrumbs ul {
  list-style: none;
  display: inline-block;
  margin-left: 0px;
  padding-left: 0px;
}
.breadcrumbs ul li {
  display: inline-block;
  padding-right: 10px;
}
.breadcrumbs ul li:after {
  font-family: 'unicredit-icons';
  content: '\f105';
  padding-left: 10px;
  color: #ccc;
}
.breadcrumbs ul li:last-child:after {
  content: '';
}
.custom-chart {
  position: relative;
  height: 120px;
  width: 120px;
  margin: 0 auto;
  margin-top: 15px;
}
.custom-chart .chart {
  position: absolute;
  width: 120px;
  height: 120px;
  margin: 0 auto;
  border-radius: 50%;
}
.doughnutTip {
  position: absolute;
  min-width: 30px;
  max-width: 300px;
  padding: 5px 15px;
  border-radius: 1px;
  background: rgba(0, 0, 0, 0.8);
  color: #ddd;
  font-size: 17px;
  text-shadow: 0 1px 0 #000;
  text-transform: uppercase;
  text-align: center;
  line-height: 1.3;
  letter-spacing: 0.06em;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  pointer-events: none;
}
.doughnutTip::after {
  position: absolute;
  left: 50%;
  bottom: -6px;
  content: '';
  height: 0;
  margin: 0 0 0 -6px;
  border-right: 5px solid transparent;
  border-left: 5px solid transparent;
  border-top: 6px solid rgba(0, 0, 0, 0.7);
  line-height: 0;
}
.doughnutSummary {
  position: absolute;
  color: #000;
  top: 16px;
  text-align: center;
  cursor: default;
  left: 0;
  width: 120px !important;
  right: 0;
  margin: 0 auto !important;
}
.doughnutSummaryNumber {
  position: absolute;
  width: 100%;
  font-size: 45px;
}
.chart path:hover {
  opacity: 0.65;
}
.tickets-label {
  padding-bottom: 20px;
  font-family: 'unicreditmedium';
  padding-top: 10px;
  display: block;
  margin: 0 auto;
}
.tickets-label.ticket-blue {
  color: #388bca;
}
.tickets-label.ticket-lightblue {
  color: #1bacc2;
}
.tickets-label.ticket-purple {
  color: #7757a4;
}
.tickets-label.ticket-green {
  color: #3db049;
}
.tickets-label.ticket-darkgreen {
  color: #0f796b;
}
.tickets-label.ticket-yellow {
  color: #bfcb30;
}
.tickets-label.ticket-blue {
  color: #388bca;
}
.tickets-label.ticket-lightblue {
  color: #1bacc2;
}
.tickets-label.ticket-purple {
  color: #7757a4;
}
.tickets-label.ticket-green {
  color: #3db049;
}
.tickets-label.ticket-darkgreen {
  color: #0f796b;
}
.tickets-label.ticket-yellow {
  color: #bfcb30;
}
.tickets-label.ticket-orange {
  color: #f58523;
}
.tickets-label.ticket-strawberry {
  color: #dd1860;
}
.tickets-label.ticket-lightpurple {
  color: #a33694;
}
.tickets-label.ticket-deepgreen {
  color: #004c3d;
}
.tickets-label.ticket-lightorange {
  color: #faaa18;
}
.tickets-label.ticket-olivegreen {
  color: #9e9f36;
}
.tickets-label.ticket-red {
  color: #e01e25;
}
.chart {
  -webkit-transition: all 0.2s ease;
  -moz-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  -ms-transition: all 0.2s ease;
  transition: all 0.2s ease;
}
.lavorazione .doughnutSummaryNumber {
  color: #338acc;
}
.lavorazione.active .chart {
  background: #82b9e5;
}
.lavorazione.active .doughnutSummaryNumber {
  color: #fff;
}
.lavorazione .chart:hover {
  background: #dfebf6;
  cursor: pointer;
}
.lavorazione .chart:hover .doughnutSummaryNumber {
  color: #338acc;
}
.sospese .doughnutSummaryNumber {
  color: #1bacc2;
}
.sospese.active .chart {
  background: #71ccda;
}
.sospese.active .doughnutSummaryNumber {
  color: #fff;
}
.sospese .chart:hover {
  background: #d9f0f3;
  cursor: pointer;
}
.sospese .chart:hover .doughnutSummaryNumber {
  color: #00acc4;
}
.second-opinion .doughnutSummaryNumber {
  color: #7757a4;
}
.second-opinion.active .chart {
  background: #ac98c8;
}
.second-opinion.active .doughnutSummaryNumber {
  color: #fff;
}
.second-opinion .chart:hover {
  background: #e8e3ef;
  cursor: pointer;
}
.second-opinion .chart:hover .doughnutSummaryNumber {
  color: #7755a6;
}
.third-opinion .doughnutSummaryNumber {
  color: #3db049;
}
.third-opinion.active .chart {
  background: #87cf8f;
}
.third-opinion.active .doughnutSummaryNumber {
  color: #fff;
}
.third-opinion .chart:hover {
  background: #e0f1e1;
  cursor: pointer;
}
.third-opinion .chart:hover .doughnutSummaryNumber {
  color: #3db049;
}
.concluse .doughnutSummaryNumber {
  color: #0f796b;
}
.concluse.active .chart {
  background: #6cada5;
}
.concluse.active .doughnutSummaryNumber {
  color: #fff;
}
.concluse .chart:hover {
  background: #d9e8e6;
  cursor: pointer;
}
.concluse .chart:hover .doughnutSummaryNumber {
  color: #00796b;
}
.pratiche .doughnutSummaryNumber {
  color: #bfcb30;
}
.pratiche.active .chart {
  background: #d7e07c;
}
.pratiche.active .doughnutSummaryNumber {
  color: #fff;
}
.pratiche .chart:hover {
  background: #f3f5dd;
  cursor: pointer;
}
.pratiche .chart:hover .doughnutSummaryNumber {
  color: #bfcd14;
}
.annullata .doughnutSummaryNumber {
  color: #f58523;
}
.annullata .donut:before,
.annullata .donut:after {
  background: #f58524;
}
.annullata.active .chart,
.annullata.active .donut-center {
  background: #f8b57a;
}
.annullata.active .doughnutSummaryNumber,
.annullata.active .donut-count {
  color: #fff;
}
.annullata .chart:hover,
.annullata .donut-center:hover {
  background: #fbeadc;
  cursor: pointer;
}
.annullata .chart:hover .doughnutSummaryNumber,
.annullata .donut-center:hover .doughnutSummaryNumber {
  color: #f58523;
}
.annullata .donut:hover .donut-count {
  color: #f58523;
}
.annullata .donut:hover .donut-center {
  background: #fbeadc;
}
.attiva .doughnutSummaryNumber {
  color: #dd1860;
}
.attiva.active .chart {
  background: #e9739e;
}
.attiva.active .doughnutSummaryNumber {
  color: #fff;
}
.attiva .chart:hover {
  background: #f7dae5;
  cursor: pointer;
}
.attiva .chart:hover .doughnutSummaryNumber {
  color: #dd1860;
}
.richiesta .doughnutSummaryNumber {
  color: #a33694;
}
.richiesta.active .chart {
  background: #c785be;
}
.richiesta.active .doughnutSummaryNumber {
  color: #fff;
}
.richiesta .chart:hover {
  background: #efdeed;
  cursor: pointer;
}
.richiesta .chart:hover .doughnutSummaryNumber {
  color: #a33694;
}
.richiesta-perizia .doughnutSummaryNumber {
  color: #faaa18;
}
.richiesta-perizia.active .chart {
  background: #fbcb73;
}
.richiesta-perizia.active .doughnutSummaryNumber {
  color: #fff;
}
.richiesta-perizia .chart:hover {
  background: #fcf0da;
  cursor: pointer;
}
.richiesta-perizia .chart:hover .doughnutSummaryNumber {
  color: #faaa18;
}
.terminata .doughnutSummaryNumber {
  color: #004c3d;
}
.terminata.active .chart {
  background: #65948b;
}
.terminata.active .doughnutSummaryNumber {
  color: #fff;
}
.terminata .chart:hover {
  background: #d6e2e0;
  cursor: pointer;
}
.terminata .chart:hover .doughnutSummaryNumber {
  color: #004c3d;
}
.riaperta .doughnutSummaryNumber {
  color: #9e9f36;
}
.riaperta.active .chart {
  background: #c4c485;
}
.riaperta.active .doughnutSummaryNumber {
  color: #fff;
}
.riaperta .chart:hover {
  background: #eeeede;
  cursor: pointer;
}
.riaperta .chart:hover .doughnutSummaryNumber {
  color: #9e9f36;
}
.first-opinion .doughnutSummaryNumber {
  color: #e01e25;
}
.first-opinion.active .chart {
  background: #eb777b;
}
.first-opinion.active .doughnutSummaryNumber {
  color: #fff;
}
.first-opinion .chart:hover {
  background: #f8dbdc;
  cursor: pointer;
}
.first-opinion .chart:hover .doughnutSummaryNumber {
  color: #e01e25;
}
.timeline-btn {
  width: 45px;
  height: 45px;
  background: transparent;
  border: 3px solid #b2e7f1;
  border-radius: 50%;
  font-size: 26px;
  padding: 0;
  line-height: 30px;
  margin-top: 40px;
}
.timeline-btn:active,
.timeline-btn:hover,
.timeline-btn:focus {
  border: 3px solid #00afd0;
}
.timeline-btn:active i,
.timeline-btn:hover i,
.timeline-btn:focus i {
  color: #00afd0;
}
.timeline-btn.timeline-right.active {
  border: 3px solid #00afd0;
}
.timeline-btn.timeline-right.active i {
  color: #00afd0;
}
.timeline-btn i {
  color: #b2e7f1;
  margin-top: -3px;
  display: block;
}
.timeline {
  overflow: hidden;
  height: 150px;
  display: inline-block;
}
.timeline .timeline-bar {
  position: relative;
  width: calc(100% - 90px);
  float: left;
}
.timeline .timeline-bar .timeline-progress {
  height: 6px;
  display: block;
  background: #cccccc;
  position: absolute;
  top: 60px;
  left: 50px;
  right: 66px;
}
.timeline .timeline-bar .timeline-event {
  width: 150px;
  float: left;
  position: relative;
  height: 150px;
}
.timeline .timeline-bar .timeline-event:last-child {
  width: 100px;
}
.timeline .timeline-bar .timeline-event .timeline-description {
  position: absolute;
  left: 15px;
  color: #666;
  text-align: center;
  font-size: 12px;
}
.timeline
  .timeline-bar
  .timeline-event
  .timeline-description.timeline-description-up {
  top: 0;
}
.timeline
  .timeline-bar
  .timeline-event
  .timeline-description.timeline-description-down {
  top: 80px;
}
.timeline .timeline-bar .timeline-event .timeline-description label {
  color: #666;
  font-family: 'unicreditmedium';
  margin-bottom: 0;
  font-size: 14px;
}
.timeline .timeline-bar .timeline-event .event-attach {
  width: 22px;
  height: 22px;
  display: block;
  background: #fff;
  border: 3px solid #ccc;
  border-radius: 50%;
  position: absolute;
  left: 33px;
  top: 52px;
}
.timeline .timeline-bar .timeline-event .event-bar {
  display: block;
  width: 100%;
  height: 6px;
  top: 60px;
  position: absolute;
  left: 48px;
}
.timeline .timeline-bar .timeline-event.timeline-event-passed .event-attach {
  background: #ccecea;
  border: 3px solid #ccecea;
}
.timeline .timeline-bar .timeline-event.timeline-event-passed .event-bar {
  background: #ccecea;
}
.timeline
  .timeline-bar
  .timeline-event.timeline-event-passed
  .timeline-description {
  color: #00a197;
}
.timeline
  .timeline-bar
  .timeline-event.timeline-event-passed
  .timeline-description
  label {
  color: #00a197;
}
.timeline .timeline-bar .timeline-event.timeline-event-current .event-attach {
  background: #00afd0;
  border: 2px solid #00afd0;
  border-radius: 0;
  width: 5px;
  left: 43px;
}
.timeline
  .timeline-bar
  .timeline-event.timeline-event-current
  .timeline-description {
  color: #00afd0;
}
.timeline
  .timeline-bar
  .timeline-event.timeline-event-current
  .timeline-description
  label {
  color: #00afd0;
}
footer {
  position: fixed;
  bottom: 0;
  left: 80px;
  padding: 20px 25px;
  background: #fff;
  border-top: 1px solid #e4e4e4;
  width: calc(100% - 80px);
  z-index: 97;
}
@media (max-width: 992px) {
  footer {
    z-index: 100;
    left: 0;
    width: 100%;
  }
}
footer.opened {
  width: calc(100% - 280px);
  left: 280px;
}
@media (max-width: 992px) {
  footer.opened {
    width: 100%;
    left: 0;
  }
}
#wizard-progress {
  text-align: right;
  padding: 20px 0;
  color: #333333;
  position: relative;
  overflow: hidden;
}
#wizard-progress.home-wizard {
  float: none;
  margin: 0 auto;
  max-width: 600px;
  overflow: hidden;
}
#wizard-progress .btn {
  margin-left: 12px;
}
#wizard-progress .page {
  display: inline-block;
  float: left;
  text-align: center;
  font-family: 'unicreditmedium';
  min-width: 100px;
  max-width: 100px;
  padding: 0px 8px;
  font-size: 13px;
  text-transform: uppercase;
  position: relative;
}
#wizard-progress .page a {
  display: block;
  text-decoration: none;
  color: #00a197;
}
#wizard-progress .page p {
  text-align: center;
  margin: 5px 0px 0px 0px;
}
#wizard-progress .page .progress-badge {
  background: #e4e4e4;
  border: 1px solid #e4e4e4;
  z-index: 10;
  position: relative;
  font-size: 18px;
  font-family: 'unicreditbold';
}
#wizard-progress .page .progress-badge .icon-check {
  color: #fff;
}
#wizard-progress .page.complete {
  cursor: pointer;
}
#wizard-progress .page.complete .progress-badge {
  background: #ccecea;
  border: 1px solid #ccecea;
  color: #00a197;
}
#wizard-progress .page.complete p {
  color: #00a197;
}
#wizard-progress .page.complete .sep {
  background: #ccecea;
}
#wizard-progress .page.incomplete .progress-badge {
  background: #ccc;
  border: 1px solid #ccc;
}
#wizard-progress .page.incomplete a {
  color: #fff;
}
#wizard-progress .page.incomplete p {
  color: #666;
}
#wizard-progress .page.incomplete .sep {
  background: #ccc;
}
#wizard-progress .page.current {
  color: #00afd0;
  cursor: pointer;
}
#wizard-progress .page.current a {
  color: #00afd0;
}
#wizard-progress .page.current .progress-badge {
  background: #00afd0;
  color: #fff;
  border: 1px solid #00afd0;
}
#wizard-progress .page.current .sep {
  background: #cccccc;
}
#wizard-progress .page.current.complete .progress-badge {
  background: #00a197;
  border: 1px solid #00a197;
}
#wizard-progress .page.current.complete .sep {
  background: #ccecea;
}
#wizard-progress .page.current.incomplete .progress-badge {
  background: #ccc;
  border: 1px solid #ccc;
}
#wizard-progress .page.current.incomplete a {
  color: #666;
}
#wizard-progress .page.current.incomplete .sep {
  background: #aa1c0d;
}
#wizard-progress .progress-badge {
  width: 36px;
  height: 36px;
  display: inline-block;
  background: whitesmoke;
  border: 1px solid #cccccc;
  text-align: center;
  line-height: 36px;
  border-radius: 50%;
}
.interactive-tabs {
  overflow: hidden;
}
.interactive-tabs .base-wrapper {
  background: #f3f3f3;
  padding: 20px;
}
.interactive-tabs .base-wrapper .panel-group {
  margin-bottom: 0;
}
.interactive-tabs .inner-base-wrapper {
  background: #fff;
  margin: 0px;
  padding: 15px 0px;
  height: 500px;
  overflow-y: auto;
}
.interactive-tabs .inner-base-wrapper .panel-body {
  background: #f9f9f9;
}
.interactive-tabs .nav-controls {
  margin: 0;
  height: 44px;
}
.interactive-tabs .nav-controls .dropdown {
  padding: 0;
}
.interactive-tabs .nav-controls .dropdown .btn {
  background: transparent;
  border: 0;
  padding: 10px;
}
.interactive-tabs .nav-controls .btn {
  width: 44px;
  height: 44px;
  background: #f8f8f8;
  border-radius: 0;
  position: relative;
  opacity: 1;
}
.interactive-tabs .nav-controls .btn[disabled] {
  opacity: 1 !important;
  color: #ccc;
}
.interactive-tabs .nav-controls .btn-plus {
  padding: 0;
  width: 25px;
  background: #e6e6e6;
  border-radius: 0;
}
.interactive-tabs .nav-controls .nav-tabs {
  max-width: initial;
  border: 0;
  white-space: nowrap;
  height: 44px;
  display: inline;
  position: relative;
}
.interactive-tabs .nav-controls .nav-tabs > li {
  display: inline-block !important;
  float: none !important;
}
.interactive-tabs .nav-controls .nav-tabs > li.active > a,
.interactive-tabs .nav-controls .nav-tabs > li.active > a:focus,
.interactive-tabs .nav-controls .nav-tabs > li.active > a:hover {
  background-color: #f3f3f3;
  border: 1px solid #f3f3f3;
}
.interactive-tabs .nav-controls .nav-tabs a {
  position: relative;
  padding-right: 35px;
  background: #f8f8f8;
  border-radius: 0;
  color: #868686;
}
.interactive-tabs .nav-controls .nav-tabs a .state {
  height: 15px;
  width: 15px;
  top: 15px;
  right: 15px;
  position: absolute;
}
.wrapped {
  max-width: calc(100% - 157px);
  float: left;
}
.insert-document {
  display: block;
  text-align: center;
  border: 2px dashed #ddd;
  color: #999;
  padding: 20px;
  margin: 10px;
}
.insert-document span {
  display: inline-block;
  line-height: 20px;
}
.insert-document span i {
  font-size: 30px;
  display: inline-block;
  float: left;
  margin-right: 10px;
}
.ui-sortable-handle {
  background: #ccecea;
  cursor: pointer;
}
.ui-sortable-handle:hover {
  background: #ccecea !important;
}
.ui-droppable-hover .insert-document {
  border-color: #00a197;
  color: #00a197;
}
.donut-chart {
  padding: 20px 0px;
}
.data-headline {
  font-size: 30px;
  text-transform: uppercase;
  margin-bottom: 0;
}
.data-headline.disabled {
  color: #ccc;
}
.data-sum {
  font-size: 25px;
}
.data-sum.active {
  color: #00afd0;
}
.data-sum.disabled {
  color: #ccc;
}
.data-chart {
  padding-top: 20px;
}
.data-chart .data-list {
  list-style: none;
  margin-left: 0;
  padding-left: 0;
}
.data-chart .data-list li {
  border-bottom: 1px solid #ddd;
  padding: 12px 0px;
  cursor: pointer;
}
.data-chart .data-list li:hover {
  color: #00afd0;
}
.data-chart .data-list li.active {
  color: #00afd0;
  font-family: 'unicreditmedium';
}
.data-chart .data-list li span {
  float: right;
}
.data-chart .data-list li:first-child {
  border-top: 1px solid #ddd;
}
.data-chart h4 {
  text-transform: uppercase;
}
.asset-choice .custom-radio label {
  margin-bottom: 0;
  padding-bottom: 0;
  margin-top: 6px;
}
.donut-count {
  font: 0.3em/120em 'Proxima Nova', Helvetica, Arial, sans-serif;
  position: absolute;
  color: #ffdb75;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 6;
  font-size: 42px;
}
.donut {
  width: 120px;
  height: 120px;
  font-size: 120px;
  position: relative;
  overflow: hidden;
  display: table;
  display: inline-block;
  margin: 0 10px;
}
.donut:before,
.donut:after {
  content: '';
  background: #ffdb75;
  position: absolute;
  display: block;
  width: 50%;
  height: 100%;
  transform-origin: 100% 50%;
  border-radius: 100% 0 0 100% / 50% 0 0 50%;
}
.donut:before {
  z-index: 2;
  border-right: none;
}
.donut:after {
  border-left: none;
  z-index: 3;
  transform: rotate(180deg);
  opacity: 0;
}
.donut-center {
  background: #000;
  width: 85%;
  height: 85%;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 4;
  border-radius: 100%;
}
.donut-mask {
  width: 100%;
  height: 100%;
  position: absolute;
}
.donut-mask:before,
.donut-mask:after {
  content: '';
  background: #fff;
  position: absolute;
  display: block;
  width: 50%;
  height: 100%;
  transform-origin: 100% 50%;
  border-radius: 100% 0 0 100% / 50% 0 0 50%;
}
.donut-mask:before {
  border-right: none;
  z-index: 3;
}
.donut-mask:after {
  border-left: none;
  transform: rotate(180deg);
  z-index: 1;
}
.donut--1:before {
  animation: rota1-1 0.8s forwards;
}
@keyframes rota1-1 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(3.6deg);
  }
}
.donut--2:before {
  animation: rota1-2 0.8s forwards;
}
@keyframes rota1-2 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(7.2deg);
  }
}
.donut--3:before {
  animation: rota1-3 0.8s forwards;
}
@keyframes rota1-3 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(10.8deg);
  }
}
.donut--4:before {
  animation: rota1-4 0.8s forwards;
}
@keyframes rota1-4 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(14.4deg);
  }
}
.donut--5:before {
  animation: rota1-5 0.8s forwards;
}
@keyframes rota1-5 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(18deg);
  }
}
.donut--6:before {
  animation: rota1-6 0.8s forwards;
}
@keyframes rota1-6 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(21.6deg);
  }
}
.donut--7:before {
  animation: rota1-7 0.8s forwards;
}
@keyframes rota1-7 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(25.2deg);
  }
}
.donut--8:before {
  animation: rota1-8 0.8s forwards;
}
@keyframes rota1-8 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(28.8deg);
  }
}
.donut--9:before {
  animation: rota1-9 0.8s forwards;
}
@keyframes rota1-9 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(32.4deg);
  }
}
.donut--10:before {
  animation: rota1-10 0.8s forwards;
}
@keyframes rota1-10 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(36deg);
  }
}
.donut--11:before {
  animation: rota1-11 0.8s forwards;
}
@keyframes rota1-11 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(39.6deg);
  }
}
.donut--12:before {
  animation: rota1-12 0.8s forwards;
}
@keyframes rota1-12 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(43.2deg);
  }
}
.donut--13:before {
  animation: rota1-13 0.8s forwards;
}
@keyframes rota1-13 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(46.8deg);
  }
}
.donut--14:before {
  animation: rota1-14 0.8s forwards;
}
@keyframes rota1-14 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(50.4deg);
  }
}
.donut--15:before {
  animation: rota1-15 0.8s forwards;
}
@keyframes rota1-15 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(54deg);
  }
}
.donut--16:before {
  animation: rota1-16 0.8s forwards;
}
@keyframes rota1-16 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(57.6deg);
  }
}
.donut--17:before {
  animation: rota1-17 0.8s forwards;
}
@keyframes rota1-17 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(61.2deg);
  }
}
.donut--18:before {
  animation: rota1-18 0.8s forwards;
}
@keyframes rota1-18 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(64.8deg);
  }
}
.donut--19:before {
  animation: rota1-19 0.8s forwards;
}
@keyframes rota1-19 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(68.4deg);
  }
}
.donut--20:before {
  animation: rota1-20 0.8s forwards;
}
@keyframes rota1-20 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(72deg);
  }
}
.donut--21:before {
  animation: rota1-21 0.8s forwards;
}
@keyframes rota1-21 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(75.6deg);
  }
}
.donut--22:before {
  animation: rota1-22 0.8s forwards;
}
@keyframes rota1-22 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(79.2deg);
  }
}
.donut--23:before {
  animation: rota1-23 0.8s forwards;
}
@keyframes rota1-23 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(82.8deg);
  }
}
.donut--24:before {
  animation: rota1-24 0.8s forwards;
}
@keyframes rota1-24 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(86.4deg);
  }
}
.donut--25:before {
  animation: rota1-25 0.8s forwards;
}
@keyframes rota1-25 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(90deg);
  }
}
.donut--26:before {
  animation: rota1-26 0.8s forwards;
}
@keyframes rota1-26 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(93.6deg);
  }
}
.donut--27:before {
  animation: rota1-27 0.8s forwards;
}
@keyframes rota1-27 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(97.2deg);
  }
}
.donut--28:before {
  animation: rota1-28 0.8s forwards;
}
@keyframes rota1-28 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(100.8deg);
  }
}
.donut--29:before {
  animation: rota1-29 0.8s forwards;
}
@keyframes rota1-29 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(104.4deg);
  }
}
.donut--30:before {
  animation: rota1-30 0.8s forwards;
}
@keyframes rota1-30 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(108deg);
  }
}
.donut--31:before {
  animation: rota1-31 0.8s forwards;
}
@keyframes rota1-31 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(111.6deg);
  }
}
.donut--32:before {
  animation: rota1-32 0.8s forwards;
}
@keyframes rota1-32 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(115.2deg);
  }
}
.donut--33:before {
  animation: rota1-33 0.8s forwards;
}
@keyframes rota1-33 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(118.8deg);
  }
}
.donut--34:before {
  animation: rota1-34 0.8s forwards;
}
@keyframes rota1-34 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(122.4deg);
  }
}
.donut--35:before {
  animation: rota1-35 0.8s forwards;
}
@keyframes rota1-35 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(126deg);
  }
}
.donut--36:before {
  animation: rota1-36 0.8s forwards;
}
@keyframes rota1-36 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(129.6deg);
  }
}

.donut--37:before {
  animation: rota1-37 0.8s forwards;
}
@keyframes rota1-37 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(133.2deg);
  }
}

.donut--38:before {
  animation: rota1-38 0.8s forwards;
}
@keyframes rota1-38 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(136.8deg);
  }
}

.donut--39:before {
  animation: rota1-39 0.8s forwards;
}
@keyframes rota1-39 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(140.4deg);
  }
}

.donut--40:before {
  animation: rota1-40 0.8s forwards;
}
@keyframes rota1-40 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(144deg);
  }
}

.donut--41:before {
  animation: rota1-41 0.8s forwards;
}
@keyframes rota1-41 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(147.6deg);
  }
}

.donut--42:before {
  animation: rota1-42 0.8s forwards;
}
@keyframes rota1-42 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(151.2deg);
  }
}

.donut--43:before {
  animation: rota1-43 0.8s forwards;
}
@keyframes rota1-43 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(154.8deg);
  }
}

.donut--44:before {
  animation: rota1-44 0.8s forwards;
}
@keyframes rota1-44 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(158.4deg);
  }
}

.donut--45:before {
  animation: rota1-45 0.8s forwards;
}
@keyframes rota1-45 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(162deg);
  }
}

.donut--46:before {
  animation: rota1-46 0.8s forwards;
}
@keyframes rota1-46 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(165.6deg);
  }
}

.donut--47:before {
  animation: rota1-47 0.8s forwards;
}
@keyframes rota1-47 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(169.2deg);
  }
}

.donut--48:before {
  animation: rota1-48 0.8s forwards;
}
@keyframes rota1-48 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(172.8deg);
  }
}

.donut--49:before {
  animation: rota1-49 0.8s forwards;
}
@keyframes rota1-49 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(176.4deg);
  }
}

.donut--50:before {
  animation: rota1-50 0.8s forwards;
}
.donut--50:after {
  animation: rota2-50 0s forwards 0.8s;
}
@keyframes rota1-50 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-50 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(180deg);
    opacity: 1;
  }
}
.donut--51:before {
  animation: rota1-51 0.8s forwards;
}
.donut--51:after {
  animation: rota2-51 0.016s forwards 0.8s;
}
@keyframes rota1-51 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-51 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(183.6deg);
    opacity: 1;
  }
}
.donut--52:before {
  animation: rota1-52 0.8s forwards;
}
.donut--52:after {
  animation: rota2-52 0.032s forwards 0.8s;
}
@keyframes rota1-52 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-52 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(187.2deg);
    opacity: 1;
  }
}
.donut--53:before {
  animation: rota1-53 0.8s forwards;
}
.donut--53:after {
  animation: rota2-53 0.048s forwards 0.8s;
}
@keyframes rota1-53 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-53 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(190.8deg);
    opacity: 1;
  }
}
.donut--54:before {
  animation: rota1-54 0.8s forwards;
}
.donut--54:after {
  animation: rota2-54 0.064s forwards 0.8s;
}
@keyframes rota1-54 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-54 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(194.4deg);
    opacity: 1;
  }
}
.donut--55:before {
  animation: rota1-55 0.8s forwards;
}
.donut--55:after {
  animation: rota2-55 0.08s forwards 0.8s;
}
@keyframes rota1-55 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-55 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(198deg);
    opacity: 1;
  }
}
.donut--56:before {
  animation: rota1-56 0.8s forwards;
}
.donut--56:after {
  animation: rota2-56 0.096s forwards 0.8s;
}
@keyframes rota1-56 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-56 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(201.6deg);
    opacity: 1;
  }
}
.donut--57:before {
  animation: rota1-57 0.8s forwards;
}
.donut--57:after {
  animation: rota2-57 0.112s forwards 0.8s;
}
@keyframes rota1-57 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-57 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(205.2deg);
    opacity: 1;
  }
}
.donut--58:before {
  animation: rota1-58 0.8s forwards;
}
.donut--58:after {
  animation: rota2-58 0.128s forwards 0.8s;
}
@keyframes rota1-58 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-58 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(208.8deg);
    opacity: 1;
  }
}
.donut--59:before {
  animation: rota1-59 0.8s forwards;
}
.donut--59:after {
  animation: rota2-59 0.144s forwards 0.8s;
}
@keyframes rota1-59 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-59 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(212.4deg);
    opacity: 1;
  }
}
.donut--60:before {
  animation: rota1-60 0.8s forwards;
}
.donut--60:after {
  animation: rota2-60 0.16s forwards 0.8s;
}
@keyframes rota1-60 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-60 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(216deg);
    opacity: 1;
  }
}
.donut--61:before {
  animation: rota1-61 0.8s forwards;
}
.donut--61:after {
  animation: rota2-61 0.176s forwards 0.8s;
}
@keyframes rota1-61 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-61 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(219.6deg);
    opacity: 1;
  }
}
.donut--62:before {
  animation: rota1-62 0.8s forwards;
}
.donut--62:after {
  animation: rota2-62 0.192s forwards 0.8s;
}
@keyframes rota1-62 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-62 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(223.2deg);
    opacity: 1;
  }
}
.donut--63:before {
  animation: rota1-63 0.8s forwards;
}
.donut--63:after {
  animation: rota2-63 0.208s forwards 0.8s;
}
@keyframes rota1-63 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-63 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(226.8deg);
    opacity: 1;
  }
}
.donut--64:before {
  animation: rota1-64 0.8s forwards;
}
.donut--64:after {
  animation: rota2-64 0.224s forwards 0.8s;
}
@keyframes rota1-64 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-64 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(230.4deg);
    opacity: 1;
  }
}
.donut--65:before {
  animation: rota1-65 0.8s forwards;
}
.donut--65:after {
  animation: rota2-65 0.24s forwards 0.8s;
}
@keyframes rota1-65 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-65 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(234deg);
    opacity: 1;
  }
}
.donut--66:before {
  animation: rota1-66 0.8s forwards;
}
.donut--66:after {
  animation: rota2-66 0.256s forwards 0.8s;
}
@keyframes rota1-66 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-66 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(237.6deg);
    opacity: 1;
  }
}
.donut--67:before {
  animation: rota1-67 0.8s forwards;
}
.donut--67:after {
  animation: rota2-67 0.272s forwards 0.8s;
}
@keyframes rota1-67 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-67 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(241.2deg);
    opacity: 1;
  }
}
.donut--68:before {
  animation: rota1-68 0.8s forwards;
}
.donut--68:after {
  animation: rota2-68 0.288s forwards 0.8s;
}
@keyframes rota1-68 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-68 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(244.8deg);
    opacity: 1;
  }
}
.donut--69:before {
  animation: rota1-69 0.8s forwards;
}
.donut--69:after {
  animation: rota2-69 0.304s forwards 0.8s;
}
@keyframes rota1-69 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-69 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(248.4deg);
    opacity: 1;
  }
}
.donut--70:before {
  animation: rota1-70 0.8s forwards;
}
.donut--70:after {
  animation: rota2-70 0.32s forwards 0.8s;
}
@keyframes rota1-70 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-70 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(252deg);
    opacity: 1;
  }
}
.donut--71:before {
  animation: rota1-71 0.8s forwards;
}
.donut--71:after {
  animation: rota2-71 0.336s forwards 0.8s;
}
@keyframes rota1-71 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-71 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(255.6deg);
    opacity: 1;
  }
}
.donut--72:before {
  animation: rota1-72 0.8s forwards;
}
.donut--72:after {
  animation: rota2-72 0.352s forwards 0.8s;
}
@keyframes rota1-72 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-72 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(259.2deg);
    opacity: 1;
  }
}
.donut--73:before {
  animation: rota1-73 0.8s forwards;
}
.donut--73:after {
  animation: rota2-73 0.368s forwards 0.8s;
}
@keyframes rota1-73 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-73 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(262.8deg);
    opacity: 1;
  }
}
.donut--74:before {
  animation: rota1-74 0.8s forwards;
}
.donut--74:after {
  animation: rota2-74 0.384s forwards 0.8s;
}
@keyframes rota1-74 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-74 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(266.4deg);
    opacity: 1;
  }
}
.donut--75:before {
  animation: rota1-75 0.8s forwards;
}
.donut--75:after {
  animation: rota2-75 0.4s forwards 0.8s;
}
@keyframes rota1-75 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-75 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(270deg);
    opacity: 1;
  }
}
.donut--76:before {
  animation: rota1-76 0.8s forwards;
}
.donut--76:after {
  animation: rota2-76 0.416s forwards 0.8s;
}
@keyframes rota1-76 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-76 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(273.6deg);
    opacity: 1;
  }
}
.donut--77:before {
  animation: rota1-77 0.8s forwards;
}
.donut--77:after {
  animation: rota2-77 0.432s forwards 0.8s;
}
@keyframes rota1-77 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-77 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(277.2deg);
    opacity: 1;
  }
}
.donut--78:before {
  animation: rota1-78 0.8s forwards;
}
.donut--78:after {
  animation: rota2-78 0.448s forwards 0.8s;
}
@keyframes rota1-78 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-78 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(280.8deg);
    opacity: 1;
  }
}
.donut--79:before {
  animation: rota1-79 0.8s forwards;
}
.donut--79:after {
  animation: rota2-79 0.464s forwards 0.8s;
}
@keyframes rota1-79 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-79 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(284.4deg);
    opacity: 1;
  }
}
.donut--80:before {
  animation: rota1-80 0.8s forwards;
}
.donut--80:after {
  animation: rota2-80 0.48s forwards 0.8s;
}
@keyframes rota1-80 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-80 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(288deg);
    opacity: 1;
  }
}
.donut--81:before {
  animation: rota1-81 0.8s forwards;
}
.donut--81:after {
  animation: rota2-81 0.496s forwards 0.8s;
}
@keyframes rota1-81 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-81 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(291.6deg);
    opacity: 1;
  }
}
.donut--82:before {
  animation: rota1-82 0.8s forwards;
}
.donut--82:after {
  animation: rota2-82 0.512s forwards 0.8s;
}
@keyframes rota1-82 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-82 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(295.2deg);
    opacity: 1;
  }
}
.donut--83:before {
  animation: rota1-83 0.8s forwards;
}
.donut--83:after {
  animation: rota2-83 0.528s forwards 0.8s;
}
@keyframes rota1-83 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-83 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(298.8deg);
    opacity: 1;
  }
}
.donut--84:before {
  animation: rota1-84 0.8s forwards;
}
.donut--84:after {
  animation: rota2-84 0.544s forwards 0.8s;
}
@keyframes rota1-84 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-84 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(302.4deg);
    opacity: 1;
  }
}
.donut--85:before {
  animation: rota1-85 0.8s forwards;
}
.donut--85:after {
  animation: rota2-85 0.56s forwards 0.8s;
}
@keyframes rota1-85 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-85 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(306deg);
    opacity: 1;
  }
}
.donut--86:before {
  animation: rota1-86 0.8s forwards;
}
.donut--86:after {
  animation: rota2-86 0.576s forwards 0.8s;
}
@keyframes rota1-86 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-86 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(309.6deg);
    opacity: 1;
  }
}
.donut--87:before {
  animation: rota1-87 0.8s forwards;
}
.donut--87:after {
  animation: rota2-87 0.592s forwards 0.8s;
}
@keyframes rota1-87 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-87 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(313.2deg);
    opacity: 1;
  }
}
.donut--88:before {
  animation: rota1-88 0.8s forwards;
}
.donut--88:after {
  animation: rota2-88 0.608s forwards 0.8s;
}
@keyframes rota1-88 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-88 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(316.8deg);
    opacity: 1;
  }
}
.donut--89:before {
  animation: rota1-89 0.8s forwards;
}
.donut--89:after {
  animation: rota2-89 0.624s forwards 0.8s;
}
@keyframes rota1-89 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-89 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(320.4deg);
    opacity: 1;
  }
}
.donut--90:before {
  animation: rota1-90 0.8s forwards;
}
.donut--90:after {
  animation: rota2-90 0.64s forwards 0.8s;
}
@keyframes rota1-90 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-90 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(324deg);
    opacity: 1;
  }
}
.donut--91:before {
  animation: rota1-91 0.8s forwards;
}
.donut--91:after {
  animation: rota2-91 0.656s forwards 0.8s;
}
@keyframes rota1-91 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-91 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(327.6deg);
    opacity: 1;
  }
}
.donut--92:before {
  animation: rota1-92 0.8s forwards;
}
.donut--92:after {
  animation: rota2-92 0.672s forwards 0.8s;
}
@keyframes rota1-92 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-92 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(331.2deg);
    opacity: 1;
  }
}
.donut--93:before {
  animation: rota1-93 0.8s forwards;
}
.donut--93:after {
  animation: rota2-93 0.688s forwards 0.8s;
}
@keyframes rota1-93 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-93 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(334.8deg);
    opacity: 1;
  }
}
.donut--94:before {
  animation: rota1-94 0.8s forwards;
}
.donut--94:after {
  animation: rota2-94 0.704s forwards 0.8s;
}
@keyframes rota1-94 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-94 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(338.4deg);
    opacity: 1;
  }
}
.donut--95:before {
  animation: rota1-95 0.8s forwards;
}
.donut--95:after {
  animation: rota2-95 0.72s forwards 0.8s;
}
@keyframes rota1-95 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-95 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(342deg);
    opacity: 1;
  }
}
.donut--96:before {
  animation: rota1-96 0.8s forwards;
}
.donut--96:after {
  animation: rota2-96 0.736s forwards 0.8s;
}
@keyframes rota1-96 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-96 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(345.6deg);
    opacity: 1;
  }
}
.donut--97:before {
  animation: rota1-97 0.8s forwards;
}
.donut--97:after {
  animation: rota2-97 0.752s forwards 0.8s;
}
@keyframes rota1-97 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-97 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(349.2deg);
    opacity: 1;
  }
}
.donut--98:before {
  animation: rota1-98 0.8s forwards;
}
.donut--98:after {
  animation: rota2-98 0.768s forwards 0.8s;
}
@keyframes rota1-98 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-98 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(352.8deg);
    opacity: 1;
  }
}
.donut--99:before {
  animation: rota1-99 0.8s forwards;
}
.donut--99:after {
  animation: rota2-99 0.784s forwards 0.8s;
}
@keyframes rota1-99 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-99 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(356.4deg);
    opacity: 1;
  }
}
.donut--100:before {
  animation: rota1-100 0.8s forwards;
}
.donut--100:after {
  animation: rota2-100 0.8s forwards 0.8s;
}
@keyframes rota1-100 {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(180deg);
  }
}
@keyframes rota2-100 {
  0% {
    transform: rotate(180deg);
    opacity: 1;
  }
  100% {
    transform: rotate(360deg);
    opacity: 1;
  }
}

.accordion-lable-detail-text {
  color: #999;
  text-transform: lowercase;
}

.align-top {
  vertical-align: top;
}

.align-middle {
  vertical-align: middle;
}

.align-baseline {
  vertical-align: baseline;
}

.align-bottom {
  vertical-align: bottom;
}