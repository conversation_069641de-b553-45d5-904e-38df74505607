<fieldset [disabled]="!landingService.positionLocked">
<ng-container *ngIf="wizardCode === 'WSIM'">
<fieldset [disabled]="landingService.isLockedTask[currentTask]">
  <div class="row">
    <div class="col-sm-12">
      <h3>{{'UBZ.SITE_CONTENT.1011101' | translate }}</h3>
    </div>
    <div class="col-sm-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.1001110' | translate }}</label>
      {{simulation.ndg}}
    </div>
    <div class="col-sm-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.1011110' | translate }}</label>
      {{domainAssetType.translationCod | translate}}
    </div>
    <div class="col-sm-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.1011111' | translate }}</label>
      {{simulation.heading}}
    </div>
    <div class="col-sm-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.1101110' | translate }}</label>
      {{simulation.credlineAmount | currency:'EUR':true:'1.2-2'}}
    </div>
  </div>

  <div class="row">
    <div class="col-sm-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.1101111' | translate }}</label>
      {{domainMacroProcess.translationCod | translate}}
    </div>
    <div class="col-sm-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.1110000' | translate }}</label>
      {{domainStructureType.translationCod | translate}}
    </div>
    <div class="col-sm-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.1110001' | translate }}</label>
      {{domainScopeType.translationCod | translate}}
    </div>
    <div class="col-sm-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.1110010' | translate }}</label>
      {{domainAppraisalType.translationCod | translate}}
    </div>
  </div>

  <div class="row">
    <div class="col-sm-12">
        <h3>{{'UBZ.SITE_CONTENT.1110011' | translate }}</h3>
    </div>
  </div>
  <div class="row">
    <div class="col-sm-12">
      <table class="uc-table">
        <thead>
          <tr>
            <th scope="col" class="col-sm-10">{{'UBZ.SITE_CONTENT.1110100' | translate }}</th>
            <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1110101' | translate }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let doc of documents; let i = index" class="invalid no-expert" data-index="1">
            <td attr.data-label="{{'UBZ.SITE_CONTENT.10010110' | translate }}">{{doc.categoryDesc}}</td>
            <td attr.data-label="{{'UBZ.SITE_CONTENT.111110' | translate }}">
              <ng-container *appAuthKey="'UBZ_SUMMARY_DOWNLOAD'">
                <button type="button" class="btn btn-empty" (click)="downloadPdf(doc.documentCod)">
                  <i class="fa fa-download" aria-hidden="true"></i>
                </button>
              </ng-container>
              <ng-container *appAuthKey="'UBZ_SUMMARY_OPEN'">
                <button type="button" class="btn btn-empty" (click)="openPdf(doc.documentCod)">
                  <i class="fa fa-file-pdf-o" aria-hidden="true"></i>
                </button>
              </ng-container>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</fieldset>
  <app-navigation-footer showSaveDraft="true" showPrevious="true" (saveButtonClick)="confirmModal.show()" (closeDraftButtonClick)="exit()" (previousButtonClick)="previous()"
    (cancelButtonClick)="cancelPosition()" [footerClass]="menuService.footerProperty" [activeTaskCode]="currentTask"
    cancelButtonString="{{'UBZ.SITE_CONTENT.1001010100' | translate}}" confirmButtonString="{{'UBZ.SITE_CONTENT.110001110' | translate }}"></app-navigation-footer>

<fieldset [disabled]="landingService.isLockedTask[currentTask]">
  <div class="modal fade" bsModal #confirmModal="bs-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h2>{{'UBZ.SITE_CONTENT.1110110' | translate }}</h2>
          <button type="button" class="close" aria-label="Close" (click)="confirmModal.hide()">
            <i class="icon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <p>{{'UBZ.SITE_CONTENT.1111011' | translate }}?</p>
          <p>{{'UBZ.SITE_CONTENT.1110111' | translate }}</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary waves-effect" (click)="confirm()">
            {{'UBZ.SITE_CONTENT.1111000' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>
</fieldset>
</ng-container>
<ng-container *ngIf="wizardCode === 'WRPE' && simulation.positionId">
  <fieldset [disabled]="landingService.isLockedTask[currentTask]">
    <app-checklist [positionId]="simulation.positionId" [isRequestId]="true" (isComplete)="setSaving($event)"></app-checklist>
  </fieldset>
<app-navigation-footer showSaveDraft="true" showPrevious="true" [saveIsEnable]="saveIsEnable"
  (previousButtonClick)="previous()" (cancelButtonClick)="cancelPosition()" (saveButtonClick)="confirm()"
  (closeDraftButtonClick)="exit()"confirmButtonString= "{{(accessPoint === 'RP' && appraisalOwner === 'CLI'   && posSegment === 'IND')  ||(accessPoint === 'PT' && posSegment === 'IND') ? '':('UBZ.SITE_CONTENT.110001110' | translate )}}"
  [activeTaskCode]="currentTask"></app-navigation-footer>
</ng-container>
<ng-container *ngIf="(accessPoint === 'RP'||accessPoint === 'PT' )&& macroProcess === 'ITL' && posSegment === 'IND' && forcingOwner ==='CLI'">
  <app-navigation-footer showSaveDraft="true" showPrevious="true" [saveIsEnable]="saveIsEnable"
  (previousButtonClick)="previous()" (cancelButtonClick)="cancelPosition()" (saveButtonClick)="confirm()"
  (closeDraftButtonClick)="exit()"confirmButtonString= ""
  [activeTaskCode]="currentTask"></app-navigation-footer>
</ng-container>
<ng-container *ngIf="(accessPoint === 'PT' ||accessPoint === 'RP' )  && forcingOwner ==='BNC' ">
  <app-navigation-footer showSaveDraft="true" showPrevious="true" [saveIsEnable]="saveIsEnable"
  (previousButtonClick)="previous()" (cancelButtonClick)="cancelPosition()" (saveButtonClick)="confirm()"
  (closeDraftButtonClick)="exit()"confirmButtonString= "{{('UBZ.SITE_CONTENT.110001110' | translate )}}"
  [activeTaskCode]="currentTask"></app-navigation-footer>
</ng-container>
</fieldset>
