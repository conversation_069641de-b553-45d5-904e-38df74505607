import {
  Component,
  OnInit,
  Input,
  ViewChild,
  AfterContentInit,
  Inject
} from '@angular/core';
import { NgForm } from '@angular/forms';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { APP_CONSTANTS, IAppConstants } from '../../../../../app.constants';

@Component({
  selector: 'app-photovoltaic',
  templateUrl: './photovoltaic.component.html',
  styleUrls: ['./photovoltaic.component.css']
})
export class PhotovoltaicComponent implements OnInit, AfterContentInit {
  @Input() model: any = {};
  @ViewChild('f') private form: NgForm;

  constructor(
    public _accordionAPFService: AccordionAPFService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) {}

  ngOnInit() {}

  ngAfterContentInit() {
    if (isNaN(this.model.prudentialUnitValue)) {
      this.model.prudentialUnitValue = 0;
    }
    if (isNaN(this.model.fotoSystems)) {
      this.model.fotoSystems = 0;
    }
    this.model.systemMeasurement = 'kWp';
    this.calculateTotal();
  }

  public isValid(): boolean {
    return this.form && this.form.valid;
  }

  calculateTotal() {
    this.model.prudentialValue =
      this.model.prudentialUnitValue * this.model.fotoSystems;
    this.model.roundPrudentialValue = Math.round(this.model.prudentialValue);
  }

}
