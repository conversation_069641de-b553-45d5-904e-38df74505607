export class PageFieldConditions {
  ALL = new FieldConditions(
    'true',
    'false',
    'true',
    'true',
    'false',
    'false',
    'true',
    'false',
    'false',
    'true',
    'true',
    'false',
    'false',
    'false',
    'true',
    'false',
    'false'
  );
  REQ = new FieldConditions(
    'true',
    'false',
    'true',
    'true',
    'false',
    'false',
    'this.selectedCounterId !== \'300\' && this.selectedCounterId !== \'400\' && this.selectedCounterId !== \'500\'',
    'this.selectedCounterId === \'300\' || this.selectedCounterId === \'400\' || this.selectedCounterId === \'500\'',
    'false',
    'this.searchTypes === \'STRUCT\'',
    'true',
    'false',
    'false',
    'false',
    'true',
    'this.selectedCounterId === \'200RIC-CON\'',
    'this.selectedCounterId === \'200RIC-ANN\''
  );
  SIM = new FieldConditions(
    'true',
    'false',
    'true',
    'true',
    'false',
    'false',
    'false',
    'false',
    'false',
    "this.searchTypes === 'STRUCT'",
    'true',
    'false',
    'false',
    'false',
    'true',
    'false',
    'false'
  );
  LAA = new FieldConditions(
    'true',
    'true',
    'true',
    'true',
    'false',
    'true',
    'false',
    'false',
    'false',
    "this.searchTypes === 'STRUCT'",
    'true',
    'false',
    'false',
    'false',
    'true',
    'false',
    'false'
  );
  LAPS = new FieldConditions(
    'true',
    'false',
    'true',
    'true',
    'false',
    'false',
    'false',
    'true',
    'true',
    "this.searchTypes === 'STRUCT'",
    'true',
    'false',
    'false',
    'false',
    'true',
    'false',
    'false'
  );
  // 'LASC' = new FieldConditions('true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true');
  // 'LASS' = new FieldConditions('true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true');
  // 'PCM' = new FieldConditions('true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true');
  // 'WAM' = new FieldConditions('true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true', 'true');
}

class FieldConditions {
  id: string;
  appraisalId: string;
  ndg: string;
  heading: string;
  type: string;
  phase: string;
  statusPhase: string;
  statusPhaseApp: string;
  task: string;
  inChargeUser: string;
  insertDate: string;
  updateDate: string;
  deleteDate: string;
  creationUser: string;
  creationBranch: string;
  conclusionChangeDate: string;
  annulmentChangeDate: string;

  public constructor(
    id: string,
    appraisalId: string,
    ndg: string,
    heading: string,
    type: string,
    phase: string,
    statusPhase: string,
    statusPhaseApp: string,
    task: string,
    inChargeUser: string,
    insertDate: string,
    updateDate: string,
    deleteDate: string,
    creationUser: string,
    creationBranch: string,
    conclusionChangeDate: string,
    annulmentChangeDate: string
  ) {
    this.id = id;
    this.appraisalId = appraisalId;
    this.ndg = ndg;
    this.heading = heading;
    this.type = type;
    this.phase = phase;
    this.statusPhase = statusPhase;
    this.statusPhaseApp = statusPhaseApp;
    this.task = task;
    this.inChargeUser = inChargeUser;
    this.insertDate = insertDate;
    this.updateDate = updateDate;
    this.deleteDate = deleteDate;
    this.creationUser = creationUser;
    this.creationBranch = creationBranch;
    this.conclusionChangeDate = conclusionChangeDate;
    this.annulmentChangeDate = annulmentChangeDate;
  }
}
