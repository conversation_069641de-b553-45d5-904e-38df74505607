<div class="row">
  <div class="MyTabs nav-wrap nav-controls">
    <ul class="nav nav-tabs pull-left">
      <li role="presentation" [ngClass]="{'active': !selectedAsset, 'MyTabs__Tab': true}" id="first-tab">
        <a role="button" [ngClass]="{'cursor-pointer': selectedAsset}" data-toggle="tab" role="tab"
          (click)="triggerSelectAsset(null)">{{'UBZ.SITE_CONTENT.10001011010' | translate }}</a>
      </li>
      <ng-container *ngFor="let ass of assets; let i = index">
        <li *ngIf="i < maxVisibleTabs" [ngClass]="{'active': ass === selectedAsset,
            'MyTabs__Tab': true}">
          <a role="tab" class="cursor-pointer" (click)="triggerSelectAsset(ass)">
            <i class="fa fa-map-marker" data-placement="bottom" data-toggle="tooltip"
              [title]="getAssetAddress(ass)"></i>
              <span *ngIf="activeCategories.indexOf(ass.resourceItemCategory) > -1">
                {{ ass.resourceItemCategory }} - {{ ass.resourceItemId }}
              </span>
              <span *ngIf="activeCategories.indexOf(ass.resourceItemCategory) === -1">
                <span class="expiredCategory"> {{ ass.resourceItemCategory }}</span> - {{ ass.resourceItemId }} 
              </span>
           
            <br>

            <!-- Display Asset Id + Tooltip: { Asset Id, Disabled State and Promiscuo State } -->
            <app-tab-managed-asset-details [resItemId]="ass.assetId" [assetDetail]="ass.assetDetail"
              [isMultipleAssetPerObject]="ass.isMultipleAssetPerObject"
              [isAppraisalConvalidated]="isAppraisalConvalidated">
            </app-tab-managed-asset-details>

          </a>
        </li>
      </ng-container>
      <li id="dropdown-button">
        <div dropdown class="MyTabs__Dropdown" *ngIf="shouldShowDropdown">
          <button class="MyTabs__Dropdown__Toggle btn btn-empty btn-plus" dropdownToggle>
            <i class="fa fa-caret-down"></i>
          </button>

          <div *dropdownMenu class="MyTabs__Dropdown__Menu dropdown-menu dropdown-menu-right" role="menu">
            <ul>
              <ng-container *ngFor="let asset of assets; let i = index">
                <li *ngIf="i >= maxVisibleTabs" class="dropdown-item"
                  [ngClass]="{'MyTabs__Dropdown__Menu__Item--active': selectedAsset === asset}">
                  <a (click)="triggerSelectAsset(asset)">
                    <i class="fa fa-map-marker" data-placement="bottom" data-toggle="tooltip"
                      [title]="getAssetAddress(asset)"></i>
                      <span *ngIf="activeCategories.indexOf(asset.resourceItemCategory) > -1">
                        {{ asset.resourceItemCategory }} - {{ asset.resourceItemId }}
                      </span>
                      <span *ngIf="activeCategories.indexOf(asset.resourceItemCategory) === -1">
                        <span class="expiredCategory"> {{ asset.resourceItemCategory }}</span> - {{ asset.resourceItemId }} 
                      </span>
                   
                    <br>

                    <!-- Display Asset Id + Tooltip: { Asset Id, Disabled State and Promiscuo State } -->
                    <app-tab-managed-asset-details [resItemId]="asset.assetId" [assetDetail]="asset.assetDetail"
                      [isMultipleAssetPerObject]="asset.isMultipleAssetPerObject"
                      [isAppraisalConvalidated]="isAppraisalConvalidated">
                    </app-tab-managed-asset-details>

                    <span class="state"
                      [ngClass]="{'green' : (asset.conclusionStatusFlag === 'Y'), 'red' : (asset.conclusionStatusFlag === 'N')}"></span>
                  </a>
                </li>
              </ng-container>
            </ul>
          </div>
        </div>
      </li>
    </ul>
  </div>
</div>