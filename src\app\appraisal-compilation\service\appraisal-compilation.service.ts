import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';
import { Observable } from 'rxjs/Observable';
import { AppConstants } from '../../app.constants';
import { TemplateValidation } from '../model/template-validation';

@Injectable()
export class AppraisalCompilationService {
  private page = AppConstants.appraisalCompilationPage;

  constructor(private _http: Http) {}

  // positionId: string - identificativo della perizia
  // fieldObject: Object - oggetto contenente i dati da salvare (apfform e stringa del page)
  // mode: string - stringa rappresentativa del tipo di ndg (COR / IND)
  public saveGenericData(
    positionId: string,
    fieldsObject: any,
    mode: string
  ): Observable<any> {
    positionId = encodeURIComponent(positionId);
    // mode = 'SHI' per shipping, 'COR per ndg corporate, 'IND' per ndg individual
    const urlMap = {
      COR: '/UBZ-ESA-RS/service/apfService/apf/templateSaveForms/' + positionId,
      IND: '/UBZ-ESA-RS/service/individual/v1/templateSaveForms/' + positionId,
    };
    const objToSave = {
      page: this.page,
      apfmap: fieldsObject,
    };
    return this._http.post(urlMap[mode], objToSave).map((res) => res.json());
  }

  public getEndJobEvaluation(): Observable<any> {
    const url = '/assets/data/fake-appraisal-compilation/fake-sal.json';
    return this._http.get(url).map((res) => res.json());
  }

  // START GUARANTEE PAGE
  public getAssociateAssets(positionId: string) {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisalObject/${positionId}`;
    // return this._http.get('/assets/data/fake-associate-assets.json').map((resp: Response) => resp.json());
    return this._http.get(url).map((res) => res.json());
  }

  public addAsset(positionId: string, category: string, collateralData: any) {
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisalObject/assetImmGar`;
    const input = {
      appraisalId: positionId,
      category: category,
      collateral: collateralData,
    };
    return this._http.post(url, input).map((res) => res.json());
  }

  public deleteAsset(assetId: string) {
    assetId = encodeURIComponent(assetId);
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisalObject/${assetId}/assetImmGar`;
    return this._http.delete(url).map((res) => res.json());
  }

  public motivateAssetDeletion(assetId: string, reason: string, notes: string) {
    assetId = encodeURIComponent(assetId);
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisalObject/${assetId}/assetImmGar/note`;
    const input = { reason: reason, notes: notes };
    return this._http.put(url, input).map((res) => res.json());
  }

  public deleteConsistency(
    assetId: string,
    structType: string,
    ruleId: number
  ) {
    assetId = encodeURIComponent(assetId);
    const url = `/UBZ-ESA-RS/service/appraisal/v1/consistency/${assetId}/${structType}/${ruleId}`;
    return this._http.delete(url).map((res) => res.json());
  }

  public copyAsset(assetId: string, appraisalId: string) {
    assetId = encodeURIComponent(assetId);
    appraisalId = encodeURIComponent(appraisalId);
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisalObject/duplicate`;
    return this._http
      .post(url, { appraisalId: appraisalId, objectCod: assetId })
      .map((res) => res.text());
  }

  public getStaticComponentsOfPropertyGuarantee(
    positionId: string
  ): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/immGarService/immobiligetdata/${positionId}`;
    return this._http.get(url).map((res: Response) => res.json());
    // return this._http.get('/assets/data/fake-guarantees-page.json').map((res: Response) => res.json());
  }

  public getSingleStaticComponentOfPropertyGuarantee(
    assetId: string,
    accordionName: string
  ): Observable<any> {
    assetId = encodeURIComponent(assetId);
    accordionName = encodeURIComponent(accordionName);
    const url = `/UBZ-ESA-RS/service/immGarService/immobiligetdata/${assetId}/${accordionName}`;
    return this._http.get(url).map((res: Response) => res.json());
  }

  public saveNewStaticAccordion(
    accordionName: string,
    assetId?: string,
    apForm?: any
  ) {
    accordionName = encodeURIComponent(accordionName);
    assetId = encodeURIComponent(assetId);
    const url = `/UBZ-ESA-RS/service/immGarService/${accordionName}/${assetId}`;
    return this._http
      .post(url, { page: apForm.page, apfmap: apForm.model })
      .map((res: Response) => res.json());
  }

  public saveExistingStaticAccordion(
    accordionName: string,
    idCode: string,
    apForm: any
  ) {
    accordionName = encodeURIComponent(accordionName);
    idCode = encodeURIComponent(idCode);
    const url = `/UBZ-ESA-RS/service/immGarService/${accordionName}/${idCode}`;
    return this._http
      .put(url, { page: apForm.page, apfmap: apForm.model })
      .map((res: Response) => res.json());
  }

  public deleteStaticAccordion(accordionName: string, idCode: string) {
    accordionName = encodeURIComponent(accordionName);
    idCode = encodeURIComponent(idCode);
    const url = `/UBZ-ESA-RS/service/immGarService/${accordionName}/${idCode}`;
    return this._http.delete(url).map((res: Response) => res.json());
  }
  // END GUARANTEE PAGE

  saveImmGaranzia(
    mode: string,
    assetId: string,
    apfsave: any,
    staticsave: any
  ) {
    assetId = encodeURIComponent(assetId);
    // mode = 'SHI' per shipping, 'COR per ndg corporate, 'IND' per ndg individual
    const urlMap = {
      SHI: `/UBZ-ESA-RS/service/immGarService/garanzia/${assetId}`,
      COR: `/UBZ-ESA-RS/service/immGarService/immobilisavedata/${assetId}`,
      IND: `/UBZ-ESA-RS/service/individual/v1/immobilisavedata/${assetId}`,
    };
    const input = {
      staticsave: staticsave,
      apfsave: apfsave,
    };
    return this._http.post(urlMap[mode], input).map((res) => res.json());
  }

  saveIndustrialGenericData(
    positionId: string,
    apfs: any,
    statics: any
  ): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url =
      '/UBZ-ESA-RS/service/immGarService/industrialDatiGenerali/' + positionId;
    const toSave = {};
    toSave['apfsave'] = apfs;
    toSave['staticsave'] = statics;
    return this._http.post(url, toSave).map((res) => res.json());
  }

  saveGoodDescriptionData(systemId: string, toSave: any): Observable<any> {
    systemId = encodeURIComponent(systemId);
    const url =
      '/UBZ-ESA-RS/service/immGarService/indsystemDetailsAPF/' + systemId;
    return this._http.post(url, toSave).map((res) => res.json());
  }

  saveRegisterData(
    positionId: string,
    apfs: any,
    statics: any
  ): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = '/UBZ-ESA-RS/service/immGarService/indCatasto/' + positionId;
    const toSave = {};
    toSave['apfsave'] = apfs;
    toSave['staticsave'] = statics;
    return this._http.post(url, toSave).map((res) => res.json());
  }

  getRegisterStaticData(assetId: string): Observable<any> {
    assetId = encodeURIComponent(assetId);
    const url = '/UBZ-ESA-RS/service/immGarService/indCatasto/' + assetId;
    return this._http.get(url).map((res) => res.json());
  }

  getShippingStatics(objectCode: string): Observable<any> {
    objectCode = encodeURIComponent(objectCode);
    const url =
      '/UBZ-ESA-RS/service/immGarService/garanziaStatic/' + objectCode;
    return this._http.get(url).map((res) => res.json());
  }

  getTemplateValidation(
    validation: TemplateValidation
  ): Observable<TemplateValidation> {
    const url = '/UBZ-ESA-RS/service/note/v1/validationTemplate/getData';
    return this._http.post(url, validation).map((res) => res.json());
  }

  saveTemplateValidation(validation: TemplateValidation): Observable<boolean> {
    const url = '/UBZ-ESA-RS/service/note/v1/validationTemplate';
    return this._http.post(url, validation).map((res) => res.json());
  }

  public getMobileType(appraisalObjects: any[]): string {
    return appraisalObjects[0].resItemCategory;
  }
}
