<form #f="ngForm" novalidate>
  <ng-container *ngIf="isForTemplate">
    <div class="row form-group" *ngIf="!!socId">
      <div class="col-sm-3">
        <label>{{'UBZ.SITE_CONTENT.10000011000' | translate }}*</label>
        <input class="form-control" type="text" name="idExpert" required [(ngModel)]="spExpertId" appForcePattern
          regexPattern="{{constants.REGEX_PATTERN.ONLY_NUMBER}}" (change)="writeInfo()">
      </div>
      <div class="col-sm-3">
        <label>{{'UBZ.SITE_CONTENT.1010111000' | translate }}*</label>
        <input class="form-control" type="text" name="nameExpert" required [(ngModel)]="spExpertDesc" appForcePattern
          regexPattern="{{constants.REGEX_PATTERN.ONLY_TEXT}}" (change)="writeInfo()">
      </div>
      <div class="col-sm-3">
        <label>{{'UBZ.SITE_CONTENT.10000011001' | translate }}*</label>
        <input class="form-control" type="text" name="idRev" required [(ngModel)]="spRevisionId" appForcePattern
          regexPattern="{{constants.REGEX_PATTERN.ONLY_NUMBER}}" (change)="writeInfo()">
      </div>
      <div class="col-sm-3">
        <label>{{'UBZ.SITE_CONTENT.10000011010' | translate }}*</label>
        <input class="form-control" type="text" name="nameRev" required [(ngModel)]="spRevisionDesc" appForcePattern
          regexPattern="{{constants.REGEX_PATTERN.ONLY_TEXT}}" (change)="writeInfo()">
      </div>
    </div>
    <!-- Sezione trasparenza -->
    <div class="row form-group">
      <fieldset [disabled]="isTransparencyDisabled">
        <div class="col-sm-3" *ngIf="showFreeAppraisalCheck">
          <div class="custom-checkbox">
            <input name="free-appraisal-check" id="free-appraisal-check" type="checkbox" class="checkbox"
              [(ngModel)]="freeAppraisalCheck" (ngModelChange)="setBillingFormRequired()">
            <label class="check-label" for="free-appraisal-check">{{'UBZ.SITE_CONTENT.10011011111' | translate}}</label>
          </div>
        </div>
        <div class="col-sm-3" *ngIf="showBillingForm">
          <label>{{'UBZ.SITE_CONTENT.10011011101' | translate}}{{(billingFormRequired || billingDate) ? '*' :
            ''}}</label>
          <app-importo [(ngModel)]="billingAmount" [name]="'billing-amount'"
            [required]="billingFormRequired || billingDate" [ngClassAdd]="(billingAmount === 0) ? 'error' : ''"
            [ngClassCondition]="true" (ngModelChange)="writeInfo()"></app-importo>
        </div>
        <div class="col-sm-3" *ngIf="showBillingForm">
          <label>{{'UBZ.SITE_CONTENT.10011011110' | translate}}{{(billingFormRequired || billingAmount) ? '*' :
            ''}}</label>
          <app-calendario [(ngModel)]="billingDate" [name]="'billing-date'"
            [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate" [required]="billingFormRequired || billingAmount"
            (ngModelChange)="writeInfo()"></app-calendario>
        </div>
      </fieldset>
    </div>
    <div class="row form-group">
      <fieldset [disabled]="isTransparencyDisabled">
        <div class="col-sm-3" *ngIf="isSecondBillingForm">
          <div class="custom-checkbox">
            <input name="appraisal-check" id="appraisal-check" type="checkbox" class="checkbox"
              [(ngModel)]="secondfreeAppraisalCheck" (ngModelChange)="setBillingSecoundFormRequired()">
            <label class="check-label" for="appraisal-check">{{'UBZ.SITE_CONTENT.100000010011' | translate}}</label>
          </div>
        </div>
        <div class="col-sm-3" *ngIf="!showSecondBillingForm && isSecondBillingForm">
          <label>{{'UBZ.SITE_CONTENT.100000010100' | translate}}{{(secondBillingFormRequired || billingDateSecond) ? '*' : ''}}</label>
          <app-importo [(ngModel)]="billingAmountSecond" [name]="'billing-amountsecound'"
            [required]="secondBillingFormRequired || billingDateSecond"
            [ngClassAdd]="(billingAmountSecond === 0) ? 'error' : ''" [ngClassCondition]="true"
            (ngModelChange)="writeInfo()"></app-importo>
        </div>
        <div class="col-sm-3" *ngIf="!showSecondBillingForm && isSecondBillingForm">
          <label>{{'UBZ.SITE_CONTENT.100000010101' | translate}}{{(secondBillingFormRequired || billingAmountSecond) ? '*' : ''}}</label>
          <app-calendario [(ngModel)]="billingDateSecond" [name]="'billing-datesecound'"
            [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
            [required]="secondBillingFormRequired || billingAmountSecond"
            (ngModelChange)="writeInfo()"></app-calendario>
        </div>
      </fieldset>
    </div>
  </ng-container>
  <ng-container *ngIf="!isForTemplate">
    <div class="col-sm-12 text-right">
      <app-task-access-rights [positionId]="positionId" [taskId]="taskId" [taskCod]="taskCod"
        [excludeList]="['UBZ_PRZ_OK']" (taskLocked)="taskLocked()" (taskUnlocked)="taskUnlocked()"
        (taskLockedByOtherUser)="tasklockedByOtherUser($event)"></app-task-access-rights>
    </div>
    <div *appAuthKey="'UBZ_FIX_APPOINTMENT_SEARCH_EXPERT'" class="col-sm-12">
      <h4 class="section-heading">{{'UBZ.SITE_CONTENT.101110100' | translate }}</h4>
      <div class="row">
        <div class="col-sm-3">
          <label>{{'UBZ.SITE_CONTENT.10000011000' | translate }}*</label>
          <input class="form-control" type="text" name="idExpert" required [(ngModel)]="spExpertId" appForcePattern
            regexPattern="{{constants.REGEX_PATTERN.ONLY_NUMBER}}">
        </div>
        <div class="col-sm-3">
          <label>{{'UBZ.SITE_CONTENT.1010111000' | translate }}*</label>
          <input class="form-control" type="text" name="nameExpert" required [(ngModel)]="spExpertDesc" appForcePattern
            regexPattern="{{constants.REGEX_PATTERN.ONLY_TEXT}}">
        </div>
        <div class="col-sm-3">
          <label>{{'UBZ.SITE_CONTENT.10000011001' | translate }}*</label>
          <input class="form-control" type="text" name="idRev" required [(ngModel)]="spRevisionId" appForcePattern
            regexPattern="{{constants.REGEX_PATTERN.ONLY_NUMBER}}">
        </div>
        <div class="col-sm-3">
          <label>{{'UBZ.SITE_CONTENT.10000011010' | translate }}*</label>
          <input class="form-control" type="text" name="nameRev" required [(ngModel)]="spRevisionDesc" appForcePattern
            regexPattern="{{constants.REGEX_PATTERN.ONLY_TEXT}}">
        </div>
      </div>
    </div>
    <div class="col-sm-6">
      <h4 class="section-heading">{{'UBZ.SITE_CONTENT.101110111' | translate }}</h4>
      <div class="row">
        <div class="col-sm-6 form-group">
          <label>{{'UBZ.SITE_CONTENT.10111' | translate }}*</label>
          <app-calendario [name]="'dataContatto'" [(ngModel)]="model.dataContatto"
            [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate" [maxDate]="today" [required]="true"
            (ngModelChange)="setDataMinSopralluogo()">
          </app-calendario>
        </div>
        <div class="col-sm-3 form-group">
          <label>{{'UBZ.SITE_CONTENT.101111000' | translate }}*</label>
          <div class="custom-select">
            <select class="form-control" name="oraContatto" required="" [(ngModel)]="model.oraContatto">
              <option value="" hidden selected>{{'UBZ.SITE_CONTENT.11000' | translate }}</option>
              <option *ngFor="let ora of orePermesse" value="{{ora}}">{{ora | number:'2.0-0'}}</option>
            </select>
          </div>
        </div>
        <div class="col-sm-3 form-group">
          <label>&nbsp;</label>
          <div class="custom-select">
            <select class="form-control" name="minContatto" required="" [(ngModel)]="model.minContatto">
              <option value="" hidden selected>{{'UBZ.SITE_CONTENT.101111010' | translate }}</option>
              <option *ngFor="let min of minPermessi" value="{{min}}">{{min | number:'2.0-0'}}</option>
            </select>
          </div>
        </div>
      </div>
    </div>
    <div class="col-sm-6">
      <h4 class="section-heading">{{'UBZ.SITE_CONTENT.101111001' | translate }}</h4>
      <div class="row">
        <div class="col-sm-6 form-group">
          <label>{{'UBZ.SITE_CONTENT.10111' | translate }}*</label>
          <app-calendario [name]="'dataSopralluogo'" [(ngModel)]="model.dataSopralluogo"
            [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate" [minDate]="dataMinSopralluogo"
            [maxDate]="maxValidDate" [required]="true">
          </app-calendario>
        </div>
        <div class="col-sm-3 form-group">
          <label>{{'UBZ.SITE_CONTENT.101111000' | translate }}*</label>
          <div class="custom-select">
            <select class="form-control" name="oraSopralluogo" required="" [(ngModel)]="model.oraSopralluogo">
              <option value="" hidden selected>{{'UBZ.SITE_CONTENT.11000' | translate }}</option>
              <option *ngFor="let ora of orePermesse" value="{{ora}}">{{ora | number:'2.0-0'}}</option>
            </select>
          </div>
        </div>
        <div class="col-sm-3 form-group">
          <label>&nbsp;</label>
          <div class="custom-select">
            <select class="form-control" name="minSopralluogo" required="" [(ngModel)]="model.minSopralluogo">
              <option value="" hidden selected>{{'UBZ.SITE_CONTENT.101111010' | translate }}</option>
              <option *ngFor="let min of minPermessi" value="{{min}}">{{min | number:'2.0-0'}}</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </ng-container>
</form>

<div class="container-fluid" *ngIf="isTaskLocked">
  <div class="row">
    <div class="col-sm-12 btn-set">
      <button type="button" class="btn btn-primary waves-effect waves-secondary pull-right"
        (click)="saveAppointment(); $event.stopPropagation();"
        [disabled]="!buttonIsEnable()">{{'UBZ.SITE_CONTENT.101111011' | translate }}</button>
      <button type="button" class="btn btn-primary waves-effect waves-secondary pull-right"
        (click)="saveInDraft()">{{'UBZ.SITE_CONTENT.1100111111'
        | translate }}</button>
    </div>
  </div>
</div>