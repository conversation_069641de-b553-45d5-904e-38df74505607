import {
  Component,
  OnChanges,
  Input,
  On<PERSON><PERSON>roy,
  SimpleChanges
} from '@angular/core';
import { Subscription } from 'rxjs/Subscription';
import { DomainService } from '../../../../../shared/domain/domain.service';
import { RegistryService } from '../../../../service/registry.service';
import { CompetencePronvince } from '../../../../model/registry.models';

@Component({
  selector: 'app-competence-provinces',
  templateUrl: './competence-provinces.component.html',
  styleUrls: ['./competence-provinces.component.css']
})
export class CompetenceProvincesComponent implements OnChanges, OnDestroy {
  public isEditable: boolean;
  @Input() public anagId: string;
  public aProvinceExists: boolean;
  public provinces: CompetencePronvince[] = [];
  private _subscription: Subscription;
  private _ProvSubscription: Subscription;
  public availableProvinces: any[] = [];

  constructor(
    private _registryService: RegistryService,
    private _domainService: DomainService
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes && this.anagId) {
      this.refreshProvinces();
      this._ProvSubscription = this._domainService
        .newGetDomain('UBZ_DOM_PROVINCE')
        .subscribe(res => {
          this.availableProvinces = res;
        });
    }
  }

  ngOnDestroy() {
    if (this._subscription && !this._subscription.closed) {
      this._subscription.unsubscribe();
    }
    if (this._ProvSubscription && !this._ProvSubscription.closed) {
      this._ProvSubscription.unsubscribe();
    }
  }

  private refreshProvinces(addIfEmpty?: boolean): void {
    addIfEmpty = addIfEmpty || false;
    this._subscription = this._registryService
      .getCompetenceProvinces(this.anagId)
      .subscribe(res => {
        this.provinces = res;
        if (addIfEmpty && !this.aProvinceExists) {
          this.addCompetenceProvince();
        }
        this.aProvinceExists = this.provinces.length > 0 ? true : false;
      });
  }

  public addCompetenceProvince() {
    this.provinces.push(new CompetencePronvince());
    this.aProvinceExists = true;
  }

  public startEdit(event: any): void {
    event.stopPropagation();
    this.refreshProvinces(true);
    this.isEditable = true;
  }

  public stopEdit(event: any): void {
    event.stopPropagation();
    this.refreshProvinces(false);
    this.isEditable = false;
  }

  public save(event: any): void {
    this._registryService
      .saveCompetenceProvinces(this.anagId, this.provinces)
      .subscribe(res => {
        this.isEditable = false;
      });
  }
}
