import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ChecklistService } from '../service/checklist.service';
import { DomainService } from '../../domain/domain.service';
import { Observable } from 'rxjs/Observable';
import * as FileSaver from 'file-saver';
import { PositionService } from '../../position/position.service';
import { MessageService } from '../../messages/services/message.service';
import { FractionedAppraisalResponse } from '../../position/models/fractioned-appraisal-response.model';

@Component({
  selector: 'app-checklist',
  templateUrl: './checklist.component.html',
  styleUrls: ['./checklist.component.css']
})
export class ChecklistComponent implements OnInit {
  @Input() positionId: string; // either request or appraisal 
  @Input() requestId: string; // used only in case of checklist use into
  @Input() isRequestId: boolean;
  @Output() isComplete = new EventEmitter();

  accordions: any[];
  allPageIsValid = true;
  allRowSelected = false;
  isValidClass = { Y: 'state green', N: 'state red' };
  renderIconPrint = false;
  renderIconUpload = false;
  checkboxStatus: boolean[] = [];
  progToDocCodMap: string[] = [];
  accordionsStatusOpen: boolean[] = [];
  inputModalObject: any;
  inputModalObject2: any;
  entityTypeDom: any[];
  assetTypeDom: any[];
  categoryTypeDom: any[];
  statusDom: any[];

  // Booleani che gestiscono l'apertura delle modal
  aggiungiDocumentoIsOpen = false;
  aggiungiDocumentoWithUpload = false;
  uploadFileAssociatiIsOpen = false;
  uploadFileSingleIsOpen = false;
  modalNoteIsOpen = false;
  modalHistoryIsOpen = false;
  modalInvalidIsOpen = false;
  bankCod: string = '';
  macroProcess: string = '';

  constructor(
    private checklistService: ChecklistService,
    private domainService: DomainService,
    private messageService: MessageService,
    private positionService: PositionService,
    private translateService: TranslateService
  ) { }

  ngOnInit() {
    this.initChecklistPageData();
    this.checkIfAppraisalIsFractioned();
  }

  initChecklistPageData() {
    this.inputModalObject = null; // utilizzato per il refresh della pagina
    this.inputModalObject2 = null; // utilizzato per il refresh della pagina
    Observable.forkJoin(
      this.checklistService.newGetChecklist(this.positionId),
      this.domainService.newGetDomain('UBZ_DOM_CATEGORY_TYPE'),
      this.domainService.newGetDomain('UBZ_DOM_RESITEM_TYPE'),
      this.domainService.newGetDomain('UBZ_DOM_CHK_ENTITY_TYPE'),
      this.domainService.newGetDomain('UBZ_DOM_STATUS')
    ).subscribe(res => {
      this.accordions = res[0];
      this.categoryTypeDom = res[1];
      this.assetTypeDom = res[2];
      this.entityTypeDom = res[3];
      this.statusDom = res[4];
      this.initAccordionState();
      this.checkIfComplete();
    });

    if (!this.isRequestId && typeof this.positionId !== 'undefined' && this.positionId) {

      this.positionService.getAppraisalInfo(this.positionId)
        .subscribe((res: any) => {
          if (typeof res.appraisal.bankCod !== 'undefined') {
            this.bankCod = res.appraisal.bankCod;
          }
          if (typeof res.appraisal.macroProcess !== 'undefined') {
            this.macroProcess = res.appraisal.macroProcess;
          }
          if (typeof res.requestId !== 'undefined' && res.requestId) {
            this.requestId = res.requestId;
          }
        });
    }
  }

  initAccordionState() {
    for (const accordion of this.accordions) {
      this.accordionsStatusOpen.push(false);
      for (const row of accordion.groups) {
        this.checkboxStatus[row.prog] = false;
        this.progToDocCodMap[row.prog] = row.documentCod
          ? row.documentCod
          : row.groupCod;
      }
    }
  }

  getAccordionStatusClass(accord: any) {
    let areAllDocumentValidated = 'Y';
    for (const row of accord.groups) {
      if (row.acquired !== 'Y' && row.flagMandatory === 'Y') {
        areAllDocumentValidated = 'N';
        break;
      }
    }
    return this.isValidClass[areAllDocumentValidated];
  }

  getRowClass(isAcquired: string, isMandatory: string) {
    if (isAcquired !== 'Y' && isMandatory === 'Y') {
      return this.isValidClass['N'];
    } else {
      return this.isValidClass['Y'];
    }
  }

  toggleAllAssetSelected() {
    this.allRowSelected = !this.allRowSelected;
    for (const ind in this.checkboxStatus) {
      if (true) {
        this.checkboxStatus[ind] = this.allRowSelected;
      }
    }
    if (this.allRowSelected === true) {
      for (const ind in this.accordionsStatusOpen) {
        if (true) {
          this.accordionsStatusOpen[ind] = true;
        }
      }
    }
    this.setRenderIcons();
  }

  setRenderIcons() {
    let print = false;
    let count = 0;
    for (const ind in this.checkboxStatus) {
      if (this.checkboxStatus.hasOwnProperty(ind)) {
        if (this.checkboxStatus[ind] === true && this.isDocumentAcquired(ind)) {
          print = true;
        }
        if (this.checkboxStatus[ind] === true) {
          count++;
        }
      }
    }
    setTimeout(() => {
      this.renderIconPrint = print;
    }, 0);
    if (count >= 2) {
      setTimeout(() => {
        this.renderIconUpload = true;
      }, 0);
    } else {
      setTimeout(() => {
        this.renderIconUpload = false;
      }, 0);
    }
  }

  private isDocumentAcquired(ind) {
    for (const accordion of this.accordions) {
      for (const row of accordion.groups) {
        if (row.prog == ind && row.acquired === 'Y') {
          return true;
        }
      }
    }
    return false;
  }

  setCheckboxStatus(prog: string) {
    this.checkboxStatus[prog] = !this.checkboxStatus[prog];
    this.setRenderIcons();
  }

  openCloseAccordion(index) {
    this.accordionsStatusOpen[index] = !this.accordionsStatusOpen[index];
  }

  openAggiungiDocumento(withUpload: boolean) {
    this.aggiungiDocumentoWithUpload = withUpload;
    this.inputModalObject = this.accordions;
    this.aggiungiDocumentoIsOpen = true;
  }

  closeAggiungiDocumento(ret) {
    this.inputModalObject = null;
    this.aggiungiDocumentoIsOpen = false;
    if (ret.refreshPage === true) {
      this.initChecklistPageData();
      if (ret.accordionIndex !== -1) {
        this.accordionsStatusOpen.forEach((value, index, array) => {
          if (index === ret.accordionIndex) {
            this.accordionsStatusOpen[index] = true;
          } else {
            this.accordionsStatusOpen[index] = false;
          }
        });
      }
    }
  }

  openModalFileAssociati() {
    this.uploadFileAssociatiIsOpen = true;
  }

  closeModalFileAssociati(refreshPage) {
    this.uploadFileAssociatiIsOpen = false;
    if (refreshPage === true) {
      this.initChecklistPageData();
    }
  }

  downloadLastUpload(row, accord) {
    this.checklistService.getVersions(row.prog).subscribe(x => {
      let max = x[0];
      x.forEach(el => {
        if (el.uploadDate > max.uploadDate) {
          max = el;
        }
      });
      this.checklistService.downloadDocument(this.isRequestId ? null : this.positionId, this.isRequestId ? this.positionId : this.requestId,
        max.prog, max.versionId)
        .subscribe(file => {
          const content = file.header.get('content-disposition');
          FileSaver.saveAs(file.document, content);
        });
    });
  }

  openModalUploadFileSingle(row, accordion) {
    this.inputModalObject = row;
    if (accordion.entityType === 'ASS') {
      this.inputModalObject2 = {
        entityType: this.entityTypeDom[accordion.entityType],
        resItemType: this.assetTypeDom[accordion.resItemType],
        resItemCategory: this.categoryTypeDom[accordion.resItemCategory]
      };
    }
    this.uploadFileSingleIsOpen = true;
  }

  closeModalUploadFileSingle(refreshPage) {
    this.inputModalObject = null;
    this.inputModalObject2 = null;
    this.uploadFileSingleIsOpen = false;
    if (refreshPage === true) {
      this.initChecklistPageData();
    }
  }

  openModalNote(row, accordion) {
    this.inputModalObject = row;
    if (accordion.entityType === 'ASS') {
      this.inputModalObject2 = {
        entityType: this.entityTypeDom[accordion.entityType],
        resItemType: this.assetTypeDom[accordion.resItemType],
        resItemCategory: this.categoryTypeDom[accordion.resItemCategory]
      };
    }
    this.modalNoteIsOpen = true;
  }

  closeModalNote(refreshPage) {
    this.inputModalObject = null;
    this.inputModalObject2 = null;
    this.modalNoteIsOpen = false;
    if (refreshPage === true) {
      this.initChecklistPageData();
    }
  }

  openModalHistory(row, accordion) {
    this.inputModalObject = row;
    if (accordion.entityType === 'ASS') {
      this.inputModalObject2 = {
        entityType: this.entityTypeDom[accordion.entityType],
        resItemType: this.assetTypeDom[accordion.resItemType],
        resItemCategory: this.categoryTypeDom[accordion.resItemCategory]
      };
    }
    this.modalHistoryIsOpen = true;
  }

  closeModalHistory(refreshPage) {
    this.inputModalObject = null;
    this.inputModalObject2 = null;
    this.modalHistoryIsOpen = false;
    if (refreshPage === true) {
      this.initChecklistPageData();
    }
  }

  openModalInvalid(row, accordion) {
    this.inputModalObject = row;
    if (accordion.entityType === 'ASS') {
      this.inputModalObject2 = {
        entityType: this.entityTypeDom[accordion.entityType],
        resItemType: this.assetTypeDom[accordion.resItemType],
        resItemCategory: this.categoryTypeDom[accordion.resItemCategory]
      };
    }
    this.modalInvalidIsOpen = true;
  }

  closeModalInvalid(refreshPage) {
    this.inputModalObject = null;
    this.inputModalObject2 = null;
    this.modalInvalidIsOpen = false;
    if (refreshPage === true) {
      this.initChecklistPageData();
    }
  }

  checkIfComplete() {
    this.checklistService.newAcquiredCheck(this.positionId).subscribe(x => {
      if (x.length === 0) {
        this.allPageIsValid = true;
      } else {
        this.allPageIsValid = false;
      }
      this.isComplete.emit(this.allPageIsValid);
    });
  }

  getStatusFormatted(mandatoryFor: string) {
    if (mandatoryFor) {
      const mfFormatted = mandatoryFor.replace(';', '');
      if (this.statusDom[mfFormatted]) {
        return this.statusDom[mfFormatted].translationCod;
      } else {
        return '';
      }
    } else {
      return '';
    }
  }

  printChecklist() {
    this.checklistService.print(this.positionId, 'LDC').subscribe(file => {
      FileSaver.saveAs(file, 'document.pdf');
    });
  }

  printMandato() {
    this.checklistService.print(this.positionId, 'MAN').subscribe(file => {
      FileSaver.saveAs(file, 'document.pdf');
    });
  }

  checkIfAppraisalIsFractioned() {
    this.positionService.isAppraisalFractioned(this.positionId)
      .subscribe((resp: FractionedAppraisalResponse) => {
        if (resp && resp.frazCheck === true && resp.warning) {
          this.messageService.showWarning(
            resp.warning.message,
            this.translateService.instant('UBZ.SITE_CONTENT.11110000000'));
        }
      });
  }
}
