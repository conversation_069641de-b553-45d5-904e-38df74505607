import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>nit,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild,
  ViewChildren,
  QueryList,
  Inject,
  ChangeDetectorRef,
  AfterViewChecked,
} from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { Observable } from 'rxjs/Observable';

// Components
import { PriceEvaluationTabComponent } from './component/price-evaluation-tab/price-evaluation-tab.component';
import { AccordionApplicationFormComponent } from '../../../shared/application-form/components/accordion-application-form/accordion-application-form.component';
import { MerchantabilityComponent } from './component/merchantability/merchantability.component';
import { PhotovoltaicComponent } from './component/photovoltaic/photovoltaic.component';
import { AppraisalTemplateValidationComponent } from '../appraisal-template-validation/appraisal-template-validation.component';
import {
  MarketConsiderationComponent,
  ReportSummaryComponent,
  ComparablesComponent
} from '.';
import { EstimationTabComponent } from './component/estimation-tab/estimation-tab.component';

// Models
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';
import { Domain } from '../../../shared/domain/domain';
import {
  ReportSummaryModel,
  ComparableElementModel,
  ConsistencyTableModel,
  Rilevation,
  SingleFieldModel,
  MerchantabilityStaticModel,
  PriceEvaluationModel
} from '.';

// Services
import { EvaluationService } from '.';
import { LandingService } from '../../../simulation/services/landing/landing.service';
import { PositionService } from '../../../shared/position/position.service';
import { DomainService } from '../../../shared/domain/domain.service';
import { AppraisalCompilationService } from '../../service/appraisal-compilation.service';

@Component({
  selector: 'app-evaluations',
  templateUrl: './evaluations.component.html',
  styleUrls: ['./evaluations.component.css'],
  providers: [EvaluationService, AppraisalCompilationService]
})
export class EvaluationsComponent implements OnInit,AfterViewChecked,OnDestroy {
  @ViewChild(PriceEvaluationTabComponent)
  priceEvalComponent: PriceEvaluationTabComponent;
  @ViewChildren(AccordionApplicationFormComponent)
  accordionApfs: QueryList<AccordionApplicationFormComponent>;
  @ViewChild(MerchantabilityComponent)
  private merchantabilityComponent: MerchantabilityComponent;
  @ViewChild(PhotovoltaicComponent)
  private photovoltaicComponent: PhotovoltaicComponent;
  @ViewChild(AppraisalTemplateValidationComponent)
  templateValidationComponent: AppraisalTemplateValidationComponent;
  @ViewChild(EstimationTabComponent)
  estimationTabComponent: EstimationTabComponent;
  @ViewChild(MarketConsiderationComponent)
  marketConsiderationComponent: MarketConsiderationComponent;
  @ViewChild(ReportSummaryComponent)
  reportSummaryComponent: ReportSummaryComponent;
  @ViewChild(ComparablesComponent) comparablesComponent: ComparablesComponent;

  private appraisalType: string;
  private _subscription;
  private _updateSubscription;
  retrivedIndData: boolean = false;;

  positionId: string;
  wizardCode: string;
  bpmTaskId: string;
  bpmTaskCod: string;
  currentTask = 'UBZ-PER-VAL';
  consistencyList: ConsistencyTableModel[] = [];
  marketConsideration: string = '';
  reportSummary: ReportSummaryModel = new ReportSummaryModel();
  comparables: ComparableElementModel[] = new Array();
  rilevazioni: Rilevation[] = [];
  singleFields: SingleFieldModel = new SingleFieldModel();
  noteRilevazionePrezzi: string;
  merchantabilityModel: MerchantabilityStaticModel;
  riepilogoInvestimenti: string;
  agrarianLoan: boolean;
  industrialLoan: boolean;
  photovoltaicData: any;
  isShipping: boolean;
  isAereomobile: boolean;
  consistencyTypes: Domain[] = [];
  opType: string;
  isParsed = false;
  haveDisabledFields = false; // Quando true i campi sono disabilitati (ad esempio per perizie SAL o Fine lavori)
  objAppEval: any = new PriceEvaluationModel();
  saveDraftCallback = this.saveDraft.bind(this);
  isTemplateLight: boolean;
  isTemplateUpdate: boolean;

  constructor(
    private _activatedRoute: ActivatedRoute,
    public _landingService: LandingService,
    private _evaluationService: EvaluationService,
    public _positionService: PositionService,
    private _domainService: DomainService,
    private _appraisalCompilationService: AppraisalCompilationService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants,
    private ref: ChangeDetectorRef


  ) { }

  ngOnInit() {

    this._subscription = this._activatedRoute.params
      .switchMap((params: Params) => {
        this.positionId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        this.bpmTaskId = params['taskId'];
        this.bpmTaskCod = params['taskCod'];
        this._landingService.checkIfCompletedTask(
          this.positionId,
          this.currentTask
        );
        return this._positionService.getAppraisalInfo(this.positionId);
      })
      .switchMap(res => {
        this.setAppraisalInfo(res);
        let individualDomainString = '';
        if (this._landingService.posSegment === 'IND') {
          individualDomainString = 'IND_';
        }
        return this._domainService.newGetDomain('UBZ_DOM_' + individualDomainString + 'CONSISTENCE_TYPE');
      })
      .switchMap(response => {
        this.consistencyTypes = response;
        if (this.isShipping || this.isAereomobile) {
          return Observable.empty();
        }
        if (!this.industrialLoan) {
          return Observable.forkJoin(
            this._evaluationService.getEvalData(this.positionId)
          );
        } else {
          return Observable.forkJoin(
            this._evaluationService.getEvalData(this.positionId),
            this._evaluationService.getPhotovoltaicData(this.positionId)
          );
        }
      })
      .subscribe((res: any) => {
        if (this.agrarianLoan) {
          this.merchantabilityModel = res[0].investmentSumm;
        }
        this.consistencyList = res[0].appcons;
        this.rilevazioni = res[0].appRivel;
        this.noteRilevazionePrezzi = res[0].zoneAverPriceNote;
        this.singleFields = res[0].immEstimate;
        this.objAppEval = res[0].objAppEval;
        if (this.industrialLoan) {
          if (!res[1].partialSaveData) {
            this.photovoltaicData = res[1];
          } else {
            const draftStuff = JSON.parse(res[1].partialSaveData);
            this.photovoltaicData = draftStuff.partialSaveData;
          }
        }
        let draftStuff = {};
        if (res[0].partialSaveData) {
          draftStuff = JSON.parse(res[0].partialSaveData);
          if (draftStuff['staticData'] && draftStuff['staticData'].evalStaticData) {
            this.consistencyList = draftStuff['staticData'].evalStaticData['appcons'];
            this.rilevazioni = draftStuff['staticData'].evalStaticData['appRivel'];
            this.noteRilevazionePrezzi = draftStuff['staticData'].evalStaticData['zoneAverPriceNote'];
            this.singleFields = draftStuff['staticData'].evalStaticData['immEstimate'];
            this.merchantabilityModel = draftStuff['staticData'].evalStaticData['investmentSumm'];
            this.objAppEval = draftStuff['staticData'].evalStaticData['objAppEval'];
          } else {
            this.consistencyList = draftStuff['appcons'];
            this.rilevazioni = draftStuff['appRivel'];
            this.noteRilevazionePrezzi = draftStuff['zoneAverPriceNote'];
            this.singleFields = draftStuff['immEstimate'];
            this.merchantabilityModel = draftStuff['investmentSumm'];
            this.objAppEval = draftStuff['objAppEval'];
          }
        }
        // Se ndg Individual si richiama il servizio che restituisce i campi per gli accordion dedicati
        if (this._landingService.posSegment === 'IND') {
          this.getEvalGetDataIndividual(this.positionId, draftStuff);
        }

        // Template di aggiornamento: nasconde actualValue e lo imposta a null
        if (this.isTemplateUpdate) {
          this.singleFields.valActualStat = null;
        }
      });
}

ngAfterViewChecked() {
    this.ref.detectChanges();
  }

ngOnDestroy() {
    this._subscription.unsubscribe();
    if (this._updateSubscription) {
      this._updateSubscription.unsubscribe();
    }
  }

  /**
   * @name setAppraisalInfo
   * @description Metodo invocato sul recupero dell'oggetto perizia, imposta le variabili ad esso connesse
   * @param appraisalInfo Oggetto perizia recuperato
   */
  private setAppraisalInfo(appraisalInfo) {
    this._landingService.posSegment = appraisalInfo.appraisal.posSegment;
    this.isTemplateLight = appraisalInfo.isTemplateLight;
    this.opType = appraisalInfo.appraisal.loanScope;
    this.agrarianLoan = appraisalInfo.appraisal.loanScope === 'MUT' ? true : false;
    this.industrialLoan = appraisalInfo.appraisal.loanScope === 'IND' ? true : false;
    // Check if is template update (template di aggiornamento perizia)
    this.isTemplateUpdate = appraisalInfo.templateType && appraisalInfo.templateType === 'AGL';
    if (appraisalInfo.appraisal.resItemType === 'MOB') {
      if (
        this._appraisalCompilationService.getMobileType(
          appraisalInfo.appraisalObject
        ) === this.constants.appraisalTypes.AEREOMOBILE
      ) {
        this.isShipping = false;
        this.isAereomobile = true;
      } else {
        this.isShipping = true;
        this.isAereomobile = false;
      }
    }
    this.appraisalType = appraisalInfo.appraisal.appraisalType;
    this.hasDisabledFields(appraisalInfo);
    if (this.isShipping || this.isAereomobile) {
      this._updateSubscription = this._evaluationService
        .updateEvalFromUnit(this.positionId)
        .subscribe(() => {
          this.isParsed = true;
        });
    } else {
      this.isParsed = true;
    }
  }

  // Effettua i controlli su i campi dell'oggetto appraisal
  // ed eventualmente imposta la variabile di disabilitazione campi in pagina
  hasDisabledFields(appraisalInfo) {
    // Se la perizia è in second opinion ed è di tipo SAL bisogna abilitare i campi in pagina
    if (appraisalInfo['opinionType'] === 'SO') {
      this.haveDisabledFields = false;
      return;
    }
    if (
      (this.isShipping || this.isAereomobile) &&
      appraisalInfo.appraisal.originProcess !== 'MIG' &&
      !appraisalInfo.migParent &&
      (this.appraisalType === this.constants.appraisalTypes.FINE_LAVORI ||
        this.appraisalType === this.constants.appraisalTypes.SAL)
    ) {
      this.haveDisabledFields = true;
    } else {
      this.haveDisabledFields = false;
    }
  }

  checkSaveEnable(): boolean {

    if (this.accordionApfs) {
      for (const accApf of this.accordionApfs.toArray()) {
        if (!accApf.isAllValid()) {

          return false;
        }
      }
    }
    if (
      (this.priceEvalComponent && !this.priceEvalComponent.sectionIsValid) ||
      (this.merchantabilityComponent && !this.merchantabilityComponent.isValidSection()) ||
      (this.photovoltaicComponent && !this.photovoltaicComponent.isValid()) ||
      (this.marketConsiderationComponent && !this.marketConsiderationComponent.isValid()) ||
      (this.reportSummaryComponent && !this.reportSummaryComponent.isValid()) ||
      (this.comparablesComponent && !this.comparablesComponent.isValid()) ||
      (this.templateValidationComponent && !this.templateValidationComponent.form.valid)
    ) {

      return false;
    } else {

      return true;
    }

  }

  saveData() {
    this.savePageData().subscribe(res => {
      this._landingService.goNextPage(
        this.positionId,
        this.currentTask,
        this.wizardCode,
        this._activatedRoute
      );
    });
  }

  savePageData(): Observable<any> {
    const apfs = {};
    for (const accApf of this.accordionApfs.toArray()) {
      for (const key in accApf.model) {
        if (accApf.model.hasOwnProperty(key)) {
          apfs[key] = accApf.model[key];
        }
      }
    }
    const statics =
      this.isShipping || this.isAereomobile ? {} : this.getStaticSaveObject();
    return this._evaluationService
      .saveData(this.positionId, apfs, statics, this._landingService.posSegment)
      .switchMap(() => this.saveTemplateValidation());
  }

  saveDraft(): Observable<any> {
    const apfMap = {};
    for (const appForm of this.accordionApfs.toArray()) {
      if (appForm.drivers && appForm.drivers['DD_ORD_PAG']) {
        apfMap[appForm.drivers['DD_ORD_PAG']] = JSON.stringify(appForm.model);
      } else {
        apfMap[1] = JSON.stringify(appForm.model);
      }
    }
    const statics =
      this.isShipping || this.isAereomobile ? {} : this.getStaticSaveObject();
    return this._landingService
      .saveDraft(
        this.positionId,
        JSON.stringify(statics),
        apfMap,
        'VALUTAZIONI'
      )
      .switchMap(() => this.saveTemplateValidation());
  }

  /**
   * @function
   * @name getStaticSaveObject
   * @description Controlla la presenza degli accordion statici
   * Per ogni accordion presente, ne recupera i dati per inserirli nell'oggetto
   * da salvare a BE
   * Per salvataggio individual wrappa l'oggeto static all'interno dell'oggetto salvataggio individual
   */
  private getStaticSaveObject(): any {
    const statics = {};
    if (this.priceEvalComponent) {
      statics['appRivel'] = this.priceEvalComponent.getData();
      statics['objAppEval'] = this.priceEvalComponent.getEvalObj();
      statics['zoneAverPriceNote'] = this.priceEvalComponent.noteRilevazione;
    }
    if (this.merchantabilityComponent) {
      statics['investmentSumm'] = this.merchantabilityComponent.getModel();
    } else {
      statics['investmentSumm'] = {};
    }
    if (this.estimationTabComponent) {
      statics['appcons'] = this.estimationTabComponent.getData();
    }
    statics['immEstimate'] = this.singleFields;
    if (this.photovoltaicData && this.photovoltaicData.fotoAppraisal) {
      const obj = JSON.parse(JSON.stringify(this.photovoltaicData));
      delete obj.roundPrudentialValue;
      statics['fotoeval'] = obj;
    }
    // INDIVIDUAL
    if (this._landingService.posSegment === 'IND') {
      const saveIndividualObj = {
        staticData: {},
        staticIndividualData: {}
      };
      saveIndividualObj.staticData['evalStaticData'] = statics;
      // Se il template è light gli accordion consDiMercato, refSindiStima e comparables non sono presenti
      if (!this.isTemplateLight && !this.isTemplateUpdate) {
        const consDiMercato = this.marketConsiderationComponent.getData();
        const refSindiStima = this.reportSummaryComponent.getData();
        const comparableElement = this.comparablesComponent.getData();
        saveIndividualObj.staticIndividualData = {
          consDiMercato: consDiMercato,
          refSindiStima: refSindiStima,
          comparableElement: comparableElement
        };
      } else {
        saveIndividualObj.staticIndividualData = {};
      }
      return saveIndividualObj;
    } else {
      return statics;
    }
  }

  private saveTemplateValidation(): Observable<boolean> {
    if (this.templateValidationComponent) {
      return this._appraisalCompilationService.saveTemplateValidation(
        this.templateValidationComponent.appValidation
      );
    } else {
      return Observable.of(true);
    }
  }

  goToGenericTask() {
    this._landingService.goToGenericTask(
      this.positionId,
      this.bpmTaskId,
      this.bpmTaskCod
    );
  }

  goToPreviousTask() {
    this._landingService.goToPreviousTask(
      this.positionId,
      this.wizardCode,
      this._activatedRoute
    );
  }

  // Invoca il servizio che restituisce i campi per gli accordion visibili in caso
  // di ndg INDIVIDUAL
  getEvalGetDataIndividual(positionId: string, draftStuff: Object) {
    this._evaluationService.getEvalDataIndividual(this.positionId).subscribe(result => {
      if (draftStuff && draftStuff['staticIndividualData']) {
        this.marketConsideration = draftStuff['staticIndividualData'].consDiMercato.markConsideration;
        this.reportSummary = draftStuff['staticIndividualData'].refSindiStima;
        this.comparables = draftStuff['staticIndividualData'].comparableElement;
        this.retrivedIndData = true;
      } else {
        this.marketConsideration = result.consDiMercato.markConsideration;
        this.reportSummary = result.refSindiStima;
        this.comparables = result.comparableElement;
        this.retrivedIndData = true;
      }
    });
  }
}
