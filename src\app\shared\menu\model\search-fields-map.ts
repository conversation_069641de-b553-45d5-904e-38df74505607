import { SearchField } from './search-field';

export class SearchFieldsMap {
  // 'TU': SearchField[] = [
  //   new SearchField('idRequest',            'text',   'UBZ.SITE_CONTENT.10100101',  'col-sm-3 form-group' ),
  //   new SearchField('ndg',                  'text',   'UBZ.SITE_CONTENT.1001110',   'col-sm-3 form-group' ),
  //   new SearchField('name',                 'text',   'UBZ.SITE_CONTENT.1101101',   'col-sm-3 form-group' ),
  //   new SearchField('from',                 'date',   'UBZ.SITE_CONTENT.10100110',  'col-sm-3 form-group' ),
  //   new SearchField('to',                   'date',   'UBZ.SITE_CONTENT.10100111',  'col-sm-3 form-group' ),
  //   new SearchField('assetFamily',          'select', 'UBZ.SITE_CONTENT.100101',    'col-sm-3 form-group', 'UBZ_DOM_RESITEM_TYPE' ),
  //   new SearchField('appraisalType',        'select', 'UBZ.SITE_CONTENT.1011001',   'col-sm-3 form-group', 'UBZ_DOM_APPRAISAL_TYPE' ),
  //   new SearchField('appraisalScope',       'select', 'UBZ.SITE_CONTENT.1110001',   'col-sm-3 form-group', 'UBZ_DOM_SCOPE_TYPE' ),
  //   new SearchField('appraisalId',          'text',   'UBZ.SITE_CONTENT.10010001',  'col-sm-3 form-group' ),
  //   // new SearchField('assetType',            'select', 'Tipo Asset',           'col-sm-3 form-group', 'UAM_ASSET_DETAIL' ),
  //   new SearchField('assetId',              'text',   'UBZ.SITE_CONTENT.100100',    'col-sm-3 form-group' ),
  //   new SearchField('category',             'select', 'UBZ.SITE_CONTENT.100110',    'col-sm-3 form-group', 'UBZ_DOM_CATEGORY_TYPE' ),
  //   new SearchField('address',              'text',   'UBZ.SITE_CONTENT.101011',    'col-sm-3 form-group' ),
  //   new SearchField('sheet',                'text',   'UBZ.SITE_CONTENT.110001',    'col-sm-3 form-group' ),
  //   new SearchField('folder',               'text',   'UBZ.SITE_CONTENT.110010',    'col-sm-3 form-group' ),
  //   new SearchField('subaltern',            'text',   'UBZ.SITE_CONTENT.110011',    'col-sm-3 form-group' ),
  //   new SearchField('warrantyProgressive',  'text',   'UBZ.SITE_CONTENT.10010100',  'col-sm-3 form-group' ),
  //   new SearchField('technicalForm',        'select', 'UBZ.SITE_CONTENT.10010101',  'col-sm-3 form-group', 'UBZ_DOM_COLLAT_TEC_FORM' ),
  //   new SearchField('particleTable',        'text',   'UBZ.SITE_CONTENT.10101000',  'col-sm-3 form-group' ),
  //   new SearchField('bodyTable',            'text',   'UBZ.SITE_CONTENT.10101001',  'col-sm-3 form-group' ),
  //   new SearchField('nimo',                 'text',   'UBZ.SITE_CONTENT.10101010',  'col-sm-3 form-group' ),
  //   new SearchField('serialNumber',         'text',   'UBZ.SITE_CONTENT.10101011',  'col-sm-3 form-group' )
  // ];
  RI: SearchField[] = [
    new SearchField(
      'requestId',
      'text',
      'UBZ.SITE_CONTENT.10101100',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'appraisalId',
      'text',
      'UBZ.SITE_CONTENT.10010001',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'status',
      'select',
      'UBZ.SITE_CONTENT.10101101',
      'col-sm-3 form-group',
      'UBZ_DOM_STATUS'
    ), // temp variable name
    new SearchField(
      'ndg',
      'text',
      'UBZ.SITE_CONTENT.1001110',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'heading',
      'text',
      'UBZ.SITE_CONTENT.1101101',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'creationFrom',
      'date',
      'UBZ.SITE_CONTENT.10100110',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'creationTo',
      'date',
      'UBZ.SITE_CONTENT.10100111',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'assetFamily',
      'select',
      'UBZ.SITE_CONTENT.100101',
      'col-sm-3 form-group',
      'UBZ_DOM_RESITEM_TYPE'
    ),
    new SearchField(
      'appraisalScope',
      'select',
      'UBZ.SITE_CONTENT.1110001',
      'col-sm-3 form-group',
      'UBZ_DOM_SCOPE_TYPE'
    ),
    new SearchField(
      'companyName',
      // 'text',
      'selectSoc',
      'UBZ.SITE_CONTENT.1000110100',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'expertName',
      'text',
      'UBZ.SITE_CONTENT.1000110101',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'requestFrom',
      'date',
      'UBZ.SITE_CONTENT.10101110',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'requestTo',
      'date',
      'UBZ.SITE_CONTENT.10101111',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'closedFrom',
      'date',
      'UBZ.SITE_CONTENT.10110000',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'closedTo',
      'date',
      'UBZ.SITE_CONTENT.10110001',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'progCollateral',
      'text',
      'UBZ.SITE_CONTENT.10010100',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'originProcess',
      'selectOrigProcess',
      'UBZ.SITE_CONTENT.11110011010',
      'col-sm-3 form-group',
      'UBZ_DOM_PROCESS_TYPE',
      'SEARCH'
    ),
    new SearchField(
      'externalPositionId',
      'textExtAppraisalId',
      'UBZ.SITE_CONTENT.11110011011',
      'col-sm-3 form-group'
    )
  ];
  PE: SearchField[] = [
    new SearchField(
      'requestId',
      'text',
      'UBZ.SITE_CONTENT.10101100',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'appraisalId',
      'text',
      'UBZ.SITE_CONTENT.10010001',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'status',
      'select',
      'UBZ.SITE_CONTENT.10101101',
      'col-sm-3 form-group',
      'UBZ_DOM_STATUS'
    ), // temp variable name
    new SearchField(
      'ndg',
      'text',
      'UBZ.SITE_CONTENT.1001110',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'heading',
      'text',
      'UBZ.SITE_CONTENT.1101101',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'creationFrom',
      'date',
      'UBZ.SITE_CONTENT.10100110',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'creationTo',
      'date',
      'UBZ.SITE_CONTENT.10100111',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'assetFamily',
      'select',
      'UBZ.SITE_CONTENT.100101',
      'col-sm-3 form-group',
      'UBZ_DOM_RESITEM_TYPE'
    ),
    new SearchField(
      'appraisalScope',
      'select',
      'UBZ.SITE_CONTENT.1110001',
      'col-sm-3 form-group',
      'UBZ_DOM_SCOPE_TYPE'
    ),
    new SearchField(
      'companyName',
      // 'text',
      'selectSoc',
      'UBZ.SITE_CONTENT.1000110100',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'expertName',
      'text',
      'UBZ.SITE_CONTENT.1000110101',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'requestFrom',
      'date',
      'UBZ.SITE_CONTENT.10101110',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'requestTo',
      'date',
      'UBZ.SITE_CONTENT.10101111',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'closedFrom',
      'date',
      'UBZ.SITE_CONTENT.10110000',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'closedTo',
      'date',
      'UBZ.SITE_CONTENT.10110001',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'progCollateral',
      'text',
      'UBZ.SITE_CONTENT.10010100',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'originProcess',
      'selectOrigProcess',
      'UBZ.SITE_CONTENT.11110011010',
      'col-sm-3 form-group',
      'UBZ_DOM_PROCESS_TYPE',
      'SEARCH'
    ),
    new SearchField(
      'externalPositionId',
      'textExtAppraisalId',
      'UBZ.SITE_CONTENT.11110011011',
      'col-sm-3 form-group'
    )
  ];
  ASAS: SearchField[] = [
    new SearchField(
      'idAsset',
      'text',
      'UBZ.SITE_CONTENT.100100',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'assetType',
      'select',
      'UBZ.SITE_CONTENT.100101',
      'col-sm-3 form-group',
      'UBZ_DOM_RESITEM_TYPE'
    ),
    new SearchField(
      'address',
      'text',
      'UBZ.SITE_CONTENT.101011',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'reRegistrySheet',
      'text',
      'UBZ.SITE_CONTENT.110001',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'reRegistryPart',
      'text',
      'UBZ.SITE_CONTENT.110010',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'reRegistrySub',
      'text',
      'UBZ.SITE_CONTENT.110011',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'tableRegistryPart',
      'text',
      'UBZ.SITE_CONTENT.10101000',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'tableRegistryBody',
      'text',
      'UBZ.SITE_CONTENT.10101001',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'numImm',
      'text',
      'UBZ.SITE_CONTENT.10101010',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'serNum',
      'text',
      'UBZ.SITE_CONTENT.10101011',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'progGar',
      'text',
      'UBZ.SITE_CONTENT.10010100',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'formTecGar',
      'select',
      'UBZ.SITE_CONTENT.10010101',
      'col-sm-3 form-group',
      'UBZ_DOM_COLLAT_TEC_FORM'
    ),
    new SearchField(
      'ndg',
      'text',
      'UBZ.SITE_CONTENT.1001110',
      'col-sm-3 form-group'
    )
  ];
  AS: SearchField[] = [
    new SearchField(
      'resItemId',
      'text',
      'UBZ.SITE_CONTENT.100100',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'assetType',
      'select',
      'UBZ.SITE_CONTENT.100101',
      'col-sm-3 form-group',
      'UBZ_DOM_RESITEM_TYPE'
    ),
    new SearchField(
      'address',
      'text',
      'UBZ.SITE_CONTENT.101011',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'reRegistrySheet',
      'text',
      'UBZ.SITE_CONTENT.110001',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'reRegistryPart',
      'text',
      'UBZ.SITE_CONTENT.110010',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'reRegistrySub',
      'text',
      'UBZ.SITE_CONTENT.110011',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'tableRegistryPart',
      'text',
      'UBZ.SITE_CONTENT.10101000',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'tableRegistryBody',
      'text',
      'UBZ.SITE_CONTENT.10101001',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'numImm',
      'text',
      'UBZ.SITE_CONTENT.10101010',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'serNum',
      'text',
      'UBZ.SITE_CONTENT.10101011',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'progGar',
      'text',
      'UBZ.SITE_CONTENT.10010100',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'formTecGar',
      'select',
      'UBZ.SITE_CONTENT.10010101',
      'col-sm-3 form-group',
      'UBZ_DOM_COLLAT_TEC_FORM'
    ),
    new SearchField(
      'originNdg',
      'text',
      'UBZ.SITE_CONTENT.1001110',
      'col-sm-3 form-group'
    )
  ];
  GA: SearchField[] = [
    new SearchField(
      'collateralId',
      'text',
      'UBZ.SITE_CONTENT.10010100',
      'col-sm-3 form-group'
    ),
    new SearchField(
      'technicalForm',
      'select',
      'UBZ.SITE_CONTENT.10010101',
      'col-sm-3 form-group',
      'UBZ_DOM_COLLAT_TEC_FORM'
    )
  ];
}
