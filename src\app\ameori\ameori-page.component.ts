import { Component, OnInit } from '@angular/core';
import { AmeoriService } from './service/ameori.service';

@Component({
  selector: 'app-ameori-page',
  templateUrl: './ameori-page.component.html',
  styleUrls: ['./ameori-page.component.css'],
  providers: [AmeoriService]
})
export class AmeoriPageComponent implements OnInit {
  ameoriList: any[] = [];
  isSearchClicked: boolean = false;
  isEmptyList: boolean = true;
  reportType: string = '';
  reportNumber: number = 0;
  isPopUpOpen: boolean = false;
  selectedAmeori: any;
  page: number = 1;
  pageSize = 10;
  count: number = 0;
  constructor(private ameoriService: AmeoriService) {}

  ngOnInit() {}

  fillAmeori(searchResult: any) {
    this.isSearchClicked = true;
    console.log(searchResult);
    this.ameoriList =
      typeof searchResult.ameoriList.list !== 'undefined'
        ? searchResult.ameoriList.list
        : [];
    this.count = searchResult.ameoriList.count;
    this.isEmptyList = this.ameoriList.length > 0 ? false : true;
    this.reportType = searchResult.reportType;
    this.reportNumber = searchResult.reportNumber;
  }

  openModal(ameori: any) {
    this.selectedAmeori = ameori;
    this.isPopUpOpen = true;
  }

  closeModal() {
    this.isPopUpOpen = false;
    this.search();
  }

  changePageSize() {
    console.log(this.pageSize);
    this.search();
  }

  changePage(page: any) {
    console.log(page);
    this.page = page.page;
    this.search();
  }

  search() {
    this.ameoriService
      .searchList(this.reportType, this.reportNumber, this.page, this.pageSize)
      .subscribe(res => {
        let ameoriList = res;
        this.ameoriList =
          typeof ameoriList.list !== 'undefined' ? ameoriList.list : [];
        this.count = ameoriList.count;
      });
  }
}
