<div *ngIf="isOpen" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #stateBox="bs-modal" (onHidden)="closeModal(false)" [config]="{show: 'true'}">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.11000101' | translate }}</h2>
        <button type="button" class="close pull-right" aria-label="Close" (click)="closeModal(false)">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <form #form1="ngForm" name="form1" novalidate (ngSubmit)="onSubmit()">
        <div class="row">
            <div class="col-sm-12">
                <h4>{{'UBZ.SITE_CONTENT.11000110' | translate }}</h4>
            </div>
            <div *ngIf="section" class="col-sm-3 form-group">
              <label>
                  {{'UBZ.SITE_CONTENT.11010000' | translate }} {{ (section.entityType) ? (section.entityType.translationCod | translate) : ''}}
                </label>
                {{ (section.resItemType) ? (section.resItemType.translationCod | translate) : '' }}
                {{ (section.resItemCategory) ? ('-' + (section.resItemCategory.translationCod | translate)) : '' }}
            </div>
            <div class="col-sm-3 form-group">
                <label>{{'UBZ.SITE_CONTENT.11001000' | translate }}</label>
                {{document.lastUpload.userId}}
            </div>
            <div class="col-sm-3 form-group">
                <label>{{'UBZ.SITE_CONTENT.1111110' | translate }}</label>
                <ng-container *ngIf = "document.lastUpload && document.lastUpload.uploadDate">
                  {{document.lastUpload.uploadDate | date: 'dd-MM-y'}} {{document.lastUpload.uploadDate | customTime}}
                </ng-container>
            </div>
            <div class="col-sm-3 form-group">
                <label>{{'UBZ.SITE_CONTENT.10000001' | translate }}</label>
                <span [class]="isValidClass[document.acquired]"></span>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <h4>{{'UBZ.SITE_CONTENT.11000111' | translate }}</h4>
            </div>
            <div class="col-sm-7 form-group">
                <label>{{'UBZ.SITE_CONTENT.10001000' | translate }}*</label>
                <input type="text" name="inserisciTitle" class="form-control required" placeholder="{{'UBZ.SITE_CONTENT.110100100' | translate }}..."
                  required #inserisciTitle [ngClass]="(form1.submitted && !inserisciTitle.validity.valid) ? 'error' : 'valid'" [(ngModel)]="noteTitle" maxlength="50"/>
                <div *ngIf="form1.submitted && !inserisciTitle.validity.valid" class="tooltip fade bottom in" role="tooltip" style="top: 67px; left: 127px;">
                  <div class="tooltip-arrow" style="left: 50%;"></div>
                  <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
                </div>
            </div>
            <div class="col-sm-5 form-group">
                <label></label>
                <div class="custom-checkbox">
                    <input type="checkbox" name="invia" id="invia" class="checkbox" [(ngModel)]="sendMail">
                    <label for="invia">{{'UBZ.SITE_CONTENT.11001001' | translate }}</label>
                </div>
            </div>
        </div>
        <div class="row">
            <div *ngIf="sendMail" class="col-sm-6 form-group">
                <label>{{'UBZ.SITE_CONTENT.11001010' | translate }}*</label>
                <input type="text" name="inserisciEmail" class="form-control required" placeholder="{{'UBZ.SITE_CONTENT.110100101' | translate }}" data-placement="bottom"
                  required #to [ngClass]="(form1.submitted && !to.validity.valid) ? 'error' : 'valid'" [(ngModel)]="mailTo" />
                <div *ngIf="form1.submitted && !to.validity.valid" class="tooltip fade bottom in" role="tooltip" style="top: 67px; left: 127px;">
                  <div class="tooltip-arrow" style="left: 50%;"></div>
                  <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
                </div>
            </div>
            <div *ngIf="sendMail" class="col-sm-6 form-group">
                <label>{{'UBZ.SITE_CONTENT.1100110100' | translate }}</label>
                <input type="text" name="cc" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.110100101' | translate }}" #cc [(ngModel)]="mailCc"/>
            </div>
            <div class="col-sm-12 form-group">
              <textarea class="form-control required" data-placement="bottom" name="inserisciNota" required #testoNota
                [ngClass]="(form1.submitted && !testoNota.validity.valid) ? 'error' : 'valid'" placeholder="{{'UBZ.SITE_CONTENT.10001001' | translate }}..." [(ngModel)]="noteText"></textarea>
              <div *ngIf="form1.submitted && !testoNota.validity.valid" class="tooltip fade bottom in" role="tooltip" style="top: 67px; left: 127px;">
                <div class="tooltip-arrow" style="left: 50%;"></div>
                <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
              </div>
            </div>
            <div class="col-sm-12 form-group">
                <button type="submit" name="send-note" class="btn btn-primary waves-effect pull-right">
                  {{'UBZ.SITE_CONTENT.11000111' | translate }}
                </button>
            </div>
        </div>
        </form>
        <!--  NOTE -->
        <div class="row" *ngIf=" notes && notes.length > 0">
            <div class="col-sm-12">
                <h4>{{'UBZ.SITE_CONTENT.11000101' | translate }}</h4>
                <accordion class="panel-group" id="accordion">
                  <accordion-group #group class="panel" *ngFor="let note of notes">
                    <div accordion-heading class="acc-note-headline" role="tab">
                      <h4 class="panel-title">
                        <a role="button">
                          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
                          {{note.noteTitle}}
                        </a>
                      </h4>
                    </div>
                    <div class="panel-collapse collapse in" role="tabpanel">
                      <div class="panel-body">
                        <div class="panel-box">
                            <div class="row">
                                <div class="col-sm-12">
                                    {{note.noteDesc}}
                                </div>
                            </div>
                        </div>
                      </div>
                    </div>
                  </accordion-group>
                </accordion>
            </div>
        </div>
      </div>
    </div>
  </div>
</div>
