import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';
import { DomainService } from '../../domain/domain.service';
import { Note } from '../../notes/model/note';
import { LockUnlockTask } from '../../access-rights/model/lock-unlock-task';
import { Observable } from 'rxjs';

@Injectable()
export class ModalButtonService {
  storedData = { };
  constructor(private _domainService: DomainService, private _http: Http) { }

  getReasons(mode: string) {
    // mode can be 'SOS' for "sospensione" and 'REV' for "revoca"
    return this._domainService.newGetDomain('UBZ_DOM_REASON_TYPE', mode);
  }

  dropAssignment(type: string, positionId: string) {
    const urlMap = {
      SOS: '/assets/data/fake-sospensione-incarico.json',
      REV: `/UBZ-ESA-RS/service/appraisal/v1/appraisals/${positionId}/revokeAppraisal`
    };
    return this._http.get(urlMap[type]).map(res => res.json());
  }

  public reopenAppraisal(appraisalId: string, note: Note) {
    const url = `/UBZ-ESA-RS/service/bpmProcess/v1/BpmProcessResume/${appraisalId}`;
    return this._http.post(url, note).map((resp: Response) => resp.text());
  }

  public abandonAppraisal(positionId: string) {
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisals/${positionId}/abandonAppraisal`;
    return this._http.delete(url).map(res => res.json());
  }

  /**
   * @function
   * @name suspendAppraisal
   * @description Invocato su processo di sospensione perizia. Racchiude salvataggio nota + sospensione
   * Serve per controllare che sia possibile max una sospensione della perizia da parte della società peritale
   * @param {Note} note Nota dettaglio sospensione
   * @returns {Observable<any>}
   */
  suspendAppraisal(note: Note, closeTask: LockUnlockTask): Observable<any> {
    const url = '/UBZ-ESA-RS/service/taskManager/v1/tasks/suspend';
    return this._http.put(url, {
      note: note,
      closeTask: closeTask
    }).map((resp: Response) => resp.json());
  }

  /**
   * @function
   * @name suspendAppraisalInvalidateDocs
   * @description Invocato su processo di sospensione perizia. Racchiude salvataggio nota + sospensione + invalidazione documenti
   * @param {Note} note Nota dettaglio sospensione
   * @param {string[]} invalidDocs Array di in da invalidare
   * @returns {Observable<any>}
   */
  suspendAppraisalInvalidateDocs(note: Note, closeTask: LockUnlockTask, invalidDocs: string[]): Observable<any> {
    const url = '/UBZ-ESA-RS/service/taskManager/v1/tasks/suspendDocument';
    return this._http.put(url, {
      note: note,
      closeTask: closeTask,
      invalidDocs: invalidDocs
    }).map((resp: Response) => resp.json());
  }

  setStoredData(note: Note, closeTask: LockUnlockTask) {
    this.storedData = {
      note: note,
      closeTask: closeTask
    }
  }
  getStoredData() {
    return this.storedData;
  }
}
