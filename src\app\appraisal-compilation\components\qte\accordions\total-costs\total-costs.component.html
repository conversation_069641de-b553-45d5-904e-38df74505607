<!-- fixme - togliere codice commentato -->
<form #f="ngForm" (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
          {{'UBZ.SITE_CONTENT.110110110' | translate }}
          <span class="state" [ngClass]="{'red': f.invalid, 'green': f.valid}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <div class="row">
            <div class="col-sm-4 form-group">
              <label>{{'UBZ.SITE_CONTENT.110110111' | translate }}*</label>
              <!-- <div class="calendar">
                <input ngui-datetime-picker date-only="true" type="text" name="quadroTecnicoEconomico" [(ngModel)]="model.qteDate"
                  class="form-control calendar" placeholder="{{'UBZ.SITE_CONTENT.1000010001' | translate }}" required appDateReadOnly/>
              </div> -->
              <app-calendario
                [(ngModel)]="model.qteDate"
                [name]="'quadroTecnicoEconomico'"
                [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                [required]="true">
              </app-calendario>
            </div>
          </div>
          <div class="row form-group">
            <table class="uc-table">
              <thead>
                <tr>
                  <th>{{'UBZ.SITE_CONTENT.110111000' | translate }}</th>
                  <th>{{'UBZ.SITE_CONTENT.110101110' | translate }}</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>{{'UBZ.SITE_CONTENT.110111001' | translate }}</td>
                  <td>
                    <app-importo               
                      [name] ="'crn'"
                      [required] = "false"
                      [(ngModel)] = "model.crn"> 
                    </app-importo>
                  </td>
                  <!-- <td><input currencyMask [options] = "{thousands: '.'}" type="text" name="crn" [(ngModel)]="model.crn" class="form-control"></td> -->
                </tr>
                <tr>
                  <td>{{'UBZ.SITE_CONTENT.110111010' | translate }}</td>
                  <td>
                    <app-importo               
                      [name] ="'generalTechnicalExpenses'"
                      [required] = "false"
                      [(ngModel)] = "model.generalTechnicalExpenses"> 
                    </app-importo>
                  </td>
                  <!-- <td><input currencyMask [options] = "{thousands: '.'}" type="text" name="generalTechnicalExpenses" [(ngModel)]="model.generalTechnicalExpenses" class="form-control"></td> -->
                </tr>
                <tr>
                  <td>{{'UBZ.SITE_CONTENT.110111011' | translate }}</td>
                  <td>
                    <app-importo               
                      [name] ="'geognosticSurvey'"
                      [required] = "false"
                      [(ngModel)] = "model.geognosticSurvey"> 
                    </app-importo>
                  </td>
                  <!-- <td><input currencyMask [options] = "{thousands: '.'}" type="text" name="geognosticSurvey" [(ngModel)]="model.geognosticSurvey" class="form-control"></td> -->
                </tr>
                <tr>
                  <td>{{'UBZ.SITE_CONTENT.110111100' | translate }}</td>
                  <td>
                    <app-importo               
                      [name] ="'areaAcquisition'"
                      [required] = "false"
                      [(ngModel)] = "model.areaAcquisition"> 
                    </app-importo>
                  </td>
                  <!-- <td><input currencyMask [options] = "{thousands: '.'}" type="text" name="areaAcquisition" [(ngModel)]="model.areaAcquisition" class="form-control"></td> -->
                </tr>
                <tr>
                  <td>{{'UBZ.SITE_CONTENT.110111101' | translate }}</td>
                  <td>
                    <app-importo               
                      [name] ="'unexpectedExpenses'"
                      [required] = "false"
                      [(ngModel)] = "model.unexpectedExpenses"> 
                    </app-importo>
                  </td>
                  <!-- <td><input currencyMask [options] = "{thousands: '.'}" type="text" name="unexpectedExpenses" [(ngModel)]="model.unexpectedExpenses" class="form-control"></td> -->
                </tr>
                <tr>
                  <td>{{'UBZ.SITE_CONTENT.110111110' | translate }}</td>
                  <td>
                    <app-importo               
                      [name] ="'otherExpenses'"
                      [required] = "false"
                      [(ngModel)] = "model.otherExpenses"> 
                    </app-importo>
                  </td>
                  <!-- <td><input currencyMask [options] = "{thousands: '.'}" type="text" name="otherExpenses" [(ngModel)]="model.otherExpenses" class="form-control"></td> -->
                </tr>
                <tr>
                  <td>{{'UBZ.SITE_CONTENT.110111111' | translate }}</td>
                  <td>
                    <app-importo               
                      [name] ="'urbanizzationExpenses'"
                      [required] = "false"
                      [(ngModel)] = "model.urbanizzationExpenses"> 
                    </app-importo>
                  </td>
                  <!-- <td><input currencyMask [options] = "{thousands: '.'}" type="text" name="urbanizzationExpenses" [(ngModel)]="model.urbanizzationExpenses" class="form-control"></td> -->
                </tr>
                <tr>
                  <td>{{'UBZ.SITE_CONTENT.111000000' | translate }}</td>
                  <td>
                    <app-importo               
                      [name] ="'connectionsExpenses'"
                      [required] = "false"
                      [(ngModel)] = "model.connectionsExpenses"> 
                    </app-importo>
                  </td>
                  <!-- <td><input currencyMask [options] = "{thousands: '.'}" type="text" name="connectionsExpenses" [(ngModel)]="model.connectionsExpenses" class="form-control"></td> -->
                </tr>
                <tr>
                  <td>{{'UBZ.SITE_CONTENT.111000001' | translate }}</td>
                  <td>
                    <app-importo               
                      [name] ="'ctn'"
                      [required] = "false"
                      [(ngModel)] = "model.ctn"> 
                    </app-importo>
                  </td>
                  <!-- <td><input currencyMask [options] = "{thousands: '.'}" type="text" name="ctn" [(ngModel)]="model.ctn" class="form-control"></td> -->
                </tr>
                <tr>
                  <td>{{'UBZ.SITE_CONTENT.111000010' | translate }}</td>
                  <td>
                    <app-importo               
                      [name] ="'roundedTotal'"
                      [required] = "false"
                      [(ngModel)] = "model.roundedTotal"> 
                    </app-importo>
                  </td>
                  <!-- <td><input currencyMask [options] = "{thousands: '.'}" type="text" name="roundedTotal" [(ngModel)]="model.roundedTotal" class="form-control"></td> -->
                </tr>
              </tbody>
            </table>
          </div>
          <div class="row">
            <div class="col-sm-4 form-group">
              <label>{{'UBZ.SITE_CONTENT.111000011' | translate }}</label>
              <div class="radio-buttons">
                <div class="custom-radio">
                  <input type="radio" name="n1" id="1" class="radio" [value]="true" [(ngModel)]="model.flagUpdate">
                  <label for="1">{{'UBZ.SITE_CONTENT.100011011' | translate }}</label>
                </div>
                <div class="custom-radio">
                  <input type="radio" name="n1" id="2" class="radio" [value]="false" [(ngModel)]="model.flagUpdate">
                  <label for="2">{{'UBZ.SITE_CONTENT.100011100' | translate }}</label>
                </div>
              </div>
            </div>
            <div class="col-sm-4 form-group" *ngIf="model.flagUpdate">
              <label>{{'UBZ.SITE_CONTENT.111000100' | translate }}*</label>
              <!-- <div class="calendar">
                <input ngui-datetime-picker date-only="true" type="text" name="dataAggiornamento" [(ngModel)]="model.qteUpdateDate"
                  class="form-control calendar" placeholder="{{'UBZ.SITE_CONTENT.1000010001' | translate }}" required appDateReadOnly/>
              </div> -->
              <app-calendario
                [(ngModel)]="model.qteUpdateDate"
                [name]="'dataAggiornamento'"
                [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                [required]="true">
              </app-calendario>
            </div>
            <div class="col-sm-4 form-group">
              <label>{{'UBZ.SITE_CONTENT.111000101' | translate }}*</label>
              <app-importo               
                [name] ="'prezzoPrimaCessione'"
                [required] = "true"
                [(ngModel)] = "model.firstSalePrice"> 
              </app-importo>
              <!-- <input 
                type="text" 
                name="prezzoPrimaCessione" 
                [(ngModel)]="model.firstSalePrice" 
                currencyMask
                [options] = "{thousands: '.'}"
                class="form-control"
                required/> -->
            </div>
          </div>
          <div class="row">
            <div class="col-sm-4 form-group">
              <label>{{'UBZ.SITE_CONTENT.111000110' | translate }}</label>
              <div class="radio-buttons">
                <div class="custom-radio">
                  <input type="radio" name="n2" id="3" class="radio" [value]="true" [(ngModel)]="model.guaranteeflag">
                  <label for="3">{{'UBZ.SITE_CONTENT.100011011' | translate }}</label>
                </div>
                <div class="custom-radio">
                  <input type="radio" name="n2" id="4" class="radio" [value]="false" [(ngModel)]="model.guaranteeflag">
                  <label for="4">{{'UBZ.SITE_CONTENT.100011100' | translate }}</label>
                </div>
              </div>
            </div>
            <div class="col-sm-8 form-group">                            
              <label>
                <span><i class="icon-search note-tooltip" [tooltip]="model.deltaReason" triggers="click"></i></span>
                {{'UBZ.SITE_CONTENT.111000111' | translate }}*
              </label>
              <textarea type="text" name="motivazione" [(ngModel)]="model.deltaReason"
                class="form-control" required></textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
  </accordion-group>
</form>
