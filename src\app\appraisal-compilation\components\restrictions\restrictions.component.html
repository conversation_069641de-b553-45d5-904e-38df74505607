<fieldset [disabled]="!_landingService.positionLocked">
<fieldset [disabled]="_landingService.isLockedTask[currentTask]">
<h4 class="section-heading">{{'UBZ.SITE_CONTENT.1010001110' | translate }}</h4>
<table class="uc-table" id="table1">
  <thead>
    <tr>
      <th scope="col" class="col-sm-1"></th>
      <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.1010001111' | translate }}</th>
      <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.1010010001' | translate }}</th>
      <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.1010010000' | translate }}</th>
      <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.1010010010' | translate }}</th>
      <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.101011' | translate }}</th>
      <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.101001' | translate }}</th>
      <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.110001' | translate }}</th>
      <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.10000110000' | translate }}</th>
      <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.1110111011' | translate }}</th>
    </tr>
  </thead>
  <tbody class="sortable connectedSortable container" [dragula]="'first-bag'" [dragulaModel]="limitationObject.residualAssets" id="firstBagId">
    <tr class="no-record" *ngIf="!limitationObject.residualAssets.length">
      <td colspan="5" data-label="" class="text-center">
        <div class="insert-document" [ngClass]="{'no-record-active': activeClassEnable[0]}">
          <span>
            <i class="icon-drag"></i>
            {{'UBZ.SITE_CONTENT.1010010011' | translate }}
          </span>
        </div>
      </td>
    </tr>
    <tr *ngFor="let asset of limitationObject.residualAssets">
      <td class="text-center col-sm-1">
        <i class="icon-drag"></i>
      </td>
      <td class="col-sm-4">{{ unitTypes && unitTypes[asset.unitType] ? (unitTypes[asset.unitType].translationCod | translate) : '' }}</td>
      <td class="col-sm-4">{{ asset.bookValue  | number:'1.2-2' }}</td>
      <td class="col-sm-4">{{ asset.pledgedValue  | number:'1.2-2' }}</td>
      <td class="col-sm-4">{{ asset.insuranceValue  | number:'1.2-2' }}</td>
      <td class="col-sm-4">{{ (asset.address ? asset.address : '') + ' ' + (asset.streetNumber ? asset.streetNumber : '') + (asset.zipCod ? ', ' + asset.zipCod : '') }}</td>
      <td class="col-sm-4">{{ asset.city ? asset.city : '' }}</td>
      <td class="col-sm-4">{{ asset.reRegistrySheet ? asset.reRegistrySheet : '' }}</td>
      <td class="col-sm-4">{{ asset.reRegistryPart ? asset.reRegistryPart : '' }}</td>
      <td class="col-sm-4">{{ asset.reRegistrySub ? asset.reRegistrySub : '' }}</td>
    </tr>
  </tbody>
  <tfoot>
    <tr>
      <td></td>
      <td><strong>{{'UBZ.SITE_CONTENT.101010010' | translate }}</strong></td>
      <td>{{ commercialValue[0] | currency:'EUR':true:'1.2-2' }}</td>
      <td>{{ cautionalValue[0] | currency:'EUR':true:'1.2-2' }}</td>
      <td>{{ insuranceValue[0] | currency:'EUR':true:'1.2-2' }}</td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
      <td></td>
    </tr>
  </tfoot>
</table>
<h4 class="section-heading">{{'UBZ.SITE_CONTENT.1010010100' | translate }}</h4>
<table class="uc-table" id="table2">
  <thead>
    <tr>
      <th scope="col" class="col-sm-1"></th>
      <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.1010001111' | translate }}</th>
      <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.1010010001' | translate }}</th>
      <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.1010010000' | translate }}</th>
      <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.1010010010' | translate }}</th>
    </tr>
  </thead>
  <tbody class="sortable connectedSortable container" [dragula]="'first-bag'" [dragulaModel]="limitationObject.alienAssets" id="secondBagIds">
    <tr class="no-record" *ngIf="!limitationObject.alienAssets.length">
      <td colspan="5" class="text-center">
        <div class="insert-document" [ngClass]="{'no-record-active': activeClassEnable[1]}">
          <span>
            <i class="icon-drag"></i>
            {{'UBZ.SITE_CONTENT.1010010011' | translate }}
          </span>
        </div>
      </td>
    </tr>
    <tr *ngFor="let asset of limitationObject.alienAssets">
      <td class="text-center col-sm-1">
        <i class="icon-drag"></i>
      </td>
      <td class="col-sm-4">{{ unitTypes && unitTypes[asset.unitType] ? (unitTypes[asset.unitType].translationCod | translate) : '' }}</td>
      <td class="col-sm-4">{{ asset.bookValue  | number:'1.2-2' }}</td>
      <td class="col-sm-4">{{ asset.pledgedValue  | number:'1.2-2' }}</td>
      <td class="col-sm-4">{{ asset.insuranceValue  | number:'1.2-2' }}</td>
    </tr>
  </tbody>
  <tfoot>
    <tr>
      <td></td>
      <td><strong>{{'UBZ.SITE_CONTENT.101010010' | translate }}</strong></td>
      <td>{{ commercialValue[1] | currency:'EUR':true:'1.2-2' }}</td>
      <td>{{ cautionalValue[1] | currency:'EUR':true:'1.2-2' }}</td>
      <td>{{ insuranceValue[1] | currency:'EUR':true:'1.2-2' }}</td>
    </tr>
  </tfoot>
</table>
</fieldset>

<app-navigation-footer showSaveDraft="true" showPrevious="true" [showCancelButton]="false" [saveDraftCallback]="saveDraftCallback"
  (saveButtonClick)="saveData()" (previousButtonClick)="goToPreviousTask()" (closeDraftButtonClick)="goToGenericTask()"
  [activeTaskCode]="currentTask">
</app-navigation-footer>
</fieldset>
