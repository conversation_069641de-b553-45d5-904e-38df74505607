import { Component, OnInit, Input } from '@angular/core';
import { AccordionAPFService } from '../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
@Component({
  selector: 'app-construction-info',
  templateUrl: './construction-info.component.html',
  styleUrls: ['./construction-info.component.css']
})
export class ConstructionInfoComponent implements OnInit {
  @Input() salComplessivo: any;
  constructionNote: string = '';
  modalOpen = false;
  newNote: string;

  constructor(public _accordionAPFService: AccordionAPFService) {}

  ngOnInit() {}

  openAddModal() {
    this.modalOpen = true;
  }

  closeModal() {
    this.newNote = '';
    this.modalOpen = false;
  }

  modifyNote() {
    this.newNote = this.constructionNote;
    this.modalOpen = true;
  }

  addNote(note: string) {
    this.constructionNote = note;
    this.salComplessivo['datiSalComplessivo']['buildSiteInfo'] = note;
    this.closeModal();
  }
}
