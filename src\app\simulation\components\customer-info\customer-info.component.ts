import { Component, OnInit, ViewChild, Inject } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Observable } from 'rxjs/Observable';

import { ApplicationFormComponent } from '../../../shared/application-form/components/application-form.component';
import { CustomerService } from '../../services/customer/customer.service';
import { MenuService } from '../../../shared/menu/services/menu.service';
import { LandingService } from '../../../simulation/services/landing/landing.service';
import { GenericInfoService } from '../../services/generic-info/generic-info.service';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';
import { PositionService } from '../../../shared/position/position.service';

@Component({
  selector: 'app-customer-info',
  templateUrl: './customer-info.component.html',
  styleUrls: ['./customer-info.component.css']
})
export class CustomerInfoComponent implements OnInit {
  @ViewChild(ApplicationFormComponent)
  applicationForm: ApplicationFormComponent;

  simulationId: string;
  wizardCode: string;
  private isSurroga: boolean;
  currentTask: string;
  showNdgDetails = false; // Mostra la sezione dedicata al dettaglio ndg

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private customerService: CustomerService,
    public menuService: MenuService,
    public landingService: LandingService,
    private genericInfoService: GenericInfoService,
    private positionService: PositionService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) {}

  ngOnInit() {
    this.activatedRoute.parent.params
      .switchMap((params: Params) => {
        this.simulationId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        this.currentTask =
          this.wizardCode === 'WSIM' ? 'UBZ-SIM-NDG' : 'UBZ-REQ-NDG';
        return Observable.forkJoin(
          this.genericInfoService.getGenericInfo(
            this.simulationId,
            this.wizardCode
          )
        );
      })
      .subscribe(res => {
        // Imposta l'originationProcess nel landingService che lo utilizzerà per bloccare i campi a seconda del suo valore
        this.landingService.originationProcess = res[0].originationProcess;
        if (this.wizardCode === 'WRPE') {
          if (res[0].originationProcess === 'SUR') {
            this.isSurroga = true;
          } else {
            this.isSurroga = false;
          }
        }
        this.landingService.checkIfCompletedTask(
          this.simulationId,
          this.currentTask
        );
      });
    this.positionService.getPositionDetail(this.simulationId).subscribe(res => {
      // Controlla in ndgType se la richiesta è di cointestazione
      // per mostrare la sezione dedicata
      if (res && res.applicant && res.applicant.ndgType === 'CO') {
        // Invoca servizio per recuperare lista ndg associati alla coi
        this.customerService.getCoiNdgs(res.applicant.ndg).subscribe(result => {
          this.customerService.coiNdgList = result;
          this.showNdgDetails = true;
        });
      }
    });
  }

  backToSearch() {
    if (this.wizardCode === 'WRPE') {
      if (this.isSurroga) {
        this.router.navigate([
          `customer-search/${this.wizardCode}/${this.simulationId}/-/SUR`
        ]);
      } else {
        this.router.navigate([
          `customer-search/${this.wizardCode}/${this.simulationId}/-/PER`
        ]);
      }
    } else {
      this.router.navigate([`customer-search/${this.wizardCode}/-/-/-`]);
    }
  }

  newSaveCustomerInfo() {
    // Se siamo in richiesta frazionamento
    if (
      this.activatedRoute.parent.url['value'][0] &&
      this.activatedRoute.parent.url['value'][0].path ===
        'guaranteeFractionation'
    ) {
      return this.router.navigate(
        [`../${this.constants.landingMap['UBZ-REQ-GAR']}`],
        {
          relativeTo: this.activatedRoute
        }
      );
    }
    this.customerService
      .saveCustomerInfo(
        this.simulationId,
        this.applicationForm.model,
        this.applicationForm.page,
        this.wizardCode
      )
      .subscribe(() => {
        this.landingService.goNextPage(
          this.simulationId,
          this.currentTask,
          this.wizardCode,
          this.activatedRoute
        );
      });
  }

  cancelPosition() {
    this.landingService
      .cancelPosition(this.simulationId, this.wizardCode)
      .subscribe(() => this.router.navigate(['/']));
  }
}
