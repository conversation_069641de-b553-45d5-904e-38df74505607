import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { Http, Response } from '@angular/http';

@Injectable()
export class GuaranteeInfoService {
  constructor(private http: Http) {}

  getGuaranteeList(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/collateral/v1/collaterals/${positionId}/fromFidieGaranzie`;
    return this.http.get(url).map((resp: Response) => {
      return resp.json();
    });
  }

  // Recupera le garanzie associate al ndg in caso di richiesta di frazionamento
  // ndg - ndg selezionato per il frazionamento
  // type - Codice di familyType (IMM o MOB)
  getGuaranteeListFrg(ndg: string, type: string) {
    return this.http
    .get(`/UBZ-ESA-RS/service/collateralFrg/v1/getCollateralsFracFromFidi/${ndg}/${type}`)
    .map((resp: Response) => {
      return resp.json();
    });
  }

  importGuarantee(positionId: string, guarantees: any, credAmount: number): Observable<any> {
    const url = `/UBZ-ESA-RS/service/collateral/v1/collaterals/`;
    const input = {
      collateralList: guarantees,
      positionId: positionId,
      credlineAmount: credAmount
    };
    return this.http.post(url, input).map((resp: Response) => resp.json());
  }
}
