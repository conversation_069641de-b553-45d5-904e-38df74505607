<form #form="ngForm" novalidate (click)="_accordionAPFService.handleOpenProperty(group, form)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
          <ng-container *ngIf="appraisalType === constants.appraisalTypes.BENI_INDUSTRIALI ||
            mobileAppraisalType === constants.appraisalTypes.SHIPPING ||
            mobileAppraisalType === constants.appraisalTypes.AEREOMOBILE; else notMobile">
            {{'UBZ.SITE_CONTENT.1010001101' | translate }}
          </ng-container>
          <ng-template #notMobile>{{'UBZ.SITE_CONTENT.100000001' | translate }}</ng-template>
          <span class="state" [ngClass]="{'green': isValid(), 'red': !isValid()}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <table class="uc-table" *ngIf="appraisalType !== constants.appraisalTypes.BENI_INDUSTRIALI &&
            mobileAppraisalType !== constants.appraisalTypes.SHIPPING &&
            mobileAppraisalType !== constants.appraisalTypes.AEREOMOBILE">
          <thead>
            <tr>
              <th scope="col" class="col-sm-2" rowspan="2">{{'UBZ.SITE_CONTENT.10010110' | translate }}</th>
              <th scope="col" class="col-sm-2" colspan="3">{{'UBZ.SITE_CONTENT.100000010' | translate }}</th>
              <th scope="col" class="col-sm-2" colspan="3">{{'UBZ.SITE_CONTENT.100000011' | translate }}</th>
            </tr>
            <tr>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.100000100' | translate }}</th>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.100000101' | translate }}</th>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.100000110' | translate }}</th>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.100000100' | translate }}</th>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.100000101' | translate }}</th>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.100000110' | translate }}</th>
            </tr>
          </thead>
          <tbody>

            <tr *ngFor="let item of costiDiRealizzazione; let i = index;">
              <ng-container *ngIf="!(item.costType.includes('CCR') || item.costType.includes('CCF')) && !(item.costType.includes('TOT'))">
                <td>{{ item.costType | translate }}</td>
                <fieldset [disabled]="disable">
                  <td>
                    <input class="form-control" type="number" name="expectedMqMc-{{i}}" [(ngModel)]="item.expectedMqMc" required="" appOnlyNumbers (input)="updateTotals()">
                  </td>
                </fieldset>
                <td>
                  <fieldset [disabled]="disable">
                    <app-importo [name]="'expectedCostMqMc-' + i" [required]="false" [(ngModel)]="item.expectedCostMqMc" (input)="updateTotals()">
                    </app-importo>
                  </fieldset>
                </td>

                <td>{{getExpectedTotCost(i) | number:'1.2-2'}}</td>
                <td>
                  <fieldset [disabled]="disable">
                    <input class="form-control" type="number" name="executedMqMc-{{i}}" [(ngModel)]="item.executedMqMc" required="" appOnlyNumbers (input)="updateTotals()">
                  </fieldset>
                </td>
                <td>
                  <fieldset [disabled]="disable">
                    <app-importo [name]="'executedCostMqMc-' + i" [required]="false" [(ngModel)]="item.executedCostMqMc" (input)="updateTotals()">
                    </app-importo>
                  </fieldset>
                </td>

                <td>{{getTotExecutedCost(i) | number:'1.2-2'}}</td>
              </ng-container>
              <!-- I totali sono da visualizzare senza input -->
              <ng-container *ngIf="(item.costType.includes('CCR') || item.costType.includes('CCF')) && !(item.costType.includes('TOT'))">
                <td>{{ item.costType | translate }}</td>
                <td></td>
                <td></td>
                <td>
                  <label>{{ item.expectedTotCost | number:'1.2-2' }}</label>
                </td>
                <td></td>
                <td></td>
                <td>
                  <label>{{ item.totExecutedCost | number:'1.2-2' }}</label>
                </td>
              </ng-container>
            </tr>

          </tbody>
          <tfoot class="table-foot">
            <tr>
              <td>
                <strong>{{'UBZ.SITE_CONTENT.100000111' | translate }}</strong>
              </td>
              <td></td>
              <td></td>
              <td class="text-center">
                <strong>{{ expectedCostTotal | currency:'EUR':true:'1.2-2' }}</strong>
              </td>
              <td></td>
              <td></td>
              <td class="text-center">
                <strong>{{ executedCostTotal | currency:'EUR':true:'1.2-2' }}</strong>
              </td>
            </tr>
          </tfoot>
        </table>
        <div class="panel-box">
          <div class="row" *ngIf="mobileAppraisalType === constants.appraisalTypes.AEREOMOBILE">
            <div class="col-sm-6 form-group">
              <label>{{'UBZ.SITE_CONTENT.1010000011' | translate }}*</label>
              <fieldset [disabled]="disable">
                <input type="text" name="aircraftName" [(ngModel)]="salComplessivo.aircraftName" class="form-control" required>
              </fieldset>
            </div>
            <div class="col-sm-6 form-group">
              <label>{{'UBZ.SITE_CONTENT.1010000100' | translate }}*</label>
              <input type="text" name="aircraftIdentNum" [(ngModel)]="salComplessivo.aircraftIdentNum" class="form-control" required>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6 form-group" *ngIf="mobileAppraisalType !== constants.appraisalTypes.SHIPPING && mobileAppraisalType !== constants.appraisalTypes.AEREOMOBILE">
              <label>{{'UBZ.SITE_CONTENT.100001010' | translate }}: {{'UBZ.SITE_CONTENT.100001011' | translate }}*</label>
              <fieldset [disabled]="disable">
                <div class="custom-radio" *ngFor="let domain of  (buildSiteRecapCosts | domainMapToDomainArray) ; let i = index">
                  <input type="radio" name="buildSiteRecapCosts" id="buildSiteRecapCosts-{{i}}" class="radio" [(ngModel)]="salComplessivo.buildSiteRecapCosts"
                    value="{{domain.domCode}}" required="">
                  <label for="buildSiteRecapCosts-{{i}}">{{domain.translationCod | translate }}</label>
                </div>
              </fieldset>
            </div>
            <div class="col-sm-6 form-group" *ngIf="mobileAppraisalType === constants.appraisalTypes.AEREOMOBILE">
              <label>{{'UBZ.SITE_CONTENT.1010000101' | translate }}*</label>
              <input type="text" name="inspectionPlace" [(ngModel)]="salComplessivo.inspectionPlace" required class="form-control">
            </div>
          </div>
          <div class="row">
            <div class="col-sm-5 form-group">
              <label>{{'UBZ.SITE_CONTENT.100001110' | translate }}*</label>
              <fieldset [disabled]="disable">
                <div class="custom-radio" *ngFor="let domain of  (planningProcesses | domainMapToDomainArray) ; let i = index">
                  <input type="radio" name="planningProcess" id="planningProcess-{{i}}" class="radio" [(ngModel)]="salComplessivo.planningProcess"
                    value="{{domain.domCode}}" required="">
                  <label for="planningProcess-{{i}}">{{domain.translationCod | translate }}</label>
                </div>
              </fieldset>
            </div>
            <div class="col-sm-4 form-group">
              <label>{{'UBZ.SITE_CONTENT.100010010' | translate }}*</label>
              <fieldset [disabled]="disable">
                <app-calendario [(ngModel)]="salComplessivo.planningEndDate" [name]="'planningEndDate'" [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                  [required]="true" [maxDate]="maxDate" [minDate]="minPlanningEndDate" [$refreshCalendar]="$refreshCalendar">
                </app-calendario>
              </fieldset>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-5 form-group" *ngIf="salComplessivo.planningProcess === 'SOS'">
              <label>{{'UBZ.SITE_CONTENT.100010011' | translate }}*</label>
              <fieldset [disabled]="disable">
                <div class="custom-radio" *ngFor="let domain of  (suspensionReasons | domainMapToDomainArray) ; let i = index">
                  <input type="radio" name="suspensionReason" id="suspensionReason-{{i}}" class="radio" [(ngModel)]="salComplessivo.suspensionReason"
                    value="{{domain.domCode}}" required="">
                  <label for="suspensionReason-{{i}}">{{ domain.translationCod | translate }}</label>
                </div>
              </fieldset>
            </div>
            <div class="col-sm-4 form-group" *ngIf="salComplessivo.suspensionReason === 'ALR' && salComplessivo.planningProcess === 'SOS'">
              <label for="notaCausaleSospensione">{{'UBZ.SITE_CONTENT.100011001' | translate }}*</label>
              <fieldset [disabled]="disable">
                <div class="form-group">
                  <input type="text" name="otherSuspReasNotes" [(ngModel)]="salComplessivo.otherSuspReasNotes" required="" class="form-control">
                </div>
              </fieldset>
            </div>
          </div>
          <div class="row" *ngIf="mobileAppraisalType === constants.appraisalTypes.SHIPPING || mobileAppraisalType === constants.appraisalTypes.AEREOMOBILE">
            <div class="col-sm-4 form-group">
              <label>{{'UBZ.SITE_CONTENT.1010000110' | translate }}*</label>
              <fieldset [disabled]="disable">
                <div class="custom-radio" *ngFor="let domain of  (projectCorrespondences | domainMapToDomainArray) ; let i = index">
                  <input type="radio" name="projectCorrespondences" id="projectCorrespondences-{{i}}" class="radio" [(ngModel)]="salComplessivo.objectsProjectCorr"
                    value="{{domain.domCode}}" required="">
                  <label for="projectCorrespondences-{{i}}">{{domain.translationCod | translate }}</label>
                </div>
              </fieldset>
            </div>
            <div class="col-sm-4 form-group" *ngIf="salComplessivo.objectsProjectCorr === 'AME' || salComplessivo.objectsProjectCorr === 'AMN'">
              <label>
                <span>
                  <i class="icon-search note-tooltip" [tooltip]="salComplessivo.changesDescNoCorr" triggers="click"></i>
                </span>
                {{'UBZ.SITE_CONTENT.1010000111' | translate }}*
              </label>
              <textarea name="descrModifiche" class="form-control" [(ngModel)]="salComplessivo.changesDescNoCorr" [disabled]="disable"
                required></textarea>
            </div>
            <div class="col-sm-4 form-group">
              <label>
                <span>
                  <i class="icon-search note-tooltip" [tooltip]="salComplessivo.diversIndemnDesc" triggers="click"></i>
                </span>
                {{'UBZ.SITE_CONTENT.1010001000' | translate }}
              </label>
              <textarea name="difformita" class="form-control" [(ngModel)]="salComplessivo.diversIndemnDesc" [disabled]="disable"></textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
  </accordion-group>
</form>
