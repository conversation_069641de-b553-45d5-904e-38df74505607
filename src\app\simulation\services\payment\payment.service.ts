import { Injectable } from '@angular/core';
import { Http} from '@angular/http';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class PaymentService {

  constructor(private _http: Http) { }


  getCustomeres(idRequest:string):Observable<any>{
    const url=`/UBZ-ESA-RS/service/reqPer/v1/reqAppraisals/${idRequest}/payment/customer/selections`
    return this._http.get(url).map((res) => res.json());

  }

updateCustomer(idRequest:string,dataobj:any):Observable<any>{


    const url=`/UBZ-ESA-RS/service/reqPer/v1/reqAppraisals/${idRequest}/payment/customer`
    return this._http.put(url, dataobj).map((res) => res.json())

  }
 
saveCustomer(idRequest:string):Observable<any>{
    const url=`/UBZ-ESA-RS/service/reqPer/v1/reqAppraisals/${idRequest}/payment/customer`
    return this._http.get(url).map((res) => res.json());

  }


  deleteCustomer(idRequest: string): Observable<any> {
    const url = `/UBZ-ESA-RS/service/reqPer/v1/reqAppraisals/${idRequest}/payment/customer`;
    
    return new Observable(observer => {
      this._http.delete(url)
        .subscribe(
          response => {
            observer.next(response);
            observer.complete();
          },
          error => {
            console.error('Error deleting customer:', error);
            observer.error(error);
          }
        );
    });
  }
 
getInvoice(positionId: string){
 const url='/UBZ-ESA-RS/service/reqPer/v1/reqAppraisal/' + positionId
 return this._http.get(url).map((res) => res.json());

}
}
