import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChild,

} from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { ChecklistService } from '../../../service/checklist.service';
import { DomainService } from '../../../../domain/domain.service';
import { Domain } from '../../../../domain/domain';
import { PositionService } from '../../../../position/position.service';

@Component({
  selector: 'app-modal-nuovo-documento',
  templateUrl: './modal-nuovo-documento.component.html'
})
export class ModalNuovoDocumentoComponent implements OnInit {
  @ViewChild('fileToUpload') fileToUpload: any;
  fileName = '';

  @Input() isOpen = false;
  @Input() positionId: string;
  @Input() requestId: string;
  @Input() accordions: any[];
  @Input() isUpload: boolean;
  @Output() modalClose = new EventEmitter();

  requiredDocument = false;
  domains: Domain[] = [];
  entityType = '';
  documentDesc = '';
  requiredPhase = '';
  selectedAccordion: any;
  selectedAccordionIndex = -1;
  canUpload = false;
  entityTypeDom: any[];
  macroProcess = '';
  originProcess = '';
  appraisalId: string
  allobjectCod: any[]
  parentCod: any = "PER"
  allDocCategory: any = {}
  allDocCategoryTwo: any = {}
  parentCodTwo: any = "ASS"
  idObjectCode: any
  selectObjectCode: any
  selectDocumentCategory: any
  selectedOption2 = ''
  selectedOption3 = ''

  constructor(
    private positionService: PositionService,
    private checklistService: ChecklistService,
    private domainService: DomainService,

  ) { }

  ngOnInit() {
    Observable.forkJoin(
      this.domainService.getStatusName(),
      this.domainService.newGetDomain('UBZ_DOM_CHK_ENTITY_TYPE'),
      this.domainService.getCategoryDoc(this.parentCod),
      this.domainService.getCategoryDoc(this.parentCodTwo),
    ).subscribe(res => {
      this.domains = res[0];
      this.entityTypeDom = res[1]
      this.allDocCategory = res[2]
      this.allDocCategoryTwo = res[3]
      if (typeof this.requestId !== 'undefined') {
        this.domainService.getObjectCode(this.positionId).subscribe((res: any) => {
          this.allobjectCod = res.appraisalObject
        })
      }
      if (this.requestId) {
        // this.requestId = richiesta
        // this.positionId = perizia
        this.positionService.getAppraisalInfo(this.positionId).subscribe(response => {
          this.originProcess = response.appraisal.originProcess;
          this.macroProcess = response.appraisal.macroProcess;
          if (this.isDocumentForEntityTypeAppraisal()) {
            this.entityType = '';
            this.setSelectedAccordion();

          }

        });
      } else {
        // this.positionId = richiesta
        // perizia non utilizzabile
        // @todo:
        // ottenere macroprocesso da richiesta (requestId)
        // disabilitare bottone aggiungi doc se macroprocess = MLP
      }

    });
  }

  assignObjectCode(object: any) {
    this.selectObjectCode = object.target.value
  }

  assignDocumentCategory(obj: any) {
    this.selectDocumentCategory = obj.target.value

  }
  closeModal(refreshPage: boolean) {
    this.isOpen = false;
    this.modalClose.emit({
      refreshPage: refreshPage,
      accordionIndex: this.selectedAccordionIndex
    });
  }

  setFileName() {
    this.fileName = this.fileToUpload.nativeElement.files[0].name;
  }

  uploadFile() {
    let id;
    if (this.requestId) {
      id = this.requestId;
    } else {
      id = this.positionId;
    }
    let mandatoryFor = '*;*';
    if (this.requiredPhase !== '') {
      mandatoryFor =
        this.requiredPhase.substring(0, 3) +
        ';' +
        this.requiredPhase.substring(3);
    }
    const uploadRequest = {
      positionId: 'UBZ-' + id,
      documents: [
        {
          positionId: 'UBZ-' + id,
          entityType: this.isDocumentForEntityTypeAppraisal() ? this.entityType : this.selectedAccordion.entityType,
          documentCod: this.isDocumentForEntityTypeAppraisal() ? this.selectDocumentCategory : '999',
          ndg: this.isDocumentForEntityTypeAppraisal() ? null : this.selectedAccordion.ndg,
          offerId: this.isDocumentForEntityTypeAppraisal() ? this.entityType === "PER" ? this.positionId : this.selectObjectCode : this.selectedAccordion.offerId,
          versionId: Math.floor(Math.random() * 1000 + 1),
          customDocDesc: this.documentDesc,
          documentName: this.fileToUpload.nativeElement.files[0].name,
          // mandatoryFor: mandatoryFor
          // acquiredOriginal: $scope.uploadsSelected[i].acquiredOriginal,
          // notes: $scope.uploadNotes,
          documentCategory: this.isDocumentForEntityTypeAppraisal() ? this.selectDocumentCategory : '999',
          // documentName: this.documentName,
          positionType: 'UBZ'
        }
      ],
      content: null
    };
    this.checklistService
      .newUploadDocument(
        this.fileToUpload.nativeElement.files[0],
        uploadRequest
      )
      .subscribe(() => {
        this.closeModal(true);
      });
  }

  requireDocument() {
    let mandatoryFor = '*;*';
    if (this.requiredPhase !== '') {
      mandatoryFor =
        this.requiredPhase.substring(0, 3) +
        ';' +
        this.requiredPhase.substring(3);
    }
    const body = {
      positionId: 'UBZ-' + this.positionId,
      positionType: 'UBZ',
      entityType: this.isDocumentForEntityTypeAppraisal() ? this.entityType : this.selectedAccordion.entityType,
      groupCod: '999',
      // ndg: vuoto
      offerId: this.isDocumentForEntityTypeAppraisal() ? this.positionId : this.selectedAccordion.offerId,
      documentCod: '999',
      mandatoryFor: mandatoryFor
    };
    this.checklistService.requireDocument(body).subscribe(x => {
      this.closeModal(true);
    });
  }

  setSelectedAccordion() {
    let ind = 0;
    if (this.accordions && this.accordions.length > 0) {
      for (const acc of this.accordions) {
        if (this.entityType === acc.entityType && this.isDocumentForEntityTypeAppraisal()) {
          this.selectedAccordion = acc;
          break;
        }
        else if (this.entityType === acc.offerId) {
          this.selectedAccordion = acc;
          break;
        }
        ind++;
      }
    } else {
      ind = -1;
    }
    this.selectedAccordionIndex = ind;
    this.selectedOption3 = ''
  }

  checkIfCanUpload() {
    if (
      this.isDocumentForEntityTypeAppraisal() && this.entityType !== '' && (this.entityType === 'PER' && this.selectedOption3 !== '') &&
      ((this.fileName !== '' && this.isUpload === true) ||
        (this.isUpload === false &&
          (this.requiredDocument === false ||
            (this.requiredDocument === true && this.requiredPhase !== ''))))
    ) {
      this.canUpload = true;
    }
    else if (
      !this.isDocumentForEntityTypeAppraisal() && this.entityType !== '' &&
      ((this.fileName !== '' && this.isUpload === true) ||
        (this.isUpload === false &&
          (this.requiredDocument === false ||
            (this.requiredDocument === true && this.requiredPhase !== ''))))
    ) {
      this.canUpload = true;
    }
    else if (
      this.isDocumentForEntityTypeAppraisal() && this.selectedOption2 !== '' && (this.entityType === 'ASS' && this.selectedOption3 !== '') &&
      ((this.fileName !== '' && this.isUpload === true) ||
        (this.isUpload === false &&
          (this.requiredDocument === false ||
            (this.requiredDocument === true && this.requiredPhase !== ''))))) {
      this.canUpload = true;
    }
    else {
      this.canUpload = false;
    }
  }
  isDocumentForEntityTypeAppraisal(): boolean {
    return (this.macroProcess === 'MLP' || this.originProcess === 'FRA' || this.macroProcess === 'MLR');
  }
}
