import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { GuaranteeTransferService } from '../../services/guarantee-transfer.service';

@Component({
  selector: 'app-target-guarantees',
  templateUrl: './target-guarantees.component.html',
  styleUrls: ['./target-guarantees.component.css']
})
export class TargetGuaranteesComponent implements OnInit {
  guaranteeList: any[] = [];
  guaranteeIdChecked = null;
  guaranteeCollatTecForm: string;
  guaranteeDescription: string;
  previousModalIsOpen: boolean;
  appraisals: any[] = [];

  constructor(
    public guaranteeTransferService: GuaranteeTransferService,
    private router: Router
  ) { }

  ngOnInit() {

    this.guaranteeTransferService.staticHeaderArray.find(obj => obj['label'] === 'UBZ.SITE_CONTENT.***********')['value'] = null;
    // Se l'array non è popolato forza uscita dal wizard
    if (!this.guaranteeTransferService.guaranteeFromList.length) {
      return this.guaranteeTransferService.cancelRequest();
    }
    setTimeout(() => (this.guaranteeTransferService.wizardStep = 3), 10);
    this.guaranteeList = this.guaranteeTransferService.guaranteeToList;
    for (const el of this.guaranteeTransferService.guaranteeFromList) {
      if (el.jointCod === this.guaranteeTransferService.guaranteeFrom) {
        this.guaranteeCollatTecForm = el.collatTecForm;
        this.guaranteeDescription = el.description;
      }
    }
  }


  // FIXME - ESEGUI ROUTING RELATIVO FRA LE ROTTE CHILD
  /**
   * @name save
   * @description Esegue la sostituzione della garanzia ed effettua la navigazione verso il
   * prossimo step del wizard
  */
  save() {

    this.guaranteeTransferService
      .changeGuarantee(
        this.guaranteeTransferService.ndgOrigin,
        (this.guaranteeTransferService.ndgDestination && this.guaranteeTransferService.ndgDestination !== '-') ? this.guaranteeTransferService.ndgDestination : this.guaranteeTransferService.ndgOrigin,
        this.guaranteeTransferService.guaranteeFrom,
        this.guaranteeIdChecked,
        this.guaranteeTransferService.assetType,
        this.createRealAppraisalsList()
      )
      .subscribe(() => {
        this.guaranteeTransferService.guaranteeTo = this.guaranteeIdChecked;
        this.guaranteeTransferService.staticHeaderArray.find(obj => obj['label'] === 'UBZ.SITE_CONTENT.***********')['value'] = this.guaranteeIdChecked;
        this.guaranteeTransferService.collatTecTo = this.getCollatTecFormTo();
        this.router.navigate([
          `guaranteeTransfer/${this.guaranteeTransferService.ndgOrigin}/${this.guaranteeTransferService.ndgDestination}/guarantees-transfer-confirm`
        ]);
      });
  }


  getCollatTecFormTo() {
    for (const el of this.guaranteeTransferService.guaranteeToList) {
      if (el.jointCod === this.guaranteeIdChecked) {
        return el.collatTecForm;
      }
      else {
        return null;
      }
    }

  }

  createRealAppraisalsList() {
    if (this.guaranteeTransferService.appraisalList) {
      for (const appraisal of this.guaranteeTransferService.appraisalList) {
        for (const collat of appraisal.collaterals) {
          for (const asset of collat.assets) {
            if (this.appraisals.indexOf(asset.realAppraisalId) === -1) {
              this.appraisals.push(asset.realAppraisalId);
            }
          }
        }
      }
      return this.appraisals;
    }
  }
  // FIXME - ESEGUI ROUTING RELATIVO FRA LE ROTTE CHILD
  /**
   * @name previous
   * @description Effettua la navigazione verso il precedente step del wizard
  */
  previous() {
    this.router.navigateByUrl(`guaranteeTransfer/${this.guaranteeTransferService.ndgOrigin}/${this.guaranteeTransferService.ndgDestination}/start-guarantees`);
  }

  /**
   * @name openModal
   * @description Apre la modal precedente
  */
  openModal() {
    this.previousModalIsOpen = true;
  }

  /**
   * @name closeModal
   * @description Chiude la modal precedente
  */
  closeModal() {
    this.previousModalIsOpen = false;
  }

  /**
   * @name confirmUndo
   * @description Sul conferma della modal, chiude e invoca previous()
  */
  confirmUndo() {
    this.previousModalIsOpen = false;
    this.previous();
  }

}
