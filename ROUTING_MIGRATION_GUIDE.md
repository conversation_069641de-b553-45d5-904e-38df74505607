# URL Routing Migration Guide

## Overview
This guide documents the migration from hash-based routing (`#/route`) to HTML5 pushState routing (`/route`) in the DeTrim application.

## Changes Made

### 1. Router Configuration
**File:** `src/app/app-routing.module.ts`
```typescript
// BEFORE (hash-based routing)
RouterModule.forRoot(routes, { useHash: true })

// AFTER (HTML5 pushState routing)
RouterModule.forRoot(routes, { useHash: false })
```

### 2. Server Configuration
**File:** `src/assets/WEB-INF/web.xml`
- Added error page configuration to serve index.html for 404 errors
- This ensures client-side routing works when users refresh or access URLs directly

### 3. Build Configuration
**File:** `.angular-cli.json`
- Updated assets configuration to include WEB-INF directory in build output

### 4. Backward Compatibility
**File:** `src/app/shared/services/url-migration.service.ts`
- Created service to handle legacy hash-based URLs
- Automatically redirects old `#/route` URLs to new `/route` format

## URL Format Changes

### Before (Hash-based)
```
https://ubz-uj.collaudo.usinet.it/UBZ-EFA-PF/UBZ/#/dashboard/ALL
https://ubz-uj.collaudo.usinet.it/UBZ-EFA-PF/UBZ/#/index
https://ubz-uj.collaudo.usinet.it/UBZ-EFA-PF/UBZ/#/wizard/123/456
```

### After (Clean URLs)
```
https://ubz-uj.collaudo.usinet.it/UBZ-EFA-PF/UBZ/dashboard/ALL
https://ubz-uj.collaudo.usinet.it/UBZ-EFA-PF/UBZ/index
https://ubz-uj.collaudo.usinet.it/UBZ-EFA-PF/UBZ/wizard/123/456
```

## Testing Checklist

### Before Deployment
- [ ] Test all routes work without hash in development
- [ ] Test page refresh on different routes
- [ ] Test direct URL access (bookmarks)
- [ ] Test browser back/forward buttons
- [ ] Test legacy hash URLs redirect properly

### After Deployment
- [ ] Verify server serves index.html for unknown routes
- [ ] Test existing bookmarks still work (with migration service)
- [ ] Monitor for 404 errors in server logs
- [ ] Test on different browsers

## Rollback Plan

If issues occur, you can quickly rollback by changing one line:

**File:** `src/app/app-routing.module.ts`
```typescript
// ROLLBACK: Change this line back to
RouterModule.forRoot(routes, { useHash: true })
```

Then rebuild and redeploy. This will restore hash-based routing immediately.

## Risk Mitigation

1. **Server Configuration**: The web.xml file ensures proper server-side handling
2. **Legacy URL Support**: UrlMigrationService handles old hash-based URLs
3. **Gradual Migration**: Users with old bookmarks will be automatically redirected
4. **Quick Rollback**: Single line change to revert if needed

## Monitoring

After deployment, monitor:
- Server logs for 404 errors
- User reports of broken links
- Analytics for any drop in page views
- Browser console errors

## Benefits

1. **SEO Friendly**: Clean URLs are better for search engines
2. **User Experience**: URLs look cleaner and more professional
3. **Sharing**: Easier to share and bookmark specific pages
4. **Modern Standard**: Follows current web development best practices
