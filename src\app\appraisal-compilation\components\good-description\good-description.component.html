<fieldset [disabled]="!_landingService.positionLocked">
  <fieldset [disabled]="_landingService.isLockedTask[currentTask] || haveDisabledFields">
    <accordion class="panel-group" id="accordion">
      <app-accordion-application-form 
        [overwriteAPFData]="true" 
        (change)="pageIsValid()" 
        (calendarChange)="pageIsValid()"       
        [idCode]="positionId" 
        [page]="'DESC_BENE'" 
        [positionId]="positionId"
        [formDisabled] = "!_landingService.positionLocked || _landingService.isLockedTask[currentTask] || haveDisabledFields">
      </app-accordion-application-form>
    
      <app-appraisal-template-validation *ngIf="_positionService.isInternalSecondOpinion" (statusChange)="pageIsValid()" [positionId]="positionId" templateName="DESC_BENE" [haveDisabledFields]="haveDisabledFields"></app-appraisal-template-validation>    
    </accordion>
  </fieldset>

  <app-navigation-footer showSaveDraft="true" showPrevious="true" [saveIsEnable]="pageCompleted" [showCancelButton]="false"
    (saveButtonClick)="saveData()" (previousButtonClick)="goToPreviousTask()" (closeDraftButtonClick)="goToGenericTask()"
    [activeTaskCode]="currentTask" [saveDraftCallback]="saveDraftCallback"
  ></app-navigation-footer>
</fieldset>
