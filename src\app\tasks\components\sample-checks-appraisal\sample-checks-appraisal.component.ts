import { Component, OnInit, Input, AfterViewInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';

import { GenericTaskService} from '../../services/generic-task/generic-task.service';
import { DomainService } from '../../../shared/domain/domain.service';
import { TranslateService } from '@ngx-translate/core';
import { MessageService } from '../../../shared/messages/services/message.service';

@Component({
  selector: 'app-sample-checks-appraisal',
  templateUrl: './sample-checks-appraisal.component.html'
})
export class SampleChecksAppraisalComponent implements OnInit {
  public page: any = {};
  public appraisalId: string;
  public valueDom: any = {};
  public saveIsEnable = false;

  constructor(
    private genericTaskService: GenericTaskService,
    private _activatedRoute: ActivatedRoute,
    private domainService: DomainService,
    private router: Router,
    private messageService: MessageService,
    private translateService: TranslateService
  ) { }

  ngOnInit() {
    this.domainService.newGetDomain('UBZ_DOM_ANSWER').subscribe( x => {
      this.valueDom = x;
    });
    this._activatedRoute.params.subscribe( params => {
        this.appraisalId = params['positionId'];
        this.catchTable();
      });
  }

  private catchTable() {
    this.genericTaskService.getAppraisalEvaluations(this.appraisalId).subscribe( x => {
      this.page = x;
      this.checkSaveEnable();
    });
  }

  saveEvaluation () {
    this.genericTaskService.saveAppraisalEvaluations(this.appraisalId , this.page).subscribe( x => {
      this.messageService.showSuccess(
        this.translateService.instant('UBZ.SITE_CONTENT.1001100000'),
        this.translateService.instant('UBZ.SITE_CONTENT.1001100001')
      );
      this.router.navigateByUrl('sampleChecks/dashboard');
    });
  }

  checkSaveEnable() {
    for (const el of this.page.field){
      if ( !el.value ) {
        this.saveIsEnable = false;
        return;
      }
    }
    this.saveIsEnable = true;
  }

}
