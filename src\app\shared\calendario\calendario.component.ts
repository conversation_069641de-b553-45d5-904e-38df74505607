// FIXME - SEMBRA CI SIA UN BUG DEL NGUI-DATETIME-PICKER
// ESEMPIO
// SE IMPOSTO max-date = new Date() potrò selezionare date valide fino ad oggi compreso!
// SE IMPOSTO min-date = new Date() potrò selezionare date valide a partire da oggi escluso!
// Per questo motivo il controllo del minDate passa da < a <=
import {
  Component,
  Input,
  Inject,
  ViewChild,
  AfterViewInit,
  forwardRef,
  OnDestroy
} from '@angular/core';
import {
  NgForm,
  NG_VALUE_ACCESSOR,
  ControlValueAccessor
} from '@angular/forms';
import { unmaskDate, maskReceivedDate, dateIsValid } from '../../app.constants';
import { DOCUMENT } from '@angular/platform-browser';
import { NguiDatetime } from '@ngui/datetime-picker';
import { Subject } from 'rxjs';

NguiDatetime.formatDate = (date: Date): string => {
  let day = date.getDate().toString();
  let month = (date.getMonth() + 1).toString();
  const year = date.getFullYear().toString();

  if (day.length === 1) {
    day = `0${day}`;
  }
  if (month.length === 1) {
    month = `0${month}`;
  }
  return `${day}-${month}-${year}`;
};
NguiDatetime.parseDate = (str: any): Date => {
  const day = parseInt(str.substring(0, 2), 10);
  const month = parseInt(str.substring(3, 5), 10) - 1;
  const year = parseInt(str.substring(6), 10);
  return new Date(year, month, day);
};
NguiDatetime.firstDayOfWeek = 1;

@Component({
  selector: 'app-calendario',
  templateUrl: './calendario.component.html',
  styleUrls: ['./calendario.component.css'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CalendarioComponent),
      multi: true
    }
  ]
})
export class CalendarioComponent
  implements AfterViewInit, ControlValueAccessor, OnDestroy {
  @ViewChild('f')
  form: NgForm;

  @Input()
  name: string;
  @Input()
  ngClassCondition: boolean;
  @Input()
  ngClassAdd: string;
  @Input()
  required: boolean;
  @Input()
  disabled: boolean;
  @Input()
  title: string;
  @Input()
  placeholder: string;
  @Input()
  minDate: Date;
  @Input()
  maxDate: Date;
  @Input()
  forceShowDatepickerPosition: string; // valori ammessi: top/bottom
  @Input()
  $refreshCalendar: Subject<boolean>; // Observable utilizzato dal componente padre per scatenare refresh (statusChange) del component

  formattedValue: string;
  unmaskedValue: Date;
  dateMask = [
    /[0-3]/,
    /\d/,
    '-',
    /[0-1]/,
    /\d/,
    '-',
    /[0-2]/,
    /\d/,
    /\d/,
    /\d/
  ];
  showDatepickerOnBottom: boolean = false;
  // Mostra e nasconde il datePicker al click sul bottone
  shouldShowDatePicker = false;
  datepickerModel: string;
  calendarInvalid: boolean = false; // boolean che attiva class invalid sul campo
  propagatedChange: any; // Valore propagato al componente padre, controllato su evento blur per evidenziare eventuali errori
  datepickerSelection: boolean = false; // flag che indica se il valore scelto è stato selezionato tramite datepicker

  constructor(@Inject(DOCUMENT) private document: any) {}

  /**
   * @function
   * @name ngOnDestroy
   * @description Esegue unsubscribe() dell'observable utilizzato per il refresh
   */
  ngOnDestroy() {
    if (this.$refreshCalendar) {
      this.$refreshCalendar.unsubscribe();
    }
  }

  /**
   * Questo è il metodo con il quale questo componente riceve il model dal form durante l'inizializzazione.
   * Qui prendiamo il valore esistente e lo formattiamo per mostrarlo all'utente.
   */
  public writeValue(model: string) {
    // Sul setting iniziale si imposta datepickerSelction=true per scatenare l'onBlurAction()
    // all'interno del metodo propagateResponse()
    this.datepickerSelection = true;
    this.formattedValue = maskReceivedDate(model);
    // Se il title non è settata non bisogna mostrare il tooltip sull'elemento
    if (!this.title) {
      this.title = '';
    }
  }

  // registers 'fn' that will be fired wheb changes are made
  // this is how we emit the changes back to the form
  public registerOnChange(fn: any) {
    this.propagateChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.propagateTouch = fn;
  }

  // The method set in registerOnChange to emit changes back to the form
  private propagateChange = (_: any) => {};

  private propagateTouch = () => {};

  ngAfterViewInit() {
    if (this.$refreshCalendar) {
      this.$refreshCalendar.subscribe(value => {
        if (value) {
          setTimeout(() => {
            // Sul refresh del component (invocata dal padre) si imposta datepickerSelection=true
            // per scatenare l'onBlurAction() al termine dell'afterViewInit ed eventualmente invalidare il campo
            this.datepickerSelection = true;
            this.form.form.updateValueAndValidity();
          }, 100);
        }
      });
    }
    // Subscribe invocata ad ogni cambiamento del campo
    this.form.form.statusChanges.subscribe(() => {
      if (this.formattedValue) {
        if (dateIsValid(this.formattedValue)) {
          this.unmaskedValue = unmaskDate(this.formattedValue);
          if (this.maxDate) {
            if (this.unmaskedValue > this.maxDate) {
              this.propagatedChange = null;
              return this.propagateResponse(null);
            }
          }
          if (this.minDate) {
            if (this.unmaskedValue <= this.minDate) {
              this.propagatedChange = null;
              return this.propagateResponse(null);
            }
          }
          this.propagatedChange = this.unmaskedValue;
          return this.propagateResponse(this.unmaskedValue);
        } else {
          this.propagatedChange = null;
          return this.propagateResponse(null);
        }
      } else {
        this.calendarInvalid = false;
        this.propagatedChange = undefined;
        return this.propagateResponse(null);
      }
    });
  }

  /**
   * @name propagateResponse
   * @description Invocato per emettere al componente padre il valore selezionato dal componente calendario
   * Se datepickerSelection === true, il valore è selezionato tramite calendario; viene reimpostata la variabile a false
   * e viene invocato il metodo associato all'evento blur del campo
   * @param value Valore da emettere al componente padre
   */
  propagateResponse(value) {
    if (this.datepickerSelection) {
      this.datepickerSelection = false;
      this.onBlurAction();
    }
    this.propagateChange(value);
  }

  // Invocato sull'evento blur del campo
  // propagatedChange === null indica che il campo data è invalido
  // nel caso setta la variabile che attiva la classe invalid
  onBlurAction() {
    if (this.propagatedChange === null) {
      this.calendarInvalid = true;
    } else {
      this.calendarInvalid = false;
    }
  }

  // Formatta il valore selezionato nel datepicker per inserire il valore sotto forma di stringa
  // all'interno del campo input
  maskSelectedValue(selectedValue) {
    this.formattedValue = maskReceivedDate(selectedValue);
    this.datepickerSelection = true;
    this.toggleDatePicker();
  }

  // Mostra e nascondi il calendario al click sul bottone
  toggleDatePicker() {
    if (!this.shouldShowDatePicker) {
      switch (this.forceShowDatepickerPosition) {
        case 'top':
          this.showDatepickerOnBottom = false;
          break;
        case 'bottom':
          this.showDatepickerOnBottom = true;
          break;
        default:
          this.showDatepickerOnBottom = true;
          // Si calcolano le coordinate del campo input che contiene il componente datepicker
          const viewportOffset = this.document
            .getElementById('datepickerInput')
            .getBoundingClientRect();
          // Si calcola lo spazio residuo in pagina al di sotto del campo input
          // Altezza totale di window meno la posizione del campo input
          const spazioDisponibileSotto =
            window.innerHeight - viewportOffset.bottom;
          const spazioDisponibileSopra =
            window.innerHeight - viewportOffset.top;
          // Se lo spazioDisponibile sotto è minore di 230px (altezza del datepicker)
          if (spazioDisponibileSotto < 230) {
            if (spazioDisponibileSopra > 230) {
              // Mostra datepicker al di sopra del campo input se c'è spazio,
              // altrimenti lascia che appaia di sotto e obblighi lo scroll dell'utente
              this.showDatepickerOnBottom = false;
            }
          }
          break;
      }
    }
    this.shouldShowDatePicker = !this.shouldShowDatePicker;
  }
}
