import { Component, OnInit, Input } from '@angular/core';
import { Observable } from 'rxjs/Observable';

import { Collateral } from '../../../../wizard-detail/model/collateral';
import { DomainService } from '../../../domain/domain.service';

@Component({
  selector: 'app-associated-guarantees-table',
  templateUrl: './associated-guarantees-table.component.html',
  styleUrls: ['./associated-guarantees-table.component.css']
})
export class AssociatedGuaranteesTableComponent implements OnInit {
  @Input() list: Collateral[] = []; // Array con i valori da mostrare in tabella
  @Input() renderIdAsset: boolean; // boolean per mostrare cella idOggettoPerizia con array in input di tipo Collateral[]
  percentageDomain: any[] = [];

  constructor(private domainService: DomainService) {}

  ngOnInit() {
    this.domainService.newGetDomain('UBZ_DOM_POOL_TYPE').subscribe( x => {
      this.percentageDomain = x;
    });
  }
}
