import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject } from '@angular/core';
import { Activated<PERSON>oute, Params } from '@angular/router';

import { GenericTaskService } from '../../../tasks/services/generic-task/generic-task.service';
import { Observable } from 'rxjs/Observable';
import { LandingService } from '../../../simulation/services/landing/landing.service';
import { PositionService } from '../../../shared/position/position.service';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';
import { DomainService } from '../../../shared/domain/domain.service';
import { TranslateService } from '@ngx-translate/core';
import { AppraisalCompilationService } from '../../../appraisal-compilation/service/appraisal-compilation.service';

@Component({
  selector: 'app-third-opinion',
  templateUrl: './third-opinion.component.html',
  styleUrls: ['./third-opinion.component.css'],
  providers: [AppraisalCompilationService]
})
export class ThirdOpinionComponent implements OnIni<PERSON>, OnDestroy {
  PropertyType: any = {
    GROSS_SURFACE: 'GROSS_SURFACE',
    COMM_COEFFICIENT: 'COMM_COEFFICIENT',
    COMM_SURFACE: 'COMM_SURFACE',
    MQ_VALUE: 'MQ_VALUE',
    PLEDGED_MQ_VALUE: 'PLEDGED_MQ_VALUE'
  };
  OpinionType: any = {
    FIRST_OPINION: 'first',
    SECOND_OPINION: 'second'
  };

  private positionId: string;
  private wizardCode: string;
  private bpmTaskId: string;
  private bpmTaskCod: string;
  private appraisalType: string;
  structureTypeDom: any;
  isMobile = false;
  isIndustriale = false;
  isAgrario = false;
  differences: any;
  currentTask = 'UBZ-PER-TOP';
  saveDraftCallback = this.saveDraft.bind(this);
  haveDisabledFields = false; // Quando true i campi sono disabilitati (ad esempio per perizie SAL o Fine lavori)
  private _subscription: any;
  modalIsOpen = false;
  selectedIdToDelete = null;
  labelObjToDelete: string;
  selectedStructToDelete = null;
  selectedRuleToDelete = null;

  // Array per tenere traccia dei totali dei valori di first e second opinion
  grossSurfaceFirst = {};
  grossSurfaceSecond = {};
  commCoeffFirst = {};
  commCoeffSecond = {};
  commSurfFirst = {};
  commSurfSecond = {};
  mqValueFirst = {};
  mqValueSecond = {};
  commValueFirst = {};
  commValueSecond = {};
  pledgedMqValueFirst = {};
  pledgedMqValueSecond = {};
  pledgedValueFirst = {};
  pledgedValueSecond = {};
  grossSurfaceThird = {};
  commCoeffThird = {};
  commSurfThird = {};
  mqValueThird = {};
  commValueThird = {};
  pledgedMqValueThird = {};
  pledgedValueThird = {};

  constructor(
    @Inject(APP_CONSTANTS) public constants: IAppConstants,
    private _activatedRoute: ActivatedRoute,
    private _genericTaskService: GenericTaskService,
    public _landingService: LandingService,
    private _positionService: PositionService,
    private _domainService: DomainService,
    private _translateService: TranslateService,
    private _appraisalCompilationService: AppraisalCompilationService
  ) { }

  ngOnInit() {
    this._subscription = this._activatedRoute.params
      .switchMap((params: Params) => {
        this.positionId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        this.bpmTaskId = params['taskId'];
        this.bpmTaskCod = params['taskCod'];
        this._landingService.checkIfCompletedTask(
          this.positionId,
          this.currentTask
        );
        return Observable.forkJoin(
          this._genericTaskService.getDifferencesBetweenOpinionsV2(
            this.positionId
          ),
          this._positionService.getAppraisalInfo(this.positionId),
          this._domainService.newGetDomain('UBZ_DOM_CONSISTENCE_TYPE')
        );
      })
      .subscribe(res => {
        this.differences = res[0];
        this.addAssetIds();
        this.calculateTotals();
        this.setAppraisalInfo(res[1]);
        this.structureTypeDom = res[2];
      });
  }

  /**
 * @name setAppraisalInfo
 * @description Metodo invocato sul recupero dell'oggetto perizia, imposta le variabili ad esso connesse
 * @param appraisalInfo Oggetto perizia recuperto dal wizard container
 */
  setAppraisalInfo(appraisalInfo) {
    if (
      appraisalInfo.appraisal.appraisalType ===
      this.constants.appraisalTypes.SHIPPING ||
      appraisalInfo.appraisal.appraisalType ===
      this.constants.appraisalTypes.AEREOMOBILE
    ) {
      this.isMobile = true;
    }
    if (
      appraisalInfo.appraisal.loanScope ===
      this.constants.appraisalTypes.BENI_INDUSTRIALI
    ) {
      this.isIndustriale = true;
    }
    if (
      appraisalInfo.appraisal.loanScope ===
      this.constants.appraisalTypes.MUTUI_AGRARI
    ) {
      this.isAgrario = true;
    }
    this.appraisalType = appraisalInfo.appraisal.appraisalType;

    // Alcune perizie non migrate hanno campi disabilitati
    if (
      appraisalInfo.appraisal.originProcess !== 'MIG' &&
      !appraisalInfo.migParent
    ) {
      // Beni mobili SAL/FLA hanno alcuni campi disabilitati
      this.haveDisabledFields = this.isBeneMobile(appraisalInfo) && 
        (
          this.appraisalType === this.constants.appraisalTypes.FINE_LAVORI ||
          this.appraisalType === this.constants.appraisalTypes.SAL
        );
      // Frazionamento ha alcuni campi disabilitati
      this.haveDisabledFields = this.haveDisabledFields || (this.appraisalType === this.constants.appraisalTypes.FRAZIONAMENTO);
    }
  }

  isBeneMobile(appraisalInfo) {
    return appraisalInfo.appraisal
      && appraisalInfo.appraisal.resItemType
      && appraisalInfo.appraisal.resItemType === 'MOB';
  }

  ngOnDestroy() {
    if (this._subscription) {
      this._subscription.unsubscribe();
    }
  }

  private addAssetIds() {
    for (const ind in this.differences.mapListObject) {
      this.differences.mapListObject[ind].id = ind;
    }
  }

  private calculateTotals(): void {
    for (const key in this.differences.mapListObject) {
      if (this.differences.mapListObject.hasOwnProperty(key)) {
        this.grossSurfaceFirst[key] = 0;
        this.grossSurfaceSecond[key] = 0;
        this.commCoeffFirst[key] = 0;
        this.commCoeffSecond[key] = 0;
        this.commSurfFirst[key] = 0;
        this.commSurfSecond[key] = 0;
        this.mqValueFirst[key] = 0;
        this.mqValueSecond[key] = 0;
        this.commValueFirst[key] = 0;
        this.commValueSecond[key] = 0;
        this.pledgedMqValueFirst[key] = 0;
        this.pledgedMqValueSecond[key] = 0;
        this.pledgedValueFirst[key] = 0;
        this.pledgedValueSecond[key] = 0;
        this.calculateItemTotals(key);
        for (const item of this.differences.mapListObject[key].mapDiffOpinion) {
          // Calcolo i prodotti dei vari item
          item.commSurfaceFirst =
            item.grossSurfaceFirst * item.commCoefficientFirst;
          item.commSurfaceSecond =
            item.grossSurfaceSecond * item.commCoefficientSecond;
          item.bookValueFirst = item.commSurfaceFirst * item.mqValueFirst;
          item.bookValueSecond = item.commSurfaceSecond * item.mqValueSecond;
          item.pledgedValueFirst =
            item.pledgedMqValueFirst * item.commSurfaceFirst;
          item.pledgedValueSecond =
            item.pledgedMqValueSecond * item.commSurfaceSecond;
          // Calcolo i valori totali da mostrare su ogni riga di ogni gruppo
          this.grossSurfaceFirst[key] += item.grossSurfaceFirst;
          this.grossSurfaceSecond[key] += item.grossSurfaceSecond;
          this.commCoeffFirst[key] += item.commCoefficientFirst;
          this.commCoeffSecond[key] += item.commCoefficientSecond;
          this.commSurfFirst[key] += item.commSurfaceFirst;
          this.commSurfSecond[key] += item.commSurfaceSecond;
          this.mqValueFirst[key] += item.mqValueFirst;
          this.mqValueSecond[key] += item.mqValueSecond;
          this.commValueFirst[key] += item.bookValueFirst;
          this.commValueSecond[key] += item.bookValueSecond;
          this.pledgedMqValueFirst[key] += item.pledgedMqValueFirst;
          this.pledgedMqValueSecond[key] += item.pledgedMqValueSecond;
          this.pledgedValueFirst[key] += item.pledgedValueFirst;
          this.pledgedValueSecond[key] += item.pledgedValueSecond;
        }
      }
    }
  }

  saveDraft(): Observable<any> {
    const toSave = this.createObjectToSave();
    return this._genericTaskService.saveThirdOpinionData(
      this.positionId,
      toSave
    );
  }

  save() {
    this.saveDraft().subscribe(res => {
      this._landingService.goNextPage(
        this.positionId,
        this.currentTask,
        this.wizardCode,
        this._activatedRoute
      );
    });
  }

  private createObjectToSave(): any {
    const obj: any = {};
    obj['totBookValue'] = this.differences.totBookValue.thirdOpinionValue;
    obj['pledgedUnitValue'] = 0;
    obj['totPledgedValue'] = this.differences.totPledgedValue.thirdOpinionValue;
    obj[
      'totPrudentialValue'
    ] = this.differences.totPrudentialValue.thirdOpinionValue;
    obj['marketValue'] = this.differences.totMarketValue.thirdOpinionValue;
    obj['insuranceValue'] = this.differences.totVAlAss.thirdOpinionValue;
    obj['actualValue'] = this.differences.totVAlReal.thirdOpinionValue;
    obj['objevaluationMap'] = this.differences.mapListObject;
    obj['conclusion'] = this.differences.conclusion;
    obj['notes'] = this.differences.notes;
    return obj;
  }

  goToPreviousTask() {
    this._landingService.goToPreviousTask(this.positionId, this.wizardCode, this._activatedRoute);
  }

  goToGenericTask() {
    this._landingService.goToGenericTask(this.positionId, this.bpmTaskId, this.bpmTaskCod);
  }

  selectProperty(property: string, type: string, row: any, itemName: string) {
    switch (property) {
      case this.PropertyType.GROSS_SURFACE:
        row.grossSurfaceThird =
          type === this.OpinionType.FIRST_OPINION
            ? row.grossSurfaceFirst
            : row.grossSurfaceSecond;
        this.calculateCommSurface(row, itemName);
        break;
      case this.PropertyType.COMM_COEFFICIENT:
        if (type === this.OpinionType.FIRST_OPINION) {
          row.commCoefficientThird = row.commCoefficientFirst;
        } else {
          row.commCoefficientThird = row.commCoefficientSecond;
        }
        this.calculateCommSurface(row, itemName);
        break;
      case this.PropertyType.COMM_SURFACE:
        if (type === this.OpinionType.FIRST_OPINION) {
          row.commSurfaceThird = row.commSurfaceFirst;
        } else {
          row.commSurfaceThird = row.commSurfaceSecond;
        }
        this.calculateBookValue(row, itemName);
        break;
      case this.PropertyType.MQ_VALUE:
        if (type === this.OpinionType.FIRST_OPINION) {
          row.mqValueThird = row.mqValueFirst;
        } else {
          row.mqValueThird = row.mqValueSecond;
        }
        this.calculateBookValue(row, itemName);
        break;
      case this.PropertyType.PLEDGED_MQ_VALUE:
        if (type === this.OpinionType.FIRST_OPINION) {
          row.pledgedMqValueThird = row.pledgedMqValueFirst;
        } else {
          row.pledgedMqValueThird = row.pledgedMqValueSecond;
        }
        this.calculatePledgedValue(row, itemName);
        break;
    }
  }

  public calculateCommSurface(row, itemName: string) {
    row.commSurfaceThird = row.grossSurfaceThird * row.commCoefficientThird;
    this.calculateBookValue(row, itemName);
    this.calculatePledgedValue(row, itemName);
  }

  public calculateBookValue(row, itemName: string) {
    row.bookValueThird = row.mqValueThird * row.commSurfaceThird;
    this.calculateItemTotals(itemName);
  }

  public calculatePledgedValue(row, itemName: string) {
    row.pledgedValueThird = row.pledgedMqValueThird * row.commSurfaceThird;
    this.calculateItemTotals(itemName);
  }

  private calculateItemTotals(itemName: string) {
    if (
      this.differences &&
      this.differences.mapListObject &&
      this.differences.mapListObject[itemName]
    ) {
      this.grossSurfaceThird[itemName] = 0;
      this.commCoeffThird[itemName] = 0;
      this.commSurfThird[itemName] = 0;
      this.mqValueThird[itemName] = 0;
      this.commValueThird[itemName] = 0;
      this.pledgedMqValueThird[itemName] = 0;
      this.pledgedValueThird[itemName] = 0;
      for (const item of this.differences.mapListObject[itemName]
        .mapDiffOpinion) {
        this.grossSurfaceThird[itemName] += item.grossSurfaceThird;
        this.commCoeffThird[itemName] += item.commCoefficientThird;
        this.commSurfThird[itemName] += item.commSurfaceThird;
        this.mqValueThird[itemName] += item.mqValueThird;
        this.commValueThird[itemName] += item.bookValueThird;
        this.pledgedMqValueThird[itemName] += item.pledgedMqValueThird;
        this.pledgedValueThird[itemName] += item.pledgedValueThird;
      }
    }
  }

  public getObjectInfo(itemName: string): string {
    if (
      this.differences &&
      this.differences.mapListObject &&
      this.differences.mapListObject[itemName]
    ) {
      const item = this.differences.mapListObject[itemName];
      return (
        this._translateService.instant('UBZ.SITE_CONTENT.110001') +
        ': ' +
        item['reRegistrySheet'] +
        '\n' +
        this._translateService.instant('UBZ.SITE_CONTENT.110010') +
        ': ' +
        item['reRegistryPart'] +
        '\n' +
        this._translateService.instant('UBZ.SITE_CONTENT.110011') +
        ': ' +
        item['reRegistrySub']
      );
    } else {
      return '';
    }
  }

  openModal(id, label, structureType?, ruleId?) {
    this.selectedIdToDelete = id;
    this.modalIsOpen = true;
    this.labelObjToDelete = label;
    this.selectedStructToDelete = structureType;
    this.selectedRuleToDelete = ruleId;
  }

  closeModal() {
    this.modalIsOpen = false;
    this.selectedIdToDelete = null;
    this.selectedStructToDelete = null;
    this.selectedRuleToDelete = null;
  }

  confirmDelete() {
    this.launchDelete().subscribe(x => {
      this.modalIsOpen = false;
      this.ngOnInit();
    });
  }

  launchDelete() {
    if (this.selectedStructToDelete && this.selectedRuleToDelete) {
      return this._appraisalCompilationService.deleteConsistency(
        this.selectedIdToDelete,
        this.selectedStructToDelete,
        this.selectedRuleToDelete
      );
    } else {
      return this._appraisalCompilationService.deleteAsset(
        this.selectedIdToDelete
      );
    }
  }
}
