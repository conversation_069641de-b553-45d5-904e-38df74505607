import {
  Component,
  OnInit,
  Input,
  <PERSON><PERSON><PERSON><PERSON>,
  Query<PERSON>ist,
  ViewChildren,
  ElementRef,
  ViewChild
} from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import 'rxjs/add/observable/forkJoin';
import 'rxjs/add/operator/switchMap';
import { ModalDirective } from 'ngx-bootstrap/modal';

// Models
import { AppraisalInfoResponse } from '../../model/appraisal-info-response';

// Components
import { ApplicationFormComponent } from '../../../shared/application-form/components/application-form.component';

// Services
import { AssetService } from '../../../simulation/services/asset/asset.service';
import { Guarantee } from '../../model/guarantee';
import { LandingService } from '../../../simulation/services/landing/landing.service';
import { GenericInfoService } from '../../../simulation/services/generic-info/generic-info.service';
import { PositionService } from '../../../shared/position/position.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-appraisal-info',
  templateUrl: './appraisal-info.component.html',
  styleUrls: ['./appraisal-info.component.css']
})
export class AppraisalInfoComponent implements OnInit, OnDestroy {
  public requestId: string;
  wizardCode: string;
  taskCode = 'UBZ-REQ-AST';
  assetResponse: AppraisalInfoResponse;
  @ViewChildren(ApplicationFormComponent)
  applicationFormComponents: QueryList<ApplicationFormComponent>;
  @ViewChildren('saveAsset') saveAsset: QueryList<ElementRef>;
  readOnlyAssets: Object = {};
  // FIXME - TOGLI SE IL CAMBIO AD OGGETTO FUNZIONA CORRETTAMENTE
  // readOnlyAssets: boolean[] = [];
  stateBoxIsOpened = false;
  modalGuarantee: Guarantee = null;
  @ViewChild('guaranteeBox') public guaranteeBox: ModalDirective = null;
  guaranteesList: Guarantee[] = [];
  private selectedGuarantees: Object = {};
  // FIXME - TOGLI SE IL CAMBIO AD OGGETTO FUNZIONA CORRETTAMENTE
  // private selectedGuarantees: any[] = [];
  assetToOpen = -1;
  deleteModalIsOpen = false;
  selectedAssetId: number;
  assetFormValid: any = {};
  canAddAsset = true;
  canModifyAsset = true;
  canDeleteAsset = true;
  subscription: Subscription;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    public assetService: AssetService,
    private positionService: PositionService,
    public _landingService: LandingService,
    private _genericInfoService: GenericInfoService
  ) { }
  ngOnInit() {
    this.activatedRoute.parent.params
      .switchMap((params: Params) => {
        this.requestId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        return Observable.forkJoin(
          // Prendo i dati della richiesta a partire dall'ID nel parametro
          this.assetService.getAssetList(this.requestId, this.wizardCode),
          this.positionService.getCollateralData(this.requestId),
          this._genericInfoService.getGenericInfo(
            this.requestId,
            this.wizardCode
          )
        );
      })
      .subscribe(res => {
        this.assetResponse = res[0];
        if (
          this.assetResponse['listObjcet'] &&
          this.assetResponse['listObjcet'].length > 0
        ) {
          this.assetResponse['listObjcet'].forEach(element => {
            this.assetFormValid[element.idObject] = true;
          });
        }
        this.guaranteesList = res[1];
        this.assetService.showRestrictionsCheckboxes = res[2].ricSalFram;
        // Imposta originationProcess nel landingService e scatena il metodo di controllo task
        // FIXME - togliere codice chiodato
        // this._landingService.originationProcess = 'EMP';
        this._landingService.originationProcess = res[2].originationProcess;
        this.checkAssetAddPermission(res[2]);
        this._landingService.checkIfCompletedTask(
          this.requestId,
          this.taskCode
        );
        this.initializeAssetListProps();
        this.selectDefaultGuarantees();
        this.checkSaveIsEnable();
      });
  }
  // Se c'è solo una garanzia, questa è selezionata di default
  selectDefaultGuarantees(): boolean {
    // FIXME - TOGLIERE SE REWRITE DEL METODO FUNZIONA CORRETTAMENTE
    // if (this.guaranteesList.length === 1) {
    //   this.selectedGuarantees = this.guaranteesList[0];
    //   return true;
    // }
    // return false;
    if (this.guaranteesList.length === 1) {
      for (const asset of this.assetResponse.listObjcet) {
        this.selectedGuarantees[
          asset.idObject
        ] = this.guaranteesList[0].progCollateral;
      }
      return true;
    }
    return false;
  }

  private checkAssetAddPermission(appraisalInfo: any) {
    this.canAddAsset = true;
    if (appraisalInfo.macroProcess === 'AGG') {
      this.canAddAsset = false;
    }
    const nonEditableAssetAppScope = ['AGG', 'AGD'];
    if (nonEditableAssetAppScope.indexOf(appraisalInfo.appraisalScope) > -1) {
      this.canAddAsset = false;
      this.canModifyAsset = false;
    }
    const forbiddenAssetAppType = [
      'SAL',
      'FLA',
      'RES',
      'FRA'
    ];
    if (forbiddenAssetAppType.indexOf(appraisalInfo.appraisalType) > -1) {
      this.canAddAsset = false;
      this.canDeleteAsset = false;
      this.canModifyAsset = false;
    }
  }

  // Scorre la listObject degli asset ricevuti
  private initializeAssetListProps(): void {
    this.assetResponse.listObjcet.forEach(asset => {
      this.selectedGuarantees[asset.idObject] = asset.progCollateral;
      this.assetService.assetsSelectedWithCheckbox[asset.idObject] =
        asset.limSelectedFlag;
      if (asset.idObject === this.assetService.firstTimeAssets[0]) {
        this.readOnlyAssets[this.assetService.firstTimeAssets[0]] = false;
      } else if (asset.progCollateral === null) {
        this.readOnlyAssets[asset.idObject] = false;
      } else {
        this.readOnlyAssets[asset.idObject] = true;
      }
    });
    this.assetToOpen = this.assetService.firstTimeAssets[0];
    this.assetService.firstTimeAssets = [];
    // FIXME - TOGLIERE SE REWRITE DEL METODO FUNZIONA CORRETTAMENTE
    // for (const index in this.assetResponse.listObjcet) {
    //   if (this.assetResponse.listObjcet.hasOwnProperty(index)) {
    //     const asset = this.assetResponse.listObjcet[index];
    //     this.selectedGuarantees[
    //       this.assetResponse.listObjcet[index].idObject
    //     ] = this.assetResponse.listObjcet[index].progCollateral;
    //     this.assetService.assetsSelectedWithCheckbox[asset.idObject] =
    //       asset.limSelectedFlag;
    //     if (
    //       this.assetResponse.listObjcet[index].idObject ===
    //       this.assetService.firstTimeAssets[0]
    //     ) {
    //       this.readOnlyAssets[this.assetService.firstTimeAssets[0]] = false;
    //     } else if (
    //       this.assetService.listObjcet[index].progCollateral === null
    //     ) {
    //       this.readOnlyAssets[
    //         this.assetService.listObjcet[index].idObject
    //       ] = false;
    //     } else {
    //       this.readOnlyAssets[
    //         this.assetResponse.listObjcet[index].idObject
    //       ] = true;
    //     }
    //   }
    // }
    // this.assetToOpen = this.assetService.firstTimeAssets[0];
    // this.assetService.firstTimeAssets = [];
  }
  goToNewAsset() {
    this.router.navigate([`../new-asset`], { relativeTo: this.activatedRoute });
  }
  goToAssetSearch() {
    this.router.navigate([`../asset-search`], {
      relativeTo: this.activatedRoute
    });
  }
  deleteAsset(assetId: number) {
    this.assetService
      .deleteAsset(assetId, this.wizardCode, this.requestId)
      .subscribe(x =>
        this.assetService
          .getAssetList(this.requestId, this.wizardCode)
          .subscribe(res => {
            this.assetResponse = res;
            this.initializeAssetListProps();
            this.checkSaveIsEnable();
          })
      );
  }
  private getApplicationFormForAssetId(
    assetId: number
  ): ApplicationFormComponent {
    for (const index in this.applicationFormComponents.toArray()) {
      if (
        parseInt(this.applicationFormComponents.toArray()[index].idCode, 10) ===
        assetId
      ) {
        return this.applicationFormComponents.toArray()[index];
      }
    }
    return null;
  }
  // Crea il modello per l'assetId passato in input
  // Se la richiesta è TGP (_landingService.originationProcess === 'TGP') la garanzia non c'è e si settano
  // progCollateral, collateralTecForm e garantType a null
  private getModelForAssetId(assetId: number): any {
    const guarantee = this.getSelectedGuarantee(assetId);
    const apf: ApplicationFormComponent = this.getApplicationFormForAssetId(
      assetId
    );
    // Se la garanzia non è presente si setta il valore di collateral a null
    let collateralObj = null;
    if (guarantee) {
      collateralObj = {
        progCollateral: guarantee.progCollateral,
        collateralTecForm: guarantee.collateralTecForm,
        garantType: guarantee.garantType
      };
    }
    if (apf) {
      return {
        page: apf.page,
        apfmap: apf.model,
        collateral: collateralObj
      };
    } else {
      return null;
    }
  }
  public refreshApplicationFormForAssetId(assetId: number) {
    this.getApplicationFormForAssetId(assetId).refreshApplicationForm();
    this.updateAccordionTitleForAssetId(assetId);
  }
  updateAccordionTitleForAssetId(assetId: number): void {
    let desc: string = '';
    const assetModel = this.getModelForAssetId(assetId);
    if (assetModel && assetModel.apfmap) {
      for (const key in assetModel.apfmap) {
        desc = this.getModelForAssetId(assetId).apfmap[key]['FD_AST_DES'];
      }
    }
    // Update of the title
    for (const asset of this.assetResponse.listObjcet) {
      if (asset.idObject === assetId) {
        asset.descObject = desc;
      }
    }
    // FIXME - TOGLIERE SE REWRITE DEL METODO FUNZIONA CORRETTAMENTE
    // let desc: string;
    // // Retrieve the description in the model
    // for (const key in this.getModelForAssetId(assetId).apfmap) {
    //   if (this.getModelForAssetId(assetId).apfmap.hasOwnProperty(key)) {
    //     desc = this.getModelForAssetId(assetId).apfmap[key]['FD_AST_DES'];
    //   }
    // }
    // // Update of the title
    // for (const asset of this.assetResponse.listObjcet) {
    //   if (asset.idObject === assetId) {
    //     asset.descObject = desc;
    //   }
    // }
  }
  saveAssetChanges(assetId: number) {
    this.assetService
      .saveAssetChanges(
        this.getModelForAssetId(assetId),
        this.requestId,
        assetId,
        this.wizardCode
      )
      .subscribe(
        () => {
          this.saveAssetAfterSubscribe(assetId);
        },
        err => {
          if (err.businessError === 'W') {
            this.saveAssetAfterSubscribe(assetId);
          }
        }
      );
  }
  private saveAssetAfterSubscribe(assetId: number) {
    this.refreshApplicationFormForAssetId(assetId);
    this.setReadOnlyAssetProprieties(assetId, true);
    if (assetId === this.assetToOpen) {
      this.assetToOpen = -1;
    }
    this.checkSaveIsEnable();
  }
  setReadOnlyAssetProprieties(assetId: number, value: boolean) {
    this.readOnlyAssets[assetId] = value;
    this.assetService.saveIsEnable =
      !this.existAssetOpened() && !this.existFormNotValid();
  }
  // Controlla se c'è qualche asset aperto
  private existAssetOpened(): boolean {
    for (const index in this.readOnlyAssets) {
      if (!this.readOnlyAssets[index]) {
        return true;
      }
    }
    return false;
  }
  //controlla se ci sono asset con form invalidi
  private existFormNotValid(): boolean {
    for (const index in this.assetFormValid) {
      if (!this.assetFormValid[index]) {
        return true;
      }
    }
    return false;
  }
  // Raggruppa i pulsanti save delle garanzie ed invoca successivamente il submit del form
  // Se richiesta TGP (_landingService.originationProcess === 'TGP), salta ciclo e invoca direttamente saveAssetChanges(asset.idObject)
  upperSaveButtonClick(index: number) {
    if (this._landingService.originationProcess !== 'TGP') {
      for (const asset of this.saveAsset.toArray()) {
        if (asset.nativeElement.id === `saveAsset${index}`) {
          asset.nativeElement.click();
          break;
        }
      }
    } else {
      this.saveAssetChanges(this.assetResponse.listObjcet[index].idObject);
    }
    // FIXME - TOGLIERE SE REWRITE DEL METODO FUNZIONA CORRETTAMENTE
    // for (const i in this.saveAsset.toArray()) {
    //   if (
    //     this.saveAsset.toArray().hasOwnProperty(i) &&
    //     this.saveAsset.toArray()[i].nativeElement.id === `saveAsset${index}`
    //   ) {
    //     this.saveAsset.toArray()[i].nativeElement.click();
    //     break;
    //   }
    // }
  }
  openGuaranteeDetails(guarantee: Guarantee) {
    this.stateBoxIsOpened = true;
    this.modalGuarantee = guarantee;
  }
  onGuaranteeDetailsHidden() {
    this.guaranteeBox.hide();
  }
  closeGuaranteeDetails() {
    this.stateBoxIsOpened = false;
    this.modalGuarantee = null;
  }
  checkIfSelected(assetIndex: number, guaranteeIndex: number) {
    if (this.selectDefaultGuarantees()) {
      return true;
    } else {
      if (this.assetResponse.listObjcet && this.guaranteesList) {
        if (
          this.assetResponse.listObjcet[assetIndex].progCollateral ===
          this.guaranteesList[guaranteeIndex].progCollateral
        ) {
          return true;
        } else {
          return false;
        }
      }
    }
    // FIXME - TOGLIERE SE REWRITE DEL METODO FUNZIONA CORRETTAMENTE
    // if (this.guaranteesList.length === 1) {
    //   // Se c'è solo una garanzia, questa è selezionata di default
    //   this.selectedGuarantees[
    //     this.assetResponse.listObjcet[assetIndex].idObject
    //   ] = this.guaranteesList[guaranteeIndex].progCollateral;
    //   return true;
    // }
    // if (this.assetResponse.listObjcet && this.guaranteesList) {
    // if (
    //   this.assetResponse.listObjcet[assetIndex].progCollateral ===
    //   this.guaranteesList[guaranteeIndex].progCollateral
    // ) {
    //   return true;
    // } else {
    //   return false;
    // }
    // }
  }

  selectGuaranteeForAsset(assetIndex: number, guaranteeProgCollateral: string) {
    // this.selectedGuarantees = guaranteeProgCollateral;
    // FIXME - TOGLIERE SE REWRITE DEL METODO FUNZIONA CORRETTAMENTE
    this.selectedGuarantees[assetIndex] = guaranteeProgCollateral;
  }
  getSelectedGuarantee(assetId: number) {
    let selectedGuarantee = null;
    if (this.selectedGuarantees[assetId]) {
      this.guaranteesList.forEach(guarantee => {
        if (this.selectedGuarantees[assetId] === guarantee.progCollateral) {
          selectedGuarantee = guarantee;
        }
      });
    }
    return selectedGuarantee;
    // FIXME - TOGLIERE SE REWRITE DEL METODO FUNZIONA CORRETTAMENTE
    // if (this.selectedGuarantees[assetId]) {
    //   for (let i = 0; i < this.guaranteesList.length; i++) {
    //     if (
    //       this.selectedGuarantees[assetId] ===
    //       this.guaranteesList[i].progCollateral
    //     ) {
    //       return this.guaranteesList[i];
    //     }
    //   }
    // }
  }
  formNotValid(index: number, assetId: string) {
    if (this.applicationFormComponents.toArray()[index]) {
      if (this.applicationFormComponents.toArray()[index].appForm.invalid) {
        this.assetFormValid[assetId] = false;
        return true;
      }
    }
    // Se _landingService.originationProcess === 'TGP' la garanzia non è richiesta
    if (
      this._landingService.originationProcess !== 'TGP' &&
      !this.selectedGuarantees[assetId]
    ) {
      this.assetFormValid[assetId] = false;
      return true;
    }
    this.assetFormValid[assetId] = true;
    return false;
  }

  undoAsset(index: number, assetId: string) {
    const apf: ApplicationFormComponent = this.getApplicationFormForAssetId(
      +assetId
    );
    this.subscription = apf.formLoaded.subscribe(() => {
      this.formNotValid(index, assetId);
      this.setReadOnlyAssetProprieties(+assetId, true);
      if (this.subscription && !this.subscription.closed) {
        this.subscription.unsubscribe();
      }
    });
    this.refreshApplicationFormForAssetId(+assetId);
  }

  ngOnDestroy() {
    if (this.subscription && !this.subscription.closed) {
      this.subscription.unsubscribe();
    }
  }

  checkSaveIsEnable() {
    this.assetService.saveIsEnable = !this.existAssetOpened();

    if (this.existFormNotValid() === true) {
      this.assetService.saveIsEnable = false;
      return;
    }

    if (
      this.assetResponse &&
      this.assetResponse.listObjcet &&
      this.assetResponse.listObjcet.length === 0
    ) {
      this.assetService.saveIsEnable = false;
      return;
    }
    // Le garanzia su gli asset sono obbligatorie sse _landingService.originationProcess !== TGP
    if (this._landingService.originationProcess !== 'TGP') {
      for (const asset of this.assetResponse.listObjcet) {
        if (!this.selectedGuarantees[asset.idObject]) {
          this.assetService.saveIsEnable = false;
          return;
        }
      }
    }
  }
  openDeleteModal(assetId: number) {
    this.selectedAssetId = assetId;
    this.deleteModalIsOpen = true;
  }

  // Gestisce il submit della modale di cancellazione utente
  submitDeleteModal() {
    this.assetService
      .deleteAsset(this.selectedAssetId, this.wizardCode, this.requestId)
      .subscribe(x => {
        this.assetFormValid[this.selectedAssetId] = true;
        this.closeDeleteModal();
      });
  }

  // Chiude e resetta le variabili per la modale di cancellazione
  closeDeleteModal() {
    this.deleteModalIsOpen = false;
    this.selectedAssetId = null;
    this.assetService
      .getAssetList(this.requestId, this.wizardCode)
      .subscribe(res => {
        this.assetResponse = res;
        this.initializeAssetListProps();
        this.checkSaveIsEnable();
      });
  }
  public changeSelectAll(event: any) {
    const isChecked = event.target.checked;
    for (const key in this.assetService.assetsSelectedWithCheckbox) {
      if (this.assetService.assetsSelectedWithCheckbox.hasOwnProperty(key)) {
        this.assetService.assetsSelectedWithCheckbox[key] = isChecked;
      }
    }
  }

}
