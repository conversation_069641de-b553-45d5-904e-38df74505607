import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'objectMapToObjectArray'
})
export class ObjectMapToObjectArrayPipe implements PipeTransform {
  transform(value: any, args?: any): any {
    const keys = [];
    for (const key in value) {
      if (value.hasOwnProperty(key)) {
        const obj = new Object();
        obj['_name_'] = key;
        for (const fieldName in value[key]) {
          if (value[key].hasOwnProperty(fieldName)) {
            obj[fieldName] = value[key][fieldName];
          }
        }
        keys.push(obj);
      }
    }
    return keys;
  }
}
