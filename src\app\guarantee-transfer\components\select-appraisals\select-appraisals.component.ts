import { Component, OnInit } from '@angular/core';
import { GuaranteeTransferService } from '../../services/guarantee-transfer.service';
import { Router } from '@angular/router';
import { Observable } from 'rxjs/Observable';

@Component({
  selector: 'app-select-appraisals',
  templateUrl: './select-appraisals.component.html'
})
export class SelectAppraisalsComponent implements OnInit {
  appraisalsCheckArray: boolean[];
  saveIsEnable: boolean = false;
  ndgAppraisals: any[] = [];
  allSelected: boolean = false; // flag su checkbox di seleziona tutti

  constructor(
    public guaranteeTransfer: GuaranteeTransferService,
    private router: Router
  ) { }

  ngOnInit() {
    setTimeout(() => (this.guaranteeTransfer.wizardStep = 1), 10);
    this.guaranteeTransfer
      .getNdgAppraisals(this.guaranteeTransfer.ndgOrigin)
      .subscribe(result => {
        if (result) {
          this.ndgAppraisals = result;
          this.appraisalsCheckArray = new Array(result.length).fill(false);
          this.setAppraisalsCheckArray();
        }
      });
  }

  /**
   * @name setAppraisalsCheckArray
   * @description Se le perizie sono state selezionate precedentemente scorre l'array
   * di queste per valorizzarle a video e al termine invoca il servizio per controllare l'abilitazione
   * del pulsante prosegui
  */
  setAppraisalsCheckArray(): void {
    this.ndgAppraisals.forEach((element, index) => {
      for (const selectedAppraisal of this.guaranteeTransfer.appraisalList) {
        if (selectedAppraisal.appraisalId === element.appraisalId) {
          this.appraisalsCheckArray[index] = true;
          break;
        }
      }
    });
    this.setSaveIsEnable();
  }

  /**
   * @name selectAll
   * @description Metodo legato al toggle del check seleziona tutti, seleziona tutte le perizie presenti
   */
  selectAll() {
    this.allSelected = !this.allSelected;
    this.appraisalsCheckArray.fill(this.allSelected);
    this.setSaveIsEnable();
  }

  /**
   * @name setSaveIsEnable
   * @description Sul toggle delle perizie elencate definisce l'abilitazione del pulsante prosegui
   */
  setSaveIsEnable(): boolean {
    for (const element of this.appraisalsCheckArray) {
      if (element) {
        return this.saveIsEnable = true;
      }
    }
    return this.saveIsEnable = false;
  }

  /**
   * @name sendAppraisalList
   * @description Inserisce le perizie selezionate all'interno del array appraisalList
   * per passarlo al servizio di salvataggio e sull'esito positivo di questo esegue la navigazione
   * verso il prossimo step del wizard
  */
  sendAppraisalList(): void {
    this.guaranteeTransfer.appraisalList = new Array();
    this.appraisalsCheckArray.forEach((element, index) => {
      if (element === true) {
        this.guaranteeTransfer.appraisalList.push(this.ndgAppraisals[index]);
      }
    });
    // FIXME - ESEGUI ROUTING RELATIVO FRA LE ROTTE CHILD

    this.checkAnacreditGuarantees()
      .subscribe(resCont => {
      this.guaranteeTransfer
        .saveSelectedAppraisal(this.guaranteeTransfer.appraisalList)
        .subscribe(() => {
          this.router.navigate([`guaranteeTransfer/${this.guaranteeTransfer.ndgOrigin}/${this.guaranteeTransfer.ndgDestination}/start-guarantees`]);
        });
    });
  }

  checkAnacreditGuarantees(): Observable<any> {
    //stop wizard if one appraisal ha Antergate Estimation
    let appraisalListAntergateCheck = [];
    this.guaranteeTransfer.appraisalList.forEach((element, index) => {
      appraisalListAntergateCheck.push(element['appraisalId']);
    });
    return this.guaranteeTransfer.checkAnacreditGuarantees(appraisalListAntergateCheck);
  }

  cancelRequest() {
    this.router.navigate(['/']);
  }
}
