<div class="box-wrap">
  <div class="box-layout">
    <!-- DEFINIZIONE DELL'HEADER PER I WIZARD STATICI -->
    <div class="box-row" *ngIf="staticHeaderArray.length">
      <ng-container *ngFor="let property of staticHeaderArray">
        <div class="box-col" *ngIf="property['label'] && property['label'] !== 'type'">
          <div class="table-display">
            <div class="vertical-align">
              <label>{{ property['label'] | translate }}</label>
              <p>{{ property['value'] ? property['value'] : '-'}}</p>
            </div>
          </div>
        </div>
      </ng-container>
      <div class="box-col" *ngIf="isFrazionamentoHeader">
        <div class="table-display">
          <div class="vertical-align">
            <label>{{'UBZ.SITE_CONTENT.11100011' | translate }} </label>
            <p>
              <ng-container *appAuthKey="'UBZ_POSITION.HEADER_LOCK'">
                <span class="lock" style="cursor: pointer" (click)="clickOnLockFrazionamento()">
                  <i [class]="lockClass[(currentUser === this.lockingUser.toUpperCase()).toString()]" aria-hidden="true"></i>
                </span>
              </ng-container>
              {{'UBZ.SITE_CONTENT.10110' | translate }}
              <span>{{lockingUser ? lockingUser : '---'}}</span>              
            </p>
          </div>
        </div>
      </div>
    </div>
    <!-- DEFINIZIONE DELL'HEADER PER I WIZARD DINAMICI -->
    <div class="box-row" *ngIf="!staticHeaderArray.length">
      <div class="box-col">
        <div class="table-display">
          <div class="vertical-align">
            <label>{{'UBZ.SITE_CONTENT.10001' | translate }}</label>
            <p>
              {{position.applicant ? position.applicant.ndg : '---'}}
              <span>{{position.applicant ? position.applicant.heading : '---'}}</span>
            </p>
          </div>
        </div>
      </div>
      <div [ngClass]="{'box-col': wizardCode !== constants.wizardCodes['PER'], 'box-col-16': wizardCode === constants.wizardCodes['PER']}">
        <div class="table-display">
          <div class="vertical-align">
            <label *ngIf="wizardCode === constants.wizardCodes['SIM']">{{'UBZ.SITE_CONTENT.10010' | translate }}</label>
            <label *ngIf="wizardCode === constants.wizardCodes['REQ']">{{'UBZ.SITE_CONTENT.10100101' | translate }}</label>
            <label *ngIf="wizardCode === constants.wizardCodes['PER']">{{'UBZ.SITE_CONTENT.10010001' | translate }}</label>
            <p>
              {{positionId}}
            </p>
          </div>
        </div>
      </div>
      <div *ngIf="wizardCode === constants.wizardCodes['PER']" class="box-col-16">
        <div class="table-display">
          <div class="vertical-align">
          
            <label>{{'UBZ.SITE_CONTENT.*********' | translate }}</label>
            <p>
              {{appraisalCompany}}
            </p>
          </div>
        </div>
      </div>
      <div [ngClass]="{'box-col': wizardCode !== constants.wizardCodes['PER'], 'box-col-16': wizardCode === constants.wizardCodes['PER']}">
        <div class="table-display">
          <div class="vertical-align">
            <label>{{'UBZ.SITE_CONTENT.11100011' | translate }} </label>
            <p>
              <ng-container *appAuthKey="'UBZ_POSITION.HEADER_LOCK'">
                <ng-container *ngIf="!isSimulationFinished && !avoidLocking">
                  <span class="lock" style="cursor: pointer" *ngIf="wizardCode !== constants.wizardCodes['PER']" (click)="clickOnLock()">
                    <i [class]="lockClass[locked.toString()]" aria-hidden="true"></i>
                  </span>
                </ng-container>
              </ng-container>
              {{'UBZ.SITE_CONTENT.10110' | translate }}
              <span *ngIf="wizardCode !== constants.wizardCodes['PER']">{{position.userInCharge ? position.userInCharge : '---'}}</span>
              <span *ngIf="wizardCode === constants.wizardCodes['PER']">{{lockingUser ? lockingUser : '---'}}</span>
            </p>
          </div>
        </div>
      </div>
      <div class="hidden-sm hidden-xs" [ngClass]="{'box-col': wizardCode !== constants.wizardCodes['PER'], 'box-col-16': wizardCode === constants.wizardCodes['PER']}">
        <div class="table-display">
          <div class="vertical-align">
            <label>{{'UBZ.SITE_CONTENT.10011' | translate }}</label>
            <p>
              <button type="button" class="btn btn-clean waves-effect waves-secondary pull-right" data-toggle="modal" data-target="#stato-pratica"
                (click)="showStateBoxDialog()">
                <i class="icon-plus_open_lightbox"></i>
              </button>
              <span>{{ getPositionStatusName(position.phaseCode , position.statusCode) | translate}}</span>
            </p>
          </div>
        </div>
      </div>

      <div *ngIf="wizardCode === constants.wizardCodes['REQ'] || wizardCode === constants.wizardCodes['PER']" class="hidden-sm hidden-xs"
        [ngClass]="{'box-col': wizardCode !== constants.wizardCodes['PER'], 'box-col-16': wizardCode === constants.wizardCodes['PER']}">
        <div class="table-display">
          <div class="vertical-align">
            <label>{{'UBZ.SITE_CONTENT.11111011' | translate }}</label>
            <p>
              <button *ngIf="collaterals && collaterals.length !== 0" type="button" class="btn btn-clean waves-effect waves-secondary pull-right"
                data-toggle="modal" data-target="#stato-pratica" (click)="showCollateralsBoxDialog()">
                <i class="icon-plus_open_lightbox"></i>
              </button>
              {{getCollateralIds()}}
            </p>
          </div>
        </div>
      </div>

    </div>
  </div>
  <div class="box-toggle hidden-md hidden-lg">
    <button type="button" class="btn btn-clean">
      <i class="icon-menu"></i>
    </button>
  </div>
</div>

<!--MODAL BOX STATE-->
<div class="modal fade" id="stato-pratica" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #stateBox="bs-modal">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.10101' | translate }}</h2>
        <button type="button" class="close pull-right" data-dismiss="modal" aria-label="Close" (click)="hideStateBoxDialog()" *ngIf="positionService.externalLinkParams['method'] !== 'dettaglioStatoPerizia'">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <div *ngFor="let row of positionObjects; let first = first" class="row">
          <div class="col-xs-6">
            <div class="status-check inactive">
              <i class="icon-check" [ngClass]="{'icon-check-active': !first}"></i>
              <div class="simulation">
                <span class="sim-label" [ngClass]="{'active-status': !first}">
                  {{ (statusDomain && statusDomain[row.statusType]) ? (statusDomain[row.statusType].translationCod | translate) : '' }}
                </span>
                <span class="sim-description">
                  <strong>{{'UBZ.SITE_CONTENT.10110' | translate }}</strong> {{row.user}}</span>
              </div>
            </div>
          </div>
          <div class="col-xs-6">
            <div class="status-check inactive text-right">
              <span class="data-table">
                <strong>{{positionsStatusNames[row.phaseCod + row.statusCod]?.translationStatusCod | translate}}</strong>
              </span>
              <span class="data-stamp">
                <strong>{{'UBZ.SITE_CONTENT.10111' | translate }}</strong> {{row.statusChange | date: 'dd-MM-yyyy' }}
                <strong>{{'UBZ.SITE_CONTENT.11000' | translate }}</strong> {{row.statusChange | customTime }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!--  MODAL COLLATERALS -->
<div class="modal fade" id="id-garanzie" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #collateralsBox="bs-modal">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.11110011' | translate }}</h2>
        <button type="button" class="close pull-right" data-dismiss="modal" aria-label="Close" (click)="hideCollateralsBoxDialog()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <table class="uc-table">
              <thead>
                <tr>
                  <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10011101' | translate }}</th>
                  <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10010101' | translate }}</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let c of collaterals">
                  <td data-label="Progressivo">
                    <span *ngIf="wizardCode !== constants.wizardCodes['PER']">{{c.progCollateral}}</span>
                    <span *ngIf="wizardCode === constants.wizardCodes['PER']">{{c.jointCod}}</span>
                  </td>
                  <td data-label="Forma">
                    <span *ngIf="wizardCode !== constants.wizardCodes['PER']">{{c.collateralTecForm}} - {{c.collateralDesc}}</span>
                    <span *ngIf="wizardCode === constants.wizardCodes['PER']">{{c.collatTecForm}} - {{c.collatDesc}}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<app-modal-lock-confirm [positionId]="position.id" [productType]="position.productType" [isOpen]="lockModalIsOpen" [operationType]="lockModalOpType"
  [locked]="locked" [position]="position" [statusDomain]="positionsStatusNames" (close)="modalClose()" (positionLocked)="locked = true; positionLocked.emit()"
  (positionUnlocked)="locked = false; positionUnlocked.emit()"></app-modal-lock-confirm>

<!--  MODAL LOCK FRAZIONAMENTO -->
<div *ngIf="staticHeaderArray.length && isFrazionamentoHeader" class="modal fade" id="id-lock-fra" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #lockFraBox="bs-modal">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2 *ngIf="currentUser  !== this.lockingUser.toUpperCase()">{{'UBZ.SITE_CONTENT.110010110' | translate }}</h2>
        <h2 *ngIf="currentUser   === this.lockingUser.toUpperCase()">{{'Pratica in carico' | translate }}</h2>
        <button type="reset" class="close pull-right" aria-label="Close" (click)="hideLockFraBox()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="row pratica-states pb-3" *ngIf="currentUser   === this.lockingUser.toUpperCase()">
          <div class="col-sm-12">
            {{'Pratica in carico a utente corrente.' | translate }}
          </div>
        </div>
        <div class="row pratica-states pb-3" *ngIf="currentUser !== this.lockingUser.toUpperCase()">
          <div class="col-sm-6">
            <div class="pratica-states__iconbox iconbox--active float-left">
              <i class="fa fa-lock"></i>
            </div>
            <div class="pratica-states__main float-left">
              <span class="d-block pratica-states--active">{{'UBZ.SITE_CONTENT.1111100' | translate }}</span>
              <span class="d-block pratica-states--sub">{{ lockingUser ? lockingUser : '---' }}</span>
            </div>
          </div>
          <!-- <div class="col-sm-6 text-right">
            <div class="pratica-states_data">
              <span class="d-block pratica-states--active"><strong>{{ getStatusCode() | translate }}</strong></span>
              <span class="d-block pratica-states--sub"><strong>{{'UBZ.SITE_CONTENT.10111' | translate }}</strong> {{position.lastUpdate | date:'d/M/y' }} <strong>{{'UBZ.SITE_CONTENT.11000' | translate }}</strong> {{position.lastUpdate | date:'HH:mm:ss' }}</span>
            </div>
          </div> -->
        </div>
      </div>
      <div class="modal-footer">
        <button *ngIf="currentUser !== this.lockingUser.toUpperCase()" class="btn btn-primary waves-effect" (click)="submitLockFraModal()">
          <span>{{'UBZ.SITE_CONTENT.110011001' | translate }}</span>
        </button>
        <button *ngIf="currentUser === this.lockingUser.toUpperCase()" class="btn btn-primary waves-effect" (click)="hideLockFraBox()">
          <span>{{'Chiudi' | translate }}</span>
        </button>
      </div>
    </div>
  </div>
</div>
    