import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  Inject
} from '@angular/core';
import { NgForm } from '@angular/forms';
import { DOCUMENT } from '@angular/platform-browser';
import { NotesService } from '../../../../notes/services/notes.service';
import { Note } from '../../../../notes/model/note';

@Component({
  selector: 'app-modal-note',
  templateUrl: './modal-note.component.html',
  styleUrls: ['./modal-note.component.css'],
  providers: [NotesService]
})
export class ModalNoteComponent implements OnInit {
  @Input() isOpen = false;
  @Input() document: any;
  @Input() section: any;
  @Output() modalClose = new EventEmitter();
  @ViewChild('form1') form1: NgForm;
  isValidClass = { Y: 'state green', N: 'state red' };
  sendMail = false;
  notes: any;
  noteTitle = '';
  mailTo = '';
  mailCc = '';
  noteText = '';

  constructor(
    private notesService: NotesService,
    @Inject(DOCUMENT) private documentJS: any
  ) { }

  ngOnInit() {
    this.notesService.getNotes(this.document.prog, 'CHK').subscribe(x => {
      this.notes = x;
    });
  }

  closeModal(refreshPage: boolean) {
    this.isOpen = false;
    this.modalClose.emit(refreshPage);
  }

  onSubmit() {
    if (this.form1.valid) {
      if (this.sendMail === true) {
        let href = `mailto:${this.mailTo}?subject=${this.noteTitle}&body=${this
          .noteText}`;
        if (this.mailCc) {
          href = `${href}&cc=${this.mailCc}`;
        }
        this.documentJS.location.href = href;
        this.ngOnInit();
        this.closeModal(true);
      } else {
        const nota: Note = new Note(this.document.prog, 'CHK', 'M');
        nota['noteTitle'] = this.noteTitle;
        nota['noteDesc'] = this.noteText;
        nota['noteType'] = 'CHK';
        this.notesService.saveNote(nota).subscribe(() => {
          this.ngOnInit();
          this.closeModal(true);
        });
      }
    }
  }
}
