import { Injectable, Inject } from '@angular/core';
import { Http, Response } from '@angular/http';
import { TranslateLoader } from '@ngx-translate/core';
import { Observable } from 'rxjs/Observable';
import { APP_CONSTANTS, IAppConstants } from '../../app.constants';

@Injectable()
export class TranslationsLoader extends TranslateLoader {
  private url = '/UBZ-ESA-RS/service/umfService/translation/labels'; // URL to web api

  constructor(
    private http: Http,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) {
    super();
  }

  public getTranslation(lang: string): Observable<Object> {
    let labels = sessionStorage.getItem('labels');
    if (labels) {
      // Recupera la copia in cache
      return Observable.from([JSON.parse(labels)]);
    }
    return this.http
      .get(`${this.url}/${this.constants.processCode}/${lang}`, {
        withCredentials: true
      })
      .map((res: Response) => {
        const labels = res.json();
        sessionStorage.setItem('labels', JSON.stringify(labels));
        return labels;
      });
  }
}
