import {
  Component,
  Input,
  ElementRef,
  OnChanges,
  AfterViewChecked,
  SimpleChanges,
  Renderer2,
  On<PERSON><PERSON>roy
} from '@angular/core';

// Services
import { CustomerService } from '../../services';

@Component({
  selector: 'app-ndg-list-manager',
  templateUrl: './ndg-list-manager.component.html',
  styleUrls: ['./ndg-list-manager.component.css']
})
export class NdgListManagerComponent
  implements AfterViewChecked, OnChanges, OnDestroy {
  @Input()
  positionId: string;
  selectedNdg = '';
  maxVisibleTabs = 0;
  layoutDone = false;
  shouldShowDropdown = true;
  private onResizeFunction: any;

  constructor(
    public customerService: CustomerService,
    private elementRef: ElementRef,
    private _renderer: Renderer2
  ) {
    this.onResizeFunction = _renderer.listen(
      'window',
      'resize',
      this.debounce(() => {
        this.layout();
      })
    );
  }

  private debounce(func: () => void, wait = 70) {
    let h: any;
    return () => {
      clearTimeout(h);
      h = setTimeout(() => func(), wait);
    };
  }

  ngAfterViewChecked() {
    if (!this.customerService.coiNdgList || this.layoutDone) {
      return;
    }
    setTimeout(() => this.layout(), 0);
  }

  ngOnChanges(changes: SimpleChanges) {
    // Se non è cambiato nulla devo ritornare
    setTimeout(() => this.layout(), 0);
  }

  ngOnDestroy() {
    this.onResizeFunction(); // Remove the listener on the resize event
  }

  // Calcola il numero massimo di tab visibili in base alla larghezza della
  // finestra, e nasconde i tab restanti dentro a un menu a tendina
  layout() {
    if (!this.customerService.coiNdgList) {
      return;
    }
    const domElement = this.elementRef.nativeElement.querySelector('.MyTabs');
    const tabContainerWidth = domElement.offsetWidth;
    let tabWidth = 160; // larghezza ideale di un tab
    const dropdownWidth = 40; // larghezza del pulsante freccetta
    const someTab = domElement.querySelector('.MyTabs__Tab');

    if (someTab) {
      tabWidth = someTab.offsetWidth;
    }
    this.maxVisibleTabs = Math.floor(
      (tabContainerWidth - dropdownWidth) / tabWidth
    );
    this.layoutDone = true;
    this.shouldShowDropdown =
      this.customerService.coiNdgList.length > this.maxVisibleTabs;
  }

  /**
   * @function
   * @name toggleNdgDetail
   * @description Chiude il dettaglio del ndg in input è già aperto, se è chiuso lo apre
   * @param ndg ndg per il quale aprire/chiudere il dettaglio
   */
  toggleNdgDetail(ndg) {
    if (this.selectedNdg === ndg) {
      this.selectedNdg = null;
    } else {
      this.selectedNdg = ndg;
    }
  }
}
