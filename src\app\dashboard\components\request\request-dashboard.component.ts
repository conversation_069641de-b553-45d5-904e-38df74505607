import { Component, OnInit } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { Router } from '@angular/router';

import { DashboardDetailComponent } from '../dashboard-detail.component';
import { SearchService } from '../../../shared/search/search.service';
import { DomainService } from '../../../shared/domain/domain.service';
import { Navigable } from '../../model/navigable-interface';

@Component({
  selector: 'app-request-dashboard',
  templateUrl: '../dashboard-detail.component.html',
  styleUrls: ['./request-dashboard.component.css']
})
export class RequestDashboardComponent extends DashboardDetailComponent
  implements OnInit, Navigable {
  headFields = this.pageHeaders['REQ'];
  fieldsConditions = this.pageFieldConditions['REQ'];

  constructor(
    public searchService: SearchService,
    public domainService: DomainService,
    public router: Router
  ) {
    super(domainService, searchService);
  }

  ngOnInit() {
    super.ngOnInit();
  }

  getPositionsCounts(
    inChargeUser: boolean,
    inChargeBranch: boolean
  ): Observable<any> {
    return this.searchService.getRequestsCounts(inChargeUser, inChargeBranch);
  }

  getPositionsData(
    excel: boolean,
    page: number,
    pageSize: number,
    orderBy: string,
    asc: boolean,
    filter: any
  ): Observable<any> {
    return this.searchService.getRequestsData(
      excel,
      page,
      pageSize,
      orderBy,
      asc,
      filter
    );
  }

  goToTarget(id) {
    this.router.navigateByUrl(`landing/WRPE/${id}`);
  }
}
