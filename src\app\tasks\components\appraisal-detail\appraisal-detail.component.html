<div class="row controls">
  <div class="col-sm-12 text-right">
    <ng-container *ngIf="isXmlVisible && !isCtu">
      <ng-container *appAuthKey="'UBZ_APPRAISAL_DETAILS_DOWNLOAD_XML'">
        <button type="button" class="btn btn-empty pull-right" (click)="downloadXml()">Download XML Perizia</button>
      </ng-container>
      <ng-container *appAuthKey="'UBZ_APPRAISAL_DETAILS_UPLOAD_XML'">
        <button type="button" class="btn btn-empty pull-right" (click)="uploadXml()">Upload XML Perizia</button>
      </ng-container>
    </ng-container>
    <ng-container *appAuthKey="'UBZ_APPRAISAL_DETAIL_PRINT'">
      <button *ngIf="originationProcess !== 'MIG' && !massiveUpload && !isCtu && showPrintButton" type="button" class="btn btn-empty pull-right" (click)="printAppraisalDetail()">{{'UBZ.SITE_CONTENT.1100111000' | translate }}</button>
    </ng-container>
    <ng-container *appAuthKey="'UBZ_APPRAISAL_DETAILS_RE_OPEN'">
      <app-drop-assignment-button *ngIf="!positionService.isInternalSecondOpinion && !isCtu" [positionId]="positionId" type="RIA" (saveSuccessful)="goToDashboard(true)"></app-drop-assignment-button>
    </ng-container>
    <ng-container *appAuthKey="'UBZ_APPRAISAL_DETAILS_VIEW_APPRAISAL'">
      <button *ngIf="!isCompileAppraisalVisible && originationProcess !== 'MIG' && !massiveUpload" type="button" class="btn btn-empty pull-right" (click)="goToAppraisalCompile(true)">{{'UBZ.SITE_CONTENT.1010111001' | translate }}</button>
    </ng-container>
    <ng-container *appAuthKey="'UBZ_APPRAISAL_DETAILS_VIEW_APPRAISAL_MIG'">
      <button *ngIf="!isCompileAppraisalVisible && (originationProcess === 'MIG' || massiveUpload || isCtu)" type="button" class="btn btn-empty pull-right" (click)="goToAppraisalCompile(true)">{{'UBZ.SITE_CONTENT.1010111001' | translate }}</button>
    </ng-container>
    <ng-container *appAuthKey="'UBZ_START_INDIVIDUAL_SECOND_OPINION'">
      <button *ngIf="isIndividualSecondOpinionAllowed" type="button" class="btn btn-empty pull-right" (click)="startIndividualSecondOpinion()">{{'UBZ.SITE_CONTENT.10011100110' | translate }}</button>
    </ng-container>
    <ng-container *ngIf="canSeeButtons && (originationProcess !== 'MIG' && !massiveUpload)">
      <ng-container *appAuthKey="'UBZ_APPRAISAL_DETAILS_COMPILE'">
        <div *ngIf="isTaskLocked && isCompileAppraisalVisible" class="inline-status">
          <span *ngIf="appraisalCompiled" class="state green"></span>
          <span *ngIf="!appraisalCompiled" class="state red"></span>
          <button type="button" class="btn btn-empty pull-right only-side-padding" (click)="goToAppraisalCompile(false)">{{compileAppraisalLabel | translate }}</button>
        </div>
      </ng-container>
      <app-task-access-rights [positionId]="positionId" [taskId]="taskId" [taskCod]="taskCod" [isCte]="isMacroProcessCte" (taskLocked)="taskLocked()" (taskUnlocked)="taskUnlocked()" (taskLockedByOtherUser)="tasklockedByOtherUser($event)">
        <app-task-button *ngIf="isCompileAppraisalVisible" [eventCod]="'UBZ_PRZ_OK'" [buttonLabel]="'Valida'" [visible]="appraisalCompiled" (success)="goToDashboard(false)"></app-task-button>
      </app-task-access-rights>
      <ng-container *appAuthKey="'UBZ_APPRAISAL_DETAILS_REVOKE'">
        <app-drop-assignment-button *ngIf="isTaskLocked" [positionId]="positionId" type="REV" (saveSuccessful)="goToDashboard(true)"></app-drop-assignment-button>
      </ng-container>
    </ng-container>
  </div>
  <div class="col-sm-12">
    <h4 class="section-heading">{{'UBZ.SITE_CONTENT.1000110111' | translate }}</h4>
    <div *ngIf="!(appraisalDetail.expertName || appraisalDetail.expertSurname || appraisalDetail.reviserName || appraisalDetail.reviserSurname)" class="Search__NoResults">
      <div class="Search__NoResults__Icon">
        <i class="icon-appuntamento"></i>
      </div>
      <div class="Search__NoResults__Text">
        <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011001' | translate }}</p>
      </div>
    </div>
    <table class="uc-table uc-table-clean">
      <tbody>
        <tr *ngIf="appraisalDetail.expertName || appraisalDetail.expertSurname">
          <td data-label=""><strong>{{'UBZ.SITE_CONTENT.101110101' | translate }}</strong> {{appraisalDetail.expertName}} {{appraisalDetail.expertSurname}}</td>
          <td data-label="">
            <ng-container *ngIf="appraisalDetail.taskAssignmentDate">
              <span>{{'UBZ.SITE_CONTENT.1000111001' | translate }} {{ appraisalDetail.taskAssignmentDate | date:'dd/MM/yyyy' }}</span> <span>{{'UBZ.SITE_CONTENT.1000111000' | translate }} {{ appraisalDetail.taskAssignmentDate | customTime}}</span>
            </ng-container>
          </td>
        </tr>
        <tr *ngIf="appraisalDetail.reviserName || appraisalDetail.reviserSurname">
          <td data-label=""><strong>{{'UBZ.SITE_CONTENT.101110110' | translate }}</strong> {{appraisalDetail.reviserName}} {{appraisalDetail.reviserSurname}}</td>
          <td data-label="">
            <ng-container *ngIf="appraisalDetail.taskAssignmentDate">
              <span>{{'UBZ.SITE_CONTENT.1000111001' | translate }} {{ appraisalDetail.taskAssignmentDate | date:'dd/MM/yyyy' }}</span> <span>{{'UBZ.SITE_CONTENT.1000111000' | translate }} {{ appraisalDetail.taskAssignmentDate | customTime}}</span>
            </ng-container>
          </td>
        </tr>
      </tbody>
    </table>
    <h4 class="section-heading">{{'UBZ.SITE_CONTENT.1000111010' | translate }}</h4>
    <div *ngIf="!(appraisalDetail.customerContactDate || appraisalDetail.expectedSurveyDate)" class="Search__NoResults">
      <div class="Search__NoResults__Icon">
        <i class="icon-incarico"></i>
      </div>
      <div class="Search__NoResults__Text">
        <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011011' | translate }}</p>
      </div>
    </div>
    <table class="uc-table uc-table-clean">
      <tbody>
        <tr *ngIf="appraisalDetail.customerContactDate">
          <td data-label=""><strong>{{'UBZ.SITE_CONTENT.1000111011' | translate }}</strong> {{ appraisalDetail.customerContactDate| date:'dd/MM/yyyy' }}, {{'UBZ.SITE_CONTENT.11000' | translate }} {{ appraisalDetail.customerContactDate | customTime}}</td>
          <td data-label="">
            <ng-container *ngIf="appraisalDetail.customerContactDateInsert">
              <span>{{'UBZ.SITE_CONTENT.1000111001' | translate }} {{ appraisalDetail.customerContactDateInsert | date:'dd/MM/yyyy' }}</span> <span>{{'UBZ.SITE_CONTENT.1000111000' | translate }} {{ appraisalDetail.customerContactDateInsert | customTime }}</span>
            </ng-container>
          </td>
        </tr>
        <tr *ngIf="appraisalDetail.expectedSurveyDate">
          <td data-label=""><strong>{{'UBZ.SITE_CONTENT.1000111100' | translate }}</strong> {{ appraisalDetail.expectedSurveyDate| date:'dd/MM/yyyy' }}, {{'UBZ.SITE_CONTENT.11000' | translate }} {{ appraisalDetail.expectedSurveyDate | customTime}}</td>
          <td data-label="">
            <ng-container *ngIf="appraisalDetail.expectedSurveyDateInsert">
              <span>{{'UBZ.SITE_CONTENT.1000111001' | translate }} {{ appraisalDetail.expectedSurveyDateInsert | date:'dd/MM/yyyy' }}</span> <span>{{'UBZ.SITE_CONTENT.1000111000' | translate }} {{ appraisalDetail.expectedSurveyDateInsert | customTime }}</span>
            </ng-container>
          </td>
        </tr>
      </tbody>
    </table>
    <h4 class="section-heading">{{'UBZ.SITE_CONTENT.1000111101' | translate }}</h4>
    <div *ngIf="!(appraisalDetail.surveyDate || appraisalDetail.surveyOutcome)" class="Search__NoResults">
      <div class="Search__NoResults__Icon">
        <i class="icon-sopralluogo"></i>
      </div>
      <div class="Search__NoResults__Text">
        <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011100' | translate }}</p>
      </div>
    </div>
    <table class="uc-table uc-table-clean" *ngIf="appraisalDetail.surveyDate">
      <tbody>
        <tr *ngIf="appraisalDetail.surveyDate || appraisalDetail.surveyOutcome">
          <td data-label=""><strong>{{'UBZ.SITE_CONTENT.1000111110' | translate }}</strong> {{ appraisalDetail.surveyDate | date:'dd/MM/yyyy' }}</td>
          <td data-label="">
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<app-upload-xml *ngIf="isModalXmlOpen" [isOpen]="isModalXmlOpen" [positionId]="positionId" (closeModal)="closeXmlModal()"></app-upload-xml>
