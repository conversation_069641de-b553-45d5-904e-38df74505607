import {
  Compo<PERSON>,
  OnIni<PERSON>,
  <PERSON>Child,
  Inject,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>
} from '@angular/core';
import { ActivatedRoute, Event, Params } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';

// Components
import { AccordionApplicationFormComponent } from '../../../shared/application-form/components/accordion-application-form/accordion-application-form.component';
import { AppraisalTemplateValidationComponent } from '../appraisal-template-validation/appraisal-template-validation.component';
import { JobAssignmentComponent } from './job-assignment/job-assignment.component';

// Services
import { MenuService } from '../../../shared/menu/services/menu.service';
import { AppraisalCompilationService } from '../../service/appraisal-compilation.service';
import { LandingService } from '../../../simulation/services/landing/landing.service';
import { PositionService } from '../../../shared/position/position.service';
import { GenericTaskService } from '../../../tasks/services/generic-task/generic-task.service';
import { PhysicalRiskComponent } from './physical-risk/physical-risk.component';
import { PhysicalRiskService } from '../../service/physical-risk.service';


@Component({
  selector: 'app-generic-data',
  templateUrl: './generic-data.component.html',
  styleUrls: ['./generic-data.component.css'],
  providers: [MenuService, AppraisalCompilationService]
})
export class GenericDataComponent
  implements OnInit, AfterViewChecked, OnDestroy {
  positionId: string;
  wizardCode: string;
  bpmTaskId: string;
  bpmTaskCod: string;
  currentTask = 'UBZ-PER-DGE';
  appraisalType: string;
  @ViewChild(AccordionApplicationFormComponent)
  accordionApf: AccordionApplicationFormComponent;
  @ViewChild(AppraisalTemplateValidationComponent)
  templateValidationComponent: AppraisalTemplateValidationComponent;
  @ViewChild(JobAssignmentComponent)
  jobAssignmentComponent: JobAssignmentComponent;
  @ViewChild(PhysicalRiskComponent) phisicalRisk: PhysicalRiskComponent
  industrialGood: boolean;
  isShipping: boolean;
  isAereomobile: boolean;
  saveIsEnabled = false;
  isTemplateLight: boolean;
  isTemplateUpdate: boolean;
  appraisalInfo: any;
  private _subscription;
  public jobAssignmentData: any = {};
  private jobAssignmentValid: boolean;
  public experts: any;
  saveDraftCallback = this.saveDraft.bind(this);
  receiveData: { [key: string]: string } = {}
  physicalTrafficLights: any
  appraisalId: string;

  constructor(
    public menuService: MenuService,
    public _activatedRoute: ActivatedRoute,
    private _appraisalCompilationService: AppraisalCompilationService,
    public _landingService: LandingService,
    public _positionService: PositionService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants,
    private _genericTaskService: GenericTaskService,
    private service: PhysicalRiskService
  ) {

  }

  ngOnInit() {

    this._subscription = this._activatedRoute.parent.params
      .switchMap((params: Params) => {
        this.positionId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        this.bpmTaskId = params['taskId'];
        this.bpmTaskCod = params['taskCod'];
        this.appraisalId = params['positionId'];
        this._landingService.checkIfCompletedTask(
          this.positionId,
          this.currentTask
        );
        return Observable.forkJoin(
          this._positionService.getAppraisalInfo(this.positionId),
          this._positionService.getAppraisalAssignment(this.positionId)
        );
      })
      .subscribe(res => {
        this.setAppraisalInfo(res[0]);
        this.jobAssignmentData = res[1];
      });

  }

  /**
   * @name setAppraisalInfo
   * @description Metodo invocato sul recupero dell'oggetto perizia, imposta le variabili ad esso connesse
   * @param appraisalInfo Oggetto perizia recuperato
   */
  setAppraisalInfo(appraisalInfo) {
    this.appraisalInfo = appraisalInfo;
    this._landingService.posSegment = appraisalInfo.appraisal.posSegment;
    this.industrialGood =
      appraisalInfo.appraisal.loanScope === 'IND' ? true : false;
    if (appraisalInfo.appraisal.resItemType === 'MOB') {
      if (
        this._appraisalCompilationService.getMobileType(
          appraisalInfo.appraisalObject
        ) === this.constants.appraisalTypes.AEREOMOBILE
      ) {
        this.isShipping = false;
        this.isAereomobile = true;
      } else {
        this.isShipping = true;
        this.isAereomobile = false;
      }
    }
    this.appraisalType = appraisalInfo.appraisal.appraisalType;
    this.isTemplateLight = appraisalInfo.isTemplateLight;
    // Check if is template update (template di aggiornamento perizia)
    this.isTemplateUpdate = appraisalInfo.templateType && appraisalInfo.templateType === 'AGL';
  }

  ngAfterViewChecked() {
    const tmp: boolean =
      this.accordionApf.isAllValid() &&
      (!this.templateValidationComponent ||
        this.templateValidationComponent.form.valid) &&
      (!this.jobAssignmentComponent || this.jobAssignmentValid) &&
      (!this.phisicalRisk || this.physicalTrafficLights)
    if (this.saveIsEnabled !== tmp) {
      setTimeout(() => {
        this.saveIsEnabled = tmp;

      }, 0);
    }
 }

  ngOnDestroy() {
    this._subscription.unsubscribe();
  }

  saveData() {
    this.diversificatedSave()
      .switchMap(() => {
        return this.saveAdditionalData();
      })
      .subscribe(() => {
        this._landingService.goNextPage(
          this.positionId,
          this.currentTask,
          this.wizardCode,
          this._activatedRoute
        );
      });
  }

  saveDraft(): Observable<any> {
    const apfMap = {};
    apfMap[1] = JSON.stringify(this.accordionApf.model);

    return this._landingService
      .saveDraft(
        this.positionId,
        JSON.stringify({}),
        apfMap,
        'INFO_DATI_GENERALI'
      )
      .switchMap(() => {
        return this.saveAdditionalData();
      });

  }

  saveAdditionalData() {
    if (this.templateValidationComponent || this.jobAssignmentComponent || this.experts || this.phisicalRisk) {
      //create array of save services we have to call
      let saveServices: Observable<any>[] = new Array;
      if (this.templateValidationComponent) {
        saveServices.push(this._appraisalCompilationService.saveTemplateValidation(
          this.templateValidationComponent.appValidation
        ));
      }
      if (this.jobAssignmentComponent) {
        saveServices.push(this.saveExpertsData());
      }
      //if internal second opinion do not save billing data

      if (this.phisicalRisk) {
        saveServices.push(this.service.updateTableData(this.appraisalId, this.receiveData));
      }
      if (
        !(this.appraisalInfo['opinionType'] === 'SO' && this.appraisalInfo['internalAgent'])
        && this.experts
      ) {
        saveServices.push(this._genericTaskService.saveBillingData(
          this.experts.positionId,
          this.experts.billingAmount,
          this.experts.billingDate,
          this.experts.freeAppraisalCheck,
          this.experts.billingAmountSecond,
          this.experts.billingDateSecond,
          this.experts.secondfreeAppraisalCheck
        ));
     
      }
      //if no save is requested, return true
      if (saveServices.length === 0) return Observable.of(true);
      return Observable.forkJoin(saveServices);
    } else {
      return Observable.of(true);
    }
  }

  saveExpertsData(): Observable<any> {
    if (this.experts && this.experts.expertSocId && this.experts.positionId) {
      return this._genericTaskService.newAssigneExpert(
        this.experts.positionId,
        this.experts.expertSocId,
        this.experts.expertId,
        this.experts.reviserId,
        this.experts.spExpertId,
        this.experts.spRevisionId,
        this.experts.spExpertDesc,
        this.experts.spRevisionDesc
      );
    } else return Observable.of(true);
  }

  saveExperts(event) {
    this.experts = event;
  }

  // Eseguiti controlli per stabilire se (isShipping | isAeromobile), industrialGood, oppure NDG (corporate o individual)
  // In base alla scelta viene invocato il corretto servizio di salvataggio
  diversificatedSave(): Observable<any> {
    if (this.isShipping || this.isAereomobile) {
      return Observable.of(true);
    }
    const apfs = this.accordionApf.getFormsWithModel();
    if (this.industrialGood) {
      return this._appraisalCompilationService.saveIndustrialGenericData(
        this.positionId,
        { page: 'INFO_DATI_GENERALI', apfmap: apfs },
        []
      );
    }
    return this._appraisalCompilationService.saveGenericData(
      this.positionId,
      apfs,
      this._landingService.posSegment
    );
  }

  goToGenericTask() {
    this._landingService.goToGenericTask(
      this.positionId,
      this.bpmTaskId,
      this.bpmTaskCod
    );
  }

  chooseExpert(chosen: any) {
    if (chosen.type === 'PER') {
      this.jobAssignmentData.expertId = chosen.id;
      this.jobAssignmentData.expertName = chosen.name;
      this.jobAssignmentData.expertSurname = chosen.surname;
    } else {
      this.jobAssignmentData.reviserId = chosen.id;
      this.jobAssignmentData.reviserName = chosen.name;
      this.jobAssignmentData.reviserSurname = chosen.surname;
    }
  }

  jobAssignmentFormStatusChanges(sectionValid: boolean): void {
    this.jobAssignmentValid = sectionValid;
  }

  passData(d: any) {
    this.receiveData = d
  }

  checkTrafficLights(d: any) {
    this.physicalTrafficLights = d

  }
}
