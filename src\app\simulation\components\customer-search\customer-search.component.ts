import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';

import { CustomerService } from '../../services/customer/customer.service';
import { DomainService } from '../../../shared/domain/domain.service';
import { Domain } from '../../../shared/domain/domain';
import { MenuService } from '../../../shared/menu/services/menu.service';
import { GuaranteeFractionationService } from '../../../guarantee-fractionation/services/guarantee-fractionation.service';

@Component({
  selector: 'app-customer-search',
  templateUrl: './customer-search.component.html',
  styleUrls: ['./customer-search.component.css']
})
export class CustomerSearchComponent implements OnInit {
  isProspect: boolean;
  ndg: string;
  familyAsset = '';
  isFamilyAssetBlocked = false;
  singleFamilyChosen = false;
  requestType = 'PER';
  accessPoint= 'RP';
  public wizardCode;
  private positionId;
  public domains: Domain[] = [];
  ndgList = [];
  FamNdgList: any;

  constructor(
    private customerService: CustomerService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private domainService: DomainService,
    public guaranteeFractionationService: GuaranteeFractionationService,
    private menuService: MenuService
  ) {}

  ngOnInit() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.wizardCode = params['wizardCode'];
      this.positionId = params['positionId'];
      if (params['requestType']) {
        this.requestType = params['requestType'];
        if (this.requestType === 'CTE') {
          // gestione richiesta tecnica
          this.accessPoint = 'PT';
        }
      }
      const familyAssetFromUrl = params['famAsset'];
      if (this.wizardCode === 'WRPE') {
        this.isProspect = true;
      }
      if (familyAssetFromUrl !== '-') {
        this.isFamilyAssetBlocked = true;
        this.familyAsset = familyAssetFromUrl;
      }
    });
    this.domainService.newGetDomain('UBZ_DOM_RESITEM_TYPE').subscribe(x => {
      this.domains = x;
    });
    if (this.requestType && this.requestType === 'FRG') {
      this.customerService.getNdgList().subscribe(data => {
        this.FamNdgList = data;
        for (const item of this.FamNdgList) {
          let ndgLabel = item['ndg'];
          if (item['inChargeUser']) {
            ndgLabel += ' - In lavorazione da utente ' + item['inChargeUser'];
          }
          this.ndgList.push({ value: item['ndg'], label: ndgLabel });
        }
      });
    }
  }

  getFamilyAsset(ndg: string) {
    this.singleFamilyChosen = false;
    for (const item of this.FamNdgList) {
      if (item['ndg'] === ndg) {
        if (item['assetType'].length === 1) {
          this.familyAsset = item.assetType[0];
          this.singleFamilyChosen = true;
        }
      }
    }
  }

  getCustomerInfo() {
    if (this.wizardCode === 'WRPE' && this.isFamilyAssetBlocked) {
      this.customerService
        .startAppraisalRequest(this.positionId, this.ndg)
        .subscribe(x => {
          this.menuService.setMenuStatus(0, 2);
          if (x.esito) {
            this.router.navigate([
              `lading/WRPE/${this.positionId}/customer-info`
            ]);
          } else {
            this.router.navigate([
              `customer-search/WRPE/${this.positionId}/${x.familyAsset}`
            ]);
          }
        });
    } else if (this.wizardCode === 'WRPE') {
      // Se siamo in richiesta di frazionamento
      if (this.requestType === 'FRG') {
        let ndgFound = false;
        this.FamNdgList.forEach(elem => {
          if (elem['ndg'] === this.ndg) {
            this.guaranteeFractionationService.lockingUser =
              elem['inChargeUser'];
            ndgFound = true;
          }
        });
        if (!ndgFound) {
          this.guaranteeFractionationService.lockingUser = '';
        }
        this.guaranteeFractionationService.ndg = this.ndg;
        this.guaranteeFractionationService.familyAsset = this.familyAsset;
        this.guaranteeFractionationService.setCurrentStep('');
        this.guaranteeFractionationService.getFraWizardData().subscribe(() => {
          if (this.guaranteeFractionationService.wizardStepActive === 0
          || this.guaranteeFractionationService.wizardStepActive === 4) {
            const targetUrl = `guaranteeFractionation/${this.familyAsset}/${this.ndg}/summary`;
            this.router.navigate([targetUrl]);
            return;
          }        
          this.guaranteeFractionationService.goToNextStep();
        });
        return;
      }
      // Controllo sul tipo di richiesta (se perizia o surroga)
      this.customerService
        .newAppraisalRequest(this.familyAsset, this.ndg, this.requestType, this.accessPoint)
        .subscribe(data =>
          this.router.navigate([
            `wizard/WRPE/${data.simulationId}/customer-info`
          ])
        );
    } else {
      this.customerService
        .searchNdgAndCreateSimulation(
          this.isProspect,
          this.ndg,
          this.familyAsset
        )
        .subscribe(data =>
          this.router.navigate([
            `wizard/WSIM/${data.idSimulation}/customer-info`
          ])
        );
    }
  }
}
