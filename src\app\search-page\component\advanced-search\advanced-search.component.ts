import { Component, OnInit, Inject } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { DOCUMENT } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import * as FileSaver from 'file-saver';

import { SearchPageService } from '../../service/search-page.service';
import { DomainService } from '../../../shared/domain/domain.service';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';
import { SearchFieldsMap } from '../../../shared/menu/model/search-fields-map';
import { PositionService } from '../../../shared/position/position.service';

@Component({
  selector: 'app-advanced-search',
  templateUrl: './advanced-search.component.html',
  styleUrls: ['./advanced-search.component.css']
})
export class AdvancedSearchComponent implements OnInit {
  searchType = '';
  statusPhase = {};
  categoryTypeDomain = {};
  resItemTypeDomain = {};
  macroProcessDomain = {};
  appraisalTypeDomain = {};
  scopeTypeDomain = {};
  pageSize = 10;
  pageNum = 1;
  filters = [];
  newSearchFieldsMap = {};
  collateralModalIsOpen = false;
  collateralsToView: any[] = [];

  constructor(
    public searchPageService: SearchPageService,
    public domainService: DomainService,
    private router: Router,
    private translateService: TranslateService,
    private positionService: PositionService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants,
    @Inject(DOCUMENT) private document: any
  ) { }

  ngOnInit() {
    if (this.searchPageService && this.searchPageService.advancedSearch) {
      if (!Object.keys(this.searchPageService.advancedSearch).length) {
        this.router.navigate(['/index']);
        return;
      }
      this.searchType = this.searchPageService.advancedSearch['searchType'];
      this.pageSize = this.searchPageService.advancedSearch['pageSize'];
      this.pageNum = this.searchPageService.advancedSearch['pageNum'];
      Observable.forkJoin(
        this.domainService.getStatusName(),
        this.domainService.newGetDomain('UBZ_DOM_CATEGORY_TYPE','-',true),
        this.domainService.newGetDomain('UBZ_DOM_RESITEM_TYPE'),
        this.domainService.newGetDomain('UBZ_DOM_MACRO_PROCESS'),
        this.domainService.newGetDomain('UBZ_DOM_APPRAISAL_TYPE'),
        this.domainService.newGetDomain('UBZ_DOM_SCOPE_TYPE')
      ).subscribe(res => {
        this.statusPhase = res[0];
        this.categoryTypeDomain = res[1];
        this.resItemTypeDomain = res[2];
        this.macroProcessDomain = res[3];
        this.appraisalTypeDomain = res[4];
        this.scopeTypeDomain = res[5];
      });
      this.calculateFiltersStructure();
      this.setFiltersToDisplay();
    }
  }

  changePageSize() {
    this.pageNum = 1;
    this.searchPageService
      .doNewAdvancedSearch(this.pageNum, Number(this.pageSize))
      .subscribe(x => {
        this.searchPageService.advancedSearch['searchResults'] = x.positions;
      });
  }

  changePage(event) {
    this.pageNum = event.page;
    this.searchPageService
      .doNewAdvancedSearch(this.pageNum, Number(this.pageSize))
      .subscribe(x => {
        this.searchPageService.advancedSearch['searchResults'] = x.positions;
      });
  }

  private setFiltersToDisplay() {
    if (this.searchPageService.advancedSearch['searchType']) {
      this.filters.push(
        this.translateService.instant(
          this.constants.searchTypes[
            this.searchPageService.advancedSearch['searchType']
          ]
        )
      );
    }
    for (const ind in this.searchPageService.advancedSearch['filters']) {
      if (true) {
        if (this.searchPageService.advancedSearch['filters'][ind] !== '') {
          if (this.newSearchFieldsMap[this.searchType][ind]) {
            this.domainService
              .newGetDomain(this.newSearchFieldsMap[this.searchType][ind])
              .subscribe(x => {
                this.filters.push(
                  this.translateService.instant(
                    x[this.searchPageService.advancedSearch['filters'][ind]]
                      .translationCod
                  )
                );
              });
          } else {
            this.filters.push(
              this.searchPageService.advancedSearch['filters'][ind]
            );
          }
        }
      }
    }
  }

  private calculateFiltersStructure() {
    const searchFieldsMap = new SearchFieldsMap();
    for (const ind1 in searchFieldsMap) {
      if (true) {
        this.newSearchFieldsMap[ind1] = {};
        for (const el of searchFieldsMap[ind1]) {
          if (el.domTableName) {
            this.newSearchFieldsMap[ind1][el.name] = el.domTableName;
          }
        }
      }
    }
  }

  goToDetailsPage(appraisalId) {
    this.searchPageService.goToDetailsPage(appraisalId, '/advancedSearch');
    // FIXME - TOGLIERE SE CENTRALIZZAZIONE METODO FUNZIONA CORRETTAMENTE
    // this.searchPageService.toggleBackSearchButton('/advancedSearch');
    // this.router.navigate([`/generic-task/${appraisalId}/-/-/`]);
    // this.menuService.changeHeadMenuStatus(0);
  }

  goToAppraisalRequest(requestId) {
    this.searchPageService.goToAppraisalRequest(requestId, '/advancedSearch');
    // FIXME - TOGLIERE SE CENTRALIZZAZIONE METODO FUNZIONA CORRETTAMENTE
    // this.searchPageService.toggleBackSearchButton('/advancedSearch');
    // this.router.navigate([`landing/WRPE/${requestId}`]);
    // this.menuService.changeHeadMenuStatus(0);
  }

  goToAssetPage(idAsset) {
    this.document.location.href = `/UAM-EFA-PF/UAM/#/detail/false/${idAsset}`;
  }

  goToWarrentPage(assetId) {
    // console.log('where redirect?');
  }

  exctractReport() {
    this.searchPageService
      .extractReport(this.pageNum, this.pageSize)
      .subscribe(file => {
        FileSaver.saveAs(file, 'document.xls');
      });
  }

  openCollateralModal(row) {
    if (this.searchType !== 'PE') {
      this.positionService.getCollateralData(row.id).subscribe(x => {
        row.collaterals = x;
        this.collateralsToView = row.collaterals;
        this.collateralModalIsOpen = true;
      });
    } else {
      // Se è una compilazione perizia entra qui
      this.positionService.getCollateralAssetList(row.appraisalId).subscribe(resp => {
        row.collaterals = resp;
        this.collateralsToView = row.collaterals;
        this.collateralModalIsOpen = true;
      });
    }
  }

  closeCollateralModal() {
    this.collateralModalIsOpen = false;
    this.collateralsToView = [];
  }

  getCollateralIds(row) {
    let retStr: string;
    if (row.collaterals) {
      if (this.searchType !== 'PE') {
        retStr = this.parseForSim(row);
      } else {
        retStr = this.parseForPer(row);
      }
    } else {
      retStr = '';
    }
    return retStr;
  }

  private parseForPer(row): string {
    let retStr = '';
    let charCounter = 0;
    for (const col of row.collaterals) {
      const elLength = col.jointCod.length;
      if (elLength + charCounter <= 7) {
        if (charCounter !== 0) {
          retStr += ',';
        }
        retStr += col.jointCod;
        charCounter += elLength;
      } else if (elLength <= 7) {
        const subIndex = 6 - charCounter;
        retStr += ',' + col.jointCod.substring(0, subIndex) + '...';
        break;
      }
    }
    return retStr;
  }

  private parseForSim(row): string {
    let retStr = '';
    let charCounter = 0;
    for (const col of row.collaterals) {
      const elLength = col.progCollateral.length;
      if (elLength + charCounter <= 7) {
        if (charCounter !== 0) {
          retStr += ',';
        }
        retStr += col.progCollateral;
        charCounter += elLength;
      } else if (elLength <= 7) {
        const subIndex = 6 - charCounter;
        retStr += ',' + col.progCollateral.substring(0, subIndex) + '...';
        break;
      }
    }
    return retStr;
  }
  
}
