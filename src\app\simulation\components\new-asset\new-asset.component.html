<div class="container-fluid">
  <div class="row step-navigation">
    <div class="col-sm-12 btn-set">
      <ng-container *appAuthKey="'UBZ_NAVIGATION.FOOTER_CANCEL'">
        <button type="button" class="btn btn-empty waves-effect waves-secondary pull-right" id="cancel-asset-creation" (click)="goToAssetList()"><i class="fa fa-times" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.100000' | translate }}</button>
      </ng-container>
    </div>
  </div>
  <form novalidate="">
    <section id="search-for-asset" class="form-variables" style="display: block;">
      <app-application-form [page]="page[wizardCode]" [drivers]="drivers" [setReadOnly]="false" [positionId]="simulationId" (formSubmit)="createAsset()">
        <div class="row">
          <div class="col-sm-12 btn-set">
            <ng-container *appAuthKey="'UBZ_NAVIGATION.FOOTER_CANCEL'">
              <button type="submit" id="clone" class="btn btn-primary waves-effect pull-right" [disabled]="applicationForm.appForm.invalid">{{'UBZ.SITE_CONTENT.1011100' | translate }}</button>
            </ng-container>
          </div>
        </div>
      </app-application-form>
    </section>
  </form>
</div>
