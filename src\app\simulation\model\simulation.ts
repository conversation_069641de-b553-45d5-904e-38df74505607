export class Simulation {
  positionId: string;
  positionType: string;
  macroProcess: string;
  appraisalStruct: string;
  appraisalScope: string;
  collatTecForm: string;
  flagPeriziaTerzi: boolean;
  operationType: string;
  flagTipoPoolCapofila: boolean;
  appraisalType: string;
  credlineAmount: number;
  assetType: string;
  category: string;
  prospectCod: string;
  ndg: string;
  heading: string;
  corporate: boolean;
  surveyOutcome: number;
  originationProcess: string;
  note: string;
  ricSalFram: boolean;
  fromaSim: boolean;
  expertName: string;
  flagForcingOwner?: string;
  forcingOwner?: string;
  forcingNote?: string;
  accessPoint?: string;
}
