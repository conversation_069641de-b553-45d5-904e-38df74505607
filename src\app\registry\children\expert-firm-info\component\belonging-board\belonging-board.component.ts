import {
  Component,
  OnChanges,
  Input,
  <PERSON><PERSON><PERSON>roy,
  SimpleChanges
} from '@angular/core';
import { RegistryService } from '../../../../service/registry.service';
import { Subscription } from 'rxjs/Subscription';
import { ExpertFirm } from '../../../../model/registry.models';

@Component({
  selector: 'app-belonging-board',
  templateUrl: './belonging-board.component.html',
  styleUrls: ['./belonging-board.component.css']
})
export class BelongingBoardComponent implements OnChanges, OnDestroy {
  public modifyMode = false;
  @Input() public anagId: string;
  @Input() public subjectType: string;
  public belongingBoardPresent: boolean;
  public registerData: any = {};
  private _subscriptions: Subscription[] = [];

  constructor(private _registryService: RegistryService) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes && this.anagId && this.subjectType) {
      this.refreshRegisterData();
    }
  }

  ngOnDestroy() {
    for (const subscription of this._subscriptions) {
      if (subscription && !subscription.closed) {
        subscription.unsubscribe();
      }
    }
  }

  private refreshRegisterData() {
    this._subscriptions[0] = this._registryService
      .getRegisterData(this.anagId, this.subjectType)
      .subscribe(res => {
        this.registerData = res;
        this.registerData.registrationData =
          this.registerData.registrationData !== 0
            ? new Date(this.registerData.registrationData)
            : null;
        this.registerData.registerBoardDate =
          this.registerData.registerBoardDate !== 0
            ? new Date(this.registerData.registerBoardDate)
            : null;
        this.checkIfRegisterExists();
      });
  }

  private checkIfRegisterExists(): void {
    if (
      this.registerData.numberRegistration ||
      this.registerData.registerType ||
      this.registerData.registerBoard
    ) {
      this.belongingBoardPresent = true;
    }
  }

  public enableModify(event: any): void {
    this.stopPropagation(event);
    this.modifyMode = true;
  }

  public startEdit(event: any) {
    this.stopPropagation(event);
    this.refreshRegisterData();
    this.modifyMode = true;
  }

  public abortAdding(event: any): void {
    this.stopPropagation(event);
    this.modifyMode = false;
  }

  public saveAddedData(event: any): void {
    this.stopPropagation(event);
    const toSave = JSON.parse(JSON.stringify(this.registerData));
    toSave.registerBoardDate = Date.parse(toSave.registerBoardDate);
    toSave.registrationData = Date.parse(toSave.registrationData);
    this._subscriptions[1] = this._registryService
      .saveRegisterData(this.anagId, toSave, this.subjectType)
      .subscribe(res => {
        this.refreshRegisterData();
        this.modifyMode = false;
      });
  }

  private stopPropagation(event: any): void {
    event.stopPropagation();
  }
}
