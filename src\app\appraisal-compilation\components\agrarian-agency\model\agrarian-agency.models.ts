export class SalaryCalculationItem {
  expenseId: number;
  expenseType: string;
  farmId: string;
  lossAmount = 0;
}

export class SalaryCalculationModel {
  IEC: SalaryCalculationItem = new SalaryCalculationItem();
  ICS: SalaryCalculationItem = new SalaryCalculationItem();
  AMM: SalaryCalculationItem = new SalaryCalculationItem();
  ASA: SalaryCalculationItem = new SalaryCalculationItem();
  QDA: SalaryCalculationItem = new SalaryCalculationItem();
  SAR: SalaryCalculationItem = new SalaryCalculationItem();
  STI: SalaryCalculationItem = new SalaryCalculationItem();
  MDA: SalaryCalculationItem = new SalaryCalculationItem();
  ALE: SalaryCalculationItem = new SalaryCalculationItem(); // Totale
  documentations: string;
}

export class GrossProductionItem {
  farmId: string;
  haSurface: string;
  incomeId: string;
  incomeType: string;
  measurement: string;
  productType: string;
  totPrice = 0;
  totProduction: number;
  unitPrice: number;
  unitProduction: number;
}

export class GrossProductionModel {
  ULS: GrossProductionItem = new GrossProductionItem(); // Utile lordo stalla
  RGA: GrossProductionItem = new GrossProductionItem(); // ricavi da agriturismo
  PAC: GrossProductionItem = new GrossProductionItem(); // AGEA
}

export class AgrarianAgencyModel {
  appFarmNoIncomePlv: GrossProductionItem[] = [
    new GrossProductionItem(),
    new GrossProductionItem(),
    new GrossProductionItem()
  ];
  appFarmLoss: SalaryCalculationModel;
  appFarmIncomePlv: GrossProductionItem;
  docNotes: string;
}
