import { Injectable, Inject } from '@angular/core';
import {
  Http,
  Response,
  ResponseContentType
} from '@angular/http';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';
import { ApiService } from '../../api/api.service';

@Injectable()
export class ChecklistService {
  base_checklist_url = 'CHK-ESA-RS/service/checklist/v1';
  base_checklist_url_v2 = 'CHK-ESA-RS/service/checklist/v2';
  base_chk_wrapped_url = '/UBZ-ESA-RS/service/chkService/v1';
  base_chk_wrapped_url_v2 = '/UBZ-ESA-RS/service/chkService/v2';
  base_ubz_url = '/UBZ-ESA-RS/service/checklist/v1';
  responseData: any;

  constructor(
    private http: Http,
    private apiService: ApiService
  ) { }

  newUploadDocument(file: any, postData: any) {
    return this.apiService.apiUrl.switchMap(umfUrl => {
      // const url = `${umfUrl}${this.base_checklist_url}/upload`;
      const url = `${this.base_chk_wrapped_url}/upload`;
      const formData: FormData = new FormData();
      formData.append('documentStorageRequest', JSON.stringify(postData));
      formData.append('file', file, file.name);
      return this.http.post(url, formData).map((resp: Response) => resp.json());
    });
  }

  requireDocument(body: any) {
    return this.apiService.apiUrl.switchMap(umfUrl => {
      const url = `${umfUrl}${this.base_checklist_url}/newDocument`;
      return this.http.put(url, body).map((resp: Response) => resp.text());
    });
  }

  public postponeUpload(itemId: string, phaseStatus: string) {
    const phase = encodeURIComponent(phaseStatus.substring(0, 3));
    const status = encodeURIComponent(phaseStatus.substring(3));
    const url = `${this.base_ubz_url}/defer/${itemId}/${phase}-${status}`;
    return this.http.get(url).map((resp: Response) => resp.text());
  }

  public newGetChecklist(positionId: string) {
    positionId = encodeURIComponent(positionId);
    const url = `${this.base_ubz_url}/checklist/${positionId}`;
    return this.http.get(url).map((resp: Response) => resp.json());
  }

  public newAcquiredCheck(positionId: string) {
    positionId = encodeURIComponent(positionId);
    const url = `${this.base_ubz_url}/acquiredCheck/${positionId}`;
    // const url = '/assets/data/fake-checklist-services/fake-acquiredCheck2.json';
    return this.http.get(url).map((resp: Response) => resp.json());
  }

  public getVersions(itemId: string) {
    return this.apiService.apiUrl.switchMap(umfUrl => {
      itemId = encodeURIComponent(itemId);
      // const url = `${umfUrl}${this.base_checklist_url}/versions/${itemId}`;
      const url = `${this.base_chk_wrapped_url}/versions/${itemId}`;
      return this.http.get(url).map((resp: Response) => resp.json());
    });
  }

  public deleteDocument(appraisalId: string, requestId: string, itemId: string, versionId: string) {
    const input = {
      appraisalId: appraisalId,
      requestId: requestId,
      itemId: itemId,
      version: versionId
    };
    const url = `${this.base_chk_wrapped_url}/delete`;
    return this.http.put(url, input).map((resp: Response) => resp.text());
  }

  public downloadDocument(
    appraisalId: string,
    requestId: string,
    itemId: string,
    versionId: string
  ) {
    // return this.apiService.apiUrl.switchMap(umfUrl => {
    // const url = `${umfUrl}${this.base_checklist_url_v2}/download`;
    const url = `${this.base_chk_wrapped_url_v2}/download`;
    const input = {
      appraisalId: appraisalId,
      requestId: requestId,
      itemId: itemId,
      version: versionId
    };
    return this.http
      .put(url, input, { responseType: ResponseContentType.Blob })
      .map((resp: Response) => {
        return { header: resp.headers, document: resp.blob() };
      });
    // });
  }

  public displayDocument(
    appraisalId: string,
    requestId: string,
    itemId: string,
    versionId: string
  ) {
    const url = `${this.base_chk_wrapped_url_v2}/download`;
    const input = {
      appraisalId: appraisalId,
      requestId: requestId,
      itemId: itemId,
      version: versionId
    };
    return this.http
      .put(url, input, { responseType: ResponseContentType.ArrayBuffer })
      .map((resp: Response) => {
        return { header: resp.headers, document: resp.arrayBuffer() };
      });
  }

  public newInvalidateDocument(itemId: string, reasonOfInvalidation: string) {
    // return this.apiService.apiUrl.switchMap(umfUrl => {
    itemId = encodeURIComponent(itemId);
    reasonOfInvalidation = encodeURIComponent(reasonOfInvalidation);
    // const url = `${umfUrl}${this
    //   .base_checklist_url}/invalidate/${itemId}/${reasonOfInvalidation}`;
    const url = `${this
      .base_chk_wrapped_url}/invalidate/${itemId}/${reasonOfInvalidation}`;
    return this.http.put(url, {}).map((resp: Response) => resp.text());
    // });
  }

  public print(id: string, docType: string) {
    return this.apiService.apiUrl.switchMap(umfUrl => {
      const url = `/UBZ-ESA-RS/service/print/v1/prints/${id}/${docType}/downloadJasPdf`;
      return this.http
        .get(url, { responseType: ResponseContentType.Blob })
        .map((resp: Response) => {
          return resp.blob();
        });
    });
  }

  public GetChecklistServicing(positionId: string) {
    positionId = encodeURIComponent(positionId);
    const url = `${this.base_ubz_url}/checklist/servicing/${positionId}`;
    return this.http.get(url).map((resp: Response) => resp.json());
  }
}
