<section id="page-content-wrapper">
<ng-container *appAuthKey="'UBZ_EXTERNAL_MORTAGE_ACCESS'">

  <div class="row">
    <div class="col-sm-12 section-headline">
      <h1 class="antergate-title"><i class="icon-richiesta_perizia"></i>{{'UBZ.SITE_CONTENT.10011010011' | translate }}</h1>
      <h2>{{'UBZ.SITE_CONTENT.10100011' | translate }} {{requestId}}</h2>
    </div>
  </div>

  <fieldset *ngIf="requestData?.collaterals.length" [disabled]="requestData?.isDraft === false">
    <accordion class="panel-group" id="accordion">
      <div *ngFor="let collateral of requestData.collaterals; let collateralIndex=index">
        <form #f="ngForm">
          <accordion-group #group class="panel" isOpen="true">
            <div accordion-heading class="acc-note-headline" role="tab">
              <h4 class="panel-title">
                <a role="button">
                  <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
                  {{ collateral.prog }} {{ collateral.collatDesc }}
                  <span class="state" [ngClass]="{'green': isValid[collateralIndex], 'red': !isValid[collateralIndex]}"></span>
                </a>
              </h4>
            </div>
            <div class="panel-collapse collapse in" role="tabpanel">
              <div class="panel-body">
                <div class="anacredit-table__caption">
                  <span>{{'UBZ.SITE_CONTENT.10011101011' | translate }}</span>
                  <span class="importo">{{ collateral.antergateValue | currency:'EUR':true:'1.2-2' }}</span>
                </div>
                <table class="uc-table anacredit-table">
                  <thead>
                    <tr>
                      <!-- Id asset -->
                      <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.100100' | translate }}</th>
                      <!-- Tipo asset -->
                      <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.10010111010' | translate }}</th>
                      <!-- Categoria catastale -->
                      <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.10010011' | translate }}</th>
                      <!-- Stima esposizione Antergate -->
                      <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.10011001111' | translate }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let asset of collateral.assets; let rowIndex=index">
                      <td attr.data-label="{{'UBZ.SITE_CONTENT.100100' | translate }}" scope="col" class="col-sm-3">{{ asset.resItemId
                        }}</td>
                      <td attr.data-label="{{'UBZ.SITE_CONTENT.10010111010' | translate }}" scope="col" class="col-sm-3">
                        {{ (resItemTypeDomain && resItemTypeDomain[asset.familyAsset]) ? (resItemTypeDomain[asset.familyAsset].translationCod | translate)
                        : '' }}
                      </td>
                      <td attr.data-label="{{'UBZ.SITE_CONTENT.10010011' | translate }}" scope="col" class="col-sm-3">
                        {{ (categoryDomains && categoryDomains[asset.familyAsset] && categoryDomains[asset.familyAsset][asset.categoryAsset]) ? (categoryDomains[asset.familyAsset][asset.categoryAsset].translationCod
                        | translate) : '' }}
                      </td>
                      <td attr.data-label="{{'UBZ.SITE_CONTENT.10011001111' | translate }}" scope="col" class="col-sm-3">
                        <app-importo [name]="'antergate-number-' + rowIndex" [ngClassAdd]="'align-right'" [ngClassCondition]="true" [required]="false"
                          [(ngModel)]="asset.antergateSplittedValue" (ngModelChange)="checkIsValid()">
                        </app-importo>
                      </td>
                    </tr>
                  </tbody>
                  <tfoot class="table-foot">
                    <tr>
                      <td></td>
                      <td></td>
                      <td>
                        <strong>{{'UBZ.SITE_CONTENT.100000111' | translate }}:</strong>
                      </td>
                      <td class="text-right">
                        <strong>{{ calculatedValues[collateralIndex] | currency:'EUR':true:'1.2-2' }}</strong>
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          </accordion-group>
        </form>
      </div>
    </accordion>
  </fieldset>
</ng-container>
</section>

<app-navigation-footer [showPrevious]="true" [showSaveDraft]="requestData?.isDraft" [showCancelButton]="false" [showSaveButton]="requestData?.isDraft"
  [saveIsEnable]="isAllValid" [confirmButtonString]="'UBZ.SITE_CONTENT.110001110' | translate" [saveDraftCallback]="saveDraftCallback"
  (saveButtonClick)="save()" (previousButtonClick)="goToExternalMortagesList()" (closeDraftButtonClick)="goToExternalMortagesList()">
</app-navigation-footer>
