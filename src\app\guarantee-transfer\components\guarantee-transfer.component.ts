import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { GuaranteeTransferService } from '../services/guarantee-transfer.service';

@Component({
  selector: 'app-guarantee-transfer',
  templateUrl: './guarantee-transfer.component.html'
})
export class GuaranteeTransferComponent implements OnInit {

  constructor(
    private activatedRoute: ActivatedRoute,
    public guaranteeTransfer: GuaranteeTransferService
  ) { }

  ngOnInit() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.guaranteeTransfer.ndgOrigin = params['ndgOrigin'];
      this.guaranteeTransfer.ndgDestination = params['ndgDestination'];
      this.guaranteeTransfer.initializeStaticHeaderArray();
      // Recupera le informazioni del ndg di origine
      this.guaranteeTransfer.getNdgDetails(params['ndgOrigin']).subscribe(response => {
        if (response && response.heading) {
          this.guaranteeTransfer.staticHeaderArray
            .find(obj => obj['label'] === 'UBZ.SITE_CONTENT.10010111011')['value'] = this.guaranteeTransfer.ndgOrigin + ' ' + response.heading;
        }
      });
      // Se ndg di destinazione è stato specificato se ne recuperano le informazioni associate
      if (this.guaranteeTransfer.ndgDestination !== '-') {
        this.guaranteeTransfer.getNdgDetails(this.guaranteeTransfer.ndgDestination).subscribe(response => {
          if (response && response.heading) {
            this.guaranteeTransfer.staticHeaderArray
              .find(obj => obj['label'] === 'UBZ.SITE_CONTENT.10010111100')['value'] = this.guaranteeTransfer.ndgDestination + ' ' + response.heading;
          }
        });
      }
    });
  }
  
}
