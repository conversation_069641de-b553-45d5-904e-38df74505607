import { Component, OnInit, Input } from '@angular/core';
import {
  ConsistencyTableModel,
  SingleFieldModel
} from '../../model/evaluations.models';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';

@Component({
  selector: 'app-estimation-tab',
  templateUrl: './estimation-tab.component.html',
  styleUrls: ['./estimation-tab.component.css']
})
export class EstimationTabComponent implements OnInit {
  @Input() positionId: string;
  @Input() list: ConsistencyTableModel[] = [];
  @Input() fields: SingleFieldModel = new SingleFieldModel();
  @Input() consistencyTypes: any;
  @Input() opType: string;
  @Input() isTemplateUpdate = false;

  constructor(
    public _accordionAPFService: AccordionAPFService
  ) { }

  ngOnInit() { }

  getValUnitarioTot(): number {
    let valUnitarioTot = 0;
    for (const item of this.list) {
      valUnitarioTot += item.valUniCom;
    }
    return valUnitarioTot;
  }

  getValUnitarioCauzionaleTot(): number {
    let valUnitarioCauzionaleTot = 0;
    for (const item of this.list) {
      valUnitarioCauzionaleTot += item.valUniCau;
    }
    return valUnitarioCauzionaleTot;
  }

  /**
   * @name getData
   * @description Invocato dal componente padre per popolare oggetto da salvare
   */
  getData() {
    return this.list;
  }
}
