import { Component, OnInit } from '@angular/core';
import { RegistryService } from './service/registry.service';
import { SECTIONS } from './model/registry.models';
import { MenuService } from '../../app/shared/menu/services/menu.service';

@Component({
  selector: 'app-registry',
  templateUrl: './registry.component.html',
  styleUrls: ['./registry.component.css'],
  providers: [RegistryService]
})
export class RegistryComponent implements OnInit {
  public ACTIVABLE_SECTIONS = SECTIONS;
  public activeSection = this.ACTIVABLE_SECTIONS['SOC'];

  constructor(private menuService: MenuService) {}

  ngOnInit() {
    this.menuService.setInitHeadStatusByUrl('/registry');
    // console.log(this.activeSection);
  }
}
