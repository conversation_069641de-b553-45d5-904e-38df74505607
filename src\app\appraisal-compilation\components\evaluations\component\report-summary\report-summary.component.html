<form #f="ngForm" novalidate (click)="accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
          {{'UBZ.SITE_CONTENT.10010010110' | translate }}
          <span class="state" [ngClass]="{'green': f.valid, 'red': !f.valid}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <div class="row vertical-margin">
            <div class="col-sm-4">{{'UBZ.SITE_CONTENT.10010010111' | translate }}</div>
            <div class="col-sm-2">
              <div class="d-inline pull-right">
                <div class="custom-radio">
                  <input type="radio" name="cityPlanning" id="cityPlanningTrue" class="radio" value="Y" [(ngModel)]="reportSummaryObject['cityPlanning']"
                    required>
                  <label for="cityPlanningTrue" class="radio-label">{{'UBZ.SITE_CONTENT.100011011' | translate }}</label>
                  <input type="radio" name="cityPlanning" id="cityPlanningFalse" class="radio" value="N" [(ngModel)]="reportSummaryObject['cityPlanning']"
                    required>
                  <label for="cityPlanningFalse" class="radio-label">{{'UBZ.SITE_CONTENT.100011100' | translate }}</label>
                </div>
              </div>
            </div>

            <div class="col-sm-4">{{'UBZ.SITE_CONTENT.10010011000' | translate }}</div>
            <div class="col-sm-2">
              <div class="d-inline pull-right">
                <div class="custom-radio">
                  <input type="radio" name="cadastral" id="cadastralTrue" value="Y" [(ngModel)]="reportSummaryObject['cadastral']"
                    required>
                  <label for="cadastralTrue" class="radio-label">
                    {{'UBZ.SITE_CONTENT.100011011' | translate }}
                  </label>
                  <input type="radio" name="cadastral" id="cadastralFalse" value="N" [(ngModel)]="reportSummaryObject['cadastral']"
                    required>
                  <label for="cadastralFalse" class="radio-label">{{'UBZ.SITE_CONTENT.100011100' | translate }}</label>
                </div>
              </div>
            </div>
          </div>

          <div class="row vertical-margin">
            <div class="col-sm-4">{{'UBZ.SITE_CONTENT.10010011001' | translate}}</div>
            <div class="col-sm-2">
              <div class="d-inline pull-right">
                <div class="custom-radio">
                  <input type="radio" name="constraints" id="constraintsTrue" value="Y" [(ngModel)]="reportSummaryObject['constraints']"
                    required>
                  <label for="constraintsTrue" class="radio-label">{{'UBZ.SITE_CONTENT.100011011' | translate }}</label>
                  <input type="radio" name="constraints" id="constraintsFalse" value="N" [(ngModel)]="reportSummaryObject['constraints']"
                    required>
                  <label for="constraintsFalse" class="radio-label">{{'UBZ.SITE_CONTENT.100011100' | translate }}</label>
                </div>
              </div>
            </div>

            <div class="col-sm-4">{{'UBZ.SITE_CONTENT.10010011010' | translate}}</div>
            <div class="col-sm-2">
              <div class="d-inline pull-right">
                <div class="custom-radio">
                  <input type="radio" name="titol" id="titolTrue" value="Y" [(ngModel)]="reportSummaryObject['titol']"
                    required>
                  <label for="titolTrue" class="radio-label">{{'UBZ.SITE_CONTENT.100011011' | translate }}</label>
                  <input type="radio" name="titol" id="titolFalse" value="N" [(ngModel)]="reportSummaryObject['titol']"
                    required>
                  <label for="titolFalse" class="radio-label">{{'UBZ.SITE_CONTENT.100011100' | translate }}</label>
                </div>
              </div>
            </div>
          </div>

          <div class="row vertical-margin">
            <div class="col-sm-3">{{'UBZ.SITE_CONTENT.10010011011' | translate}}</div>
            <div class="col-sm-3">
              <div class="custom-select">
                <select class="form-control" [(ngModel)]="reportSummaryObject['congruity']" name="congruity" required>
                  <option value="" hidden>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let elem of (congruityDom | domainMapToDomainArray)" [value]="elem.domCode">{{elem.translationCod
                    | translate}}</option>
                </select>
              </div>
            </div>
          </div>

          <div class="row vertical-margin">
            <div class="col-sm-3">{{'UBZ.SITE_CONTENT.10010011100' | translate}}</div>
            <div class="col-sm-3">
              <div class="custom-select">
                <select class="form-control" [(ngModel)]="reportSummaryObject['estateStatus']" name="estate-status"
                  required>
                  <option value="" hidden>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let elem of (estateStatusDom | domainMapToDomainArray)" [value]="elem.domCode">{{elem.translationCod
                    | translate}}</option>
                </select>
              </div>
            </div>

            <div class="col-sm-3">{{'UBZ.SITE_CONTENT.10010011101' | translate}}</div>
            <div class="col-sm-3">
              <div class="custom-select">
                <select class="form-control" [(ngModel)]="reportSummaryObject['merchantability']" name="merchantability"
                  required>
                  <option value="" hidden>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let elem of (merchantabilityDom | domainMapToDomainArray)" [value]="elem.domCode">{{elem.translationCod
                    | translate}}</option>
                </select>
              </div>
            </div>
          </div>

          <div class="row vertical-margin">
            <div class="col-sm-3">{{ 'UBZ.SITE_CONTENT.10010011110' | translate }}</div>
            <div class="col-sm-2 ">
              <div class="custom-select">
                <select class="form-control" [(ngModel)]="reportSummaryObject['outcome']" name="outcome">
                  <option [ngValue]="null" hidden>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let elem of (estimateDom | domainMapToDomainArray)" [value]="elem.domCode">{{elem.translationCod
                    | translate}}</option>
                </select>
              </div>
            </div>
            <div class="col-sm-1" style="padding-top: 6px;">
              <span class="state text-center" [ngClass]="{'green': reportSummaryObject['outcome']==='001', 'red': reportSummaryObject['outcome']==='003', 'yellow': reportSummaryObject['outcome']==='002'}"></span>
            </div>
            <div class="col-sm-2">
              <label>
                <span>
                  <i class="icon-search note-tooltip" [tooltip]="reportSummaryObject['expertSummary']"  triggers="click"></i>
                </span>
                {{'UBZ.SITE_CONTENT.10010011111' | translate }}
              </label>
            </div>
            <div class="col-sm-4">
              <textarea class="form-control" name="considerazione" [(ngModel)]="reportSummaryObject['expertSummary']" required></textarea>
            </div>
          </div>

        </div>
      </div>
    </div>
  </accordion-group>
</form>
