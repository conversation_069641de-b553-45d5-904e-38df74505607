<div *ngIf="isOpen" class="modal fade" bsModal #modalBox="bs-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" (onHidden)="hide()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2 *ngIf="isPresaInCarico()">{{'UBZ.SITE_CONTENT.110010110' | translate }}</h2>
        <h2 *ngIf="isRevoca()">{{'UBZ.SITE_CONTENT.110010111' | translate }}</h2>
        <button type="reset" class="close pull-right" aria-label="Close" (click)="hide()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="row pratica-states pb-3" *ngIf="isRevoca()">
          <div class="col-sm-12">
            {{'UBZ.SITE_CONTENT.110011000' | translate }}
          </div>
        </div>
        <div class="row pratica-states pb-3" *ngIf="isPresaInCarico()">
          <div class="col-sm-6">
            <div class="pratica-states__iconbox iconbox--active float-left">
              <i class="fa fa-lock"></i>
            </div>
            <div class="pratica-states__main float-left">
              <span class="d-block pratica-states--active">{{'UBZ.SITE_CONTENT.1111100' | translate }}</span>
              <span class="d-block pratica-states--sub">{{ position.userInCharge ? position.userInCharge : '---' }}</span>
            </div>
          </div>
          <div class="col-sm-6 text-right">
            <div class="pratica-states_data">
              <span class="d-block pratica-states--active"><strong>{{ getStatusCode() | translate }}</strong></span>
              <span class="d-block pratica-states--sub"><strong>{{'UBZ.SITE_CONTENT.10111' | translate }}</strong> {{position.lastUpdate | date:'d/M/y' }} <strong>{{'UBZ.SITE_CONTENT.11000' | translate }}</strong> {{position.lastUpdate | date:'HH:mm:ss' }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary waves-effect" (click)="buttonPressed()">
          <span *ngIf="isPresaInCarico()">{{'UBZ.SITE_CONTENT.110011001' | translate }}</span>
          <span *ngIf="isRevoca()">{{'UBZ.SITE_CONTENT.1100010' | translate }}</span>
        </button>
      </div>
    </div>
  </div>
</div>
