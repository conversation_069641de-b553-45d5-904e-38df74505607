import { CommonModule } from '@angular/common';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { HttpModule } from '@angular/http';
import { By } from '@angular/platform-browser';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { ToastModule } from 'ng2-toastr';
import { CookieModule } from 'ngx-cookie';
import { APP_CONSTANTS, AppConstants } from '../../../app.constants';
import { AuthKeyDirective } from '../../../shared/access-rights/auth-key.directive';
import { AccessRightsService } from '../../../shared/access-rights/services/access-rights.service';
import { DomainService } from '../../../shared/domain/domain.service';
import { MenuService } from '../../../shared/menu/services/menu.service';
import { MessageService } from '../../../shared/messages/services/message.service';
import { DomainMapToDomainArrayPipe } from '../../../shared/pipes/domain-map-to-domain-array/domain-map-to-domain-array.pipe';
import { PositionService } from '../../../shared/position/position.service';
import { SharedService } from '../../../shared/services/shared.service';
import { UserDataService } from '../../../shared/user-data/user-data.service';
import { RegistryService } from '../../service/registry.service';
import { ExpertFirmRegistryComponent } from './expert-firm-registry.component';

describe('ExpertFirmRegistryComponent', () => {
  let component: ExpertFirmRegistryComponent;
  let fixture: ComponentFixture<ExpertFirmRegistryComponent>;


  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule, ReactiveFormsModule, FormsModule, CommonModule, HttpModule, TranslateModule.forRoot(), CookieModule.forRoot(), ToastModule.forRoot()],
      declarations: [ExpertFirmRegistryComponent, AuthKeyDirective, DomainMapToDomainArrayPipe],
      providers:
        [MessageService, UserDataService, MenuService, RegistryService, DomainService, SharedService, AccessRightsService, PositionService,
          { provide: APP_CONSTANTS, useValue: AppConstants }],
      schemas: [NO_ERRORS_SCHEMA]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ExpertFirmRegistryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should test getStatusRed', () => {
    const mockExpertFirm = {
      endAbilitation : "2021-01-01"
    }
  expect(component.getStatus(mockExpertFirm)).toBe("red")
  });

  it('should test getStatusRed', () => {
    const mockExpertFirm = {
      endAbilitation : new Date().setDate(new Date().getDate() + 31)
    }
  expect(component.getStatus(mockExpertFirm)).toBe("green")
  });

  it('should test getStatusRed', () => {
    const mockExpertFirm = {
      endAbilitation : new Date()
    }
  expect(component.getStatus(mockExpertFirm)).toBe("yellow")
  });
});
