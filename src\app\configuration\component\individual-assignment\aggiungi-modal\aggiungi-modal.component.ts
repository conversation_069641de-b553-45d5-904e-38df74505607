import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  ValidatorFn,
} from '@angular/forms';
import { ConfigurationService } from '../../../service/configuration.service';
import { SocietyConf } from '../../../configuration.models';
import { Router } from '@angular/router';

@Component({
  selector: 'app-aggiungi-modal',
  templateUrl: './aggiungi-modal.component.html',
  styleUrls: ['./aggiungi-modal.component.css'],
})
export class AggiungiModalComponent implements OnInit {
  @Input() isOpen: boolean;
  @Input() inactiveList: SocietyConf[];
  @Input() activeList: SocietyConf[];
  @Output() modalClose = new EventEmitter();
  selectedSociety: any[];
  form: FormGroup;
  societiesFormArray;

  constructor(
    private formBuilder: FormBuilder,
    private _configurationService: ConfigurationService,
    private router: Router
  ) {}

  ngOnInit() {
    let maxNumberOfActiveSocieties = 10;
    let maxSelections = maxNumberOfActiveSocieties - this.activeList.length;
    this.form = this.formBuilder.group({
      societies: new FormArray(
        [],
        this.validateSelectedCheckboxes(1, maxSelections)
      ),
    });
    this.addCheckboxes();
  }

  closeModal(refreshPage: boolean) {
    this.isOpen = false;
    this.modalClose.emit(refreshPage);
  }

  addCheckboxes() {
    this.societiesFormArray = this.form.controls['societies'] as FormArray;
    let orderedInactiveList = this.orderSocietiesAlphabetical(
      this.inactiveList
    );
    if (orderedInactiveList) {
      orderedInactiveList.forEach(() =>
        this.societiesFormArray.push(new FormControl(false))
      );
    }
  }

  submit() {
    const selectedSociety = this.form.value.societies
      .map((checked, i) => (checked ? this.inactiveList[i] : null))
      .filter((v) => v !== null);
    this._configurationService.addNewSociety(selectedSociety).subscribe(() => {
      this.closeModal(true);
    });
  }

  validateSelectedCheckboxes(min = 1, max) {
    const selectionValidator: ValidatorFn = (formArray: FormArray) => {
      const totalSelected = formArray.controls
        .map((control) => control.value)
        .reduce((prev, next) => (next ? prev + next : prev), 0);
      return totalSelected >= min && totalSelected <= max
        ? null
        : { required: true };
    };
    return selectionValidator;
  }

  orderSocietiesAlphabetical(socArray) {
    socArray.sort((a, b) => {
      if (a.heading === b.heading) return 0;
      if (a.heading === null) return 1;
      if (b.heading === null) return -1;
      if (a.heading < b.heading) return -1;
      if (a.heading > b.heading) return 1;
    });
    return socArray;
  }
}
