[{"applicationForm": "869", "sections": [{"formCode": "869", "formDesc": "PRSIM-INFONDG-PF", "sectionCode": "1332", "sectionDesc": "Persona Fisica", "rows": [{"maxColumns": 6, "fields": [{"span": 6, "code": "FD_HEAD_CO", "description": "Intestazione", "disabled": false, "domainCod": null, "label": "Intestazione", "mandatory": "N", "pattern": null, "readOnlyInSection": false, "readOnly": true, "renderIf": "true", "styleClass": "col-sm-6", "tooltip": null, "type": "Text", "usage": "INPUT", "value": null, "xpath": null, "placeholder": null, "showLabel": true, "fieldValue": "EKPCQAQC AZYG", "validationRule": null, "required": "false", "parentFieldCod": null}, {"span": 3, "code": "FD_SES_DES", "description": "<PERSON><PERSON>", "disabled": false, "domainCod": null, "label": "<PERSON><PERSON>", "mandatory": "N", "pattern": null, "readOnlyInSection": false, "readOnly": true, "renderIf": "true", "styleClass": "col-sm-3", "tooltip": null, "type": "Text", "usage": "INPUT", "value": null, "xpath": null, "placeholder": null, "showLabel": true, "fieldValue": "M", "validationRule": null, "required": "false", "parentFieldCod": null}, {"span": 3, "code": "FD_DT_BIRT", "description": "Data di Nascita", "disabled": false, "domainCod": "FD_DT_BIRT", "label": "Data di Nascita", "mandatory": "N", "pattern": null, "readOnlyInSection": false, "readOnly": true, "renderIf": "true", "styleClass": "col-sm-3", "tooltip": null, "type": "Timestamp", "usage": "INPUT", "value": null, "xpath": null, "placeholder": null, "showLabel": true, "fieldValue": -60397200000, "validationRule": null, "required": "false", "parentFieldCod": "CARTE_CONTAINER"}, {"span": 3, "code": "FD_NAT_DES", "description": "<PERSON><PERSON> di Nascita", "disabled": false, "domainCod": null, "label": "<PERSON><PERSON> di Nascita", "mandatory": "N", "pattern": null, "readOnlyInSection": false, "readOnly": false, "renderIf": "true", "styleClass": "col-sm-3", "tooltip": null, "type": "Text", "usage": "INPUT", "value": null, "xpath": null, "placeholder": null, "showLabel": true, "fieldValue": "DESENZANO DEL GARDA", "validationRule": null, "required": "false", "parentFieldCod": null}, {"span": 3, "code": "FD_PRV_DES", "description": "Provincia di Nascita", "disabled": false, "domainCod": null, "label": "Provincia di Nascita", "mandatory": "N", "pattern": null, "readOnlyInSection": false, "readOnly": true, "renderIf": "true", "styleClass": "col-sm-3", "tooltip": null, "type": "Text", "usage": "INPUT", "value": null, "xpath": null, "placeholder": null, "showLabel": true, "fieldValue": "BS", "validationRule": null, "required": "false", "parentFieldCod": null}, {"span": 3, "code": "FD_COD_FIS", "description": "<PERSON><PERSON>", "disabled": false, "domainCod": null, "label": "<PERSON><PERSON>", "mandatory": "N", "pattern": null, "readOnlyInSection": false, "readOnly": true, "renderIf": "true", "styleClass": "col-sm-3", "tooltip": null, "type": "Text", "usage": "INPUT", "value": null, "xpath": null, "placeholder": null, "showLabel": true, "fieldValue": "****************", "validationRule": null, "required": "false", "parentFieldCod": null}, {"span": 3, "code": "FD_COD_FIS2", "description": "Codice Fiscale2", "disabled": false, "domainCod": "UBZ_DOM_ADDRESS_TYPE", "label": "Codice Fiscale2", "mandatory": "Y", "pattern": null, "readOnlyInSection": false, "readOnly": true, "renderIf": "true", "styleClass": "col-sm-3", "tooltip": null, "type": "Text", "usage": "INPUT", "value": null, "xpath": null, "placeholder": null, "showLabel": true, "fieldValue": "RES", "validationRule": null, "required": "false", "parentFieldCod": null}, {"span": 3, "code": "FD_COD_FIS2", "description": "Codice Fiscale 3", "disabled": false, "domainCod": "UBZ_DOM_ADDRESS_TYPE", "label": "Codice Fiscale 3", "mandatory": "Y", "pattern": null, "readOnlyInSection": false, "readOnly": false, "renderIf": "true", "styleClass": "col-sm-3", "tooltip": null, "type": "Text", "usage": "INPUT", "value": null, "xpath": null, "placeholder": null, "showLabel": true, "fieldValue": null, "validationRule": null, "required": "false", "parentFieldCod": null}]}], "collapsable": true, "startCollapsed": false, "visible": true}], "error": false, "messages": []}]