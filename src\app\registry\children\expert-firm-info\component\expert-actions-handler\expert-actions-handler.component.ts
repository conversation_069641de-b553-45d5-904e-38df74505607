import { Component, OnInit, Input, EventEmitter, Output } from '@angular/core';
import { RegistryService } from '../../../../service/registry.service';

class ExpertAction {
  action: string;
  noteTitle: string;
  noteText: string;
  noteType = 'CSP';
}

@Component({
  selector: 'app-expert-actions-handler',
  templateUrl: './expert-actions-handler.component.html',
  styleUrls: ['./expert-actions-handler.component.css']
})
export class ExpertActionsHandlerComponent implements OnInit {
  public ACTIVATION_CODE = 'ATT';
  public SOSPENSION_CODE = 'SOS';
  public REVOKE_CODE = 'RAD';

  @Input() subjectType: string;
  @Input() public idAnag: string;
  @Output() modalClose: EventEmitter<boolean> = new EventEmitter()
  public noteType: string;
  public isOpen: boolean;
  public action: ExpertAction;
  public localNoteType: string;

  @Output()
  downloadSupplyContract: EventEmitter<void> = new EventEmitter<void>();

  constructor(private _registryService: RegistryService) { }

  ngOnInit() { }

  public openModal(noteType: string) {
    this.action = new ExpertAction();
    this.action.action = noteType;
    this.localNoteType = noteType;
    if (noteType === this.ACTIVATION_CODE) {
      this.saveNote();
      return;
    }
    this.isOpen = true;
  }

  public closeModal() {
    this.isOpen = false;
  }

  public saveNote() {
    this._registryService
      .saveExpertAction(this.idAnag, this.action)
      .subscribe(res => {
        this.closeModal();
        this.modalClose.emit(true);
      });
  }

  public downloadSupplyPressed() {
    this.downloadSupplyContract.emit();
  }
}
