import {
  Component,
  OnInit,
  AfterViewInit,
  Input,
  Output,
  Inject,
  EventEmitter,
  ViewChildren,
  ContentChildren,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ChangeDetectorRef,
  ViewChild
} from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { Subscription } from 'rxjs/Subscription';
import { TranslateService } from '@ngx-translate/core';

import { TaskButtonComponent } from './task-button.component';
import { AccessRightsService } from './services/access-rights.service';
import { UserDataService } from '../user-data/user-data.service';
import { MessageService } from '../messages/services/message.service';
import { Task } from './model/task';
import { TaskOutcome } from './model/task-outcome';
import { LockUnlockTask } from './model/lock-unlock-task';
import { APP_CONSTANTS, IAppConstants } from '../../app.constants';
import { ModalButtonService } from '../drop-assignment-button/service/modal-button.service';
import { PositionService } from '../../shared/position/position.service';
import { Note } from '../notes/model/note';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { DomainService } from '../domain/domain.service';
import { NgForm } from '@angular/forms';
import { NotesService } from '../../shared/notes/services/notes.service';

@Component({
  selector: 'app-task-access-rights',
  templateUrl: './task-access-rights.component.html',
  styleUrls: ['./task-access-rights.component.css'],
  providers: [NotesService]
})
export class TaskAccessRightsComponent
  implements OnInit, AfterViewInit, OnDestroy {
  @Input() positionId: string;
  @Input() taskId: string;
  @Input() taskCod: string;
  @Input() excludeList: string[] = [];
  @Input() isCte: boolean;

  @Output() taskLocked = new EventEmitter();
  @Output() taskLockedByOtherUser = new EventEmitter<string>();
  @Output() taskUnlocked = new EventEmitter();

  locked: boolean;
  lockedByOtherUser: boolean;
  filteredTaskOutcomes: TaskOutcome[] = [];
  childSubscriptions: Subscription[] = [];

  @ContentChildren(TaskButtonComponent)
  contentButtons: QueryList<TaskButtonComponent>;
  @ViewChildren(TaskButtonComponent)
  childButtons: QueryList<TaskButtonComponent>;

  @ViewChild('addNoteModal') addNoteModal: ModalDirective;
  newNote: Note = new Note();
  noteTypeDomain: any;
  @ViewChild('f') f: NgForm;

  constructor(
    private cdr: ChangeDetectorRef,
    private accessRightsService: AccessRightsService,
    private userDataService: UserDataService,
    private messageService: MessageService,
    private translateService: TranslateService,
    private modalButtonService: ModalButtonService,
    private positionService: PositionService,
    private domainService: DomainService,
    private router: Router,
    private notesService: NotesService,
    @Inject(APP_CONSTANTS) public constants: IAppConstants
  ) {}

  ngOnInit() {}

  ngAfterViewInit() {
    this.init();
  }

  ngOnDestroy() {
    for (const subscription of this.childSubscriptions) {
      subscription.unsubscribe();
    }
  }

  init() {
    const user = this.userDataService.getUserData().username;
    this.accessRightsService
      .getOpenTaskForUser(`${this.constants.processCode}${this.positionId}`)
      .switchMap((taskList: Task[]) => {
        let requestedTask: Task;
        for (const i in taskList) {
          if (
            taskList.hasOwnProperty(i) &&
            taskList[i].taskCod === this.taskCod &&
            `${taskList[i].taskId}` === this.taskId
          ) {
            requestedTask = taskList[i];
            break;
          }
        }

        if (requestedTask) {
          if (!!requestedTask.lockingUser) {
            if (
              requestedTask.lockingUser.toUpperCase() === user.toUpperCase()
            ) {
              this.locked = true;
              this.lockedByOtherUser = false;
              this.taskLocked.emit();
              return this.accessRightsService.getOutcomesByTaskCod(
                requestedTask.taskCod
              );
            } else {
              this.locked = false;
              this.lockedByOtherUser = true;
              this.taskLockedByOtherUser.emit(requestedTask.lockingUser);
              const msg = this.translateService.instant(
                'UB1.TASKACCESSRIGHTS.DIRECTIVE.ERROR.ATTIVITAUTENTE'
              );
              this.messageService.showInfo(
                `${msg}${requestedTask.lockingUser.toUpperCase()}`,
                this.translateService.instant('UB1.TITLE.INFO')
              );
              return Observable.empty();
            }
          } else {
            this.locked = false;
            this.lockedByOtherUser = false;
            const lockUnlockObj = new LockUnlockTask(this.taskId, this.taskCod);
            lockUnlockObj.force = false;
            return this.accessRightsService.lockAndGetOutcomeList(
              lockUnlockObj
            );
          }
        } else {
          return Observable.empty();
        }
      })
      .subscribe((outcomes: TaskOutcome[]) => {
        if (outcomes) {
          if (!this.locked) {
            this.messageService.showInfo(
              this.translateService.instant(
                'UB1.TASKACCESSRIGHTS.DIRECTIVE.ATTIVITA'
              ),
              this.translateService.instant('UB1.TITLE.INFO')
            );
            this.locked = true;
            this.taskLocked.emit();
          }
          this.filterOutcomes(outcomes);
          this.attachListenerToChildren();
        }
        this.cdr.detectChanges();
      });
  }

  releaseTask() {
    const lockUnlockObj = new LockUnlockTask(this.taskId, this.taskCod);
    lockUnlockObj.force = true;
    this.accessRightsService.unlockTask(lockUnlockObj).subscribe(() => {
      this.messageService.showInfo(
        this.translateService.instant(
          'UB1.TASKACCESSRIGHTS.DIRECTIVE.ATTIVITARILASCIATA'
        ),
        this.translateService.instant('UB1.TITLE.INFO')
      );
      this.locked = false;
      this.lockedByOtherUser = false;
      this.taskUnlocked.emit();
      this.filteredTaskOutcomes = [];
    });
  }

  forceLockTask() {
    const lockUnlockObj = new LockUnlockTask(this.taskId, this.taskCod);
    lockUnlockObj.force = true;
    this.lockAndGetOutcomeList(lockUnlockObj);
  }

  lockAndGetOutcomeList(lockUnlockObj: LockUnlockTask) {
    this.accessRightsService
      .lockAndGetOutcomeList(lockUnlockObj)
      .subscribe((outcomes: TaskOutcome[]) => {
        this.messageService.showInfo(
          this.translateService.instant(
            'UB1.TASKACCESSRIGHTS.DIRECTIVE.ATTIVITA'
          ),
          this.translateService.instant('UB1.TITLE.INFO')
        );
        this.locked = true;
        this.lockedByOtherUser = false;
        this.taskLocked.emit();
        this.filterOutcomes(outcomes);
        this.attachListenerToChildren();
      });
  }

  completeTask(eventCod: string, noteMessage?: Note) {
    const lockUnlockObj = new LockUnlockTask(this.taskId, this.taskCod);
    lockUnlockObj.eventCode = eventCod;
    const buttonComp: TaskButtonComponent = this.findTaskButtonByEventCod(
      eventCod
    );

    // Per la sospensione si richiama il servizio specifico che include salvataggio della nota e sospensione della pratica
    if (eventCod === 'UBZ_SUSP' && noteMessage) {
      lockUnlockObj.positionId = this.positionId;
      this.positionService.getAppraisalInfo(this.positionId).subscribe(res => {
        if (
          noteMessage['reasonCode'] &&
          noteMessage['reasonCode'] === 'DNC' &&
          res['appraisal'] &&
          res['appraisal']['posSegment'] &&
          res['appraisal']['posSegment'] === 'IND' &&
          res['appraisal']['originProcess'] &&
          (res['appraisal']['originProcess'] === 'EMP' ||
            res['appraisal']['originProcess'] === 'UB6')
        ) {
          this.modalButtonService.setStoredData(noteMessage, lockUnlockObj);
          this.router.navigateByUrl(
            'document-suspend/' + noteMessage['positionId']
          );
        } else {
          this.modalButtonService
            .suspendAppraisal(noteMessage, lockUnlockObj)
            .subscribe(response => {
              if (response) {
                buttonComp.success.emit();
                return this.emitSuccessMessage();
              }
            });
        }
      });
    } else if (eventCod === 'UBZ_PRZ_KO' && (this.newNote.noteDesc === null || this.newNote.noteDesc === undefined || this.newNote.noteDesc === '')) {
      this.newNote = new Note();
      this.addNoteModal.show();
      this.domainService.newGetDomain('UBZ_DOM_NOTE_TYPE').subscribe(res => 
        this.noteTypeDomain = res
      );
      this.newNote.positionId = this.positionId;
      this.newNote.noteType = 'RIA';
      this.newNote.positionType = 'PER';
      this.newNote.noteTitle = this.translateService.instant('UBZ.TaskAccessRights.DefaultTitle');
    } else {
      if (this.addNoteModal.isShown) {        
        this.notesService.saveNote(this.newNote).subscribe(() => {
          this.addNoteModal.hide();
          this.messageService.showSuccess(
            this.translateService.instant('UBZ.SITE_CONTENT.10001100'),
            this.translateService.instant('UBZ.SITE_CONTENT.10001101')
          );
        });
      }
      
      this.executeBeforeCallback(buttonComp)
        .switchMap((beforeOutcome: boolean) => {
          if (beforeOutcome) {
            return this.accessRightsService.closeTask(lockUnlockObj);
          } else {
            return Observable.empty();
          }
        })
        .subscribe(
          () => {
            if (buttonComp) {
              buttonComp.success.emit();
              this.emitSuccessMessage();
            }
          },
          () => {
            if (buttonComp) {
              buttonComp.error.emit();
            }
          }
        );
    }
  }

  emitSuccessMessage() {
    this.messageService.showSuccess(
      this.translateService.instant('UBZ.SITE_CONTENT.1001100000'),
      this.translateService.instant('UBZ.SITE_CONTENT.1001100001')
    );
  }

  private executeBeforeCallback(
    buttonComp: TaskButtonComponent
  ): Observable<boolean> {
    if (buttonComp && buttonComp.before) {
      return buttonComp.before();
    } else {
      return Observable.of(true);
    }
  }

  goHome() {
    this.router.navigateByUrl('dashboard/LAA');
  }

  private filterOutcomes(taskOutcomes: TaskOutcome[]) {
    this.filteredTaskOutcomes = [];
    const buttons: TaskButtonComponent[] = this.contentButtons
      ? this.contentButtons.toArray()
      : [];

    for (const button of buttons) {
      if (this.excludeList.indexOf(button.eventCod) === -1) {
        this.excludeList.push(button.eventCod);
      }
    }

    for (const outcome of taskOutcomes) {
      if (this.excludeList.indexOf(outcome.eventCod) === -1) {
        this.filteredTaskOutcomes.push(outcome);
      }
    }
  }

  private attachListenerToChildren() {
    for (const item of this.contentButtons.toArray()) {
      this.childSubscriptions.push(
        item.buttonClick.subscribe(eventCod => this.completeTask(eventCod))
      );
    }
  }

  private findTaskButtonByEventCod(eventCod: string): TaskButtonComponent {
    let taskButtonToReturn: TaskButtonComponent;
    let buttons: TaskButtonComponent[] = [];
    if (this.contentButtons) {
      buttons = buttons.concat(this.contentButtons.toArray());
    }
    if (this.childButtons) {
      buttons = buttons.concat(this.childButtons.toArray());
    }
    for (const i in buttons) {
      if (buttons.hasOwnProperty(i) && buttons[i].eventCod === eventCod) {
        taskButtonToReturn = buttons[i];
        break;
      }
    }
    return taskButtonToReturn;
  }
}
