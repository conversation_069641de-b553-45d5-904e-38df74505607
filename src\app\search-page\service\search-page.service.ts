import { Injectable } from '@angular/core';
import { Http, Response, ResponseContentType } from '@angular/http';
import { Observable } from 'rxjs/Observable';
import { Router } from '@angular/router';

import { SearchParams } from '../models/search-params';
import { MenuService } from '../../shared/menu/services/menu.service';

@Injectable()
export class SearchPageService {
  searchUrlServicing: string = '/UBZ-ESA-RS/service/search/v1/positions/appraisal/servicing';
  advancedSearch = {};
  fastSearch = {}; // searchType , searchText
  showBackSearchButton = false;
  searchUrlBack = ''; // url pagina di ricerca su cui tornare indietro
  serviceUrls = {
    RI: '/UBZ-ESA-RS/service/search/v1/positions/preappraisal/adv',
    PE: '/UBZ-ESA-RS/service/search/v1/positions/appraisal/adv',
    ASAS: '/UBZ-ESA-RS/service/search/v1/positions/asset',
    AS: '/UAM-ESA-RS/service/asset/v1/searchAsset',
    GA: '/UBZ-ESA-RS/service/search/v1/positions/collateral'
  }; // as è mockato, poi si chiamerà un servizo di UAM (waiting back-end)

  constructor(private http: Http, private menuService: MenuService, private router: Router) { }

  // per la ricerca avanzata asset -> advancedSearch['searchType'] === 'AS'
  // aggiungendo nel json ‘deletedAsset: true’ si visualizzano anche gli asset cancellati.
  doNewAdvancedSearch(
    pageNum: number,
    pageSize: number,
    objectFilter?: any,
    searchType?: string
  ) {
    if (objectFilter) {
      this.advancedSearch['filters'] = objectFilter;
      if (searchType) {
        this.advancedSearch['searchType'] = searchType;
      }
    }
    const url = this.serviceUrls[this.advancedSearch['searchType']];
    this.advancedSearch['pageNum'] = pageNum;
    this.advancedSearch['pageSize'] = pageSize;
    const params = new SearchParams(
      pageNum,
      pageSize,
      'true',
      false,
      this.advancedSearch['filters']
    );
    if (this.advancedSearch['searchType'] === 'AS') {
      params['deletedAsset'] = true;
    }
    return this.http.post(url, params).map((resp: Response) => resp.json());
  }

  // per la ricerca avanzata asset -> advancedSearch['searchType'] === 'AS'
  // aggiungendo nel json ‘deletedAsset: true’ si visualizzano anche gli asset cancellati.
  extractReport(pageNum: number, pageSize: number) {
    const url = this.serviceUrls[this.advancedSearch['searchType']];
    this.advancedSearch['pageNum'] = pageNum;
    this.advancedSearch['pageSize'] = pageSize;
    const params = new SearchParams(
      pageNum,
      pageSize,
      'true',
      false,
      this.advancedSearch['filters'],
      true
    );
    if (this.advancedSearch['searchType'] === 'AS') {
      params['deletedAsset'] = true;
    }
    return this.http
      .post(url, params, { responseType: ResponseContentType.Blob })
      .map((resp: Response) => resp.blob());
  }

  doNewFastSearch(searchText: string) {
    this.fastSearch['searchType'] = 'RI';
    this.fastSearch['searchText'] = searchText;
    const url = `/UBZ-ESA-RS/service/search/v1/fastsearch`;
    return this.http
      .post(url, { searchText })
      .map((resp: Response) => resp.json());
  }

  getExpSocList(): Observable<any> {
    return this.http
      .get('/UBZ-ESA-RS/service/domain/v1/SocPer/list')
      .map((resp: Response) => resp.json());
  }

  // Toggle visualizzazione button "Torna alla ricerca"
  // urlBack in input è il path della pagina elenco risultati (fastSearch / advancedSearch)
  toggleBackSearchButton(urlBack?: string) {
    if (urlBack) {
      this.searchUrlBack = urlBack;
    }
    this.showBackSearchButton = !this.showBackSearchButton;
  }

  goToDetailsPage(appraisalId, backSearchUrl: string) {
    this.toggleBackSearchButton(backSearchUrl);
    this.router.navigate([`/generic-task/${appraisalId}/-/-/`]);
    this.menuService.changeHeadMenuStatus(0);
  }

  goToAppraisalRequest(requestId, backSearchUrl: string) {
    // FIXME - TOGLIERE SE CENTRALIZZAZIONE METODO FUNZIONA CORRETTAMENTE    
    this.toggleBackSearchButton(backSearchUrl);
    this.router.navigate([`landing/WRPE/${requestId}`]);
    this.menuService.changeHeadMenuStatus(0);
  }


  doNewSearchServicing(pageNum: number, pageSize: number, objectFilter?: any, searchType?: string) {
    if (objectFilter) {
      this.advancedSearch['filters'] = objectFilter;
      if (searchType) {
        this.advancedSearch['searchType'] = searchType;
      }
    }
    const url = this.searchUrlServicing;
    this.advancedSearch['pageNum'] = pageNum;
    this.advancedSearch['pageSize'] = pageSize;
    const params = new SearchParams(
      pageNum,
      pageSize,
      'true',
      false,
      this.advancedSearch['filters']
    );
    return this.http.post(url, params).map((resp: Response) => resp.json());
  }
}
