import {
  Component,
  OnInit,
  Input,
  Output,
  ViewChild,
  EventEmitter
} from '@angular/core';
import { NgForm } from '@angular/forms';

import { AccordionAPFService } from '../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { TemplateValidation } from '../../model/template-validation';
import { AppraisalCompilationService } from '../../service/appraisal-compilation.service';

@Component({
  selector: 'app-appraisal-template-validation',
  templateUrl: './appraisal-template-validation.component.html',
  styleUrls: ['./appraisal-template-validation.component.css']
})
export class AppraisalTemplateValidationComponent implements OnInit {
  @Input() positionId: string;
  @Input() assetId: number;
  @Input() templateName: string;
  @Input() haveDisabledFields: boolean;

  @Output() statusChange = new EventEmitter<boolean>();

  @ViewChild('f') form: NgForm;

  appValidation: TemplateValidation;

  constructor(
    public accordionAPFService: AccordionAPFService,
    private appraisalCompilationService: AppraisalCompilationService
  ) {}

  ngOnInit() {
    this.appValidation = new TemplateValidation(
      this.positionId,
      this.assetId,
      this.templateName
    );
    this.form.form.statusChanges.subscribe(() =>
      this.statusChange.emit(this.form.valid)
    );
    this.appraisalCompilationService
      .getTemplateValidation(this.appValidation)
      .subscribe(res => {
        this.appValidation.validate = res.validate;
        this.appValidation.note = res.note;
      });
  }
}
