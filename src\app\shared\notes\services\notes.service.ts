import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';
import { Observable } from 'rxjs/Observable';

import { Note } from '../model/note';

@Injectable()
export class NotesService {
  private baseUrl = '/UBZ-ESA-RS/service/note/v1/notes';

  constructor(private http: Http) {}

  getNotes(positionId: string, positionType: string): Observable<Note[]> {
    positionId = encodeURIComponent(positionId);
    positionType = encodeURIComponent(positionType);
    const url = `${this.baseUrl}/${positionId}/${positionType}`;
    return this.http.get(url).map((resp: Response) => resp.json());
  }

  saveNote(note: Note): Observable<any> {
    const url = `${this.baseUrl}`;
    return this.http.post(url, note).map((resp: Response) => resp.text());
  }
}
