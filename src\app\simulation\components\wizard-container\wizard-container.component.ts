import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Chil<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject } from '@angular/core';
import { Activated<PERSON>oute, Params, Router } from '@angular/router';

import { LandingService } from '../../services/landing/landing.service';
import { WizardComponent } from '../../../shared/wizard/components/wizard.component';
import { PositionHeaderComponent } from '../../../shared/position/position-header/position-header.component';
import { AccessRightsService } from '../../../shared/access-rights/services/access-rights.service';
import { UserDataService } from '../../../shared/user-data/user-data.service';
import { Task } from '../../../shared/access-rights/model/task';
import { Domain } from '../../../shared/domain/domain';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';

import { WizardDetailService } from '../../../wizard-detail/services/wizard-detail.service';
import { Subscription } from 'rxjs/Subscription';
import { Observable } from 'rxjs/Observable';
import 'rxjs/add/operator/switchMap';
import { PositionService } from '../../../shared/position/position.service';

@Component({
  selector: 'app-wizard-container',
  templateUrl: './wizard-container.component.html',
  styleUrls: ['./wizard-container.component.css']
})
export class WizardContainerComponent implements OnInit, OnDestroy {
  @ViewChild(WizardComponent)
  wizardComponent: WizardComponent;
  @ViewChild(PositionHeaderComponent)
  posHeaderComponent: PositionHeaderComponent;
  positionId: string;
  wizardCode: string;
  bpmTaskId: string;
  bpmTaskCod: string;
  readOnly: string;
  subscription: Subscription;
  lockingUser: string;
  private _opinionTypes: Domain[] = [];
  opinionType: string = '';

  private lockingAppraisal: string;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    public landingService: LandingService,
    private accessRightsService: AccessRightsService,
    private userDataService: UserDataService,
    private _wizardDetailService: WizardDetailService,
    private positionService: PositionService,
    @Inject(APP_CONSTANTS) public constants: IAppConstants
  ) {}

  ngOnInit() {
    this.activatedRoute.params
      .switchMap((params: Params) => {
        this.positionId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        this.bpmTaskId = params['taskId'];
        this.bpmTaskCod = params['taskCod'];
        this.readOnly = params['readOnly'];
        if (this.wizardCode === this.constants.wizardCodes.PER) {
          return this.positionService.getAppraisalInfo(this.positionId);
        } else {
          return Observable.empty();
        }
      })
      .switchMap((res: any) => {
        if (this.wizardCode === this.constants.wizardCodes.PER) {
          this.opinionType = res.opinionType;
          this._wizardDetailService.getOpinionTypes().subscribe((res: any) => {
            this._opinionTypes = res;
            if (this.opinionType && this._opinionTypes[this.opinionType]) {
              this.opinionType = this._opinionTypes[
                this.opinionType
              ].translationCod;
            }
          });
        }
        if (this.readOnly === 'false' && this.bpmTaskId && this.bpmTaskCod) {
          return this.accessRightsService.getOpenTaskForUser(
            `${this.constants.processCode}${this.positionId}`
          );
        } else {
          // Se flag readOnly = true si setta la variabile per disabilitare campi/pulsanti in pagina
          if (this.readOnly) {
            this.landingService.positionLocked = false;
          }
          return Observable.empty();
        }
      })
      .subscribe((taskList: Task[]) => {
        if (taskList && taskList.length > 0) {
          const user = this.userDataService.getUserData().username;
          let requestedTask: Task;
          for (const i in taskList) {
            if (
              taskList.hasOwnProperty(i) &&
              taskList[i].taskCod === this.bpmTaskCod &&
              `${taskList[i].taskId}` === this.bpmTaskId
            ) {
              requestedTask = taskList[i];
              break;
            }
          }

          if (requestedTask) {
            if (!!requestedTask.lockingUser) {
              this.lockingUser = requestedTask.lockingUser;
              if (
                requestedTask.lockingUser.toUpperCase() === user.toUpperCase()
              ) {
                this.landingService.positionLocked = true;
              }
            }
          }
        }
      });

    this.subscription = this.landingService.wizardRefreshed.subscribe(() => {
      this.wizardComponent.getWizard();
      this.posHeaderComponent.refreshData();
    });
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  goToDashboard() {
    this.router.navigateByUrl('index');
  }

  goToAppraisalDetail() {
    this.router.navigateByUrl(
      `generic-task/${this.positionId}/${this.bpmTaskId}/${this.bpmTaskCod}`
    );
  }
}
