import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Location } from '@angular/common';

import { AppraisalMigrationService } from '../service/appraisal-migration.service';
import { DomainService } from '../../shared/domain';

@Component({
  selector: 'app-appraisal-migration',
  templateUrl: './appraisal-migration.component.html',
  styleUrls: ['./appraisal-migration.component.css'],
  providers: [AppraisalMigrationService, DomainService]
})
export class AppraisalMigrationComponent implements OnInit {
  appraisalId: string;
  wizardCode: string;
  taskLockingUser: string;
  appraisal: any = {};
  selectedAsset: any = null;
  resourceItemType: string;
  resourceItemCategory: string;  
  activeDomain: any = [];
  activeCategories: any = [];

  constructor(
    private appraisalMigrationService: AppraisalMigrationService,
    private domainService: DomainService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private location: Location
  ) {}

  ngOnInit() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.appraisalId = params['appraisalId'];
      this.wizardCode = params['wizardCode'];
      if (params['taskLockingUser'] !== '-') {
        this.taskLockingUser = params['taskLockingUser'];
      }
      this.appraisalMigrationService
        .getAppraisalDetail(this.appraisalId)
        .subscribe(x => {
          this.appraisal = x;
          this.resourceItemType = x.finVal.resourceItem[0].resourceItemType;
          this.resourceItemCategory =
            x.finVal.resourceItem[0].resourceItemCategory;
        });
    });
    this.getCategory();
  }

  getCategory() {
    this.domainService.newGetDomain('UBZ_DOM_CATEGORY_TYPE').subscribe(res => {
      this.activeDomain = this.domainService.transform(res).forEach(cat => {
        this.activeCategories.push(cat.domCode);
      });;
    });

  }

  changeTab(asset) {
    this.selectedAsset = asset;
  }

  navigateBack() {
    if (this.appraisal && 
      typeof this.appraisal.finVal !== 'undefined' && 
      typeof this.appraisal.finVal.macroProcess !== 'undefined' &&
      this.appraisal.finVal.macroProcess === 'MLP') {
      this.router.navigate([`/generic-task/${this.appraisalId}/-/-/`]);
      return;
    }
    this.location.back();
  }
}
