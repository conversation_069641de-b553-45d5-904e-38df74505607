<div class="row">
  <div class="col-sm-12 section-headline">
    <h1 *ngIf="dashboardType === 'SIM'"><i class="icon-dashboard"></i> {{'UBZ.SITE_CONTENT.1001011101' | translate }}</h1>
    <h1 *ngIf="dashboardType === 'REQ'"><i class="icon-dashboard"></i> {{'UBZ.SITE_CONTENT.1100100' | translate}}</h1>
    <h1 *ngIf="dashboardType === 'ALL'"><i class="icon-dashboard"></i> {{'UBZ.SITE_CONTENT.1001011110' | translate }}</h1>
    <h1 *ngIf="dashboardType === 'OUT-SLA'"><i class="icon-dashboard"></i> {{'UBZ.SITE_CONTENT.1001011111' | translate }}</h1>
    <h1 *ngIf="dashboardType === 'LAA'"><i class="icon-dashboard"></i> {{'UBZ.SITE_CONTENT.11' | translate }}</h1>
    <h1 *ngIf="dashboardType === 'LAPS'"><i class="icon-dashboard"></i> {{'UBZ.SITE_CONTENT.10111001' | translate }}</h1>
    <h1 *ngIf="dashboardType === 'LASC'"><i class="icon-dashboard"></i> {{'UBZ.SITE_CONTENT.10111010' | translate }}</h1>
    <h1 *ngIf="dashboardType === 'LASS'"><i class="icon-dashboard"></i> {{'UBZ.SITE_CONTENT.10111011' | translate }}</h1>
    <h1 *ngIf="dashboardType === 'PCM'"><i class="icon-dashboard"></i> {{'UBZ.SITE_CONTENT.10111100' | translate }}</h1>
    <h1 *ngIf="dashboardType === 'WAM'"><i class="icon-dashboard"></i> {{'UBZ.SITE_CONTENT.10111100' | translate }}</h1>
    <h2>{{'UBZ.SITE_CONTENT.1100101' | translate}}</h2>
  </div>
</div>
<section id="dashboard" class="dashboard">
  <div class="row">
    <div class="uc-datatabs">
      <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" [ngClass]="activeClassMap[activeTabs[0].toString()]">
          <a href="" aria-controls="carico" role="tab" data-toggle="tab" (click)="activeTabs[0] = true ; activeTabs[1] = false">{{'UBZ.SITE_CONTENT.1100110' | translate}}</a>
        </li>
        <li role="presentation" [ngClass]="activeClassMap[activeTabs[1].toString()]">
          <a href="" aria-controls="struttura" role="tab" data-toggle="tab" (click)="activeTabs[1] = true ; activeTabs[0] = false">{{'UBZ.SITE_CONTENT.1100111' | translate}}</a>
        </li>
      </ul>
      <div class="tab-content">
        <ng-container *ngIf="activeTabs[0]">
          <app-simulations-dashboard *ngIf="dashboardType === 'SIM'" [searchTypes]="'USER'" [active]="activeTabs[0]"></app-simulations-dashboard>
          <app-request-dashboard *ngIf="dashboardType === 'REQ'" [searchTypes]="'USER'" [active]="activeTabs[0]"></app-request-dashboard>
          <app-all-dashboard *ngIf="dashboardType === 'ALL'" [searchTypes]="'USER'" [active]="activeTabs[0]"></app-all-dashboard>
          <app-loan-administration-all *ngIf="dashboardType === 'LAA'" [searchTypes]="'USER'" [active]="activeTabs[0]"></app-loan-administration-all>
          <app-loan-administation-property-surveillance *ngIf="dashboardType === 'LAPS'" [searchTypes]="'USER'" [active]="activeTabs[0]"></app-loan-administation-property-surveillance>
          <app-loan-administation-shipping-surveillance *ngIf="dashboardType === 'LASS'" [searchTypes]="'USER'" [active]="activeTabs[0]"></app-loan-administation-shipping-surveillance>
          <app-perital-company-monitoring *ngIf="dashboardType === 'PCM'" [searchTypes]="'USER'" [active]="activeTabs[0]"></app-perital-company-monitoring>
          <app-well-acquainted-monitoring *ngIf="dashboardType === 'WAM'" [searchTypes]="'USER'" [active]="activeTabs[0]"></app-well-acquainted-monitoring>
        </ng-container>
        <ng-container *ngIf="activeTabs[1]">
          <app-simulations-dashboard *ngIf="dashboardType === 'SIM'" [searchTypes]="'STRUCT'" [active]="activeTabs[1]"></app-simulations-dashboard>
          <app-request-dashboard *ngIf="dashboardType === 'REQ'" [searchTypes]="'STRUCT'" [active]="activeTabs[1]"></app-request-dashboard>
          <app-all-dashboard *ngIf="dashboardType === 'ALL'" [searchTypes]="'STRUCT'" [active]="activeTabs[1]"></app-all-dashboard>
          <app-loan-administration-all *ngIf="dashboardType === 'LAA'" [searchTypes]="'STRUCT'" [active]="activeTabs[1]"></app-loan-administration-all>
          <app-loan-administation-property-surveillance *ngIf="dashboardType === 'LAPS'" [searchTypes]="'STRUCT'" [active]="activeTabs[1]"></app-loan-administation-property-surveillance>
          <app-loan-administation-shipping-surveillance *ngIf="dashboardType === 'LASS'" [searchTypes]="'STRUCT'" [active]="activeTabs[1]"></app-loan-administation-shipping-surveillance>
          <app-perital-company-monitoring *ngIf="dashboardType === 'PCM'" [searchTypes]="'STRUCT'" [active]="activeTabs[1]"></app-perital-company-monitoring>
          <app-well-acquainted-monitoring *ngIf="dashboardType === 'WAM'" [searchTypes]="'STRUCT'" [active]="activeTabs[1]"></app-well-acquainted-monitoring>
        </ng-container>
      </div>
    </div>
  </div>
</section>