import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { Router } from '@angular/router';
import { ApiLogStatusService } from './services/api-log-status.service';
import { ApiLogStatusFilterModel } from './models/api-log-status.filter.model';
import { Domain, DomainService } from '../../../shared/domain';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-api-log-status',
  templateUrl: './api-log-status.component.html',
  styleUrls: ['./api-log-status.component.css'],
  providers: [ApiLogStatusService, ApiLogStatusFilterModel]
})
export class ApiLogStatusComponent implements OnInit {
  @ViewChild('paginator')
  paginator: any;
  @Input()
  positionId: string;
  filtersOpen: boolean;
  pageSize = 10;
  searchFilter: ApiLogStatusFilterModel = new ApiLogStatusFilterModel();
  apiAppraisals: any[] = [];
  apiAppraisalsStateList: any[] = [];
  apiAppraisalsSubStateList: any[] = [];
  statusTypeDomain: Domain[] = [];
  subStatusTypeDomain: Domain[] = [];
  apiAppraisalsNumber: number;
  private pageNumber = 1;
  private searchActive: boolean;

  constructor(
    private _apiLogStatusService: ApiLogStatusService,
    private _router: Router,
    private domainService: DomainService
  ) {}

  ngOnInit() {
    this.getList();
    Observable.forkJoin([
      this.domainService.newGetDomain('UBZ_DOM_STATUS'),
      this.domainService.newGetDomain('UBZ_DOM_API_SUBSTATUS')
    ]).subscribe(result => {
      if (result && result[0]) {
        this.statusTypeDomain = result[0];
      }
      if (result && result[1]) {
        this.subStatusTypeDomain = result[1];
      }
    });
  }

  /**
   * @function
   * @name getList
   * @description Recupera le perizie di positionId, eventualmente filtrate secondo quanto inserito in pagina
   */
  private getList() {
    if (!this.searchActive) {
      this.getApiAppraisalList();
    } else {
      this.filter();
    }
  }

  /**
   * @function
   * @name getApiAppraisalList
   * @description Invoca il servizio del componente per effettuare la chiamata e recuperare la lista di perizie
   */
  private getApiAppraisalList(): void {
    this._apiLogStatusService
      .getApiAppraisalList(this.positionId, this.pageNumber, this.pageSize)
      .subscribe(res => {
        this.parseResults(res);
      });
  }

  /**
   * @function
   * @name filter
   * @description Imposta a true il flag di ricerca ed setta i valori per l'oggetto filtro da usare nella chiamata del servizio
   */
  filter() {
    this.searchActive = true;
    this.searchFilter.appraisalId =
      this.searchFilter.appraisalId === ''
        ? null
        : this.searchFilter.appraisalId;
    this.searchFilter.appPhaseCod =
      this.searchFilter.appPhaseCod === ''
        ? null
        : this.searchFilter.appPhaseCod;
    this.searchFilter.page = this.pageNumber;
    this.searchFilter.pageSize = this.pageSize;
    this._apiLogStatusService.filter(this.searchFilter).subscribe(res => {
      this.parseResults(res);
    });
  }

  /**
   * @function
   * @name startFilter
   * @description Invocato sul click del pulsante "Filtra" in pagina, scatena una nuova ricerca filtrata
   */
  startFilter() {
    // Inizializzo la ricerca
    // I parametri sono già impostati
    this.searchActive = true;
    if (this.pageNumber > 1) {
      // Cambiare pagina fa scattare il get list
      this.paginator.page = 1;
    } else {
      // Se non cambio pagina invoco la funzione
      this.filter();
    }
  }

  /**
   * @function
   * @name parseResult
   * @description Setta le variabili per mostrare a video i risultati
   * @param httpResults Oggetto ricevuto dal BE con i risultati paginati e count totale
   */
  private parseResults(httpResults: any): void {
    this.apiAppraisals = httpResults.details;
    this.apiAppraisalsNumber = httpResults.count;
    this.addStateToList();
    this.addSubStateToList();
  }

  /**
   * @function
   * @name addStateToList
   * @description Inizializza l'array contenente gli stati filtrabili e scorre la lista di perizie recuperato per
   * popolare l'array in maniera univoca
   */
  addStateToList() {
    let toInsertFlag;
    this.apiAppraisalsStateList = new Array();
    this.apiAppraisals.forEach(api => {
      toInsertFlag = true;
      for (const state of this.apiAppraisalsStateList) {
        if (
          state.appPhaseCod === api.appPhaseCod &&
          state.appStatusCod === api.appStatusCod
        ) {
          // Se l'oggetto è già stato inserito setta il flag = false
          toInsertFlag = false;
          break;
        }
      }
      if (toInsertFlag) {
        this.apiAppraisalsStateList.push({
          appPhaseCod: api.appPhaseCod,
          appStatusCod: api.appStatusCod
        });
      }
    });
  }

  /**
   * @function
   * @name addSubStateToList
   * @description Inizializza l'array contenente i sub stati filtrabili e scorre la lista di perizie recuperato per
   * popolare l'array in maniera univoca
   */
  addSubStateToList() {
    this.apiAppraisalsSubStateList = new Array();
    this.apiAppraisals.forEach(api => {
      if (this.apiAppraisalsSubStateList.indexOf(api.appSubStatusCod) === -1) {
        this.apiAppraisalsSubStateList.push(api.appSubStatusCod);
      }
    });
  }

  /**
   * @function
   * @name changePage
   * @description Aggiorna il numero di pagina del paginatore e invoca il servizio per recuperare la lista perizie aggiornata
   * @param event
   */
  changePage(event: any) {
    this.pageNumber = event.page;
    this.getList();
  }

  /**
   * @function
   * @name pageSizeChanged
   * @description Sulla modifica dei risultati visibili in pagina invoca il servizio per recuperare la lista perizie aggiornata
   */
  pageSizeChanged() {
    this.getList();
  }

  /**
   * @function
   * @name goToApiAppraisalsDetail
   * @description Esegue la navigazione verso il dettaglio della perizia selezionata
   * @param {string} appraisalId Id della perizia da aprire nel dettaglio
   */
  goToApiAppraisalsDetail(appraisalId: string) {
    this._router.navigate([`/generic-task/${appraisalId}/-/-/`]);
  }

  /**
   * @function
   * @name openFilters
   * @description Instanzia un nuovo oggetto filtro e apre la sezione dedicata in pagina
   */
  openFilters(): void {
    this.searchFilter = new ApiLogStatusFilterModel();
    this.filtersOpen = true;
  }

  /**
   * @function
   * @name closeFilters
   * @description Invoca il servizio per recuperare la lista perizie aggiornata e setta a false i flag di ricerca e la variabile
   * per visualizzare la sezione dei filtri
   */
  closeFilters(): void {
    this.getApiAppraisalList();
    this.filtersOpen = false;
    this.searchActive = false;
  }

  /**
   * @function
   * @name cleanSearchFilter
   * @description Instanzia un nuovo oggetto filtri
   */
  cleanSearchFilter() {
    this.searchFilter = new ApiLogStatusFilterModel();
  }
}
