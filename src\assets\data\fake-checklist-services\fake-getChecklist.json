[{"prog": null, "entityType": "CL", "ndg": "1033", "offerId": "", "heading": "ROSSI DI SCHIO DOMENICO", "offerDesc": null, "groups": [{"ndg": null, "prog": 15406, "groupCod": "DOCCIT", "categoryDesc": "Documento legato alla cittadinanza", "documentCod": null, "flagMandatory": "N", "mandatoryFor": "100;DATI-RDY", "flagRetroCancel": "N", "flagToPrint": null, "flagForce": null, "forced": null, "flagToValidate": "Y", "validated": null, "validatedDate": null, "flagFromChecklist": "Y", "flagToSign": null, "flagAcquireOriginal": "N", "acquired": "Y", "acquiredOriginal": null, "docAdmitted": [{"prog": 15406, "documentCod": "CARSOG", "documentDesc": "Carta di soggiorno"}, {"prog": 15406, "documentCod": "PERSOG", "documentDesc": "Permesso di soggiorno"}], "lastUpload": {"prog": 15406, "documentCod": "CARSOG", "documentDesc": "Carta di soggiorno", "userId": "C316031", "uploadDate": 1493893058548, "notes": null, "repositoryId": "16630", "customDocDesc": null, "xpiToken": null}, "infoDcr": {"dcrDate": null, "dcrStatus": null, "dcrToken": null, "dcrNeeded": false}, "origin": null, "flagVisible": null, "dateCertNeeded": false}, {"ndg": null, "prog": 15391, "groupCod": "BUSTA", "categoryDesc": "Busta paga", "documentCod": "BUSTA", "flagMandatory": "Y", "mandatoryFor": "100;DATI-RDY", "flagRetroCancel": "N", "flagToPrint": null, "flagForce": null, "forced": null, "flagToValidate": "Y", "validated": null, "validatedDate": null, "flagFromChecklist": "Y", "flagToSign": null, "flagAcquireOriginal": "N", "acquired": "Y", "acquiredOriginal": null, "docAdmitted": [{"prog": 15391, "documentCod": "BUSTA", "documentDesc": "Busta paga"}], "lastUpload": {"prog": 15391, "documentCod": "BUSTA", "documentDesc": "Busta paga", "userId": "C316031", "uploadDate": 1493893058548, "notes": null, "repositoryId": "16630", "customDocDesc": null, "xpiToken": null}, "infoDcr": {"dcrDate": null, "dcrStatus": null, "dcrToken": null, "dcrNeeded": false}, "origin": null, "flagVisible": null, "dateCertNeeded": false}]}, {"prog": null, "entityType": "PRA", "ndg": "", "offerId": "", "heading": null, "offerDesc": null, "groups": [{"ndg": null, "prog": 15403, "groupCod": "DOCAGGCRO", "categoryDesc": "Documento aggiuntivo", "documentCod": null, "flagMandatory": "N", "mandatoryFor": "500;*", "flagRetroCancel": "N", "flagToPrint": null, "flagForce": null, "forced": null, "flagToValidate": "N", "validated": null, "validatedDate": null, "flagFromChecklist": "Y", "flagToSign": null, "flagAcquireOriginal": "N", "acquired": "Y", "acquiredOriginal": null, "docAdmitted": [{"prog": 15403, "documentCod": "AGGCRO", "documentDesc": "Documento aggiuntivo"}], "lastUpload": {"prog": 15403, "documentCod": "AGGCRO", "documentDesc": "Documento aggiuntivo", "userId": "C316031", "uploadDate": 1493893058548, "notes": null, "repositoryId": "16630", "customDocDesc": null, "xpiToken": null}, "infoDcr": {"dcrDate": null, "dcrStatus": null, "dcrToken": null, "dcrNeeded": false}, "origin": null, "flagVisible": null, "dateCertNeeded": false}, {"ndg": null, "prog": 15404, "groupCod": "CONTR", "categoryDesc": "<PERSON><PERSON><PERSON>", "documentCod": "CONTR", "flagMandatory": "N", "mandatoryFor": "100;DATI-RDY", "flagRetroCancel": "N", "flagToPrint": null, "flagForce": null, "forced": null, "flagToValidate": "N", "validated": null, "validatedDate": null, "flagFromChecklist": "Y", "flagToSign": null, "flagAcquireOriginal": "N", "acquired": "N", "acquiredOriginal": null, "docAdmitted": [{"prog": 15404, "documentCod": "CONTR", "documentDesc": "<PERSON><PERSON><PERSON>"}], "lastUpload": {"prog": 15404, "documentCod": "CONTR", "documentDesc": "<PERSON><PERSON><PERSON>", "userId": "C316031", "uploadDate": 1493893058548, "notes": null, "repositoryId": "16630", "customDocDesc": null, "xpiToken": null}, "infoDcr": {"dcrDate": null, "dcrStatus": null, "dcrToken": null, "dcrNeeded": false}, "origin": null, "flagVisible": null, "dateCertNeeded": false}, {"ndg": null, "prog": 15405, "groupCod": "GIA", "categoryDesc": "<PERSON><PERSON><PERSON>", "documentCod": "GIA", "flagMandatory": "N", "mandatoryFor": "100;DATI-RDY", "flagRetroCancel": "N", "flagToPrint": null, "flagForce": null, "forced": null, "flagToValidate": "N", "validated": null, "validatedDate": null, "flagFromChecklist": "Y", "flagToSign": null, "flagAcquireOriginal": "N", "acquired": "Y", "acquiredOriginal": null, "docAdmitted": [{"prog": 15405, "documentCod": "GIA", "documentDesc": "<PERSON><PERSON><PERSON>"}], "lastUpload": {"prog": 15405, "documentCod": "GIA", "documentDesc": "<PERSON><PERSON><PERSON>", "userId": "C316031", "uploadDate": 1493893058548, "notes": null, "repositoryId": "16630", "customDocDesc": null, "xpiToken": null}, "infoDcr": {"dcrDate": null, "dcrStatus": null, "dcrToken": null, "dcrNeeded": false}, "origin": null, "flagVisible": null, "dateCertNeeded": false}]}]