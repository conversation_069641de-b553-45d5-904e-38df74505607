import { Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { DomainService } from '../../../../../shared/domain/domain.service';
import { RegistryService } from '../../../../service/registry.service';

@Component({
  selector: 'app-driver-assignment',
  templateUrl: './driver-assignment.component.html',
  styleUrls: ['./driver-assignment.component.css']
})
export class DriverAssignmentComponent implements OnChanges {
  @Input() public idAnag: string;
  @Input() subjectType: string;

  public isEditable = false;
  public macroProcesses: any = [];
  public creditSegments: any = [];
  public goodTypes: any = [];
  public categoryTypes: any = [];
  public expiredCategoryTypes: any = [];
  public activeDomainCodes: any = [];
  public appraisalType: any = [];
  public regions: any = [];

  public pageContent: any[];
  public aRuleExists: boolean;

  public isOpen = false;

  constructor(
    private _domainService: DomainService,
    private _registryService: RegistryService
  ) { }

  ngOnChanges(changes: SimpleChanges) {
    if (changes && this.idAnag) {
      Observable.forkJoin(
        this._domainService.newGetDomain('UBZ_DOM_MACRO_PROCESS'),
        this._domainService.newGetDomain('UBZ_DOM_POS_SEGMENT'),
        this._domainService.newGetDomain('UBZ_DOM_RESITEM_TYPE'),
        this._domainService.newGetDomain('UBZ_DOM_CATEGORY_TYPE', 'IMM'),
        this._domainService.newGetDomain('UBZ_DOM_APPRAISAL_TYPE'),
        this._registryService.getRegionDomainForRegistry(),
        this._domainService.newGetDomain('UBZ_DOM_CATEGORY_TYPE', 'IMM', true)
      ).subscribe(res => {
        this.macroProcesses = res[0];
        this.creditSegments = res[1];
        this.goodTypes = res[2];
        this.categoryTypes = res[3];
        this.appraisalType = res[4];
        this.regions = res[5];
        this.expiredCategoryTypes = res[6];
        this.refreshPageContent();
      });
    }
  }

  private refreshPageContent(addIfNotExists?: boolean): void {
    addIfNotExists = addIfNotExists || false;
    this._registryService.getDriverAssignment(this.idAnag).subscribe(res => {
      this.pageContent = res;
      this.parseDates();
      if (addIfNotExists && this.pageContent.length === 0) {
        this.pageContent.push({});
      }
      this.aRuleExists = this.pageContent.length > 0;
      this.deleteCategoryIfExpired();

    });
  }

  deleteCategoryIfExpired() {
    this._domainService.transform(this.categoryTypes).forEach(cat => {
      this.activeDomainCodes.push(cat.domCode);
    });
    this.pageContent.forEach(element => {
      if (this.isEditable && (this.activeDomainCodes.indexOf(element.specificCategory) === -1)) {
        element.specificCategory = '';
      }
    });
  }

  private parseDates(): void {
    for (const admitted of this.pageContent) {
      if (admitted.startAbilitation && admitted.startAbilitation !== '') {
        admitted.startAbilitation = new Date(admitted.startAbilitation);
      }
      if (admitted.endAbilitation && admitted.endAbilitation !== '') {
        admitted.endAbilitation = new Date(admitted.endAbilitation);
      }
    }
  }

  modify(addIfNotExists: boolean) {
    this.refreshPageContent(addIfNotExists);
    this.isEditable = true;
    this.isOpen = true;
  }

  undo() {
    this.refreshPageContent();
    this.isEditable = false;
  }

  save() {
    this._registryService
      .saveDriverAssignment(this.idAnag, this.pageContent)
      .subscribe(x => {
        this.refreshPageContent();
        this.isEditable = false;
      });
  }

  public addNewRule(): void {
    this.pageContent.push({});
  }
}
