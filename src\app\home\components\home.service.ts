import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class HomeService {
  constructor(private http: Http) {}

  public getHomePage(): Observable<string> {
    const url = '/UBZ-ESA-RS/service/configuration/v1/dashboardForProfile';
    return this.http.get(url).map((resp: Response) => resp.text());
  }
}
