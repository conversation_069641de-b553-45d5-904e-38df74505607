<section id="page-content-wrapper">
  <div class="row step-navigation">
    <div class="col-sm-12 btn-set">
      <ng-container *ngIf="!filtersOpen">
        <button type="button" class="btn btn-empty waves-effect waves-secondary pull-right" (click)="openFilters()">
          <i class="icon-filter"></i> {{'UBZ.SITE_CONTENT.1100100011' | translate }}
        </button>
      </ng-container>
      <ng-container *ngIf="filtersOpen">
        <button type="button" class="btn btn-empty waves-effect waves-secondary pull-right" (click)="closeFilters()">
          <i class="fa fa-times" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.1100100101' | translate }}
        </button>
      </ng-container>
    </div>
  </div>

  <section class="row" *ngIf="filtersOpen">
    <!-- Filtri -->
    <!-- Status -->
    <div class="col-md-2 col-sm-12 form-group">
      <label>{{'UBZ.SITE_CONTENT.10000001' | translate }}</label>
      <div class="custom-select">
        <select class="form-control" [(ngModel)]="searchFilter.appPhaseCod" name="searchFilter.appPhaseCod">
          <option [ngValue]="null" hidden>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
          <option *ngFor="let state of apiAppraisalsStateList" [ngValue]="state.appPhaseCod">
            {{(statusTypeDomain && statusTypeDomain[state.appPhaseCod + state.appStatusCod]) ? (statusTypeDomain[state.appPhaseCod +
            state.appStatusCod].translationCod | translate) : '' }}
          </option>
        </select>
      </div>
    </div>
    <!-- Sub status -->
    <div class="col-md-2 col-sm-12 form-group">
      <label>{{'UBZ.SITE_CONTENT.10011101010' | translate }}</label>
      <div class="custom-select">
        <select class="form-control" [(ngModel)]="searchFilter.appSubStatusCod" name="searchFilter.appSubStatusCod">
          <option [ngValue]="null" hidden>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
          <option *ngFor="let subState of apiAppraisalsSubStateList" [ngValue]="subState">
            {{(subStatusTypeDomain && subStatusTypeDomain[subState]) ? (subStatusTypeDomain[subState].translationCod | translate) : ''
            }}
          </option>
        </select>
      </div>
    </div>
    <div class="col-md-4 col-md-offset-2 col-sm-12 form-group pull-right">
      <label></label>
      <div class="btn-set">
        <button class="btn btn-primary waves-effect pull-right" type="button" (click)="startFilter()">{{'UBZ.SITE_CONTENT.1100100110'
          | translate }}</button>
        <ng-container>
          <button *ngIf="(searchFilter.appPhaseCod || searchFilter.appSubStatusCod)" class="btn btn-secondary waves-effect pull-right"
            type="button" (click)="cleanSearchFilter()">{{'UBZ.SITE_CONTENT.1010010101' | translate }}</button>
        </ng-container>
      </div>
    </div>
  </section>

  <section class="row">
    <!-- Tabella -->
    <div class="col-sm-12">
      <table class="table table-hover" *ngIf="apiAppraisalsNumber > 0; else noRecordFound">
        <thead>
          <tr>
            <!-- Perizia status code -->
            <th scope="col">{{'UBZ.SITE_CONTENT.10000001' | translate }}</th>
            <!-- Perizia sub status code -->
            <th scope="col">{{'UBZ.SITE_CONTENT.10011101010' | translate }}</th>
            <!-- Data inserimento -->
            <th scope="col">{{'UBZ.SITE_CONTENT.1111110' | translate }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let appraisal of apiAppraisals">
            <td attr.data-label="{{'UBZ.SITE_CONTENT.10000001' | translate}}">
              {{(statusTypeDomain && statusTypeDomain[appraisal.appPhaseCod + appraisal.appStatusCod]) ? (statusTypeDomain[appraisal.appPhaseCod
              + appraisal.appStatusCod].translationCod | translate) : '' }}
            </td>
            <td attr.data-label="{{'UBZ.SITE_CONTENT.10011101010' | translate}}">
              {{(subStatusTypeDomain && subStatusTypeDomain[appraisal.appSubStatusCod]) ? (subStatusTypeDomain[appraisal.appSubStatusCod].translationCod
              | translate) : '' }}
            </td>
            <td attr.data-label="{{'UBZ.SITE_CONTENT.1111110' | translate}}">
              {{appraisal.insertDate ? ((appraisal.insertDate | date: 'dd-MM-y') + ', ' + (appraisal.insertDate | customTime)) : ''}}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <ng-template #noRecordFound>
      <div class="Search__NoResults">
        <div class="Search__NoResults__Icon">
          <i class="icon-placeholder_note"></i>
        </div>
        <div class="Search__NoResults__Text">
          <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1001011001' | translate }}</h3>
          <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011010' | translate }}</p>
        </div>
      </div>
    </ng-template>
    <ng-container *ngIf="apiAppraisalsNumber > 10">
      <div class="col-sm-6">
        <div class="results">
          <span>{{'UBZ.SITE_CONTENT.1111111' | translate }}</span>
          <div class="custom-select">
            <select class="form-control" [(ngModel)]="pageSize" (ngModelChange)="pageSizeChanged()">
              <option [ngValue]="10" selected>10 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{apiAppraisalsNumber}}</option>
              <option [ngValue]="20" *ngIf="apiAppraisalsNumber > 20">20 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{apiAppraisalsNumber}}</option>
              <option [ngValue]="30" *ngIf="apiAppraisalsNumber > 30">30 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{apiAppraisalsNumber}}</option>
              <option [ngValue]="40" *ngIf="apiAppraisalsNumber > 40">40 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{apiAppraisalsNumber}}</option>
              <option [ngValue]="50" *ngIf="apiAppraisalsNumber > 50">50 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{apiAppraisalsNumber}}</option>
              <option value="{{apiAppraisalsNumber}}">{{apiAppraisalsNumber}} {{'UBZ.SITE_CONTENT.10000000' | translate }}
                {{apiAppraisalsNumber}}
              </option>
            </select>
          </div>
        </div>
      </div>
      <div class="col-sm-6" class="pull-right">
        <pagination #paginator [boundaryLinks]="true" [directionLinks]="false" [totalItems]="apiAppraisalsNumber" [ngModel]="pageNumber"
          [itemsPerPage]="pageSize" (pageChanged)="changePage($event)" class="pagination" previousText="&lsaquo;" nextText="&rsaquo;"
          firstText="&laquo;" lastText="&raquo;"></pagination>
      </div>
    </ng-container>
  </section>
</section>
