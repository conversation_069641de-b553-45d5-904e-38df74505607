{"partialSaveData": "{\"1016\":{\"FD_NAV_IT\":null,\"FD_NAV_IC\":null,\"FD_INS_END\":null,\"FD_INS_AMN\":null}}", "afl": [{"applicationForm": "1016", "sections": [{"formCode": "1016", "formDesc": "MS-Gar-Assicurazione", "sectionCode": "1996", "sectionDesc": "Assicurazione", "rows": [{"maxColumns": 6, "fields": [{"span": 3, "code": "FD_NAV_IT", "description": "Tipo assicurazione", "disabled": false, "domainCod": null, "label": "Tipo assicurazione", "mandatory": "N", "pattern": null, "readOnlyInSection": false, "readOnly": false, "renderIf": "true", "styleClass": "col-sm-3", "tooltip": "FD_NAV_IT[MS-Gar-Assicurazione- FORM_ID:1016 SEC_ID:1996]", "type": "Text", "usage": null, "value": null, "xpath": null, "placeholder": null, "showLabel": true, "fieldValue": null, "validationRule": null, "required": "true", "parentFieldCod": null, "parentFieldValue": null, "precision": 200, "hide": false}, {"span": 3, "code": "FD_NAV_IC", "description": "Certificato assicurazione (numerico)", "disabled": false, "domainCod": null, "label": "Certificato assicurazione (numerico)", "mandatory": "N", "pattern": "^[0-9]+$", "readOnlyInSection": false, "readOnly": false, "renderIf": "true", "styleClass": "col-sm-3", "tooltip": "FD_NAV_IC[MS-Gar-Assicurazione- FORM_ID:1016 SEC_ID:1996]", "type": "Number", "usage": null, "value": null, "xpath": null, "placeholder": null, "showLabel": true, "fieldValue": null, "validationRule": null, "required": "true", "parentFieldCod": null, "parentFieldValue": null, "precision": 0, "scale": 0, "minStep": 1, "maxValue": null, "hide": false}, {"span": 3, "code": "FD_INS_END", "description": "Data scadenza assicurazione", "disabled": false, "domainCod": null, "label": "Data scadenza assicurazione", "mandatory": "N", "pattern": null, "readOnlyInSection": false, "readOnly": false, "renderIf": "true", "styleClass": "col-sm-3", "tooltip": "FD_INS_END[MS-Gar-Assicurazione- FORM_ID:1016 SEC_ID:1996]", "type": "Timestamp", "usage": null, "value": null, "xpath": null, "placeholder": null, "showLabel": true, "fieldValue": null, "validationRule": null, "required": "true", "parentFieldCod": null, "parentFieldValue": null, "precision": 0, "hide": false}, {"span": 3, "code": "FD_INS_AMN", "description": "Importo assicurazione", "disabled": false, "domainCod": null, "label": "Importo assicurazione", "mandatory": "N", "pattern": "^[0-9]{1,11}(\\.[0-9]{0,2})?$", "readOnlyInSection": false, "readOnly": false, "renderIf": "true", "styleClass": "col-sm-3", "tooltip": "FD_INS_AMN[MS-Gar-Assicurazione- FORM_ID:1016 SEC_ID:1996]", "type": "Number", "usage": null, "value": null, "xpath": null, "placeholder": null, "showLabel": true, "fieldValue": null, "validationRule": null, "required": "true", "parentFieldCod": null, "parentFieldValue": null, "precision": 13, "scale": 2, "minStep": 0.01, "maxValue": 99999999999.99, "hide": false}]}], "collapsable": true, "startCollapsed": false, "visible": true}], "error": false, "messages": []}]}