import {
  Component,
  OnInit,
  ViewChild,
  Input,
  Inject,
  Output,
  EventEmitter,
} from "@angular/core";
import { ActivatedRoute, Router, Params } from "@angular/router";
import { NgForm } from "@angular/forms";
import { Observable } from "rxjs/Observable";
import { TranslateService } from "@ngx-translate/core";
// Models
import { SendAppraisalModel } from "../../model/send-appraisal.models";
import { ExpertSearchModel } from "../../model/expert";
import { Appointment } from "../../model/appointment";
import { LockUnlockTask } from "../../../shared/access-rights/model/lock-unlock-task";
import { Parameter } from "../../../shared/access-rights/model/parameter";
import { IAppConstants, APP_CONSTANTS } from "../../../app.constants";
// Services
import { GenericTaskService } from "../../services/generic-task/generic-task.service";
import { AccessRightsService } from "../../../shared/access-rights/services/access-rights.service";
import { PositionService } from "../../../shared/position/position.service";
import { MessageService } from "../../../shared/messages/services/message.service";
import { UserDataService } from "../../../shared/user-data/user-data.service";
import { UserData } from "../../../shared/user-data/user-data";

class TemplateChosenExpert {
  id: string;
  name: string;
  surname: string;
  type: string;
  ndg: string;
}
@Component({
  selector: "app-send-appraisal",
  templateUrl: "./send-appraisal.component.html",
  styleUrls: ["./send-appraisal.component.css"],
})
export class SendAppraisalComponent implements OnInit {
  @Input()
  positionId: string;
  @Input()
  isForTemplate = false;
  @Input()
  socId;
  @Output()
  expertChosen: EventEmitter<TemplateChosenExpert> =
    new EventEmitter<TemplateChosenExpert>();
  @Output()
  experts: EventEmitter<any> = new EventEmitter<any>();
  taskId: string;
  taskCod: string;
  idSocPer: string;
  orePermesse: string[] = [
    "08",
    "09",
    "10",
    "11",
    "12",
    "13",
    "14",
    "15",
    "16",
    "17",
    "18",
    "19",
    "20",
  ];
  minPermessi: string[] = ["00", "15", "30", "45"];
  @ViewChild(NgForm)
  public form: NgForm;
  model: SendAppraisalModel = new SendAppraisalModel();
  searchType: string;
  isModalOpen = false;
  isTaskLocked = false;
  today: Date = new Date();
  dataMinSopralluogo: Date = new Date();
  readonly maxValidDate: Date = new Date(3000, 12, 31);
  expertId: string;
  reviserId: string;
  spExpertId: string;
  spRevisionId: string;
  spExpertDesc: string;
  spRevisionDesc: string;
  // CR Trasparenza
  showFreeAppraisalCheck: boolean = true;
  showBillingForm: boolean = true;
  freeAppraisalCheck: boolean;
  billingFormRequired: boolean;
  billingAmount: number;
  billingDate: Date;
  transparencePositionId: string = "";
  isTransparencyDisabled = false;
  isTemplateUpdate: boolean;
  secondfreeAppraisalCheck: boolean;
  showSecondBillingForm: boolean = true;
  //
  billingAmountSecond: number;
  billingDateSecond: Date;
  secondBillingFormRequired: boolean;
  isSecondBillingForm: boolean = true;

  constructor(
    private _activatedRoute: ActivatedRoute,
    private _router: Router,
    private _expertAssignmentService: GenericTaskService,
    private _accessRightsService: AccessRightsService,
    private _userDataService: UserDataService,
    private _positionService: PositionService,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    @Inject(APP_CONSTANTS) readonly constants: IAppConstants
  ) {}

  ngOnInit() {
    this.setDataMinSopralluogo();
    this._activatedRoute.params
      .switchMap((params: Params) => {
        this.taskId = params["taskId"];
        this.taskCod = params["taskCod"];
        return this._positionService.getAppraisalAssignment(this.positionId);
      })
      .subscribe((res: any) => {
        this.idSocPer = res.socId;
        this.expertId = res.expertId;
        this.reviserId = res.reviserId;
        this.spExpertId = res.spExpertId;
        this.spRevisionId = res.spRevisionId;
        this.spExpertDesc = res.spExpertDesc;
        this.spRevisionDesc = res.spRevisionDesc;
        if (res.customerContactDate) {
          this.model.dataContatto = new Date(res.customerContactDate);
          this.dataMinSopralluogo = this.model.dataContatto;
          this.model.oraContatto = this.model.dataContatto.getHours();
          this.model.minContatto = this.model.dataContatto.getMinutes();
        }
        if (res.expectedSurveyDate) {
          this.model.dataSopralluogo = new Date(res.expectedSurveyDate);
          this.model.oraSopralluogo = this.model.dataSopralluogo.getHours();
          this.model.minSopralluogo = this.model.dataSopralluogo.getMinutes();
        }
        this.writeInfo();
      });
    if (this.isForTemplate) {
      // Subscribe sull'observable contenente le informazioni della perizia
      // Se la perizia è a carico del cliente i campi di fatturazione sono obbligatori, facoltativi altrimenti
      this._positionService
        .getAppraisalInfo(this.positionId)
        .subscribe((appraisalInfo) => {
          // Check if is template update (template di aggiornamento perizia)
          this.isTemplateUpdate =
            appraisalInfo.templateType && appraisalInfo.templateType === "AGL";
          //il template di aggiornamento (isTemplateUpdate) non mostra la trasparenza e mantiene la perizia carico banca (gratuita)
          if (
            appraisalInfo &&
            appraisalInfo.appraisalOwner === "CLI" &&
            !this.isTemplateUpdate
          ) {
            this.billingFormRequired = true;
            this.secondBillingFormRequired = true;
            this.showFreeAppraisalCheck = true;
          } else {
            this.billingFormRequired = false;
            this.secondBillingFormRequired = false;
            this.showFreeAppraisalCheck = false;
          }
          //gestione trasparenza
          //se internal second opinion mostro i dati trasparenza
          //della prima opinion e poi non li salvo
          //negli altri casi mostro i campi trasparenza e li salvo con gli altri
          if (
            appraisalInfo["opinionType"] === "SO" &&
            appraisalInfo["internalAgent"]
          ) {
            this.transparencePositionId = appraisalInfo.firstOpinionId;
            this.isTransparencyDisabled = true;
          } else {
            this.transparencePositionId = this.positionId;
            this.isTransparencyDisabled = false;
          }
          //il template di aggiornamento (isTemplateUpdate) non mostra la trasparenza e mantiene la perizia carico banca (gratuita)
          if (this.isTemplateUpdate) {
            this.transparencePositionId = this.positionId;
            this.isTransparencyDisabled = true;
            this.isSecondBillingForm = false;
            this.showBillingForm = false;
            this.showSecondBillingForm = false;
            this.freeAppraisalCheck = true;
            this.writeInfo();
          } else {
            this._expertAssignmentService
              .getBillingData(this.transparencePositionId)
              .subscribe((response) => {
                if (response) {
                  this.billingAmount = response.billingAmount;
                  this.billingAmountSecond = response.billingAmountSecond;
                  this.billingDate =
                    response.billingAmountDate === null
                      ? null
                      : new Date(response.billingAmountDate);
                  this.billingDateSecond =
                    response.billingDateSecond === null
                      ? null
                      : new Date(response.billingDateSecond);
                  this.freeAppraisalCheck =
                    response.gratisFlag === "Y" ? true : false;
                  if (this.freeAppraisalCheck) {
                    this.showBillingForm = false;
                  } else {
                    this.showBillingForm = true;
                  }

                  this.secondfreeAppraisalCheck =
                    response.flagSecondSurvey === "Y" ? true : false;

                  if (this.secondfreeAppraisalCheck) {
                    this.showSecondBillingForm = false;
                  } else {
                    this.showSecondBillingForm = true;
                  }
                  this.writeInfo();
                }
              });
          }
        });
    }
  }

  private setDataMinSopralluogo() {
    this.dataMinSopralluogo.setTime(
      this.model.dataContatto
        ? this.goBehindADay(this.model.dataContatto)
        : this.goBehindADay(new Date())
    );
  }

  private goBehindADay(data: Date): number {
    return data.getTime() - this.constants.MILLISECONDS_IN_A_DAY;
  }

  public getData(): SendAppraisalModel {
    return this.model;
  }

  closeModal() {
    this.isModalOpen = false;
  }
  /**
   * @function
   * @name writeInfo
   * @description Emette i valori inseriti verso il componente padre.
   * Se i campi trasparenza sono presenti e il flag 'Perizia gratuita' è checkato, si inviano valori null per data e importo fattura.
   * In questo modo i valori rimangono presenti a video nel caso in cui l'utente modifichi nuovamente il valore del flag
   */
  writeInfo() {
    this.experts.emit({
      positionId: this.positionId,
      expertSocId: this.idSocPer,
      expertId: this.expertId,
      reviserId: this.reviserId,
      spExpertId: this.spExpertId,
      spRevisionId: this.spRevisionId,
      spExpertDesc: this.spExpertDesc,
      spRevisionDesc: this.spRevisionDesc,
      billingAmount: this.freeAppraisalCheck ? null : this.billingAmount,
      freeAppraisalCheck: this.freeAppraisalCheck ? "Y" : "N",
      billingDate: this.freeAppraisalCheck ? null : this.billingDate,
      secondfreeAppraisalCheck: this.secondfreeAppraisalCheck ? "Y" : "N",
      billingAmountSecond: this.secondfreeAppraisalCheck ? this.billingAmountSecond : null,
      billingDateSecond: this.secondfreeAppraisalCheck ? this.billingDateSecond : null,
    });
    
  }

  saveInDraft() {
    const appointments: Appointment[] = [];
    let oraSopralluogo: Date;
    if (
      this.model.dataSopralluogo &&
      this.model.oraSopralluogo !== null &&
      this.model.minSopralluogo !== null
    ) {
      oraSopralluogo = this.parseDate(
        this.model.dataSopralluogo.getTime(),
        this.model.oraSopralluogo,
        this.model.minSopralluogo
      );
      appointments.push(
        new Appointment("SUR", this.model.dataSopralluogo, oraSopralluogo)
      );
    }
    let oraContatto: Date;
    if (
      this.model.dataContatto &&
      this.model.oraContatto !== null &&
      this.model.minContatto !== null
    ) {
      oraContatto = this.parseDate(
        this.model.dataContatto.getTime(),
        this.model.oraContatto,
        this.model.minContatto
      );
      appointments.push(
        new Appointment("CON", this.model.dataContatto, oraContatto)
      );
    }
    if (appointments.length > 0) {
      this._expertAssignmentService
        .setAppointment(this.positionId, appointments)
        .subscribe((res) => {});
    }
    this._expertAssignmentService
      .newAssigneExpert(
        this.positionId,
        this.idSocPer,
        this.expertId,
        this.reviserId,
        this.spExpertId,
        this.spRevisionId,
        this.spExpertDesc,
        this.spRevisionDesc
      )
      .subscribe((res) => {});
  }

  private parseDate(time: number, hour: number, minutes: number): Date {
    const data: Date = new Date(time);
    data.setHours(hour);
    data.setMinutes(minutes);
    return data;
  }

  private datesAreValid(oraContatto: Date, oraSopralluogo: Date) {
    return oraContatto.getTime() < oraSopralluogo.getTime();
  }

  saveAppointment() {
    const oraSopralluogo = this.parseDate(
      this.model.dataSopralluogo.getTime(),
      this.model.oraSopralluogo,
      this.model.minSopralluogo
    );
    const oraContatto = this.parseDate(
      this.model.dataContatto.getTime(),
      this.model.oraContatto,
      this.model.minContatto
    );
    if (!this.datesAreValid(oraContatto, oraSopralluogo)) {
      this._messageService.showError(
        this._translateService.instant("UBZ.SITE_CONTENT.1010111011"),
        this._translateService.instant("UBZ.SITE_CONTENT.1010111100")
      );
      return;
    }
    this.saveExpert()
      .switchMap(() => {
        const appointments: Appointment[] = [];
        appointments.push(
          new Appointment("SUR", this.model.dataSopralluogo, oraSopralluogo)
        );
        appointments.push(
          new Appointment("CON", this.model.dataContatto, oraContatto)
        );
        return this._expertAssignmentService.setAppointment(
          this.positionId,
          appointments
        );
      })
      .switchMap(() => {
        const lockUnlockObj = new LockUnlockTask(this.taskId, this.taskCod);
        lockUnlockObj.eventCode = "UBZ_PRZ_OK";
        lockUnlockObj.parametersList.push(
          new Parameter(
            "UBZ_FIXAPPOINTMENT_appointmentDate",
            this.formatDate(oraSopralluogo)
          )
        );
        return this._accessRightsService.closeTask(lockUnlockObj);
      })
      .subscribe(() => {
        this._messageService.showSuccess(
          this._translateService.instant("UBZ.SITE_CONTENT.1001100000"),
          this._translateService.instant("UBZ.SITE_CONTENT.1001100001")
        );
        this._router.navigateByUrl("dashboard/LAA");
      });
  }

  saveExpert(): Observable<any> {
    return this._expertAssignmentService.newAssigneExpert(
      this.positionId,
      this.idSocPer,
      this.expertId,
      this.reviserId,
      this.spExpertId,
      this.spRevisionId,
      this.spExpertDesc,
      this.spRevisionDesc
    );
  }

  buttonIsEnable() {
    if (!this.form) {
      return false;
    }
    return this.form.valid;
  }

  getName(expert: ExpertSearchModel): string {
    if (!expert) {
      return "";
    }
    let name = "";
    name = expert.firstName ? expert.firstName : "";
    name += expert.lastname ? " " + expert.lastname : "";
    return name;
  }

  taskLocked() {
    this.isTaskLocked = true;
    this._userDataService.getAll().subscribe((res: UserData) => {
      this._expertAssignmentService.taskLockingUser = res.username;
    });
  }

  tasklockedByOtherUser(user: string) {
    this._expertAssignmentService.taskLockingUser = user;
    this.isTaskLocked = false;
  }

  taskUnlocked() {
    this._expertAssignmentService.taskLockingUser = undefined;
    this.isTaskLocked = false;
  }

  private formatDate(date: Date): string {
    let day = `${date.getDate()}`;
    if (day.length === 1) {
      day = `0${day}`;
    }
    let month = `${date.getMonth() + 1}`;
    if (month.length === 1) {
      month = `0${month}`;
    }
    let hours = `${date.getHours()}`;
    if (hours.length === 1) {
      hours = `0${hours}`;
    }
    let minutes = `${date.getMinutes()}`;
    if (minutes.length === 1) {
      minutes = `0${minutes}`;
    }
    let seconds = `${date.getSeconds()}`;
    if (seconds.length === 1) {
      seconds = `0${seconds}`;
    }
    return `${date.getFullYear()}-${month}-${day}T${hours}:${minutes}:${seconds}`;
  }

  noChars(event) {
    event.preventDefault();
  }
  /**
   * @function
   * @name setBillingFormRequired
   * @description Sul flag della checkbox freeAppraisalCheck imposta il required dei campi importo e data fatturazione
   */
  setBillingFormRequired() {
    this.billingFormRequired = !this.freeAppraisalCheck;
    this.showBillingForm = !this.freeAppraisalCheck;
    this.writeInfo();
  }
  setBillingSecoundFormRequired() {
    this.secondBillingFormRequired = !this.secondfreeAppraisalCheck;
    this.showSecondBillingForm = !this.secondfreeAppraisalCheck;
    this.writeInfo();
  }
}
