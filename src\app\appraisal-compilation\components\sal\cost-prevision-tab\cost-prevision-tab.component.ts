import {
  Component,
  OnInit,
  ViewChild,
  OnChanges,
  Input,
  Inject,
} from "@angular/core";
import { NgForm } from "@angular/forms";
import { Observable } from "rxjs/Observable";
import { APP_CONSTANTS, IAppConstants } from "../../../../app.constants";
import {
  CostPrevisionTableModel,
  SALComplessivoModel,
} from "../model/sal.models";
import { DomainService } from "../../../../shared/domain/domain.service";
import { Domain } from "../../../../shared/domain/domain";
import { AccordionAPFService } from "../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service";
import { TableResults } from "./model/results";
import { Subject } from "../../../../../../node_modules/rxjs";

@Component({
  selector: "app-cost-prevision-tab",
  templateUrl: "./cost-prevision-tab.component.html",
  styleUrls: ["./cost-prevision-tab.component.css"],
})
export class CostPrevisionTabComponent implements OnInit, OnChanges {
  @ViewChild(NgForm) form: NgForm;
  @Input() salComplessivo: SALComplessivoModel;
  @Input() costiDiRealizzazione: CostPrevisionTableModel[];
  @Input() appraisalType: string;
  @Input() mobileAppraisalType: string;
  @Input() disable: boolean;
  minPlanningEndDate: Date = new Date();
  maxDate = new Date(3000, 12, 31);
  tableResults: TableResults = new TableResults();
  expenseTypes: Domain[] = [];
  buildSiteRecapCosts: Domain[] = [];
  planningProcesses: Domain[] = [];
  suspensionReasons: Domain[] = [];
  projectCorrespondences: Domain[] = [];
  $refreshCalendar: Subject<boolean> = new Subject<boolean>();
  private TotalCostTypeDomCode = "UBZ.EXPENSE_TYPE.TOT";
  expectedCostTotal = 0;
  executedCostTotal = 0;
  isInitComplete = false;

  constructor(
    @Inject(APP_CONSTANTS) public constants: IAppConstants,
    private _domainService: DomainService,
    public _accordionAPFService: AccordionAPFService
  ) {}

  ngOnInit() {
    Observable.forkJoin(
      this._domainService.newGetDomain("UBZ_DOM_EXPENSE_TYPE"),
      this._domainService.newGetDomain("UBZ_DOM_BUILD_SITE_RECAP_COSTS"),
      this._domainService.newGetDomain("UBZ_DOM_PLANNING_PROCESS"),
      this._domainService.newGetDomain("UBZ_DOM_REASON_TYPE", "SLV"),
      this._domainService.newGetDomain("UBZ_DOM_PROJECT_CORR")
    ).subscribe((res) => {
      this.expenseTypes = res[0];
      this.buildSiteRecapCosts = res[1];
      this.planningProcesses = res[2];
      this.suspensionReasons = res[3];
      this.projectCorrespondences = res[4];
    });
    this.setMinPlanningEndDate();
  }

  ngOnChanges() {
    this.setMinPlanningEndDate();
    this.calculateTotals();
  }

  setMinPlanningEndDate() {
    // Se l'accordion è disabilitato si modifica la data minima per rendere il campo valido e avere il "pallozzo" verde
    if (this.disable === true) {
      this.minPlanningEndDate = new Date(1970, 1, 1);
      this.$refreshCalendar.next(false);
    } else {
      this.minPlanningEndDate = new Date();
      this.$refreshCalendar.next(true);
    }
  }

  totalsInit() {
    let doUpdate = false;
    for (const cost of this.costiDiRealizzazione) {
      cost.expectedTotCost = Number.parseFloat(cost.expectedTotCost.toFixed(2));
      cost.totExecutedCost = Number.parseFloat(cost.totExecutedCost.toFixed(2));
      if (cost.costType === this.TotalCostTypeDomCode) {
        this.expectedCostTotal = Number.parseFloat(
          cost.expectedTotCost.toFixed(2)
        );
        this.executedCostTotal = Number.parseFloat(
          cost.totExecutedCost.toFixed(2)
        );
        if (this.expectedCostTotal === 0 || this.executedCostTotal === 0) {
          doUpdate = true;
        }
      }
    }
    this.isInitComplete = true;
    if (doUpdate) {
      setTimeout(() => this.updateTotals(), 30);
    }
  }

  isValid(): boolean {
    if (!this.form) {
      return false;
    }
    return this.form.valid;
  }

  // legacy, not used
  getEstimatedSup(): number {
    let total = 0;
    for (const model of this.costiDiRealizzazione) {
      if (model.costType !== this.TotalCostTypeDomCode) {
        total += model.expectedMqMc;
      }
    }
    if (this.tableResults) {
      this.tableResults.expectedMqMc = total;
    }
    return total;
  }

  // legacy, not used
  getExecutedSup(): number {
    let total = 0;
    for (const model of this.costiDiRealizzazione) {
      if (model.costType !== this.TotalCostTypeDomCode) {
        total += model.executedMqMc;
      }
    }
    if (this.tableResults) {
      this.tableResults.executedMqMc = total;
    }
    return total;
  }

  // TOTALS (1of2)
  calculateTotEstimatedCost() {
    let total = 0;
    let targetItem: any;
    for (const item of this.costiDiRealizzazione) {
      if (
        item &&
        item.costType &&
        (item.costType.includes("CCR") || item.costType.includes("CCF"))
      ) {
        total += item.expectedTotCost;
      } else if (item && item.expectedMqMc && item.expectedCostMqMc) {
        total += item.expectedMqMc * item.expectedCostMqMc;
      }
      if (
        item &&
        item.costType &&
        item.costType === this.TotalCostTypeDomCode
      ) {
        targetItem = item;
      }
    }
    if (targetItem) {
      targetItem.expectedTotCost = total;
    }

    this.expectedCostTotal = total;
  }

  // TOTALS (2of2)
  calculateTotExecutedCost() {
    let total = 0;
    let targetItem: any;
    for (const item of this.costiDiRealizzazione) {
      if (
        item &&
        item.costType &&
        (item.costType.includes("CCR") || item.costType.includes("CCF"))
      ) {
        total += item.totExecutedCost;
      } else if (item && item.executedMqMc && item.executedCostMqMc) {
        total += item.executedMqMc * item.executedCostMqMc;
      }
      if (
        item &&
        item.costType &&
        item.costType === this.TotalCostTypeDomCode
      ) {
        targetItem = item;
      }
    }
    if (targetItem) {
      targetItem.totExecutedCost = total;
    }

    this.executedCostTotal = total;
  }

  // Cost single item (1of2)
  getExpectedTotCost(index: number): number {
    const item = this.costiDiRealizzazione[index];
    if (item && item.expectedMqMc && item.expectedCostMqMc) {
      item.expectedTotCost = item.expectedMqMc * item.expectedCostMqMc;
      return item.expectedTotCost;
    } else {
      item.expectedTotCost = 0;
      return null;
    }
  }

  // Cost single item (2of2)
  getTotExecutedCost(index: number): number {
    const item = this.costiDiRealizzazione[index];
    if (item && item.executedMqMc && item.executedCostMqMc) {
      item.totExecutedCost = item.executedMqMc * item.executedCostMqMc;
      return item.totExecutedCost;
    } else {
      item.totExecutedCost = 0;
      return null;
    }
  }

  updateTotals() {
    if (this.isInitComplete) {
      this.calculateTotals();
    }
  }

  calculateTotals() {
    this.calculateTotEstimatedCost();
    this.calculateTotExecutedCost();
  }

  // legacy, not used
  refreshCostiDiRealizzazione(): void {
    for (const item of this.costiDiRealizzazione) {
      if (item.costType === this.TotalCostTypeDomCode) {
        item.executedCostMqMc = this.tableResults.executedCostMqMc;
        item.executedMqMc = this.tableResults.executedMqMc;
        item.expectedCostMqMc = this.tableResults.expectedCostMqMc;
        item.expectedMqMc = this.tableResults.expectedMqMc;
        item.expectedTotCost = this.tableResults.expectedTotCost;
        item.totExecutedCost = this.tableResults.totExecutedCost;
        return;
      }
    }
  }
}
