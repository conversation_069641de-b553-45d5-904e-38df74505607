<form #f="ngForm">
  <accordion-group #group class="panel" [ngClass]="{'empty-accordion': !aProvinceExists}" [isOpen]="isEditable" [isDisabled]="!aProvinceExists">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          {{'UBZ.SITE_CONTENT.1011101011' | translate }}
          <div class="accordion-button">
            <ng-container *appAuthKey="'UBZ_REGISTRY.COMPETENCE_PROVINCES_OPEN_NEW'">
              <button *ngIf="!isEditable && !aProvinceExists" type="button" class="btn btn-empty" (click)="startEdit($event)">
                <i class="fa icon-add"></i>{{'UBZ.SITE_CONTENT.111100001' | translate }}
              </button>
            </ng-container>
            <ng-container *appAuthKey="'UBZ_REGISTRY.COMPETENCE_PROVINCES_MODIFY'">
              <button *ngIf="!isEditable && aProvinceExists" type="button" class="btn btn-empty" (click)="startEdit($event)">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.11111' | translate }}
              </button>
            </ng-container>
            <ng-container *ngIf="isEditable">
              <button *appAuthKey="'UBZ_REGISTRY.COMPETENCE_PROVINCES_CANCEL'" type="button" class="btn btn-empty" (click)="stopEdit($event)">
                <i class="fa fa-times"></i>{{'UBZ.SITE_CONTENT.100000' | translate }}
              </button>
              <button *appAuthKey="'UBZ_REGISTRY.COMPETENCE_PROVINCES_SAVE'" type="button" class="btn btn-empty" (click)="save($event)" [disabled]="f.invalid">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.100001' | translate }}
              </button>
            </ng-container>
          </div>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <form #f="ngForm">
            <ng-container *ngIf="isEditable; else only_visualization">
            <div class="row" *ngFor="let prov of provinces; let index = index">
              <div class="col-sm-12 col-md-3 form-group">
                <label>{{'UBZ.SITE_CONTENT.1011101100' | translate }}*</label>
                <div class="custom-select">
                  <select  class="form-control" name="province-{{index}}" [(ngModel)]="prov.provinceName" required>
                    <option value="" disabled selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                     <option *ngFor="let dom of ( availableProvinces | domainMapToDomainArray )" value="{{ dom.domCode }}">{{ dom.translationCod | translate }}</option> -->
                   </select>
                 </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12 btn-set">
                <button *appAuthKey="'UBZ_REGISTRY.COMPETENCE_PROVINCES_ADD_NEW'" class="btn btn-primary waves-effect pull-right" [disabled]="f.invalid" type="button" (click)="addCompetenceProvince()">{{'UBZ.SITE_CONTENT.1011101101' | translate }}</button>
              </div>
            </div>
          </ng-container>
          </form>
        </div>
      </div>
    </div>
  </accordion-group>
</form>

<ng-template #only_visualization>
  <div class="row" *ngFor="let prov of provinces; let index = index">
    <div class="col-sm-12 col-md-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.1011101100' | translate }}</label>
      <span>{{ availableProvinces && availableProvinces[prov.provinceName] ? (availableProvinces[prov.provinceName].translationCod | translate) : '' }}</span>
    </div>
  </div>
</ng-template>
