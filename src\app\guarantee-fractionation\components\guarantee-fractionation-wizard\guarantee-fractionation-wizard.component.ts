import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-guarantee-fractionation-wizard',
  templateUrl: './guarantee-fractionation-wizard.component.html',
  styleUrls: ['./guarantee-fractionation-wizard.component.css']
})
export class GuaranteeFractionationWizardComponent implements OnInit {
  _stepNum: number; // indicatore dello step attuale del wizard, contatore parte da 1
  _stepActive: number; // indicatore dello step attuale del wizard, contatore parte da 1
  @Input('stepNum')
  set stepNum(value: number) {
      this._stepNum = value;
  }
  @Input('stepActive')
  set stepActive(value: number) {
      this._stepActive = value;
  }
  @Input() ndg: number;
  @Input() familyAsset: string;
  @ViewChild('modal') modal;
  modalIsOpen: boolean;
  selectedRoute: string;

  labels = [
    'UBZ.SITE_CONTENT.10010110000',
    'UBZ.SITE_CONTENT.10010110001',
    'UBZ.SITE_CONTENT.10010110010',
    'UBZ.SITE_CONTENT.10010110011'
  ];

  constructor(private router: Router) {}

  ngOnInit() {}

  /**
   * @name clickOnCounter
   * @param index - Indice del wizard sul quale di vuole navigare
   */
  clickOnCounter(index: number) {
    if (this._stepActive >= index && this._stepActive < 4) {
      switch (index) {
        case 1:
          // this.openModal();
          this.router.navigateByUrl(`guaranteeFractionation/${this.familyAsset}/${this.ndg}/guarantee-info`);
          this.selectedRoute = null;
          break;
        case 2:
          // this.openModal();
          this.router.navigateByUrl(`guaranteeFractionation/${this.familyAsset}/${this.ndg}/asset-choice`);
          this.selectedRoute = null;
          break;
        case 3:
            // this.openModal();
            this.router.navigateByUrl(`guaranteeFractionation/${this.familyAsset}/${this.ndg}/guarantee-on-arrival`);
            this.selectedRoute = null;
            break;
        case 4:
            // this.openModal();
            this.router.navigateByUrl(`guaranteeFractionation/${this.familyAsset}/${this.ndg}/summary`);
            this.selectedRoute = null;
            break;
      }
    }
  }

  private openModal(selectedRoute: string) {
    this.modalIsOpen = true;
    this.selectedRoute = selectedRoute;
  }

  closeModal() {
    this.modalIsOpen = false;
    this.selectedRoute = null;
  }

  confirmUndo() {
    this.modalIsOpen = false;
    this.router.navigateByUrl(this.selectedRoute);
    this.selectedRoute = null;
  }
}
