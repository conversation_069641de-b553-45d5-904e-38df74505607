.container {
    display: flex;
    align-items: center;
    max-width: 88%;
    padding: 0;
}
.societies-array {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
}
form {
    max-width: 90%;
}
.info-labels {
    display:flex;
    flex-direction: column;
    justify-content: center;
    text-align: center;
    align-self: flex-start;
    margin-top: 100px;
}

/* Buttons */

.rollback-buttons {
    display: flex;
    justify-content: flex-end;
}
.btn#addSocietyBtn {
    align-self: flex-start;
}
.btn#undo, #restore {
    margin: 2rem;
}


