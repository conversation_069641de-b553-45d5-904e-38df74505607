<div *ngIf="isOpen" class="modal fade" id="modal-differenze" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #addInstallation="bs-modal" (onHidden)="closeModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.1001101000' | translate }}</h2>
        <button type="button" class="close pull-right" aria-label="Close" (click)="closeModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <form #f="ngForm">
        <p>{{'UBZ.SITE_CONTENT.1001101001' | translate }}</p>
        <div class="radio-buttons">
          <div class="custom-radio">
            <input type="radio" name="a1" id="l1" class="radio" value="{{differences['firstOpinionValue']}}" [(ngModel)]="selectedValue">
            <label for="l1">{{differences['firstOpinionValue']}}</label>
          </div>
          <div class="custom-radio">
            <input type="radio" name="a1" id="l2" class="radio" value="{{differences['secondOpinionValue']}}" [(ngModel)]="selectedValue">
            <label for="l2">{{differences['secondOpinionValue']}}</label>
          </div>
        </div>
        <h4 class="section-heading">{{'UBZ.SITE_CONTENT.1001101010' | translate }}</h4>
        <label>{{ toTranslate | translate }}</label>
        <!-- Se testo normale -->
        <div class="form-group col-sm-12" *ngIf="type === 'text' && (!domainCode || domainCode === '')">
          <input type="text" name="val" [(ngModel)]="selectedValue" class="form-control">
        </div>
        <!-- Se è presente una nome di dominio stampo il dominio -->
        <div class="custom-select" *ngIf="domainCode !== ''">
          <select class="form-control" name="dom-sel" [(ngModel)]="selectedValue">
            <option value="">{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let domain of (domains | domainMapToDomainArray)" value="{{domain.domCode}}">{{ domain.translationCod | translate }}</option>
          </select>
        </div>
      </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary waves-effect" data-dismiss="modal" id="confirm-valore" (click)="confirmValue()" [disabled]="f.invalid">{{'UBZ.SITE_CONTENT.1100010' | translate }}</button>
      </div>
    </div>
  </div>
</div>
