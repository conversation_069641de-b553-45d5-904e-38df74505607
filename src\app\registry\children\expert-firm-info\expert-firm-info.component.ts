import { Component, OnInit, Inject, OnD<PERSON>roy, ViewChild } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { RegistryService } from '../../service/registry.service';
import {
  ExpertFirm,
  EXPERT_APPRAISAL_TYPES
} from '../../model/registry.models';
import { IAppConstants, APP_CONSTANTS } from '../../../app.constants';
import * as FileSaver from 'file-saver';
import { ExpertFirmGenericInfoComponent } from './component/expert-firm-generic-info/expert-firm-generic-info.component';

@Component({
  selector: 'app-expert-firm-info',
  templateUrl: './expert-firm-info.component.html',
  styleUrls: ['./expert-firm-info.component.css'],
  providers: [RegistryService]
})
export class ExpertFirmInfoComponent implements OnInit, On<PERSON><PERSON>roy {
  @ViewChild('expertFirmGenericInfo') expertFirmGenericInfo: ExpertFirmGenericInfoComponent;
  public readonly USE_MOCK = false;
  public subjectType: string;

  public editAction = true;
  public firmId: string;
  public firmDetail: ExpertFirm = new ExpertFirm();
  public subjectTypes;
  public ExpertAppraisalType = EXPERT_APPRAISAL_TYPES;
  public subSubjectType: string = '';

  constructor(
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    public _registryService: RegistryService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) {
    this.subjectTypes = this.constants.SubjectType;
  }

  ngOnInit() {
    this._activatedRoute.params
      .switchMap((params: Params) => {
        this.firmId = params['expertFirmId'];
        this.subjectType = params['subjectType'];
        return this._registryService.getExpertFirmDetail(
          this.firmId,
          this.USE_MOCK,
          this.subjectType
        );
      })
      .subscribe(res => {
        this.firmDetail = res;
        this.parseFirmDetail(this.firmDetail);
        if (this.subjectType === 'PER') {
          this.subSubjectType = this.firmDetail['anagSubjectType'];
        }
      });
  }

  ngOnDestroy() {
    this._registryService.fromExpertFirm = false;
    this._registryService.lastExpertFirm = '';
  }

  // Evento scatenato dalal chiusura della modale in expert-action-handler
  // Scatena refresh dei dati in expertFirmGenericInfo
  // Possibilità di estendere il comportamento ad altri componenti
  refreshComponents(booleanEvent) {
    if (booleanEvent) {
      this.expertFirmGenericInfo.refreshGenericData();
    }
  }

  public goToExpertPage(): void {
    this._router.navigateByUrl('/registry');
  }

  private parseFirmDetail(firmDetail: ExpertFirm) {
    firmDetail.startAbilitation = new Date(firmDetail.startAbilitation);
    firmDetail.endAbilitation = new Date(firmDetail.endAbilitation);
    firmDetail.registerData.registerBoardDate = new Date(
      firmDetail.registerData.registerBoardDate
    );
    firmDetail.registerData.registrationData = new Date(
      firmDetail.registerData.registrationData
    );
  }

  public downloadContract() {
    const downloadRequest = {};
    downloadRequest['positionId'] = this.subjectType + '-' + this.firmId;
    downloadRequest['contractId'] = this.firmDetail.contractId;
    this._registryService
      .downloadSupplyContract(downloadRequest)
      .subscribe(file => {
        FileSaver.saveAs(file, 'supply-contract.pdf');
      });
  }

  public goToFirmDetail(expertFirm: string) {
    this._registryService.fromExpertFirm = false;
    this._registryService.lastExpertFirm = '';
    this._router.navigateByUrl(`registry/SOC/${expertFirm}`);
  }
}
