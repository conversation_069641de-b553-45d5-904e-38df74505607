<fieldset [disabled]="!landingService.positionLocked">
  <router-outlet></router-outlet>
  <app-navigation-footer (saveButtonClick)="goToNextPage()" (closeDraftButtonClick)="exit()" (previousButtonClick)="previous()"
    (cancelButtonClick)="cancelPosition()" showSaveDraft="true" showPrevious="true" [footerClass]="menuService.footerProperty"
    [saveIsEnable]="assetService.saveIsEnable" [activeTaskCode]="currentTask"
    cancelButtonString="{{wizardCode === constants.wizardCodes.SIM ? ('UBZ.SITE_CONTENT.1001010100' | translate) : ('UBZ.SITE_CONTENT.1010' | translate)}}">
  </app-navigation-footer>
</fieldset>

<div *ngIf="isModalOpen" class="modal fade" bsModal #modalBox="bs-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" (onHidden)="hideModal()" [config]="{show: 'true'}">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.10101010101' | translate }}</h2>
        <button type="reset" class="close pull-right" aria-label="Close" (click)="hideModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="row pratica-states" style="margin-bottom: 20px;">
          <div class="col-sm-12">
            {{'UBZ.SITE_CONTENT.10101010110' | translate }}
          </div>
        </div>
        <div class="row pratica-states">
          <div class="col-sm-12">
            <ng-container *ngIf="assetsChecks.length > 0">
              <table class="uc-table">
                <thead>
                  <tr>
                    <th scope="col" style="text-align: left">{{'UBZ.SITE_CONTENT.100100' | translate }}</th>
                    <th scope="col" style="text-align: right">{{'UBZ.SITE_CONTENT.10010001' | translate }}</th>
                    <th scope="col" style="text-align: right">{{'UBZ.SITE_CONTENT.101011' | translate }}</th>
                    <th scope="col" style="text-align: right">{{'UBZ.SITE_CONTENT.101001' | translate }}</th>
                    <th scope="col" style="text-align: right">{{'UBZ.SITE_CONTENT.110001' | translate }}</th>
                    <th scope="col" style="text-align: right">{{'UBZ.SITE_CONTENT.10000110000' | translate }}</th>
                    <th scope="col" style="text-align: right">{{'UBZ.SITE_CONTENT.1110111011' | translate }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let assetCheck of assetsChecks">
                    <td>{{ assetCheck.assetId }}</td>
                    <td>
                      <ng-container *ngFor="let info of assetCheck.assets ; let i = index">
                        <span *ngIf="i > 0">, </span>
                        <span>{{ info.appraisalId }}</span>
                      </ng-container>
                    </td>
                    <td><span>{{ (assetCheck['assets'][0].address ? assetCheck['assets'][0].address : '') + (assetCheck['assets'][0].numAddress ? (' ' + assetCheck['assets'][0].numAddress) : '') + (assetCheck['assets'][0].cap ? ', ' + assetCheck['assets'][0].cap : '') }}</span></td>
                    <td><span>{{ assetCheck['assets'][0].city ? assetCheck['assets'][0].city : '' }}</span></td>
                    <td><span>{{ assetCheck['assets'][0].tabRegSheet ? assetCheck['assets'][0].tabRegSheet : '' }}</span></td>
                    <td><span>{{ assetCheck['assets'][0].tabTablePart ? assetCheck['assets'][0].tabTablePart : '' }}</span></td>
                    <td><span>{{ assetCheck['assets'][0].tabRegSub ? assetCheck['assets'][0].tabRegSub : '' }}</span></td>
                  </tr>
                </tbody>
              </table>
            </ng-container>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary waves-effect" (click)="hideModal()">
          <span>{{'UBZ.SITE_CONTENT.1101010011' | translate }}</span>
        </button>
      </div>
    </div>
  </div>
</div>

