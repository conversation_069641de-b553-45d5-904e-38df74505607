import { Component, OnInit, Input, Inject } from '@angular/core';
import { ActivatedRoute, Router, Params } from '@angular/router';
import { WizardDetailService } from '../services/wizard-detail.service';
import { AppraisalResponse } from '../model/appraisal-response';
import { CustomerResponse } from '../model/customer-response';
import { Collateral } from '../model/collateral';
import { AppraisalStory } from '../model/appraisal-story';
import { AppraisalObjectsInfo } from '../model/appraisal-objects-info';
import { Domain } from '../../shared/domain/domain';
import { Observable } from 'rxjs/Observable';
import 'rxjs/add/observable/forkJoin';
import { Status } from '../../shared/domain/status';
import { ConfirmService } from '../../simulation/services/confirm/confirm.service';
import { MenuService } from '../../shared/menu/services/menu.service';
import { PositionService } from '../../shared/position/position.service';
import { APP_CONSTANTS, IAppConstants } from '../../app.constants';

@Component({
  selector: 'app-wizard-detail',
  templateUrl: './wizard-detail.component.html',
  styleUrls: ['./wizard-detail.component.css'],
  providers: [ConfirmService]
})
export class WizardDetailComponent implements OnInit {
  // SelectedSection can be 'sintesi', 'storico', 'attivita' and 'note' only
  selectedSection = 'sintesi';
  positionId: string;
  wizardCode: string;
  bpmTaskId: string;
  bpmTaskCod: string;
  positionType: string;
  appraisalResp: AppraisalResponse = new AppraisalResponse();
  customerResp: CustomerResponse = new CustomerResponse();
  appraisalHistory: AppraisalStory[] = [];
  appraisalStatusTranslatedCode: string;
  assignmentType: string;
  private statusNames: Status[] = [];
  private macroProcesses: Domain[] = [];
  private appraisalTypes: Domain[] = [];
  private scopeTypes: Domain[] = [];
  private ndgTypes: Domain[] = [];
  private opinionTypes: Domain[]= [];

  isDettaglioSimulazione: boolean;
  showNewAppraisalButton = false;
  sendUcscFlag:any

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private wizardDetailService: WizardDetailService,
    private confirmService: ConfirmService,
    private menuService: MenuService,
    public positionService: PositionService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) {}

  ngOnInit() {
    this.fetchSintesi();
  }

  getMacroprocess = function() {
    if (
      this.macroProcesses &&
      this.macroProcesses[this.appraisalResp.macroProcess]
    ) {
      return this.macroProcesses[this.appraisalResp.macroProcess]
        .translationCod;
    } else {
      return '';
    }
  };

  getAppraisalType = function() {
    if (
      this.appraisalTypes &&
      this.appraisalTypes[this.appraisalResp.appraisalType]
    ) {
      return this.appraisalTypes[this.appraisalResp.appraisalType]
        .translationCod;
    } else {
      return '';
    }
  };

  getOpinionType = function() {
    if (
      this.opinionTypes &&
      this.opinionTypes[this.appraisalResp.opinionType]
    ) {
      return this.opinionTypes[this.appraisalResp.opinionType]
        .translationCod;
    } else {
      return '';
    }
  };

  getAppraisalScope = function() {
    if (this.scopeTypes && this.scopeTypes[this.appraisalResp.appraisalScope]) {
      return this.scopeTypes[this.appraisalResp.appraisalScope].translationCod;
    } else {
      return '';
    }
  };

  public getNdgType(): string {
    if (this.ndgTypes && this.ndgTypes[this.customerResp.ndgType]) {
      return this.ndgTypes[this.customerResp.ndgType].translationCod;
    }
    return '';
  }

  selezionaTab(tab: string) {
    this.selectedSection = tab;
    if (this.selectedSection === 'storico') {
      this.fetchHistory();
    }
    if (this.selectedSection === 'sintesi') {
      this.fetchSintesi();
    }
  }

  fetchSintesi() {
    this.activatedRoute.params
      .switchMap((params: Params) => {
        this.positionId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        this.bpmTaskId = params['taskId'];
        this.bpmTaskCod = params['taskCod'];
        this.setPositionTypeFromWizardCode(this.wizardCode);
        this.wizardCode === 'WSIM'
          ? (this.isDettaglioSimulazione = true)
          : (this.isDettaglioSimulazione = false);
        return Observable.forkJoin(
          // Prendo i dati della richiesta a partire dall'ID nel parametro
          this.wizardDetailService.getRequestData(
            this.positionId,
            this.wizardCode
          ),
          // Prendo i domini per poter poi prendere le label che mi servono
          this.wizardDetailService.getAppraisalTypes(),
          this.wizardDetailService.getScopeTypes(),
          this.wizardDetailService.getNDGTypes(),
          this.wizardDetailService.getStatus(),
          this.wizardDetailService.getMacroprocesses(),
          this.wizardDetailService.getOpinionTypes(),
          this.wizardDetailService.getAssignmentType(this.positionId, 'RIC'),
        );
      })
      .switchMap(res => {
        this.appraisalResp = res[0];
        this.sendUcscFlag=res[0].invoiceUcscFlag
       
    
        if (
          this.appraisalResp.statusCod === 'DC' &&
          this.appraisalResp.phaseCod === '010' &&
          this.wizardCode === 'WSIM'
        ) {
          this.showNewAppraisalButton = true;
        }
        this.appraisalTypes = res[1];
      
        this.scopeTypes = res[2];
        this.ndgTypes = res[3];
        this.statusNames = res[4];
        this.macroProcesses = res[5];
        this.opinionTypes = res[6];
        this.assignmentType = res[7].appEmpType ? res[7].appEmpType : null;
        // Ricavo lo stato
        const tmp = this.appraisalResp.phaseCod + this.appraisalResp.statusCod;
        this.appraisalStatusTranslatedCode = this.getTranslatedStatus(tmp);

        return Observable.forkJoin(
          // Prendo i dati del cliente
          this.wizardDetailService.getCustomerData(
            this.appraisalResp.customerId
          )
        );
      })
      .subscribe(res => {
        this.customerResp = res[0];
      });
  }

  fetchHistory() {
    this.activatedRoute.params
      .switchMap((params: Params) => {
        this.positionId = params['positionId'];
        return Observable.forkJoin(
          this.wizardDetailService.getAppraisalHistory(this.positionId),
          // Prendere i domini
          this.wizardDetailService.getAppraisalTypes(),
          this.wizardDetailService.getStatus(),
          this.wizardDetailService.getOpinionTypes()
        );
      })
      .subscribe(res => {
        this.appraisalHistory = res[0];
        this.appraisalTypes = res[1];
        this.statusNames = res[2];
        this.opinionTypes = res[3];
        this.resolveHistory();
      });
  }

  resolveHistory() {
    let tmp: string;
    for (const perizia of this.appraisalHistory) {
      perizia.appraisalType = this.appraisalTypes[
        perizia.appraisalType
      ].translationCod;
      perizia.opinionType = this.opinionTypes[
        perizia.opinionType
      ].translationCod;
      tmp = perizia.phaseCod + perizia.statusCod;
      perizia.translatedStatus = this.getTranslatedStatus(tmp);
    }
  }

  getTranslatedStatus(combined: string): string {
    if (this.statusNames[combined]) {
      return this.statusNames[combined].translationStatusCod;
    } else {
      return null;
    }
  }

  startAppraisalRequest() {
    this.confirmService.startAppraisalRequest(this.positionId).subscribe(x => {
      this.menuService.setMenuStatus(0, 2);
      if (x.esito) {
        this.router.navigate([`wizard/WRPE/${this.positionId}/customer-info`]);
      } else {
        this.router.navigate([
          `customer-search/WRPE/${this.positionId}/${x.familyAsset}`
        ]);
      }
    });
  }

  private setPositionTypeFromWizardCode(wizardCode: string) {
    if (wizardCode === 'WSIM') {
      this.positionType = 'SIM';
    } else if (wizardCode === 'WRPE') {
      this.positionType = 'RIC';
    } else {
      this.positionType = 'PER';
    }
  }

  goToDashboard() {
    if (this.wizardCode === this.constants.wizardCodes.SIM) {
      this.router.navigateByUrl('dashboard/SIM');
    } else if (this.wizardCode === this.constants.wizardCodes.REQ) {
      this.router.navigateByUrl('dashboard/REQ');
    }
  }

  goToAppraisalDetail(appraisalId: string) {
    this.router.navigateByUrl(`generic-task/${appraisalId}/-/-`);
  }
}
