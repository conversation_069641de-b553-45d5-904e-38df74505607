import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class TaskLandingService {
  constructor(private http: Http) {}

  getPageByTaskCod(taskCod: string): Observable<string> {
    taskCod = encodeURIComponent(taskCod);
    const url = `/UBZ-ESA-RS/service/taskManager/v1/tasks/${taskCod}`;
    return this.http.get(url).map((resp: Response) => resp.text());
  }
}
