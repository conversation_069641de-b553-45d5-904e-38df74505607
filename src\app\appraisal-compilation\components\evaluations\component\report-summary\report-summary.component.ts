import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { ReportSummaryModel } from '../..';
import { Observable } from 'rxjs/Observable';
import { DomainService } from '../../../../../shared/domain';
import { LandingService } from '../../../../../simulation';

@Component({
  selector: 'app-report-summary',
  templateUrl: './report-summary.component.html',
  styleUrls: ['./report-summary.component.css']
})
export class ReportSummaryComponent implements OnInit {
  @ViewChild('f') form: NgForm;
  @Input('reportSummaryObject') reportSummaryObject: ReportSummaryModel; // Oggetto contenenti i valori per i campi a video

  // Domini per i campi select
  estateStatusDom: any[];
  merchantabilityDom: any[];
  congruityDom: any[];
  estimateDom: any[];

  constructor(
    public accordionAPFService: AccordionAPFService,
    private domainService: DomainService,
    private landingService: LandingService
  ) {}

  // L'evento change del form viene intercettato dal componente padre EvaluationsComponent e
  // viene scatenato il metodo del padre checkSaveEnable() per validare lo step del wizard
  ngOnInit() {
    Observable.forkJoin(
      this.domainService.newGetDomain('UBZ_DOM_BUILDING_STATUS'),
      this.domainService.newGetDomain(
        'UBZ_DOM_COMM_OPINION',
        this.landingService.posSegment
      ),
      // this.domainService.newGetDomain('UBZ_DOM_ESTIMATE_REPORT'),
      this.domainService.newGetDomain('UBZ_DOM_CONGRUITY'),
      this.domainService.newGetDomain('UBZ_DOM_ESTIMATE_REPORT')
    ).subscribe(response => {
      this.estateStatusDom = response[0];
      this.merchantabilityDom = response[1];
      this.congruityDom = response[2];
      this.estimateDom = response[3];
    });
  }

  public isValid(): boolean {
    return this.form && this.form.valid;
  }

  // Invocato dal componente padre, restituisce un oggetto contenente le variabili del componente da salvare
  getData() {
    return this.reportSummaryObject;
  }
}
