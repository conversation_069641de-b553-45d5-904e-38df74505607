<!-- fixme - togliere codice commentato -->
<section id="interactive-tabs" class="interactive-tabs">
  <div class="row">
    <p class="col-sm-12">
      <strong>{{'UBZ.SITE_CONTENT.11001011' | translate }}:</strong>
      {{'UBZ.SITE_CONTENT.1011001000' | translate }}
      <strong>{{'UBZ.SITE_CONTENT.1011001001' | translate }}</strong>
      <i class="fa fa-user" aria-hidden="true" style="color: #cfcfcf"></i>
    </p>
  </div>
  <div class="row">
    <div class="col-sm-12">
      <app-tab-manager [assets]="assets" [selectedAsset]="selectedAsset" [readOnly]="!_landingService.positionLocked || _landingService.isLockedTask[currentTask]"
        [activeCategories]="activeCategories" [canAddAsset]="canAddDeleteAsset"
        [isAppraisalConvalidated]="isAppraisalConvalidated" (onSelectAsset)="changeSelectedAsset($event)"
        (onNewAsset)="openNewAssetModal()">
      </app-tab-manager>
      <div class="tab-content">
        <div role="tabpanel" class="tab-pane active" id="sub1">
          <div class="base-wrapper">
            <div class="row">
              <div class="col-sm-12">
                <div class="row">
                  <div class="col-sm-12">
                    <div class="edit-controls">
                      <fieldset [disabled]="!_landingService.positionLocked || _landingService.isLockedTask[currentTask]">
                        <ng-container *appAuthKey="'UBZ_GUARANTEES_SAVE'">
                          <button *ngIf="isEditable === true" type="button"
                            class="btn btn-empty waves-effect waves-secondary pull-right blue-text" id="save-asset"
                            (click)="save()" [disabled]="!isAValidPage">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.1000101001' | translate
                            }}
                          </button>
                        </ng-container>
                        <fieldset [disabled]="haveDisabledFields">
                          <ng-container *appAuthKey="'UBZ_GUARANTEES_COPY'">
                            <button *ngIf="isEditable === false && canAddDeleteAsset === true" type="button"
                              class="btn btn-empty waves-effect waves-secondary pull-right blue-text" id="copia-asset"
                              (click)="copy()">
                              <i class="fa fa-files-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.1000101010' | translate
                              }}
                            </button>
                          </ng-container>
                          <ng-container *appAuthKey="'UBZ_GUARANTEES_DELETE'">
                            <button *ngIf="canAddDeleteAsset === true" type="button"
                              class="btn btn-empty waves-effect waves-secondary pull-right blue-text"
                              data-toggle="modal" data-target="#remove-asset" (click)="delete()">
                              <i class="fa fa-trash-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.1000101011' | translate
                              }}
                            </button>
                          </ng-container>
                        </fieldset>
                        <ng-container *appAuthKey="'UBZ_GUARANTEES_MODIFY'">
                          <button *ngIf="isEditable === false" type="button"
                            class="btn btn-empty waves-effect waves-secondary pull-right blue-text" id="modifica-asset"
                            (click)="edit()">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.1000101100' | translate
                            }}
                          </button>
                        </ng-container>
                      </fieldset>
                    </div>
                  </div>
                </div>
                <div class="row inner-base-wrapper">
                  <fieldset [disabled]="!_landingService.positionLocked || _landingService.isLockedTask[currentTask] || !isEditable">
                    <fieldset [disabled]="haveDisabledFields">
                      <div *ngIf="staticPageContent && selectedAsset && !isShipping && !isTemplateUpdate"
                        class="col-sm-4 form-group">
                        <label>{{'UBZ.SITE_CONTENT.1000101101' | translate }}*</label>
                        <app-calendario [(ngModel)]="staticPageContent['docConclusionDate']"
                          [name]="'docConclusionDate'" [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                          [required]="true" [forceShowDatepickerPosition]="'bottom'" (ngModelChange)="pageIsValid()">
                        </app-calendario>
                      </div>
                      <!-- Se surveyNecFlag === 'NES' non è stato scelto il sopralluogo e bisogna nascondere il relativo campo data -->
                      <ng-container *ngIf="appraisalInfo && appraisalInfo.appraisal.surveyNecFlag !== 'NES'">
                        <div *ngIf="isInterno && staticPageContent && selectedAsset" class="col-sm-4 form-group">
                          <label>{{'UBZ.SITE_CONTENT.1000101110' | translate }}*</label>
                          <app-calendario [name]="'surveyDate'" [(ngModel)]="staticPageContent['surveyDate']"
                            [required]="true" [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                            [forceShowDatepickerPosition]="'bottom'" (ngModelChange)="pageIsValid()" [maxDate]="today">
                          </app-calendario>
                        </div>
                        <div *ngIf="isEsterno && staticPageContent && selectedAsset" class="col-sm-4 form-group">
                          <label>{{'UBZ.SITE_CONTENT.1010110010' | translate }}*</label>
                          <app-calendario [name]="'externSurveyDate'"
                            [(ngModel)]="staticPageContent['externSurveyDate']" [required]="true"
                            [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                            [forceShowDatepickerPosition]="'bottom'" (ngModelChange)="pageIsValid()" [maxDate]="today">
                          </app-calendario>
                        </div>
                      </ng-container>
                      <div *ngIf="staticPageContent && selectedAsset && isShipping" class="col-sm-4 form-group">
                        <label>{{'UBZ.SITE_CONTENT.1010110011' | translate }}*</label>
                        <app-calendario [name]="'inspectionDate'" [(ngModel)]="staticPageContent['inspectionDate']"
                          [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                          [forceShowDatepickerPosition]="'bottom'" [required]="true" (ngModelChange)="pageIsValid()">
                        </app-calendario>
                      </div>
                    </fieldset>
                    <div class="col-sm-12">
                      <div class="row">
                        <div class="col-sm-12">
                          <!-- Aggiunti fieldset su ogni accordion per gestire disabilitazione fra parti statiche e dinamiche  -->
                          <!-- Gli APF di SAL si gestiscono autonomamente sulla disabilitazione dei campi -->
                          <accordion *ngIf="staticPageContent && selectedAsset && !isShipping && !isAeremobile"
                            class="panel-group" id="accordion">
                            <fieldset class="validation-sect" [disabled]="forceApfDisabled">
                              <app-accordion-application-form [overwriteAPFData]="true" [positionId]="positionId"
                                [idCode]="selectedAsset.idObject" [page]="'IMM_GARANZIA'" [mapApf]="fieldObject"
                                [drivers]="{'DD_ORD_PAG':1}">
                              </app-accordion-application-form>
                            </fieldset>

                            <fieldset class="validation-sect" [disabled]="haveDisabledFields" *ngIf="!isTemplateUpdate">
                              <app-property #property (statusChange)="pageIsValid()"
                                [pageContent]="staticPageContent['ownershipdetails']" [positionId]="positionId"
                                [objectCode]="selectedAsset.idObject" [domainResp]="domainsFromChildsObj['UBZ_DOM_RIGHT_TYPE']">
                              </app-property>
                            </fieldset>

                            <fieldset class="validation-sect" [disabled]="forceApfDisabled">
                              <app-accordion-application-form [overwriteAPFData]="true" [positionId]="positionId"
                                [idCode]="selectedAsset.idObject" [page]="'IMM_GARANZIA'" [mapApf]="fieldObject" [drivers]="{'DD_ORD_PAG':2}">
                              </app-accordion-application-form>
                            </fieldset>

                            <fieldset class="validation-sect" [disabled]="haveDisabledFields" *ngIf="!isTemplateUpdate">
                              <app-installations
                                *ngIf="!(appraisalInfo?.isTemplateLight && appraisalInfo?.appraisal?.posSegment ==='IND')"
                                #installation (change)="pageIsValid()" [pageContent]="staticPageContent['systemObject']"
                                [assetId]="selectedAsset.idObject" [positionId]="positionId"></app-installations>
                            </fieldset>

                            <fieldset class="validation-sect" [disabled]="forceApfDisabled">
                              <app-accordion-application-form [overwriteAPFData]="true" [positionId]="positionId"
                                [idCode]="selectedAsset.idObject" [page]="'IMM_GARANZIA'" [mapApf]="fieldObject" [drivers]="{'DD_ORD_PAG':3}">
                              </app-accordion-application-form>
                            </fieldset>

                            <fieldset class="validation-sect" [disabled]="haveDisabledFields" *ngIf="!isTemplateUpdate">
                              <app-origin *ngIf="!agrarianLoan" #origin (change)="pageIsValid()"
                                [pageContent]="staticPageContent['originObject']" [assetId]="selectedAsset.idObject"
                                [positionId]="positionId" [domainResp]="domainsFromChildsObj['UBZ_DOM_ORIGIN_TYPE']"></app-origin>
                            </fieldset>

                            <fieldset class="validation-sect" [disabled]="forceApfDisabled">
                              <app-accordion-application-form [overwriteAPFData]="true" [positionId]="positionId"
                                [idCode]="selectedAsset.idObject" [page]="'IMM_GARANZIA'" [mapApf]="fieldObject" [drivers]="{'DD_ORD_PAG':4}">
                              </app-accordion-application-form>
                            </fieldset>

                            <fieldset class="validation-sect" [disabled]="haveDisabledFields" *ngIf="!isTemplateUpdate">
                              <app-building-measures
                                *ngIf="!(appraisalInfo?.isTemplateLight && appraisalInfo?.appraisal?.posSegment ==='IND')"
                                #buildingMeasures (statusChange)="pageIsValid()" (change)="pageIsValid()"
                                [pageContent]="[ staticPageContent['buildingProv'] , staticPageContent['objPropertyDesc'] ]"
                                [assetId]="selectedAsset.idObject" [positionId]="positionId"
                                [domainResp]="domainsBuildingMeasuresObj"></app-building-measures>
                            </fieldset>

                            <fieldset class="validation-sect" [disabled]="haveDisabledFields" *ngIf="!isTemplateUpdate">
                              <app-building-amnesty #buildinfAmnesty *ngIf="_landingService.posSegment === 'COR'"
                                (change)="pageIsValid()" [pageContent]="staticPageContent['buildingSanc']"
                                [domainResp]="domainsFromChildsObj['UBZ_DOM_SANCTION_TYPE']"
                                [assetId]="selectedAsset.idObject" [positionId]="positionId"
                                [agrarianLoan]="agrarianLoan">
                              </app-building-amnesty>
                            </fieldset>

                            <fieldset class="validation-sect" [disabled]="forceApfDisabled">
                              <app-accordion-application-form [overwriteAPFData]="true" [positionId]="positionId"
                                [idCode]="selectedAsset.idObject" [page]="'IMM_GARANZIA'" [mapApf]="fieldObject" [drivers]="{'DD_ORD_PAG':5}">
                              </app-accordion-application-form>
                            </fieldset>

                            <fieldset class="validation-sect" [disabled]="haveDisabledFields" *ngIf="!isTemplateUpdate">
                              <app-construction-site-security #constructionSiteSecurity
                                *ngIf="!agrarianLoan && _landingService.posSegment === 'COR'"
                                (statusChange)="pageIsValid()" [pageContent]="staticPageContent['objbuildsec']"
                                [assetId]="selectedAsset.idObject" [positionId]="positionId"
                                [domainResp]="domainsFromChildsObj['UBZ_DOM_PERSON_ROLE']">
                              </app-construction-site-security>
                            </fieldset>

                            <app-consistency [domainResp]="domainsConsistencyObj" #consistency [differences]="opinionDifferences"
                              (statusChange)="pageIsValid()" [pageContent]="staticPageContent['objevaluation']"
                              [positionId]="positionId" [agrarianLoan]="agrarianLoan"
                              [isMarketValueRequired]="isMarketValueRequired" [haveDisabledFields]="haveDisabledFields"
                              [haveDisabledFieldsSpecial]="haveDisabledFieldsSpecial"
                              [haveActualValue]="!isTemplateUpdate">
                            </app-consistency>

                            <!-- Sul primo SAL tutte le colonne della tabella di COSTI DI REALIZZAZIONE/RISTRUTTURAZIONE FABBRICATO sono sbloccate-->
                            <app-renovation-manufactured
                              *ngIf="!(appraisalInfo?.isTemplateLight && appraisalInfo?.appraisal?.posSegment ==='IND') && !isTemplateUpdate"
                              #renovationManufactured (change)="pageIsValid()"
                              [pageContent]="staticPageContent['unitActivies']" [positionId]="positionId"
                              [appraisalType]="appraisalType"
                              [haveDisabledFields]="haveDisabledFieldsRenovationManifactured"
                              [haveDisabledFieldsSpecial]="haveDisabledFieldsSpecial">
                            </app-renovation-manufactured>

                            <fieldset class="validation-sect" [disabled]="haveDisabledFields">
                              <app-appraisal-template-validation
                                *ngIf="_positionService.isInternalSecondOpinion && !(appraisalInfo?.isTemplateLight && appraisalInfo?.appraisal?.posSegment ==='IND')"
                                #templateValidation (statusChange)="pageIsValid()" [positionId]="positionId"
                                [assetId]="selectedAsset.idObject" templateName="IMM_GARANZIA"
                                [haveDisabledFields]="haveDisabledFields"></app-appraisal-template-validation>
                            </fieldset>
                          </accordion> 

                          <accordion *ngIf="staticPageContent && selectedAsset && (isShipping || isAeremobile)"
                            class="panel-group" id="accordion">

                            <fieldset class="validation-sect" [disabled]="forceApfDisabled">
                              <app-accordion-application-form [overwriteAPFData]="true"
                                *ngIf="isShipping || isAeremobile" [positionId]="positionId"
                                [idCode]="selectedAsset.idObject" [page]="'GARANZIA'">
                              </app-accordion-application-form>
                            </fieldset>

                            <fieldset class="validation-sect" [disabled]="haveDisabledFields">
                              <app-appraisal-template-validation
                                *ngIf="_positionService.isInternalSecondOpinion && !(appraisalInfo?.isTemplateLight && appraisalInfo?.appraisal?.posSegment ==='IND')"
                                #templateValidation (statusChange)="pageIsValid()" [positionId]="positionId"
                                [assetId]="selectedAsset.idObject" templateName="IMM_GARANZIA"
                                [haveDisabledFields]="haveDisabledFields"></app-appraisal-template-validation>
                            </fieldset>

                          </accordion>
                        </div>
                      </div>
                    </div>
                  </fieldset>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<app-new-asset-modal [appraisalScope]="mobileScope" [isOpen]="newAssetModalIsOpen" [positionId]="positionId"
  (modalClose)="closeNewAssetModal()" [requestId]="requestId"></app-new-asset-modal>
<!-- CUSTOM MODAL gestisce la cancellazione dell'asset -->
<app-custom-modal *ngIf="domainsFromChildsObj['UBZ_DOM_MOTIVATIONS_DISABLE']" [domainResp]="domainsFromChildsObj['UBZ_DOM_MOTIVATIONS_DISABLE']" [modalType]="'deleteAsset'" [isOpen]="deleteAssetModalIsOpen" [largeModalFlag]="false" [headerTitle]="'UBZ.SITE_CONTENT.1000101011'"
  [positionId]="''" [idCode]="''" [apfString]="''" [messagesArray]="['UBZ.SITE_CONTENT.11111100011']" [buttonTitle]="['UBZ.SITE_CONTENT.11110', 'UBZ.SITE_CONTENT.100000']"
  [disabledFlag]="false" (modalSubmit)="submitDeleteModal($event)" (modalClose)="closeDeleteModal()">
</app-custom-modal>

<fieldset [disabled]="!_landingService.positionLocked">
  <app-navigation-footer [footerClass]="menuService.footerProperty" [saveIsEnable]="saveIsEnabled()"
    [showCancelButton]="false" (saveButtonClick)="goNextPage()" (closeDraftButtonClick)="draftSaved()"
    [saveDraftCallback]="draftButtonCallback" showSaveDraft="true" showPrevious="true"
    (previousButtonClick)="goToPreviousTask()" [activeTaskCode]="currentTask"></app-navigation-footer>
</fieldset>