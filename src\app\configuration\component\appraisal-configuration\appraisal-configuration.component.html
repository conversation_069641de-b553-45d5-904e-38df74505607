<div class="row step-navigation">
  <div class="col-sm-12 btn-set">
    <ng-container *ngIf="!editAction">
      <ng-container *appAuthKey="'UBZ_CONFIGURATION.APPRAISAL_NEW'">
        <button type="button" class="btn btn-empty waves-effect waves-secondary pull-right" (click)="goToNewRule()">
          <i class="icon-add"></i> {{'UBZ.SITE_CONTENT.1010100100' | translate }}
        </button>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="editAction">
      <ng-container *appAuthKey="'UBZ_CONFIGURATION.APPRAISAL_ABORT'">
        <button type="button" class="btn btn-empty waves-effect waves-secondary pull-right" (click)="abortEditAction()">
          <i aria-hidden="true" class="fa fa-times"></i> {{'UBZ.SITE_CONTENT.100000' | translate }}
        </button>
      </ng-container>
    </ng-container>
  </div>
</div>

<div *ngIf="length === 0" class="Search__NoResults">
  <div class="Search__NoResults__Text">
    <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1101010000' | translate }}</h3>
    <p class="Search__NoResults__Subtitle">
      {{'UBZ.SITE_CONTENT.10001110111' | translate }}
    </p>
  </div>
</div>

<div class="row" *ngIf="!editAction && length > 0">
  <div class="col-sm-12">
    <h3>{{'UBZ.SITE_CONTENT.101100111' | translate }}</h3>
    <table class="uc-table">
      <thead>
        <tr>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1010001' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1010100101' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.11101101' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1010000' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1010101011' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1010101100' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1010011101' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1110101' | translate }}</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngIf="configurations">
          <ng-container *ngFor="let item of configurations">
            <tr>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1010001' | translate }}">{{ macroProcessTypes && macroProcessTypes[item.macroProcess] ? (macroProcessTypes[item.macroProcess].translationCod
                | translate) : '' }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1010100101' | translate }}">{{ posSegments && posSegments[item.posSegment] ? (posSegments[item.posSegment].translationCod | translate)
                : '' }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.11101101' | translate }}">{{ appraisalTypes && appraisalTypes[item.appraisalType] ? (appraisalTypes[item.appraisalType].translationCod
                | translate) : '' }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1010000' | translate }}">{{ scopeTypes && scopeTypes[item.appraisalScope] ? (scopeTypes[item.appraisalScope].translationCod | translate)
                : '' }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1010101011' | translate }}">{{ structureTypes && structureTypes[item.appraisalStruct] ? (structureTypes[item.appraisalStruct].translationCod
                | translate) : '' }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1010101100' | translate }}">{{ goodTypes && goodTypes[item.goodType] ? (goodTypes[item.goodType].translationCod | translate) : '' }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1010011101' | translate }}">
                <i class="icon-check" [ngClass]="{'icon-lightblue': item.activeFlag, 'light-grey': !item.activeFlag}"></i>
              </td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1110101' | translate }}">
                <ng-container *appAuthKey="'UBZ_CONFIGURATION.APPRAISAL_MODIFY'">
                  <button type="button" class="btn btn-empty" (click)="modifySelectedRule(item)">
                    <i class="fa fa-pencil-square-o"></i>
                  </button>
                </ng-container>
                <ng-container *appAuthKey="'UBZ_CONFIGURATION.APPRAISAL_REMOVE'">
                  <button type="button" class="btn btn-empty" (click)="removeRule(item)">
                    <i class="fa fa-trash-o" aria-hidden="true"></i>
                  </button>
                </ng-container>
                <button type="button" class="btn btn-empty" (click)="selectRuleToExpand(item)">
                  <i class="fa" [ngClass]="{'fa-plus-square-o': !(ruleToExpand.ruleId === item.ruleId), 'fa-minus-square-o': (ruleToExpand.ruleId === item.ruleId)}"
                    aria-hidden="true"></i>
                </button>
              </td>
            </tr>
            <tr *ngIf="ruleToExpand && ruleToExpand.ruleId === item.ruleId">
              <td colspan="8" class="row">
                <div class="col-sm-12 text-left">
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.1010101000' | translate }}: </strong>
                    {{ appraisalOwners && appraisalOwners[item.appraisalOwner] ? (appraisalOwners[item.appraisalOwner].translationCod | translate)
                    : '' }}
                  </p>
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.1010110' | translate }}: </strong>
                    <i class="icon-check" [ngClass]="{'icon-lightblue': item.externalAppraisalFlag, 'light-grey': !item.externalAppraisalFlag}"></i>
                  </p>
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.1010101110' | translate }}: </strong>
                    <span>{{ item.autoOrigination }}</span>
                  </p>
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.1010101010' | translate }}: </strong>
                    {{ item.priority }}
                  </p>
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.1010101111' | translate }}: </strong>
                    {{ item.bornDate | date:'shortDate' }} - {{ item.deadDate | date:'shortDate' }}
                  </p>
                </div>
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </table>
  </div>
</div>

<ng-container *ngIf="editAction">
  <form #f="ngForm" (ngSubmit)="confirmEditAction()">
    <div class="row">
      <div class="col-sm-12">
        <h3>{{'UBZ.SITE_CONTENT.1010101101' | translate }}</h3>
      </div>
    </div>
    <div class="row">
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1010001' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="selectedRule.macroProcess" required name="macroProcess">
            <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let row of (macroProcessTypes | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1010100101' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="selectedRule.posSegment" required name="posSegment">
            <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let row of (posSegments | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1010101100' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="selectedRule.goodType" required name="goodType">
            <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let row of (goodTypes | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.11101101' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="selectedRule.appraisalType" required name="appraisalType">
            <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let row of (appraisalTypes | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1010000' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="selectedRule.appraisalScope" required name="appraisalScope">
            <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let row of (scopeTypes | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1010101011' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="selectedRule.appraisalStruct" required name="appraisalStruct">
            <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let row of (structureTypes | domainMapToDomainArray | sortCategoryType)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1010101000' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="selectedRule.appraisalOwner" required name="appraisalOwner">
            <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let row of (appraisalOwners | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label class="hidden-sm"></label>
        <div class="custom-checkbox">
          <input div="custom-checkbox" type="checkbox" id="externalAppraisalFlag" name="externalAppraisalFlag" [(ngModel)]="selectedRule.externalAppraisalFlag">
          <label for="externalAppraisalFlag" data-toggle="tooltip" attr.title="{{'UBZ.SITE_CONTENT.1010110' | translate }}">{{'UBZ.SITE_CONTENT.1010110' | translate }}</label>
        </div>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1010101110' | translate }}*</label>
        <input type="text" name="autoOrigination" [(ngModel)]="selectedRule.autoOrigination" class="form-control" required>
      </div>
    </div>
    <div class="row">
      <div class="col-md-4 col-sm-12 form-group">
        <label for="bornDate">{{'UBZ.SITE_CONTENT.1010100001' | translate }}*</label>
        <app-calendario [name]="'bornDate'" [(ngModel)]="selectedRule.bornDate" [placeholder]="'UBZ.SITE_CONTENT.1010100001' | translate"
          [required]="true">
        </app-calendario>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label for="deadDate">{{'UBZ.SITE_CONTENT.1010100010' | translate }}*</label>
        <app-calendario [name]="'deadDate'" [(ngModel)]="selectedRule.deadDate" [placeholder]="'UBZ.SITE_CONTENT.1010100001' | translate"
          [required]="true">
        </app-calendario>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label class="hidden-sm"></label>
        <div class="custom-checkbox">
          <input div="custom-checkbox" type="checkbox" id="activeFlag" name="activeFlag" [(ngModel)]="selectedRule.activeFlag">
          <label for="activeFlag" data-toggle="tooltip" attr.title="{{'UBZ.SITE_CONTENT.1010011101' | translate }}">{{'UBZ.SITE_CONTENT.1010011101' | translate }}</label>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1010101010' | translate }}*</label>
        <input appOnlyNumbers type="text" name="priority" [(ngModel)]="selectedRule.priority" class="form-control" required>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12 btn-set">
        <ng-container *appAuthKey="'UBZ_CONFIGURATION.APPRAISAL_CONFIRM'">
          <button class="btn btn-primary waves-effect pull-right" [disabled]="f.invalid" type="submit">{{'UBZ.SITE_CONTENT.1100010' | translate }}</button>
        </ng-container>
      </div>
    </div>
  </form>
</ng-container>
