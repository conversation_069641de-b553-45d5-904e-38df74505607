import { Component, OnInit, Input } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { PhysicalRiskService } from '../../../appraisal-compilation/service/physical-risk.service';
import { DomainService } from '../../../shared/domain/domain.service';
import { GenericTaskService } from '../../../tasks/services/generic-task/generic-task.service';

@Component({
  selector: 'app-appraisal-migration-info',
  templateUrl: './appraisal-migration-info.component.html',
  styleUrls: ['./appraisal-migration-info.component.css']
})
export class AppraisalMigrationInfoComponent implements OnInit {
  @Input() data;
  @Input() appraisalId;
  serviceDom: any[] = [];
  buildDom: any[] = [];
  planningDom: any[] = [];
  registryDom: any[] = [];
  billingAmount: number;
  billingDate: Date;
  freeAppraisalCheck = true;

  allResponseData: any = {}
  allLivello: any[] = [];
  allRischio: any[] = []


  constructor(
    private domainService: DomainService,
    private genericTaskService: GenericTaskService,
    private service: PhysicalRiskService,
   
  ) { }

  ngOnInit() {
   
      Observable.forkJoin(
      this.domainService.newGetDomain('UBZ_DOM_SERVICE_POSITION'),
      this.domainService.newGetDomain('UBZ_DOM_BUILD_FEATURES'),
      this.domainService.newGetDomain('UBZ_DOM_PLANNING_PROCESS'),
      this.domainService.newGetDomain('UBZ_DOM_REGISTRY_CORR'),
      this.genericTaskService.getBillingData(this.appraisalId),
      this.service.getConfigurationTable(),
      this.service.getUpdateData(this.appraisalId)
      ).subscribe(x => {
      this.serviceDom = x[0];
      this.buildDom = x[1];
      this.planningDom = x[2];
      this.registryDom = x[3];
      this.getTransparencyData(x[4]);
      this.allLivello = x[5].riskLevels;
      this.allRischio = x[5].riskTypes;
      this.allResponseData = x[6]

    });

  }

  getTransparencyData(response) {
    this.billingAmount = response.billingAmount;
    this.billingDate = response.billingAmountDate === null ? null : new Date(response.billingAmountDate);
    this.freeAppraisalCheck = response.gratisFlag === 'Y' ? true : false;
  }



  getProperty(riskType: string): string {
    return this.allResponseData[riskType];
  }


  getRiskLevel(riskType: string): string {
    const physicalRiskLevel = this.getProperty(riskType);
    for (const level of this.allLivello) {
      if (level.domCode === physicalRiskLevel) {
        return level.translationCod;
      }
    }
    return '';
  }


}
