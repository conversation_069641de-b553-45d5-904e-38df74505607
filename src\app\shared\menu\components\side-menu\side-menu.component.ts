import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';

import { SearchMenuComponent } from '../search-menu/search-menu.component';
import { MenuElement } from '../../model/menu-element';
import { MenuService } from '../../services/menu.service';

@Component({
  selector: 'app-side-menu',
  templateUrl: './side-menu.component.html',
  styleUrls: ['./side-menu.component.css']
})
export class SideMenuComponent implements OnInit {
  @ViewChild(SearchMenuComponent) searchComponent: SearchMenuComponent;

  constructor(public menuService: MenuService, private router: Router) {}

  ngOnInit() {}

  public changePageAndProperty(url: string, index: number, hasChilds: boolean) {
    if (!hasChilds) {
      this.menuService.menuStatus[2] = -1;
      this.changePage(url);
    }
    this.changeMenuPropriety(index, hasChilds);
  }

  public changePage(url: string) {
    this.router
      .navigateByUrl('/emptyRedirect', { skipLocationChange: true })
      .then(() => {
        this.router.navigate([url]);
        this.menuService.sideMenuPropriety = 'toggled';
      });
    // this.router.navigate([url]);
    // this.menuService.sideMenuPropriety = 'toggled';
  }

  private changeMenuPropriety(index: number, hasChilds: boolean) {
    if (this.menuService.sideMenuPropriety === 'toggled') {
      this.menuService.changeSideMenuPropriety(index);
    }
    this.menuService.menuStatus[1] = index;
    this.changeSecondLevelMenuStatus(index, hasChilds);
  }

  private changeSecondLevelMenuStatus(index: number, hasChilds: boolean) {
    if (hasChilds === false) {
      this.menuService.indexOpenedVoice = -1;
    } else {
      if (this.menuService.indexOpenedVoice === index) {
        this.menuService.indexOpenedVoice = -1;
      } else {
        this.menuService.indexOpenedVoice = index;
      }
    }
  }

  public clickOnThirdLevelElement(url: string, index: number) {
    this.menuService.menuStatus[2] = index;
    this.changePage(url);
  }

  openSideSearch() {
    this.menuService.isSideSearchOpen = true;
    setTimeout(() => {
      this.searchComponent.isOpened.emit(true);
    }, 0);
  }
}
