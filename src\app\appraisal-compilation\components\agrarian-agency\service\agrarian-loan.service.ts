import { Injectable } from '@angular/core';
import { Http } from '@angular/http';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class AgrarianLoanService {
  constructor(private _http: Http) {}

  public getAgrarianData(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url =
      '/UBZ-ESA-RS/service/immGarService/farmoverallgetdata/' + positionId;
    return this._http.get(url).map(res => res.json());
  }

  public saveAgrarianData(
    positionId: string,
    statics: any,
    apfs: any
  ): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const obj = {};
    obj['staticsave'] = statics;
    obj['apfsave'] = { apfmap: apfs, page: 'AZ_AGR_COMPLESSO' };
    const url =
      '/UBZ-ESA-RS/service/immGarService/farmoverallsavedata/' + positionId;
    return this._http.post(url, obj).map(res => res.json());
  }
}
