import { Component, OnInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Router, Params } from '@angular/router';

import { ApplicationFormComponent } from '../../../shared/application-form/components/application-form.component';
import { AssetService } from '../../services/asset/asset.service';

@Component({
  selector: 'app-new-asset',
  templateUrl: './new-asset.component.html',
  styleUrls: ['./new-asset.component.css']
})
export class NewAssetComponent implements OnInit {
  @ViewChild(ApplicationFormComponent)
  public applicationForm: ApplicationFormComponent;

  drivers = { DD_AST_TIPO: this.assetService.familyAssetType };
  simulationId: string;
  wizardCode: string;
  page = { WSIM: 'INFO_ASSET', WRPE: 'INFO_ASSET_RIC' };

  constructor(
    private assetService: AssetService,
    private activatedRoute: ActivatedRoute,
    private cdRef: ChangeDetectorRef,
    private router: Router
  ) {}

  ngOnInit() {
    this.activatedRoute.parent.params.subscribe((params: Params) => {
      this.simulationId = params['positionId'];
      this.wizardCode = params['wizardCode'];
    });
    setTimeout(() => {
      this.assetService.saveIsEnable = false;
      this.cdRef.detectChanges();
    }, 0);
  }

  public createAsset() {
    this.assetService
      .createAsset(
        this.applicationForm.model,
        this.applicationForm.page,
        this.simulationId,
        this.wizardCode
      )
      .subscribe(res => {
        this.assetService.firstTimeAssets.push(Number.parseInt(res));
        this.goToAssetList();
      });
  }

  goToAssetList() {
    if (this.wizardCode === 'WSIM') {
      this.router.navigate([`../asset-list`], {
        relativeTo: this.activatedRoute
      });
    }
    if (this.wizardCode === 'WRPE') {
      this.router.navigate([`../appraisal-list`], {
        relativeTo: this.activatedRoute
      });
    }
  }
}
