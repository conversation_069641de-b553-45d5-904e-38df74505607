<button type="button" class="btn btn-empty pull-right" (click)="open()">{{buttonLabels[type] | translate}}</button>

<div *ngIf="isOpen" class="modal fade suspension-modal" bsModal #modalBox="bs-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" (onHidden)="hide()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <form #f="ngForm" (ngSubmit)="submitted()" novalidate>
        <div class="modal-header">
          <h2>{{titles[type] | translate}}</h2>
          <button type="reset" class="close pull-right" aria-label="Close" (click)="hide()">
            <i class="icon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-sm-12" *ngIf="isSopensione()">
              <p>{{'UBZ.SITE_CONTENT.100000000' | translate }}.</p>
            </div>
            <div *ngIf="type !== 'RIA'" class="col-sm-12 form-group">
              <label>{{ selectLabels[type] | translate }}*</label>
              <div class="custom-select">
                <select class="form-control" name="macroprocess" [(ngModel)]="model.reasonCode" name="noteType" #noteType="ngModel" required
                    [ngClass]="(f.submitted && !noteType.valid) ? 'error' : 'valid'">
                  <option disabled selected value="">{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let row of (reasonTypesDomain | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
                </select>
              </div>
              <div *ngIf="f.submitted && !noteType.valid" class="tooltip fade bottom in" role="tooltip" style="top: 67px; left: 127px;">
                <div class="tooltip-arrow" style="left: 50%;"></div>
                <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12 form-group">
              <label>{{'UBZ.SITE_CONTENT.11001011' | translate }}*</label>
              <textarea rows="4" class="form-control required" data-placement="bottom" [(ngModel)]="model.noteDesc" name="noteDesc" #noteDesc="ngModel" required
                [ngClass]="(f.submitted && !noteDesc.valid) ? 'error' : 'valid'" placeholder="{{'UBZ.SITE_CONTENT.10001011' | translate }}..."></textarea>
              <div *ngIf="f.submitted && !noteDesc.valid" class="tooltip fade bottom in" role="tooltip" style="top: 67px; left: 127px;">
                <div class="tooltip-arrow" style="left: 50%;"></div>
                <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-primary waves-effect" [disabled]="f.invalid">{{buttonLabels[type] | translate}}</button>
        </div>
      </form>
    </div>
  </div>
</div>
