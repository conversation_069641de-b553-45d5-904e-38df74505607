import { Component, OnInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { Observable } from 'rxjs/Observable';

import { DomainService } from '../../../shared/domain/domain.service';
import { Domain } from '../../../shared/domain/domain';
import { DomainTable } from '../../../shared/domain/domain-table';
import { AssetService } from '../../services/asset/asset.service';
import { AssetRequestObject } from '../../model/asset-request-object';
import { AssetSearch } from '../../model/asset-search';
import { AssetSearchResponse } from '../../model/asset-search-response';
import { AssetResponseObject } from '../../model/asset-response-object';
import { ApplicationFormComponent } from '../../../shared/application-form/components/application-form.component';

@Component({
  selector: 'app-asset-search',
  templateUrl: './asset-search.component.html'
})
export class AssetSearchComponent implements OnInit {
  @ViewChild('autoShownModal') public autoShownModal: ModalDirective;

  @ViewChild(ApplicationFormComponent)
  applicationForm: ApplicationFormComponent;

  simulationId: string;
  wizardCode: string;
  assetSearchObject = new AssetSearch();
  assetSearchResponseObject = new AssetSearchResponse();
  domainTable: DomainTable[] = [];
  private domains = [
    'domainAssetType',
    'domainProvince',
    'domainNation',
    'domainCity',
    'domainCategoryAsset'
  ];
  isForeignState = false;
  isSubaltern = false;
  isResearchDone = false;
  searchedAssetDetails: any;
  isDialogOpened = false;
  positionId: number;
  assetToBeImported: boolean[] = [];
  public selectAll = false;

  homeCity: string;
  homeAddress: string;

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private domainService: DomainService,
    public assetService: AssetService,
    private cdRef: ChangeDetectorRef
  ) {}

  public startPage() {
    setTimeout(() => {
      this.assetService.saveIsEnable = false;
      this.cdRef.detectChanges();
    }, 0);
    this.assetSearchObject = new AssetSearch();
    this.assetSearchResponseObject = new AssetSearchResponse();
    this.assetSearchObject.filter.assetType = this.assetService.familyAssetType;
    this.domainTable = [];
    this.isForeignState = false;
    this.isSubaltern = false;
    this.isResearchDone = false;
    this.searchedAssetDetails = {};
    this.activatedRoute.parent.params.subscribe((params: Params) => {
      this.simulationId = params['positionId'];
      this.wizardCode = params['wizardCode'];
    });
    for (const index in this.domains) {
      if (true) {
        this.domainTable[this.domains[index]] = new DomainTable();
      }
    }
    this.domainTable[
      `domainCategoryAsset${this.assetSearchObject.filter.assetType}`
    ] = new DomainTable();
    Observable.forkJoin(
      this.domainService.newGetDomain('UBZ_DOM_RESITEM_TYPE'),
      this.domainService.newGetDomain('UBZ_DOM_PROVINCE'),
      this.domainService.newGetDomain('UBZ_DOM_CITIZENSHIP'),
      this.domainService.newGetDomain(
        'UBZ_DOM_CATEGORY_TYPE',
        this.assetSearchObject.filter.assetType,
        true
      )
    ).subscribe(res => {
      this.domainTable['domainAssetType'].domains = res[0];
      this.domainTable['domainProvince'].domains = res[1];
      this.domainTable['domainNation'].domains = res[2];
      this.domainTable[
        `domainCategoryAsset${this.assetSearchObject.filter.assetType}`
      ].domains =
        res[3];
    });
  }

  ngOnInit() {
    this.startPage();
  }

  public setProvince(value: string) {
    this.domainTable['domainCity' + value] = new DomainTable();
    this.domainService
      .getDomainCity(value)
      .subscribe(res => (this.domainTable['domainCity' + value].domains = res));
  }

  public setIsForeignState(checked: boolean) {
    this.isForeignState = checked;

    this.assetSearchObject.filter.province = undefined;
    this.assetSearchObject.filter.zipCod = undefined;
    this.assetSearchObject.filter.streetNum = undefined;
    this.assetSearchObject.filter.country = undefined;
    this.assetSearchObject.filter.city = undefined;
    this.assetSearchObject.filter.address = undefined;
    this.homeCity = undefined;
    this.homeAddress = undefined;
  }

  public setIsSubaltern(checked: boolean) {
    this.isSubaltern = checked;
    if (checked) {
      this.assetSearchObject.filter.reRegistrySub = null;
    }
  }

  public findAssets() {
    if (!this.isForeignState) {
      this.assetSearchObject.filter.city = this.homeCity;
      this.assetSearchObject.filter.address = this.homeAddress;
    }
    this.assetService.findAsset(this.assetSearchObject).subscribe(
      response => {
        this.assetSearchResponseObject = response;
        this.isResearchDone = true;
        this.setSearchedAssetdetails();
      }
    );
  }

  public getDomain(domainName: string, rootName = '') {
    if (this.domainTable && this.domainTable[domainName + rootName]) {
      return this.domainTable[domainName + rootName].domains;
    } else {
      return null;
    }
  }

  private getDomainValueForDomCode(index: string, value: string): string {
    let ret = '';
    if (
      this.domainTable &&
      this.domainTable[index] &&
      this.domainTable[index].domains[value]
    ) {
      ret = this.domainTable[index].domains[value].translationCod;
    }
    return ret;
  }

  private setSearchedAssetdetails() {
    this.searchedAssetDetails = {};
    if (this.assetSearchObject.filter.resItemId) {
      this.searchedAssetDetails[
        'assetId'
      ] = this.assetSearchObject.filter.resItemId;
    }
    if (this.assetSearchObject.filter.assetType) {
      this.searchedAssetDetails['assetType'] = this.getDomainValueForDomCode(
        'domainAssetType',
        this.assetSearchObject.filter.assetType
      );
    }
    if (this.assetSearchObject.filter.category) {
      this.searchedAssetDetails[
        'assetCategory'
      ] = this.getDomainValueForDomCode(
        `domainCategoryAsset${this.assetSearchObject.filter.assetType
          ? this.assetSearchObject.filter.assetType
          : ''}`,
        this.assetSearchObject.filter.category
      );
    }
    if (this.assetSearchObject.filter.originNdg) {
      this.searchedAssetDetails[
        'originNdg'
      ] = this.assetSearchObject.filter.originNdg;
    }
    if (this.assetSearchObject.filter.province) {
      this.searchedAssetDetails[
        'assetProvince'
      ] = this.getDomainValueForDomCode(
        'domainProvince',
        this.assetSearchObject.filter.province
      );
    }
    if (this.assetSearchObject.filter.city && this.homeCity) {
      this.searchedAssetDetails['assetCity'] = this.getDomainValueForDomCode(
        `domainCity${this.assetSearchObject.filter.province
          ? this.assetSearchObject.filter.province
          : ''}`,
        this.assetSearchObject.filter.city
      );
    }
    if (this.assetSearchObject.filter.city && !this.homeCity) {
      this.searchedAssetDetails[
        'assetForeignCity'
      ] = this.assetSearchObject.filter.city;
    }
    if (this.assetSearchObject.filter.zipCod) {
      this.searchedAssetDetails[
        'zipCod'
      ] = this.assetSearchObject.filter.zipCod;
    }
    if (this.assetSearchObject.filter.address && this.homeAddress) {
      this.searchedAssetDetails[
        'address'
      ] = this.assetSearchObject.filter.address;
    }
    if (this.assetSearchObject.filter.address && !this.homeAddress) {
      this.searchedAssetDetails[
        'foreignAddress'
      ] = this.assetSearchObject.filter.address;
    }
    if (this.assetSearchObject.filter.streetNum) {
      this.searchedAssetDetails[
        'streetNum'
      ] = this.assetSearchObject.filter.streetNum;
    }
    if (this.assetSearchObject.filter.country) {
      this.searchedAssetDetails['assetNation'] = this.getDomainValueForDomCode(
        'domainNation',
        this.assetSearchObject.filter.country
      );
    }
    if (this.assetSearchObject.filter.buildYear) {
      this.searchedAssetDetails[
        'buildYear'
      ] = this.assetSearchObject.filter.buildYear;
    }
    if (this.assetSearchObject.filter.reRegistrySheet) {
      this.searchedAssetDetails[
        'reRegistrySheet'
      ] = this.assetSearchObject.filter.reRegistrySheet;
    }
    if (this.assetSearchObject.filter.reRegistryPart) {
      this.searchedAssetDetails[
        'reRegistryPart'
      ] = this.assetSearchObject.filter.reRegistryPart;
    }
    if (this.assetSearchObject.filter.reRegistrySub) {
      this.searchedAssetDetails[
        'reRegistrySub'
      ] = this.assetSearchObject.filter.reRegistrySub;
    }
  }

  showDialog(assetId: number): void {
    this.positionId = assetId;
    this.isDialogOpened = true;
  }

  hideDialog(): void {
    this.autoShownModal.hide();
  }

  onHidden(): void {
    this.isDialogOpened = false;
  }

  setImportedStatusForAllAssets() {
    this.selectAll = !this.selectAll;
    for (
      let index = 0;
      index < this.assetSearchResponseObject.positions.length;
      index++
    ) {
      this.assetToBeImported[
        this.assetSearchResponseObject.positions[index].resItemId
      ] = this.selectAll;
    }
  }

  changeCheckedStatus(idAsset: number) {
    this.assetToBeImported[idAsset] = !this.assetToBeImported[idAsset];
  }

  importAllSelectedAsset() {
    const idToImport = [];
    for (let index = 0; index < this.assetToBeImported.length; index++) {
      if (this.assetToBeImported[index]) {
        idToImport.push(index);
      }
    }

    this.assetService
      .importAssets(this.simulationId, idToImport, this.wizardCode)
      .subscribe(x => this.returnToAssetList());
  }

  importAsset(assetId: number) {
    this.assetService
      .importAssets(this.simulationId, [assetId], this.wizardCode)
      .subscribe(x => this.returnToAssetList());
  }

  returnToAssetList() {
    if (this.wizardCode === 'WSIM') {
      this.router.navigate([`../asset-list`], {
        relativeTo: this.activatedRoute
      });
    } else {
      this.router.navigate([`../appraisal-list`], {
        relativeTo: this.activatedRoute
      });
    }
  }

  existAnAssetToImport(): boolean {
    if (this.assetToBeImported.length === 0) {
      return false;
    }
    for (const booleanValue of this.assetToBeImported) {
      if (booleanValue) {
        return true;
      }
    }
    return false;
  }

  private refreshAssets() {
    this.selectAll = false;
    this.assetToBeImported = [];
    this.findAssets();
  }

  changePageSize() {
    this.assetSearchObject.page = 1;
    this.refreshAssets();
  }

  changePage(event: any) {
    this.assetSearchObject.page = event.page;
    this.refreshAssets();
  }
}
