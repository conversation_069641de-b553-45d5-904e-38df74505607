<app-task-access-rights [positionId]="positionId" [taskId]="taskId" [taskCod]="taskCod"
  [excludeList]="['UBZ_MAS_CHGD','UBZ_MAS_CONF']" (taskLocked)="taskLocked()" (taskUnlocked)="taskUnlocked()"
  (taskLockedByOtherUser)="tasklockedByOtherUser($event)"></app-task-access-rights>
<h4 class="section-heading">{{'UBZ.SITE_CONTENT.101101110' | translate }}</h4>
<div class="row">
  <div class="col-sm-3 form-group">
    <label *ngIf="!isOperCTEAppraisalRequest">{{'UBZ.SITE_CONTENT.1000111111' | translate }}</label>
    <label *ngIf="isOperCTEAppraisalRequest">{{'UBZ.SITE_CONTENT.1000111111' | translate }}*</label>
    <div class="custom-select">
      <select (change)="selectExpertType()" class="form-control" [(ngModel)]="_expertType">
        <ng-container *ngIf="(invoiceFlag === 'N' || invoiceFlag === null) && opinionType === 'PRI' || (invoiceFlag === 'Y' || invoiceFlag === null || invoiceFlag === 'N') && opinionType === 'SO' || opinionType === 'TO'">
          <option *ngFor="let item of ( _expertTypes | domainMapToDomainArray )" [disabled]="isExpertListLocked(item)"
            value="{{item.domCode}}">{{ item.translationCod | translate }}</option>
        </ng-container>
        <ng-container *ngIf="invoiceFlag === 'Y' &&  opinionType === 'PRI'">
          <option *ngFor="let item of ( _expertTypes | domainMapToDomainArray ).slice(-1)"
            [disabled]="isExpertListLocked(item)" value="{{item.domCode}}">{{ item.translationCod | translate }}
          </option>
        </ng-container>
      </select>
    </div>
  </div>
  <div class="col-sm-3 form-group" *ngIf="isSecondOpinion">
    <label>{{'UBZ.SITE_CONTENT.1100111010' | translate }}</label>
    <div class="custom-select">
      <select class="form-control" [(ngModel)]="surveyType">
        <option [ngValue]="undefined">{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
        <option *ngFor="let item of ( surveyTypeDomain | domainMapToDomainArray )"
          [disabled]="isSecondOpinionIndividual && item.domCode !== 'NES'" value="{{item.domCode}}">{{
          item.translationCod | translate }}</option>
      </select>
    </div>
  </div>
</div>
<table class="uc-table" *ngIf="expertListForPrinting.length > 0; else expertListIsEmpty">
  <thead>
    <tr>
      <th scope="col" class="col-sm-1"></th>
      <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.1001110' | translate }}</th>
      <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.1101101' | translate }}</th>
      <th scope="col" class="col-sm-5">{{'UBZ.SITE_CONTENT.101101111' | translate }}</th>
      <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.101110000' | translate }}</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let expert of expertListForPrinting; let index = index">
      <td data-label="">
        <div class="custom-radio">
          <input type="radio" name="ndg" id="{{index}}" class="radio" [(ngModel)]="_chosenExpert" [value]="expert"
            (change)="checkIsValid()">
          <label for="{{index}}"></label>
        </div>
      </td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.1001110' | translate }}">{{expert.ndg}}</td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.1101101' | translate }}">{{expert.heading}}</td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.101101111' | translate }}">{{expert.actQuantity}}</td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.101110000' | translate }}">{{expert.rating}}</td>
    </tr>
  </tbody>
</table>

<ng-template #expertListIsEmpty>
  <!-- <h1>{{ 'UBZ.SITE_CONTENT.1001011001' | translate }}</h1> -->
  <div class="Search__NoResults">
    <div class="Search__NoResults__Icon">
      <i class="icon-placeholder_note"></i>
    </div>
    <div class="Search__NoResults__Text">
      <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1001011001' | translate }}</h3>
      <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011010' | translate }}</p>
    </div>
  </div>
</ng-template>


<div class="row" *ngIf="searchResults?.count > 4">
  <div class="col-sm-12 top-bottom-spacing">
    <button type="button" class="btn btn-empty pull-right" (click)="toogleSeeAll()">
      <i class="fa" [ngClass]="{'fa-plus-square-o': !seeAll, 'fa-minus-square-o': seeAll}" aria-hidden="true"></i>
      <span *ngIf="!seeAll">{{'UBZ.SITE_CONTENT.101110001' | translate }}</span>
      <span *ngIf="seeAll">{{'UBZ.SITE_CONTENT.101110010' | translate }}</span>
    </button>
  </div>
</div>

<div *ngIf="isTaskLocked" class="row">
  <div class="col-sm-12">
    <button type="button" class="btn btn-primary pull-right" id="assegna" (click)="saveExpert()"
      [disabled]="getButtonDisableState()">
      {{'UBZ.SITE_CONTENT.101110011' | translate }}
    </button>
  </div>
</div>

<!-- Pagination -->
<div *ngIf="_expertType !== 'SOC'" class="row">
  <div *ngIf="searchResults.count > 10" class="col-sm-6">
    <div class="results">
      <span>{{'UBZ.SITE_CONTENT.1111111' | translate }}</span>
      <div class="custom-select form-group">
        <select class="form-control" [(ngModel)]="_pageSize" (change)="changePageSize($event)">
          <option value="10" selected>10 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{searchResults.count}}</option>
          <option *ngIf="searchResults?.count > 20" value="20">20 {{'UBZ.SITE_CONTENT.10000000' | translate }}
            {{searchResults.count}}</option>
          <option *ngIf="searchResults?.count > 30" value="30">30 {{'UBZ.SITE_CONTENT.10000000' | translate }}
            {{searchResults.count}}</option>
          <option *ngIf="searchResults?.count > 40" value="40">40 {{'UBZ.SITE_CONTENT.10000000' | translate }}
            {{searchResults.count}}</option>
          <option *ngIf="searchResults?.count > 50" value="50">50 {{'UBZ.SITE_CONTENT.10000000' | translate }}
            {{searchResults.count}}</option>
          <option *ngIf="searchResults?.count <= 50" value="{{searchResults.count}}">{{searchResults.count}}
            {{'UBZ.SITE_CONTENT.10000000' | translate }} {{searchResults.count}}</option>
        </select>
      </div>
    </div>
  </div>
  <div *ngIf="searchResults.count > 10" class="col-sm-6" class="pull-right">
    <pagination [boundaryLinks]="true" [directionLinks]="false" [totalItems]="searchResults?.count"
      [(ngModel)]="_pageNumber" [itemsPerPage]="_pageSize" [maxSize]="10" (pageChanged)="changePage($event)"
      class="pagination" previousText="&lsaquo;" nextText="&rsaquo;" firstText="&laquo;" lastText="&raquo;">
    </pagination>
  </div>
</div>
<!-- Fine pagination -->