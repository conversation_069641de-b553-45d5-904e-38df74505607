<div class="row">
  <div class="col-sm-12">
    <h3 *ngIf="appraisalType === ExpertAppraisalType.COMPLETED">{{'UBZ.SITE_CONTENT.11111000' | translate }}</h3>
    <h3 *ngIf="appraisalType === ExpertAppraisalType.IN_CHARGE">{{'UBZ.SITE_CONTENT.1011101110' | translate }}</h3>
    <h3 *ngIf="appraisalType === ExpertAppraisalType.REVOCATED">{{'UBZ.SITE_CONTENT.1011101111' | translate }}</h3>
    <ng-container>
      <table class="uc-table" *ngIf="anAppraisalExists; else noRecordFound">
        <thead>
          <tr>
            <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1101100' | translate }}</th>
            <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1001110' | translate }}</th>
            <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1101101' | translate }}</th>
            <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10000001' | translate }}</th>
            <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1111110' | translate }}</th>
            <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10111110' | translate }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let appraisal of (appraisals | slice:0:(seeAll ? appraisals.length : MAX_RESULTS_COUNT))">
            <td data-label="ID Pratica">{{appraisal.appraisalId}}</td>
            <td data-label="NDG">{{appraisal.ndg}}</td>
            <td data-label="Denominazione">{{appraisal.heading}}</td>
            <td data-label="Stato">{{ domainStatus[appraisal.phaseCode + appraisal.statusCode]?.translationStatusCod | translate  }}</td>
            <td data-label="Data inserimento">{{appraisal.insertDate}}</td>
            <td data-label="Sportello creazione">{{ appraisal.branch }}</td>
          </tr>
        </tbody>
      </table>
      <div class="row" *ngIf="appraisals?.length > MAX_RESULTS_COUNT">
        <div class="col-sm-12 top-bottom-spacing">
          <button type="button" class="btn btn-empty pull-right" (click)="toogleSeeAll()">
            <i class="fa" [ngClass]="{'fa-plus-square-o': !seeAll, 'fa-minus-square-o': seeAll}" aria-hidden="true"></i>
            <span *ngIf="!seeAll">{{'UBZ.SITE_CONTENT.101110001' | translate }}</span>
            <span *ngIf="seeAll">{{'UBZ.SITE_CONTENT.101110010' | translate }}</span>
          </button>
        </div>
      </div>
    </ng-container>
    <ng-template #noRecordFound>
      <div class="Search__NoResults">
        <div class="Search__NoResults__Icon">
          <i class="icon-placeholder_note"></i>
        </div>
        <div class="Search__NoResults__Text">
          <h3 class="Search__NoResults__Title">
            <span *ngIf="appraisalType === ExpertAppraisalType.COMPLETED">{{'UBZ.SITE_CONTENT.1011110000' | translate }}</span>
            <span *ngIf="appraisalType === ExpertAppraisalType.IN_CHARGE">{{'UBZ.SITE_CONTENT.1011110001' | translate }}</span>
            <span *ngIf="appraisalType === ExpertAppraisalType.REVOCATED">{{'UBZ.SITE_CONTENT.1011110010' | translate }}</span>
          </h3>
          <p class="Search__NoResults__Subtitle">
            {{'UBZ.SITE_CONTENT.1011110011' | translate }}
          </p>
        </div>
      </div>
    </ng-template>
  </div>
</div>
