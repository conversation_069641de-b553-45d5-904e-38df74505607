<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="unicreditheavy_italic" horiz-adv-x="1187" >
<font-face units-per-em="2048" ascent="1536" descent="-512" />
<missing-glyph horiz-adv-x="440" />
<glyph unicode="&#xfb01;" horiz-adv-x="1306" d="M-47 -319q0 16 16 71q23 76 40.5 137.5t30.5 115.5t25.5 103.5t22.5 104.5l105 535h-125q-37 0 -37 28q0 10 8 51l16 84q6 31 16.5 41.5t32.5 16.5l138 30l16 88q16 92 57 154t105.5 97.5t153 51t202.5 15.5h17q29 0 67.5 -3t79.5 -8t82 -13.5t76 -18.5q23 -6 30 -13.5 t7 -17.5q0 -14 -5.5 -37.5t-9.5 -42.5l-22 -98q-6 -29 -16.5 -37t-26.5 -8q-27 0 -99.5 15.5t-164.5 15.5q-59 0 -99.5 -6.5t-66 -20.5t-38 -34.5t-16.5 -49.5l-4 -24h615q33 0 45 -9.5t12 -27.5q0 -12 -4 -35t-14 -76l-156 -797q-4 -29 -20.5 -44t-57.5 -15h-188 q-35 0 -47.5 9t-12.5 26q0 12 4.5 32.5t10.5 49.5l120 631h-358l-96 -494q-20 -104 -39 -189t-37 -153t-37.5 -122t-40.5 -99q-16 -35 -35.5 -44t-54.5 -9h-164q-59 0 -59 43z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1439" d="M-47 -319q0 16 16 71q23 76 40.5 137.5t30.5 115.5t25.5 103.5t22.5 104.5l105 535h-125q-37 0 -37 28q0 10 8 51l16 84q6 31 16.5 41.5t32.5 16.5l138 30l16 88q33 178 150.5 248t328.5 70h17q37 0 80 -4t83.5 -11.5t76.5 -16.5t63 -17l172 24q14 2 33.5 4t35.5 2 q31 0 45.5 -12t8.5 -47l-185 -952q-6 -29 -11 -52.5t-5 -39.5q0 -29 37 -29h57q29 0 39 -8t10 -29q0 -16 -12 -78l-14 -69q-8 -41 -25.5 -55.5t-54.5 -14.5h-139q-92 0 -136.5 14.5t-70.5 44.5q-16 20 -24.5 43t-8.5 58t8 87t23 124l143 737q-35 10 -97.5 22.5t-129.5 12.5 h-6q-94 0 -130 -26.5t-47 -80.5l-6 -32h176q45 0 45 -29q0 -10 -12 -74l-20 -104q-8 -49 -62 -49h-178l-96 -494q-20 -104 -39 -189t-37 -153t-37.5 -122t-40.5 -99q-16 -35 -35.5 -44t-54.5 -9h-164q-59 0 -59 43z" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="440" />
<glyph unicode=" "  horiz-adv-x="440" />
<glyph unicode="&#x09;" horiz-adv-x="440" />
<glyph unicode="&#xa0;" horiz-adv-x="440" />
<glyph unicode="!" horiz-adv-x="522" d="M-31 111q0 94 53.5 152.5t157.5 58.5h6q66 0 106 -32t40 -97q0 -94 -53.5 -152.5t-157.5 -58.5h-6q-66 0 -106 31.5t-40 97.5zM82 457q0 25 6 61l111 756q8 53 27.5 69.5t72.5 16.5h184q41 0 57.5 -10.5t16.5 -34.5q0 -8 -3 -26.5t-19 -80.5l-175 -721q-12 -51 -31.5 -63 t-66.5 -12h-125q-55 0 -55 45z" />
<glyph unicode="&#x22;" horiz-adv-x="1101" d="M182 911l74 535q4 43 49 43h256q43 0 33 -43l-135 -535q-6 -23 -17.5 -30t-31.5 -7h-199q-35 0 -29 37zM708 911l74 535q4 43 49 43h256q43 0 33 -43l-135 -535q-6 -23 -17.5 -30t-31.5 -7h-199q-35 0 -29 37z" />
<glyph unicode="#" horiz-adv-x="1439" d="M25 383l51 117q10 23 24.5 34t46.5 11h150l84 200h-160q-57 0 -26 66l51 117q10 23 24.5 34t46.5 11h162l127 299q14 33 28.5 43t43.5 10h162q29 0 41 -11t-4 -52l-123 -289h209l127 299q14 33 28.5 43t42.5 10h162q29 0 41 -11t-4 -52l-123 -289h152q57 0 26 -66 l-51 -116q-10 -23 -24.5 -34.5t-47.5 -11.5h-153l-84 -200h164q57 0 26 -66l-51 -117q-10 -23 -24.5 -34t-47.5 -11h-164l-112 -264q-14 -33 -28.5 -43t-43.5 -10h-162q-29 0 -41 11.5t4 51.5l109 254h-209l-113 -264q-14 -33 -28.5 -43t-42.5 -10h-162q-29 0 -41 11.5 t4 51.5l109 254h-150q-57 0 -26 66zM571 545h209l84 200h-209z" />
<glyph unicode="$" d="M34 67.5q-7 14.5 1 47.5l37 166q8 35 23.5 44t43.5 -1q68 -27 140.5 -43.5t140.5 -20.5l55 285l-61 16q-141 35 -196.5 102.5t-55.5 180.5q0 31 5 77t17 99q18 84 52 137t79 88q66 51 157 73.5t233 24.5l30 158q6 29 35 29h59q29 0 23 -29l-33 -162q72 -6 137.5 -20 t114.5 -31q35 -12 35 -41q0 -10 -7 -32.5t-24 -67.5l-31 -84q-10 -25 -16 -34t-22 -9q-10 0 -17.5 1t-19.5 5q-41 10 -95.5 19.5t-107.5 13.5l-51 -254l71 -19q133 -35 188.5 -90t57.5 -159q0 -29 -5 -88.5t-23 -151.5q-33 -166 -157 -240.5t-360 -74.5h-6l-26 -138 q-6 -29 -35 -28h-60q-29 0 -22 28l27 142q-78 6 -159 20t-128 33q-37 14 -44 28.5zM500 926q0 -35 21.5 -55.5t82.5 -39.5l43 226q-78 -8 -112.5 -42t-34.5 -89zM537 262q80 8 114.5 44t34.5 124q0 29 -22.5 49.5t-77.5 36.5z" />
<glyph unicode="%" horiz-adv-x="1894" d="M135 825q0 23 3 48.5t7 50.5l15 73q35 178 126 262t269 84q125 0 199.5 -59t74.5 -176q0 -23 -3 -48.5t-7 -49.5l-14 -74q-35 -178 -127 -262t-270 -84q-125 0 -199 59.5t-74 175.5zM310 -3q-9 15 7 38l975 1272q16 20 31.5 28t48.5 8h176q27 0 36 -15t-7 -38l-975 -1272 q-16 -20 -31.5 -28t-48.5 -8h-176q-27 0 -36 15zM395 846q0 -29 14.5 -39t39.5 -10h4q35 0 55 26.5t31 85.5l22 115q8 47 8 63q0 29 -14 39.5t-39 10.5h-4q-35 0 -55.5 -27t-30.5 -86l-23 -115q-8 -47 -8 -63zM1065 217q0 23 3 48.5t7 49.5l15 74q35 178 126 262t269 84 q125 0 199.5 -59.5t74.5 -175.5q0 -23 -3 -48.5t-7 -50.5l-14 -73q-35 -178 -127.5 -262t-270.5 -84q-125 0 -198.5 59t-73.5 176zM1325 238q0 -29 14.5 -39.5t38.5 -10.5h4q35 0 55.5 27t30.5 86l23 115q8 47 8 63q0 29 -14.5 39t-38.5 10h-4q-35 0 -55.5 -26.5 t-30.5 -85.5l-23 -115q-8 -47 -8 -63z" />
<glyph unicode="&#x26;" horiz-adv-x="1284" d="M-8 293q0 156 88 255t239 152q-68 78 -91 143.5t-23 125.5q0 94 37 163.5t101.5 116.5t151.5 70.5t185 23.5h12q174 0 277.5 -72.5t103.5 -207.5q0 -78 -27.5 -141.5t-74.5 -115.5t-108.5 -94t-131.5 -75l154 -209q16 33 28.5 74t20.5 80l6 30q4 18 12.5 24.5t30.5 6.5 h160q31 0 31 -22q0 -10 -2.5 -20.5t-6.5 -35.5q-20 -104 -54 -190t-77 -146l86 -118q6 -10 9.5 -15.5t3.5 -15.5q0 -18 -25 -37l-164 -109q-23 -14 -33 -14q-16 0 -28 17l-88 120q-66 -37 -157 -56t-198 -19h-22q-207 0 -316.5 81.5t-109.5 229.5zM330 346q0 -53 36.5 -91 t120.5 -38h7q88 0 157 33l-217 291q-49 -35 -76.5 -82t-27.5 -113zM520 971q0 -37 23.5 -77t54.5 -81l12 -16q55 31 100.5 85t45.5 126q0 41 -27 61.5t-70 20.5h-6q-55 0 -94 -30t-39 -89z" />
<glyph unicode="'" horiz-adv-x="575" d="M182 911l74 535q4 43 49 43h256q43 0 33 -43l-135 -535q-6 -23 -17.5 -30t-31.5 -7h-199q-35 0 -29 37z" />
<glyph unicode="(" horiz-adv-x="702" d="M61 373q0 274 129.5 523t356.5 450q25 23 40 31.5t31 8.5q20 0 48 -16l88 -63q35 -25 34 -41q0 -23 -26 -52q-180 -190 -280.5 -396t-100.5 -458q0 -102 26.5 -211.5t81.5 -226.5q12 -25 13 -41q0 -23 -37 -43l-123 -65q-25 -12 -41 -13q-27 0 -49 33 q-86 121 -138.5 271.5t-52.5 308.5z" />
<glyph unicode=")" horiz-adv-x="702" d="M-115 -119q0 23 27 51q180 190 280.5 396.5t100.5 457.5q0 102 -26.5 212t-82.5 227q-12 25 -12 41q0 23 37 43l123 65q25 12 41 12q27 0 49 -32q86 -121 138 -271.5t52 -308.5q0 -274 -129 -523t-356 -450q-25 -23 -40 -32t-32 -9q-20 0 -47 17l-88 63q-35 25 -35 41z " />
<glyph unicode="*" horiz-adv-x="856" d="M164 1200q0 6 3 15.5t17 35.5l58 111q12 27 37 27q12 0 28 -11l144 -88l10 217q2 37 43 37h164q29 0 28 -22q0 -6 -4 -19.5t-10 -34.5l-59 -178l170 86q12 6 20 10.5t16 4.5q20 0 27 -35l16 -94q4 -18 5.5 -28.5t1.5 -21.5q0 -27 -41 -34l-215 -54l131 -118 q14 -12 18 -20.5t4 -18.5q0 -20 -31 -41l-112 -78q-16 -10 -31 -10q-10 0 -17 8t-12 18l-77 156l-142 -168q-16 -23 -35 -23q-12 0 -26 11l-103 82q-10 8 -16 16t-6 20q0 18 25 37l190 135l-193 48q-18 4 -22 10t-4 12z" />
<glyph unicode="+" d="M70 461l24 127q10 49 25.5 65.5t56.5 16.5h258l51 268q8 41 23.5 55.5t69.5 14.5h151q49 0 62.5 -15.5t3.5 -66.5l-50 -256h256q41 0 51.5 -18.5t2.5 -55.5l-25 -127q-10 -49 -25.5 -65.5t-56.5 -16.5h-258l-51 -268q-8 -41 -23.5 -55.5t-68.5 -14.5h-152 q-49 0 -62.5 15.5t-2.5 66.5l49 256h-256q-41 0 -51.5 18.5t-1.5 55.5z" />
<glyph unicode="," horiz-adv-x="503" d="M-127 -291q-33 23 2 58q27 29 50.5 54t41.5 48q27 29 43.5 50.5t16.5 41.5q0 10 -13 27l-20 18q-27 25 -27 64q0 29 17 110q16 82 61 120t131 38h21q78 0 109.5 -27.5t33.5 -83.5q0 -14 -2 -33.5t-10 -56.5q-8 -41 -31 -96t-60.5 -118.5t-91 -131t-122.5 -131.5 q-35 -31 -74 -4z" />
<glyph unicode="-" horiz-adv-x="745" d="M33 451v153q0 37 17.5 51.5t64.5 14.5h450q47 0 64.5 -14.5t17.5 -51.5v-153q0 -37 -15.5 -51.5t-66.5 -14.5h-450q-51 0 -66.5 14.5t-15.5 51.5z" />
<glyph unicode="." horiz-adv-x="493" d="M-31 84q0 23 4 51.5t9 50.5q23 90 69.5 121t122.5 31h19q139 0 139 -96q0 -23 -3 -51.5t-12 -57.5q-23 -90 -68.5 -120.5t-121.5 -30.5h-18q-74 0 -107 26.5t-33 75.5z" />
<glyph unicode="/" horiz-adv-x="831" d="M-111 -14q0 10 6.5 25.5t28.5 60.5l674 1261q23 41 47.5 56.5t67.5 15.5h182q47 0 47 -33q0 -10 -6 -25.5t-29 -60.5l-674 -1261q-23 -41 -47 -56.5t-67 -15.5h-180q-49 0 -50 33z" />
<glyph unicode="0" d="M37 367q0 86 19.5 212t56.5 265q37 137 90 232t125.5 154.5t168 86t216.5 26.5h4q213 0 323.5 -93t110.5 -292q0 -86 -19.5 -211.5t-56.5 -265.5q-37 -137 -90 -232t-126 -154.5t-168 -86t-216 -26.5h-4q-213 0 -323.5 93t-110.5 292zM375 389q0 -72 31.5 -101.5 t101.5 -29.5h8q100 0 152.5 71.5t91.5 231.5q53 219 53 375q0 72 -31.5 101.5t-101.5 29.5h-8q-100 0 -152.5 -71.5t-91.5 -231.5q-53 -219 -53 -375z" />
<glyph unicode="1" d="M84 37q0 8 2 22.5t6 30.5l21 109q8 41 23.5 53t53.5 12h222l141 721h-10l-189 -80q-31 -12 -45 -12q-27 0 -39 29l-47 129q-4 12 -8 22t-4 21q0 27 41 45l338 147q29 12 49 19.5t38.5 12.5t37 6t41.5 1h135q49 0 49 -37q0 -16 -3 -38.5t-11 -63.5l-178 -922h213 q33 0 43 -8t10 -29q0 -8 -2 -22.5t-6 -30.5l-21 -108q-8 -41 -23.5 -53.5t-54.5 -12.5h-770q-33 0 -43 8t-10 29z" />
<glyph unicode="2" d="M16 72l25 116q20 100 48 178t74 140.5t115.5 110t174.5 85.5l129 48q66 25 106.5 46t63 44.5t31 50.5t8.5 61q0 113 -152 113h-4q-59 0 -127 -13.5t-139 -35.5q-37 -10 -51.5 -2t-22.5 39l-37 141q-8 29 1 47.5t44 30.5q90 33 192.5 52t200.5 19h13q94 0 171.5 -18 t133 -60t86 -108.5t30.5 -165.5q0 -184 -99 -288.5t-283 -170.5l-99 -34q-78 -29 -127 -53.5t-79.5 -51t-46 -58.5t-23.5 -71h538q35 0 46.5 -16.5t5.5 -50.5l-27 -136q-8 -37 -21.5 -49t-54.5 -12h-786q-41 0 -54.5 15.5t-3.5 56.5z" />
<glyph unicode="3" d="M2 90q-4 21 6 49l39 123q10 33 26.5 44t63.5 -5q39 -12 80 -22.5t80 -17.5t72.5 -11t58.5 -4h6q115 0 178.5 47t63.5 162q0 53 -43 77.5t-141 24.5h-103q-41 0 -51 11.5t-2 52.5l24 127q8 37 22.5 53t57.5 16h52q72 0 118.5 9.5t77.5 31.5q55 37 55 111q0 61 -33.5 84.5 t-99.5 23.5h-2q-53 0 -127.5 -14t-142.5 -35q-33 -10 -50.5 -5t-25.5 32l-37 135q-10 39 0.5 54.5t40.5 27.5q37 14 87 27.5t103.5 22.5t106.5 15t101 6h26q188 0 291.5 -72.5t103.5 -234.5q0 -111 -57 -199.5t-172 -129.5l-2 -7q78 -25 120 -79t42 -148q0 -229 -147.5 -360 t-438.5 -131h-8q-45 0 -97.5 6t-102.5 15t-97 22.5t-84 27.5q-35 16 -39 37z" />
<glyph unicode="4" d="M-10 350l10 51q4 18 8 33t12.5 29t20.5 32.5t35 47.5l559 717q31 41 54.5 53t72.5 12h256q43 0 59.5 -13.5t7.5 -54.5l-139 -708h115q33 0 45 -11.5t6 -41.5l-31 -162q-6 -29 -19 -40t-48 -11h-119l-41 -215q-8 -43 -24.5 -55.5t-57.5 -12.5h-184q-41 0 -55.5 13.5 t-6.5 54.5l41 215h-514q-76 0 -63 67zM358 549h263l90 455z" />
<glyph unicode="5" d="M14 109q0 10 3 21t8 28l34 108q10 35 27.5 49.5t58.5 -2.5q84 -31 163 -49t138 -18h11q115 0 184.5 61.5t69.5 175.5q0 57 -36 90t-112 33h-4q-47 0 -96 -13t-111 -36q-29 -10 -48 -9t-44 13l-72 35q-31 14 -30 45q0 12 4 29.5t8 38.5l135 555q8 35 26.5 48t55.5 13h660 q39 0 51 -9t4 -50l-31 -156q-6 -29 -20.5 -42t-55.5 -13h-420q-12 0 -19 -4t-11 -23l-47 -197q43 14 102 24.5t113 10.5h10q152 0 240 -77.5t88 -243.5q0 -117 -37 -220.5t-112 -179t-187.5 -119.5t-261.5 -44h-11q-47 0 -98 6t-101 15t-96.5 22.5t-83.5 27.5 q-29 10 -39 23.5t-10 32.5z" />
<glyph unicode="6" d="M78 428q0 86 14 182.5t44 191.5t73 184t98 159q90 111 204 154.5t259 43.5q78 0 163 -15t140 -38q35 -14 39 -32.5t-6 -53.5l-43 -121q-12 -35 -30.5 -41t-55.5 5q-47 12 -98.5 22t-98.5 10h-12q-94 0 -149.5 -33.5t-96.5 -113.5q-14 -29 -27.5 -58.5t-23.5 -60.5 q59 25 123.5 37t120.5 12h6q154 0 249 -73.5t95 -245.5q0 -137 -39 -241.5t-107.5 -175.5t-165 -107.5t-210.5 -36.5h-25q-233 0 -336.5 119.5t-103.5 326.5zM403 408q0 -76 32 -118t106 -42h18q49 0 83 21.5t55.5 55t30.5 76.5t9 88q0 59 -34.5 84t-98.5 25h-6 q-41 0 -90 -7t-92 -20q-12 -86 -13 -163z" />
<glyph unicode="7" d="M56 19.5q-7 19.5 16 48.5l708 985h-504q-37 0 -45 17t-2 52l25 131q8 43 22.5 57.5t61.5 14.5h797q41 0 54 -11t7 -46l-12 -78q-6 -41 -30 -85t-77 -120l-643 -930q-23 -35 -47.5 -45t-67.5 -10h-213q-43 0 -50 19.5z" />
<glyph unicode="8" d="M20 315q0 117 73 214.5t183 154.5q-53 47 -76.5 107.5t-23.5 138.5q0 98 39 176t107.5 130t164 79.5t205.5 27.5h13q104 0 185 -23.5t134 -65.5t81 -99t28 -125q0 -57 -17.5 -109.5t-47.5 -98.5t-67.5 -83t-80.5 -63q72 -47 107.5 -108.5t35.5 -149.5q0 -86 -31 -163 t-94 -136q-68 -63 -174.5 -100t-255.5 -37h-21q-238 0 -356 94q-111 86 -111 239zM369 381q0 -55 36.5 -98t124.5 -43h13q80 0 128 34.5t48 100.5q0 53 -35 85t-127 75l-82 36q-45 -31 -75.5 -82t-30.5 -108zM512 961q0 -47 28.5 -78t94.5 -62l72 -35q35 35 65.5 85.5 t30.5 103.5q0 59 -35 90t-94 31h-4q-66 0 -112 -34.5t-46 -100.5z" />
<glyph unicode="9" d="M76 67.5q-4 18.5 6 53.5l43 121q12 35 30.5 41t55.5 -4q47 -12 98 -22.5t99 -10.5h12q94 0 149.5 33.5t96.5 113.5q27 49 49 119q-57 -25 -122 -37t-120 -12h-6q-154 0 -249 73.5t-95 245.5q0 137 38 241.5t107.5 175.5t165.5 107.5t211 36.5h25q233 0 336.5 -119.5 t103.5 -326.5q0 -86 -14.5 -182.5t-44 -191.5t-72.5 -184t-98 -159q-90 -111 -204 -154.5t-259 -43.5q-78 0 -163 15t-140 38q-35 14 -39 32.5zM451 836q0 -59 34.5 -84t98.5 -25h6q41 0 90 7t92 20q12 86 12 164q0 76 -31.5 117.5t-105.5 41.5h-18q-49 0 -83 -21.5 t-55.5 -55t-30.5 -76.5t-9 -88z" />
<glyph unicode=":" horiz-adv-x="493" d="M-31 84q0 23 4 51.5t9 50.5q23 90 69.5 121t122.5 31h19q139 0 139 -96q0 -23 -3 -51.5t-12 -57.5q-23 -90 -68.5 -120.5t-121.5 -30.5h-18q-74 0 -107 26.5t-33 75.5zM121 864q0 23 4 51.5t9 50.5q23 90 69.5 121t122.5 31h19q139 0 139 -96q0 -23 -3 -51.5t-12 -57.5 q-23 -90 -68.5 -120.5t-121.5 -30.5h-18q-74 0 -107 26.5t-33 75.5z" />
<glyph unicode=";" horiz-adv-x="503" d="M-123 -291q-33 23 2 58q27 29 50.5 54t41.5 48q27 29 43.5 50.5t16.5 41.5q0 10 -13 27l-20 18q-27 25 -27 64q0 29 17 110q16 82 61 120t131 38h21q78 0 109.5 -27.5t33.5 -83.5q0 -14 -2 -33.5t-10 -56.5q-8 -41 -31 -96t-60.5 -118.5t-91 -131t-122.5 -131.5 q-35 -31 -74 -4zM114 864q0 23 4 51.5t9 50.5q23 90 69.5 121t122.5 31h19q139 0 139 -96q0 -23 -3 -51.5t-12 -57.5q-23 -90 -68.5 -120.5t-121.5 -30.5h-18q-74 0 -107 26.5t-33 75.5z" />
<glyph unicode="&#x3c;" d="M92 442.5q-6 16.5 0 51.5l14 77q8 39 19.5 53.5t46.5 30.5l797 394q25 12 41 12q29 0 43 -39l41 -111q14 -39 14 -57t-13.5 -29.5t-39.5 -25.5l-549 -267l442 -262q41 -25 41 -47q0 -20 -31 -63l-90 -119q-31 -39 -55 -39q-20 0 -39 12l-649 396q-27 16 -33 32.5z" />
<glyph unicode="=" d="M27 209l24 127q10 49 25.5 65.5t56.5 16.5h825q41 0 51.5 -18.5t2.5 -55.5l-25 -127q-10 -49 -25.5 -65.5t-56.5 -16.5h-825q-41 0 -51.5 18.5t-1.5 55.5zM125 713l25 127q10 49 25 65.5t56 16.5h826q41 0 51 -18.5t2 -55.5l-25 -127q-10 -49 -25.5 -65.5t-55.5 -16.5 h-826q-41 0 -51 18.5t-2 55.5z" />
<glyph unicode="&#x3e;" d="M37 209q0 18 13 29.5t40 25.5l549 266l-442 263q-41 25 -41 47q0 20 30 63l90 119q31 39 56 39q20 0 39 -12l649 -396q27 -16 33 -32.5t0 -51.5l-15 -77q-8 -39 -19 -53.5t-46 -30.5l-797 -394q-25 -12 -41 -12q-29 0 -43 39l-41 111q-14 39 -14 57z" />
<glyph unicode="?" horiz-adv-x="948" d="M74 111q0 94 53 152.5t158 58.5h6q66 0 105.5 -32t39.5 -97q0 -94 -53 -152.5t-158 -58.5h-6q-66 0 -105.5 31.5t-39.5 97.5zM144.5 1248.5q5.5 15.5 43.5 29.5q84 31 190.5 48t203.5 17h8q188 0 276 -74.5t88 -213.5q0 -80 -14 -137.5t-47 -101.5t-83 -81t-122 -73 l-70 -37q-53 -29 -86.5 -49.5t-54 -40t-31 -39.5t-16.5 -47q-6 -35 -53 -35h-168q-33 0 -33 24q0 111 46 194t175 150l70 37q39 20 67.5 39t47 38t27.5 43t9 56q0 45 -30.5 63.5t-89.5 18.5h-6q-47 0 -116 -13t-134 -34q-55 -18 -64 23l-33 151q-6 29 -0.5 44.5z" />
<glyph unicode="@" horiz-adv-x="1679" d="M78 526q0 166 54 325t175 286q123 127 291 192.5t367 65.5q133 0 247.5 -36t199.5 -106.5t133 -176t48 -242.5q0 -106 -28.5 -206t-84 -177.5t-138 -124.5t-189.5 -47q-92 0 -154.5 26.5t-87.5 73.5h-6q-29 -33 -78 -52.5t-108 -19.5h-4q-113 0 -163 58.5t-50 150.5 q0 45 11 112.5t30 135.5q37 135 99.5 195.5t176.5 60.5q66 0 110 -22.5t68 -57.5l17 31q8 16 16 21t31 5h113q23 0 31.5 -7t4.5 -29l-69 -345q-14 -72 -15 -110q0 -29 13.5 -46.5t48.5 -17.5q47 0 79.5 35t52 88t28 116.5t8.5 123.5q0 186 -114 283.5t-310 97.5 q-125 0 -229.5 -42t-182.5 -124q-86 -90 -127 -214t-41 -261q0 -193 118 -297.5t359 -104.5q111 0 214.5 12.5t183.5 30.5q27 6 36 1t13 -25l20 -101q4 -27 -3 -37t-36 -20q-43 -12 -98 -24.5t-115.5 -21.5t-124 -14t-124.5 -5q-352 0 -534.5 155.5t-182.5 431.5zM745 539 q0 -27 11.5 -43.5t40.5 -16.5q35 0 56 24.5t34 84.5l24 121q14 66 15 79q0 51 -56 52q-33 0 -54 -20.5t-36 -69.5q-8 -27 -14 -57.5t-11 -59.5t-7.5 -53.5t-2.5 -40.5z" />
<glyph unicode="A" horiz-adv-x="1239" d="M-102 39q0 23 7 39t19 43q70 145 144.5 294.5t148.5 293t144.5 276.5t132.5 248q29 51 64.5 71.5t96.5 20.5h176q61 0 90 -20.5t35 -71.5q35 -256 66 -519t61 -556q4 -35 6.5 -60.5t2.5 -48.5q0 -25 -17.5 -37t-64.5 -12h-181q-47 0 -67.5 14.5t-24.5 53.5l-14 170h-383 l-80 -170q-18 -39 -38.5 -53.5t-82.5 -14.5h-182q-59 0 -59 39zM451 508h264q-4 98 -12.5 224t-16.5 235h-22q-53 -113 -108.5 -232t-104.5 -227z" />
<glyph unicode="B" horiz-adv-x="1165" d="M12 61q0 23 3 46.5t12 60.5l209 1092q8 39 25.5 52t60.5 17q70 8 148.5 11t162.5 3h31q242 0 342 -69.5t100 -200.5q0 -115 -47 -215t-162 -153v-7q78 -18 123 -71.5t45 -147.5q0 -90 -24.5 -183t-98.5 -165q-72 -70 -189.5 -100.5t-287.5 -30.5h-29q-111 0 -205 3 t-163 9q-55 6 -56 49zM393 299q0 -16 27 -18q14 -2 34.5 -3.5t41.5 -1.5h10q80 0 124 20.5t64 65.5q12 29 16.5 54.5t4.5 58.5q0 51 -33 70.5t-104 19.5h-136l-41 -217q-8 -39 -8 -49zM489 809h78q139 0 174 102q6 18 9.5 40t3.5 38q0 45 -28 61.5t-81 16.5h-14 q-20 0 -41 -1t-35 -3q-18 -4 -23 -23z" />
<glyph unicode="C" horiz-adv-x="1136" d="M57 342q0 25 4.5 63.5t12.5 96t22.5 136.5t36.5 183q33 154 75 248t118 158q70 59 170 87.5t243 28.5h15q98 0 191 -20t171 -61q27 -14 27 -31q0 -12 -6.5 -31.5t-24.5 -60.5l-53 -117q-14 -31 -35 -31q-20 0 -41 10q-53 25 -110.5 40.5t-122.5 15.5h-9q-125 0 -188 -70 q-16 -16 -27.5 -36.5t-20.5 -48.5t-18.5 -65.5t-19.5 -93.5q-29 -139 -40 -219t-11 -131q0 -66 43 -95.5t157 -29.5h13q59 0 130.5 13.5t127.5 33.5q23 8 37 9q10 0 18 -8.5t12 -30.5l13 -90q4 -33 7 -54.5t3 -38.5q0 -35 -74 -61q-68 -25 -167 -42t-206 -17h-20 q-295 0 -393 137q-25 35 -42.5 89t-17.5 134z" />
<glyph unicode="D" horiz-adv-x="1280" d="M12 66q0 10 5.5 38.5t19.5 96.5l199 1034q10 49 30.5 63.5t55.5 18.5q135 12 348 12h22q188 0 295 -37t160 -104q57 -74 71 -180q5 -37 4 -82q0 -84 -16 -197q-18 -131 -42.5 -226t-55.5 -164t-67.5 -116t-82.5 -84q-94 -78 -229 -108.5t-332 -30.5h-43q-78 0 -158.5 2 t-121.5 6q-61 6 -62 58zM401 309q0 -14 7.5 -18t19.5 -6q18 -2 39.5 -2h38.5h18q66 0 112 14t81 45q25 20 43 44t33.5 56.5t29 77.5t27.5 107q16 68 25.5 136t9.5 109q0 78 -35 121q-25 31 -66 43.5t-104 12.5h-18q-18 0 -44.5 -1.5t-42.5 -3.5q-23 -2 -30.5 -9t-12.5 -25 l-112 -590q-18 -102 -19 -111z" />
<glyph unicode="E" horiz-adv-x="1067" d="M45 147q0 72 17.5 203t52.5 336q23 129 44 222t38 157q23 82 52.5 132t73.5 79t103 39t139 10h490q29 0 38 -5t9 -22q0 -8 -3 -27.5t-12 -62.5l-18 -100q-8 -37 -19.5 -49t-60.5 -12h-424q-29 0 -40 -9.5t-17 -31.5q-10 -35 -22.5 -93.5t-22.5 -113.5h420q31 0 43 -5.5 t12 -21.5t-3 -37.5t-13 -62.5l-19 -84q-6 -27 -20.5 -37t-46.5 -10h-426q-6 -33 -13.5 -72t-11.5 -74q-4 -29 -6 -49t-2 -37q0 -31 37 -30h426q29 0 41 -6.5t12 -28.5q0 -12 -3 -34t-9 -60l-15 -87q-8 -37 -23.5 -50t-58.5 -13h-534q-115 0 -160 30.5t-45 116.5z" />
<glyph unicode="F" horiz-adv-x="1050" d="M8 33q0 10 4 29.5t13 60.5l186 962q12 59 31.5 104.5t54.5 78.5t86 45t111 12h567q45 0 45 -33q0 -10 -4 -35.5t-12 -66.5l-17 -86q-6 -33 -22.5 -45t-57.5 -12h-411q-39 0 -47 -39l-41 -215h393q31 0 43 -6.5t12 -22.5q0 -14 -4 -41t-10 -61l-13 -72q-6 -33 -21 -44 t-54 -11h-398l-90 -469q-4 -23 -9 -35.5t-14.5 -19.5t-23.5 -9t-39 -2h-190q-37 0 -52.5 5t-15.5 28z" />
<glyph unicode="G" horiz-adv-x="1273" d="M57 332q0 23 2 53.5t9.5 80.5t20.5 124.5t36 189.5q18 98 38.5 169t44 123t53.5 89t67 68q68 55 173 84.5t273 29.5h8q104 0 215 -22.5t195 -65.5q16 -8 20.5 -16t4.5 -18q0 -12 -4.5 -25.5t-14.5 -36.5l-59 -137q-10 -23 -19.5 -29t-21.5 -6q-8 0 -21.5 3t-52.5 18 q-49 18 -117.5 33.5t-132.5 15.5h-8q-76 0 -125 -15.5t-82 -48.5q-16 -16 -28.5 -37.5t-23.5 -51.5t-20.5 -69.5t-19.5 -95.5q-16 -88 -26.5 -145t-16.5 -94t-8 -59.5t-2 -41.5q0 -68 35 -96q43 -37 131 -37h12q33 0 66.5 2t60.5 11q23 6 34 15t15 34l39 188h-129 q-37 0 -37 25q0 10 2 23.5t6 37.5l21 107q8 39 22.5 51t48.5 12h385q51 0 52 -35q0 -12 -5.5 -42.5t-15.5 -76.5l-96 -477q-6 -33 -21.5 -50t-54.5 -34q-74 -33 -189.5 -54t-238.5 -21h-22q-166 0 -265.5 34.5t-144.5 92.5q-35 43 -51.5 93t-16.5 130z" />
<glyph unicode="H" horiz-adv-x="1333" d="M12 35q0 6 2 24.5t13 65.5l221 1147q6 35 19.5 44t60.5 9h198q39 0 51.5 -8t12.5 -25q0 -16 -4 -32.5t-11 -51.5l-77 -403h391l88 457q8 43 22.5 53t61.5 10h196q39 0 51.5 -7t12.5 -24q0 -16 -5 -36.5t-9 -49.5l-222 -1145q-8 -43 -21 -53t-60 -10h-203q-57 0 -58 35 q0 6 2.5 24.5t12.5 65.5l76 401h-392l-88 -463q-8 -37 -25.5 -50t-58.5 -13h-200q-57 0 -58 35z" />
<glyph unicode="I" horiz-adv-x="616" d="M18 33q0 16 4.5 37.5t8.5 46.5l225 1155q6 35 19.5 44t60.5 9h199q39 0 51 -8t12 -25q0 -16 -5 -35.5t-9 -48.5l-224 -1155q-6 -35 -19 -44t-60 -9h-199q-39 0 -51.5 8t-12.5 25z" />
<glyph unicode="J" horiz-adv-x="790" d="M-61 35q0 16 3 35.5t11 54.5l16 84q10 47 26.5 60.5t57.5 13.5h64q84 0 120.5 35.5t55.5 127.5l158 816q8 43 23 53t63 10h206q35 0 43.5 -6t8.5 -23q0 -16 -15 -86l-143 -737q-20 -104 -39.5 -172t-41 -113t-46.5 -71.5t-55 -50.5q-55 -43 -134 -54.5t-192 -11.5h-133 q-57 0 -57 35z" />
<glyph unicode="K" horiz-adv-x="1208" d="M12 33q0 8 3 26.5t12 59.5l221 1143q10 43 25.5 53t56.5 10h198q37 0 50.5 -5t13.5 -22q0 -14 -5 -41.5t-18 -89.5l-67 -352h14q12 0 21.5 8t31.5 33l342 418q25 29 48.5 40t64.5 11h223q49 -1 59 -19q2 -4 3 -9q0 -18 -27 -50l-442 -518q-27 -29 -41.5 -45t-14.5 -33 q0 -12 6.5 -25.5t22.5 -43.5l254 -484q23 -39 23 -61q0 -18 -11.5 -27.5t-40.5 -9.5h-229q-41 0 -59.5 12.5t-34.5 42.5l-203 416q-18 39 -27.5 54.5t-21.5 15.5h-14l-95 -484q-6 -35 -20.5 -46t-61.5 -11h-211q-27 0 -38 5t-11 28z" />
<glyph unicode="L" horiz-adv-x="968" d="M37 135q0 14 1 28.5t3 33t7 46t13 70.5l185 949q8 43 22.5 53t61.5 10h211q49 0 49 -27q0 -14 -5 -40.5t-18 -88.5l-164 -843q-2 -8 -2 -14q0 -33 43 -33h334q53 0 53 -33q0 -10 -3 -28.5t-11 -59.5l-18 -92q-8 -39 -21.5 -52.5t-62.5 -13.5h-490q-104 0 -146 35t-42 100 z" />
<glyph unicode="M" horiz-adv-x="1644" d="M-6 41q0 14 5 37.5t9 46.5q29 131 60.5 272.5t64.5 282.5t66.5 278.5t64.5 260.5q18 66 43 86t80 20h178q72 0 99.5 -20.5t33.5 -77.5q20 -211 38 -384t34 -339h14q43 82 88.5 173t91.5 184t91 185.5t88 180.5q29 57 63.5 77.5t106.5 20.5h203q45 0 65.5 -12.5 t20.5 -46.5q0 -23 -5.5 -61t-13.5 -91q-16 -100 -34.5 -225t-39 -263.5t-44 -280.5t-48.5 -275q-8 -41 -24.5 -55.5t-67.5 -14.5h-172q-41 0 -53.5 9t-12.5 28q0 23 6.5 52.5t16.5 76.5q29 147 59.5 314t61.5 347h-8q-92 -182 -171 -336.5t-145 -277.5q-25 -47 -52.5 -59.5 t-74.5 -12.5h-129q-49 0 -68.5 15.5t-25.5 56.5q-16 113 -35.5 274.5t-36.5 339.5h-8q-23 -98 -43 -199.5t-40.5 -199.5t-39 -189.5t-32.5 -168.5q-8 -43 -24.5 -56.5t-68.5 -13.5h-163q-41 0 -56.5 9t-15.5 32z" />
<glyph unicode="N" horiz-adv-x="1316" d="M12 37q0 14 4 33.5t9 48.5l221 1138q8 41 26.5 54.5t55.5 13.5h82q47 0 70.5 -12.5t39.5 -44.5l330 -678h4l131 665q8 43 26.5 56.5t57.5 13.5h174q61 0 62 -35q0 -10 -5.5 -35.5t-11.5 -56.5l-219 -1130q-8 -41 -27.5 -54.5t-54.5 -13.5h-84q-43 0 -67.5 11.5 t-42.5 45.5l-334 660h-4l-125 -647q-8 -43 -25.5 -56.5t-58.5 -13.5h-176q-35 0 -46.5 8t-11.5 29z" />
<glyph unicode="O" horiz-adv-x="1296" d="M57 352q0 43 12.5 143.5t43.5 258.5q18 98 40.5 175t53.5 137t71.5 106.5t96.5 82.5q129 88 393 88h16q117 0 207 -24.5t144 -71.5q104 -92 104 -256q0 -70 -12 -162.5t-41 -244.5q-20 -104 -41 -182t-48.5 -137.5t-64.5 -103.5t-86 -79q-74 -51 -175 -75.5t-245 -24.5 h-22q-117 0 -208 26.5t-140 69.5q-53 47 -76 112.5t-23 161.5zM410 418q0 -68 26 -101q39 -49 127 -49h25q47 0 80.5 12.5t62.5 36.5q43 37 68.5 115t48.5 193q23 121 32 182t9 102q0 70 -25 101q-39 47 -127 47h-24q-47 0 -81 -12.5t-63 -36.5q-41 -35 -67.5 -114 t-48.5 -194q-23 -117 -33 -179t-10 -103z" />
<glyph unicode="P" horiz-adv-x="1177" d="M12 33q0 10 5.5 33.5t11.5 56.5l217 1128q8 43 26.5 59.5t69.5 20.5q141 12 340 12h25q111 0 196.5 -15t144 -54t89 -105.5t30.5 -169.5q0 -53 -11 -120.5t-35.5 -136t-65.5 -128t-103 -96.5q-82 -49 -185 -66.5t-241 -17.5h-98l-72 -368q-4 -23 -9 -35.5t-14 -19.5 t-23.5 -9t-39.5 -2h-190q-37 0 -52.5 5t-15.5 28zM481 715h107q96 0 144 31.5t71 115.5q16 66 16 107q0 37 -18 61q-31 37 -121 37h-16q-23 0 -52.5 -1t-48.5 -5q-16 -2 -20 -25z" />
<glyph unicode="Q" horiz-adv-x="1296" d="M57 352q0 43 12.5 143.5t43.5 258.5q18 98 40.5 175t53.5 137t71.5 106.5t96.5 82.5q129 88 393 88h16q117 0 207 -24.5t144 -71.5q104 -92 104 -256q0 -70 -12.5 -163t-40.5 -244q-20 -104 -41 -182t-48.5 -137.5t-64.5 -103.5t-86 -79q-100 -70 -256 -90 q43 -49 99.5 -103.5t109.5 -95.5q27 -20 27 -39q0 -16 -10.5 -26.5t-28.5 -24.5l-129 -100q-29 -20 -49 -21q-27 0 -45 19q-41 35 -78 81t-71 97t-60.5 105.5t-44.5 103.5q-84 8 -152 31.5t-102 58.5q-53 47 -76 112.5t-23 161.5zM410 418q0 -68 26 -101q39 -49 127 -49h25 q47 0 80.5 12.5t62.5 36.5q43 37 68.5 115t48.5 193q23 121 32 182t9 102q0 70 -25 101q-39 47 -127 47h-24q-47 0 -81 -12.5t-63 -36.5q-41 -35 -67.5 -114t-48.5 -194q-23 -117 -33 -179t-10 -103z" />
<glyph unicode="R" horiz-adv-x="1210" d="M16 63l230 1184q8 43 26.5 60.5t59.5 21.5q57 6 149 10t197 4h22q102 0 189.5 -13t152 -48t100.5 -95.5t36 -154.5q0 -88 -18.5 -170t-58.5 -151.5t-103.5 -122.5t-155.5 -82l180 -424q8 -18 10 -26.5t2 -18.5q0 -16 -12 -26.5t-43 -10.5h-211q-53 0 -71.5 11.5 t-28.5 45.5l-138 394q-4 12 -10 17t-24 5h-60l-78 -410q-8 -37 -22 -50t-62 -13h-200q-47 0 -55.5 14.5t-2.5 48.5zM489 745h109q63 0 100 10.5t60 28.5q35 29 51 85.5t16 103.5q0 55 -30.5 73.5t-104.5 18.5h-35h-38.5t-32.5 -2q-16 -2 -26.5 -10.5t-14.5 -34.5z" />
<glyph unicode="S" horiz-adv-x="1099" d="M-19.5 67.5q-7.5 14.5 1.5 47.5l36 170q8 35 24.5 44t45.5 -1q80 -29 165 -46.5t157 -17.5h10q86 0 130 19.5t62 62.5q16 39 17 84q0 35 -25.5 58.5t-103.5 41.5l-125 29q-143 33 -201.5 102.5t-58.5 182.5q0 51 8 106.5t26.5 108.5t48 101t74.5 83q66 53 160 76.5 t236 23.5h8q92 0 184 -16t158 -41q35 -12 35 -41q0 -10 -7.5 -31.5t-23.5 -66.5l-35 -92q-12 -35 -28.5 -41t-47.5 4q-66 18 -126 28.5t-119 10.5h-11q-106 0 -155 -33t-49 -98q0 -39 27.5 -62.5t117.5 -46.5l129 -33q133 -35 191.5 -90t58.5 -157q0 -145 -28.5 -254 t-94.5 -177q-122 -124 -419 -124h-5h-18q-43 0 -96.5 4t-106.5 12t-101.5 18.5t-80.5 22.5q-37 14 -44.5 28.5z" />
<glyph unicode="T" horiz-adv-x="1083" d="M113 1077q0 10 3 28.5t7 35.5l22 121q8 41 24.5 52t53.5 11h875q31 0 44 -6t13 -27q0 -10 -3 -26.5t-11 -55.5l-19 -102q-8 -39 -22.5 -50t-55.5 -11h-266l-190 -981q-8 -41 -23.5 -53.5t-62.5 -12.5h-197q-37 0 -50 7t-13 30q0 10 6 39.5t12 60.5l176 910h-268 q-31 0 -43 7t-12 23z" />
<glyph unicode="U" horiz-adv-x="1294" d="M57 305q0 35 3 73t14 85l155 799q8 43 22.5 53t61.5 10h203q31 0 43 -10t12 -33q0 -14 -6 -51l-147 -758q-10 -57 -10 -90q0 -51 20 -74q33 -41 133 -41h10q100 0 150 41q29 23 44 63t28 101l153 789q10 43 23.5 53t60.5 10h201q31 0 43 -10t12 -33q0 -14 -6 -51 l-150 -768q-12 -68 -27.5 -121t-35.5 -98t-49 -81t-70 -69q-146 -112 -415 -112h-5h-24q-141 0 -232.5 28.5t-142.5 83.5q-72 84 -72 211z" />
<glyph unicode="V" horiz-adv-x="1175" d="M137 1272q0 31 14.5 42t61.5 11h186q45 0 59.5 -8t14.5 -49q0 -111 3 -234t7 -243.5t11.5 -230t17.5 -191.5h10q111 219 212.5 450.5t183.5 448.5q16 41 32.5 49t59.5 8h202q35 0 48.5 -5t13.5 -22q0 -20 -14.5 -53t-24.5 -57q-59 -133 -126 -275.5t-137.5 -286 t-143 -284.5t-144.5 -270q-18 -35 -47 -53.5t-84 -18.5h-201q-49 0 -71.5 18.5t-30.5 53.5q-25 119 -44.5 271.5t-34.5 313t-23.5 320.5t-10.5 295z" />
<glyph unicode="W" horiz-adv-x="1689" d="M140 660q-2 127 -2 252q0 182 5 360q2 29 17.5 41t56.5 12h201q37 0 53 -9t14 -44q-14 -270 -23 -489.5t-16 -413.5h9q43 98 89 207.5t91 219t86 211t76 185.5q10 29 28.5 41t59.5 12h149q41 0 57.5 -12t16.5 -41q2 -186 4 -400t6 -423h8q66 188 144 410t170 493 q10 35 30.5 44t57.5 9h201q59 0 59 -29q0 -14 -12.5 -44.5t-22.5 -59.5q-51 -137 -107.5 -280.5t-114.5 -288t-117.5 -286.5t-119.5 -274q-16 -35 -41.5 -49t-81.5 -14h-170q-59 0 -79.5 16.5t-24.5 51.5q-10 141 -12 316t-4 359h-13q-72 -180 -147.5 -357t-140.5 -318 q-16 -35 -43 -51.5t-86 -16.5h-189q-55 0 -75.5 14.5t-22.5 48.5q-18 285 -24 597z" />
<glyph unicode="X" horiz-adv-x="1191" d="M-84 31q0 10 6 21t15 24l376 555q10 14 17.5 27.5t9.5 23.5q4 14 -4 45l-182 543q-8 23 7 39t54 16h205q43 0 61.5 -10t24.5 -35l92 -397q6 -25 10 -34t15 -9q12 0 39 43l243 397q16 25 37 35t66 10h204q43 0 50.5 -15.5t-9.5 -39.5l-385 -561q-10 -16 -17 -27.5 t-7 -22.5q0 -23 8 -47l192 -536q10 -29 1 -52.5t-56 -23.5h-196q-43 0 -61.5 15.5t-24.5 45.5l-113 410q-6 25 -10 34t-15 9q-12 0 -39 -43l-249 -410q-23 -37 -46.5 -49t-70.5 -12h-201q-23 0 -36 8t-11 23z" />
<glyph unicode="Y" horiz-adv-x="1085" d="M137 1257q-1 7 -1 13q0 55 50 55h203q45 0 60.5 -5t19.5 -44q4 -66 11 -142.5t16.5 -154.5t19.5 -151.5t21 -133.5h10q33 59 71.5 133t76.5 152t74 154.5t67 142.5q14 39 30.5 44t61.5 5h213q41 0 41 -29q0 -20 -12.5 -45.5t-28.5 -60.5q-49 -100 -107.5 -208t-122 -213 t-128 -204.5t-126.5 -181.5l-61 -320q-8 -37 -23.5 -50t-54.5 -13h-211q-57 0 -57 35q0 20 14 86l53 270q-29 86 -57.5 191.5t-53 220.5t-43 230.5t-26.5 223.5z" />
<glyph unicode="Z" horiz-adv-x="1083" d="M-66 35q0 23 9 57l8 39q6 35 16 54.5t37 52.5l641 809h-420q-61 0 -61 36q0 12 3 31t9 47l19 99q8 41 25.5 53t58.5 12h786q37 0 49 -11t6 -48l-14 -70q-6 -33 -18.5 -56.5t-45.5 -64.5l-651 -796h471q33 0 47.5 -7.5t14.5 -29.5q0 -10 -2 -28.5t-11 -59.5l-18 -88 q-8 -41 -25.5 -53.5t-58.5 -12.5h-813q-61 0 -62 35z" />
<glyph unicode="[" horiz-adv-x="729" d="M-25 -143q0 10 2.5 25.5t8.5 43.5l274 1405q6 35 21.5 45t50.5 10h409q53 0 54 -28q0 -16 -9 -58l-26 -135q-4 -31 -18.5 -44t-51.5 -13h-155l-195 -1006h156q29 0 39 -7t10 -21q0 -8 -2 -23.5t-8 -44.5l-25 -127q-6 -35 -20.5 -45t-51.5 -10h-409q-53 0 -54 33z" />
<glyph unicode="\" horiz-adv-x="804" d="M133 1356q0 25 14.5 37t49.5 12h198q47 0 59.5 -15.5t18.5 -56.5l191 -1272q4 -23 6 -40t2 -27q0 -16 -13.5 -28.5t-50.5 -12.5h-200q-47 0 -59.5 15.5t-18.5 56.5l-187 1249q-6 41 -8 57.5t-2 24.5z" />
<glyph unicode="]" horiz-adv-x="729" d="M-90 -147q0 16 8 57l27 135q4 31 18 44t51 13h156l195 1006h-156q-29 0 -39 7t-10 22q0 8 2 23.5t8 43.5l25 127q6 35 20 45t51 10h410q53 0 53 -32q0 -10 -2 -25.5t-8 -44.5l-275 -1405q-6 -35 -21 -45t-50 -10h-410q-53 0 -53 29z" />
<glyph unicode="^" horiz-adv-x="1167" d="M158 834q0 12 5 22t21 27l357 389q31 31 50 42t66 11h95q35 0 52 -11t34 -42l200 -377q8 -18 12.5 -28.5t4.5 -20.5q0 -16 -9.5 -26.5t-37.5 -26.5l-119 -72q-29 -18 -46.5 -14t-31.5 26l-174 271l-285 -275q-27 -25 -43 -29t-39 17l-86 76q-27 23 -26 41z" />
<glyph unicode="_" horiz-adv-x="1157" d="M-70 -143q0 37 17.5 51t64.5 14h862q47 0 64.5 -14t17.5 -51v-121q0 -37 -15 -51.5t-67 -14.5h-862q-51 0 -66.5 14.5t-15.5 51.5v121z" />
<glyph unicode="`" horiz-adv-x="847" d="M229 1444q0 18 23 43l115 125q23 23 43 22q18 0 47 -26l307 -297q14 -14 14 -29q0 -18 -33 -51l-69 -70q-14 -14 -24.5 -21t-22.5 -7q-10 0 -21.5 5t-21.5 13l-330 252q-27 20 -27 41z" />
<glyph unicode="a" horiz-adv-x="1030" d="M-20 231q0 35 4 74t15 77t28.5 72.5t42.5 61.5q49 53 136 80t216 27h12q41 0 86 -2.5t68 -6.5q6 23 9 41.5t3 28.5v4q0 41 -34 59.5t-107 18.5h-6q-39 0 -87.5 -6t-86.5 -17q-16 -4 -41 -9t-37 -5q-29 0 -31 33l-10 104q-2 20 -3 38t-1 28q0 14 6 22.5t24 14.5 q31 10 75 19t93 17.5t100.5 12.5t100.5 4h6q104 0 174 -17.5t117 -52.5q37 -29 57.5 -72.5t20.5 -115.5q0 -29 -7.5 -93.5t-29.5 -174.5l-88 -439q-6 -31 -22.5 -44t-57.5 -13h-107q-33 0 -44 8t-13 33l-2 37h-4q-35 -45 -101.5 -70.5t-181.5 -25.5h-6q-141 0 -213.5 66.5 t-72.5 182.5zM274 293q0 -37 22.5 -57.5t78.5 -20.5h10q72 0 109 31q18 16 27 37.5t20 62.5l10 57q-23 4 -53.5 5.5t-59.5 1.5h-22q-74 0 -108 -30t-34 -87z" />
<glyph unicode="b" horiz-adv-x="1097" d="M18 229q0 47 8.5 103.5t24.5 146.5l166 848q6 33 23.5 46t56.5 13h195q53 0 53 -38q0 -16 -5 -41t-16 -76l-51 -254h4q41 20 103.5 32.5t142.5 12.5h10q125 0 200 -55.5t75 -180.5q0 -41 -5.5 -92t-19.5 -121l-29 -147q-29 -139 -61.5 -220t-91.5 -128 q-70 -55 -158 -75.5t-217 -20.5h-23q-145 0 -222 26.5t-115 75.5q-20 27 -34 59.5t-14 85.5zM354 322q0 -45 22.5 -64.5t80.5 -19.5h8q45 0 70.5 9t42.5 27q16 20 26 54t21 84l26 133q10 45 15.5 79.5t5.5 63.5q0 41 -28 58.5t-75 17.5h-10q-66 0 -98 -25q-23 -16 -34 -41.5 t-21 -72.5l-35 -176q-10 -47 -13.5 -80t-3.5 -47z" />
<glyph unicode="c" horiz-adv-x="925" d="M-4 264q0 37 5 84t15 107l29 155q23 123 64 201t112 127q53 37 134 60.5t184 23.5h10q80 0 166 -20.5t145 -49.5q18 -10 24.5 -19t6.5 -20q0 -14 -6 -29.5t-17 -39.5l-43 -94q-10 -23 -18 -31t-23 -8q-12 0 -36 10q-35 16 -84.5 29.5t-90.5 13.5h-10q-80 0 -129 -43 q-29 -25 -46 -64t-30 -108l-16 -92q-6 -35 -10 -63.5t-4 -51.5q0 -53 31.5 -78.5t111.5 -25.5h10q37 0 92.5 10t92.5 20q20 6 32 6q29 0 31 -34l10 -103q2 -16 3 -31.5t1 -29.5q0 -18 -12 -28.5t-43 -20.5q-63 -20 -139 -32.5t-172 -12.5h-21q-121 0 -195.5 26.5 t-111.5 73.5q-29 35 -41 81t-12 101z" />
<glyph unicode="d" horiz-adv-x="1087" d="M-2 250q0 29 3 78t17 125l31 159q23 121 56.5 194t87.5 120q47 43 126.5 69.5t180.5 26.5h18q59 0 108.5 -10.5t84.5 -26.5h4l67 342q12 59 82 59h187q33 0 45 -9t12 -27q0 -14 -12 -82l-168 -873q-12 -68 -25.5 -117t-29 -86.5t-36 -65.5t-51.5 -50q-57 -43 -146 -68.5 t-249 -25.5h-18q-127 0 -194.5 25.5t-102.5 53.5q-35 31 -56.5 78t-21.5 111zM328 309q0 -71 95 -71h3h12q29 0 57.5 6t47.5 20q18 14 30.5 33.5t22.5 73.5l49 252q8 38 8 60q0 7 -1 13q-3 24 -13 37q-25 31 -94 31h-17q-68 0 -102 -35q-23 -23 -35 -55.5t-22 -89.5 l-23 -121q-8 -43 -13 -83t-5 -71z" />
<glyph unicode="e" horiz-adv-x="1009" d="M-4 287q0 53 6 109.5t16 105.5l15 71q33 160 79 243t107 126q53 37 137 58.5t201 21.5h6q92 0 165 -19.5t120 -64.5q35 -35 54.5 -83t19.5 -120q0 -43 -8.5 -99t-22.5 -134l-10 -56q-6 -35 -21.5 -46t-48.5 -11h-485q-8 -39 -9 -72q0 -41 34 -60t136 -19h13q47 0 108.5 9 t108.5 19q8 2 18 4t21 2q27 0 30 -34l9 -95q2 -18 3 -34.5t1 -26.5q0 -20 -14.5 -31.5t-49.5 -21.5q-55 -16 -145 -31.5t-182 -15.5h-29q-182 0 -282.5 67.5t-100.5 237.5zM365 602h223q20 0 26 21l4 18q8 39 9 64q0 88 -105 88h-4q-63 0 -96 -31q-20 -18 -30.5 -46 t-20.5 -79z" />
<glyph unicode="f" horiz-adv-x="792" d="M-47 -319q0 16 16 71q23 76 40.5 137.5t30.5 115.5t25.5 103.5t22.5 104.5l105 535h-125q-37 0 -37 28q0 10 8 51l16 84q6 31 16.5 41.5t32.5 16.5l138 30l16 88q16 86 47 145.5t84 98.5q98 74 281 74h20q59 0 113.5 -8t87.5 -19q23 -6 30 -14t7 -18q0 -12 -5 -34 t-12 -44l-22 -88q-8 -29 -17.5 -38t-23.5 -9q-23 0 -61.5 9t-81.5 9h-7q-63 0 -90 -27q-12 -12 -20 -32.5t-15 -55.5l-6 -32h176q45 0 45 -29q0 -10 -12 -74l-20 -104q-6 -29 -19.5 -39t-42.5 -10h-178l-96 -494q-20 -104 -39 -189t-37 -153t-37.5 -122t-40.5 -99 q-16 -35 -35.5 -44t-54.5 -9h-164q-59 0 -59 43z" />
<glyph unicode="g" horiz-adv-x="1077" d="M0 223q-2 21 -2 45q0 72 18 170l29 154q12 63 26.5 113.5t34 91.5t45 73.5t58.5 61.5q53 47 130 68.5t169 21.5h20q88 0 156 -25.5t96 -72.5h4l19 41q10 25 21.5 32t35.5 7h129q25 0 35 -11.5t4 -42.5l-176 -907q-25 -127 -64.5 -210t-99 -131t-139.5 -66.5t-184 -18.5 h-19q-78 0 -159 12.5t-134 28.5q-33 8 -33 31q0 10 4.5 27.5t8.5 35.5l28 113q8 25 17.5 32t50.5 -3q51 -12 99 -19.5t100 -7.5h12q98 0 145 37t64 119l6 30h-4q-35 -27 -95.5 -40t-156.5 -13h-16q-70 0 -131.5 21.5t-100.5 66.5q-43 51 -51 135zM332 340q0 -31 12 -51 q18 -33 88 -33h14q66 0 101 31q37 31 49 98l45 238q9 43 9 66q0 26 -11 44q-16 31 -88 31h-16q-72 0 -103 -29q-27 -27 -39 -65.5t-26 -116.5l-19 -94q-8 -41 -12 -69t-4 -50z" />
<glyph unicode="h" horiz-adv-x="1073" d="M-33 35q0 16 4 36.5t11 49.5l237 1216q6 29 20.5 39t47.5 10h200q35 0 46.5 -9t11.5 -25t-5 -43t-12 -56l-55 -282h6q47 25 102.5 38t125.5 13h4q127 0 199.5 -49t72.5 -164q0 -37 -4 -82t-18 -111l-111 -557q-6 -29 -20.5 -44t-55.5 -15h-186q-35 0 -48.5 9t-13.5 30 q0 23 13 78l98 508q6 33 6 57q0 41 -25.5 59.5t-72.5 18.5q-25 0 -48.5 -6t-41.5 -21q-35 -29 -49 -108l-111 -566q-6 -29 -20.5 -44t-55.5 -15h-190q-35 0 -48.5 9t-13.5 26z" />
<glyph unicode="i" horiz-adv-x="544" d="M-23 35q0 -16 12.5 -25.5t47.5 -9.5h188q41 0 57.5 15.5t20.5 43.5l156 797q10 53 14 75.5t4 35.5q0 18 -12 27.5t-45 9.5h-189q-41 0 -57 -15.5t-22 -44.5l-160 -827q-6 -29 -10.5 -49.5t-4.5 -32.5zM193 1204q0 225 204 226h15q147 0 147 -115q0 -225 -205 -225h-14 q-147 0 -147 114z" />
<glyph unicode="j" horiz-adv-x="534" d="M-317 -328q0 -34 43 -34h2h110q106 0 189.5 25.5t140.5 78.5q51 47 84 127t61 229l148 758q10 53 14 75.5t4 35.5q0 18 -12 27.5t-45 9.5h-189q-41 0 -57 -15.5t-22 -44.5l-162 -838q-12 -66 -27.5 -106.5t-36 -63t-49.5 -31.5t-67 -9h-39q-31 0 -44.5 -10.5t-17.5 -39.5 l-18 -100q-4 -27 -7 -42t-3 -32zM195 1204q0 225 204 226h15q147 0 147 -115q0 -225 -205 -225h-14q-147 0 -147 114z" />
<glyph unicode="k" horiz-adv-x="1036" d="M-33 37q0 12 3 30.5t10 51.5l237 1208q6 29 23.5 44t58.5 15h186q33 0 45.5 -9t12.5 -27q0 -20 -13 -82l-118 -609h20q8 0 16.5 8.5t30.5 34.5l199 254q23 29 43 38.5t61 9.5h230q35 0 35 -25q0 -14 -27 -47l-301 -352q-25 -29 -34 -41.5t-9 -26.5q0 -16 29 -80l161 -364 q8 -20 6 -35q-6 -33 -49 -33h-205q-43 0 -60 11.5t-28 41.5l-110 283q-16 41 -22.5 51t-16.5 10h-21l-65 -336q-6 -31 -24.5 -46t-59.5 -15h-186q-33 0 -45.5 9t-12.5 28z" />
<glyph unicode="l" horiz-adv-x="630" d="M0 160q0 35 8 87t23 124l186 956q8 37 23.5 48t56.5 11h186q41 0 53.5 -12t6.5 -47l-185 -952q-6 -29 -11 -52.5t-5 -39.5q0 -29 37 -29h57q29 0 39 -8t10 -29q0 -16 -12 -78l-14 -69q-10 -43 -26.5 -56.5t-53.5 -13.5h-139q-92 0 -136.5 14.5t-70.5 44.5 q-16 20 -24.5 43t-8.5 58z" />
<glyph unicode="m" horiz-adv-x="1619" d="M-33 37q0 10 3 27.5t12 58.5l159 823q6 31 22.5 44.5t51.5 13.5h107q35 0 47 -9.5t16 -38.5l4 -43h6q57 53 131 81t172 28h23q86 0 143.5 -27.5t73.5 -81.5h6q57 53 132 81t173 28h17q121 0 192.5 -49t71.5 -164q0 -37 -5 -82t-18 -111l-108 -557q-6 -29 -22.5 -44 t-57.5 -15h-189q-41 0 -51 15.5t-4 43.5l111 568q6 33 6 57q0 76 -96 76q-25 0 -48.5 -6t-39.5 -21q-35 -29 -50 -106l-110 -568q-6 -29 -22.5 -44t-57.5 -15h-188q-41 0 -51.5 15.5t-4.5 43.5l111 568q6 33 6 57q0 76 -96 76q-25 0 -49.5 -6t-40.5 -21q-18 -16 -30.5 -40.5 t-21.5 -65.5l-110 -568q-6 -29 -20.5 -44t-55.5 -15h-190q-59 0 -60 37z" />
<glyph unicode="n" horiz-adv-x="1071" d="M-33 37q0 10 3 27.5t12 58.5l159 823q6 31 22.5 44.5t51.5 13.5h107q31 0 43 -7.5t16 -29.5l6 -43h6q55 49 131 73.5t181 24.5h4q127 0 199.5 -49t72.5 -164q0 -37 -4 -82t-19 -111l-110 -557q-6 -29 -20.5 -44t-55.5 -15h-186q-35 0 -48.5 9t-13.5 30q0 23 13 78l98 508 q6 33 6 57q0 41 -25.5 59.5t-72.5 18.5q-25 0 -48.5 -6t-41.5 -21q-35 -29 -50 -108l-110 -566q-6 -29 -20.5 -44t-55.5 -15h-190q-59 0 -60 37z" />
<glyph unicode="o" horiz-adv-x="1064" d="M-4 272q0 27 5 69t13.5 93t18.5 105.5t22 103.5q27 113 70 184.5t121 120.5q59 37 136 55.5t185 18.5h37q98 0 169 -21.5t116 -58.5t65.5 -91t20.5 -120q0 -53 -14.5 -148.5t-45.5 -215.5q-33 -127 -74.5 -198t-105.5 -112q-63 -41 -143 -58t-184 -17h-41 q-98 0 -169 21.5t-116 57.5q-45 37 -65.5 91.5t-20.5 119.5zM324 322q0 -41 21.5 -62.5t74.5 -21.5h4q29 0 55.5 6t44.5 20q29 23 46.5 62t33.5 116q23 100 31 154.5t8 85.5q0 41 -21.5 62.5t-74.5 21.5h-4q-29 0 -55.5 -6t-45.5 -21q-29 -23 -45.5 -61.5t-34.5 -116.5 q-23 -100 -30.5 -154.5t-7.5 -84.5z" />
<glyph unicode="p" horiz-adv-x="1087" d="M-101 -300q1 15 11 62l233 1190q6 29 21.5 40.5t50.5 11.5h98q33 0 45.5 -8.5t16.5 -30.5l6 -41h4q59 47 136 72.5t188 25.5h14q137 0 209 -58.5t72 -187.5q0 -37 -11.5 -115.5t-40.5 -234.5q-23 -123 -54.5 -214t-98.5 -146q-61 -51 -132 -67.5t-169 -16.5h-11 q-51 0 -108 12t-92 29l-62 -314q-8 -45 -27.5 -58t-72.5 -13h-160q-67 0 -67 37q0 11 1 25zM346 311q0 -41 25.5 -57t79.5 -16h10q45 0 73.5 13t47 39.5t30 68.5t23.5 98q16 72 25.5 134t9.5 87q0 86 -105 86h-14q-66 0 -98.5 -28.5t-49.5 -110.5l-41 -209q-16 -78 -16 -105 z" />
<glyph unicode="q" horiz-adv-x="1077" d="M-4 258q0 74 16 162l33 172q23 121 60.5 204t93 133t131.5 71.5t178 21.5h20q92 0 158 -25.5t94 -72.5h4l19 41q10 25 21.5 32t35.5 7h129q25 0 35 -11.5t4 -42.5l-240 -1241q-10 -45 -25 -58t-69 -13h-166q-67 0 -67 37q0 11 1 25q1 15 11 62l51 263q-35 -16 -90 -29.5 t-127 -13.5h-8q-135 0 -219 63.5t-84 212.5zM328 319q0 -81 100 -81h2h10q63 0 99 24q23 16 34 42t21 73l35 176q10 47 13 80t3 47q0 45 -22.5 64.5t-79.5 19.5h-8q-45 0 -71 -9.5t-42 -27.5q-16 -20 -26.5 -54t-20.5 -83l-27 -133q-8 -41 -14 -78t-6 -60z" />
<glyph unicode="r" horiz-adv-x="757" d="M-33 37q0 10 4 32.5t11 53.5l159 823q6 33 21.5 45.5t52.5 12.5h107q31 0 44 -9.5t15 -33.5l4 -62h4q45 59 104.5 82t143.5 23h94q39 0 39 -23q0 -10 -3 -28.5l-9 -55.5l-19 -100q-6 -35 -19 -46.5t-44 -11.5h-58q-117 0 -165 -43q-25 -23 -40.5 -61.5t-27.5 -104.5 l-92 -471q-6 -29 -20.5 -44t-55.5 -15h-190q-59 0 -60 37z" />
<glyph unicode="s" horiz-adv-x="927" d="M-37 65.5q-2 18.5 8 55.5l39 129q12 39 43 39q18 0 43 -8.5t57.5 -17.5t74.5 -17t94 -8h4q72 0 105.5 14t33.5 55q0 23 -25.5 34t-107.5 34l-33 8q-59 16 -98 34.5t-64 43.5q-63 63 -63 186q0 188 109.5 281.5t355.5 93.5h10q70 0 143.5 -10.5t122.5 -26.5q27 -8 27 -27 q0 -6 -4 -22t-15 -53l-30 -113q-4 -18 -13.5 -24.5t-21.5 -6.5q-25 0 -85.5 13.5t-127.5 13.5h-4q-137 0 -138 -61q0 -10 4.5 -18.5t16.5 -16.5t35.5 -15.5t64.5 -17.5l33 -8q135 -33 186 -88q51 -53 52 -150q0 -211 -118 -310t-355 -99h-13q-37 0 -81 4t-88 12t-83 18.5 t-65 20.5q-27 10 -29 28.5z" />
<glyph unicode="t" horiz-adv-x="792" d="M20 774q0 8 2.5 20.5t6.5 36.5l14 68q8 35 19.5 49.5t48.5 24.5l112 26l39 201q4 29 21.5 44.5t58.5 15.5h186q41 0 51.5 -15.5t6.5 -44.5l-39 -196h170q23 0 33 -5.5t10 -21.5q0 -10 -12 -72l-19 -98q-8 -39 -20.5 -49t-40.5 -10h-170l-78 -402q-6 -31 -6 -47 q0 -29 19.5 -37t64.5 -8h94q29 0 39 -7t10 -28q0 -10 -4 -33.5t-10 -58.5l-11 -51q-10 -45 -24 -60.5t-53 -15.5h-164q-123 0 -184.5 19.5t-88.5 56.5q-28 40 -28 98q0 34 3 68t13 82l84 424h-108q-45 0 -46 26z" />
<glyph unicode="u" horiz-adv-x="1052" d="M2 229q0 33 5 71t16 85l108 559q6 31 22.5 45.5t57.5 14.5h186q35 0 48.5 -9.5t13.5 -25.5q0 -10 -5 -41t-16 -76l-94 -485q-6 -31 -6 -56q0 -65 87 -65h3h8q47 0 84 26q16 14 26.5 36t20.5 69l111 567q6 31 22.5 45.5t57.5 14.5h186q35 0 48.5 -10.5t13.5 -28.5 q0 -14 -13 -76l-106 -545q-20 -104 -54 -169.5t-90 -106.5q-61 -45 -138 -65.5t-204 -20.5h-22q-178 0 -277.5 60t-99.5 187z" />
<glyph unicode="v" horiz-adv-x="1003" d="M45 944q-2 33 10.5 46.5t50.5 13.5h191q35 0 46 -8.5t13 -39.5q2 -80 8.5 -164.5t14.5 -164.5t16 -150.5t17 -124.5h8q33 61 71.5 139t75.5 158t71 158t60 141q14 35 28.5 45.5t49.5 10.5h197q47 0 47 -27q0 -16 -12.5 -49t-32.5 -74q-45 -98 -101.5 -210t-115 -221.5 t-115.5 -208.5t-104 -173q-14 -20 -30.5 -30.5t-49.5 -10.5h-203q-31 0 -46 9t-22 34q-25 90 -48 203.5t-41.5 235.5t-33 241t-20.5 221z" />
<glyph unicode="w" horiz-adv-x="1411" d="M45 942q0 37 16.5 49.5t49.5 12.5h198q31 0 42 -9.5t11 -27.5q0 -16 -1 -48t-3 -61q-2 -47 -5 -111.5t-4 -134t-2 -137t-1 -121.5h8q20 53 49 124t57.5 146.5t56.5 149.5t48 133q10 29 22.5 38t47.5 9h184q35 0 45 -12t10 -37q-1 -83 -1 -177v-96q1 -148 6 -278h6 q29 70 59.5 153t59 164t53 156.5t41.5 130.5q8 27 21.5 36.5t45.5 9.5h211q41 0 41 -25q0 -16 -9 -44t-24 -63q-41 -100 -87 -211.5t-93 -221t-92 -211t-82 -179.5q-16 -35 -33.5 -42t-50.5 -7h-190q-41 0 -54.5 12.5t-15.5 34.5q-4 55 -7 120.5t-6 135.5q-3 69 -4 139v46 q0 45 1 87h-8q-47 -123 -103.5 -258t-114.5 -270q-10 -25 -28.5 -36t-53.5 -11h-196q-35 0 -50.5 10t-19.5 35q-10 80 -19 189.5t-15.5 230.5t-11.5 245t-5 232z" />
<glyph unicode="x" horiz-adv-x="1060" d="M-102 27q0 4 2 10t8 17.5t19.5 29.5t35.5 49l256 344q8 12 15.5 25.5t7.5 29.5q0 8 -6 25l-138 332q-16 41 -21 57.5t-5 26.5q0 31 47 31h227q35 0 48.5 -7.5t21.5 -35.5l78 -279q6 -20 16 -20t18 14l170 276q23 33 38.5 42.5t56.5 9.5h233q39 0 39 -25 q0 -10 -12.5 -29.5t-42.5 -60.5l-275 -354q-10 -16 -16 -26.5t-6 -25.5q0 -14 6 -28l149 -314q27 -55 34 -75.5t7 -32.5q0 -33 -49 -33h-205q-35 0 -49 11.5t-24 39.5l-103 262q-6 20 -16 21q-8 0 -19 -15l-174 -276q-14 -23 -30.5 -33t-51.5 -10h-256q-35 0 -34 27z" />
<glyph unicode="y" horiz-adv-x="999" d="M47 947q0 56 74 57h172q35 0 48 -9.5t13 -40.5q4 -182 12.5 -356t22.5 -328h10q41 82 81 169t78 175t72 172t60 162q10 33 28.5 44.5t49.5 11.5h197q51 -1 51 -33q0 -12 -8 -29q-113 -281 -273 -606.5t-401 -706.5q-29 -47 -64 -47q-12 0 -27.5 5t-33.5 14l-109 43 q-27 10 -35 21t-8 24q0 12 5.5 22t13.5 21q20 31 42.5 64.5t44 67.5t41 64.5t34.5 55.5q-82 27 -107 151q-12 70 -25.5 173.5t-24.5 217t-19 224.5q-15 189 -15 197z" />
<glyph unicode="z" horiz-adv-x="903" d="M-88 37q0 8 2 22.5t10 46.5q10 47 24.5 76t49.5 70l434 491h-330q-23 0 -31 6.5t-8 22.5q0 10 13 82l16 90q8 39 22.5 49.5t43.5 10.5h696q25 0 33 -8.5t8 -26.5q0 -10 -2 -28.5t-6 -35.5q-8 -45 -22.5 -73.5t-51.5 -71.5l-446 -500h340q27 0 39 -6t12 -29q0 -6 -3 -24.5 t-14 -67.5l-16 -86q-4 -29 -18.5 -38t-42.5 -9h-697q-55 0 -55 37z" />
<glyph unicode="{" horiz-adv-x="819" d="M35 502q0 18 8 53l27 133q6 35 19 45t40 15q59 8 93 31.5t42 70.5l47 240q33 166 113 231t221 65h189q47 0 47 -32q0 -16 -9 -56l-26 -135q-10 -55 -64 -55h-96q-27 0 -42 -10.5t-21 -44.5l-41 -209q-16 -86 -61.5 -149.5t-125.5 -84.5l-2 -8q74 -25 93.5 -86t2.5 -147 l-34 -181q-8 -35 -9 -57q0 -27 46 -27h100q39 0 39 -30q0 -8 -2 -21.5t-6 -38.5l-25 -129q-8 -35 -22.5 -47t-55.5 -12h-178q-115 0 -173 41t-58 139q0 29 4 59.5t8 57.5l47 239q10 47 -13.5 70t-82.5 33q-39 6 -39 37z" />
<glyph unicode="|" horiz-adv-x="573" d="M0 0q0 20 12 80l242 1249q8 43 28.5 59.5t75.5 16.5h146q31 0 50 -11.5t19 -35.5q0 -10 -4 -31.5t-8 -48.5l-242 -1249q-8 -43 -28.5 -59.5t-75.5 -16.5h-145q-31 0 -50.5 11t-19.5 36z" />
<glyph unicode="}" horiz-adv-x="819" d="M-92 -141q0 16 8 55l27 135q8 55 63 55h96q27 0 42.5 10.5t21.5 45.5l41 209q16 86 61 149.5t125 83.5l2 8q-74 25 -93 86.5t-3 147.5l35 180q8 35 8 57q0 27 -45 27h-100q-39 0 -39 31q0 8 2 21.5t6 37.5l24 129q8 35 22.5 47t55.5 12h178q115 0 173.5 -40.5 t58.5 -139.5q0 -29 -4 -59.5t-8 -56.5l-48 -240q-10 -47 13.5 -69.5t83.5 -32.5q39 -6 39 -37q0 -18 -9 -54l-26 -133q-6 -35 -19.5 -45t-40.5 -14q-59 -8 -93 -31.5t-42 -71.5l-47 -239q-33 -166 -112.5 -231.5t-221.5 -65.5h-188q-47 0 -47 33z" />
<glyph unicode="~" horiz-adv-x="1167" d="M72 539q0 16 10 37.5t27 44.5q66 92 139.5 147t169.5 55q59 0 104 -18.5t85 -40t77 -39.5t78 -18t68.5 22.5t56.5 63.5q14 23 29.5 27.5t44.5 -13.5l90 -62q25 -16 24 -36q0 -16 -10 -38t-27 -44q-66 -92 -139.5 -147.5t-169.5 -55.5q-59 0 -104 18.5t-85 40t-77 40 t-78 18.5t-68.5 -22.5t-56.5 -63.5q-14 -23 -29.5 -28t-44.5 13l-90 62q-25 16 -24 37z" />
<glyph unicode="&#xa1;" horiz-adv-x="522" d="M-106 -311q0 8 3 26.5t19 80.5l175 721q12 51 31.5 63t66.5 12h125q55 0 55 -45q0 -25 -6 -61l-111 -756q-8 -53 -27.5 -69.5t-72.5 -16.5h-184q-41 0 -57.5 10.5t-16.5 34.5zM119 811q0 94 53.5 152.5t157.5 58.5h6q66 0 106 -31.5t40 -97.5q0 -94 -53.5 -152.5 t-157.5 -58.5h-6q-66 0 -106 32t-40 97z" />
<glyph unicode="&#xa2;" d="M143 264q0 37 5.5 84t15.5 107l29 155q23 123 63.5 201t112.5 127q53 37 134 60.5t183 23.5h35l27 137q4 16 9 22.5t21 6.5h60q31 0 24 -29l-31 -151q49 -10 95.5 -24.5t81.5 -31.5q18 -10 24 -19t6 -20q0 -14 -6 -29.5t-16 -39.5l-43 -96q-18 -39 -41 -39q-10 0 -18.5 3 t-18.5 7q-29 12 -56.5 22.5t-56.5 16.5l-100 -518q35 4 69.5 11t61.5 17q12 4 19.5 5t13.5 1q29 0 31 -34l10 -103q2 -16 3 -31.5t1 -29.5q0 -18 -12.5 -28.5t-42.5 -20.5q-45 -14 -94.5 -24.5t-108.5 -14.5l-27 -144q-6 -29 -35 -28h-59q-27 0 -20 28l26 138h-14 q-121 0 -196 26.5t-111 73.5q-29 35 -41.5 81t-12.5 101zM475 342q0 -45 20.5 -69.5t71.5 -32.5l103 520q-51 -8 -88 -39q-29 -25 -46.5 -64t-29.5 -108l-17 -92q-6 -35 -10 -63.5t-4 -51.5z" />
<glyph unicode="&#xa3;" d="M-8 51l24 123q4 25 14.5 35t26.5 20q96 51 142.5 116t62.5 157l8 37h-139q-29 0 -36 10t-1 35l31 127q4 16 13 25t32 14l143 20l25 119q23 117 53.5 191.5t77.5 125.5q63 72 152.5 104.5t216.5 32.5h4q86 0 167 -14t146 -39q37 -14 40 -33.5t-7 -50.5l-49 -135 q-10 -29 -27.5 -34t-58.5 10q-41 14 -90.5 26t-94.5 12q-92 0 -135 -51q-18 -23 -31.5 -56.5t-23.5 -90.5l-21 -105h289q33 0 42 -12t3 -47l-26 -139q-6 -31 -17.5 -38t-40.5 -7h-299l-6 -33q-16 -82 -56 -146.5t-87 -97.5h497q35 0 46.5 -15.5t5.5 -49.5l-27 -136 q-8 -37 -21.5 -49t-54.5 -12h-864q-33 0 -44 11.5t-5 39.5z" />
<glyph unicode="&#xa4;" d="M19.5 293q0.5 18 29.5 41l131 106q-4 18 -6 39t-2 41q0 74 19.5 142.5t68.5 132.5l-90 102q-16 20 -12 38.5t32 45.5l138 119q23 20 40 21t35 -21l97 -113q74 27 162 27q43 0 81.5 -8.5t71.5 -22.5l135 111q29 23 45.5 22.5t32.5 -22.5l100 -129q18 -25 17.5 -43.5 t-29.5 -40.5l-131 -107q4 -18 6 -38.5t2 -41.5q0 -74 -19.5 -142.5t-68.5 -131.5l90 -103q16 -20 12.5 -38.5t-32.5 -45.5l-137 -118q-23 -20 -40.5 -21.5t-35.5 21.5l-96 112q-74 -26 -162 -26q-43 0 -82 8t-72 22l-135 -110q-29 -23 -45 -23t-33 23l-100 129 q-18 25 -17.5 43zM444 580q0 -53 30 -84t83 -31q41 0 71.5 14.5t51 39t31 55t10.5 61.5q0 53 -29.5 83t-83.5 32q-41 0 -71.5 -14.5t-51 -39t-31 -55.5t-10.5 -61z" />
<glyph unicode="&#xa5;" d="M49 185.5q-6 11.5 0 41.5l19 99q6 33 16 43t39 10h252q-4 20 -10.5 41.5t-10.5 40.5h-215q-29 0 -35 11t0 42l19 98q6 33 16.5 43.5t38.5 10.5h119q-35 143 -62.5 296.5t-37.5 294.5q-6 68 49 68h207q45 0 60 -5t19 -44q4 -66 11.5 -142.5t15.5 -154.5t18.5 -151.5 t20.5 -133.5h10q33 59 72 133t78 152t74.5 154.5t66.5 142.5q14 39 32.5 44t63.5 5h201q53 0 25 -68q-66 -141 -152 -295.5t-178 -295.5h110q29 0 35 -11.5t0 -42.5l-18 -98q-6 -33 -16.5 -43t-38.5 -10h-209l-54 -82h246q29 0 35 -11.5t0 -41.5l-18 -99q-6 -33 -16.5 -43 t-39.5 -10h-243l-21 -111q-8 -37 -23.5 -50t-54.5 -13h-198q-39 0 -50.5 13.5t-3.5 49.5l21 111h-250q-29 0 -35 11.5z" />
<glyph unicode="&#xa6;" horiz-adv-x="573" d="M0 6q0 18 12 80l80 403q10 59 31.5 74t62.5 15h166q35 0 48.5 -9.5t13.5 -33.5q0 -16 -3 -34t-8 -42l-81 -418q-10 -59 -40 -73.5t-85 -14.5h-115q-45 0 -63.5 12t-18.5 41zM160 823q0 16 3 33.5t7 42.5l82 418q10 59 40 73.5t85 14.5h115q45 0 63 -12.5t18 -40.5 q0 -18 -12 -80l-80 -404q-10 -59 -31.5 -73.5t-62.5 -14.5h-166q-35 0 -48 9.5t-13 33.5z" />
<glyph unicode="&#xa7;" horiz-adv-x="1011" d="M-4 82q0 14 5 29.5t17 44.5l43 98q20 49 43 49q16 0 37 -8q59 -25 122 -38t114 -13h10q66 0 92.5 13t26.5 42q0 23 -23.5 33t-81.5 22l-75 17q-141 31 -189.5 81t-48.5 119q0 76 46 133.5t120 94.5q-41 25 -61.5 72t-22.5 102q0 18 1 38.5t9 55.5q18 78 65.5 131t112 85 t140 46t149.5 14h15q78 0 150.5 -12t125.5 -33q25 -10 25 -28t-23 -72l-51 -123q-8 -18 -14.5 -23t-16.5 -5q-8 0 -17.5 3t-25.5 7q-39 10 -86 19t-90 9h-8q-74 0 -97.5 -14t-23.5 -37q0 -16 25.5 -25.5t95.5 -25.5l74 -16q113 -25 165 -64t50 -108q0 -80 -45.5 -143.5 t-122.5 -108.5q49 -23 70.5 -70t21.5 -102q0 -199 -126 -294t-349 -95h-25q-37 0 -78 5t-82 13t-78.5 19.5t-68.5 23.5q-41 16 -41 39zM375 684q0 -20 18.5 -36.5t73.5 -29.5l98 -22q14 12 24.5 32.5t10.5 43.5q0 20 -22.5 33.5t-79.5 25.5l-84 19q-14 -10 -26.5 -26.5 t-12.5 -39.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="800" d="M170 1255q0 23 6 48l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -6 -47l-2 -9q-16 -63 -50 -98t-106 -35h-14q-51 0 -80 24.5t-29 69.5zM551 1255q0 23 6 48l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47l-2 -9q-16 -63 -50 -98 t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1728" d="M86 567q0 184 64.5 345t182.5 282t283.5 190.5t366.5 69.5q162 0 286 -43t207 -122t124.5 -188.5t41.5 -244.5q0 -184 -64.5 -345t-182 -281.5t-283.5 -190.5t-367 -70q-164 0 -286.5 43t-205.5 122t-125 188.5t-42 244.5zM346 573q0 -86 26.5 -155.5t81 -120.5 t136.5 -79t192 -28q141 0 253 52.5t188.5 141.5t117.5 209t41 257q0 86 -26.5 155.5t-80.5 121t-136 79t-193 27.5q-141 0 -252.5 -52.5t-188.5 -141.5t-118 -208.5t-41 -257.5zM524 541q0 23 2 47t11 65l24 133q33 170 116 240t232 70q57 0 115.5 -13.5t97.5 -29.5 q23 -10 26 -22.5t-7 -41.5l-27 -78q-8 -23 -18.5 -27.5t-36.5 5.5q-29 10 -66 19.5t-71 9.5q-59 0 -85 -27t-40 -96l-31 -158q-4 -16 -6 -33.5t-2 -28.5q0 -33 20.5 -49t71.5 -16q47 0 131 25q27 8 35 0.5t12 -25.5l10 -80q4 -25 -9 -38t-48 -25q-45 -16 -96 -25.5t-99 -9.5 q-141 0 -201.5 52t-60.5 157z" />
<glyph unicode="&#xaa;" horiz-adv-x="702" d="M63 725q0 23 2.5 52.5t10.5 56.5q25 86 95.5 122.5t193.5 36.5h12q23 0 51.5 -1t48.5 -5q6 27 6 41q0 25 -20.5 39t-73.5 14h-6q-29 0 -66.5 -6t-78.5 -16q-12 -4 -25 -4q-12 0 -16 28l-4 39l-4.5 31.5t-2.5 26.5q0 25 23 30q47 16 117.5 26.5t132.5 10.5h10q57 0 95 -8 t67 -25q68 -39 67 -120q0 -53 -8 -96.5t-14 -77.5l-60 -293q-8 -39 -45 -39h-92q-33 0 -35 24l-2 31h-4q-20 -33 -65 -51.5t-115 -18.5h-20q-47 0 -80 13.5t-53.5 35t-31 49t-10.5 54.5zM262 770q0 -45 62 -45h12q49 0 72 20q27 20 34 66l9 39q-14 2 -37 3t-43 1h-11 q-45 0 -63 -9t-29 -36q-6 -16 -6 -39z" />
<glyph unicode="&#xab;" horiz-adv-x="1517" d="M18 440q0 20 7 49q4 18 9 32t13 25t20.5 21.5t30.5 24.5l459 319q31 23 49 23q14 0 24.5 -8t20.5 -23l74 -92q35 -43 35 -66q0 -16 -7.5 -27t-29.5 -28l-322 -241l197 -213q27 -29 27 -52q0 -14 -7.5 -26.5t-29.5 -30.5l-96 -76q-20 -16 -31.5 -23.5t-24.5 -7.5 q-20 0 -45 25l-336 322q-23 23 -30 38t-7 35zM700 440q0 20 7 49q4 18 9 32t13 25t20.5 21.5t30.5 24.5l459 319q31 23 49 23q14 0 24.5 -8t20.5 -23l74 -92q35 -43 35 -66q0 -16 -7.5 -27t-29.5 -28l-322 -241l197 -213q27 -29 27 -52q0 -14 -7.5 -26.5t-29.5 -30.5 l-96 -76q-20 -16 -31.5 -23.5t-24.5 -7.5q-20 0 -45 25l-336 322q-23 23 -30 38t-7 35z" />
<glyph unicode="&#xac;" d="M68 461l24 127q10 49 25.5 65.5t56.5 16.5h815q41 0 55.5 -19.5t6.5 -62.5l-88 -455q-8 -37 -23.5 -52t-56.5 -15h-138q-41 0 -55 15t-6 52l49 254h-612q-41 0 -51.5 18.5t-1.5 55.5z" />
<glyph unicode="&#xad;" horiz-adv-x="745" d="M33 451v153q0 37 17.5 51.5t64.5 14.5h450q47 0 64.5 -14.5t17.5 -51.5v-153q0 -37 -15.5 -51.5t-66.5 -14.5h-450q-51 0 -66.5 14.5t-15.5 51.5z" />
<glyph unicode="&#xae;" horiz-adv-x="1728" d="M86 567q0 184 64.5 345t182.5 282t283.5 190.5t366.5 69.5q162 0 286 -43t207 -122t124.5 -188.5t41.5 -244.5q0 -184 -64.5 -345t-182 -281.5t-283.5 -190.5t-367 -70q-164 0 -286.5 43t-205.5 122t-125 188.5t-42 244.5zM346 573q0 -86 26.5 -155.5t81 -120.5 t136.5 -79t192 -28q141 0 253 52.5t188.5 141.5t117.5 209t41 257q0 86 -26.5 155.5t-80.5 121t-136 79t-193 27.5q-141 0 -252.5 -52.5t-188.5 -141.5t-118 -208.5t-41 -257.5zM535 387l120 625q6 33 18.5 49t51.5 20q35 4 91 6.5t104 2.5q272 0 272 -199q0 -84 -49 -156.5 t-144 -111.5l105 -224q14 -29 6 -42t-49 -13h-129q-29 0 -39 7t-21 32l-79 213h-5l-38 -209q-4 -25 -16.5 -34t-47.5 -9h-98q-37 0 -47.5 9t-5.5 34zM825 756h35q27 0 44.5 3t33.5 17t24.5 39t8.5 45q0 29 -16.5 43.5t-57.5 14.5h-20.5t-20.5 -3z" />
<glyph unicode="&#xaf;" horiz-adv-x="802" d="M211 1243l18 100q6 33 16.5 42.5t39.5 9.5h469q25 0 32 -10.5t2 -38.5l-18 -101q-6 -33 -16.5 -42t-38.5 -9h-469q-25 0 -32 10t-3 39z" />
<glyph unicode="&#xb0;" horiz-adv-x="745" d="M145 1171q0 72 26 138.5t74 117t116.5 81t152.5 30.5q115 0 185.5 -70.5t70.5 -179.5q0 -84 -28.5 -150.5t-78 -113.5t-117 -72.5t-143.5 -25.5q-113 0 -184.5 69.5t-73.5 175.5zM362 1208q0 -33 19.5 -54t54.5 -21q45 0 78 33.5t33 86.5q0 33 -19.5 54.5t-54.5 21.5 q-45 0 -78 -33.5t-33 -87.5z" />
<glyph unicode="&#xb1;" d="M-16 74l24 127q10 49 25.5 65.5t56.5 16.5h825q41 0 51.5 -18.5t2.5 -55.5l-25 -127q-10 -49 -25.5 -65.5t-56.5 -16.5h-825q-41 0 -51.5 18.5t-1.5 55.5zM143 762l23 119q10 47 23.5 62t54.5 15h243l50 254q8 41 22 54.5t66 13.5h143q47 0 59.5 -15.5t1.5 -62.5 l-47 -244h244q39 0 48 -17t1 -52l-22 -119q-10 -47 -23.5 -62.5t-54.5 -15.5h-244l-49 -254q-8 -41 -22.5 -54t-65.5 -13h-143q-47 0 -59.5 15t-2.5 63l47 243h-243q-39 0 -48.5 17.5t-1.5 52.5z" />
<glyph unicode="&#xb2;" horiz-adv-x="917" d="M188 918l13 69q10 59 27.5 104.5t45 80t68.5 63.5t96 53l68 31q70 31 93.5 49t23.5 47q0 23 -15.5 34t-54.5 11t-84 -8t-92 -22q-41 -12 -49 28l-25 131q-4 25 4 36t31 20q59 18 133 29.5t139 11.5h8q129 0 201 -56.5t72 -169.5q0 -51 -8.5 -91t-32 -73.5t-65.5 -62.5 t-107 -59l-72 -33q-68 -31 -89 -44.5t-28 -29.5h273q33 0 40 -10t-1 -49l-21 -103q-8 -35 -17 -46t-42 -11h-479q-41 0 -51.5 13t-2.5 57z" />
<glyph unicode="&#xb3;" horiz-adv-x="917" d="M176 913.5q-6 14.5 4 44.5l37 113q6 20 17.5 31.5t44.5 1.5q37 -10 87 -20.5t95 -10.5q53 0 78.5 19.5t25.5 54.5q0 23 -17.5 31t-60.5 8h-43q-29 0 -35 8t-1 35l16 80q4 25 13 34t38 9h35q55 0 79.5 17t24.5 46q0 47 -71 47q-37 0 -83 -5t-83 -11q-25 -4 -34 5t-11 34 l-6 100q-2 31 10 44t41 24q47 14 108.5 23.5t124.5 9.5h4q135 0 206 -47.5t71 -145.5q0 -66 -31 -126t-102 -91v-6q35 -16 54 -45t19 -82q0 -68 -26.5 -122t-74.5 -92t-113.5 -58.5t-143.5 -20.5h-4q-66 0 -135.5 11t-122.5 28q-29 10 -35 24.5z" />
<glyph unicode="&#xb4;" horiz-adv-x="847" d="M287 1260q0 16 30 38l361 265q10 8 21.5 13t21.5 5q20 0 35 -27l73 -127q10 -16 11 -30q0 -16 -10.5 -26.5t-36.5 -27.5l-359 -215q-23 -14 -39 -14q-12 0 -19 6t-18 21l-59 90q-12 18 -12 29z" />
<glyph unicode="&#xb5;" horiz-adv-x="1140" d="M-100 -301l241 1245q8 37 22.5 48.5t55.5 11.5h193q41 0 50 -15.5t3 -44.5l-111 -577q-6 -31 -6 -56q0 -66 90 -65h8q47 0 84 26q16 14 26.5 36t21.5 69l110 567q6 35 21.5 47.5t58.5 12.5h186q41 0 52.5 -15.5t5.5 -44.5l-121 -622q-8 -41 1 -58.5t40 -17.5q14 0 30.5 2 t28.5 6q41 8 35 -31l-18 -147q-4 -23 -15.5 -35t-34.5 -23q-31 -14 -84 -25t-110 -11h-2q-145 0 -187 77h-6q-29 -31 -77 -54t-107 -23q-66 0 -104 16l-58 -299q-6 -33 -23.5 -46t-58.5 -13h-186q-41 0 -50 15t-5 44z" />
<glyph unicode="&#xb6;" horiz-adv-x="1359" d="M84 891q0 78 33 156.5t90 138.5q72 74 188.5 115.5t294.5 41.5h703q39 0 39 -24q0 -12 -9 -51l-26 -146q-8 -47 -58 -47h-122l-240 -1237q-8 -39 -26.5 -49t-53.5 -10h-195q-45 0 -45 33q0 8 2.5 24.5t8.5 46.5l231 1192h-129l-240 -1237q-8 -39 -26.5 -49t-52.5 -10 h-195q-45 0 -45 33q0 8 2 24.5t8 46.5l133 680q-135 25 -203.5 110t-66.5 218z" />
<glyph unicode="&#xb7;" horiz-adv-x="493" d="M47 518q0 23 4 51.5t9 50.5q23 90 69.5 121t122.5 31h19q139 0 139 -96q0 -23 -3 -51.5t-12 -57.5q-23 -90 -68.5 -120.5t-121.5 -30.5h-18q-74 0 -107 26.5t-33 75.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="909" d="M102 -414l23 72q8 23 19.5 27t31.5 -2q25 -8 61.5 -14.5t69.5 -6.5q35 0 55.5 12.5t20.5 42.5q0 33 -53 33h-6q-31 0 -51.5 -5t-36.5 -5q-10 0 -14.5 4t-8.5 10l-18 29q-8 14 -9 27q0 10 15 43l63 131q12 27 37 26h111q16 0 24 -7t-2 -28l-37 -84q31 6 58 7q63 0 107 -34 t46 -112q0 -109 -77.5 -179.5t-204.5 -70.5q-61 0 -117.5 9.5t-85.5 21.5q-33 12 -21 53z" />
<glyph unicode="&#xb9;" horiz-adv-x="917" d="M201 905l22 119q6 35 16.5 46t43.5 11h125l57 291h-8l-111 -49q-31 -14 -45 -10t-25 28l-45 103q-12 29 -11 43t28 29l252 122q33 16 59.5 24.5t67.5 8.5h98q33 0 43 -11t4 -46l-104 -533h112q33 0 41 -11t2 -46l-22 -119q-6 -35 -17.5 -46t-46.5 -11h-495q-53 0 -41 57z " />
<glyph unicode="&#xba;" horiz-adv-x="729" d="M94 768q0 25 4 60.5t21 119.5q31 160 110.5 229.5t233.5 69.5h12q125 0 186.5 -50t61.5 -142q0 -12 -1 -26.5t-3 -35t-7.5 -49.5t-13.5 -72q-31 -158 -108.5 -228.5t-233.5 -70.5h-12q-250 0 -250 195zM309 809q0 -29 18.5 -42t49.5 -13h8q41 0 62.5 20.5t35.5 83.5 q14 66 19.5 100.5t5.5 51.5q0 57 -64 57h-8q-39 0 -63.5 -22.5t-38.5 -88.5q-8 -37 -12.5 -60.5t-7.5 -38.5t-4 -26.5t-1 -21.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="1517" d="M10 209q0 16 7.5 27t29.5 28l322 241l-197 213q-27 29 -27 52q0 14 7.5 26.5t29.5 30.5l96 76q20 16 31.5 23.5t24.5 7.5q20 0 45 -25l336 -322q23 -23 30 -38t7 -35t-7 -49q-4 -18 -9 -32t-13 -25t-20.5 -21.5t-30.5 -24.5l-459 -319q-31 -23 -49 -23q-14 0 -24.5 8 t-20.5 23l-74 92q-35 43 -35 66zM694 209q0 16 7.5 27t29.5 28l322 241l-197 213q-27 29 -27 52q0 14 7.5 26.5t29.5 30.5l96 76q20 16 31.5 23.5t24.5 7.5q20 0 45 -25l336 -322q23 -23 30 -38t7 -35t-7 -49q-4 -18 -9 -32t-13 -25t-20.5 -21.5t-30.5 -24.5l-459 -319 q-31 -23 -49 -23q-14 0 -24.5 8t-20.5 23l-74 92q-35 43 -35 66z" />
<glyph unicode="&#xbc;" horiz-adv-x="2142" d="M160 557l22 119q6 35 16.5 46t43.5 11h125l57 291h-8l-111 -49q-31 -14 -45 -10t-24 28l-46 103q-12 29 -11 43t28 28l252 123q33 16 59.5 24.5t67.5 8.5h98q33 0 43 -11.5t4 -45.5l-104 -533h112q33 0 41 -11t2 -46l-22 -119q-6 -35 -17.5 -46t-46.5 -11h-495 q-53 0 -41 57zM486 -3q-9 15 8 38l974 1272q16 20 32 28t48 8h176q27 0 36 -15t-7 -38l-975 -1272q-16 -20 -31.5 -28t-48.5 -8h-176q-27 0 -36 15zM1223 229l10 52q6 29 17.5 48t39.5 54l322 385q27 33 47 44t53 11h180q41 0 49.5 -14t2.5 -49l-74 -383h55q23 0 30 -8.5 t3 -36.5l-21 -107q-6 -35 -17 -42t-34 -7h-55l-25 -127q-6 -29 -18 -39t-45 -10h-148q-53 0 -45 47l27 129h-309q-35 0 -43 10.5t-2 42.5zM1493 377h123l37 196h-4z" />
<glyph unicode="&#xbd;" horiz-adv-x="2142" d="M158 559l22 119q6 35 16.5 46t43.5 11h125l57 291h-8l-111 -49q-31 -14 -45 -10t-25 28l-45 103q-12 29 -11 43t28 29l252 122q33 16 59.5 24.5t67.5 8.5h98q33 0 43 -11t4 -46l-104 -533h112q33 0 41 -11t2 -46l-22 -119q-6 -35 -17.5 -46t-46.5 -11h-495q-53 0 -41 57z M470 -3q-9 15 8 38l974 1272q16 20 32 28t48 8h176q27 0 36.5 -15t-7.5 -38l-975 -1272q-16 -20 -31.5 -28t-48.5 -8h-176q-27 0 -36 15zM1312 76l13 69q10 59 27.5 104.5t45 80t68.5 63.5t96 53l68 31q70 31 93.5 49t23.5 47q0 23 -15.5 34t-54.5 11t-84 -8t-92 -22 q-41 -12 -49 28l-25 131q-4 25 4 36t31 20q59 18 133 29.5t139 11.5h8q129 0 201 -56.5t72 -169.5q0 -51 -8.5 -91t-32 -73.5t-65.5 -62.5t-107 -59l-72 -33q-68 -31 -89 -44.5t-28 -29.5h273q33 0 40 -10t-1 -49l-21 -103q-8 -35 -17 -46t-42 -11h-479q-41 0 -51.5 13 t-2.5 57z" />
<glyph unicode="&#xbe;" horiz-adv-x="2142" d="M147 563.5q-6 14.5 5 44.5l36 113q6 20 17.5 31.5t44.5 1.5q37 -10 87 -20.5t95 -10.5q53 0 79 19.5t26 54.5q0 23 -17.5 31t-60.5 8h-43q-29 0 -35 8t-2 35l16 79q4 25 13.5 34t37.5 9h35q55 0 80 17.5t25 46.5q0 47 -72 47q-37 0 -83 -5t-83 -11q-25 -4 -34 5t-11 34 l-6 100q-2 31 10 44t41 24q47 14 108.5 23t125.5 9h4q135 0 205.5 -47t70.5 -145q0 -66 -30.5 -126.5t-102.5 -90.5v-6q35 -16 54.5 -45t19.5 -82q0 -68 -26.5 -122.5t-75 -92t-114 -58t-143.5 -20.5h-4q-66 0 -135 11t-123 28q-29 10 -35 24.5zM464 -3q-9 15 7 38l975 1272 q16 20 31.5 28t48.5 8h176q27 0 36 -15t-7 -38l-975 -1272q-16 -20 -31.5 -28t-48.5 -8h-176q-27 0 -36 15zM1192 229l10 52q6 29 17.5 48t40.5 54l321 385q27 33 47.5 44t52.5 11h181q41 0 49 -14t2 -49l-74 -383h55q23 0 30 -8.5t3 -36.5l-20 -107q-6 -35 -17.5 -42 t-34.5 -7h-55l-24 -127q-6 -29 -18.5 -39t-45.5 -10h-147q-53 0 -45 47l26 129h-309q-35 0 -43 10.5t-2 42.5zM1462 377h123l37 196h-4z" />
<glyph unicode="&#xbf;" horiz-adv-x="948" d="M-73 -51q0 80 14 137.5t47 101.5t83 81t122 73l70 37q53 29 86.5 49.5t54 40t31 39.5t16.5 47q6 35 53 35h168q33 0 33 -24q0 -111 -46 -194t-175 -150l-70 -37q-39 -20 -67.5 -39t-47 -38t-27.5 -43t-9 -56q0 -45 30.5 -63.5t89.5 -18.5h6q47 0 116 13t134 34 q55 18 64 -23l33 -151q6 -29 0.5 -44.5t-43.5 -29.5q-84 -31 -190.5 -48t-203.5 -17h-8q-188 0 -276 74.5t-88 213.5zM445 811q0 94 53 152.5t158 58.5h6q66 0 105.5 -31.5t39.5 -97.5q0 -94 -53 -152.5t-158 -58.5h-6q-66 0 -105.5 32t-39.5 97z" />
<glyph unicode="&#xc0;" horiz-adv-x="1239" d="M-102 39q0 23 7 39t19 43q70 145 144.5 294.5t148.5 293t144.5 276.5t132.5 248q29 51 64.5 71.5t96.5 20.5h176q61 0 90 -20.5t35 -71.5q35 -256 66 -519t61 -556q4 -35 6.5 -60.5t2.5 -48.5q0 -25 -17.5 -37t-64.5 -12h-181q-47 0 -67.5 14.5t-24.5 53.5l-14 170h-383 l-80 -170q-18 -39 -38.5 -53.5t-82.5 -14.5h-182q-59 0 -59 39zM436 1667q0 14 17 39l94 141q18 29 43 29q20 0 47 -21l346 -249q18 -12 19 -31q0 -18 -25 -51l-59 -80q-27 -35 -47 -35q-16 0 -41 12l-363 203q-14 8 -22.5 17.5t-8.5 25.5zM451 508h264q-4 98 -12.5 224 t-16.5 235h-22q-53 -113 -108.5 -232t-104.5 -227z" />
<glyph unicode="&#xc1;" horiz-adv-x="1239" d="M-102 39q0 23 7 39t19 43q70 145 144.5 294.5t148.5 293t144.5 276.5t132.5 248q29 51 64.5 71.5t96.5 20.5h176q61 0 90 -20.5t35 -71.5q35 -256 66 -519t61 -556q4 -35 6.5 -60.5t2.5 -48.5q0 -25 -17.5 -37t-64.5 -12h-181q-47 0 -67.5 14.5t-24.5 53.5l-14 170h-383 l-80 -170q-18 -39 -38.5 -53.5t-82.5 -14.5h-182q-59 0 -59 39zM451 508h264q-4 98 -12.5 224t-16.5 235h-22q-53 -113 -108.5 -232t-104.5 -227zM512 1597q0 18 37 37l399 201q20 10 35 10q25 0 39 -33l53 -137q6 -16 6 -26q0 -14 -12.5 -25.5t-44.5 -24.5l-389 -151 q-20 -8 -35 -8q-25 0 -39 33l-43 98q-6 14 -6 26z" />
<glyph unicode="&#xc2;" horiz-adv-x="1239" d="M-102 39q0 23 7 39t19 43q70 145 144.5 294.5t148.5 293t144.5 276.5t132.5 248q29 51 64.5 71.5t96.5 20.5h176q61 0 90 -20.5t35 -71.5q35 -256 66 -519t61 -556q4 -35 6.5 -60.5t2.5 -48.5q0 -25 -17.5 -37t-64.5 -12h-181q-47 0 -67.5 14.5t-24.5 53.5l-14 170h-383 l-80 -170q-18 -39 -38.5 -53.5t-82.5 -14.5h-182q-59 0 -59 39zM451 508h264q-4 98 -12.5 224t-16.5 235h-22q-53 -113 -108.5 -232t-104.5 -227zM455 1497q-25 25 2 51l234 219q16 16 30.5 21.5t30.5 5.5h125q16 0 27.5 -5t21.5 -22l144 -215q12 -18 12 -30q0 -8 -4 -14.5 t-19 -16.5l-104 -68q-8 -4 -16.5 -9t-16.5 -5q-16 0 -33 23l-110 153h-11l-170 -153q-16 -14 -23 -18.5t-17 -4.5q-12 0 -25 12z" />
<glyph unicode="&#xc3;" horiz-adv-x="1239" d="M-102 39q0 23 7 39t19 43q70 145 144.5 294.5t148.5 293t144.5 276.5t132.5 248q29 51 64.5 71.5t96.5 20.5h176q61 0 90 -20.5t35 -71.5q35 -256 66 -519t61 -556q4 -35 6.5 -60.5t2.5 -48.5q0 -25 -17.5 -37t-64.5 -12h-181q-47 0 -67.5 14.5t-24.5 53.5l-14 170h-383 l-80 -170q-18 -39 -38.5 -53.5t-82.5 -14.5h-182q-59 0 -59 39zM398 1538q51 86 117.5 140.5t146.5 54.5q49 0 79.5 -14.5t54.5 -33t45 -33t50 -14.5t51.5 17.5t51.5 58.5q14 18 25 18.5t28 -10.5l76 -49q31 -18 10 -53q-51 -86 -117.5 -140.5t-146.5 -54.5q-49 0 -79 14.5 t-53.5 33t-45 33t-52.5 14.5q-29 0 -51 -17.5t-51 -58.5q-14 -18 -25.5 -18.5t-27.5 10.5l-76 49q-31 18 -10 53zM451 508h264q-4 98 -12.5 224t-16.5 235h-22q-53 -113 -108.5 -232t-104.5 -227z" />
<glyph unicode="&#xc4;" horiz-adv-x="1239" d="M-102 39q0 23 7 39t19 43q70 145 144.5 294.5t148.5 293t144.5 276.5t132.5 248q29 51 64.5 71.5t96.5 20.5h176q61 0 90 -20.5t35 -71.5q35 -256 66 -519t61 -556q4 -35 6.5 -60.5t2.5 -48.5q0 -25 -17.5 -37t-64.5 -12h-181q-47 0 -67.5 14.5t-24.5 53.5l-14 170h-383 l-80 -170q-18 -39 -38.5 -53.5t-82.5 -14.5h-182q-59 0 -59 39zM449 1577q0 23 6 47l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47l-2 -8q-16 -63 -50 -98t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5zM451 508h264q-4 98 -12.5 224t-16.5 235h-22 q-53 -113 -108.5 -232t-104.5 -227zM830 1577q0 23 6 47l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47l-2 -8q-16 -63 -50 -98t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1239" d="M-102 39q0 23 7 39t19 43q70 145 144.5 294.5t148.5 293t144.5 276.5t132.5 248q29 51 64.5 71.5t96.5 20.5h176q61 0 90 -20.5t35 -71.5q35 -256 66 -519t61 -556q4 -35 6.5 -60.5t2.5 -48.5q0 -25 -17.5 -37t-64.5 -12h-181q-47 0 -67.5 14.5t-24.5 53.5l-14 170h-383 l-80 -170q-18 -39 -38.5 -53.5t-82.5 -14.5h-182q-59 0 -59 39zM451 508h264q-4 98 -12.5 224t-16.5 235h-22q-53 -113 -108.5 -232t-104.5 -227zM575 1563q0 53 18.5 98t51.5 78t78 51t96 18q82 0 129 -47t47 -125q0 -53 -18.5 -98t-51 -78t-78 -51t-96.5 -18 q-78 0 -127 47t-49 125zM725 1585q0 -23 14.5 -34t34.5 -11q29 0 50.5 19.5t21.5 54.5q0 23 -14.5 34t-35.5 11q-29 0 -50 -19.5t-21 -54.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1853" d="M-82 39q0 16 12.5 35.5t30.5 46.5q205 299 400.5 580.5t375.5 531.5q37 53 78 72.5t103 19.5h862q29 0 38 -5t9 -22q0 -8 -3 -27.5t-12 -62.5l-18 -100q-8 -37 -19.5 -49t-60.5 -12h-424q-29 0 -40 -9.5t-17 -31.5q-10 -35 -22.5 -93.5t-22.5 -113.5h420q31 0 43 -5.5 t12 -21.5t-3 -37.5t-13 -62.5l-19 -84q-6 -27 -20.5 -37t-46.5 -10h-426q-6 -33 -13.5 -72t-11.5 -74q-4 -29 -6 -49t-2 -37q0 -31 37 -30h426q29 0 41 -6.5t12 -28.5q0 -12 -3 -34t-9 -60l-15 -87q-8 -37 -23.5 -50t-58.5 -13h-534q-115 0 -161 30.5t-46 116.5q0 37 6 91 h-377l-112 -170q-12 -18 -23.5 -31.5t-26 -22t-35 -11.5t-50.5 -3h-181q-31 0 -41 11.5t-10 27.5zM559 508h254l31 178l74 361h-23q-37 -55 -81 -127t-89 -146t-88 -143.5t-78 -122.5z" />
<glyph unicode="&#xc7;" horiz-adv-x="1136" d="M57 342q0 25 4.5 63.5t12.5 96t22.5 136.5t36.5 183q33 154 75 248t118 158q70 59 170 87.5t243 28.5h15q98 0 191 -20t171 -61q27 -14 27 -31q0 -12 -6.5 -31.5t-24.5 -60.5l-53 -117q-14 -31 -35 -31q-20 0 -41 10q-53 25 -110.5 40.5t-122.5 15.5h-9q-125 0 -188 -70 q-16 -16 -27.5 -36.5t-20.5 -48.5t-18.5 -65.5t-19.5 -93.5q-29 -139 -40 -219t-11 -131q0 -66 43 -95.5t157 -29.5h13q59 0 130.5 13.5t127.5 33.5q23 8 37 9q10 0 18 -8.5t12 -30.5l13 -90q4 -33 7 -54.5t3 -38.5q0 -35 -74 -61q-68 -25 -165 -41t-201 -18q-2 -2 -2 -7 l-37 -84q31 6 57 7q63 0 107.5 -34t46.5 -112q0 -109 -78 -179.5t-205 -70.5q-61 0 -117.5 9.5t-85.5 21.5q-33 12 -20 53l22 72q8 23 19.5 27t31.5 -2q25 -8 62 -14.5t70 -6.5q35 0 55 12.5t20 42.5q0 33 -53 33h-6q-31 0 -51.5 -5t-36.5 -5q-10 0 -14.5 4t-8.5 10l-18 29 q-8 14 -8 27q0 10 14 43l64 131q0 2 1 4t1 4q-178 25 -250 127q-25 35 -42.5 89t-17.5 134z" />
<glyph unicode="&#xc8;" horiz-adv-x="1067" d="M45 147q0 72 17.5 203t52.5 336q23 129 44 222t38 157q23 82 52.5 132t73.5 79t103 39t139 10h490q29 0 38 -5t9 -22q0 -8 -3 -27.5t-12 -62.5l-18 -100q-8 -37 -19.5 -49t-60.5 -12h-424q-29 0 -40 -9.5t-17 -31.5q-10 -35 -22.5 -93.5t-22.5 -113.5h420q31 0 43 -5.5 t12 -21.5t-3 -37.5t-13 -62.5l-19 -84q-6 -27 -20.5 -37t-46.5 -10h-426q-6 -33 -13.5 -72t-11.5 -74q-4 -29 -6 -49t-2 -37q0 -31 37 -30h426q29 0 41 -6.5t12 -28.5q0 -12 -3 -34t-9 -60l-15 -87q-8 -37 -23.5 -50t-58.5 -13h-534q-115 0 -160 30.5t-45 116.5zM389 1667 q0 14 17 39l94 141q18 29 43 29q20 0 47 -21l346 -249q18 -12 19 -31q0 -18 -25 -51l-59 -80q-27 -35 -47 -35q-16 0 -41 12l-363 203q-14 8 -22.5 17.5t-8.5 25.5z" />
<glyph unicode="&#xc9;" horiz-adv-x="1067" d="M45 147q0 72 17.5 203t52.5 336q23 129 44 222t38 157q23 82 52.5 132t73.5 79t103 39t139 10h490q29 0 38 -5t9 -22q0 -8 -3 -27.5t-12 -62.5l-18 -100q-8 -37 -19.5 -49t-60.5 -12h-424q-29 0 -40 -9.5t-17 -31.5q-10 -35 -22.5 -93.5t-22.5 -113.5h420q31 0 43 -5.5 t12 -21.5t-3 -37.5t-13 -62.5l-19 -84q-6 -27 -20.5 -37t-46.5 -10h-426q-6 -33 -13.5 -72t-11.5 -74q-4 -29 -6 -49t-2 -37q0 -31 37 -30h426q29 0 41 -6.5t12 -28.5q0 -12 -3 -34t-9 -60l-15 -87q-8 -37 -23.5 -50t-58.5 -13h-534q-115 0 -160 30.5t-45 116.5zM490 1597 q0 18 37 37l399 201q20 10 35 10q25 0 39 -33l53 -137q6 -16 6 -26q0 -14 -12.5 -25.5t-44.5 -24.5l-389 -151q-20 -8 -35 -8q-25 0 -39 33l-43 98q-6 14 -6 26z" />
<glyph unicode="&#xca;" horiz-adv-x="1067" d="M45 147q0 72 17.5 203t52.5 336q23 129 44 222t38 157q23 82 52.5 132t73.5 79t103 39t139 10h490q29 0 38 -5t9 -22q0 -8 -3 -27.5t-12 -62.5l-18 -100q-8 -37 -19.5 -49t-60.5 -12h-424q-29 0 -40 -9.5t-17 -31.5q-10 -35 -22.5 -93.5t-22.5 -113.5h420q31 0 43 -5.5 t12 -21.5t-3 -37.5t-13 -62.5l-19 -84q-6 -27 -20.5 -37t-46.5 -10h-426q-6 -33 -13.5 -72t-11.5 -74q-4 -29 -6 -49t-2 -37q0 -31 37 -30h426q29 0 41 -6.5t12 -28.5q0 -12 -3 -34t-9 -60l-15 -87q-8 -37 -23.5 -50t-58.5 -13h-534q-115 0 -160 30.5t-45 116.5zM420 1497 q-25 25 2 51l234 219q16 16 30.5 21.5t30.5 5.5h125q16 0 27.5 -5t21.5 -22l144 -215q12 -18 12 -30q0 -8 -4 -14.5t-19 -16.5l-104 -68q-8 -4 -16.5 -9t-16.5 -5q-16 0 -33 23l-110 153h-11l-170 -153q-16 -14 -23 -18.5t-17 -4.5q-12 0 -25 12z" />
<glyph unicode="&#xcb;" horiz-adv-x="1067" d="M45 147q0 72 17.5 203t52.5 336q23 129 44 222t38 157q23 82 52.5 132t73.5 79t103 39t139 10h490q29 0 38 -5t9 -22q0 -8 -3 -27.5t-12 -62.5l-18 -100q-8 -37 -19.5 -49t-60.5 -12h-424q-29 0 -40 -9.5t-17 -31.5q-10 -35 -22.5 -93.5t-22.5 -113.5h420q31 0 43 -5.5 t12 -21.5t-3 -37.5t-13 -62.5l-19 -84q-6 -27 -20.5 -37t-46.5 -10h-426q-6 -33 -13.5 -72t-11.5 -74q-4 -29 -6 -49t-2 -37q0 -31 37 -30h426q29 0 41 -6.5t12 -28.5q0 -12 -3 -34t-9 -60l-15 -87q-8 -37 -23.5 -50t-58.5 -13h-534q-115 0 -160 30.5t-45 116.5zM402 1577 q0 23 6 47l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47l-2 -8q-16 -63 -50 -98t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5zM783 1577q0 23 6 47l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47l-2 -8q-16 -63 -50 -98t-105 -35h-15 q-51 0 -79.5 24.5t-28.5 69.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="616" d="M18 33q0 16 4.5 37.5t8.5 46.5l225 1155q6 35 19.5 44t60.5 9h199q39 0 51 -8t12 -25q0 -16 -5 -35.5t-9 -48.5l-224 -1155q-6 -35 -19 -44t-60 -9h-199q-39 0 -51.5 8t-12.5 25zM120 1667q0 14 17 39l94 141q18 29 43 29q20 0 47 -21l346 -249q18 -12 19 -31 q0 -18 -25 -51l-59 -80q-27 -35 -47 -35q-16 0 -41 12l-363 203q-14 8 -22.5 17.5t-8.5 25.5z" />
<glyph unicode="&#xcd;" horiz-adv-x="616" d="M18 33q0 16 4.5 37.5t8.5 46.5l225 1155q6 35 19.5 44t60.5 9h199q39 0 51 -8t12 -25q0 -16 -5 -35.5t-9 -48.5l-224 -1155q-6 -35 -19 -44t-60 -9h-199q-39 0 -51.5 8t-12.5 25zM260 1597q0 18 37 37l399 201q20 10 35 10q25 0 39 -33l53 -137q6 -16 6 -26 q0 -14 -12.5 -25.5t-44.5 -24.5l-389 -151q-20 -8 -35 -8q-25 0 -39 33l-43 98q-6 14 -6 26z" />
<glyph unicode="&#xce;" horiz-adv-x="616" d="M18 33q0 16 4.5 37.5t8.5 46.5l225 1155q6 35 19.5 44t60.5 9h199q39 0 51 -8t12 -25q0 -16 -5 -35.5t-9 -48.5l-224 -1155q-6 -35 -19 -44t-60 -9h-199q-39 0 -51.5 8t-12.5 25zM158 1497q-25 25 2 51l234 219q16 16 30.5 21.5t30.5 5.5h125q16 0 27.5 -5t21.5 -22 l144 -215q12 -18 12 -30q0 -8 -4 -14.5t-19 -16.5l-104 -68q-8 -4 -16.5 -9t-16.5 -5q-16 0 -33 23l-110 153h-11l-170 -153q-16 -14 -23 -18.5t-17 -4.5q-12 0 -25 12z" />
<glyph unicode="&#xcf;" horiz-adv-x="616" d="M18 33q0 16 4.5 37.5t8.5 46.5l225 1155q6 35 19.5 44t60.5 9h199q39 0 51 -8t12 -25q0 -16 -5 -35.5t-9 -48.5l-224 -1155q-6 -35 -19 -44t-60 -9h-199q-39 0 -51.5 8t-12.5 25zM156 1577q0 23 6 47l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47 l-2 -8q-16 -63 -50 -98t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5zM537 1577q0 23 6 47l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47l-2 -8q-16 -63 -50 -98t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1349" d="M25 578q0 6 2 16t6 39l16 84q6 35 19.5 46t42.5 11h104l90 461q10 49 30.5 63.5t55.5 18.5q135 12 348 12h23q188 0 294.5 -37t160.5 -104q57 -74 70 -179.5t-11 -279.5q-18 -131 -43 -226t-55.5 -164t-67.5 -116t-82 -84q-94 -78 -229 -108.5t-332 -30.5h-43 q-78 0 -159 2t-122 6q-61 6 -61 58q0 10 5 38.5t19 96.5l66 348h-104q-43 0 -43 29zM471 309q0 -14 7 -18t20 -6q18 -2 39.5 -2h37.5h19q66 0 112 14t80 45q25 20 43.5 44t34 56.5t28.5 77.5t28 107q16 68 25 136t9 109q0 78 -34 121q-25 31 -66 43.5t-104 12.5h-19 q-18 0 -44 -1.5t-42 -3.5q-23 -2 -31 -9t-12 -25l-45 -236h141q43 0 43 -29q0 -6 -2 -16t-6 -39l-16 -84q-6 -35 -19.5 -46t-42.5 -11h-141l-25 -129q-18 -102 -18 -111z" />
<glyph unicode="&#xd1;" horiz-adv-x="1316" d="M12 37q0 14 4 33.5t9 48.5l221 1138q8 41 26.5 54.5t55.5 13.5h82q47 0 70.5 -12.5t39.5 -44.5l330 -678h4l131 665q8 43 26.5 56.5t57.5 13.5h174q61 0 62 -35q0 -10 -5.5 -35.5t-11.5 -56.5l-219 -1130q-8 -41 -27.5 -54.5t-54.5 -13.5h-84q-43 0 -67.5 11.5 t-42.5 45.5l-334 660h-4l-125 -647q-8 -43 -25.5 -56.5t-58.5 -13.5h-176q-35 0 -46.5 8t-11.5 29zM463 1538q51 86 117.5 140.5t146.5 54.5q49 0 79.5 -14.5t54.5 -33t45 -33t50 -14.5t51.5 17.5t51.5 58.5q14 18 25 18.5t28 -10.5l76 -49q31 -18 10 -53 q-51 -86 -117.5 -140.5t-146.5 -54.5q-49 0 -79 14.5t-53.5 33t-45 33t-52.5 14.5q-29 0 -51 -17.5t-51 -58.5q-14 -18 -25.5 -18.5t-27.5 10.5l-76 49q-31 18 -10 53z" />
<glyph unicode="&#xd2;" horiz-adv-x="1296" d="M57 352q0 43 12.5 143.5t43.5 258.5q18 98 40.5 175t53.5 137t71.5 106.5t96.5 82.5q129 88 393 88h16q117 0 207 -24.5t144 -71.5q104 -92 104 -256q0 -70 -12 -162.5t-41 -244.5q-20 -104 -41 -182t-48.5 -137.5t-64.5 -103.5t-86 -79q-74 -51 -175 -75.5t-245 -24.5 h-22q-117 0 -208 26.5t-140 69.5q-53 47 -76 112.5t-23 161.5zM410 418q0 -68 26 -101q39 -49 127 -49h25q47 0 80.5 12.5t62.5 36.5q43 37 68.5 115t48.5 193q23 121 32 182t9 102q0 70 -25 101q-39 47 -127 47h-24q-47 0 -81 -12.5t-63 -36.5q-41 -35 -67.5 -114 t-48.5 -194q-23 -117 -33 -179t-10 -103zM467 1667q0 14 17 39l94 141q18 29 43 29q20 0 47 -21l346 -249q18 -12 19 -31q0 -18 -25 -51l-59 -80q-27 -35 -47 -35q-16 0 -41 12l-363 203q-14 8 -22.5 17.5t-8.5 25.5z" />
<glyph unicode="&#xd3;" horiz-adv-x="1296" d="M57 352q0 43 12.5 143.5t43.5 258.5q18 98 40.5 175t53.5 137t71.5 106.5t96.5 82.5q129 88 393 88h16q117 0 207 -24.5t144 -71.5q104 -92 104 -256q0 -70 -12 -162.5t-41 -244.5q-20 -104 -41 -182t-48.5 -137.5t-64.5 -103.5t-86 -79q-74 -51 -175 -75.5t-245 -24.5 h-22q-117 0 -208 26.5t-140 69.5q-53 47 -76 112.5t-23 161.5zM410 418q0 -68 26 -101q39 -49 127 -49h25q47 0 80.5 12.5t62.5 36.5q43 37 68.5 115t48.5 193q23 121 32 182t9 102q0 70 -25 101q-39 47 -127 47h-24q-47 0 -81 -12.5t-63 -36.5q-41 -35 -67.5 -114 t-48.5 -194q-23 -117 -33 -179t-10 -103zM553 1597q0 18 37 37l399 201q20 10 35 10q25 0 39 -33l53 -137q6 -16 6 -26q0 -14 -12.5 -25.5t-44.5 -24.5l-389 -151q-20 -8 -35 -8q-25 0 -39 33l-43 98q-6 14 -6 26z" />
<glyph unicode="&#xd4;" horiz-adv-x="1296" d="M57 352q0 43 12.5 143.5t43.5 258.5q18 98 40.5 175t53.5 137t71.5 106.5t96.5 82.5q129 88 393 88h16q117 0 207 -24.5t144 -71.5q104 -92 104 -256q0 -70 -12 -162.5t-41 -244.5q-20 -104 -41 -182t-48.5 -137.5t-64.5 -103.5t-86 -79q-74 -51 -175 -75.5t-245 -24.5 h-22q-117 0 -208 26.5t-140 69.5q-53 47 -76 112.5t-23 161.5zM410 418q0 -68 26 -101q39 -49 127 -49h25q47 0 80.5 12.5t62.5 36.5q43 37 68.5 115t48.5 193q23 121 32 182t9 102q0 70 -25 101q-39 47 -127 47h-24q-47 0 -81 -12.5t-63 -36.5q-41 -35 -67.5 -114 t-48.5 -194q-23 -117 -33 -179t-10 -103zM475 1497q-25 25 2 51l234 219q16 16 30.5 21.5t30.5 5.5h125q16 0 27.5 -5t21.5 -22l144 -215q12 -18 12 -30q0 -8 -4 -14.5t-19 -16.5l-104 -68q-8 -4 -16.5 -9t-16.5 -5q-16 0 -33 23l-110 153h-11l-170 -153q-16 -14 -23 -18.5 t-17 -4.5q-12 0 -25 12z" />
<glyph unicode="&#xd5;" horiz-adv-x="1296" d="M57 352q0 43 12.5 143.5t43.5 258.5q18 98 40.5 175t53.5 137t71.5 106.5t96.5 82.5q129 88 393 88h16q117 0 207 -24.5t144 -71.5q104 -92 104 -256q0 -70 -12 -162.5t-41 -244.5q-20 -104 -41 -182t-48.5 -137.5t-64.5 -103.5t-86 -79q-74 -51 -175 -75.5t-245 -24.5 h-22q-117 0 -208 26.5t-140 69.5q-53 47 -76 112.5t-23 161.5zM410 418q0 -68 26 -101q39 -49 127 -49h25q47 0 80.5 12.5t62.5 36.5q43 37 68.5 115t48.5 193q23 121 32 182t9 102q0 70 -25 101q-39 47 -127 47h-24q-47 0 -81 -12.5t-63 -36.5q-41 -35 -67.5 -114 t-48.5 -194q-23 -117 -33 -179t-10 -103zM447 1538q51 86 117.5 140.5t146.5 54.5q49 0 79.5 -14.5t54.5 -33t45 -33t50 -14.5t51.5 17.5t51.5 58.5q14 18 25 18.5t28 -10.5l76 -49q31 -18 10 -53q-51 -86 -117.5 -140.5t-146.5 -54.5q-49 0 -79 14.5t-53.5 33t-45 33 t-52.5 14.5q-29 0 -51 -17.5t-51 -58.5q-14 -18 -25.5 -18.5t-27.5 10.5l-76 49q-31 18 -10 53z" />
<glyph unicode="&#xd6;" horiz-adv-x="1296" d="M57 352q0 43 12.5 143.5t43.5 258.5q18 98 40.5 175t53.5 137t71.5 106.5t96.5 82.5q129 88 393 88h16q117 0 207 -24.5t144 -71.5q104 -92 104 -256q0 -70 -12 -162.5t-41 -244.5q-20 -104 -41 -182t-48.5 -137.5t-64.5 -103.5t-86 -79q-74 -51 -175 -75.5t-245 -24.5 h-22q-117 0 -208 26.5t-140 69.5q-53 47 -76 112.5t-23 161.5zM410 418q0 -68 26 -101q39 -49 127 -49h25q47 0 80.5 12.5t62.5 36.5q43 37 68.5 115t48.5 193q23 121 32 182t9 102q0 70 -25 101q-39 47 -127 47h-24q-47 0 -81 -12.5t-63 -36.5q-41 -35 -67.5 -114 t-48.5 -194q-23 -117 -33 -179t-10 -103zM485 1577q0 23 6 47l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47l-2 -8q-16 -63 -50 -98t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5zM866 1577q0 23 6 47l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5 q0 -23 -7 -47l-2 -8q-16 -63 -50 -98t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5z" />
<glyph unicode="&#xd7;" d="M94 285.5q6 23.5 41 52.5l215 176l-137 174q-27 29 -22.5 53.5t42.5 55.5l121 100q37 31 59.5 31t47.5 -29l139 -176l219 180q39 33 62.5 29t44.5 -31l80 -100q27 -33 20.5 -56.5t-41.5 -52.5l-217 -178l143 -176q25 -31 22 -53.5t-42 -55.5l-121 -100q-35 -29 -58.5 -30 t-47.5 28l-146 178l-217 -178q-35 -29 -58.5 -29t-47.5 31l-80 100q-27 33 -21 56.5z" />
<glyph unicode="&#xd8;" horiz-adv-x="1296" d="M9 -69.5q-3 14.5 20 44.5l98 131q-37 45 -53.5 104.5t-16.5 141.5q0 43 12.5 143.5t43.5 258.5q18 98 40.5 175t53.5 137t71.5 106.5t96.5 82.5q129 88 393 88h16q135 0 236 -34l119 161q18 25 32.5 30t47.5 -17l43 -31q35 -27 38 -41t-20 -45l-113 -154q72 -88 72 -221 q0 -70 -12.5 -163t-40.5 -244q-20 -104 -41 -182t-48.5 -137.5t-64.5 -103.5t-86 -79q-74 -51 -175 -75.5t-245 -24.5h-16q-66 0 -125 9t-109 23l-106 -143q-18 -25 -32.5 -30t-47.5 18l-43 30q-35 27 -38 41.5zM416 498l405 544q-31 14 -84 15h-24q-47 0 -81 -12.5 t-63 -36.5q-41 -35 -67 -114t-49 -194q-14 -70 -22.5 -119t-14.5 -83zM477 285q31 -16 86 -17h25q47 0 80.5 12.5t62.5 36.5q43 37 68.5 115t48.5 193q14 72 22.5 122t12.5 84z" />
<glyph unicode="&#xd9;" horiz-adv-x="1294" d="M57 305q0 35 3 73t14 85l155 799q8 43 22.5 53t61.5 10h203q31 0 43 -10t12 -33q0 -14 -6 -51l-147 -758q-10 -57 -10 -90q0 -51 20 -74q33 -41 133 -41h10q100 0 150 41q29 23 44 63t28 101l153 789q10 43 23.5 53t60.5 10h201q31 0 43 -10t12 -33q0 -14 -6 -51 l-150 -768q-12 -68 -27.5 -121t-35.5 -98t-49 -81t-70 -69q-147 -113 -420 -112h-24q-141 0 -232.5 28.5t-142.5 83.5q-72 84 -72 211zM456 1667q0 14 17 39l94 141q18 29 43 29q20 0 47 -21l346 -249q18 -12 19 -31q0 -18 -25 -51l-59 -80q-27 -35 -47 -35q-16 0 -41 12 l-363 203q-14 8 -22.5 17.5t-8.5 25.5z" />
<glyph unicode="&#xda;" horiz-adv-x="1294" d="M57 305q0 35 3 73t14 85l155 799q8 43 22.5 53t61.5 10h203q31 0 43 -10t12 -33q0 -14 -6 -51l-147 -758q-10 -57 -10 -90q0 -51 20 -74q33 -41 133 -41h10q100 0 150 41q29 23 44 63t28 101l153 789q10 43 23.5 53t60.5 10h201q31 0 43 -10t12 -33q0 -14 -6 -51 l-150 -768q-12 -68 -27.5 -121t-35.5 -98t-49 -81t-70 -69q-147 -113 -420 -112h-24q-141 0 -232.5 28.5t-142.5 83.5q-72 84 -72 211zM572 1597q0 18 37 37l399 201q20 10 35 10q25 0 39 -33l53 -137q6 -16 6 -26q0 -14 -12.5 -25.5t-44.5 -24.5l-389 -151q-20 -8 -35 -8 q-25 0 -39 33l-43 98q-6 14 -6 26z" />
<glyph unicode="&#xdb;" horiz-adv-x="1294" d="M57 305q0 35 3 73t14 85l155 799q8 43 22.5 53t61.5 10h203q31 0 43 -10t12 -33q0 -14 -6 -51l-147 -758q-10 -57 -10 -90q0 -51 20 -74q33 -41 133 -41h10q100 0 150 41q29 23 44 63t28 101l153 789q10 43 23.5 53t60.5 10h201q31 0 43 -10t12 -33q0 -14 -6 -51 l-150 -768q-12 -68 -27.5 -121t-35.5 -98t-49 -81t-70 -69q-147 -113 -420 -112h-24q-141 0 -232.5 28.5t-142.5 83.5q-72 84 -72 211zM493 1497q-25 25 2 51l234 219q16 16 30.5 21.5t30.5 5.5h125q16 0 27.5 -5t21.5 -22l144 -215q12 -18 12 -30q0 -8 -4 -14.5t-19 -16.5 l-104 -68q-8 -4 -16.5 -9t-16.5 -5q-16 0 -33 23l-110 153h-11l-170 -153q-16 -14 -23 -18.5t-17 -4.5q-12 0 -25 12z" />
<glyph unicode="&#xdc;" horiz-adv-x="1294" d="M57 305q0 35 3 73t14 85l155 799q8 43 22.5 53t61.5 10h203q31 0 43 -10t12 -33q0 -14 -6 -51l-147 -758q-10 -57 -10 -90q0 -51 20 -74q33 -41 133 -41h10q100 0 150 41q29 23 44 63t28 101l153 789q10 43 23.5 53t60.5 10h201q31 0 43 -10t12 -33q0 -14 -6 -51 l-150 -768q-12 -68 -27.5 -121t-35.5 -98t-49 -81t-70 -69q-147 -113 -420 -112h-24q-141 0 -232.5 28.5t-142.5 83.5q-72 84 -72 211zM494 1577q0 23 6 47l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47l-2 -8q-16 -63 -50 -98t-105 -35h-15 q-51 0 -79.5 24.5t-28.5 69.5zM875 1577q0 23 6 47l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47l-2 -8q-16 -63 -50 -98t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1085" d="M137 1257q-6 68 49 68h203q45 0 60.5 -5t19.5 -44q4 -66 11 -142.5t16.5 -154.5t19.5 -151.5t21 -133.5h10q33 59 71.5 133t76.5 152t74 154.5t67 142.5q14 39 30.5 44t61.5 5h213q41 0 41 -29q0 -20 -12.5 -45.5t-28.5 -60.5q-49 -100 -107.5 -208t-122 -213 t-128 -204.5t-126.5 -181.5l-61 -320q-8 -37 -23.5 -50t-54.5 -13h-211q-57 0 -57 35q0 20 14 86l53 270q-29 86 -57.5 191.5t-53 220.5t-43 230.5t-26.5 223.5zM475 1597q0 18 37 37l399 201q20 10 35 10q25 0 39 -33l53 -137q6 -16 6 -26q0 -14 -12.5 -25.5t-44.5 -24.5 l-389 -151q-20 -8 -35 -8q-25 0 -39 33l-43 98q-6 14 -6 26z" />
<glyph unicode="&#xde;" horiz-adv-x="1179" d="M12 33q0 10 5.5 33.5t11.5 56.5l223 1155q8 43 23.5 54t62.5 11h190q37 0 52.5 -5t15.5 -27q0 -2 -3 -20.5t-9 -46t-13.5 -62.5t-13.5 -68q27 4 59.5 6t59.5 2h20q231 0 331.5 -73.5t100.5 -235.5q0 -82 -14 -161t-40.5 -146.5t-67.5 -121.5t-93 -85q-82 -49 -185 -67.5 t-241 -18.5h-102l-29 -147q-4 -23 -9 -35.5t-14 -19.5t-23.5 -9t-39.5 -2h-190q-37 0 -52.5 5t-15.5 28zM442 494h107q111 0 160 43q20 16 33.5 42.5t21.5 55.5t12 58.5t4 54.5q0 57 -32.5 77.5t-104.5 20.5h-18q-23 0 -52.5 -1t-48.5 -5q-16 -2 -20 -25z" />
<glyph unicode="&#xdf;" d="M-66 -109l201 1020q27 135 60.5 215t87.5 136q135 143 407 143h8q193 0 297.5 -76t104.5 -237q0 -113 -46 -201t-132 -143v-9q70 -27 107.5 -89t37.5 -161q0 -121 -41 -218t-111 -165q-129 -125 -331 -124h-4q-41 0 -89.5 5t-78.5 11q-29 6 -36 18.5t-3 34.5l28 150 q6 29 21.5 35t44.5 2q14 -2 34.5 -3.5t35.5 -1.5h2q49 0 81.5 14.5t59.5 47.5q25 33 40 79t15 105q0 70 -39 96q-20 14 -51 18.5t-76 4.5h-39q-25 0 -33 9t-3 34l30 156q6 27 16.5 35t34.5 8h15q51 0 80.5 10t52.5 29q29 23 46 60.5t17 94.5q0 109 -127 109h-4 q-86 0 -135 -62q-20 -27 -34.5 -64.5t-26.5 -98.5l-199 -1027q-8 -39 -22.5 -51t-59.5 -12h-188q-41 0 -52.5 13.5t-3.5 49.5z" />
<glyph unicode="&#xe0;" horiz-adv-x="1030" d="M-20 231q0 35 4 74t15 77t28.5 72.5t42.5 61.5q49 53 136 80t216 27h12q41 0 86 -2.5t68 -6.5q6 23 9 41.5t3 28.5v4q0 41 -34 59.5t-107 18.5h-6q-39 0 -87.5 -6t-86.5 -17q-16 -4 -41 -9t-37 -5q-29 0 -31 33l-10 104q-2 20 -3 38t-1 28q0 14 6 22.5t24 14.5 q31 10 75 19t93 17.5t100.5 12.5t100.5 4h6q104 0 174 -17.5t117 -52.5q37 -29 57.5 -72.5t20.5 -115.5q0 -29 -7.5 -93.5t-29.5 -174.5l-88 -439q-6 -31 -22.5 -44t-57.5 -13h-107q-33 0 -44 8t-13 33l-2 37h-4q-35 -45 -101.5 -70.5t-181.5 -25.5h-6q-141 0 -213.5 66.5 t-72.5 182.5zM258 1444q0 18 23 43l115 125q23 23 43 22q18 0 47 -26l307 -297q14 -14 14 -29q0 -18 -33 -51l-69 -70q-14 -14 -24.5 -21t-22.5 -7q-10 0 -21.5 5t-21.5 13l-330 252q-27 20 -27 41zM274 293q0 -37 22.5 -57.5t78.5 -20.5h10q72 0 109 31q18 16 27 37.5 t20 62.5l10 57q-23 4 -53.5 5.5t-59.5 1.5h-22q-74 0 -108 -30t-34 -87z" />
<glyph unicode="&#xe1;" horiz-adv-x="1030" d="M-20 231q0 35 4 74t15 77t28.5 72.5t42.5 61.5q49 53 136 80t216 27h12q41 0 86 -2.5t68 -6.5q6 23 9 41.5t3 28.5v4q0 41 -34 59.5t-107 18.5h-6q-39 0 -87.5 -6t-86.5 -17q-16 -4 -41 -9t-37 -5q-29 0 -31 33l-10 104q-2 20 -3 38t-1 28q0 14 6 22.5t24 14.5 q31 10 75 19t93 17.5t100.5 12.5t100.5 4h6q104 0 174 -17.5t117 -52.5q37 -29 57.5 -72.5t20.5 -115.5q0 -29 -7.5 -93.5t-29.5 -174.5l-88 -439q-6 -31 -22.5 -44t-57.5 -13h-107q-33 0 -44 8t-13 33l-2 37h-4q-35 -45 -101.5 -70.5t-181.5 -25.5h-6q-141 0 -213.5 66.5 t-72.5 182.5zM274 293q0 -37 22.5 -57.5t78.5 -20.5h10q72 0 109 31q18 16 27 37.5t20 62.5l10 57q-23 4 -53.5 5.5t-59.5 1.5h-22q-74 0 -108 -30t-34 -87zM377 1260q0 16 30 38l361 265q10 8 21.5 13t21.5 5q20 0 35 -27l73 -127q10 -16 11 -30q0 -16 -10.5 -26.5 t-36.5 -27.5l-359 -215q-23 -14 -39 -14q-12 0 -19 6t-18 21l-59 90q-12 18 -12 29z" />
<glyph unicode="&#xe2;" horiz-adv-x="1030" d="M-20 231q0 35 4 74t15 77t28.5 72.5t42.5 61.5q49 53 136 80t216 27h12q41 0 86 -2.5t68 -6.5q6 23 9 41.5t3 28.5v4q0 41 -34 59.5t-107 18.5h-6q-39 0 -87.5 -6t-86.5 -17q-16 -4 -41 -9t-37 -5q-29 0 -31 33l-10 104q-2 20 -3 38t-1 28q0 14 6 22.5t24 14.5 q31 10 75 19t93 17.5t100.5 12.5t100.5 4h6q104 0 174 -17.5t117 -52.5q37 -29 57.5 -72.5t20.5 -115.5q0 -29 -7.5 -93.5t-29.5 -174.5l-88 -439q-6 -31 -22.5 -44t-57.5 -13h-107q-33 0 -44 8t-13 33l-2 37h-4q-35 -45 -101.5 -70.5t-181.5 -25.5h-6q-141 0 -213.5 66.5 t-72.5 182.5zM274 293q0 -37 22.5 -57.5t78.5 -20.5h10q72 0 109 31q18 16 27 37.5t20 62.5l10 57q-23 4 -53.5 5.5t-59.5 1.5h-22q-74 0 -108 -30t-34 -87zM295 1223q-25 25 2 51l233 219q16 16 30.5 21.5t31.5 5.5h125q16 0 27.5 -5.5t21.5 -21.5l143 -215q12 -18 13 -31 q0 -8 -4.5 -14t-18.5 -16l-104 -68q-8 -4 -16.5 -9t-16.5 -5q-16 0 -33 22l-111 154h-10l-170 -154q-16 -14 -23 -18t-18 -4q-12 0 -24 12z" />
<glyph unicode="&#xe3;" horiz-adv-x="1030" d="M-20 231q0 35 4 74t15 77t28.5 72.5t42.5 61.5q49 53 136 80t216 27h12q41 0 86 -2.5t68 -6.5q6 23 9 41.5t3 28.5v4q0 41 -34 59.5t-107 18.5h-6q-39 0 -87.5 -6t-86.5 -17q-16 -4 -41 -9t-37 -5q-29 0 -31 33l-10 104q-2 20 -3 38t-1 28q0 14 6 22.5t24 14.5 q31 10 75 19t93 17.5t100.5 12.5t100.5 4h6q104 0 174 -17.5t117 -52.5q37 -29 57.5 -72.5t20.5 -115.5q0 -29 -7.5 -93.5t-29.5 -174.5l-88 -439q-6 -31 -22.5 -44t-57.5 -13h-107q-33 0 -44 8t-13 33l-2 37h-4q-35 -45 -101.5 -70.5t-181.5 -25.5h-6q-141 0 -213.5 66.5 t-72.5 182.5zM260 1251q51 86 118 140.5t147 54.5q49 0 79.5 -14.5t54 -33t45 -32.5t50.5 -14t51.5 17t50.5 58q14 18 25.5 18.5t28.5 -9.5l75 -50q31 -18 11 -53q-51 -86 -118 -140t-146 -54q-49 0 -79 14t-53.5 32.5t-45 33t-52.5 14.5q-29 0 -51.5 -17.5t-50.5 -58.5 q-14 -18 -25.5 -18t-27.5 10l-76 49q-31 18 -11 53zM274 293q0 -37 22.5 -57.5t78.5 -20.5h10q72 0 109 31q18 16 27 37.5t20 62.5l10 57q-23 4 -53.5 5.5t-59.5 1.5h-22q-74 0 -108 -30t-34 -87z" />
<glyph unicode="&#xe4;" horiz-adv-x="1030" d="M-20 231q0 35 4 74t15 77t28.5 72.5t42.5 61.5q49 53 136 80t216 27h12q41 0 86 -2.5t68 -6.5q6 23 9 41.5t3 28.5v4q0 41 -34 59.5t-107 18.5h-6q-39 0 -87.5 -6t-86.5 -17q-16 -4 -41 -9t-37 -5q-29 0 -31 33l-10 104q-2 20 -3 38t-1 28q0 14 6 22.5t24 14.5 q31 10 75 19t93 17.5t100.5 12.5t100.5 4h6q104 0 174 -17.5t117 -52.5q37 -29 57.5 -72.5t20.5 -115.5q0 -29 -7.5 -93.5t-29.5 -174.5l-88 -439q-6 -31 -22.5 -44t-57.5 -13h-107q-33 0 -44 8t-13 33l-2 37h-4q-35 -45 -101.5 -70.5t-181.5 -25.5h-6q-141 0 -213.5 66.5 t-72.5 182.5zM274 293q0 -37 22.5 -57.5t78.5 -20.5h10q72 0 109 31q18 16 27 37.5t20 62.5l10 57q-23 4 -53.5 5.5t-59.5 1.5h-22q-74 0 -108 -30t-34 -87zM309 1255q0 23 6 48l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -6 -47l-2 -9q-16 -63 -50 -98 t-106 -35h-14q-51 0 -80 24.5t-29 69.5zM690 1255q0 23 6 48l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47l-2 -9q-16 -63 -50 -98t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1030" d="M-20 231q0 35 4 74t15 77t28.5 72.5t42.5 61.5q49 53 136 80t216 27h12q41 0 86 -2.5t68 -6.5q6 23 9 41.5t3 28.5v4q0 41 -34 59.5t-107 18.5h-6q-39 0 -87.5 -6t-86.5 -17q-16 -4 -41 -9t-37 -5q-29 0 -31 33l-10 104q-2 20 -3 38t-1 28q0 14 6 22.5t24 14.5 q31 10 75 19t93 17.5t100.5 12.5t100.5 4h6q104 0 174 -17.5t117 -52.5q37 -29 57.5 -72.5t20.5 -115.5q0 -29 -7.5 -93.5t-29.5 -174.5l-88 -439q-6 -31 -22.5 -44t-57.5 -13h-107q-33 0 -44 8t-13 33l-2 37h-4q-35 -45 -101.5 -70.5t-181.5 -25.5h-6q-141 0 -213.5 66.5 t-72.5 182.5zM274 293q0 -37 22.5 -57.5t78.5 -20.5h10q72 0 109 31q18 16 27 37.5t20 62.5l10 57q-23 4 -53.5 5.5t-59.5 1.5h-22q-74 0 -108 -30t-34 -87zM418 1296q0 53 18.5 98.5t51.5 78t78 51t96 18.5q82 0 129 -47t47 -125q0 -53 -18.5 -98t-51 -78t-78 -51.5 t-96.5 -18.5q-78 0 -127 47.5t-49 124.5zM568 1319q0 -23 14.5 -34t34.5 -11q29 0 50 19.5t21 54.5q0 23 -14 34t-35 11q-29 0 -50 -19.5t-21 -54.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1560" d="M-20 225q0 41 5 85t21 93q33 106 129 163t285 57h14q41 0 86 -2.5t68 -6.5q6 23 9 41.5t3 28.5v4q0 41 -34 59.5t-107 18.5h-6q-39 0 -87.5 -6t-86.5 -17q-16 -4 -41 -9t-37 -5q-29 0 -31 33l-10 104q-2 20 -3 38t-1 28q0 14 6 22.5t24 14.5q31 10 75 19t93 17.5 t100.5 12.5t100.5 4h10q86 0 150.5 -16.5t105.5 -69.5h8q53 37 123 61.5t156 24.5h6q92 0 165 -19.5t120 -64.5q35 -35 54.5 -83t19.5 -120q0 -43 -8.5 -99t-22.5 -134l-10 -56q-6 -33 -21.5 -45t-48.5 -12h-485q-8 -39 -9 -72q0 -41 34 -60t136 -19h13q49 0 109.5 9 t107.5 19q8 2 18 4t21 2q27 0 30 -34l9 -95q2 -18 3 -34.5t1 -26.5q0 -33 -64 -53q-55 -16 -145 -31.5t-183 -15.5h-28q-123 0 -203 32.5t-117 79.5h-4q-63 -59 -145 -85.5t-189 -26.5h-14q-135 0 -206.5 65.5t-71.5 177.5zM274 283q0 -29 22.5 -48.5t70.5 -19.5h18 q72 0 109 31q18 16 28 37.5t19 62.5l10 57q-23 4 -53.5 5.5t-59.5 1.5h-16q-68 0 -96.5 -14.5t-42.5 -53.5q-4 -12 -6.5 -28.5t-2.5 -30.5zM915 602h224q20 0 26 21l4 18q8 39 9 64q0 88 -105 88h-4q-63 0 -96 -31q-20 -18 -30.5 -46t-20.5 -79z" />
<glyph unicode="&#xe7;" horiz-adv-x="925" d="M-4 264q0 37 5 84t15 107l29 155q23 123 64 201t112 127q53 37 134 60.5t184 23.5h10q39 0 81 -5t83 -14.5t78.5 -22.5t68.5 -28q18 -10 24.5 -19t6.5 -20q0 -14 -6 -29.5t-17 -39.5l-43 -96q-18 -39 -41 -39q-18 0 -36 10q-37 20 -85.5 32.5t-89.5 12.5h-10 q-80 0 -129 -43q-29 -25 -46 -64t-30 -108l-16 -92q-6 -35 -10 -63.5t-4 -51.5q0 -53 31.5 -78.5t111.5 -25.5h10q41 0 94.5 9t90.5 21q12 4 19 5t13 1q25 0 31 -34l10 -103q2 -16 3 -31.5t1 -29.5q0 -18 -12 -28.5t-43 -20.5q-57 -18 -122.5 -28.5t-147.5 -14.5q0 -2 -1 -3 t-1 -6l-37 -84q31 6 57 7q63 0 107.5 -34t46.5 -112q0 -109 -78 -179.5t-205 -70.5q-61 0 -117.5 9.5t-85.5 21.5q-33 12 -20 53l22 72q8 23 19.5 27t32.5 -2q25 -8 61.5 -14.5t69.5 -6.5q35 0 55.5 12.5t20.5 42.5q0 33 -54 33h-6q-31 0 -51.5 -5t-36.5 -5q-10 0 -14 4 t-8 10l-19 29q-8 14 -8 27q0 10 14 43l64 131q0 4 2 6q-74 10 -122 33.5t-77 58.5t-41 81t-12 101z" />
<glyph unicode="&#xe8;" horiz-adv-x="1009" d="M-4 287q0 53 6 109.5t16 105.5l15 71q33 160 79 243t107 126q53 37 137 58.5t201 21.5h6q92 0 165 -19.5t120 -64.5q35 -35 54.5 -83t19.5 -120q0 -43 -8.5 -99t-22.5 -134l-10 -56q-6 -35 -21.5 -46t-48.5 -11h-485q-8 -39 -9 -72q0 -41 34 -60t136 -19h13q47 0 108.5 9 t108.5 19q8 2 18 4t21 2q27 0 30 -34l9 -95q2 -18 3 -34.5t1 -26.5q0 -20 -14.5 -31.5t-49.5 -21.5q-55 -16 -145 -31.5t-182 -15.5h-29q-182 0 -282.5 67.5t-100.5 237.5zM215 1444q0 18 23 43l115 125q23 23 43 22q18 0 47 -26l307 -297q14 -14 14 -29q0 -18 -33 -51 l-69 -70q-14 -14 -24.5 -21t-22.5 -7q-10 0 -21.5 5t-21.5 13l-330 252q-27 20 -27 41zM365 602h223q20 0 26 21l4 18q8 39 9 64q0 88 -105 88h-4q-63 0 -96 -31q-20 -18 -30.5 -46t-20.5 -79z" />
<glyph unicode="&#xe9;" horiz-adv-x="1009" d="M-4 287q0 53 6 109.5t16 105.5l15 71q33 160 79 243t107 126q53 37 137 58.5t201 21.5h6q92 0 165 -19.5t120 -64.5q35 -35 54.5 -83t19.5 -120q0 -43 -8.5 -99t-22.5 -134l-10 -56q-6 -35 -21.5 -46t-48.5 -11h-485q-8 -39 -9 -72q0 -41 34 -60t136 -19h13q47 0 108.5 9 t108.5 19q8 2 18 4t21 2q27 0 30 -34l9 -95q2 -18 3 -34.5t1 -26.5q0 -20 -14.5 -31.5t-49.5 -21.5q-55 -16 -145 -31.5t-182 -15.5h-29q-182 0 -282.5 67.5t-100.5 237.5zM348 1260q0 16 30 38l361 265q10 8 21.5 13t21.5 5q20 0 35 -27l73 -127q10 -16 11 -30 q0 -16 -10.5 -26.5t-36.5 -27.5l-359 -215q-23 -14 -39 -14q-12 0 -19 6t-18 21l-59 90q-12 18 -12 29zM365 602h223q20 0 26 21l4 18q8 39 9 64q0 88 -105 88h-4q-63 0 -96 -31q-20 -18 -30.5 -46t-20.5 -79z" />
<glyph unicode="&#xea;" horiz-adv-x="1009" d="M-4 287q0 53 6 109.5t16 105.5l15 71q33 160 79 243t107 126q53 37 137 58.5t201 21.5h6q92 0 165 -19.5t120 -64.5q35 -35 54.5 -83t19.5 -120q0 -43 -8.5 -99t-22.5 -134l-10 -56q-6 -35 -21.5 -46t-48.5 -11h-485q-8 -39 -9 -72q0 -41 34 -60t136 -19h13q47 0 108.5 9 t108.5 19q8 2 18 4t21 2q27 0 30 -34l9 -95q2 -18 3 -34.5t1 -26.5q0 -20 -14.5 -31.5t-49.5 -21.5q-55 -16 -145 -31.5t-182 -15.5h-29q-182 0 -282.5 67.5t-100.5 237.5zM266 1223q-25 25 2 51l233 219q16 16 30.5 21.5t31.5 5.5h125q16 0 27.5 -5.5t21.5 -21.5l143 -215 q12 -18 13 -31q0 -8 -4.5 -14t-18.5 -16l-104 -68q-8 -4 -16.5 -9t-16.5 -5q-16 0 -33 22l-111 154h-10l-170 -154q-16 -14 -23 -18t-18 -4q-12 0 -24 12zM365 602h223q20 0 26 21l4 18q8 39 9 64q0 88 -105 88h-4q-63 0 -96 -31q-20 -18 -30.5 -46t-20.5 -79z" />
<glyph unicode="&#xeb;" horiz-adv-x="1009" d="M-4 287q0 53 6 109.5t16 105.5l15 71q33 160 79 243t107 126q53 37 137 58.5t201 21.5h6q92 0 165 -19.5t120 -64.5q35 -35 54.5 -83t19.5 -120q0 -43 -8.5 -99t-22.5 -134l-10 -56q-6 -35 -21.5 -46t-48.5 -11h-485q-8 -39 -9 -72q0 -41 34 -60t136 -19h13q47 0 108.5 9 t108.5 19q8 2 18 4t21 2q27 0 30 -34l9 -95q2 -18 3 -34.5t1 -26.5q0 -20 -14.5 -31.5t-49.5 -21.5q-55 -16 -145 -31.5t-182 -15.5h-29q-182 0 -282.5 67.5t-100.5 237.5zM264 1255q0 23 6 48l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -6 -47l-2 -9 q-16 -63 -50 -98t-106 -35h-14q-51 0 -80 24.5t-29 69.5zM365 602h223q20 0 26 21l4 18q8 39 9 64q0 88 -105 88h-4q-63 0 -96 -31q-20 -18 -30.5 -46t-20.5 -79zM645 1255q0 23 6 48l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47l-2 -9q-16 -63 -50 -98 t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5z" />
<glyph unicode="&#xec;" horiz-adv-x="544" d="M-23 35q0 12 4.5 32.5t10.5 49.5l160 827q6 29 22 44.5t57 15.5h189q33 0 45 -9.5t12 -27.5q0 -12 -4 -35t-14 -76l-156 -797q-4 -29 -20.5 -44t-57.5 -15h-188q-35 0 -47.5 9t-12.5 26zM28 1444q0 18 23 43l115 125q23 23 43 22q18 0 47 -26l307 -297q14 -14 14 -29 q0 -18 -33 -51l-69 -70q-14 -14 -24.5 -21t-22.5 -7q-10 0 -21.5 5t-21.5 13l-330 252q-27 20 -27 41z" />
<glyph unicode="&#xed;" horiz-adv-x="544" d="M-23 35q0 12 4.5 32.5t10.5 49.5l160 827q6 29 22 44.5t57 15.5h189q33 0 45 -9.5t12 -27.5q0 -12 -4 -35t-14 -76l-156 -797q-4 -29 -20.5 -44t-57.5 -15h-188q-35 0 -47.5 9t-12.5 26zM148 1260q0 16 30 38l361 265q10 8 21.5 13t21.5 5q20 0 35 -27l73 -127 q10 -16 11 -30q0 -16 -10.5 -26.5t-36.5 -27.5l-359 -215q-23 -14 -39 -14q-12 0 -19 6t-18 21l-59 90q-12 18 -12 29z" />
<glyph unicode="&#xee;" horiz-adv-x="544" d="M-23 35q0 12 4.5 32.5t10.5 49.5l160 827q6 29 22 44.5t57 15.5h189q33 0 45 -9.5t12 -27.5q0 -12 -4 -35t-14 -76l-156 -797q-4 -29 -20.5 -44t-57.5 -15h-188q-35 0 -47.5 9t-12.5 26zM72 1223q-25 25 2 51l233 219q16 16 30.5 21.5t31.5 5.5h125q16 0 27.5 -5.5 t21.5 -21.5l143 -215q12 -18 13 -31q0 -8 -4.5 -14t-18.5 -16l-104 -68q-8 -4 -16.5 -9t-16.5 -5q-16 0 -33 22l-111 154h-10l-170 -154q-16 -14 -23 -18t-18 -4q-12 0 -24 12z" />
<glyph unicode="&#xef;" horiz-adv-x="544" d="M-23 35q0 12 4.5 32.5t10.5 49.5l160 827q6 29 22 44.5t57 15.5h189q33 0 45 -9.5t12 -27.5q0 -12 -4 -35t-14 -76l-156 -797q-4 -29 -20.5 -44t-57.5 -15h-188q-35 0 -47.5 9t-12.5 26zM57 1255q0 23 6 48l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5 q0 -23 -6 -47l-2 -9q-16 -63 -50 -98t-106 -35h-14q-51 0 -80 24.5t-29 69.5zM438 1255q0 23 6 48l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47l-2 -9q-16 -63 -50 -98t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1050" d="M4 301q0 18 2 41t4 47.5t5 47t8 38.5l14 76q20 109 53 180.5t80 118.5q59 59 141 86t183 27h2q59 0 108 -11.5t80 -27.5h4q-2 59 -23.5 111t-70.5 95l-74 -81q-20 -23 -31.5 -23t-33.5 16l-52 43q-23 16 -21.5 28.5t19.5 33.5l66 74l-86 47q-29 16 -29 32q0 12 15 33 l75 115q8 10 14.5 16t16.5 6t21.5 -4t27.5 -12q29 -12 54.5 -24.5t52.5 -26.5l82 90q20 23 31.5 23t33.5 -17l51 -43q23 -16 22 -28.5t-20 -32.5l-67 -76q100 -70 153.5 -178.5t53.5 -261.5q0 -29 -2 -66t-7.5 -78t-10.5 -81t-11 -72l-31 -164q-23 -125 -58.5 -208 t-92.5 -132q-59 -53 -148.5 -74.5t-220.5 -21.5h-20q-190 0 -276.5 85t-86.5 234zM317 344q0 -106 107 -106h12q66 0 98.5 33.5t47.5 103.5l36 188q6 33 7 58q0 41 -25 62.5t-80 21.5h-6q-78 0 -113.5 -39t-52.5 -127l-18 -93q-4 -27 -8.5 -54t-4.5 -48z" />
<glyph unicode="&#xf1;" horiz-adv-x="1075" d="M-33 37q0 10 3 27.5t12 58.5l159 823q6 31 22.5 44.5t51.5 13.5h107q31 0 43 -7.5t16 -29.5l6 -43h6q55 49 131 73.5t181 24.5h4q127 0 199.5 -49t72.5 -164q0 -37 -4 -82t-19 -111l-110 -557q-6 -29 -20.5 -44t-55.5 -15h-186q-35 0 -48.5 9t-13.5 30q0 23 13 78l98 508 q6 33 6 57q0 41 -25.5 59.5t-72.5 18.5q-25 0 -48.5 -6t-41.5 -21q-35 -29 -50 -108l-110 -566q-6 -29 -20.5 -44t-55.5 -15h-190q-59 0 -60 37zM274 1251q51 86 118 140.5t147 54.5q49 0 79.5 -14.5t54 -33t45 -32.5t50.5 -14t51.5 17t50.5 58q14 18 25.5 18.5t28.5 -9.5 l75 -50q31 -18 11 -53q-51 -86 -118 -140t-146 -54q-49 0 -79 14t-53.5 32.5t-45 33t-52.5 14.5q-29 0 -51.5 -17.5t-50.5 -58.5q-14 -18 -25.5 -18t-27.5 10l-76 49q-31 18 -11 53z" />
<glyph unicode="&#xf2;" horiz-adv-x="1064" d="M-4 272q0 27 5 69t13.5 93t18.5 105.5t22 103.5q27 113 70 184.5t121 120.5q59 37 136 55.5t185 18.5h37q98 0 169 -21.5t116 -58.5t65.5 -91t20.5 -120q0 -53 -14.5 -148.5t-45.5 -215.5q-33 -127 -74.5 -198t-105.5 -112q-63 -41 -143 -58t-184 -17h-41 q-98 0 -169 21.5t-116 57.5q-45 37 -65.5 91.5t-20.5 119.5zM305 1444q0 18 23 43l115 125q23 23 43 22q18 0 47 -26l307 -297q14 -14 14 -29q0 -18 -33 -51l-69 -70q-14 -14 -24.5 -21t-22.5 -7q-10 0 -21.5 5t-21.5 13l-330 252q-27 20 -27 41zM324 322q0 -41 21.5 -62.5 t74.5 -21.5h4q29 0 55.5 6t44.5 20q29 23 46.5 62t33.5 116q23 100 31 154.5t8 85.5q0 41 -21.5 62.5t-74.5 21.5h-4q-29 0 -55.5 -6t-45.5 -21q-29 -23 -45.5 -61.5t-34.5 -116.5q-23 -100 -30.5 -154.5t-7.5 -84.5z" />
<glyph unicode="&#xf3;" horiz-adv-x="1064" d="M-4 272q0 27 5 69t13.5 93t18.5 105.5t22 103.5q27 113 70 184.5t121 120.5q59 37 136 55.5t185 18.5h37q98 0 169 -21.5t116 -58.5t65.5 -91t20.5 -120q0 -53 -14.5 -148.5t-45.5 -215.5q-33 -127 -74.5 -198t-105.5 -112q-63 -41 -143 -58t-184 -17h-41 q-98 0 -169 21.5t-116 57.5q-45 37 -65.5 91.5t-20.5 119.5zM324 322q0 -41 21.5 -62.5t74.5 -21.5h4q29 0 55.5 6t44.5 20q29 23 46.5 62t33.5 116q23 100 31 154.5t8 85.5q0 41 -21.5 62.5t-74.5 21.5h-4q-29 0 -55.5 -6t-45.5 -21q-29 -23 -45.5 -61.5t-34.5 -116.5 q-23 -100 -30.5 -154.5t-7.5 -84.5zM412 1260q0 16 30 38l361 265q10 8 21.5 13t21.5 5q20 0 35 -27l73 -127q10 -16 11 -30q0 -16 -10.5 -26.5t-36.5 -27.5l-359 -215q-23 -14 -39 -14q-12 0 -19 6t-18 21l-59 90q-12 18 -12 29z" />
<glyph unicode="&#xf4;" horiz-adv-x="1064" d="M-4 272q0 27 5 69t13.5 93t18.5 105.5t22 103.5q27 113 70 184.5t121 120.5q59 37 136 55.5t185 18.5h37q98 0 169 -21.5t116 -58.5t65.5 -91t20.5 -120q0 -53 -14.5 -148.5t-45.5 -215.5q-33 -127 -74.5 -198t-105.5 -112q-63 -41 -143 -58t-184 -17h-41 q-98 0 -169 21.5t-116 57.5q-45 37 -65.5 91.5t-20.5 119.5zM324 322q0 -41 21.5 -62.5t74.5 -21.5h4q29 0 55.5 6t44.5 20q29 23 46.5 62t33.5 116q23 100 31 154.5t8 85.5q0 41 -21.5 62.5t-74.5 21.5h-4q-29 0 -55.5 -6t-45.5 -21q-29 -23 -45.5 -61.5t-34.5 -116.5 q-23 -100 -30.5 -154.5t-7.5 -84.5zM332 1223q-25 25 2 51l233 219q16 16 30.5 21.5t31.5 5.5h125q16 0 27.5 -5.5t21.5 -21.5l143 -215q12 -18 13 -31q0 -8 -4.5 -14t-18.5 -16l-104 -68q-8 -4 -16.5 -9t-16.5 -5q-16 0 -33 22l-111 154h-10l-170 -154q-16 -14 -23 -18 t-18 -4q-12 0 -24 12z" />
<glyph unicode="&#xf5;" horiz-adv-x="1064" d="M-4 272q0 27 5 69t13.5 93t18.5 105.5t22 103.5q27 113 70 184.5t121 120.5q59 37 136 55.5t185 18.5h37q98 0 169 -21.5t116 -58.5t65.5 -91t20.5 -120q0 -53 -14.5 -148.5t-45.5 -215.5q-33 -127 -74.5 -198t-105.5 -112q-63 -41 -143 -58t-184 -17h-41 q-98 0 -169 21.5t-116 57.5q-45 37 -65.5 91.5t-20.5 119.5zM284 1251q51 86 118 140.5t147 54.5q49 0 79.5 -14.5t54 -33t45 -32.5t50.5 -14t51.5 17t50.5 58q14 18 25.5 18.5t28.5 -9.5l75 -50q31 -18 11 -53q-51 -86 -118 -140t-146 -54q-49 0 -79 14t-53.5 32.5t-45 33 t-52.5 14.5q-29 0 -51.5 -17.5t-50.5 -58.5q-14 -18 -25.5 -18t-27.5 10l-76 49q-31 18 -11 53zM324 322q0 -41 21.5 -62.5t74.5 -21.5h4q29 0 55.5 6t44.5 20q29 23 46.5 62t33.5 116q23 100 31 154.5t8 85.5q0 41 -21.5 62.5t-74.5 21.5h-4q-29 0 -55.5 -6t-45.5 -21 q-29 -23 -45.5 -61.5t-34.5 -116.5q-23 -100 -30.5 -154.5t-7.5 -84.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1064" d="M-4 272q0 27 5 69t13.5 93t18.5 105.5t22 103.5q27 113 70 184.5t121 120.5q59 37 136 55.5t185 18.5h37q98 0 169 -21.5t116 -58.5t65.5 -91t20.5 -120q0 -53 -14.5 -148.5t-45.5 -215.5q-33 -127 -74.5 -198t-105.5 -112q-63 -41 -143 -58t-184 -17h-41 q-98 0 -169 21.5t-116 57.5q-45 37 -65.5 91.5t-20.5 119.5zM303 1255q0 23 6 48l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -6 -47l-2 -9q-16 -63 -50 -98t-106 -35h-14q-51 0 -80 24.5t-29 69.5zM324 322q0 -41 21.5 -62.5t74.5 -21.5h4q29 0 55.5 6 t44.5 20q29 23 46.5 62t33.5 116q23 100 31 154.5t8 85.5q0 41 -21.5 62.5t-74.5 21.5h-4q-29 0 -55.5 -6t-45.5 -21q-29 -23 -45.5 -61.5t-34.5 -116.5q-23 -100 -30.5 -154.5t-7.5 -84.5zM684 1255q0 23 6 48l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5 q0 -23 -7 -47l-2 -9q-16 -63 -50 -98t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5z" />
<glyph unicode="&#xf7;" d="M70 461l24 127q10 49 25.5 65.5t56.5 16.5h825q41 0 51.5 -18.5t2.5 -55.5l-25 -127q-10 -49 -25.5 -65.5t-56.5 -16.5h-825q-41 0 -51.5 18.5t-1.5 55.5zM313 129l2 14q18 92 69.5 126t117.5 34h24q70 0 106 -33.5t17 -126.5l-2 -14q-16 -84 -66.5 -124t-119.5 -40h-25 q-92 0 -115.5 40t-7.5 124zM465 915l4 15q16 92 68.5 126t117.5 34h25q70 0 104.5 -34t18.5 -126l-4 -15q-16 -84 -65.5 -123.5t-119.5 -39.5h-24q-92 0 -116.5 39.5t-8.5 123.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1064" d="M-51.5 -50.5q0.5 11.5 16.5 34.5l86 106q-29 35 -42 82t-13 100q0 27 5 69t13.5 93t18.5 105.5t22 103.5q27 113 70 184.5t121 120.5q59 37 136 55.5t185 18.5h37q53 0 97 -7t81 -18l99 121q14 18 26.5 23.5t32.5 -11.5l49 -38q23 -16 23 -27.5t-17 -34.5l-86 -106 q66 -72 66 -193q0 -53 -14.5 -148.5t-45.5 -215.5q-33 -127 -74.5 -198t-105.5 -112q-63 -41 -143 -58t-184 -17h-41q-57 0 -105.5 8t-89.5 20l-92 -114q-14 -18 -26.5 -23.5t-33.5 10.5l-49 39q-23 16 -22.5 27.5zM338 444l254 316q-18 6 -45 6h-4q-29 0 -55.5 -6 t-45.5 -21q-29 -23 -45 -61.5t-35 -116.5zM362 246q18 -8 58 -8h4q29 0 55.5 6t44.5 20q29 23 46.5 61.5t33.5 116.5q10 43 16.5 76t10.5 62z" />
<glyph unicode="&#xf9;" horiz-adv-x="1052" d="M2 229q0 33 5 71t16 85l108 559q6 31 22.5 45.5t57.5 14.5h186q35 0 48.5 -9.5t13.5 -25.5q0 -10 -5 -41t-16 -76l-94 -485q-6 -31 -6 -56q0 -66 90 -65h8q47 0 84 26q16 14 26.5 36t20.5 69l111 567q6 31 22.5 45.5t57.5 14.5h186q35 0 48.5 -10.5t13.5 -28.5 q0 -14 -13 -76l-106 -545q-20 -104 -54 -169.5t-90 -106.5q-61 -45 -138 -65.5t-204 -20.5h-22q-178 0 -277.5 60t-99.5 187zM284 1444q0 18 23 43l115 125q23 23 43 22q18 0 47 -26l307 -297q14 -14 14 -29q0 -18 -33 -51l-69 -70q-14 -14 -24.5 -21t-22.5 -7 q-10 0 -21.5 5t-21.5 13l-330 252q-27 20 -27 41z" />
<glyph unicode="&#xfa;" horiz-adv-x="1052" d="M2 229q0 33 5 71t16 85l108 559q6 31 22.5 45.5t57.5 14.5h186q35 0 48.5 -9.5t13.5 -25.5q0 -10 -5 -41t-16 -76l-94 -485q-6 -31 -6 -56q0 -66 90 -65h8q47 0 84 26q16 14 26.5 36t20.5 69l111 567q6 31 22.5 45.5t57.5 14.5h186q35 0 48.5 -10.5t13.5 -28.5 q0 -14 -13 -76l-106 -545q-20 -104 -54 -169.5t-90 -106.5q-61 -45 -138 -65.5t-204 -20.5h-22q-178 0 -277.5 60t-99.5 187zM422 1260q0 16 30 38l361 265q10 8 21.5 13t21.5 5q20 0 35 -27l73 -127q10 -16 11 -30q0 -16 -10.5 -26.5t-36.5 -27.5l-359 -215 q-23 -14 -39 -14q-12 0 -19 6t-18 21l-59 90q-12 18 -12 29z" />
<glyph unicode="&#xfb;" horiz-adv-x="1052" d="M2 229q0 33 5 71t16 85l108 559q6 31 22.5 45.5t57.5 14.5h186q35 0 48.5 -9.5t13.5 -25.5q0 -10 -5 -41t-16 -76l-94 -485q-6 -31 -6 -56q0 -66 90 -65h8q47 0 84 26q16 14 26.5 36t20.5 69l111 567q6 31 22.5 45.5t57.5 14.5h186q35 0 48.5 -10.5t13.5 -28.5 q0 -14 -13 -76l-106 -545q-20 -104 -54 -169.5t-90 -106.5q-61 -45 -138 -65.5t-204 -20.5h-22q-178 0 -277.5 60t-99.5 187zM318 1223q-25 25 2 51l233 219q16 16 30.5 21.5t31.5 5.5h125q16 0 27.5 -5.5t21.5 -21.5l143 -215q12 -18 13 -31q0 -8 -4.5 -14t-18.5 -16 l-104 -68q-8 -4 -16.5 -9t-16.5 -5q-16 0 -33 22l-111 154h-10l-170 -154q-16 -14 -23 -18t-18 -4q-12 0 -24 12z" />
<glyph unicode="&#xfc;" horiz-adv-x="1052" d="M2 229q0 33 5 71t16 85l108 559q6 31 22.5 45.5t57.5 14.5h186q35 0 48.5 -9.5t13.5 -25.5q0 -10 -5 -41t-16 -76l-94 -485q-6 -31 -6 -56q0 -66 90 -65h8q47 0 84 26q16 14 26.5 36t20.5 69l111 567q6 31 22.5 45.5t57.5 14.5h186q35 0 48.5 -10.5t13.5 -28.5 q0 -14 -13 -76l-106 -545q-20 -104 -54 -169.5t-90 -106.5q-61 -45 -138 -65.5t-204 -20.5h-22q-178 0 -277.5 60t-99.5 187zM297 1255q0 23 6 48l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -6 -47l-2 -9q-16 -63 -50 -98t-106 -35h-14q-51 0 -80 24.5 t-29 69.5zM678 1255q0 23 6 48l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47l-2 -9q-16 -63 -50 -98t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="999" d="M47 944q-2 59 74 60h172q35 0 48 -9.5t13 -40.5q4 -182 12.5 -356t22.5 -328h10q41 82 81 169t78 175t72 172t60 162q10 33 28.5 44.5t49.5 11.5h197q70 -1 43 -62q-113 -281 -273 -606.5t-401 -706.5q-29 -47 -64 -47q-12 0 -27.5 5t-33.5 14l-109 43q-27 10 -35 21 t-8 24q0 12 5.5 22t13.5 21q20 31 42.5 64.5t44 67.5t41 64.5t34.5 55.5q-82 27 -107 151q-12 70 -25.5 173.5t-24.5 217t-19.5 224t-14.5 194.5zM385 1260q0 16 30 38l361 265q10 8 21.5 13t21.5 5q20 0 35 -27l73 -127q10 -16 11 -30q0 -16 -10.5 -26.5t-36.5 -27.5 l-359 -215q-23 -14 -39 -14q-12 0 -19 6t-18 21l-59 90q-12 18 -12 29z" />
<glyph unicode="&#xfe;" horiz-adv-x="1087" d="M-102 -324q0 10 1 24.5t11 61.5l309 1575q6 29 20.5 39t47.5 10h200q35 0 46.5 -9t11.5 -25t-5 -43t-12 -56l-53 -274q94 43 234 43h14q137 0 209 -58.5t72 -187.5q0 -37 -11.5 -115.5t-40.5 -234.5q-23 -123 -54.5 -214t-98.5 -146q-61 -51 -132 -67.5t-169 -16.5h-11 q-51 0 -108 12t-92 29l-62 -314q-8 -45 -27.5 -58t-72.5 -13h-160q-68 0 -67 38zM346 311q0 -41 25.5 -57t79.5 -16h10q45 0 73.5 13t47 39.5t30 68.5t23.5 98q16 72 25.5 134t9.5 87q0 86 -105 86h-14q-66 0 -98.5 -28.5t-49.5 -110.5l-41 -209q-16 -78 -16 -105z" />
<glyph unicode="&#xff;" horiz-adv-x="999" d="M47 944q-2 59 74 60h172q35 0 48 -9.5t13 -40.5q4 -182 12.5 -356t22.5 -328h10q41 82 81 169t78 175t72 172t60 162q10 33 28.5 44.5t49.5 11.5h197q70 -1 43 -62q-113 -281 -273 -606.5t-401 -706.5q-29 -47 -64 -47q-12 0 -27.5 5t-33.5 14l-109 43q-27 10 -35 21 t-8 24q0 12 5.5 22t13.5 21q20 31 42.5 64.5t44 67.5t41 64.5t34.5 55.5q-82 27 -107 151q-12 70 -25.5 173.5t-24.5 217t-19.5 224t-14.5 194.5zM250 1255q0 23 6 48l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -6 -47l-2 -9q-16 -63 -50 -98t-106 -35h-14 q-51 0 -80 24.5t-29 69.5zM631 1255q0 23 6 48l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47l-2 -9q-16 -63 -50 -98t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5z" />
<glyph unicode="&#x152;" horiz-adv-x="1806" d="M57 352q0 43 12.5 143.5t43.5 258.5q18 98 40.5 175t53.5 137t71.5 106.5t96.5 82.5q129 88 338 88h16q84 0 159 -25.5t136 -72.5q45 47 112.5 63.5t168.5 16.5h489q29 0 38 -5t9 -22q0 -8 -3 -27.5t-11 -62.5l-19 -100q-8 -37 -19 -49t-60 -12h-424q-29 0 -40.5 -9.5 t-17.5 -31.5q-10 -35 -22.5 -93.5t-22.5 -113.5h420q31 0 43 -5.5t12 -21.5t-3 -37.5t-13 -62.5l-19 -84q-6 -27 -20 -37t-47 -10h-426q-6 -33 -13.5 -72t-11.5 -74q-4 -29 -6 -49t-2 -37q0 -31 37 -30h426q29 0 41 -6.5t12 -28.5q0 -12 -3 -34t-9 -60l-14 -87 q-8 -37 -23.5 -50t-58.5 -13h-535q-78 0 -123 13.5t-65 47.5q-76 -43 -149.5 -61t-151.5 -18h-17q-117 0 -199.5 27.5t-127.5 68.5q-53 47 -76 112.5t-23 161.5zM410 418q0 -68 26 -101q39 -49 127 -49h17q53 0 108 16.5t111 53.5q10 70 23.5 156t31.5 192q16 98 31.5 174 t32.5 135q-86 61 -185 62h-20q-47 0 -81 -12.5t-63 -36.5q-41 -35 -67.5 -114t-48.5 -194q-23 -117 -33 -179t-10 -103z" />
<glyph unicode="&#x153;" horiz-adv-x="1599" d="M-4 272q0 27 5 69t13.5 93t18.5 105.5t22 103.5q27 113 70 184.5t121 120.5q59 37 135 55.5t184 18.5h25q88 0 149.5 -18.5t104.5 -51.5q115 70 303 70h20q98 0 172 -26.5t107 -63.5q37 -43 51 -90t14 -107q0 -43 -8 -99t-22 -134l-11 -56q-6 -33 -21 -45t-48 -12h-486 q-8 -39 -8 -72q0 -41 34 -60t136 -19h13q49 0 109.5 9t107.5 19q8 2 18 4t21 2q27 0 30 -34l8 -95q2 -18 3.5 -34.5t1.5 -26.5q0 -33 -64 -53q-55 -16 -145 -31.5t-183 -15.5h-28q-94 0 -161 22.5t-110 50.5q-61 -35 -139 -54t-170 -19h-27q-94 0 -164.5 21.5t-115.5 57.5 q-45 37 -65.5 91.5t-20.5 119.5zM324 322q0 -41 21.5 -62.5t74.5 -21.5h4q29 0 55.5 6t44.5 20q29 23 46.5 62t33.5 116q23 100 31 154.5t8 85.5q0 41 -21.5 62.5t-74.5 21.5h-4q-29 0 -55.5 -6t-45.5 -21q-29 -23 -45.5 -61.5t-34.5 -116.5q-23 -100 -30.5 -154.5 t-7.5 -84.5zM954 602h224q25 0 26 21l4 18q4 23 6.5 43.5t2.5 36.5q0 25 -13 43q-20 29 -86 29h-10q-61 0 -92 -29q-18 -16 -31.5 -43t-21.5 -74l-9 -41v-4z" />
<glyph unicode="&#x178;" horiz-adv-x="1085" d="M137 1257q-6 68 49 68h203q45 0 60.5 -5t19.5 -44q4 -66 11 -142.5t16.5 -154.5t19.5 -151.5t21 -133.5h10q33 59 71.5 133t76.5 152t74 154.5t67 142.5q14 39 30.5 44t61.5 5h213q41 0 41 -29q0 -20 -12.5 -45.5t-28.5 -60.5q-49 -100 -107.5 -208t-122 -213 t-128 -204.5t-126.5 -181.5l-61 -320q-8 -37 -23.5 -50t-54.5 -13h-211q-57 0 -57 35q0 20 14 86l53 270q-29 86 -57.5 191.5t-53 220.5t-43 230.5t-26.5 223.5zM379 1577q0 23 6 47l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47l-2 -8q-16 -63 -50 -98 t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5zM760 1577q0 23 6 47l2 8q16 63 50 98t106 35h14q51 0 80 -24.5t29 -69.5q0 -23 -7 -47l-2 -8q-16 -63 -50 -98t-105 -35h-15q-51 0 -79.5 24.5t-28.5 69.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="747" d="M166 1223q-25 25 2 51l233 219q16 16 30.5 21.5t31.5 5.5h125q16 0 27.5 -5.5t21.5 -21.5l143 -215q12 -18 13 -31q0 -8 -4.5 -14t-18.5 -16l-104 -68q-8 -4 -16.5 -9t-16.5 -5q-16 0 -33 22l-111 154h-10l-170 -154q-16 -14 -23 -18t-18 -4q-12 0 -24 12z" />
<glyph unicode="&#x2dc;" horiz-adv-x="706" d="M106 1251q51 86 118 140.5t147 54.5q49 0 79.5 -14.5t54 -33t45 -32.5t50.5 -14t51.5 17t50.5 58q14 18 25.5 18.5t28.5 -9.5l75 -50q31 -18 11 -53q-51 -86 -118 -140t-146 -54q-49 0 -79 14t-53.5 32.5t-45 33t-52.5 14.5q-29 0 -51.5 -17.5t-50.5 -58.5 q-14 -18 -25.5 -18t-27.5 10l-76 49q-31 18 -11 53z" />
<glyph unicode="&#x2000;" horiz-adv-x="938" />
<glyph unicode="&#x2001;" horiz-adv-x="1876" />
<glyph unicode="&#x2002;" horiz-adv-x="938" />
<glyph unicode="&#x2003;" horiz-adv-x="1876" />
<glyph unicode="&#x2004;" horiz-adv-x="625" />
<glyph unicode="&#x2005;" horiz-adv-x="469" />
<glyph unicode="&#x2006;" horiz-adv-x="312" />
<glyph unicode="&#x2007;" horiz-adv-x="312" />
<glyph unicode="&#x2008;" horiz-adv-x="234" />
<glyph unicode="&#x2009;" horiz-adv-x="375" />
<glyph unicode="&#x200a;" horiz-adv-x="104" />
<glyph unicode="&#x2010;" horiz-adv-x="745" d="M33 451v153q0 37 17.5 51.5t64.5 14.5h450q47 0 64.5 -14.5t17.5 -51.5v-153q0 -37 -15.5 -51.5t-66.5 -14.5h-450q-51 0 -66.5 14.5t-15.5 51.5z" />
<glyph unicode="&#x2011;" horiz-adv-x="745" d="M33 451v153q0 37 17.5 51.5t64.5 14.5h450q47 0 64.5 -14.5t17.5 -51.5v-153q0 -37 -15.5 -51.5t-66.5 -14.5h-450q-51 0 -66.5 14.5t-15.5 51.5z" />
<glyph unicode="&#x2012;" horiz-adv-x="745" d="M33 451v153q0 37 17.5 51.5t64.5 14.5h450q47 0 64.5 -14.5t17.5 -51.5v-153q0 -37 -15.5 -51.5t-66.5 -14.5h-450q-51 0 -66.5 14.5t-15.5 51.5z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M33 451v153q0 37 17.5 51.5t64.5 14.5h737q47 0 64.5 -14.5t17.5 -51.5v-153q0 -37 -15.5 -51.5t-66.5 -14.5h-737q-51 0 -66.5 14.5t-15.5 51.5z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M33 451v153q0 37 17.5 51.5t64.5 14.5h1761q47 0 64.5 -14.5t17.5 -51.5v-153q0 -37 -15.5 -51.5t-66.5 -14.5h-1761q-51 0 -66.5 14.5t-15.5 51.5z" />
<glyph unicode="&#x2018;" horiz-adv-x="557" d="M131 934q0 14 2 33.5t10 56.5q8 41 31 96t60.5 118.5t91 131t122.5 131.5q35 31 74 4l76 -53q33 -23 -2 -58q-27 -29 -50.5 -54t-41.5 -48q-27 -29 -43.5 -50.5t-16.5 -41.5q0 -10 13 -27l20 -18q27 -25 27 -64q0 -29 -17 -110q-16 -82 -61 -120t-131 -38h-21 q-78 0 -109.5 27.5t-33.5 83.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="559" d="M149 893q-33 23 2 58q27 29 50.5 54t41.5 48q27 29 43.5 50.5t16.5 41.5q0 10 -13 27l-20 18q-27 25 -27 64q0 29 17 110q16 82 61 120t131 38h21q78 0 109.5 -27.5t33.5 -83.5q0 -14 -2 -33.5t-10 -56.5q-8 -41 -31 -96t-60.5 -118.5t-91 -131t-122.5 -131.5 q-35 -31 -74 -4z" />
<glyph unicode="&#x201a;" horiz-adv-x="557" d="M-102 -291q-33 23 2 58q27 29 50.5 54t41.5 48q27 29 43.5 50.5t16.5 41.5q0 10 -13 27l-20 18q-27 25 -27 64q0 29 17 110q16 82 61 120t131 38h21q78 0 109.5 -27.5t33.5 -83.5q0 -14 -2 -33.5t-10 -56.5q-8 -41 -31 -96t-60.5 -118.5t-91 -131t-122.5 -131.5 q-35 -31 -74 -4z" />
<glyph unicode="&#x201c;" horiz-adv-x="1062" d="M131 934q0 14 2 33.5t10 56.5q8 41 31 96t60.5 118.5t91 131t122.5 131.5q35 31 74 4l76 -53q33 -23 -2 -58q-27 -29 -50.5 -54t-41.5 -48q-27 -29 -43.5 -50.5t-16.5 -41.5q0 -10 13 -27l20 -18q27 -25 27 -64q0 -29 -17 -110q-16 -82 -61 -120t-131 -38h-21 q-78 0 -109.5 27.5t-33.5 83.5zM637 934q0 14 2 33.5t10 56.5q8 41 31 96t60.5 118.5t91 131t122.5 131.5q35 31 74 4l76 -53q33 -23 -2 -58q-27 -29 -50.5 -54t-41.5 -48q-27 -29 -43.5 -50.5t-16.5 -41.5q0 -10 13 -27l20 -18q27 -25 27 -64q0 -29 -17 -110 q-16 -82 -61 -120t-131 -38h-21q-78 0 -109.5 27.5t-33.5 83.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="1062" d="M149 895q-33 23 2 58q27 29 50.5 54t41.5 48q27 29 43.5 50.5t16.5 41.5q0 10 -13 27l-20 18q-27 25 -27 64q0 29 17 110q16 82 61 120t131 38h21q78 0 109.5 -27.5t33.5 -83.5q0 -14 -2 -33.5t-10 -56.5q-8 -41 -31 -96t-60.5 -118.5t-91 -131t-122.5 -131.5 q-35 -31 -74 -4zM653 895q-33 23 2 58q27 29 50.5 54t41.5 48q27 29 43.5 50.5t16.5 41.5q0 10 -13 27l-20 18q-27 25 -27 64q0 29 17 110q16 82 61 120t131 38h21q78 0 109.5 -27.5t33.5 -83.5q0 -14 -2 -33.5t-10 -56.5q-8 -41 -31 -96t-60.5 -118.5t-91 -131 t-122.5 -131.5q-35 -31 -74 -4z" />
<glyph unicode="&#x201e;" horiz-adv-x="1062" d="M-102 -291q-33 23 2 58q27 29 50.5 54t41.5 48q27 29 43.5 50.5t16.5 41.5q0 10 -13 27l-20 18q-27 25 -27 64q0 29 17 110q16 82 61 120t131 38h21q78 0 109.5 -27.5t33.5 -83.5q0 -14 -2 -33.5t-10 -56.5q-8 -41 -31 -96t-60.5 -118.5t-91 -131t-122.5 -131.5 q-35 -31 -74 -4zM403 -291q-33 23 2 58q27 29 50.5 54t41.5 48q27 29 43.5 50.5t16.5 41.5q0 10 -13 27l-20 18q-27 25 -27 64q0 29 17 110q16 82 61 120t131 38h21q78 0 109.5 -27.5t33.5 -83.5q0 -14 -2 -33.5t-10 -56.5q-8 -41 -31 -96t-60.5 -118.5t-91 -131 t-122.5 -131.5q-35 -31 -74 -4z" />
<glyph unicode="&#x2022;" horiz-adv-x="819" d="M78 483q0 68 27.5 130.5t75.5 111.5t111.5 77.5t137.5 28.5q53 0 97 -18t76 -49t49.5 -74t17.5 -92q0 -68 -28 -130t-76 -111.5t-112.5 -78t-136.5 -28.5q-53 0 -97 18.5t-75.5 49t-49 73.5t-17.5 92z" />
<glyph unicode="&#x2026;" horiz-adv-x="1480" d="M-31 84q0 23 4 51.5t9 50.5q23 90 69.5 121t122.5 31h19q139 0 139 -96q0 -23 -3 -51.5t-12 -57.5q-23 -90 -68.5 -120.5t-121.5 -30.5h-18q-74 0 -107 26.5t-33 75.5zM463 84q0 23 4 51.5t8 50.5q23 90 70 121t123 31h18q139 0 139 -96q0 -23 -3 -51.5t-11 -57.5 q-23 -90 -69 -120.5t-121 -30.5h-19q-74 0 -106.5 26.5t-32.5 75.5zM956 84q0 23 4.5 51.5t8.5 50.5q23 90 69.5 121t122.5 31h19q139 0 139 -96q0 -23 -3 -51.5t-11 -57.5q-23 -90 -69 -120.5t-122 -30.5h-18q-74 0 -107 26.5t-33 75.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="375" />
<glyph unicode="&#x2039;" horiz-adv-x="835" d="M18 440q0 20 7 49q4 18 9 32t13 25t20.5 21.5t30.5 24.5l459 319q31 23 49 23q14 0 24.5 -8t20.5 -23l74 -92q35 -43 35 -66q0 -16 -7.5 -27t-29.5 -28l-322 -241l197 -213q27 -29 27 -52q0 -14 -7.5 -26.5t-29.5 -30.5l-96 -76q-20 -16 -31.5 -23.5t-24.5 -7.5 q-20 0 -45 25l-336 322q-23 23 -30 38t-7 35z" />
<glyph unicode="&#x203a;" horiz-adv-x="835" d="M10 209q0 16 7.5 27.5t29.5 27.5l322 242l-197 213q-27 29 -27 51q0 14 7.5 26.5t29.5 30.5l97 76q20 16 31.5 23.5t23.5 7.5q20 0 45 -25l336 -321q23 -23 30 -38t7 -36q0 -23 -7 -49q-4 -18 -9 -31.5t-13 -25t-20.5 -21.5t-30.5 -25l-459 -319q-31 -23 -49 -23 q-14 0 -24.5 8.5t-20.5 22.5l-74 92q-35 43 -35 66z" />
<glyph unicode="&#x205f;" horiz-adv-x="469" />
<glyph unicode="&#x20ac;" d="M31 419q-6 11 0 42l14 78q6 35 16.5 44t38.5 9h95l22 121h-94q-29 0 -35 11t0 42l14 78q6 35 16.5 44t39.5 9h98q29 117 75 201t115.5 139t164 80.5t220.5 25.5q82 0 172.5 -17t153.5 -44q35 -16 38 -37.5t-9 -54.5l-49 -135q-10 -29 -26.5 -32t-51.5 7 q-47 14 -103.5 22.5t-105.5 8.5h-2q-84 0 -147.5 -32t-96.5 -132h334q29 0 35 -11.5t0 -41.5l-15 -78q-6 -35 -16 -44t-39 -9h-340l-22 -121h340q29 0 35 -12.5t-1 -40.5l-14 -78q-6 -35 -16.5 -44t-38.5 -9h-328v-15q0 -66 45 -97.5t160 -31.5q25 0 56.5 3t65.5 8.5 t64.5 10.5t48.5 11q53 14 54 -37v-156q0 -27 -9.5 -41t-50.5 -28q-31 -10 -69.5 -20.5t-82.5 -17.5t-90 -11t-87 -4h-7q-295 0 -393 137q-25 35 -42 87t-17 122v35.5t2 44.5h-100q-29 0 -35 11z" />
<glyph unicode="&#x2122;" horiz-adv-x="1546" d="M127 1196l16 86q4 27 13.5 35t33.5 8h457q25 0 31 -9t2 -34l-16 -86q-6 -29 -15.5 -36t-38.5 -7h-118l-84 -426q-6 -29 -16.5 -36t-39.5 -7h-114q-27 0 -36 9t-5 34l84 426h-119q-29 0 -34 9t-1 34zM618 727q33 154 69 294t69 249q10 35 23.5 45t45.5 10h203q33 0 45 -10 t12 -39q0 -59 -1 -136t1 -151q29 74 59 151t52 136q12 29 26.5 39t53.5 10h205q29 0 43 -8t10 -47q-12 -109 -32.5 -249t-47.5 -294q-6 -27 -16.5 -35t-38.5 -8h-113q-51 0 -41 43q20 104 35.5 201.5t32.5 195.5q-16 -43 -36 -89t-38 -90t-35.5 -85t-32.5 -72 q-12 -29 -25 -34.5t-42 -5.5h-127q-35 0 -44 9t-11 31q-4 66 -7.5 162.5t-3.5 182.5q-25 -100 -46 -199.5t-44 -206.5q-6 -27 -19.5 -35t-37.5 -8h-102q-53 0 -44 43z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1003" d="M0 0v1004h1004v-1004h-1004z" />
<hkern u1="&#x26;" u2="&#x178;" k="82" />
<hkern u1="&#x26;" u2="&#xdd;" k="82" />
<hkern u1="&#x26;" u2="Z" k="-41" />
<hkern u1="&#x26;" u2="Y" k="82" />
<hkern u1="&#x26;" u2="T" k="102" />
<hkern u1="&#x26;" u2="V" k="41" />
<hkern u1="&#x26;" u2="J" k="-61" />
<hkern u1="&#x28;" u2="&#x178;" k="-20" />
<hkern u1="&#x28;" u2="&#xdd;" k="-20" />
<hkern u1="&#x28;" u2="j" k="-223" />
<hkern u1="&#x28;" u2="Y" k="-20" />
<hkern u1="&#x28;" u2="W" k="-20" />
<hkern u1="&#x28;" u2="T" k="-41" />
<hkern u1="&#x28;" u2="X" k="-20" />
<hkern u1="&#x28;" u2="V" k="-41" />
<hkern u1="&#x2a;" u2="&#x178;" k="-41" />
<hkern u1="&#x2a;" u2="&#x153;" k="72" />
<hkern u1="&#x2a;" u2="&#xf8;" k="72" />
<hkern u1="&#x2a;" u2="&#xf6;" k="72" />
<hkern u1="&#x2a;" u2="&#xf5;" k="72" />
<hkern u1="&#x2a;" u2="&#xf4;" k="72" />
<hkern u1="&#x2a;" u2="&#xf3;" k="72" />
<hkern u1="&#x2a;" u2="&#xf2;" k="72" />
<hkern u1="&#x2a;" u2="&#xf0;" k="72" />
<hkern u1="&#x2a;" u2="&#xef;" k="-12" />
<hkern u1="&#x2a;" u2="&#xee;" k="-12" />
<hkern u1="&#x2a;" u2="&#xed;" k="-12" />
<hkern u1="&#x2a;" u2="&#xec;" k="-12" />
<hkern u1="&#x2a;" u2="&#xeb;" k="72" />
<hkern u1="&#x2a;" u2="&#xea;" k="72" />
<hkern u1="&#x2a;" u2="&#xe9;" k="72" />
<hkern u1="&#x2a;" u2="&#xe8;" k="72" />
<hkern u1="&#x2a;" u2="&#xe7;" k="72" />
<hkern u1="&#x2a;" u2="&#xdd;" k="-41" />
<hkern u1="&#x2a;" u2="&#xc5;" k="143" />
<hkern u1="&#x2a;" u2="&#xc4;" k="143" />
<hkern u1="&#x2a;" u2="&#xc3;" k="143" />
<hkern u1="&#x2a;" u2="&#xc2;" k="143" />
<hkern u1="&#x2a;" u2="&#xc1;" k="143" />
<hkern u1="&#x2a;" u2="&#xc0;" k="143" />
<hkern u1="&#x2a;" u2="o" k="72" />
<hkern u1="&#x2a;" u2="i" k="-12" />
<hkern u1="&#x2a;" u2="e" k="72" />
<hkern u1="&#x2a;" u2="d" k="72" />
<hkern u1="&#x2a;" u2="c" k="72" />
<hkern u1="&#x2a;" u2="Y" k="-41" />
<hkern u1="&#x2a;" u2="W" k="-45" />
<hkern u1="&#x2a;" u2="T" k="-41" />
<hkern u1="&#x2a;" u2="A" k="143" />
<hkern u1="&#x2a;" u2="q" k="72" />
<hkern u1="&#x2a;" u2="V" k="-49" />
<hkern u1="&#x2c;" u2="v" k="113" />
<hkern u1="&#x2c;" u2="V" k="164" />
<hkern u1="&#x2c;" u2="J" k="-29" />
<hkern u1="&#x2e;" u2="v" k="113" />
<hkern u1="&#x2e;" u2="V" k="164" />
<hkern u1="&#x2e;" u2="J" k="-29" />
<hkern u1="&#x2f;" g2="uniFB02" k="31" />
<hkern u1="&#x2f;" g2="uniFB01" k="31" />
<hkern u1="&#x2f;" u2="&#x178;" k="-41" />
<hkern u1="&#x2f;" u2="&#x153;" k="123" />
<hkern u1="&#x2f;" u2="&#xff;" k="20" />
<hkern u1="&#x2f;" u2="&#xfd;" k="20" />
<hkern u1="&#x2f;" u2="&#xfc;" k="102" />
<hkern u1="&#x2f;" u2="&#xfb;" k="102" />
<hkern u1="&#x2f;" u2="&#xfa;" k="102" />
<hkern u1="&#x2f;" u2="&#xf9;" k="102" />
<hkern u1="&#x2f;" u2="&#xf8;" k="123" />
<hkern u1="&#x2f;" u2="&#xf6;" k="123" />
<hkern u1="&#x2f;" u2="&#xf5;" k="123" />
<hkern u1="&#x2f;" u2="&#xf4;" k="123" />
<hkern u1="&#x2f;" u2="&#xf3;" k="123" />
<hkern u1="&#x2f;" u2="&#xf2;" k="123" />
<hkern u1="&#x2f;" u2="&#xf1;" k="123" />
<hkern u1="&#x2f;" u2="&#xf0;" k="123" />
<hkern u1="&#x2f;" u2="&#xeb;" k="123" />
<hkern u1="&#x2f;" u2="&#xea;" k="123" />
<hkern u1="&#x2f;" u2="&#xe9;" k="123" />
<hkern u1="&#x2f;" u2="&#xe8;" k="123" />
<hkern u1="&#x2f;" u2="&#xe7;" k="123" />
<hkern u1="&#x2f;" u2="&#xe6;" k="123" />
<hkern u1="&#x2f;" u2="&#xe5;" k="123" />
<hkern u1="&#x2f;" u2="&#xe4;" k="123" />
<hkern u1="&#x2f;" u2="&#xe3;" k="123" />
<hkern u1="&#x2f;" u2="&#xe2;" k="123" />
<hkern u1="&#x2f;" u2="&#xe1;" k="123" />
<hkern u1="&#x2f;" u2="&#xe0;" k="123" />
<hkern u1="&#x2f;" u2="&#xdd;" k="-41" />
<hkern u1="&#x2f;" u2="&#xc5;" k="20" />
<hkern u1="&#x2f;" u2="&#xc4;" k="20" />
<hkern u1="&#x2f;" u2="&#xc3;" k="20" />
<hkern u1="&#x2f;" u2="&#xc2;" k="20" />
<hkern u1="&#x2f;" u2="&#xc1;" k="20" />
<hkern u1="&#x2f;" u2="&#xc0;" k="20" />
<hkern u1="&#x2f;" u2="z" k="61" />
<hkern u1="&#x2f;" u2="y" k="20" />
<hkern u1="&#x2f;" u2="w" k="20" />
<hkern u1="&#x2f;" u2="u" k="102" />
<hkern u1="&#x2f;" u2="t" k="41" />
<hkern u1="&#x2f;" u2="s" k="123" />
<hkern u1="&#x2f;" u2="r" k="61" />
<hkern u1="&#x2f;" u2="o" k="123" />
<hkern u1="&#x2f;" u2="n" k="123" />
<hkern u1="&#x2f;" u2="g" k="133" />
<hkern u1="&#x2f;" u2="f" k="31" />
<hkern u1="&#x2f;" u2="e" k="123" />
<hkern u1="&#x2f;" u2="d" k="123" />
<hkern u1="&#x2f;" u2="c" k="123" />
<hkern u1="&#x2f;" u2="a" k="123" />
<hkern u1="&#x2f;" u2="Y" k="-41" />
<hkern u1="&#x2f;" u2="W" k="-49" />
<hkern u1="&#x2f;" u2="T" k="-41" />
<hkern u1="&#x2f;" u2="A" k="20" />
<hkern u1="&#x2f;" u2="x" k="61" />
<hkern u1="&#x2f;" u2="v" k="20" />
<hkern u1="&#x2f;" u2="q" k="123" />
<hkern u1="&#x2f;" u2="p" k="123" />
<hkern u1="&#x2f;" u2="m" k="123" />
<hkern u1="&#x2f;" u2="V" k="-51" />
<hkern u1="&#x3a;" u2="x" k="-20" />
<hkern u1="&#x3a;" u2="v" k="-61" />
<hkern u1="&#x3b;" u2="x" k="-20" />
<hkern u1="&#x3b;" u2="v" k="-61" />
<hkern u1="A" u2="&#x2122;" k="164" />
<hkern u1="A" u2="&#x203a;" k="-31" />
<hkern u1="A" u2="&#xbb;" k="-31" />
<hkern u1="A" u2="&#xae;" k="61" />
<hkern u1="A" u2="&#x7d;" k="-20" />
<hkern u1="A" u2="v" k="33" />
<hkern u1="A" u2="q" k="8" />
<hkern u1="A" u2="b" k="20" />
<hkern u1="A" u2="]" k="-20" />
<hkern u1="A" u2="\" k="143" />
<hkern u1="A" u2="V" k="78" />
<hkern u1="A" u2="Q" k="41" />
<hkern u1="A" u2="J" k="-31" />
<hkern u1="A" u2="E" k="29" />
<hkern u1="A" u2="&#x3f;" k="51" />
<hkern u1="A" u2="&#x2f;" k="-41" />
<hkern u1="A" u2="&#x2a;" k="143" />
<hkern u1="B" u2="T" k="33" />
<hkern u1="B" u2="&#x2122;" k="45" />
<hkern u1="B" u2="&#x29;" k="31" />
<hkern u1="C" u2="&#xef;" k="-123" />
<hkern u1="C" u2="&#xee;" k="-61" />
<hkern u1="C" u2="&#xec;" k="-20" />
<hkern u1="C" u2="q" k="20" />
<hkern u1="C" u2="X" k="-33" />
<hkern u1="C" u2="V" k="-16" />
<hkern u1="C" u2="Q" k="29" />
<hkern u1="C" u2="J" k="-31" />
<hkern u1="C" u2="&#x3f;" k="8" />
<hkern u1="C" u2="&#x29;" k="-41" />
<hkern u1="D" u2="&#x2122;" k="37" />
<hkern u1="D" u2="v" k="-41" />
<hkern u1="D" u2="X" k="20" />
<hkern u1="E" u2="V" k="-20" />
<hkern u1="E" u2="Q" k="20" />
<hkern u1="E" u2="J" k="-8" />
<hkern u1="E" u2="&#x3f;" k="-20" />
<hkern u1="F" u2="&#x2026;" k="252" />
<hkern u1="F" u2="&#x201e;" k="252" />
<hkern u1="F" u2="&#x201a;" k="252" />
<hkern u1="F" u2="&#x178;" k="-20" />
<hkern u1="F" u2="&#x153;" k="41" />
<hkern u1="F" u2="&#xf8;" k="41" />
<hkern u1="F" u2="&#xf6;" k="41" />
<hkern u1="F" u2="&#xf5;" k="41" />
<hkern u1="F" u2="&#xf4;" k="41" />
<hkern u1="F" u2="&#xf3;" k="41" />
<hkern u1="F" u2="&#xf2;" k="41" />
<hkern u1="F" u2="&#xf1;" k="20" />
<hkern u1="F" u2="&#xf0;" k="41" />
<hkern u1="F" u2="&#xeb;" k="41" />
<hkern u1="F" u2="&#xea;" k="41" />
<hkern u1="F" u2="&#xe9;" k="41" />
<hkern u1="F" u2="&#xe8;" k="41" />
<hkern u1="F" u2="&#xe6;" k="49" />
<hkern u1="F" u2="&#xe5;" k="49" />
<hkern u1="F" u2="&#xe4;" k="49" />
<hkern u1="F" u2="&#xe3;" k="49" />
<hkern u1="F" u2="&#xe2;" k="49" />
<hkern u1="F" u2="&#xe1;" k="49" />
<hkern u1="F" u2="&#xe0;" k="49" />
<hkern u1="F" u2="&#xdd;" k="-20" />
<hkern u1="F" u2="&#xc6;" k="143" />
<hkern u1="F" u2="&#xc5;" k="82" />
<hkern u1="F" u2="&#xc4;" k="82" />
<hkern u1="F" u2="&#xc3;" k="82" />
<hkern u1="F" u2="&#xc2;" k="82" />
<hkern u1="F" u2="&#xc1;" k="82" />
<hkern u1="F" u2="&#xc0;" k="82" />
<hkern u1="F" u2="r" k="20" />
<hkern u1="F" u2="o" k="41" />
<hkern u1="F" u2="n" k="20" />
<hkern u1="F" u2="g" k="41" />
<hkern u1="F" u2="e" k="41" />
<hkern u1="F" u2="d" k="41" />
<hkern u1="F" u2="a" k="49" />
<hkern u1="F" u2="Y" k="-20" />
<hkern u1="F" u2="W" k="-31" />
<hkern u1="F" u2="T" k="-8" />
<hkern u1="F" u2="A" k="82" />
<hkern u1="F" u2="&#x2e;" k="252" />
<hkern u1="F" u2="&#x2c;" k="252" />
<hkern u1="F" u2="&#xef;" k="-123" />
<hkern u1="F" u2="&#xee;" k="-102" />
<hkern u1="F" u2="&#xec;" k="-102" />
<hkern u1="F" u2="q" k="41" />
<hkern u1="F" u2="p" k="20" />
<hkern u1="F" u2="m" k="20" />
<hkern u1="F" u2="V" k="-31" />
<hkern u1="F" u2="J" k="127" />
<hkern u1="F" u2="&#x3f;" k="-20" />
<hkern u1="F" u2="&#x2f;" k="70" />
<hkern u1="G" u2="&#xef;" k="-41" />
<hkern u1="G" u2="J" k="-20" />
<hkern u1="I" u2="&#x3f;" k="-41" />
<hkern u1="K" u2="&#x7d;" k="-41" />
<hkern u1="K" u2="x" k="-53" />
<hkern u1="K" u2="v" k="41" />
<hkern u1="K" u2="q" k="20" />
<hkern u1="K" u2="X" k="-41" />
<hkern u1="K" u2="V" k="-45" />
<hkern u1="K" u2="Q" k="41" />
<hkern u1="K" u2="J" k="-61" />
<hkern u1="K" u2="&#x2f;" k="-61" />
<hkern u1="L" u2="&#x2122;" k="225" />
<hkern u1="L" u2="&#xae;" k="90" />
<hkern u1="L" u2="x" k="-41" />
<hkern u1="L" u2="v" k="61" />
<hkern u1="L" u2="\" k="154" />
<hkern u1="L" u2="X" k="-20" />
<hkern u1="L" u2="V" k="102" />
<hkern u1="L" u2="Q" k="41" />
<hkern u1="L" u2="J" k="20" />
<hkern u1="L" u2="&#x3f;" k="41" />
<hkern u1="L" u2="&#x2f;" k="-61" />
<hkern u1="L" u2="&#x2a;" k="225" />
<hkern u1="L" u2="&#x26;" k="-6" />
<hkern u1="M" u2="&#x178;" k="8" />
<hkern u1="M" u2="&#xdd;" k="8" />
<hkern u1="M" u2="Y" k="8" />
<hkern u1="M" u2="T" k="10" />
<hkern u1="O" u2="X" k="20" />
<hkern u1="O" u2="V" k="20" />
<hkern u1="P" g2="uniFB02" k="-45" />
<hkern u1="P" g2="uniFB01" k="-45" />
<hkern u1="P" u2="&#x2026;" k="225" />
<hkern u1="P" u2="&#x201e;" k="225" />
<hkern u1="P" u2="&#x201a;" k="225" />
<hkern u1="P" u2="&#xff;" k="-41" />
<hkern u1="P" u2="&#xfd;" k="-41" />
<hkern u1="P" u2="&#xc6;" k="143" />
<hkern u1="P" u2="&#xc5;" k="61" />
<hkern u1="P" u2="&#xc4;" k="61" />
<hkern u1="P" u2="&#xc3;" k="61" />
<hkern u1="P" u2="&#xc2;" k="61" />
<hkern u1="P" u2="&#xc1;" k="61" />
<hkern u1="P" u2="&#xc0;" k="61" />
<hkern u1="P" u2="y" k="-41" />
<hkern u1="P" u2="w" k="-43" />
<hkern u1="P" u2="t" k="-45" />
<hkern u1="P" u2="f" k="-45" />
<hkern u1="P" u2="A" k="61" />
<hkern u1="P" u2="&#x2e;" k="225" />
<hkern u1="P" u2="&#x2c;" k="225" />
<hkern u1="P" u2="&#xef;" k="-61" />
<hkern u1="P" u2="&#xee;" k="-61" />
<hkern u1="P" u2="v" k="-41" />
<hkern u1="P" u2="X" k="20" />
<hkern u1="P" u2="J" k="113" />
<hkern u1="P" u2="&#x2f;" k="113" />
<hkern u1="P" u2="&#x26;" k="25" />
<hkern u1="Q" g2="uniFB02" k="-16" />
<hkern u1="Q" g2="uniFB01" k="-16" />
<hkern u1="Q" u2="&#x178;" k="20" />
<hkern u1="Q" u2="&#xdd;" k="20" />
<hkern u1="Q" u2="&#xc6;" k="53" />
<hkern u1="Q" u2="t" k="-16" />
<hkern u1="Q" u2="j" k="-53" />
<hkern u1="Q" u2="f" k="-16" />
<hkern u1="Q" u2="Y" k="20" />
<hkern u1="Q" u2="T" k="33" />
<hkern u1="Q" u2="X" k="20" />
<hkern u1="Q" u2="V" k="20" />
<hkern u1="R" u2="&#x2122;" k="8" />
<hkern u1="R" u2="x" k="-20" />
<hkern u1="R" u2="\" k="61" />
<hkern u1="R" u2="X" k="-12" />
<hkern u1="S" u2="J" k="-12" />
<hkern u1="T" u2="&#xfc;" k="61" />
<hkern u1="T" u2="&#xfb;" k="61" />
<hkern u1="T" u2="&#xf6;" k="82" />
<hkern u1="T" u2="&#xf5;" k="82" />
<hkern u1="T" u2="&#xf4;" k="102" />
<hkern u1="T" u2="&#xef;" k="-143" />
<hkern u1="T" u2="&#xee;" k="-123" />
<hkern u1="T" u2="&#xec;" k="-102" />
<hkern u1="T" u2="&#xbb;" k="82" />
<hkern u1="T" u2="x" k="41" />
<hkern u1="T" u2="v" k="61" />
<hkern u1="T" u2="q" k="123" />
<hkern u1="T" u2="p" k="102" />
<hkern u1="T" u2="m" k="102" />
<hkern u1="T" u2="h" k="20" />
<hkern u1="T" u2="b" k="20" />
<hkern u1="T" u2="\" k="-49" />
<hkern u1="T" u2="V" k="-33" />
<hkern u1="T" u2="Q" k="41" />
<hkern u1="T" u2="M" k="10" />
<hkern u1="T" u2="J" k="82" />
<hkern u1="T" u2="F" k="20" />
<hkern u1="T" u2="E" k="12" />
<hkern u1="T" u2="&#x40;" k="123" />
<hkern u1="T" u2="&#x3f;" k="-33" />
<hkern u1="T" u2="&#x2f;" k="131" />
<hkern u1="T" u2="&#x2a;" k="-41" />
<hkern u1="T" u2="&#x29;" k="-41" />
<hkern u1="V" g2="uniFB02" k="-20" />
<hkern u1="V" g2="uniFB01" k="-20" />
<hkern u1="V" u2="&#x2026;" k="164" />
<hkern u1="V" u2="&#x201e;" k="164" />
<hkern u1="V" u2="&#x201d;" k="-41" />
<hkern u1="V" u2="&#x201a;" k="164" />
<hkern u1="V" u2="&#x2019;" k="-41" />
<hkern u1="V" u2="&#x178;" k="-29" />
<hkern u1="V" u2="&#x153;" k="37" />
<hkern u1="V" u2="&#x152;" k="20" />
<hkern u1="V" u2="&#xfc;" k="20" />
<hkern u1="V" u2="&#xfb;" k="20" />
<hkern u1="V" u2="&#xfa;" k="20" />
<hkern u1="V" u2="&#xf9;" k="20" />
<hkern u1="V" u2="&#xf8;" k="37" />
<hkern u1="V" u2="&#xf6;" k="37" />
<hkern u1="V" u2="&#xf5;" k="37" />
<hkern u1="V" u2="&#xf4;" k="37" />
<hkern u1="V" u2="&#xf3;" k="37" />
<hkern u1="V" u2="&#xf2;" k="37" />
<hkern u1="V" u2="&#xf1;" k="20" />
<hkern u1="V" u2="&#xf0;" k="37" />
<hkern u1="V" u2="&#xed;" k="-29" />
<hkern u1="V" u2="&#xeb;" k="41" />
<hkern u1="V" u2="&#xea;" k="41" />
<hkern u1="V" u2="&#xe9;" k="41" />
<hkern u1="V" u2="&#xe8;" k="41" />
<hkern u1="V" u2="&#xe7;" k="41" />
<hkern u1="V" u2="&#xe6;" k="37" />
<hkern u1="V" u2="&#xe5;" k="37" />
<hkern u1="V" u2="&#xe4;" k="37" />
<hkern u1="V" u2="&#xe3;" k="37" />
<hkern u1="V" u2="&#xe2;" k="37" />
<hkern u1="V" u2="&#xe1;" k="37" />
<hkern u1="V" u2="&#xe0;" k="37" />
<hkern u1="V" u2="&#xdd;" k="-29" />
<hkern u1="V" u2="&#xd8;" k="20" />
<hkern u1="V" u2="&#xd6;" k="20" />
<hkern u1="V" u2="&#xd5;" k="20" />
<hkern u1="V" u2="&#xd4;" k="20" />
<hkern u1="V" u2="&#xd3;" k="20" />
<hkern u1="V" u2="&#xd2;" k="20" />
<hkern u1="V" u2="&#xc7;" k="20" />
<hkern u1="V" u2="&#xc6;" k="131" />
<hkern u1="V" u2="&#xc5;" k="57" />
<hkern u1="V" u2="&#xc4;" k="57" />
<hkern u1="V" u2="&#xc3;" k="57" />
<hkern u1="V" u2="&#xc2;" k="57" />
<hkern u1="V" u2="&#xc1;" k="57" />
<hkern u1="V" u2="&#xc0;" k="57" />
<hkern u1="V" u2="u" k="20" />
<hkern u1="V" u2="t" k="-20" />
<hkern u1="V" u2="s" k="31" />
<hkern u1="V" u2="r" k="20" />
<hkern u1="V" u2="o" k="37" />
<hkern u1="V" u2="n" k="20" />
<hkern u1="V" u2="j" k="-29" />
<hkern u1="V" u2="i" k="-29" />
<hkern u1="V" u2="g" k="41" />
<hkern u1="V" u2="f" k="-20" />
<hkern u1="V" u2="e" k="41" />
<hkern u1="V" u2="d" k="41" />
<hkern u1="V" u2="c" k="41" />
<hkern u1="V" u2="a" k="37" />
<hkern u1="V" u2="Y" k="-29" />
<hkern u1="V" u2="W" k="-20" />
<hkern u1="V" u2="T" k="-41" />
<hkern u1="V" u2="O" k="20" />
<hkern u1="V" u2="G" k="20" />
<hkern u1="V" u2="C" k="20" />
<hkern u1="V" u2="A" k="57" />
<hkern u1="V" u2="&#x2e;" k="164" />
<hkern u1="V" u2="&#x2c;" k="164" />
<hkern u1="V" u2="&#xef;" k="-164" />
<hkern u1="V" u2="&#xee;" k="-123" />
<hkern u1="V" u2="&#xec;" k="-102" />
<hkern u1="V" u2="&#xbb;" k="20" />
<hkern u1="V" u2="&#x7d;" k="-41" />
<hkern u1="V" u2="q" k="41" />
<hkern u1="V" u2="p" k="20" />
<hkern u1="V" u2="m" k="20" />
<hkern u1="V" u2="]" k="-41" />
<hkern u1="V" u2="\" k="-53" />
<hkern u1="V" u2="V" k="-29" />
<hkern u1="V" u2="Q" k="20" />
<hkern u1="V" u2="J" k="82" />
<hkern u1="V" u2="&#x40;" k="61" />
<hkern u1="V" u2="&#x3f;" k="-41" />
<hkern u1="V" u2="&#x2f;" k="82" />
<hkern u1="V" u2="&#x2a;" k="-49" />
<hkern u1="V" u2="&#x29;" k="-41" />
<hkern u1="V" u2="&#x26;" k="31" />
<hkern u1="W" u2="&#xef;" k="-164" />
<hkern u1="W" u2="&#xee;" k="-143" />
<hkern u1="W" u2="&#xec;" k="-102" />
<hkern u1="W" u2="&#x7d;" k="-41" />
<hkern u1="W" u2="q" k="29" />
<hkern u1="W" u2="]" k="-41" />
<hkern u1="W" u2="\" k="-41" />
<hkern u1="W" u2="V" k="-20" />
<hkern u1="W" u2="J" k="14" />
<hkern u1="W" u2="&#x40;" k="31" />
<hkern u1="W" u2="&#x3f;" k="-45" />
<hkern u1="W" u2="&#x2f;" k="41" />
<hkern u1="W" u2="&#x2a;" k="-45" />
<hkern u1="W" u2="&#x29;" k="-20" />
<hkern u1="W" u2="&#x26;" k="8" />
<hkern u1="X" g2="uniFB02" k="6" />
<hkern u1="X" g2="uniFB01" k="6" />
<hkern u1="X" u2="&#x153;" k="20" />
<hkern u1="X" u2="&#x152;" k="20" />
<hkern u1="X" u2="&#xff;" k="45" />
<hkern u1="X" u2="&#xfd;" k="45" />
<hkern u1="X" u2="&#xf8;" k="20" />
<hkern u1="X" u2="&#xf6;" k="20" />
<hkern u1="X" u2="&#xf5;" k="20" />
<hkern u1="X" u2="&#xf4;" k="20" />
<hkern u1="X" u2="&#xf3;" k="20" />
<hkern u1="X" u2="&#xf2;" k="20" />
<hkern u1="X" u2="&#xf1;" k="10" />
<hkern u1="X" u2="&#xf0;" k="20" />
<hkern u1="X" u2="&#xed;" k="-25" />
<hkern u1="X" u2="&#xeb;" k="20" />
<hkern u1="X" u2="&#xea;" k="20" />
<hkern u1="X" u2="&#xe9;" k="20" />
<hkern u1="X" u2="&#xe8;" k="20" />
<hkern u1="X" u2="&#xe7;" k="20" />
<hkern u1="X" u2="&#xd8;" k="20" />
<hkern u1="X" u2="&#xd6;" k="20" />
<hkern u1="X" u2="&#xd5;" k="20" />
<hkern u1="X" u2="&#xd4;" k="20" />
<hkern u1="X" u2="&#xd3;" k="20" />
<hkern u1="X" u2="&#xd2;" k="20" />
<hkern u1="X" u2="&#xc7;" k="20" />
<hkern u1="X" u2="z" k="-41" />
<hkern u1="X" u2="y" k="45" />
<hkern u1="X" u2="w" k="31" />
<hkern u1="X" u2="t" k="33" />
<hkern u1="X" u2="o" k="20" />
<hkern u1="X" u2="n" k="10" />
<hkern u1="X" u2="j" k="-25" />
<hkern u1="X" u2="i" k="-25" />
<hkern u1="X" u2="g" k="20" />
<hkern u1="X" u2="f" k="6" />
<hkern u1="X" u2="e" k="20" />
<hkern u1="X" u2="d" k="20" />
<hkern u1="X" u2="c" k="20" />
<hkern u1="X" u2="O" k="20" />
<hkern u1="X" u2="G" k="20" />
<hkern u1="X" u2="C" k="20" />
<hkern u1="X" u2="&#xef;" k="-123" />
<hkern u1="X" u2="&#xee;" k="-102" />
<hkern u1="X" u2="&#xec;" k="-82" />
<hkern u1="X" u2="&#xbb;" k="20" />
<hkern u1="X" u2="v" k="39" />
<hkern u1="X" u2="q" k="20" />
<hkern u1="X" u2="Q" k="20" />
<hkern u1="X" u2="J" k="-29" />
<hkern u1="X" u2="&#x3f;" k="-20" />
<hkern u1="X" u2="&#x29;" k="-20" />
<hkern u1="Y" u2="&#xef;" k="-143" />
<hkern u1="Y" u2="&#xee;" k="-143" />
<hkern u1="Y" u2="&#xec;" k="-102" />
<hkern u1="Y" u2="&#xbb;" k="61" />
<hkern u1="Y" u2="&#x7d;" k="-41" />
<hkern u1="Y" u2="x" k="20" />
<hkern u1="Y" u2="q" k="82" />
<hkern u1="Y" u2="p" k="61" />
<hkern u1="Y" u2="m" k="61" />
<hkern u1="Y" u2="h" k="20" />
<hkern u1="Y" u2="b" k="20" />
<hkern u1="Y" u2="]" k="-29" />
<hkern u1="Y" u2="\" k="-57" />
<hkern u1="Y" u2="V" k="-20" />
<hkern u1="Y" u2="Q" k="20" />
<hkern u1="Y" u2="M" k="20" />
<hkern u1="Y" u2="J" k="113" />
<hkern u1="Y" u2="E" k="20" />
<hkern u1="Y" u2="&#x40;" k="82" />
<hkern u1="Y" u2="&#x3f;" k="-45" />
<hkern u1="Y" u2="&#x2f;" k="119" />
<hkern u1="Y" u2="&#x2a;" k="-41" />
<hkern u1="Y" u2="&#x29;" k="-20" />
<hkern u1="Y" u2="&#x26;" k="41" />
<hkern u1="Z" u2="v" k="25" />
<hkern u1="Z" u2="q" k="20" />
<hkern u1="Z" u2="V" k="-20" />
<hkern u1="Z" u2="&#x40;" k="20" />
<hkern u1="[" u2="&#x178;" k="-29" />
<hkern u1="[" u2="&#xdd;" k="-29" />
<hkern u1="[" u2="&#xc5;" k="-20" />
<hkern u1="[" u2="&#xc4;" k="-20" />
<hkern u1="[" u2="&#xc3;" k="-20" />
<hkern u1="[" u2="&#xc2;" k="-20" />
<hkern u1="[" u2="&#xc1;" k="-20" />
<hkern u1="[" u2="&#xc0;" k="-20" />
<hkern u1="[" u2="z" k="-31" />
<hkern u1="[" u2="j" k="-186" />
<hkern u1="[" u2="Z" k="-20" />
<hkern u1="[" u2="Y" k="-29" />
<hkern u1="[" u2="W" k="-41" />
<hkern u1="[" u2="T" k="-41" />
<hkern u1="[" u2="A" k="-20" />
<hkern u1="[" u2="x" k="-20" />
<hkern u1="[" u2="V" k="-41" />
<hkern u1="\" u2="&#x178;" k="86" />
<hkern u1="\" u2="&#xff;" k="41" />
<hkern u1="\" u2="&#xfd;" k="41" />
<hkern u1="\" u2="&#xdd;" k="86" />
<hkern u1="\" u2="&#xc5;" k="-61" />
<hkern u1="\" u2="&#xc4;" k="-61" />
<hkern u1="\" u2="&#xc3;" k="-61" />
<hkern u1="\" u2="&#xc2;" k="-61" />
<hkern u1="\" u2="&#xc1;" k="-61" />
<hkern u1="\" u2="&#xc0;" k="-61" />
<hkern u1="\" u2="y" k="41" />
<hkern u1="\" u2="Y" k="86" />
<hkern u1="\" u2="W" k="61" />
<hkern u1="\" u2="T" k="123" />
<hkern u1="\" u2="A" k="-61" />
<hkern u1="\" u2="x" k="-61" />
<hkern u1="\" u2="v" k="41" />
<hkern u1="\" u2="V" k="109" />
<hkern u1="a" u2="&#x2122;" k="82" />
<hkern u1="a" u2="\" k="123" />
<hkern u1="a" u2="&#x2a;" k="51" />
<hkern u1="b" u2="&#x2122;" k="92" />
<hkern u1="b" u2="v" k="6" />
<hkern u1="b" u2="\" k="123" />
<hkern u1="b" u2="&#x2a;" k="72" />
<hkern u1="c" u2="x" k="-20" />
<hkern u1="c" u2="v" k="-20" />
<hkern u1="c" u2="\" k="82" />
<hkern u1="c" u2="&#x2f;" k="-41" />
<hkern u1="c" u2="&#x29;" k="-20" />
<hkern u1="d" u2="&#xec;" k="-61" />
<hkern u1="e" u2="&#x2122;" k="82" />
<hkern u1="e" u2="v" k="6" />
<hkern u1="e" u2="\" k="111" />
<hkern u1="e" u2="&#x2f;" k="-20" />
<hkern u1="e" u2="&#x2a;" k="72" />
<hkern u1="f" u2="&#x2026;" k="102" />
<hkern u1="f" u2="&#x201e;" k="102" />
<hkern u1="f" u2="&#x201d;" k="-61" />
<hkern u1="f" u2="&#x201a;" k="102" />
<hkern u1="f" u2="&#x2019;" k="-61" />
<hkern u1="f" u2="&#x153;" k="20" />
<hkern u1="f" u2="&#xff;" k="-20" />
<hkern u1="f" u2="&#xfd;" k="-20" />
<hkern u1="f" u2="&#xf8;" k="20" />
<hkern u1="f" u2="&#xf6;" k="20" />
<hkern u1="f" u2="&#xf5;" k="20" />
<hkern u1="f" u2="&#xf4;" k="20" />
<hkern u1="f" u2="&#xf3;" k="20" />
<hkern u1="f" u2="&#xf2;" k="20" />
<hkern u1="f" u2="&#xf0;" k="20" />
<hkern u1="f" u2="&#xed;" k="-25" />
<hkern u1="f" u2="&#xeb;" k="20" />
<hkern u1="f" u2="&#xea;" k="20" />
<hkern u1="f" u2="&#xe9;" k="20" />
<hkern u1="f" u2="&#xe8;" k="20" />
<hkern u1="f" u2="&#xe7;" k="20" />
<hkern u1="f" u2="&#xe6;" k="33" />
<hkern u1="f" u2="&#xe5;" k="33" />
<hkern u1="f" u2="&#xe4;" k="33" />
<hkern u1="f" u2="&#xe3;" k="33" />
<hkern u1="f" u2="&#xe2;" k="33" />
<hkern u1="f" u2="&#xe1;" k="33" />
<hkern u1="f" u2="&#xe0;" k="33" />
<hkern u1="f" u2="y" k="-20" />
<hkern u1="f" u2="w" k="-20" />
<hkern u1="f" u2="s" k="20" />
<hkern u1="f" u2="o" k="20" />
<hkern u1="f" u2="l" k="-20" />
<hkern u1="f" u2="j" k="-29" />
<hkern u1="f" u2="i" k="-25" />
<hkern u1="f" u2="g" k="20" />
<hkern u1="f" u2="e" k="20" />
<hkern u1="f" u2="d" k="20" />
<hkern u1="f" u2="c" k="20" />
<hkern u1="f" u2="a" k="33" />
<hkern u1="f" u2="&#x3b;" k="-41" />
<hkern u1="f" u2="&#x3a;" k="-41" />
<hkern u1="f" u2="&#x2e;" k="102" />
<hkern u1="f" u2="&#x2c;" k="102" />
<hkern u1="f" u2="&#x27;" k="-82" />
<hkern u1="f" u2="&#x22;" k="-82" />
<hkern u1="f" u2="&#x2122;" k="-102" />
<hkern u1="f" u2="&#xef;" k="-188" />
<hkern u1="f" u2="&#xee;" k="-147" />
<hkern u1="f" u2="&#xec;" k="-168" />
<hkern u1="f" u2="&#xbb;" k="41" />
<hkern u1="f" u2="&#x7d;" k="-41" />
<hkern u1="f" u2="v" k="-20" />
<hkern u1="f" u2="q" k="20" />
<hkern u1="f" u2="k" k="-20" />
<hkern u1="f" u2="h" k="-20" />
<hkern u1="f" u2="b" k="-20" />
<hkern u1="f" u2="]" k="-41" />
<hkern u1="f" u2="\" k="-102" />
<hkern u1="f" u2="&#x3f;" k="-74" />
<hkern u1="f" u2="&#x2a;" k="-82" />
<hkern u1="f" u2="&#x29;" k="-61" />
<hkern u1="f" u2="&#x21;" k="-41" />
<hkern u1="g" u2="\" k="82" />
<hkern u1="h" u2="&#x2122;" k="61" />
<hkern u1="h" u2="\" k="82" />
<hkern u1="h" u2="&#x2a;" k="82" />
<hkern u1="i" u2="&#xef;" k="-82" />
<hkern u1="i" u2="&#xee;" k="-61" />
<hkern u1="i" u2="&#x2f;" k="20" />
<hkern u1="i" u2="&#x2a;" k="-12" />
<hkern u1="j" u2="&#xef;" k="-82" />
<hkern u1="j" u2="&#xee;" k="-82" />
<hkern u1="j" u2="\" k="-16" />
<hkern u1="k" u2="v" k="-16" />
<hkern u1="k" u2="q" k="20" />
<hkern u1="l" u2="v" k="29" />
<hkern u1="l" u2="\" k="41" />
<hkern u1="l" u2="&#x2f;" k="-41" />
<hkern u1="l" u2="&#x2a;" k="61" />
<hkern u1="m" u2="&#x2122;" k="61" />
<hkern u1="m" u2="\" k="82" />
<hkern u1="m" u2="&#x2a;" k="82" />
<hkern u1="n" u2="&#x2122;" k="61" />
<hkern u1="n" u2="\" k="82" />
<hkern u1="n" u2="&#x2a;" k="82" />
<hkern u1="o" u2="&#x2122;" k="92" />
<hkern u1="o" u2="x" k="6" />
<hkern u1="o" u2="v" k="6" />
<hkern u1="o" u2="\" k="123" />
<hkern u1="o" u2="&#x2a;" k="72" />
<hkern u1="p" u2="&#x2122;" k="92" />
<hkern u1="p" u2="v" k="6" />
<hkern u1="p" u2="\" k="123" />
<hkern u1="p" u2="&#x2a;" k="72" />
<hkern u1="q" u2="j" k="-131" />
<hkern u1="q" u2="&#x2122;" k="41" />
<hkern u1="q" u2="\" k="61" />
<hkern u1="q" u2="&#x2a;" k="31" />
<hkern u1="r" u2="x" k="-20" />
<hkern u1="r" u2="v" k="-33" />
<hkern u1="r" u2="q" k="8" />
<hkern u1="r" u2="]" k="41" />
<hkern u1="r" u2="\" k="41" />
<hkern u1="r" u2="&#x2f;" k="82" />
<hkern u1="s" u2="&#x2122;" k="61" />
<hkern u1="s" u2="\" k="82" />
<hkern u1="s" u2="&#x2a;" k="51" />
<hkern u1="t" u2="\" k="61" />
<hkern u1="t" u2="&#x2f;" k="-45" />
<hkern u1="u" u2="&#x2122;" k="37" />
<hkern u1="u" u2="\" k="61" />
<hkern u1="v" g2="uniFB02" k="-53" />
<hkern u1="v" g2="uniFB01" k="-53" />
<hkern u1="v" u2="&#x2026;" k="113" />
<hkern u1="v" u2="&#x201e;" k="113" />
<hkern u1="v" u2="&#x201d;" k="-41" />
<hkern u1="v" u2="&#x201a;" k="113" />
<hkern u1="v" u2="&#x2019;" k="-41" />
<hkern u1="v" u2="&#x153;" k="6" />
<hkern u1="v" u2="&#xff;" k="-27" />
<hkern u1="v" u2="&#xfd;" k="-27" />
<hkern u1="v" u2="&#xf8;" k="6" />
<hkern u1="v" u2="&#xf6;" k="6" />
<hkern u1="v" u2="&#xf5;" k="6" />
<hkern u1="v" u2="&#xf4;" k="6" />
<hkern u1="v" u2="&#xf3;" k="6" />
<hkern u1="v" u2="&#xf2;" k="6" />
<hkern u1="v" u2="&#xf0;" k="6" />
<hkern u1="v" u2="&#xeb;" k="6" />
<hkern u1="v" u2="&#xea;" k="6" />
<hkern u1="v" u2="&#xe9;" k="6" />
<hkern u1="v" u2="&#xe8;" k="6" />
<hkern u1="v" u2="&#xe7;" k="6" />
<hkern u1="v" u2="&#xe6;" k="16" />
<hkern u1="v" u2="&#xe5;" k="16" />
<hkern u1="v" u2="&#xe4;" k="16" />
<hkern u1="v" u2="&#xe3;" k="16" />
<hkern u1="v" u2="&#xe2;" k="16" />
<hkern u1="v" u2="&#xe1;" k="16" />
<hkern u1="v" u2="&#xe0;" k="16" />
<hkern u1="v" u2="y" k="-27" />
<hkern u1="v" u2="w" k="-16" />
<hkern u1="v" u2="t" k="-53" />
<hkern u1="v" u2="o" k="6" />
<hkern u1="v" u2="f" k="-53" />
<hkern u1="v" u2="e" k="6" />
<hkern u1="v" u2="d" k="6" />
<hkern u1="v" u2="c" k="6" />
<hkern u1="v" u2="a" k="16" />
<hkern u1="v" u2="&#x3b;" k="-20" />
<hkern u1="v" u2="&#x3a;" k="-20" />
<hkern u1="v" u2="&#x2e;" k="113" />
<hkern u1="v" u2="&#x2c;" k="113" />
<hkern u1="v" u2="v" k="-16" />
<hkern u1="v" u2="q" k="6" />
<hkern u1="v" u2="\" k="41" />
<hkern u1="w" u2="&#x2122;" k="31" />
<hkern u1="w" u2="v" k="-20" />
<hkern u1="w" u2="\" k="41" />
<hkern u1="x" u2="&#x201d;" k="-20" />
<hkern u1="x" u2="&#x2019;" k="-20" />
<hkern u1="x" u2="&#x153;" k="6" />
<hkern u1="x" u2="&#xff;" k="-16" />
<hkern u1="x" u2="&#xfd;" k="-16" />
<hkern u1="x" u2="&#xf8;" k="6" />
<hkern u1="x" u2="&#xf6;" k="6" />
<hkern u1="x" u2="&#xf5;" k="6" />
<hkern u1="x" u2="&#xf4;" k="6" />
<hkern u1="x" u2="&#xf3;" k="6" />
<hkern u1="x" u2="&#xf2;" k="6" />
<hkern u1="x" u2="&#xf0;" k="6" />
<hkern u1="x" u2="&#xeb;" k="6" />
<hkern u1="x" u2="&#xea;" k="6" />
<hkern u1="x" u2="&#xe9;" k="6" />
<hkern u1="x" u2="&#xe8;" k="6" />
<hkern u1="x" u2="&#xe7;" k="6" />
<hkern u1="x" u2="z" k="-16" />
<hkern u1="x" u2="y" k="-16" />
<hkern u1="x" u2="o" k="6" />
<hkern u1="x" u2="g" k="6" />
<hkern u1="x" u2="e" k="6" />
<hkern u1="x" u2="d" k="6" />
<hkern u1="x" u2="c" k="6" />
<hkern u1="x" u2="&#x3b;" k="-20" />
<hkern u1="x" u2="&#x3a;" k="-20" />
<hkern u1="x" u2="&#x2122;" k="41" />
<hkern u1="x" u2="x" k="-12" />
<hkern u1="x" u2="]" k="-20" />
<hkern u1="x" u2="\" k="41" />
<hkern u1="x" u2="&#x2f;" k="-25" />
<hkern u1="y" u2="v" k="-20" />
<hkern u1="y" u2="\" k="41" />
<hkern u1="z" u2="&#xbb;" k="-61" />
<hkern u1="z" u2="x" k="-8" />
<hkern u1="z" u2="v" k="-20" />
<hkern u1="z" u2="]" k="-31" />
<hkern u1="z" u2="\" k="61" />
<hkern u1="&#x7b;" u2="&#x178;" k="-41" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-41" />
<hkern u1="&#x7b;" u2="&#xc5;" k="-20" />
<hkern u1="&#x7b;" u2="&#xc4;" k="-20" />
<hkern u1="&#x7b;" u2="&#xc3;" k="-20" />
<hkern u1="&#x7b;" u2="&#xc2;" k="-20" />
<hkern u1="&#x7b;" u2="&#xc1;" k="-20" />
<hkern u1="&#x7b;" u2="&#xc0;" k="-20" />
<hkern u1="&#x7b;" u2="j" k="-205" />
<hkern u1="&#x7b;" u2="Z" k="-20" />
<hkern u1="&#x7b;" u2="Y" k="-41" />
<hkern u1="&#x7b;" u2="W" k="-41" />
<hkern u1="&#x7b;" u2="T" k="-41" />
<hkern u1="&#x7b;" u2="A" k="-20" />
<hkern u1="&#x7b;" u2="V" k="-41" />
<hkern u1="&#x7b;" u2="J" k="20" />
<hkern u1="&#xa1;" u2="j" k="-164" />
<hkern u1="&#xab;" g2="uniFB02" k="-61" />
<hkern u1="&#xab;" g2="uniFB01" k="-61" />
<hkern u1="&#xab;" u2="&#x178;" k="61" />
<hkern u1="&#xab;" u2="&#xdd;" k="61" />
<hkern u1="&#xab;" u2="&#xc5;" k="-31" />
<hkern u1="&#xab;" u2="&#xc4;" k="-31" />
<hkern u1="&#xab;" u2="&#xc3;" k="-31" />
<hkern u1="&#xab;" u2="&#xc2;" k="-31" />
<hkern u1="&#xab;" u2="&#xc1;" k="-31" />
<hkern u1="&#xab;" u2="&#xc0;" k="-31" />
<hkern u1="&#xab;" u2="z" k="-61" />
<hkern u1="&#xab;" u2="f" k="-61" />
<hkern u1="&#xab;" u2="Y" k="61" />
<hkern u1="&#xab;" u2="T" k="82" />
<hkern u1="&#xab;" u2="A" k="-31" />
<hkern u1="&#xab;" u2="X" k="20" />
<hkern u1="&#xab;" u2="V" k="20" />
<hkern u1="&#xbf;" u2="j" k="-225" />
<hkern u1="&#xbf;" u2="T" k="129" />
<hkern u1="&#xc0;" u2="&#x2122;" k="164" />
<hkern u1="&#xc0;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc0;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc0;" u2="&#xae;" k="61" />
<hkern u1="&#xc0;" u2="&#x7d;" k="-20" />
<hkern u1="&#xc0;" u2="v" k="33" />
<hkern u1="&#xc0;" u2="q" k="8" />
<hkern u1="&#xc0;" u2="b" k="20" />
<hkern u1="&#xc0;" u2="]" k="-20" />
<hkern u1="&#xc0;" u2="\" k="143" />
<hkern u1="&#xc0;" u2="V" k="78" />
<hkern u1="&#xc0;" u2="Q" k="41" />
<hkern u1="&#xc0;" u2="J" k="-31" />
<hkern u1="&#xc0;" u2="E" k="29" />
<hkern u1="&#xc0;" u2="&#x3f;" k="51" />
<hkern u1="&#xc0;" u2="&#x2f;" k="-41" />
<hkern u1="&#xc0;" u2="&#x2a;" k="143" />
<hkern u1="&#xc1;" u2="&#x2122;" k="164" />
<hkern u1="&#xc1;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc1;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc1;" u2="&#xae;" k="61" />
<hkern u1="&#xc1;" u2="&#x7d;" k="-20" />
<hkern u1="&#xc1;" u2="v" k="33" />
<hkern u1="&#xc1;" u2="q" k="8" />
<hkern u1="&#xc1;" u2="b" k="20" />
<hkern u1="&#xc1;" u2="]" k="-20" />
<hkern u1="&#xc1;" u2="\" k="143" />
<hkern u1="&#xc1;" u2="V" k="78" />
<hkern u1="&#xc1;" u2="Q" k="41" />
<hkern u1="&#xc1;" u2="J" k="-31" />
<hkern u1="&#xc1;" u2="E" k="29" />
<hkern u1="&#xc1;" u2="&#x3f;" k="51" />
<hkern u1="&#xc1;" u2="&#x2f;" k="-41" />
<hkern u1="&#xc1;" u2="&#x2a;" k="143" />
<hkern u1="&#xc2;" u2="&#x2122;" k="164" />
<hkern u1="&#xc2;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc2;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc2;" u2="&#xae;" k="61" />
<hkern u1="&#xc2;" u2="&#x7d;" k="-20" />
<hkern u1="&#xc2;" u2="v" k="33" />
<hkern u1="&#xc2;" u2="q" k="8" />
<hkern u1="&#xc2;" u2="b" k="20" />
<hkern u1="&#xc2;" u2="]" k="-20" />
<hkern u1="&#xc2;" u2="\" k="143" />
<hkern u1="&#xc2;" u2="V" k="78" />
<hkern u1="&#xc2;" u2="Q" k="41" />
<hkern u1="&#xc2;" u2="J" k="-31" />
<hkern u1="&#xc2;" u2="E" k="29" />
<hkern u1="&#xc2;" u2="&#x3f;" k="51" />
<hkern u1="&#xc2;" u2="&#x2f;" k="-41" />
<hkern u1="&#xc2;" u2="&#x2a;" k="143" />
<hkern u1="&#xc3;" u2="&#x2122;" k="164" />
<hkern u1="&#xc3;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc3;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc3;" u2="&#xae;" k="61" />
<hkern u1="&#xc3;" u2="&#x7d;" k="-20" />
<hkern u1="&#xc3;" u2="v" k="33" />
<hkern u1="&#xc3;" u2="q" k="8" />
<hkern u1="&#xc3;" u2="b" k="20" />
<hkern u1="&#xc3;" u2="]" k="-20" />
<hkern u1="&#xc3;" u2="\" k="143" />
<hkern u1="&#xc3;" u2="V" k="78" />
<hkern u1="&#xc3;" u2="Q" k="41" />
<hkern u1="&#xc3;" u2="J" k="-31" />
<hkern u1="&#xc3;" u2="E" k="29" />
<hkern u1="&#xc3;" u2="&#x3f;" k="51" />
<hkern u1="&#xc3;" u2="&#x2f;" k="-41" />
<hkern u1="&#xc3;" u2="&#x2a;" k="143" />
<hkern u1="&#xc4;" u2="&#x2122;" k="164" />
<hkern u1="&#xc4;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc4;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc4;" u2="&#xae;" k="61" />
<hkern u1="&#xc4;" u2="&#x7d;" k="-20" />
<hkern u1="&#xc4;" u2="v" k="33" />
<hkern u1="&#xc4;" u2="q" k="8" />
<hkern u1="&#xc4;" u2="b" k="20" />
<hkern u1="&#xc4;" u2="]" k="-20" />
<hkern u1="&#xc4;" u2="\" k="143" />
<hkern u1="&#xc4;" u2="V" k="78" />
<hkern u1="&#xc4;" u2="Q" k="41" />
<hkern u1="&#xc4;" u2="J" k="-31" />
<hkern u1="&#xc4;" u2="E" k="29" />
<hkern u1="&#xc4;" u2="&#x3f;" k="51" />
<hkern u1="&#xc4;" u2="&#x2f;" k="-41" />
<hkern u1="&#xc4;" u2="&#x2a;" k="143" />
<hkern u1="&#xc5;" u2="&#x2122;" k="164" />
<hkern u1="&#xc5;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc5;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc5;" u2="&#xae;" k="61" />
<hkern u1="&#xc5;" u2="&#x7d;" k="-20" />
<hkern u1="&#xc5;" u2="v" k="33" />
<hkern u1="&#xc5;" u2="q" k="8" />
<hkern u1="&#xc5;" u2="b" k="20" />
<hkern u1="&#xc5;" u2="]" k="-20" />
<hkern u1="&#xc5;" u2="\" k="143" />
<hkern u1="&#xc5;" u2="V" k="78" />
<hkern u1="&#xc5;" u2="Q" k="41" />
<hkern u1="&#xc5;" u2="J" k="-31" />
<hkern u1="&#xc5;" u2="E" k="29" />
<hkern u1="&#xc5;" u2="&#x3f;" k="51" />
<hkern u1="&#xc5;" u2="&#x2f;" k="-41" />
<hkern u1="&#xc5;" u2="&#x2a;" k="143" />
<hkern u1="&#xc6;" u2="V" k="-20" />
<hkern u1="&#xc6;" u2="Q" k="20" />
<hkern u1="&#xc6;" u2="J" k="-8" />
<hkern u1="&#xc6;" u2="&#x3f;" k="-20" />
<hkern u1="&#xc7;" u2="&#xef;" k="-123" />
<hkern u1="&#xc7;" u2="&#xee;" k="-61" />
<hkern u1="&#xc7;" u2="&#xec;" k="-20" />
<hkern u1="&#xc7;" u2="q" k="20" />
<hkern u1="&#xc7;" u2="X" k="-33" />
<hkern u1="&#xc7;" u2="V" k="-16" />
<hkern u1="&#xc7;" u2="Q" k="29" />
<hkern u1="&#xc7;" u2="J" k="-31" />
<hkern u1="&#xc7;" u2="&#x3f;" k="8" />
<hkern u1="&#xc7;" u2="&#x29;" k="-41" />
<hkern u1="&#xc8;" u2="V" k="-20" />
<hkern u1="&#xc8;" u2="Q" k="20" />
<hkern u1="&#xc8;" u2="J" k="-8" />
<hkern u1="&#xc8;" u2="&#x3f;" k="-20" />
<hkern u1="&#xc9;" u2="V" k="-20" />
<hkern u1="&#xc9;" u2="Q" k="20" />
<hkern u1="&#xc9;" u2="J" k="-8" />
<hkern u1="&#xc9;" u2="&#x3f;" k="-20" />
<hkern u1="&#xca;" u2="V" k="-20" />
<hkern u1="&#xca;" u2="Q" k="20" />
<hkern u1="&#xca;" u2="J" k="-8" />
<hkern u1="&#xca;" u2="&#x3f;" k="-20" />
<hkern u1="&#xcb;" u2="V" k="-20" />
<hkern u1="&#xcb;" u2="Q" k="20" />
<hkern u1="&#xcb;" u2="J" k="-8" />
<hkern u1="&#xcb;" u2="&#x3f;" k="-20" />
<hkern u1="&#xd0;" u2="&#x2122;" k="37" />
<hkern u1="&#xd0;" u2="v" k="-41" />
<hkern u1="&#xd0;" u2="X" k="20" />
<hkern u1="&#xd2;" u2="X" k="20" />
<hkern u1="&#xd2;" u2="V" k="20" />
<hkern u1="&#xd3;" u2="X" k="20" />
<hkern u1="&#xd3;" u2="V" k="20" />
<hkern u1="&#xd4;" u2="X" k="20" />
<hkern u1="&#xd4;" u2="V" k="20" />
<hkern u1="&#xd5;" u2="X" k="20" />
<hkern u1="&#xd5;" u2="V" k="20" />
<hkern u1="&#xd6;" u2="X" k="20" />
<hkern u1="&#xd6;" u2="V" k="20" />
<hkern u1="&#xd8;" u2="X" k="20" />
<hkern u1="&#xd8;" u2="V" k="20" />
<hkern u1="&#xdd;" u2="&#xef;" k="-143" />
<hkern u1="&#xdd;" u2="&#xee;" k="-143" />
<hkern u1="&#xdd;" u2="&#xec;" k="-102" />
<hkern u1="&#xdd;" u2="&#xbb;" k="61" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-41" />
<hkern u1="&#xdd;" u2="x" k="20" />
<hkern u1="&#xdd;" u2="q" k="82" />
<hkern u1="&#xdd;" u2="p" k="61" />
<hkern u1="&#xdd;" u2="m" k="61" />
<hkern u1="&#xdd;" u2="h" k="20" />
<hkern u1="&#xdd;" u2="b" k="20" />
<hkern u1="&#xdd;" u2="]" k="-29" />
<hkern u1="&#xdd;" u2="\" k="-57" />
<hkern u1="&#xdd;" u2="V" k="-20" />
<hkern u1="&#xdd;" u2="Q" k="20" />
<hkern u1="&#xdd;" u2="M" k="20" />
<hkern u1="&#xdd;" u2="J" k="113" />
<hkern u1="&#xdd;" u2="E" k="20" />
<hkern u1="&#xdd;" u2="&#x40;" k="82" />
<hkern u1="&#xdd;" u2="&#x3f;" k="-45" />
<hkern u1="&#xdd;" u2="&#x2f;" k="119" />
<hkern u1="&#xdd;" u2="&#x2a;" k="-41" />
<hkern u1="&#xdd;" u2="&#x29;" k="-20" />
<hkern u1="&#xdd;" u2="&#x26;" k="41" />
<hkern u1="&#xde;" g2="uniFB02" k="-41" />
<hkern u1="&#xde;" g2="uniFB01" k="-41" />
<hkern u1="&#xde;" u2="&#xff;" k="-61" />
<hkern u1="&#xde;" u2="&#xfd;" k="-61" />
<hkern u1="&#xde;" u2="z" k="-20" />
<hkern u1="&#xde;" u2="y" k="-61" />
<hkern u1="&#xde;" u2="w" k="-61" />
<hkern u1="&#xde;" u2="t" k="-41" />
<hkern u1="&#xde;" u2="f" k="-41" />
<hkern u1="&#xde;" u2="T" k="82" />
<hkern u1="&#xde;" u2="x" k="-20" />
<hkern u1="&#xde;" u2="v" k="-41" />
<hkern u1="&#xdf;" u2="&#xff;" k="20" />
<hkern u1="&#xdf;" u2="&#xfd;" k="20" />
<hkern u1="&#xdf;" u2="y" k="20" />
<hkern u1="&#xdf;" u2="v" k="20" />
<hkern u1="&#xe0;" u2="&#x2122;" k="82" />
<hkern u1="&#xe0;" u2="\" k="123" />
<hkern u1="&#xe0;" u2="&#x2a;" k="51" />
<hkern u1="&#xe1;" u2="&#x2122;" k="82" />
<hkern u1="&#xe1;" u2="\" k="123" />
<hkern u1="&#xe1;" u2="&#x2a;" k="51" />
<hkern u1="&#xe2;" u2="&#x2122;" k="82" />
<hkern u1="&#xe2;" u2="\" k="123" />
<hkern u1="&#xe2;" u2="&#x2a;" k="51" />
<hkern u1="&#xe3;" u2="&#x2122;" k="82" />
<hkern u1="&#xe3;" u2="\" k="123" />
<hkern u1="&#xe3;" u2="&#x2a;" k="51" />
<hkern u1="&#xe4;" u2="&#x2122;" k="82" />
<hkern u1="&#xe4;" u2="\" k="123" />
<hkern u1="&#xe4;" u2="&#x2a;" k="51" />
<hkern u1="&#xe5;" u2="&#x2122;" k="82" />
<hkern u1="&#xe5;" u2="\" k="123" />
<hkern u1="&#xe5;" u2="&#x2a;" k="51" />
<hkern u1="&#xe6;" u2="&#x2122;" k="82" />
<hkern u1="&#xe6;" u2="v" k="6" />
<hkern u1="&#xe6;" u2="\" k="111" />
<hkern u1="&#xe6;" u2="&#x2f;" k="-20" />
<hkern u1="&#xe6;" u2="&#x2a;" k="72" />
<hkern u1="&#xe7;" u2="x" k="-20" />
<hkern u1="&#xe7;" u2="v" k="-20" />
<hkern u1="&#xe7;" u2="\" k="82" />
<hkern u1="&#xe7;" u2="&#x2f;" k="-41" />
<hkern u1="&#xe7;" u2="&#x29;" k="-20" />
<hkern u1="&#xe8;" u2="&#x2122;" k="82" />
<hkern u1="&#xe8;" u2="v" k="6" />
<hkern u1="&#xe8;" u2="\" k="111" />
<hkern u1="&#xe8;" u2="&#x2f;" k="-20" />
<hkern u1="&#xe8;" u2="&#x2a;" k="72" />
<hkern u1="&#xe9;" u2="&#x2122;" k="82" />
<hkern u1="&#xe9;" u2="v" k="6" />
<hkern u1="&#xe9;" u2="\" k="111" />
<hkern u1="&#xe9;" u2="&#x2f;" k="-20" />
<hkern u1="&#xe9;" u2="&#x2a;" k="72" />
<hkern u1="&#xea;" u2="&#x2122;" k="82" />
<hkern u1="&#xea;" u2="v" k="6" />
<hkern u1="&#xea;" u2="\" k="111" />
<hkern u1="&#xea;" u2="&#x2f;" k="-20" />
<hkern u1="&#xea;" u2="&#x2a;" k="72" />
<hkern u1="&#xeb;" u2="&#x2122;" k="82" />
<hkern u1="&#xeb;" u2="v" k="6" />
<hkern u1="&#xeb;" u2="\" k="111" />
<hkern u1="&#xeb;" u2="&#x2f;" k="-20" />
<hkern u1="&#xeb;" u2="&#x2a;" k="72" />
<hkern u1="&#xec;" u2="&#xef;" k="-82" />
<hkern u1="&#xec;" u2="&#xee;" k="-61" />
<hkern u1="&#xec;" u2="&#x2f;" k="20" />
<hkern u1="&#xec;" u2="&#x2a;" k="-12" />
<hkern u1="&#xed;" u2="&#xed;" k="-41" />
<hkern u1="&#xed;" u2="&#xec;" k="-41" />
<hkern u1="&#xed;" u2="l" k="-41" />
<hkern u1="&#xed;" u2="j" k="-41" />
<hkern u1="&#xed;" u2="i" k="-41" />
<hkern u1="&#xed;" u2="&#xef;" k="-82" />
<hkern u1="&#xed;" u2="&#xee;" k="-61" />
<hkern u1="&#xed;" u2="k" k="-41" />
<hkern u1="&#xed;" u2="h" k="-41" />
<hkern u1="&#xed;" u2="b" k="-41" />
<hkern u1="&#xed;" u2="&#x2f;" k="20" />
<hkern u1="&#xed;" u2="&#x2a;" k="-12" />
<hkern u1="&#xee;" u2="&#xed;" k="-61" />
<hkern u1="&#xee;" u2="&#xec;" k="-61" />
<hkern u1="&#xee;" u2="l" k="-41" />
<hkern u1="&#xee;" u2="j" k="-82" />
<hkern u1="&#xee;" u2="i" k="-61" />
<hkern u1="&#xee;" u2="&#xef;" k="-82" />
<hkern u1="&#xee;" u2="&#xee;" k="-61" />
<hkern u1="&#xee;" u2="k" k="-41" />
<hkern u1="&#xee;" u2="h" k="-41" />
<hkern u1="&#xee;" u2="b" k="-41" />
<hkern u1="&#xee;" u2="&#x2f;" k="20" />
<hkern u1="&#xee;" u2="&#x2a;" k="-12" />
<hkern u1="&#xef;" u2="&#xed;" k="-102" />
<hkern u1="&#xef;" u2="&#xec;" k="-102" />
<hkern u1="&#xef;" u2="l" k="-102" />
<hkern u1="&#xef;" u2="j" k="-102" />
<hkern u1="&#xef;" u2="i" k="-102" />
<hkern u1="&#xef;" u2="&#xef;" k="-82" />
<hkern u1="&#xef;" u2="&#xee;" k="-61" />
<hkern u1="&#xef;" u2="k" k="-102" />
<hkern u1="&#xef;" u2="h" k="-102" />
<hkern u1="&#xef;" u2="b" k="-102" />
<hkern u1="&#xef;" u2="&#x2f;" k="20" />
<hkern u1="&#xef;" u2="&#x2a;" k="-12" />
<hkern u1="&#xf1;" u2="&#x2122;" k="61" />
<hkern u1="&#xf1;" u2="\" k="82" />
<hkern u1="&#xf1;" u2="&#x2a;" k="82" />
<hkern u1="&#xf2;" u2="&#x2122;" k="92" />
<hkern u1="&#xf2;" u2="x" k="6" />
<hkern u1="&#xf2;" u2="v" k="6" />
<hkern u1="&#xf2;" u2="\" k="123" />
<hkern u1="&#xf2;" u2="&#x2a;" k="72" />
<hkern u1="&#xf3;" u2="&#x2122;" k="92" />
<hkern u1="&#xf3;" u2="x" k="6" />
<hkern u1="&#xf3;" u2="v" k="6" />
<hkern u1="&#xf3;" u2="\" k="123" />
<hkern u1="&#xf3;" u2="&#x2a;" k="72" />
<hkern u1="&#xf4;" u2="&#x2122;" k="92" />
<hkern u1="&#xf4;" u2="x" k="6" />
<hkern u1="&#xf4;" u2="v" k="6" />
<hkern u1="&#xf4;" u2="\" k="123" />
<hkern u1="&#xf4;" u2="&#x2a;" k="72" />
<hkern u1="&#xf5;" u2="&#x2122;" k="92" />
<hkern u1="&#xf5;" u2="x" k="6" />
<hkern u1="&#xf5;" u2="v" k="6" />
<hkern u1="&#xf5;" u2="\" k="123" />
<hkern u1="&#xf5;" u2="&#x2a;" k="72" />
<hkern u1="&#xf6;" u2="&#x2122;" k="92" />
<hkern u1="&#xf6;" u2="x" k="6" />
<hkern u1="&#xf6;" u2="v" k="6" />
<hkern u1="&#xf6;" u2="\" k="123" />
<hkern u1="&#xf6;" u2="&#x2a;" k="72" />
<hkern u1="&#xf8;" u2="&#x2122;" k="92" />
<hkern u1="&#xf8;" u2="x" k="6" />
<hkern u1="&#xf8;" u2="v" k="6" />
<hkern u1="&#xf8;" u2="\" k="123" />
<hkern u1="&#xf8;" u2="&#x2a;" k="72" />
<hkern u1="&#xf9;" u2="&#x2122;" k="37" />
<hkern u1="&#xf9;" u2="\" k="61" />
<hkern u1="&#xfa;" u2="&#x2122;" k="37" />
<hkern u1="&#xfa;" u2="\" k="61" />
<hkern u1="&#xfb;" u2="&#x2122;" k="37" />
<hkern u1="&#xfb;" u2="\" k="61" />
<hkern u1="&#xfc;" u2="&#x2122;" k="37" />
<hkern u1="&#xfc;" u2="\" k="61" />
<hkern u1="&#xfd;" u2="v" k="-20" />
<hkern u1="&#xfd;" u2="\" k="41" />
<hkern u1="&#xff;" u2="v" k="-20" />
<hkern u1="&#xff;" u2="\" k="41" />
<hkern u1="&#x152;" u2="V" k="-20" />
<hkern u1="&#x152;" u2="Q" k="20" />
<hkern u1="&#x152;" u2="J" k="-8" />
<hkern u1="&#x152;" u2="&#x3f;" k="-20" />
<hkern u1="&#x153;" u2="&#x2122;" k="82" />
<hkern u1="&#x153;" u2="v" k="6" />
<hkern u1="&#x153;" u2="\" k="111" />
<hkern u1="&#x153;" u2="&#x2f;" k="-20" />
<hkern u1="&#x153;" u2="&#x2a;" k="72" />
<hkern u1="&#x178;" u2="&#xef;" k="-143" />
<hkern u1="&#x178;" u2="&#xee;" k="-143" />
<hkern u1="&#x178;" u2="&#xec;" k="-102" />
<hkern u1="&#x178;" u2="&#xbb;" k="61" />
<hkern u1="&#x178;" u2="&#x7d;" k="-41" />
<hkern u1="&#x178;" u2="x" k="20" />
<hkern u1="&#x178;" u2="q" k="82" />
<hkern u1="&#x178;" u2="p" k="61" />
<hkern u1="&#x178;" u2="m" k="61" />
<hkern u1="&#x178;" u2="h" k="20" />
<hkern u1="&#x178;" u2="b" k="20" />
<hkern u1="&#x178;" u2="]" k="-29" />
<hkern u1="&#x178;" u2="\" k="-57" />
<hkern u1="&#x178;" u2="V" k="-20" />
<hkern u1="&#x178;" u2="Q" k="20" />
<hkern u1="&#x178;" u2="M" k="20" />
<hkern u1="&#x178;" u2="J" k="113" />
<hkern u1="&#x178;" u2="E" k="20" />
<hkern u1="&#x178;" u2="&#x40;" k="82" />
<hkern u1="&#x178;" u2="&#x3f;" k="-45" />
<hkern u1="&#x178;" u2="&#x2f;" k="119" />
<hkern u1="&#x178;" u2="&#x2a;" k="-41" />
<hkern u1="&#x178;" u2="&#x29;" k="-20" />
<hkern u1="&#x178;" u2="&#x26;" k="41" />
<hkern u1="&#x2018;" u2="v" k="-20" />
<hkern u1="&#x2018;" u2="X" k="-20" />
<hkern u1="&#x2018;" u2="V" k="-41" />
<hkern u1="&#x201a;" u2="v" k="113" />
<hkern u1="&#x201a;" u2="V" k="164" />
<hkern u1="&#x201a;" u2="J" k="-29" />
<hkern u1="&#x201c;" u2="v" k="-20" />
<hkern u1="&#x201c;" u2="X" k="-20" />
<hkern u1="&#x201c;" u2="V" k="-41" />
<hkern u1="&#x201d;" u2="s" k="102" />
<hkern u1="&#x201e;" u2="v" k="113" />
<hkern u1="&#x201e;" u2="V" k="164" />
<hkern u1="&#x201e;" u2="J" k="-29" />
<hkern u1="&#x2026;" u2="v" k="113" />
<hkern u1="&#x2026;" u2="V" k="164" />
<hkern u1="&#x2026;" u2="J" k="-29" />
<hkern u1="&#x2039;" u2="&#xc5;" k="-31" />
<hkern u1="&#x2039;" u2="&#xc4;" k="-31" />
<hkern u1="&#x2039;" u2="&#xc3;" k="-31" />
<hkern u1="&#x2039;" u2="&#xc2;" k="-31" />
<hkern u1="&#x2039;" u2="&#xc1;" k="-31" />
<hkern u1="&#x2039;" u2="&#xc0;" k="-31" />
<hkern u1="&#x2039;" u2="A" k="-31" />
<hkern g1="uniFB01" u2="&#xef;" k="-82" />
<hkern g1="uniFB01" u2="&#xee;" k="-61" />
<hkern g1="uniFB01" u2="&#x2f;" k="20" />
<hkern g1="uniFB01" u2="&#x2a;" k="-12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="g" 	k="25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="4" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="119" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="90" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-29" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="AE" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,ccedilla" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="d" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="37" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="G" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="123" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,Ccedilla" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="W" 	k="-29" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="-8" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-61" />
<hkern g1="C,Ccedilla" 	g2="c,ccedilla" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="d" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="G" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="C,Ccedilla" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="-31" />
<hkern g1="C,Ccedilla" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-31" />
<hkern g1="C,Ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="D,Eth" 	g2="w" 	k="-41" />
<hkern g1="D,Eth" 	g2="y,yacute,ydieresis" 	k="-41" />
<hkern g1="D,Eth" 	g2="T" 	k="33" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="37" />
<hkern g1="D,Eth" 	g2="AE" 	k="41" />
<hkern g1="D,Eth" 	g2="t" 	k="-16" />
<hkern g1="D,Eth" 	g2="f,uniFB01,uniFB02" 	k="-16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="T" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="W" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Y,Yacute,Ydieresis" 	k="-12" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="G" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,Ccedilla" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Z" 	k="-20" />
<hkern g1="K" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="K" 	g2="g" 	k="20" />
<hkern g1="K" 	g2="w" 	k="20" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="K" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="K" 	g2="T" 	k="-33" />
<hkern g1="K" 	g2="W" 	k="-45" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="-33" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-70" />
<hkern g1="K" 	g2="c,ccedilla" 	k="20" />
<hkern g1="K" 	g2="d" 	k="20" />
<hkern g1="K" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="K" 	g2="G" 	k="41" />
<hkern g1="K" 	g2="C,Ccedilla" 	k="41" />
<hkern g1="K" 	g2="S" 	k="-20" />
<hkern g1="K" 	g2="z" 	k="-45" />
<hkern g1="L" 	g2="w" 	k="33" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="61" />
<hkern g1="L" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="L" 	g2="T" 	k="102" />
<hkern g1="L" 	g2="W" 	k="94" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="135" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-70" />
<hkern g1="L" 	g2="AE" 	k="-66" />
<hkern g1="L" 	g2="t" 	k="41" />
<hkern g1="L" 	g2="G" 	k="41" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="238" />
<hkern g1="L" 	g2="C,Ccedilla" 	k="41" />
<hkern g1="L" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="L" 	g2="Z" 	k="-16" />
<hkern g1="L" 	g2="z" 	k="-41" />
<hkern g1="L" 	g2="j" 	k="-102" />
<hkern g1="L" 	g2="s" 	k="-14" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="205" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="33" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="t" 	k="-16" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="f,uniFB01,uniFB02" 	k="-16" />
<hkern g1="R" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="12" />
<hkern g1="R" 	g2="T" 	k="16" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="12" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-16" />
<hkern g1="R" 	g2="Z" 	k="-20" />
<hkern g1="S" 	g2="T" 	k="45" />
<hkern g1="T" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="123" />
<hkern g1="T" 	g2="g" 	k="123" />
<hkern g1="T" 	g2="w" 	k="41" />
<hkern g1="T" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="41" />
<hkern g1="T" 	g2="W" 	k="-33" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-31" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="106" />
<hkern g1="T" 	g2="AE" 	k="143" />
<hkern g1="T" 	g2="c,ccedilla" 	k="123" />
<hkern g1="T" 	g2="d" 	k="123" />
<hkern g1="T" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="123" />
<hkern g1="T" 	g2="t" 	k="41" />
<hkern g1="T" 	g2="G" 	k="41" />
<hkern g1="T" 	g2="C,Ccedilla" 	k="41" />
<hkern g1="T" 	g2="f,uniFB01,uniFB02" 	k="29" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="143" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="115" />
<hkern g1="T" 	g2="S" 	k="-8" />
<hkern g1="T" 	g2="z" 	k="61" />
<hkern g1="T" 	g2="s" 	k="102" />
<hkern g1="T" 	g2="l" 	k="20" />
<hkern g1="T" 	g2="n,ntilde" 	k="102" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="102" />
<hkern g1="T" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="T" 	g2="r" 	k="102" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="t" 	k="-12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="f,uniFB01,uniFB02" 	k="-12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="W" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="29" />
<hkern g1="W" 	g2="g" 	k="29" />
<hkern g1="W" 	g2="T" 	k="-33" />
<hkern g1="W" 	g2="W" 	k="-20" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="W" 	g2="AE" 	k="94" />
<hkern g1="W" 	g2="c,ccedilla" 	k="29" />
<hkern g1="W" 	g2="d" 	k="29" />
<hkern g1="W" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="29" />
<hkern g1="W" 	g2="t" 	k="-53" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="W" 	g2="f,uniFB01,uniFB02" 	k="-55" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="29" />
<hkern g1="W" 	g2="j" 	k="-29" />
<hkern g1="W" 	g2="s" 	k="2" />
<hkern g1="W" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="-29" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="123" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,ccedilla" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="37" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="j" 	k="-25" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="n,ntilde" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="-25" />
<hkern g1="Z" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="Z" 	g2="g" 	k="20" />
<hkern g1="Z" 	g2="w" 	k="25" />
<hkern g1="Z" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="Z" 	g2="W" 	k="-20" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="Z" 	g2="c,ccedilla" 	k="20" />
<hkern g1="Z" 	g2="d" 	k="20" />
<hkern g1="Z" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="Z" 	g2="z" 	k="-10" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="-16" />
<hkern g1="c,ccedilla" 	g2="t" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="f,uniFB01,uniFB02" 	k="-16" />
<hkern g1="g" 	g2="j" 	k="-113" />
<hkern g1="j" 	g2="j" 	k="-111" />
<hkern g1="k" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="k" 	g2="g" 	k="20" />
<hkern g1="k" 	g2="w" 	k="-16" />
<hkern g1="k" 	g2="y,yacute,ydieresis" 	k="-16" />
<hkern g1="k" 	g2="c,ccedilla" 	k="20" />
<hkern g1="k" 	g2="d" 	k="20" />
<hkern g1="k" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="k" 	g2="t" 	k="-20" />
<hkern g1="k" 	g2="f,uniFB01,uniFB02" 	k="-20" />
<hkern g1="k" 	g2="z" 	k="-16" />
<hkern g1="k" 	g2="s" 	k="25" />
<hkern g1="l" 	g2="w" 	k="20" />
<hkern g1="l" 	g2="y,yacute,ydieresis" 	k="33" />
<hkern g1="l" 	g2="t" 	k="41" />
<hkern g1="l" 	g2="quoteright,quotedblright" 	k="82" />
<hkern g1="l" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="r" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="r" 	g2="g" 	k="29" />
<hkern g1="r" 	g2="w" 	k="-37" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-33" />
<hkern g1="r" 	g2="c,ccedilla" 	k="8" />
<hkern g1="r" 	g2="d" 	k="8" />
<hkern g1="r" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="8" />
<hkern g1="r" 	g2="t" 	k="-25" />
<hkern g1="r" 	g2="f,uniFB01,uniFB02" 	k="-29" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="33" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-8" />
<hkern g1="w" 	g2="w" 	k="-20" />
<hkern g1="w" 	g2="y,yacute,ydieresis" 	k="-29" />
<hkern g1="w" 	g2="t" 	k="-41" />
<hkern g1="w" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="w" 	g2="f,uniFB01,uniFB02" 	k="-41" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="w" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="y,yacute,ydieresis" 	g2="w" 	k="-29" />
<hkern g1="y,yacute,ydieresis" 	g2="y,yacute,ydieresis" 	k="-25" />
<hkern g1="y,yacute,ydieresis" 	g2="t" 	k="-49" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="f,uniFB01,uniFB02" 	k="-49" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="70" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="25" />
<hkern g1="y,yacute,ydieresis" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="z" 	g2="quoteright,quotedblright" 	k="-49" />
<hkern g1="z" 	g2="f,uniFB01,uniFB02" 	k="-20" />
<hkern g1="z" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="y,yacute,ydieresis" 	k="119" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="143" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="164" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-98" />
<hkern g1="colon,semicolon" 	g2="w" 	k="-61" />
<hkern g1="colon,semicolon" 	g2="y,yacute,ydieresis" 	k="-61" />
<hkern g1="colon,semicolon" 	g2="T" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="j" 	k="-102" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-41" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="123" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="258" />
<hkern g1="quoteleft,quotedblleft" 	g2="Z" 	k="-20" />
<hkern g1="J" 	g2="AE" 	k="20" />
</font>
</defs></svg> 