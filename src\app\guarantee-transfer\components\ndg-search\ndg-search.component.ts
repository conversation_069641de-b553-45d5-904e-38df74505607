import { Component } from '@angular/core';
import { GuaranteeTransferService } from '../../services/guarantee-transfer.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-ndg-search',
  templateUrl: './ndg-search.component.html'
})
export class NdgSearchComponent {
  differentNdg: boolean = false;
  ndgOrigin: string = '';
  ndgDestination: string = '';

  constructor(
    public guaranteeTransfer: GuaranteeTransferService,
    private router: Router
  ) {
    this.guaranteeTransfer.reset();
  }

  /**
   * @name startGuaranteeTransfer
   * @description Esegue navigate verso rotta iniziale wizard sostituzione garanzia
  */
  startGuaranteeTransfer(): void {
    this.router.navigate([
      `guaranteeTransfer/${this.ndgOrigin}/${this.ndgDestination
        ? this.ndgDestination
        : '-'
      }/select-appraisals`
    ]);
  }

  /**
   * @name toggleDifferentNdg
   * @description Toggle checkbox "NDG DIFFERENTI", differentNdg è il valore contenuto prima del click
   */
  toggleDifferentNdg(): void {
    if (this.differentNdg) {
      this.ndgDestination = null;
    }
  }
}
