<!-- fixme -togliere codice commentato -->
<section *ngIf="menuService.isSearchBoxOpen && !isSide" id="search-form" class="search-form scrollable"
  style="display: block;">
  <form (ngSubmit)="doNewFastSearch()">
    <div class="search-form-wrapper">
      <div class="search-input">
        <input #fastInput class="search-box" required name="cerca" [(ngModel)]="fastSearch" type="text"
          style="color : #333" id="search-phrase" placeholder="Cerca per ndg, numero richiesta" appOnlyNumbers
          [acceptEnter]="true" />
        <button class="btn btn-secondary back-search" id="back-search" type="button" (click)="backToSearch()"
          *ngIf="searchPageService.showBackSearchButton">{{'UBZ.SITE_CONTENT.10010001011' | translate}}</button>
        <button type="button" class="btn btn-clean" id="search-reset" (click)="closeSearchBox()">
          <i class="icon-close"></i>
        </button>
      </div>
    </div>
  </form>
  <div class="search-form-results">
    <div class="search-heading">
      <div class="row">
        <div class="col-sm-12">
          <button type="button" class="btn btn-clean" id="toggle-search-filters"
            (click)="menuService.isAdvancedSearchOpen = !menuService.isAdvancedSearchOpen">
            {{'UBZ.SITE_CONTENT.10100000' | translate }} <i
              [class]="iconArrowStyle[menuService.isAdvancedSearchOpen]"></i>
          </button>
        </div>
      </div>
    </div>
    <div *ngIf="menuService.isAdvancedSearchOpen" class="search-content">
      <form (ngSubmit)="doNewAdvancedSearch()">
        <div class="row">
          <div class="col-sm-3 form-group">
            <label>{{'UBZ.SITE_CONTENT.10100001' | translate }}</label>
            <div class="custom-select">
              <select name="searchType" [(ngModel)]="searchType" class="form-control" (change)="calculateDomains()">
                <option *ngFor="let item of searchTypes" value="{{item.key}}">{{item.value | translate}}</option>
              </select>
            </div>
          </div>

          <ng-container *ngFor="let box of searchFieldsMap[searchType]">
            <div class="{{box.divClass}}">
              <label>{{box.label | translate}}</label>
              <input *ngIf="box.type === 'text'" name="{{box.name}}" type="text" class="form-control"
                placeholder="{{'UBZ.SITE_CONTENT.1000010000' | translate }} {{box.label | translate}}"
                [(ngModel)]="fieldValues[box.name]" />
              <!-- <div *ngIf="box.type === 'date'" class="calendar">
                  <input name="{{box.name}}" class="form-control calendar" placeholder="{{'UBZ.SITE_CONTENT.1001000000' | translate }}" [(ngModel)]="fieldValues[box.name]" 
                  ngui-datetime-picker appDateReadOnly date-only="true" />
                </div> -->
              <app-calendario *ngIf="box.type === 'date'" [name]="box.name" [(ngModel)]="fieldValues[box.name]"
                [placeholder]="'UBZ.SITE_CONTENT.1001000000' | translate">
              </app-calendario>
              <div *ngIf="box.type === 'select'" class="custom-select">
                <select name="{{box.name}}" [(ngModel)]="fieldValues[box.name]" class="form-control">
                  <option value="" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <ng-container *ngIf="domainsMap[box.name]">
                    <option *ngFor="let opt of ( domainsMap[box.name] | domainMapToDomainArray )"
                      value="{{opt.domCode}}">{{opt.translationCod | translate}}</option>
                  </ng-container>
                </select>
              </div>

              <div *ngIf="box.type === 'selectOrigProcess'" class="custom-select">
                <select (change)="resetExtPositionId()" name="{{box.name}}" [(ngModel)]="fieldValues[box.name]"
                  class="form-control">
                  <option value="" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <ng-container *ngIf="domainsMap[box.name]">
                    <option *ngFor="let opt of ( domainsMap[box.name] | domainMapToDomainArray )"
                      value="{{opt.domCode}}">{{opt.translationCod | translate}}</option>
                  </ng-container>
                </select>
              </div>

              <div *ngIf="box.type === 'selectSoc'" class="custom-select">
                <select name="{{box.name}}" [(ngModel)]="fieldValues[box.name]" class="form-control">
                  <option value="" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let item of arrayList" value="{{item}}">{{item}}</option>
                </select>
              </div>

              <input *ngIf="box.type === 'textExtAppraisalId'" [(ngModel)]="fieldValues[box.name]" name="{{box.name}}"
                type="text" class="form-control" [disabled]="!fieldValues['originProcess']"
                placeholder="{{'UBZ.SITE_CONTENT.1000010000' | translate }} {{box.label | translate}}">

            </div>
          </ng-container>
        </div>
        <div class="row">
          <div class="col-sm-12">
            <div class="search-buttons">
              <button type="submit" class="btn btn-primary pull-right" [disabled]="isSearchButtonDisabled()">
                {{'UBZ.SITE_CONTENT.10010111' | translate }}
              </button>
              <button type="button" id="reset-search-input" class="btn btn-secondary pull-right"
                (click)="resetSearch()">
                {{'UBZ.SITE_CONTENT.1010010101' | translate }}
              </button>
            </div>
          </div>
        </div>

      </form>
    </div>
  </div>
</section>

<!--  SEZIONE PER LA RICERCA AVANZATA NEL MENU A LATO-->
<section *ngIf="isSide" id="search-filters" class="hidden-md hidden-lg">
  <div class="search-content">
    <div class="container-fluid">
      <div class="row">

        <div class="col-xs-12 form-group">
          <label>{{'UBZ.SITE_CONTENT.10100001' | translate }}</label>
          <div class="custom-select">
            <select name="searchType" [(ngModel)]="searchType" class="form-control" (change)="calculateDomains()">
              <option *ngFor="let item of searchTypes" value="{{item.key}}">{{item.value | translate}}</option>
            </select>
          </div>
        </div>
        <ng-container *ngFor="let box of searchFieldsMap[searchType]">
          <div class="col-xs-12 form-group">
            <label>{{box.label | translate}}</label>
            <input *ngIf="box.type === 'text'" name="{{box.name}}" type="text" class="form-control"
              placeholder="{{'UBZ.SITE_CONTENT.1000010000' | translate }}" [(ngModel)]="fieldValues[box.name]" />
            <!-- <div *ngIf="box.type === 'date'" class="calendar">
              <input name="{{box.name}}" class="form-control calendar" placeholder="{{'UBZ.SITE_CONTENT.1001000000' | translate }}" [(ngModel)]="fieldValues[box.name]"
               ngui-datetime-picker date-only="true" />
            </div> -->
            <app-calendario *ngIf="box.type === 'date'" [name]="box.name" [(ngModel)]="fieldValues[box.name]"
              [placeholder]="'UBZ.SITE_CONTENT.1001000000' | translate">
            </app-calendario>
            <div *ngIf="box.type === 'select'" class="custom-select">
              <select name="{{box.name}}" [(ngModel)]="fieldValues[box.name]" class="form-control">
                <option value="" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                <ng-container *ngIf="domainsMap[box.name]">
                  <option *ngFor="let opt of ( domainsMap[box.name] | domainMapToDomainArray )" value="{{opt.domCode}}">
                    {{opt.translationCod | translate}}</option>
                </ng-container>
              </select>
            </div>
          </div>
        </ng-container>
      </div>
      <div class="row">
        <div class="col-sm-12">
          <div class="search-buttons">
            <button type="button" class="btn btn-primary pull-right" (click)="doNewAdvancedSearch()"
              [disabled]="isSearchButtonDisabled()">
              {{'UBZ.SITE_CONTENT.10010111' | translate }}
            </button>
            <button type="button" id="reset-search-input" class="btn btn-secondary pull-right" (click)="resetSearch()">
              {{'UBZ.SITE_CONTENT.1010010101' | translate }}
            </button>
          </div>
        </div>
      </div>


    </div>
  </div>
</section>
<!--  DON'T REMOVE!-->
<section *ngIf="menuService.isSearchBoxOpen" id="search-overlay" class="search-overlay" style="display:block;">
  <div class="row">
    <div class="col-sm-12">
    </div>
  </div>
</section>
<!--  DON'T REMOVE!-->