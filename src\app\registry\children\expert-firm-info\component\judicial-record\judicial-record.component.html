<form #f="ngForm">
  <accordion-group #group class="panel" [ngClass]="{'empty-accordion': !judicialRecordExists}" [isOpen]="modify" [isDisabled]="!judicialRecordExists">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          {{'UBZ.SITE_CONTENT.1100010110' | translate }}
          <div class="accordion-button">
            <ng-container *appAuthKey="'UBZ_REGISTRY.JUDICIAL_RECORD_OPEN_NEW'">
              <button *ngIf="!modify && !judicialRecordExists" type="button" class="btn btn-empty" (click)="startModify($event)">
                <i class="fa icon-add"></i>{{'UBZ.SITE_CONTENT.111100001' | translate }}
              </button>
            </ng-container>
            <ng-container *appAuthKey="'UBZ_REGISTRY.JUDICIAL_RECORD_MODIFY'">
              <button *ngIf="!modify && judicialRecordExists" type="button" class="btn btn-empty" (click)="startModify($event)">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.11111' | translate }}
              </button>
            </ng-container>
            <ng-container *ngIf="modify">
              <button *appAuthKey="'UBZ_REGISTRY.JUDICIAL_RECORD_CANCEL'" type="button" class="btn btn-empty" (click)="cancelModify($event)">
                <i class="fa fa-times"></i>{{'UBZ.SITE_CONTENT.100000' | translate }}
              </button>
              <button *appAuthKey="'UBZ_REGISTRY.JUDICIAL_RECORD_SAVE'" type="button" class="btn btn-empty" (click)="saveData($event)"
                [disabled]="f2.invalid">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.100001' | translate }}
              </button>
            </ng-container>
          </div>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <form #f2="ngForm">
            <ng-container *ngIf="modify; else only_visualization">
              <div class="row">
                <div class="col-sm-12 col-md-4 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1100010111' | translate }}*</label>
                  <input type="text" class="form-control" name="numJudReg" [(ngModel)]="judicialRecord.numJudReg" required>
                </div>
                <div class="col-md-4 col-sm-12 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1100001111' | translate }}*</label>
                  <app-calendario [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate" [name]="'releaseDate'" [(ngModel)]="judicialRecord.releaseDate"
                    [required]="true">
                  </app-calendario>
                </div>
                <div class="col-md-4 col-sm-12 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1100010000' | translate }}*</label>
                  <app-calendario [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate" [name]="'expiryDate'" [(ngModel)]="judicialRecord.expiryDate"
                    [required]="true">
                  </app-calendario>
                </div>
              </div>
            </ng-container>
          </form>
        </div>
      </div>
    </div>
  </accordion-group>
</form>

<ng-template #only_visualization>
  <div class="row">
    <div class="col-sm-12 col-md-4 form-group">
      <label>{{'UBZ.SITE_CONTENT.1100010111' | translate }}</label>
      {{ judicialRecord.numJudReg }}
    </div>
    <div class="col-md-4 col-sm-12 form-group">
      <label>{{'UBZ.SITE_CONTENT.1100001111' | translate }}</label>
      {{ judicialRecord.releaseDate | date:'dd-MM-yyyy' }}
    </div>
    <div class="col-md-4 col-sm-12 form-group">
      <label>{{'UBZ.SITE_CONTENT.1100010000' | translate }}</label>
      {{ judicialRecord.expiryDate | date:'dd-MM-yyyy' }}
    </div>
  </div>
</ng-template>
