<div class="form-container">
  <div class="MyTabs nav-wrap nav-controls">
    <ul class="nav nav-tabs pull-left">
      <li *ngFor="let person of customerService.coiNdgList; let i = index" class="MyTabs__Tab" [ngClass]="{
            'active': person.ndg === selectedNdg,
            'MyTabs__Tab--hidden': i > maxVisibleTabs,
            'fit_content': i === maxVisibleTabs
          }">
        <a role="tab" (click)="toggleNdgDetail(person.ndg)" *ngIf="i < maxVisibleTabs">
          <label>
            <i class="fa fa-user" aria-hidden="true"></i> &nbsp;{{person.surname}}</label>
          <label>{{person.ndg}}</label>
        </a>

        <div dropdown class="MyTabs__Dropdown" *ngIf="shouldShowDropdown && (i === maxVisibleTabs)">
          <button class="MyTabs__Dropdown__Toggle btn btn-empty btn-plus" dropdownToggle>
            <i class="fa fa-caret-down"></i>
          </button>

          <div *dropdownMenu class="MyTabs__Dropdown__Menu dropdown-menu dropdown-menu-right" role="menu">
            <ul>
              <li *ngFor="let person of customerService.coiNdgList; let i = index" class="dropdown-item" [ngClass]="{
                      'MyTabs__Dropdown__Menu__Item--active': person.ndg === selectedNdg,
                      'hidden': i < maxVisibleTabs
                    }">
                <a role="tab" (click)="toggleNdgDetail(person.ndg)">
                  <label>
                    <i class="fa fa-user" aria-hidden="true"></i>
                    &nbsp; {{person.surname}}
                  </label>
                  <label>{{person.ndg}}</label>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </li>
    </ul>
  </div>

  <div *ngFor="let person of customerService.coiNdgList; let i = index">
    <div dropdown class="" *ngIf="person.ndg === selectedNdg">
      <div class="row">
        <app-application-form [idCode]="positionId" [page]="'INFO_CLIENTE_RIC'" [positionId]="positionId" [ndg]="selectedNdg"></app-application-form>
      </div>
    </div>
  </div>
</div>
