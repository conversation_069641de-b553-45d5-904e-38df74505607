<div *ngIf="isOpen" class="modal fade" bsModal #modalBox="bs-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" (onHidden)="hide()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.101010111' | translate }}</h2>
        <button type="reset" class="close pull-right" aria-label="Close" (click)="hide()">
          <i class="icon-close"></i>
        </button>
      </div>
      <form #f="ngForm" (ngSubmit)="submit()" novalidate>                    
        <div class="modal-body">
          <div class="row form-group">
            <div class="col-md-12">
              <label for="destinazione">{{'UBZ.SITE_CONTENT.101011000' | translate }}*</label>
              <div class="custom-select">
                <select class="form-control" [(ngModel)]="modelCopy.destination" name="destination" class="form-control">
                  <option value="" hidden selected>Seleziona</option>
                  <option *ngFor="let row of (domains | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
                </select>
              </div>
            </div>
          </div>
          <div class="row form-group">
            <div class="col-md-6">
              <label for="omiMin">{{'UBZ.SITE_CONTENT.101011001' | translate }}*</label>
              <input appOnlyNumbers type="text" name="omiMin" value="" class="form-control" required="" [(ngModel)]="modelCopy.omiMin">
            </div>
            <div class="col-md-6">
              <label for="omiMax">{{'UBZ.SITE_CONTENT.101011010' | translate }}*</label>
              <input appOnlyNumbers type="text" name="omiMax" value="" class="form-control" required="" [(ngModel)]="modelCopy.omiMax">
            </div>
          </div>
          <div class="row form-group">
            <div class="col-md-6">
              <label for="nomismaMin">{{'UBZ.SITE_CONTENT.101011011' | translate }}</label>
              <input appOnlyNumbers type="text" name="nomismaMin" value="" class="form-control" [(ngModel)]="modelCopy.nomismaMin">
            </div>
            <div class="col-md-6">
              <label for="nomismaMax">{{'UBZ.SITE_CONTENT.101011100' | translate }}</label>
              <input appOnlyNumbers type="text" name="nomismaMax" value="" class="form-control" [(ngModel)]="modelCopy.nomismaMax">
            </div>
          </div>
          <div class="row form-group">
            <div class="col-md-6">
              <label for="scenariMin">{{'UBZ.SITE_CONTENT.101011101' | translate }}</label>
              <input appOnlyNumbers type="text" name="scenariMin" value="" class="form-control" [(ngModel)]="modelCopy.scenariMin">
            </div>
            <div class="col-md-6">
              <label for="scenariMax">{{'UBZ.SITE_CONTENT.101011110' | translate }}</label>
              <input appOnlyNumbers type="text" name="scenariMax" value="" class="form-control" [(ngModel)]="modelCopy.scenariMax">
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <label for="">{{'UBZ.SITE_CONTENT.1010001011' | translate }}</label>
              <input appOnlyNumbers type="text" name="otherMin" class="form-control" [(ngModel)]="modelCopy.otherMin">
            </div>
            <div class="col-md-6">
              <label for="otherMax">{{'UBZ.SITE_CONTENT.1010001100' | translate }}</label>
              <input appOnlyNumbers type="text" name="otherMax" class="form-control" [(ngModel)]="modelCopy.otherMax">
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button *ngIf="modalType==='add'" class="btn btn-primary waves-effect" [disabled]="f.form.invalid" type="submit">{{'UBZ.SITE_CONTENT.101010110' | translate }}</button>
          <button *ngIf="modalType==='modify'" class="btn btn-primary waves-effect" [disabled]="f.form.invalid" type="submit">{{'UBZ.SITE_CONTENT.11111' | translate }}</button>          
        </div>
      </form>
    </div>
  </div>
</div>
