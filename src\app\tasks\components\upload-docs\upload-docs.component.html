<app-position-header [positionId]="positionId" [wizardCode]="wizardCode" [lockingUser]="lockingUser"></app-position-header>

<div class="row">
  <div class="col-sm-12 section-headline">
    <h1><i class="icon-print_checklist"></i> {{'UBZ.SITE_CONTENT.110000111' | translate }}</h1>
    <h2>{{'UBZ.SITE_CONTENT.101100111' | translate }} {{positionId}}</h2>
  </div>
</div>

<section id="breadcrumbs" class="breadcrumbs">
  <div class="row">
    <div class="col-sm-12">
      <ul>
        <li><a role="button" (click)="goOnDashboard()">{{'UBZ.SITE_CONTENT.11' | translate }}</a></li>
        <li>{{'UBZ.SITE_CONTENT.110000111' | translate }}</li>
      </ul>
    </div>
  </div>
</section>

<app-task-access-rights [positionId]="positionId" [taskId]="taskId" [taskCod]="taskCod" (taskLocked)="taskLocked()" (taskLockedByOtherUser)="taskLockedByOtherUser($event)" (taskUnlocked)="taskUnlocked()">
  <app-task-button [eventCod]="'UBZ_UMD_OK'" [buttonLabel]="'Valida'" [before]="isChecklistComplete" (success)="goOnDashboard()"></app-task-button>
</app-task-access-rights>
<app-checklist [positionId]="positionId" [isRequestId]="false"></app-checklist>