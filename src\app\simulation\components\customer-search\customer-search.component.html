<section id="page-content-wrapper">
  <div class="container-fluid">
    <!-- Sezione titoli -->
    <div class="row">
      <div *ngIf="wizardCode !== 'WRPE'" class="col-sm-12 form-group section-headline">
        <h1><i class="icon-richiesta_perizia"></i>{{'UBZ.SITE_CONTENT.111' | translate }}</h1>
        <h2>{{'UBZ.SITE_CONTENT.1001001' | translate }}</h2>
      </div>
      <div *ngIf="wizardCode === 'WRPE'" class="col-sm-12 form-group section-headline">
        <h1 *ngIf="requestType === 'PER'"><i class="icon-richiesta_perizia"></i>{{'UBZ.SITE_CONTENT.11011100' | translate }}</h1>
        <h1 *ngIf="requestType === 'SUR'"><i class="icon-richiesta_perizia"></i>{{'UBZ.SITE_CONTENT.1010110101' | translate }}</h1>
        <h1 *ngIf="requestType === 'CTE'"><i class="icon-richiesta_perizia"></i>{{'UBZ.SITE_CONTENT.1010110111' | translate }}</h1>
        <h1 *ngIf="requestType === 'FRG'"><i class="icon-richiesta_perizia"></i>{{'UBZ.SITE_CONTENT.1101110000' | translate}} {{'UBZ.SITE_CONTENT.10100100' | translate}}</h1>
        <h2>{{'UBZ.SITE_CONTENT.1001011' | translate }}</h2>
      </div>
    </div>
    <!-- Fine sezione titoli -->
    <div class="row">
      <form name="start" (ngSubmit)="getCustomerInfo()">
        <div *ngIf="wizardCode !== 'WRPE'" class="col-sm-2 col-md-2">
          <label></label>
          <input type="radio" [(ngModel)]="isProspect" name="prospect" value="true" required />{{'UBZ.SITE_CONTENT.1001100' | translate }}
          <input type="radio" [(ngModel)]="isProspect" name="prospect" value="false" />{{'UBZ.SITE_CONTENT.1001101' | translate }}
        </div>

        <div *ngIf="requestType ==='FRG'" class="col-sm-3 col-md-3">
            <label>{{'UBZ.SITE_CONTENT.1001110' | translate }}</label>
              <div class="custom-select">
                <select class="form-control" name="ndg" [(ngModel)]="ndg" required (change)="getFamilyAsset(ndg)">
                  <option [ngValue]="undefined" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let item of ndgList" value="{{item.value}}">{{ item.label }}</option>
                </select>
              </div>
        </div>
         <div *ngIf="requestType ==='FRG' "  class="col-sm-3 col-md-3">
            <label>{{'UBZ.SITE_CONTENT.100101' | translate }}</label>
              <div class="custom-select">
                <select class="form-control" name="richiesta-famiglia" [(ngModel)]="familyAsset" required [disabled]="singleFamilyChosen">
                  <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let domain of ( domains | domainMapToDomainArray )" value="{{domain.domCode}}">{{ domain.translationCod | translate }}</option>
                </select>
              </div>

          </div>



        <div *ngIf="isProspect !== undefined && requestType !=='FRG'" class="col-sm-3 col-md-3 form-group">
          <label>{{'UBZ.SITE_CONTENT.1001110' | translate }}</label>
          <input type="text" class="form-control numeric" placeholder="{{'UBZ.SITE_CONTENT.110001101' | translate }}" name="ndg" [(ngModel)]="ndg"
            appOnlyNumbers maxLength="16" required />
        </div>
        <div *ngIf="isProspect !== undefined && requestType !=='FRG' " class="col-sm-3 col-md-3">
          <label>{{'UBZ.SITE_CONTENT.100101' | translate }}</label>
          <div class="custom-select">
            <select class="form-control" name="richiesta-famiglia" [(ngModel)]="familyAsset" required [disabled]="isFamilyAssetBlocked">
              <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
              <option *ngFor="let domain of ( domains | domainMapToDomainArray )" value="{{domain.domCode}}">{{ domain.translationCod | translate }}</option>
            </select>
          </div>
        </div>
        <div *ngIf="ndg && familyAsset && familyAsset !== ''" class="col-sm-2 col-md-2">
          <label></label>
          <ng-container *appAuthKey="'UBZ_CUSTOMER.SEARCH_NEXT'">
            <button type="submit" class="btn btn-primary">{{'UBZ.SITE_CONTENT.1001111' | translate }}</button>
          </ng-container>
        </div>
      </form>
    </div>
  </div>
</section>
