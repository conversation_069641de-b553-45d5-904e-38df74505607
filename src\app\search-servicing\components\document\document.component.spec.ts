import { TestBed, ComponentFixture } from '@angular/core/testing';
import { DocumentComponent } from './document.component';
import { Http, HttpModule } from '@angular/http';
import { BrowserModule } from '@angular/platform-browser';
import { CommonModule } from '@angular/common';
import { of } from 'rxjs/observable/of';
import { ChecklistService } from '../../../shared/checklist/service/checklist.service';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { ToastModule } from 'ng2-toastr';
import { CookieModule, CookieService } from 'ngx-cookie';
import { CustomTimePipe } from '../../../shared/pipes/custom-time/custom-time.pipe';
import { DomainMapToDomainArrayPipe } from '../../../shared/pipes/domain-map-to-domain-array/domain-map-to-domain-array.pipe';
import { CUSTOM_ELEMENTS_SCHEMA, LOCALE_ID, NO_ERRORS_SCHEMA } from '@angular/core';
import { TooltipConfig } from 'ngx-bootstrap';
import { CookieOptionsProvider } from 'ngx-cookie/src/cookie-options-provider';
import { APP_CONSTANTS, AppConstants } from '../../../app.constants';
import { getAlertConfig } from '../../../app.module';
import { UploadFileService } from '../../../massive-upload/service/upload-file.service';
import { DomainService } from '../../../shared/domain';
import { CustomHttpService } from '../../../shared/http/custom-http.service';
import { MenuService } from '../../../shared/menu/services/menu.service';
import { MessageService } from '../../../shared/messages/services/message.service';
import { PropertiesService } from '../../../shared/properties/properties.service';
import { SharedService } from '../../../shared/services/shared.service';
import { UserDataService } from '../../../shared/user-data/user-data.service';
import { ApiService } from '../../../shared/api/api.service';

describe('DocumentComponent', () => {
    let component: DocumentComponent;
    let fixture: ComponentFixture<DocumentComponent>;
    const checklistMock = jasmine.createSpyObj('ChecklistService', ['getVersions', 'displayDocument'])

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        RouterTestingModule, ReactiveFormsModule, FormsModule, CommonModule, HttpModule, TranslateModule.forRoot(), CookieModule.forRoot(), ToastModule.forRoot(), BrowserModule
      ],
      declarations: [
        DocumentComponent, CustomTimePipe, DomainMapToDomainArrayPipe
      ],
      providers: [
        MessageService,
          { provide: Http, useClass: CustomHttpService },
          { provide: APP_CONSTANTS, useValue: AppConstants },
          { provide: LOCALE_ID, useValue: 'it-IT' },
          { provide: TooltipConfig, useFactory: getAlertConfig },
          { provide: ChecklistService, useValue: checklistMock},
          UploadFileService, ApiService,
          UserDataService, CookieService, CookieOptionsProvider, MenuService, PropertiesService, DomainService, SharedService
      ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DocumentComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should get document', () => {
    const max = { prog: 'testProg', versionId: '1', uploadDate: new Date(2022, 11, 1) };
    const max1 = { prog: 'testProg1', versionId: '2', uploadDate: new Date(2022, 11, 2) };
    const max2 = { prog: 'testProg1', versionId: '3', uploadDate: new Date(2022, 11, 3) };

    checklistMock.getVersions.and.returnValue(of([max, max1, max2]));

    checklistMock.displayDocument.and.returnValue(of({
      header: new Map().set('content-disposition', 'attachment; filename=document.pdf'),
      document: new Uint8Array([])
    }));

    component.ngOnInit();

    fixture.detectChanges();

    expect(component.pdfSrc).toEqual(new Uint8Array([]));
  });

  
});