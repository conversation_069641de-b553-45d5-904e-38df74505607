import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { ChecklistService } from '../../../shared/checklist/service/checklist.service';
import { DomainService } from '../../../shared/domain';
import { MessageService } from '../../../shared/messages/services/message.service';
import { FractionedAppraisalResponse } from '../../../shared/position/models/fractioned-appraisal-response.model';
import { PositionService } from '../../../shared/position/position.service';
import * as FileSaver from 'file-saver';
import { ActivatedRoute, Params, Router } from '@angular/router';

@Component({
  selector: 'app-checklist-servicing',
  templateUrl: './checklist-servicing.component.html',
  styleUrls: ['./checklist-servicing.component.css']
})
export class ChecklistServicingComponent implements OnInit {
  @Input() positionId: string; // either request or appraisal 
  @Input() requestId: string; // used only in case of checklist use into
  @Input() isRequestId: boolean;
  @Output() isComplete = new EventEmitter();

  accordions: any[];
  allPageIsValid = true;
  allRowSelected = false;
  isValidClass = { Y: 'state green', N: 'state red' };
  renderIconPrint = false;
  renderIconUpload = false;
  checkboxStatus: boolean[] = [];
  progToDocCodMap: string[] = [];
  accordionsStatusOpen: boolean[] = [];
  inputModalObject: any;
  inputModalObject2: any;
  entityTypeDom: any[];
  assetTypeDom: any[];
  categoryTypeDom: any[];
  statusDom: any[];
  bankCod: string = '';
  macroProcess: string = '';
  disableDownload = false;

  constructor(
    private checklistService: ChecklistService,
    private domainService: DomainService,
    private messageService: MessageService,
    private positionService: PositionService,
    private translateService: TranslateService,
    private activatedRoute: ActivatedRoute,
    private router: Router
  ) { }

  ngOnInit() {
    this.initChecklistPageData();
    this.checkIfAppraisalIsFractioned();
  }

  initChecklistPageData() {
    this.inputModalObject = null; // utilizzato per il refresh della pagina
    this.inputModalObject2 = null; // utilizzato per il refresh della pagina
    Observable.forkJoin(
      this.checklistService.GetChecklistServicing(this.positionId),
      this.domainService.newGetDomain('UBZ_DOM_CATEGORY_TYPE'),
      this.domainService.newGetDomain('UBZ_DOM_RESITEM_TYPE'),
      this.domainService.newGetDomain('UBZ_DOM_CHK_ENTITY_TYPE'),
      this.domainService.newGetDomain('UBZ_DOM_STATUS')
    ).subscribe(res => {
      this.accordions = res[0];
      this.categoryTypeDom = res[1];
      this.assetTypeDom = res[2];
      this.entityTypeDom = res[3];
      this.statusDom = res[4];
      this.initAccordionState();
      this.checkIfComplete();
    });

    if (!this.isRequestId && typeof this.positionId !== 'undefined' && this.positionId) {

      this.positionService.getAppraisalInfo(this.positionId)
        .subscribe((res: any) => {
          if (typeof res.appraisal.bankCod !== 'undefined') {
            this.bankCod = res.appraisal.bankCod;
          }
          if (typeof res.appraisal.macroProcess !== 'undefined') {
            this.macroProcess = res.appraisal.macroProcess;
          }
          if (typeof res.requestId !== 'undefined' && res.requestId) {
            this.requestId = res.requestId;
          }
        });
    }
  }

  initAccordionState() {
    for (const accordion of this.accordions) {
      this.accordionsStatusOpen.push(false);
      for (const row of accordion.groups) {
        this.checkboxStatus[row.prog] = false;
        this.progToDocCodMap[row.prog] = row.documentCod
          ? row.documentCod
          : row.groupCod;
      }
    }
  }

  getAccordionStatusClass(accord: any) {
    let areAllDocumentValidated = 'Y';
    for (const row of accord.groups) {
      if (row.acquired !== 'Y' && row.flagMandatory === 'Y') {
        areAllDocumentValidated = 'N';
        break;
      }
    }
    return this.isValidClass[areAllDocumentValidated];
  }

  getRowClass(isAcquired: string, isMandatory: string) {
    if (isAcquired !== 'Y' && isMandatory === 'Y') {
      return this.isValidClass['N'];
    } else {
      return this.isValidClass['Y'];
    }
  }

  toggleAllAssetSelected() {
    this.allRowSelected = !this.allRowSelected;
    for (const ind in this.checkboxStatus) {
      if (true) {
        this.checkboxStatus[ind] = this.allRowSelected;
      }
    }
    if (this.allRowSelected === true) {
      for (const ind in this.accordionsStatusOpen) {
        if (true) {
          this.accordionsStatusOpen[ind] = true;
        }
      }
    }
    this.setRenderIcons();
  }

  setRenderIcons() {
    let print = false;
    let count = 0;
    for (const ind in this.checkboxStatus) {
      if (this.checkboxStatus.hasOwnProperty(ind)) {
        if (this.checkboxStatus[ind] === true && this.isDocumentAcquired(ind)) {
          print = true;
        }
        if (this.checkboxStatus[ind] === true) {
          count++;
        }
      }
    }
    setTimeout(() => {
      this.renderIconPrint = print;
    }, 0);
    if (count >= 2) {
      setTimeout(() => {
        this.renderIconUpload = true;
      }, 0);
    } else {
      setTimeout(() => {
        this.renderIconUpload = false;
      }, 0);
    }
  }

  private isDocumentAcquired(ind) {
    for (const accordion of this.accordions) {
      for (const row of accordion.groups) {
        if (row.prog == ind && row.acquired === 'Y') {
          return true;
        }
      }
    }
    return false;
  }

  setCheckboxStatus(prog: string) {
    this.checkboxStatus[prog] = !this.checkboxStatus[prog];
    this.setRenderIcons();
  }

  loadDocument(documentName: string, prog: number) {
    const pdf = ".pdf";
    if (documentName.toLowerCase().endsWith(pdf)) {
      this.displayLastUpload(prog);
    }
    else {
      this.disableDownload = true;
      this.messageService.showWarning(
        this.translateService.instant('UBZ.SITE_CONTENT.11111111100'), this.translateService.instant('UBZ.SITE_CONTENT.11110000000'));
    }
  }

  displayLastUpload(prog: number) {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.router.navigate(["/document", { prog }]);
    });
  }

  checkIfComplete() {
    this.checklistService.newAcquiredCheck(this.positionId).subscribe(x => {
      if (x.length === 0) {
        this.allPageIsValid = true;
      } else {
        this.allPageIsValid = false;
      }
      this.isComplete.emit(this.allPageIsValid);
    });
  }

  getStatusFormatted(mandatoryFor: string) {
    if (mandatoryFor) {
      const mfFormatted = mandatoryFor.replace(';', '');
      if (this.statusDom[mfFormatted]) {
        return this.statusDom[mfFormatted].translationCod;
      } else {
        return '';
      }
    } else {
      return '';
    }
  }

  checkIfAppraisalIsFractioned() {
    this.positionService.isAppraisalFractioned(this.positionId)
      .subscribe((resp: FractionedAppraisalResponse) => {
        if (resp && resp.frazCheck === true && resp.warning) {
          this.messageService.showWarning(
            resp.warning.message,
            this.translateService.instant('UBZ.SITE_CONTENT.11110000000'));
        }
      });
  }
}
