import {
  Component,
  Output,
  Input,
  EventEmitter,
  ElementRef,
  OnChanges,
  AfterViewChecked,
  SimpleChanges,
  Renderer2,
  On<PERSON>estroy
} from '@angular/core';

@Component({
  selector: 'app-migration-tab-manager',
  templateUrl: './migration-tab-manager.component.html',
  styleUrls: ['./migration-tab-manager.component.css']
})
export class MigrationTabManagerComponent
  implements AfterViewChecked, OnChanges, OnDestroy {
  @Input() assets: any[] = [];
  @Input() activeCategories;
  @Input() selectedAsset: any = {};
  @Input() isAppraisalConvalidated: boolean = false;
  @Output() onSelectAsset = new EventEmitter();

  layoutDone = false;
  shouldShowDropdown = true;
  hiddenAssets: any[];
  maxVisibleTabs = 0;

  private onResizeFunction: any;

  constructor(private elementRef: ElementRef, private _renderer: Renderer2) {
    this.onResizeFunction = _renderer.listen(
      'window',
      'resize',
      this.debounce(() => {
        this.layout();
      })
    );
  }

  private debounce(func: () => void, wait = 70) {
    let h: any;
    return () => {
      clearTimeout(h);
      h = setTimeout(() => func(), wait);
    };
  }

  ngAfterViewChecked() {
    if (!this.assets || this.layoutDone) {
      return;
    }
    setTimeout(() => this.layout(), 0);
  }

  ngOnChanges(changes: SimpleChanges) {
    // Se non è cambiato nulla devo ritornare
    setTimeout(() => this.layout(), 0);
  }

  ngOnDestroy() {
    this.onResizeFunction(); // Remove the listener on the resize event
  }

  // isAssetStateAvailable(asset): boolean {
  //   return asset && asset.assetDetail && asset.assetDetail.length > 0;
  // }

  getAssetAddress(asset): string {
    return `Indirizzo \n` +
      `${asset.address ? asset.address : 'N/A'}, ${asset.streetNum ? asset.streetNum : ''} \n` +
      `${asset.zipCode ? asset.zipCode : ''} ${asset.city ? asset.city : ''} ${asset.province ? asset.province : ''} \n` +
      `Foglio ${asset.realEstateInfo && asset.realEstateInfo.reRegistrySheet ? asset.realEstateInfo.reRegistrySheet : 'N/A'} \n` +
      `Mappale ${asset.realEstateInfo && asset.realEstateInfo.reRegistryPart ? asset.realEstateInfo.reRegistryPart : 'N/A'} \n` +
      `Subalterno ${asset.realEstateInfo && asset.realEstateInfo.reRegistrySub ? asset.realEstateInfo.reRegistrySub : 'N/A'}`;
  }

  /**
   * Appraisal is not convalidated (not PER-COV) so it doesn't have access to data from UAM 
   * such as stato && promiscuo => 'assetDetail' list is empty => read ID Asset from 'resiItemId'.
   * 
   * This template is also used when the asset does come form an convaldiated appraisal (PER-COV),
   * but the data is simply missing from UAM ('assetDetail' is an empty list).
   */
  // getMissingAssetStateTemplate(asset): string {
  //   return `ID Asset: ${asset.assetId ? asset.assetId : 'N/A'} \n` +
  //     `Stato asset: N/A \n` + `Asset promiscuo: N/A`;
  // }

  // getMultipleAssetState(asset): string {
  //   return asset.assetDetail.reduce((tooltip, assetDetail, index) => {
  //     return tooltip + this.reduceAssetStateToString(assetDetail) +
  //       (index !== asset.assetDetail.length - 1 ? '\n\n' : '');
  //   }, '');
  // }

  // getSingleAssetState(asset): string {
  //   if (asset.assetDetail && asset.assetDetail.length) {
  //     return this.reduceAssetStateToString(asset.assetDetail[0]);
  //   }
  // }

  // reduceAssetStateToString(assetDetail): string {
  //   const { assetId, disabledStatus, promiscuo } = assetDetail;
  //   return `ID Asset: ${assetId ? assetId : 'N/A'} \n` +
  //     `Stato asset: ${disabledStatus !== null ? (disabledStatus === true ? 'Disabilitato' : 'Attivo') : 'N/A'} \n` +
  //     `Asset promiscuo: ${promiscuo !== null && disabledStatus !== true ? (promiscuo === true ? 'Si' : 'No') : 'N/A'}`;
  // }

  triggerSelectAsset(asset) {
    if (asset !== this.selectedAsset) {
      this.onSelectAsset.emit(asset);
    }
  }

  // Calcola il numero massimo di tab visibili in base alla larghezza della
  // finestra, e nasconde i tab restanti dentro a un menu a tendina
  layout() {
    if (!this.assets) {
      return;
    }

    const domElement = this.elementRef.nativeElement.querySelector('.MyTabs');
    const tabContainerWidth = domElement.offsetWidth;
    let tabWidth = 160; // larghezza ideale di un tab
    const someTab = domElement.querySelector('.MyTabs__Tab');

    const firstTabWidth = domElement.querySelector('#first-tab').offsetWidth;
    const dropDownWidth = domElement.querySelector('#dropdown-button')
      .offsetWidth;

    if (someTab) {
      tabWidth = someTab.offsetWidth;
    }

    this.maxVisibleTabs = Math.floor(
      (tabContainerWidth - firstTabWidth - dropDownWidth) / tabWidth
    );
    this.layoutDone = true;
    this.shouldShowDropdown = this.assets.length > this.maxVisibleTabs;
  }
}
