import { Component, OnInit } from '@angular/core';

import { ConfigurationService } from '../../service/configuration.service';
import { DomainService } from 'app/shared/domain/domain.service';

@Component({
  selector: 'app-sample-checks-table',
  templateUrl: './sample-checks-table.component.html',
  providers : [ ConfigurationService ]
})
export class SampleChecksTableComponent implements OnInit {

  public page: any[] = [];
  // public valueDom: any = {};

  constructor(
    private configurationService: ConfigurationService,
    private domainService: DomainService
  ) { }

  ngOnInit() {
    this.retrivePage();
  }

  retrivePage() {
    this.configurationService.getSamplecheckTable().subscribe( x => {
      this.page = x;
    });
  }

  saveConfiguration() {
    this.configurationService.saveSamplecheckTable(this.page).subscribe( x => {
      this.retrivePage();
    });
  }

}
