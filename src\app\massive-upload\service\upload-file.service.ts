import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { Http, Response } from '@angular/http';
import { ToastsManager } from 'ng2-toastr/ng2-toastr';

@Injectable()
export class UploadFileService {
  UploadFileService(UploadFileService: any) {
    throw new Error('Method not implemented.');
  }

  constructor(private http: Http, public toaster: ToastsManager) {}
  getDataList(page:number,size:number): Observable<any> {
    return this.http
      .get(`/UBZ-ESA-RS/service/techLoadingService/v1/techLoading/${page}/${size}`)
       .map((resp: Response) => resp.json()); 
  }

  UploadDocument(file: any, selectedPhase: string, descrizione: string) {
    const url = `/UBZ-ESA-RS/service/techLoadingService/v1/techLoading`;
    let formData: FormData = new FormData();
    formData.append('uploadFile', file, file.name);
    formData.append('fileType', selectedPhase);
    formData.append('description', descrizione);
    return this.http.put(url, formData).map((resp: Response) => resp.json());
  }

  acquireUpload(
    fileType: string,
    description: string,
    loadId: number
  ): Observable<any> {
    const url = `/UBZ-ESA-RS/service/techLoadingService/v1/techLoading/flow`;
    return this.http
      .put(url, {
        fileType: fileType,
        description: description,
        loadId: loadId
      })
      .map((resp: Response) => resp.json());
  }

  getUploadDetail(
    loadId: string,
    page: number,
    pageSize: number
  ): Observable<any> {
    const input = {
      page: page,
      pageSize: pageSize
    };
    const url = `/UBZ-ESA-RS/service/techLoadingService/v1/techLoading/listAppraisal/${loadId}`;
    return this.http.post(url, input).map(res => res.json());
  }

  scheduleUpload(loadId: string, document: any): Observable<any> {
    const input = {
      externalId: document.externalId,
      appraisalId: document.appraisalId,
      requestId: document.requestId,
      docName: document.docName,
      docCategory: document.docCategory,
      docNum: document.docNum
    };
    const url = `/UBZ-ESA-RS/service/techLoadingService/v1/techLoading/${loadId}/schedule/upload`;
    return this.http.post(url, input).map(res => res.text());
  }

  getMaloEnergyHis(loadId: string, page: number, size: number): Observable<any> {
    const input = {
      page : page,
      pageSize : size
    };
    const url = `/UBZ-ESA-RS/service/techLoadingService/v1/techLoading/energy/${loadId}`;
    return this.http.post(url, input).map(res => res.json());
  }
}
