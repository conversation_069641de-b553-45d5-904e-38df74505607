<form #f="ngForm" (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
          {{'UBZ.SITE_CONTENT.1011010001' | translate }}
          <span class="state" [ngClass]="{'red': f.invalid, 'green': f.valid}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <table class="uc-table">
          <thead>
            <tr>
              <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.1011010010' | translate }}</th>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.101010010' | translate }}</th>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1011010011' | translate }}</th>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1011010100' | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let item of (model.mobAppExec | objectMapToObjectArray); let i = index">
              <tr *ngIf="expenseTypes && expenseTypes[item.costType]">
                <td>{{expenseTypes[item.costType].translationCod | translate}}</td>
                <td>
                  <app-importo [name]="'total-' + i" [required]="false" [(ngModel)]="model.mobAppExec[item.costType].totCost">
                  </app-importo>
                </td>
                <td>
                  <app-importo [name]="'alreadyTaken-' + i" [required]="false" [(ngModel)]="model.mobAppExec[item.costType].executedCost">
                  </app-importo>
                </td>
                <td>{{calculateDiff(item.costType) | currency:'EUR':true:'1.2-2'}}</td>
                <input type="hidden" name="toGive-{{i}}" [ngModel]="calculateDiff(item.costType)" appMinValue="0">
              </tr>
            </ng-container>
            <tr>
              <td>
                <strong>{{'UBZ.SITE_CONTENT.1111110100' | translate | uppercase}}</strong>
              </td>
              <td>{{calculateTotal() | currency:'EUR':true:'1.2-2' }}</td>
              <td>{{calculateAlreadyGiven() | currency:'EUR':true:'1.2-2' }}</td>
              <td>{{calculateToGive() | currency:'EUR':true:'1.2-2' }}</td>
            </tr>
          </tbody>
        </table>
        <div class="panel-box">
          <div class="row">
            <div class="col-sm-4 form-group">
              <label>{{'UBZ.SITE_CONTENT.**********' | translate }}</label>
              <td>
                <app-importo [name]="'percFis'" [required]="false" [(ngModel)]="model.physicalSalPerc">
                </app-importo>
              </td>
            </div>
            <div class="col-sm-4 form-group">
              <label>{{'UBZ.SITE_CONTENT.**********' | translate }}</label>
              <td>
                <app-importo [name]="'percCont'" [required]="false" [(ngModel)]="model.accountSalPerc">
                </app-importo>
              </td>
            </div>
          </div>
        </div>
      </div>
    </div>
  </accordion-group>
</form>
