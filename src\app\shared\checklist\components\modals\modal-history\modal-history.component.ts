import {
  Component,
  OnInit,
  EventEmitter,
  Input,
  Output,
  ViewChild
} from '@angular/core';
import { ChecklistService } from '../../../service/checklist.service';

import * as FileSaver from 'file-saver';

@Component({
  selector: 'app-modal-history',
  templateUrl: './modal-history.component.html',
  styleUrls: ['./modal-history.component.css']
})
export class ModalHistoryComponent implements OnInit {
  @Input() isOpen = false;
  @Input() document: any;
  @Input() section: any;
  @Input() positionId: string;
  @Input() requestId: string;
  @Input() isRequestId: boolean; 
  @Output() modalClose = new EventEmitter();
  isValidClass = { Y: 'state green', N: 'state red' };
  versions: any;
  public deleteConfirm = false;
  public selectedDocument: any;
  public refreshPage = false;

  constructor(private checklistService: ChecklistService) {}

  ngOnInit() {
    this.checklistService.getVersions(this.document.prog).subscribe(x => {
      this.versions = x;
    });
  }

  closeModal() {
    this.isOpen = false;
    this.modalClose.emit(this.refreshPage);
  }

  downloadDocument(doc) {
    this.checklistService.downloadDocument(this.isRequestId ? null : this.positionId, this.isRequestId ? this.positionId : this.requestId, 
      doc.prog, doc.versionId)
      .subscribe(file => {
        const content = file.header.get('content-disposition');
        FileSaver.saveAs(file.document, content);
      });
  }

  deleteDocument() {
    this.checklistService
    .deleteDocument(
      this.isRequestId ? null : this.positionId, 
      this.isRequestId ? this.positionId : this.requestId, 
        this.selectedDocument.prog,
        this.selectedDocument.versionId
      )
      .subscribe(x => {
        this.deleteConfirm = false;
        this.ngOnInit();
        this.refreshPage = true;
      });
  }
}
