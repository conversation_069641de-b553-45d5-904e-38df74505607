import { Component, OnInit, Input } from '@angular/core';
import { WizardDetailService } from '../../../../wizard-detail/services/wizard-detail.service';
import { Activity } from '../../model/activity';

@Component({
  selector: 'app-activity-history-table',
  templateUrl: './activity-history-table.component.html',
  styleUrls: ['./activity-history-table.component.css'],
  providers: [WizardDetailService]
})
export class ActivityHistoryTableComponent implements OnInit {
  @Input() positionId: string;
  activityHistory: Activity[] = [];

  constructor(private wizardDetailService: WizardDetailService) {}

  ngOnInit() {
    this.wizardDetailService
      .getActivityHistory(this.positionId)
      .subscribe(res => {
        this.activityHistory = res;
      });
  }
}
