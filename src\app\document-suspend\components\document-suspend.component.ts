import { Component, OnInit, Input, Output, EventEmitter, Inject } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ChecklistService } from '../../shared/checklist/service/checklist.service';
import { DomainService } from '../../shared/domain/domain.service';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { MessageService } from '../../shared/messages/services/message.service';
import { ModalButtonService } from '../../shared/drop-assignment-button/service/modal-button.service';
import { GenericTaskService } from '../../tasks/services/generic-task/generic-task.service';
import { PositionService } from '../../shared/position/position.service';
import { WizardDetailService } from '../../wizard-detail/services/wizard-detail.service';
import { APP_CONSTANTS, IAppConstants } from '../../app.constants';


@Component({
  selector: 'app-document-suspend',
  templateUrl: './document-suspend.component.html',
  styleUrls: ['./document-suspend.component.css']
})
export class DocumentSuspendComponent implements OnInit {
  @Output() isComplete = new EventEmitter();

  public positionId: string;
  canInvalidate = false;
  accordions: any[];
  allPageIsValid = true;
  allRowSelected = false;
  isValidClass = { Y: 'state green', N: 'state red' };
  checkboxStatus = {};
  progToDocCodMap: string[] = [];
  accordionsStatusOpen: boolean[] = [];
  entityTypeDom: any[];
  assetTypeDom: any[];
  categoryTypeDom: any[];
  statusDom: any[];
  wizardCode = this.constants.wizardCodes['PER'];
  originationProcess = '';
  opinionType = '';

  constructor(
    private checklistService: ChecklistService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private modalButtonService: ModalButtonService,
    private messageService: MessageService,
    private translateService: TranslateService,
    private positionService: PositionService,
    public genericTaskService: GenericTaskService,
    private domainService: DomainService,
    private wizardDetailService: WizardDetailService,
    @Inject(APP_CONSTANTS) public constants: IAppConstants
  ) {}

  ngOnInit() {
    this.activatedRoute.params
      .subscribe((params: Params) => {
        this.positionId = params['positionId'];
        Observable.forkJoin(
          this.checklistService.newGetChecklist(this.positionId),
          this.domainService.newGetDomain('UBZ_DOM_CATEGORY_TYPE'),
          this.domainService.newGetDomain('UBZ_DOM_RESITEM_TYPE'),
          this.domainService.newGetDomain('UBZ_DOM_CHK_ENTITY_TYPE'),
          this.domainService.newGetDomain('UBZ_DOM_STATUS'),
          this.positionService.getAppraisalInfo(this.positionId)
        ).subscribe(res => {
          //keep only object of RIC list
          let rawDocumentsList = res[0];
          for (let i = (rawDocumentsList.length - 1); i >= 0; i--) {
            if (!rawDocumentsList[i]['entityType'] || rawDocumentsList[i]['entityType'] !== 'RIC') {
              rawDocumentsList.splice(i, 1);
            }
          }
          this.accordions = rawDocumentsList;
          this.categoryTypeDom = res[1];
          this.assetTypeDom = res[2];
          this.entityTypeDom = res[3];
          this.statusDom = res[4];
          this.originationProcess = res[5]['appraisal']['originProcess'];
          this.opinionType = res[5]['opinionType'];

          this.wizardDetailService.getOpinionTypes().subscribe(res => {
            let opinionTypes = res;
            if (
              this.opinionType &&
              opinionTypes[this.opinionType]
            ) {
              this.opinionType = opinionTypes[
                this.opinionType
              ].translationCod;
            }
          });
          this.initialize();
        });
      });
  }

  initialize() {
    for (const accordion of this.accordions) {
      this.accordionsStatusOpen.push(true);
      for (const row of accordion.groups) {
        this.checkboxStatus[row.prog] = false;
        this.progToDocCodMap[row.prog] = row.documentCod
          ? row.documentCod
          : row.groupCod;
      }
    }
  }

  getAccordionStatusClass(accord: any) {
    let areAllDocumentValidated = 'Y';
    for (const row of accord.groups) {
      if (row.acquired !== 'Y' && row.flagMandatory === 'Y') {
        areAllDocumentValidated = 'N';
        break;
      }
    }
    return this.isValidClass[areAllDocumentValidated];
  }

  getRowClass(isAcquired: string, isMandatory: string) {
    if (isAcquired !== 'Y' && isMandatory === 'Y') {
      return this.isValidClass['N'];
    } else {
      return this.isValidClass['Y'];
    }
  }

  toggleAllAssetSelected() {
    this.allRowSelected = !this.allRowSelected;
    for (const ind in this.checkboxStatus) {
      if (true) {
        this.checkboxStatus[ind] = this.allRowSelected;
      }
    }
    if (this.allRowSelected === true) {
      for (const ind in this.accordionsStatusOpen) {
        if (true) {
          this.accordionsStatusOpen[ind] = true;
        }
      }
    }
    this.checkCanInvalidate();
  }

  private isDocumentAcquired(ind) {
    for (const accordion of this.accordions) {
      for (const row of accordion.groups) {
        if (row.prog == ind && row.acquired === 'Y') {
          return true;
        }
      }
    }
    return false;
  }

  setCheckboxStatus(prog: string) {
    this.checkboxStatus[prog] = !this.checkboxStatus[prog];
    this.checkCanInvalidate();
  }

  openCloseAccordion(index) {
    this.accordionsStatusOpen[index] = !this.accordionsStatusOpen[index];
  }

  getStatusFormatted(mandatoryFor: string) {
    if (mandatoryFor) {
      const mfFormatted = mandatoryFor.replace(';', '');
      if (this.statusDom[mfFormatted]) {
        return this.statusDom[mfFormatted].translationCod;
      } else {
        return '';
      }
    } else {
      return '';
    }
  }

  checkMandatory(mandatoryFor: string) {
    if (mandatoryFor) {
      const mfFormatted = mandatoryFor.replace(';', '');
      if (this.statusDom[mfFormatted]) {
        return true;
      }
    }
    return false;
  }

  checkCanInvalidate() {
    let atLeastOneChecked = false;
    for (const accordion of this.accordions) {
      for (const row of accordion.groups) {
        if (this.checkboxStatus[row.prog] === true) atLeastOneChecked = true;
      }
    }
    if (atLeastOneChecked) this.canInvalidate = true;
    else this.canInvalidate = false;
  }

  invalidDocuments() {
    let documentsToInvalidate: string[] = [];
    for (const accordion of this.accordions) {
      for (const row of accordion.groups) {
        if (this.checkboxStatus[row.prog] === true) documentsToInvalidate.push(row.prog);
      }
    }
    let storedData = this.modalButtonService.getStoredData();
    if (!storedData['note'] || !storedData['closeTask']) {
      return this.showError('UBZ.SITE_CONTENT.10011011100');
    }
    if (!storedData['note']['positionId'] ||  storedData['note']['positionId'] !== this.positionId) {
      return this.showError('UBZ.SITE_CONTENT.10011011100');
    }

    this.modalButtonService.suspendAppraisalInvalidateDocs(storedData['note'], storedData['closeTask'], documentsToInvalidate).subscribe(response => {
      if (response) {
        this.messageService.showSuccess(
          this.translateService.instant('UBZ.SITE_CONTENT.1001100000'),
          this.translateService.instant('UBZ.SITE_CONTENT.1001100001')
        );
        this.router.navigateByUrl(
          'index'
        );
      }
    });
  }

  showError(errString: string) {
    this.messageService.showSuccess(
      this.translateService.instant(errString),
      this.translateService.instant('UBZ.SITE_CONTENT.10011010100')
    );
  }

  goBack() {
    this.router.navigateByUrl(
      'generic-task/' + this.positionId + '/-/-'
    );
  }
}
