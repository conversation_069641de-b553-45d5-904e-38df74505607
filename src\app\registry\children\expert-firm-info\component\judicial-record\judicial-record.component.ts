import {
  Component,
  OnChang<PERSON>,
  Input,
  <PERSON><PERSON><PERSON><PERSON>,
  SimpleChang<PERSON>
} from '@angular/core';
import { Subscription } from 'rxjs/Subscription';
import { RegistryService } from '../../../../service/registry.service';
import { JudicialRecord } from '../../../../model/registry.models';

@Component({
  selector: 'app-judicial-record',
  templateUrl: './judicial-record.component.html',
  styleUrls: ['./judicial-record.component.css']
})
export class JudicialRecordComponent implements OnChanges, OnDestroy {
  public modify = false;
  @Input() public anagId: string;
  public judicialRecord: JudicialRecord = new JudicialRecord();
  public judicialRecordExists: boolean;
  private _subscriptions: Subscription[] = [];

  constructor(private _registryService: RegistryService) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes && this.anagId) {
      this.refreshJudicialRecord();
    }
  }

  ngOnDestroy() {
    for (const subscription of this._subscriptions) {
      if (subscription && !subscription.closed) {
        subscription.unsubscribe();
      }
    }
  }

  private refreshJudicialRecord() {
    this._subscriptions[0] = this._registryService
      .getJudicialRecord(this.anagId)
      .subscribe(res => {
        this.parseResponse(res);
        this.checkIfRecordExists();
      });
  }

  private parseResponse(httpResponse: any) {
    this.judicialRecord = httpResponse;
    this.judicialRecord.expiryDate = this.judicialRecord.expiryDate
      ? new Date(this.judicialRecord.expiryDate)
      : null;
    this.judicialRecord.releaseDate = this.judicialRecord.releaseDate
      ? new Date(this.judicialRecord.releaseDate)
      : null;
  }

  private checkIfRecordExists() {
    if (
      this.judicialRecord.expiryDate ||
      this.judicialRecord.releaseDate ||
      this.judicialRecord.numJudReg
    ) {
      this.judicialRecordExists = true;
    } else {
      this.judicialRecordExists = false;
    }
  }

  public cancelModify(event: any): void {
    event.stopPropagation();
    this.modify = false;
  }

  public saveData(event: any): void {
    event.stopPropagation();
    const toSave: JudicialRecord = JSON.parse(
      JSON.stringify(this.judicialRecord)
    );
    toSave.expiryDate = Date.parse(toSave.expiryDate);
    toSave.releaseDate = Date.parse(toSave.releaseDate);
    this._subscriptions[1] = this._registryService
      .saveJudicialRecord(this.anagId, toSave)
      .subscribe(res => {
        this.refreshJudicialRecord();
        this.modify = false;
      });
  }

  public startModify(event: any): void {
    event.stopPropagation();
    this.modify = true;
  }
}
