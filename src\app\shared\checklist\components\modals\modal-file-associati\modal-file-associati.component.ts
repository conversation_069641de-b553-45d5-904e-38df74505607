import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  Inject
} from '@angular/core';
import { ChecklistService } from '../../../service/checklist.service';
import { APP_CONSTANTS, IAppConstants } from '../../../../../app.constants';
import { PropertiesService } from 'app/shared/properties/properties.service';
import { MessageService } from 'app/shared/messages/services/message.service';

@Component({
  selector: 'app-modal-file-associati',
  templateUrl: './modal-file-associati.component.html',
  styleUrls: ['./modal-file-associati.component.css']
})
export class ModalFileAssociatiComponent implements OnInit {
  @ViewChild('fileToUpload') fileToUpload: any;
  fileName = '';
  sizeLimit: number;

  @Input() isOpen = false;
  @Input() positionId: string;
  @Input() requestId: string;
  @Input() documentToUploadMap: any;
  @Input() progToDocCodMap: any;
  @Output() modalClose = new EventEmitter();

  constructor(
    private checklistService: ChecklistService,
    private propertiesService: PropertiesService,
    private messageService: MessageService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) {}

  ngOnInit() {
    this.propertiesService.getProperty('UBZ', 'max.file.size').subscribe(x => {
      this.sizeLimit = Number(x);
    });
  }

  closeModal(refreshPage: boolean) {
    this.isOpen = false;
    this.modalClose.emit(refreshPage);
  }

  setFileName(fileName?: string) {
    if (!fileName && fileName !== '') {
      fileName = this.fileToUpload.nativeElement.files[0].name;
    }
    this.fileName = fileName;
  }

  uploadFile() {
    let id;
    if (this.requestId) {
      id = this.requestId;
    } else {
      id = this.positionId;
    }
    const uploadRequest = {
      positionId: `${this.constants.processCode}-` + id,
      documents: [],
      content: null
    };
    for (const prog in this.documentToUploadMap) {
      if (this.documentToUploadMap[prog] === true) {
        uploadRequest.documents.push({
          prog: prog,
          positionId: `${this.constants.processCode}-` + id,
          documentName: this.fileToUpload.nativeElement.files[0].name,
          documentCod: this.progToDocCodMap[prog]
        });
      }
    }
    this.checklistService
      .newUploadDocument(
        this.fileToUpload.nativeElement.files[0],
        uploadRequest
      )
      .subscribe(() => {
        this.closeModal(true);
      });
  }

  setFile() {
    if (this.fileToUpload.nativeElement.files[0].size > this.sizeLimit) {
      this.fileToUpload.nativeElement.files = null;
      this.messageService.showError(
        `LA DIMENSIONE DEL FILE NON PUO\' SUPERARE I ${this.sizeLimit /
          1048576} MEGABYTE`,
        'DIMENSIONE FILE NON CONSENTITA'
      );
      this.setFileName('');
    } else {
      this.setFileName();
    }
  }
}
