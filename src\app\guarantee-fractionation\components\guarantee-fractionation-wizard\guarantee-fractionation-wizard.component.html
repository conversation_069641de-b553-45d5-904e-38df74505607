<div class="row">
  <section id="wizard-progress">
    <ng-container *ngFor="let el of labels; let i = index; let l = last">
      <div class="page " [ngClass]="{'current': ( i + 1 === _stepActive ) , 'incomplete': ( i + 1 > _stepActive ) , 'complete': ( i + 1 < _stepActive ) }">
        <a id="step{{ i + 1 }}" role="button" (click)="clickOnCounter(i+1)" >
          <span class="progress-badge waves-effect waves-progress">
      			<ng-container *ngIf="!l">
              {{i +1 }}
            </ng-container>
            <i *ngIf="l" class="icon-check"></i>
          </span>
          <p>{{ el | translate }}</p>
          <span *ngIf="!l" class="sep"></span>
        </a>
      </div>
    </ng-container>
  </section>
</div>

<div *ngIf="modalIsOpen" class="modal fade" id="nuovo-asset" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #modal="bs-modal" (onHidden)="closeModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.1100010' | translate }}</h2>
        <button type="button" role="button" class="close pull-right" aria-label="Close" (click)="closeModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <p>{{'UBZ.SITE_CONTENT.10000000100' | translate }}.</p>
        <p>{{'UBZ.SITE_CONTENT.1111110110' | translate }}?</p>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" (click)="confirmUndo()">
          {{'UBZ.SITE_CONTENT.110101000' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>
