import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';
import { AssetObj } from '../../model/asset-obj';
import { Observable } from 'rxjs/Observable';

import 'rxjs/add/operator/toPromise';
import { AssetSearch } from '../..';

@Injectable()
export class AssetService {
  // Usato per tenere traccia di quando un asset viene importato o creato in modo
  // che sia editabile quando si atterra nella pagina della lista
  firstTimeAssets: number[] = [];
  familyAssetType: string;
  listObjcet: AssetObj[] = [];
  saveIsEnable = true;
  public assetsSelectedWithCheckbox: any = {};
  urlMap = {
    WSIM: '/UBZ-ESA-RS/service/simulation/v1/simulationObjects/',
    WRPE: '/UBZ-ESA-RS/service/reqPer/v1/reqAppraisalObjects/'
  };
  updateUrlMap = {
    WSIM:
      '/UBZ-ESA-RS/service/simulation/v1/simulationObjects/#ASSET_ID#/onSimulation/#SIMULATION_ID#',
    WRPE:
      '/UBZ-ESA-RS/service/reqPer/v1/reqAppraisalObjects/#ASSET_ID#/onRequest/#SIMULATION_ID#'
  };
  public showRestrictionsCheckboxes: boolean;

  constructor(private http: Http) {}

  getAssetList(simulationId: string, wizardCode: string): Observable<any> {
    const url = this.urlMap[wizardCode] + simulationId;
    return this.http.get(url).map((resp: Response) => {
      const result = resp.json();
      this.familyAssetType = result.familyAssetType;
      this.listObjcet = result.listObjcet;
      if (this.listObjcet.length === 0) {
        this.saveIsEnable = false;
      } else {
        this.saveIsEnable = true;
      }
      return result;
    });
  }

  saveAssetChanges(
    apfmap: Object,
    simulationId: string,
    assetId = null,
    wizardCode: string
  ): Observable<any> {
    assetId = encodeURIComponent(assetId);
    simulationId = encodeURIComponent(simulationId);
    const url = this.updateUrlMap[wizardCode]
      .replace('#ASSET_ID#', assetId)
      .replace('#SIMULATION_ID#', simulationId);
    return this.http.put(url, apfmap).map((resp: Response) => resp.text());
  }

  deleteAsset(assetId: number, wizardCode: string, simulationId: string) {
    simulationId = encodeURIComponent(simulationId);
    const url = this.updateUrlMap[wizardCode]
      .replace('#ASSET_ID#', assetId)
      .replace('#SIMULATION_ID#', simulationId);
    return this.http.delete(url).map((resp: Response) => resp.text());
  }

  createAsset(
    model: Object,
    page: string,
    idSimulation: string,
    wizardCode: string
  ) {
    const url = this.urlMap[wizardCode];
    const input = {
      apfmap: { page: page, apfmap: model },
      idSimulation: idSimulation
    };
    return this.http.post(url, input).map((resp: Response) => resp.text());
  }

  // (servizio ricerca asset - non in barra di ricerca nè ricerca avanzata)
  // ‘deletedAsset: boolean controlla se restituire ache asset cancellati
  findAsset(input: AssetSearch) {
    input.deletedAsset = false;
    const url = '/UAM-ESA-RS/service/asset/v1/searchAsset';
    return this.http.post(url, input).map((resp: Response) => resp.json());
  }

  importAssets(idSimulazione: string, assetsId: number[], wizardCode: string) {
    const url = this.urlMap[wizardCode] + 'importAsset';
    const input = {
      idSimulation: idSimulazione,
      assetsId: assetsId
    };
    this.firstTimeAssets = this.firstTimeAssets.concat(assetsId);
    return this.http.post(url, input).map((resp: Response) => resp.text());
  }

  verifyAppraisalRequest(positionId: string) {
    positionId = encodeURIComponent(positionId);
    const url =
      '/UBZ-ESA-RS/service/reqPer/v1/reqAppraisal/' +
      positionId +
      '/verifyReqPer';
      return this.http.get(url).map((resp: Response) => resp.json());
      
  }

  checkAssetsStatus(positionId: string) {
    positionId = encodeURIComponent(positionId);
    const url =
      '/UBZ-ESA-RS/service/reqPer/v1/reqAppraisals/' +
      positionId +
      '/assets/working';
    return this.http.post(url, {}).map(res => res.json());
  }
  
  checkAssetsCanBeSaved(positionId: string) {
    positionId = encodeURIComponent(positionId);
    const url =
      '/UBZ-ESA-RS/service/reqPer/v1/reqAppraisals/' +
      positionId +
      '/assets/valid';
    return this.http.post(url, {}).map(res => res.text());
  }  

  public saveRestrictionObjects(appraisalId: string): Observable<any> {
    const url = `/UBZ-ESA-RS/service/reqPer/v1/reqAppraisalObjects/${appraisalId}/restriction`;
    const toSave: any = {};
    const restrictionsArray = [];
    toSave['listOfObjectRestricted'] = restrictionsArray;
    for (const assetId in this.assetsSelectedWithCheckbox) {
      if (
        this.assetsSelectedWithCheckbox.hasOwnProperty(assetId) &&
        this.assetsSelectedWithCheckbox[assetId]
      ) {
        restrictionsArray.push(assetId);
      }
    }
    return this.http.post(url, toSave).map(res => res.json());
  }
}
