import {
  Component,
  Inject,
  Input,
  OnInit,
  QueryList,
  ViewChildren
} from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { TranslateService } from '@ngx-translate/core';
import { ExternalMortageService } from '../../service/external-mortage.service';
import { Domain } from '../../../shared/domain/domain';
import { DomainService } from '../../../shared/domain/domain.service';
import { NgForm } from '@angular/forms';
import { AccordionAPFService } from '../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { AccordionApplicationFormComponent } from '../../../shared/application-form/components/accordion-application-form/accordion-application-form.component';
import { MessageService } from '../../../shared/messages/services/message.service';


@Component({
  selector: 'app-external-mortage-process',
  templateUrl: './external-mortage-process.component.html',
  styleUrls: ['./external-mortage-process.component.css'],
  providers: [ExternalMortageService, AccordionAPFService]
})

export class ExternalMortageProcessComponent implements OnInit {

  public requestId: string;
  public requestData: any;
  public calculatedValues: number[] = [];
  public isValid = [];
  public isAllValid = false;
  public canAllValid = false;
  public resItemTypeDomain = {};
  public categoryDomains = {};
  saveDraftCallback = this.saveDraft.bind(this);

  constructor(
    private _externalMortageService: ExternalMortageService,
    private _activatedRoute: ActivatedRoute,
    public _accordionAPFService: AccordionAPFService,
    private _router: Router,
    private _translateService: TranslateService,
    private _messageService: MessageService,
    private _domainService: DomainService,
  ) { }

  ngOnInit() {
    this._activatedRoute.params
      .switchMap((params: Params) => {
        this.requestId = params['requestId'];
        return this._externalMortageService.getAnacreditRequestData(
          this.requestId
        );
      })
      .subscribe(res => {
        //get data
        this.requestData = res;
        //check is valid
        this.checkIsValid();
        //find needed domains
        let neededCategoryDomains = [];
        this.requestData['collaterals'].forEach((collateral, index) => {
          collateral['assets'].forEach((asset, assetIndex) => {
            this.calculatedValues[index] += asset['antergateSplittedValue'];
            if (asset['familyAsset'] && neededCategoryDomains.indexOf(asset['familyAsset']) === -1) {
              neededCategoryDomains.push(asset['familyAsset']);
            }
          });
        });
        //prepare forkJoin
        let observableBatch = [];
        observableBatch.push(this._domainService.newGetDomain('UBZ_DOM_RESITEM_TYPE'));
        neededCategoryDomains.forEach((element, key) => {
          observableBatch.push(this._domainService.newGetDomain('UBZ_DOM_CATEGORY_TYPE', neededCategoryDomains[key], true));
        });
        //load domains
        Observable.forkJoin(observableBatch)
         .subscribe(domResponse => {
          this.resItemTypeDomain = domResponse[0];
          neededCategoryDomains.forEach((element, key) => {
            this.categoryDomains[neededCategoryDomains[key]] = domResponse[key+1];
          });
        });
      });
  }

  checkIsValid() {
    this.isAllValid = false;
    this.canAllValid = true;
    this.requestData['collaterals'].forEach((collateral, index) => {
      this.calculatedValues[index] = 0;
      collateral['assets'].forEach((asset, assetIndex) => {
        this.calculatedValues[index] += asset['antergateSplittedValue'];
      });
      if (this.calculatedValues[index] === collateral['antergateValue']) this.isValid[index] = true;
      else {
        this.isValid[index] = false;
        this.canAllValid = false;
      }
    });
    if (this.canAllValid) this.isAllValid = true;
  }

  save() {
    if (!this.isAllValid) {
      this._messageService.showError(
        this._translateService.instant('UBZ.SITE_CONTENT.10011010101'),
        this._translateService.instant('UBZ.SITE_CONTENT.10011010100')
      );
      return;
    }
    this.requestData['isDraft'] = false;
    this._externalMortageService
    .saveAnacreditRequestData(this.requestId, this.requestData)
    .subscribe(res => {
      this._messageService.showSuccess(
        this._translateService.instant('UBZ.SITE_CONTENT.10011010111'),
        this._translateService.instant('UBZ.SITE_CONTENT.10011010110')
      );
      this.goToExternalMortagesList();
    });
  }

  goToExternalMortagesList() {
    this._router.navigateByUrl(
      'external-mortage'
    );
  }

  saveDraft(): Observable<any> {
    this.requestData['isDraft'] = true;
    return this._externalMortageService
    .saveAnacreditRequestData(this.requestId, this.requestData)
    .switchMap(res => {
      return Observable.of(true);
    });
  }
}