import { Directive, Input } from '@angular/core';
import { Validator, NG_VALIDATORS, AbstractControl, ValidatorFn } from '@angular/forms';

@Directive({
  selector: '[appMaxValue]',
  providers: [{provide: NG_VALIDATORS, useExisting: MaxValidatorDirective, multi: true}]
})
export class MaxValidatorDirective implements Validator {
  @Input() appMaxValue: number;

  constructor() { }

  validate(control: AbstractControl): {[key: string]: any} {
    return this.appMaxValue ? this.maxValueValidator(this.appMaxValue)(control) : null;
  }

  private maxValueValidator(max: number): ValidatorFn {
    return (c: AbstractControl): { [key: string]: boolean } | null => {
      if (c.value && (isNaN(c.value) || Number(c.value) > Number(max))) {
        return {'maxValue': true};
      }
      return null;
    };
  }
}
