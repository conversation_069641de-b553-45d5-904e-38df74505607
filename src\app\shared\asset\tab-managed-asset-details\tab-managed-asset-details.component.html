<ng-container *ngIf="isAppraisalConvalidated && isAssetStateAvailable(); else missingAssetState">

  <!-- Appraisal is in (PER-COV || PRZ-COV) and asset state from UAM is available -->
  <ng-container *ngIf="!isMultipleAssetPerObject; else multipleAssetPerObject">
    <span data-placement="bottom" data-toggle="tooltip" [title]="getSingleAssetState()">
      {{ assetDetail[0].assetId ? assetDetail[0].assetId : 'N/A' }}
    </span>
  </ng-container>

  <ng-template #multipleAssetPerObject>
    <span data-placement="bottom" data-toggle="tooltip" [title]="getMultipleAssetState()">
      <i class="fa fa-plus-square-o" aria-hidden="true"></i>
    </span>
  </ng-template>

</ng-container>

<!-- Appraisal is NOT in (PER-COV || PRZ-COV) or asset state from UAM is missing -->
<ng-template #missingAssetState>
  <span data-placement="bottom" data-toggle="tooltip" [title]="getMissingAssetStateTemplate()">
    {{ resItemId ? resItemId : 'N/A' }}
  </span>
</ng-template>