import { Component, OnInit, OnDestroy, Input, Inject } from '@angular/core';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { PropertiesService } from '../../../../../shared/properties/properties.service';
import { IAppConstants, APP_CONSTANTS } from '../../../../../app.constants';
@Component({
  selector: 'app-renovation-manufactured',
  templateUrl: './renovation-manufactured.component.html',
  styleUrls: ['./renovation-manufactured.component.css']
})
export class RenovationManufacturedComponent implements OnInit, OnDestroy {
  @Input() positionId: string;
  @Input() pageContent: any[];
  @Input() appraisalType: string;
  @Input() haveDisabledFields: boolean;
  @Input() haveDisabledFieldsSpecial: boolean;
  pageIsValid = false;

  rowIsModified = [
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false,
    false
  ];
  totDistributionPerc = 0;
  totDistributionAmount = 0;
  threshold: number;
  private _subscription;

  constructor(
    public _accordionAPFService: AccordionAPFService,
    private _propertyService: PropertiesService,
    @Inject(APP_CONSTANTS) public _constant: IAppConstants
  ) {}

  ngOnInit() {
    for (const ind in this.pageContent) {
      if (true) {
        this.checkIfDistributionPercIsChanged(ind);
      }
    }
    this.getTotDistributionPerc();
    this.getTotDistributionAmount();
    if (this.appraisalType === 'FLA') {
      this._subscription = this._propertyService
        .getProperty('UBZ', 'sal.end.threshold')
        .subscribe(res => {
          this.threshold = Number.parseFloat(res);
        });
    }
    this.checkIfPageIsComplete();
  }

  ngOnDestroy() {
    if (this._subscription) {
      this._subscription.unsubscribe();
    }
  }

  submitForm() {}

  onChangeDistributionPerc(ind) {
    this.checkIfDistributionPercIsChanged(ind);
    this.getTotDistributionPerc();
  }

  onChangeDistributionAmount() {
    this.getTotDistributionAmount();
  }

  private checkIfDistributionPercIsChanged(ind) {
    if (
      this.pageContent[ind].distributionPerc !==
      this.pageContent[ind].histDistributionPerc
    ) {
      this.rowIsModified[ind] = true;
    } else {
      this.rowIsModified[ind] = false;
    }
  }

  private getTotDistributionPerc() {
    this.totDistributionPerc = 0;
    for (const el of this.pageContent) {
      const num = Number(el.distributionPerc);
      this.totDistributionPerc = +(num + this.totDistributionPerc).toFixed(12);
    }
    this.checkIfPageIsComplete();
  }

  private getTotDistributionAmount() {
    this.totDistributionAmount = 0;
    for (const el of this.pageContent) {
      this.totDistributionAmount += Number(el.distributionAmount);
    }
  }

  private checkIfPageIsComplete() {
    if (this.totDistributionPerc === 100) {
      this.pageIsValid = true;
    } else {
      this.pageIsValid = false;
    }
    if (!this.pageIsValid) {
      return;
    }
    if (this.appraisalType === 'FLA') {
      for (const item of this.pageContent) {
        if (item.updatePerc < this.threshold) {
          this.pageIsValid = false;
          return;
        }
      }
      this.pageIsValid = true;
    }
  }
}
