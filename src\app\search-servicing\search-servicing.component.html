<section id="page-content-wrapper">
  <div class="container-fluid">
    <div>
      <div class="row icons">
        <div class="col-sm-12 form-group section-headline">
          <h1><i class="icon-ricerca-servicing"></i> {{'UBZ.SITE_CONTENT.11111111011' | translate}}</h1>
          <h2>{{'UBZ.SITE_CONTENT.1001011' | translate}}</h2>
        </div>
      </div>

      <form [formGroup]="servicingForm" style="margin: 2%;">
        <div class="row forms">
          <div class="col-sm-6 row search-subgroup" formGroupName="search">
            <div class="col-sm-6 form-group">
              <label>{{'UBZ.SITE_CONTENT.11111111010' | translate }}</label>
              <input type="text" class="form-control" id="ndg"
                placeholder="{{'UBZ.SITE_CONTENT.110001101' | translate }}" formControlName="ndg" [acceptEnter]="true"
                appOnlyNumbers maxLength="16" required />
            </div>
            <div class="col-sm-6 form-group">
              <label>{{'UBZ.SITE_CONTENT.10100100' | translate }}</label>
              <input type="text" class="form-control" id="warranty" formControlName="warranty" [acceptEnter]="true"
                appOnlyNumbers placeholder="{{'UBZ.SITE_CONTENT.11111000000' | translate }}" maxlength="3">
            </div>
          </div>

          <div class="col-sm-1 col-sm-1">
            <label></label>
            <button type="submit" class="btn btn-primary" [disabled]="this.servicingForm.get('search').invalid"
              (click)="searchForServicing()">{{'UBZ.SITE_CONTENT.10010111' | translate }}</button>
          </div>
        </div>
      </form>
    </div>
  </div>
  <hr class="line">


  <div role="tabpanel" class="tab-pane active" id="perizie">

    <ng-container *ngIf="searchPageService.advancedSearch['count'] > 0">
      <div class="table-responsive">
        <table class="Search__ResultsTable table table-hover">
          <thead>
            <tr>
              <th scope="col">{{'UBZ.SITE_CONTENT.10010001' | translate }}</th>
              <th scope="col">{{'UBZ.SITE_CONTENT.10100101' | translate }}</th>
              <th scope="col">{{'UBZ.SITE_CONTENT.1001110' | translate }}</th>
              <th scope="col">{{'UBZ.SITE_CONTENT.1101101' | translate }}</th>
              <th scope="col">{{'UBZ.SITE_CONTENT.10000001' | translate }}</th>
              <th scope="col">{{'UBZ.SITE_CONTENT.10011001110' | translate }}</th>
              <th scope="col">{{'UBZ.SITE_CONTENT.10011000100' | translate }}</th>
              <th scope="col">{{'UBZ.SITE_CONTENT.1110010' | translate }}</th>
              <th scope="col">{{'UBZ.SITE_CONTENT.1110001' | translate }}</th>
              <th scope="col">{{ 'UBZ.SITE_CONTENT.11110001001' | translate }}</th>
              <th scope="col">{{'UBZ.SITE_CONTENT.11110011' | translate }}</th>
              <th scope="col"></th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let row of searchPageService.advancedSearch['searchResults']">
              <td data-label="A">{{row.appraisalId}}</td>
              <td data-label="A">{{row.id}}</td>
              <td data-label="B">{{row.ndg}}</td>
              <td data-label="C">{{row.heading}}</td>
              <td data-label="E">{{ (statusPhase && statusPhase[row.phaseApp + row.statusApp]) ?
                (statusPhase[row.phaseApp
                + row.statusApp].translationStatusCod | translate) : ''}}</td>
              <td data-label="F">{{row.insertDateApp ? ((row.insertDateApp | date: 'dd-MM-y') + ' ' + (row.insertDateApp
                | customTime)) : ''}}</td>
              <td data-label="G">{{(macroProcessDomain && macroProcessDomain[row.macroprocess]) ?
                (macroProcessDomain[row.macroprocess].translationCod
                | translate) : ''}}</td>
              <td data-label="H" *ngIf="appraisalTypeDomain && appraisalTypeDomain[row.appraisalType]">
                {{appraisalTypeDomain[row.appraisalType].translationCod | translate}}
              </td>
              <td data-label="H" *ngIf="appraisalTypeDomain && !appraisalTypeDomain[row.appraisalType]">
                {{row.appraisalType}}
              </td>
              <td data-label="I">{{(scopeTypeDomain && scopeTypeDomain[row.finOperation]) ?
                (scopeTypeDomain[row.finOperation].translationCod
                | translate) : ''}}</td>
              <td data-label="M">{{row.totalAssets}} </td>
              <td data-label="L">
                <button class="btn btn-clean waves-effect waves-secondary" type="button"
                  (click)="openCollateralModal(row)">
                  <i class="icon-plus_open_lightbox" aria-hidden="true"></i>
                </button>
                <span class="align-middle">{{getCollateralIds(row)}}</span>
              </td>
              <ng-container *appAuthKey="'UBZ_ADVANCED.SEARCH_GO.DETAILS'">
                <td data-label="">
                  <a role="button" (click)="goToDetailsPage(row.appraisalId) ; $event.stopPropagation()">
                    <i class="icon-angle-double-right" aria-hidden="true"></i>
                  </a>
                </td>
              </ng-container>
            </tr>
          </tbody>
        </table>
      </div>
    </ng-container>
    <ng-container *ngIf="!showResults && searchPageService.advancedSearch['count'] === 0">
      <div class="Search__NoResults">
        <div class="Search__NoResults__Icon">
          <i class="icon-search" aria-hidden="true"></i>
        </div>
        <div class="Search__NoResults__Text">
          <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1001011001' | translate }}</h3>
          <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011010' | translate }}</p>
        </div>
      </div>
    </ng-container>
  </div>

  <div class="row"
    *ngIf="searchPageService && searchPageService.advancedSearch && searchPageService.advancedSearch['count'] && (searchPageService.advancedSearch['count'] > 10)">
    <div class="col-sm-6">
      <div class="results">
        <span>{{'UBZ.SITE_CONTENT.1111111' | translate }}</span>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="pageSize" (change)="changePageSize()">
            <option value="10" selected>10 {{'UBZ.SITE_CONTENT.10000000' | translate }}
              {{searchPageService.advancedSearch['count']}}</option>
            <option *ngIf="searchPageService.advancedSearch['count'] > 20" value="20">20 {{'UBZ.SITE_CONTENT.10000000' |
              translate
              }} {{searchPageService.advancedSearch['count']}}</option>
            <option *ngIf="searchPageService.advancedSearch['count'] > 30" value="30">30 {{'UBZ.SITE_CONTENT.10000000' |
              translate
              }} {{searchPageService.advancedSearch['count']}}</option>
            <option *ngIf="searchPageService.advancedSearch['count'] > 40" value="40">40 {{'UBZ.SITE_CONTENT.10000000' |
              translate
              }} {{searchPageService.advancedSearch['count']}}</option>
            <option *ngIf="searchPageService.advancedSearch['count'] > 50" value="50">50 {{'UBZ.SITE_CONTENT.10000000' |
              translate
              }} {{searchPageService.advancedSearch['count']}}</option>
            <option *ngIf="searchPageService.advancedSearch['count'] <= 50"
              [(ngValue)]="searchPageService.advancedSearch['count']"> {{searchPageService.advancedSearch['count']}}
              {{'UBZ.SITE_CONTENT.10000000' | translate }} {{searchPageService.advancedSearch['count']}}</option>
          </select>
        </div>
      </div>
    </div>
    <div class="col-sm-6" class="pull-right">
      <pagination [boundaryLinks]="true" [directionLinks]="false"
        [totalItems]="searchPageService.advancedSearch['count']" [ngModel]="pageNum" [itemsPerPage]="pageSize"
        [maxSize]="10" (pageChanged)="changePage($event)" class="pagination" previousText="&lsaquo;" nextText="&rsaquo;"
        firstText="&laquo;" lastText="&raquo;"></pagination>
    </div>
  </div>
</section>

<app-collateral-modal *ngIf="collateralModalIsOpen" [isOpen]="collateralModalIsOpen" [collaterals]="collateralsToView"
  [searchType]="searchType" (modalClose)="closeCollateralModal()"></app-collateral-modal>