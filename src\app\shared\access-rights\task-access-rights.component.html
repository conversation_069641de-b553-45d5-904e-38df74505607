<div *ngFor="let outcome of filteredTaskOutcomes">
  <ng-container [ngSwitch]="outcome.eventCod">

    <app-task-button *ngSwitchCase="'UBZ_SUSP'" [customButton]="true" [eventCod]="outcome.eventCod" (success)="goHome()">
      <app-drop-assignment-button [positionId]="positionId" [type]="'SOS'" (saveSuccessful)="completeTask(outcome.eventCod, $event)"></app-drop-assignment-button>
    </app-task-button>

    <app-task-button *ngSwitchCase="'UBZ_REVOKE'" [customButton]="true" [eventCod]="outcome.eventCod" (success)="goHome()">
      <app-drop-assignment-button [positionId]="positionId" [type]="'REV'" (saveSuccessful)="completeTask(outcome.eventCod)"></app-drop-assignment-button>
    </app-task-button>
    <app-task-button *ngSwitchCase="'UBZ_PRZ_KO'" [eventCod]="outcome.eventCod" [buttonLabel]="'UBZ.TaskAccessRights.RiaperturaPerizia'" (buttonClick)="completeTask(outcome.eventCod)"
        (success)="goHome()"></app-task-button>
    <ng-container *ngIf="!(isCte === true && outcome.eventCod ==='UBZ_MODIFY_APPOINTMENT')">
      <app-task-button *ngSwitchDefault [eventCod]="outcome.eventCod" [buttonLabel]="outcome.outcomeDesc" (buttonClick)="completeTask(outcome.eventCod)"
        (success)="goHome()"></app-task-button>
    </ng-container>
  </ng-container>
</div>
<div>
  <ng-content *ngIf="locked"></ng-content>
  <button *ngIf="locked" type="button" class="btn btn-empty pull-right" (click)="releaseTask()">{{'ub1.task.access.rights.releases' | translate}}</button>
  <button *ngIf="lockedByOtherUser" type="button" class="btn btn-empty pull-right" (click)="forceLockTask()">{{'ub1.task.access.rights.forceLock' | translate}}</button>
</div>

<div class="modal fade" bsModal #addNoteModal="bs-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <form #f="ngForm" (ngSubmit)="completeTask('UBZ_PRZ_KO', newNote)" novalidate>
        <div class="modal-header">
          <h2 class="text-left">{{'UBZ.SITE_CONTENT.10000010' | translate }}</h2>
          <button type="reset" class="close pull-right" aria-label="Close" (click)="addNoteModal.hide()">
            <i class="icon-close"></i>
          </button>
        </div>
        <div class="modal-body text-left">
          <div class="row">
            <div class="col-sm-12">
              <p>{{'UBZ.SITE_CONTENT.10000110' | translate }}. </p>
            </div>
            <div class="col-sm-6 form-group">
              <label>{{'UBZ.SITE_CONTENT.10000111' | translate }}*</label>
              <div class="custom-select">
                <select class="form-control" name="macroprocess" [(ngModel)]="newNote.noteType" name="noteType" #noteType="ngModel" required
                  [ngClass]="(f.submitted && !noteType.valid) ? 'error' : 'valid'">
                  <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let row of (noteTypeDomain | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod ? (row.translationCod | translate) : row.domCode}}</option>
                </select>
              </div>
              <div *ngIf="f.submitted && !noteType.valid" class="tooltip fade bottom in" role="tooltip" style="top: 67px; left: 127px;">
                <div class="tooltip-arrow" style="left: 50%;"></div>
                <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
              </div>
            </div>
            <div class="col-sm-6 form-group">
              <label>{{'UBZ.SITE_CONTENT.10001000' | translate }}*</label>
              <input type="text" class="form-control required" data-placement="bottom" [(ngModel)]="newNote.noteTitle" name="noteTitle"
                #noteTitle="ngModel" required [ngClass]="(f.submitted && !noteTitle.valid) ? 'error' : 'valid'" placeholder="{{'UBZ.SITE_CONTENT.10001010' | translate }}"
                maxlength="50" />
              <div *ngIf="f.submitted && !noteTitle.valid" class="tooltip fade bottom in" role="tooltip" style="top: 67px; left: 127px;">
                <div class="tooltip-arrow" style="left: 50%;"></div>
                <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12 form-group">
              <label>{{'UBZ.SITE_CONTENT.10001001' | translate }}*</label>
              <textarea rows="4" class="form-control required" data-placement="bottom" [(ngModel)]="newNote.noteDesc" name="noteDesc" #noteDesc="ngModel"
                required [ngClass]="(f.submitted && !noteDesc.valid) ? 'error' : 'valid'" placeholder="{{'UBZ.SITE_CONTENT.10001011' | translate }}..."
                required></textarea>
              <div *ngIf="f.submitted && !noteDesc.valid" class="tooltip fade bottom in" role="tooltip" style="top: 67px; left: 127px;">
                <div class="tooltip-arrow" style="left: 50%;"></div>
                <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-primary waves-effect">{{'UBZ.SITE_CONTENT.10000010' | translate }}</button>
        </div>
      </form>
    </div>
  </div>
</div>
