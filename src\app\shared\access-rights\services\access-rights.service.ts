import { Injectable, Inject } from '@angular/core';
import { Http, Response, Headers } from '@angular/http';
import { Observable } from 'rxjs/Observable';
import { ReplaySubject } from 'rxjs/ReplaySubject';

import { PositionService } from '../../position/position.service';
import { UserDataService } from '../../user-data/user-data.service';
import { Task } from '../model/task';
import { LockUnlockTask } from '../model/lock-unlock-task';
import { TaskOutcome } from '../model/task-outcome';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';

@Injectable()
export class AccessRightsService {
  umfBaseUrl = 'UMF-EBA-WS/services/rest/';
  ubzBaseUrl = '/UBZ-ESA-RS/service/taskManager/v1/tasks/';
  authKeysSource = new ReplaySubject<string[]>(1);

  constructor(
    private http: Http,
    private userDataService: UserDataService,
    private positionService: PositionService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) { }

  retrieveAccessRightsFunctions(positionId: string, page: string) {
    const userData = this.userDataService.getUserData();
    if (positionId && positionId !== '-') {
      this.positionService
        .getPositionDetail(positionId)
        .subscribe(position =>
          this.callAccessRights(position, page, userData.profile)
        );
    } else {
      this.callAccessRights(null, page, userData.profile);
    }
  }

  getOpenTaskForUser(positionId: string): Observable<Task[]> {
    const headers = new Headers({
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
      'Expires': 'Sat, 01 Jan 2000 00:00:00 GMT',
      'Content-type': 'application/json'
    });
    return this.http
      .get(
        `/UBZ-ESA-RS/service/umfService/taskList/openTaskForUser?positionId=${positionId}`,
        { withCredentials: true, headers: headers }
      )
      .map((resp: Response) => resp.json());
  }

  lockAndGetOutcomeList(
    lockUnlockObj: LockUnlockTask
  ): Observable<TaskOutcome[]> {
    const url = `${this.ubzBaseUrl}getOutcomeList`;
    return this.http
      .put(url, lockUnlockObj)
      .map((resp: Response) => resp.json());
  }

  unlockTask(lockUnlockObj: LockUnlockTask): Observable<any> {
    const url = `${this.ubzBaseUrl}unlockTask`;
    return this.http
      .put(url, lockUnlockObj)
      .map((resp: Response) => resp.json());
  }

  getOutcomesByTaskCod(taskCod: string): Observable<TaskOutcome[]> {
    const headers = new Headers({
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
      'Expires': 'Sat, 01 Jan 2000 00:00:00 GMT',
      'Content-type': 'application/json'
    });
    const url = `/UBZ-ESA-RS/service/umfService/taskList/taskOutcomes?taskCod=${taskCod}`;
    return this.http
      .get(url, { withCredentials: true, headers: headers })
      .map((resp: Response) => resp.json());
  }

  closeTask(lockUnlockObj: LockUnlockTask): Observable<boolean> {
    const url = `${this.ubzBaseUrl}closeTask`;
    return this.http
      .put(url, lockUnlockObj)
      .map((resp: Response) => resp.json());
  }

  private callAccessRights(position: any, page: string, profileCod: string) {
    const drivers = this.extractDrivers(position, page);
    const inputData = {
      profileCod: profileCod,
      processCod: this.constants.processCode,
      attributes: drivers
    };

    this.http
      .post('/UBZ-ESA-RS/service/umfService/accessRights/function', inputData, {
        withCredentials: true
      })
      .subscribe((resp: Response) => this.authKeysSource.next(resp.json()));
  }

  private extractDrivers(position: any, page: string): any[] {
    const drivers = [];
    drivers.push({
      key: 'DD_AR_PP',
      value: position && position.phaseCode ? position.phaseCode : 'AN'
    });
    drivers.push({
      key: 'DD_AR_ST',
      value: position && position.statusCode ? position.statusCode : 'AN'
    });
    drivers.push({
      key: 'DD_AR_PR',
      value: position && position.productType ? position.productType : 'AN'
    });
    drivers.push({
      key: 'DD_AR_CH',
      value: position && position.channel ? position.channel : 'AN'
    });
    drivers.push({
      key: 'DD_AR_PG',
      value: page
    });
    // Driver aggiunto per distinguere individual da corporate
    drivers.push({
      key: 'DD_POS_SEG',
      value: position && position.posSegment ? position.posSegment : 'AN'
    });
    return drivers;
  }
}
