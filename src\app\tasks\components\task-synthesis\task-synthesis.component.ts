import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-task-synthesis',
  templateUrl: './task-synthesis.component.html',
  styleUrls: ['./task-synthesis.component.css']
})
export class TaskSynthesisComponent {
  @Input()
  positionId: string;
  @Input()
  translatedAppraisalType: string;
  @Input()
  translatedLoanScope: string;
  @Input()
  credlineAmount = 0;
  @Input()
  flagExternalAppraisal: boolean;
  @Input()
  translatedAppraisalOwner: string;
  @Input()
  translatedMacroProcess: string;
  @Input()
  appraisalParentId: string;
  @Input()
  originationProcess: string;
  @Input()
  externalPositionId: any;
  @Input()
  assignmentType: string;
  @Input()
  originMigration: string;
  @Input() 
  isTemplateUpdate: boolean;
  @Input() 
  updateType: string = '';

  constructor() {}
}
