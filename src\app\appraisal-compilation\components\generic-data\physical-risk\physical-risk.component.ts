import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { ActivatedRoute, Params } from '@angular/router';
import { Observable } from 'rxjs';
import { AccordionAPFService } from '../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { PositionService } from '../../../../shared/position/position.service';
import { PhysicalRiskService } from '../../../service/physical-risk.service';

@Component({
  selector: 'app-physical-risk',
  templateUrl: './physical-risk.component.html',
  styleUrls: ['./physical-risk.component.css']
})

export class PhysicalRiskComponent implements OnInit {
  allLivello: any[] = [];
  allRischio: any[] = []
  allResponseData: any = {}
  total: any
  @Input() positionId: string;
  collectedData: { [key: string]: string } = {};
  appraisalId: any
  @Output() formDataCollection: EventEmitter<{ [key: string]: string }> = new EventEmitter<{ [key: string]: string }>()
  @ViewChild(NgForm) form: NgForm;
  @Output() activeForm: EventEmitter<any> = new EventEmitter()
  validForm: boolean = false
  macro_process = '';

  constructor(public _accordionAPFService: AccordionAPFService, private service: PhysicalRiskService, private _activatedRoute: ActivatedRoute,
    private positionService: PositionService,
  ) { }

  ngOnInit() {

    this._activatedRoute.params.subscribe((params: Params) => {
      this.appraisalId = params['positionId']

    });

    Observable.forkJoin(
      this.service.getConfigurationTable(),
      this.service.getUpdateData(this.appraisalId),
      this.positionService.getAppraisalInfo(this.positionId)
    )
      .subscribe((res: any) => {
        this.allLivello = res[0].riskLevels;
        this.allRischio = res[0].riskTypes;
        this.total = res[0].riskTypes.length;
        this.allResponseData = res[1];
        if (Object.keys(this.allResponseData).length > 0) {
          this.validForm = true
        };
        this.macro_process = res[2].appraisal.macroProcess;
        if (this.macro_process === "ITL") {
          this.validForm = true
        }
        this.activeForm.emit(this.validForm)
        this.formDataCollection.emit(this.allResponseData)

      })

  }

  getData(title: string, head: string) {
    for (const rischio of this.allRischio) {
      if (!this.form.value[rischio.domCode]) {
        this.validForm = false
        break;
      }
      else {
        this.validForm = true;
      }
    }

    if (this.macro_process === "ITL") {
      this.validForm = true;
    }

    this.collectedData[title] = head
    this.formDataCollection.emit(this.collectedData);
    this.activeForm.emit(this.validForm)
  }

}
