<form #f="ngForm" novalidate (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>{{'UBZ.SITE_CONTENT.111100000' | translate }}
          <span class="state green"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <table class="table">
            <tbody>
              <tr *ngFor="let el of pageContent">
                <td>
                  <ng-container *ngIf="el.sanctionFlag === 'Y'">{{'UBZ.SITE_CONTENT.111100000' | translate }}</ng-container><br/>
                  <span>{{ el.sanctionReqNum }}</span>
                </td>
                <td>
                  <span>{{(sanctionTypes && sanctionTypes[el.sanctionType]) ? (sanctionTypes[el.sanctionType].translationCod | translate) : ''}}</span>
                </td>
                <td>
                  <span>{{el.sanctionReqDate | date:'dd/MM/yyyy'}}</span>
                </td>
                <td>
                  <ng-container *appAuthKey="'UBZ_BUILDING.AMNESTY_DELETE'">
                    <button type="button" class="btn btn-empty waves-effect waves-secondary" (click)="openCustomModal('delete', el)">                      
                      <i class="fa fa-trash-o" aria-hidden="true"></i>
            			  </button>
                  </ng-container>
                  <ng-container *appAuthKey="'UBZ_BUILDING.AMNESTY_MODIFY'">
            			  <button type="button" class="btn btn-empty waves-effect waves-secondary" (click)="openCustomModal('modify', el)">
                      <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
            			  </button>
                  </ng-container>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="row">
            <div class="col-sm-12">
              <ng-container *appAuthKey="'UBZ_BUILDING.AMNESTY_ADD'">
                <button type="button" class="btn btn-empty" (click)="openCustomModal('add')">
                  <i class="fa fa-plus"></i> {{'UBZ.SITE_CONTENT.111100001' | translate }}
                </button>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </accordion-group>
</form>

<!-- CUSTOM MODAL centralizza la gestione delle modal in aggiunta, modfifica e cancellazione per il component -->
<app-custom-modal 
  [modalType] = "modalType"
  [isOpen] = "customModalIsOpen"
  [largeModalFlag] = "largeModalFlag"
  [headerTitle] = "headerTitle"
  [positionId]="positionId" 
  [idCode]="selectedPageElement ? selectedPageElement.sanctionId : ''"   
  [apfString] = "apfString"
  [messagesArray] = "messagesArray"
  [buttonTitle] = "buttonTitle"
  [disabledFlag] = "disabledFlag"  
  (modalSubmit) = "handleSubmitCustomModal($event)"
  (modalClose)="closeCustomModal()">
</app-custom-modal>
