import { Component, OnInit, Inject } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';

// Services
import { CustomerService } from '../simulation';
import { PositionService } from '../shared/position/position.service';
import { DOCUMENT } from '@angular/platform-browser';
import { RegistryService } from '../registry/service/registry.service';
import { TaskLandingService } from '../tasks';

@Component({
selector: 'app-redirect',
templateUrl: './redirect.component.html',
styleUrls: ['./redirect.component.css'],
providers: [RegistryService, TaskLandingService]
})
export class RedirectComponent implements OnInit {

	constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private positionService: PositionService,
    private customerService: CustomerService,
    private registryService: RegistryService,
    private taskLandingService: TaskLandingService,
    @Inject(DOCUMENT) private document: any        
  ) {
    // Indirizzamento del metodo da eseguire in base al link originale
    this.activatedRoute.params.subscribe(params => {
      if (this.document.location.hash.search('experts-landing') === 2) {
        this.expertsLandingRedirect();
        return;
      }
      if (this.document.location.hash.search('task-landing') === 2) {
        this.taskLandingRedirect(params);
        return;
      }
    });
    this.activatedRoute.queryParams.subscribe((params) => {
      if (this.document.location.hash.search('goPrz') === 2) {
        // Salva nell'oggetto del service i queryParams per essere consultati da i componenti dopo il redirect
        this.positionService.externalLinkParams = params;                
        this.externalLinkRedirect(Object.assign({}, params));
      }      
    });
  }

  ngOnInit() {}
  
  // In base alla tipologia di link richiamato esternamente all'applicazione esegue il giusto redirect
  // params in ingresso contiene i queryParams dell'url
  private externalLinkRedirect(params: Object) {
    switch (params['method']) {
      case 'dettaglioRichiesta':
        // link per ingaggiare dettaglio richiseta: goPrz2.do?method=dettaglioRichiesta&positionId=4000599
        setTimeout(() => {
          this.router.navigate([`landing/WRPE/${params['positionId']}`], {replaceUrl: true});                
        }, 100);
        return;
      case 'dettaglioRichiestaTgp':
        // link per ingaggiare dettaglio richiseta: goPrz2.do?method=dettaglioRichiestaTgp&tgpId=4000599      
        this.customerService.translateIdTgp(params['tgpId']).subscribe((response) => {
          if (response && response['requestId']) {
            setTimeout(() => {
              this.router.navigate([`landing/WRPE/${response['requestId']}`], {replaceUrl: true});                          
            }, 100);
          } else {
            this.router.navigate(['index']);
          }
        }, error => {
          this.router.navigate(['index']);
        });
        return;
      case 'dettaglioPerizia':
        // link per ingaggiare dettaglio perizia: goPrz2.do?method=dettaglioPerizia&appraisalId=4000541     
        setTimeout(() => {
          this.positionService.externalLinkParams = params;          
          this.router.navigate([`/generic-task/${params['appraisalId']}/-/-/`], {replaceUrl: true});        
        }, 100); 
        return;
      case 'dettaglioStatoPerizia':
        // link per ingaggiare dettaglio perizia: goPrz2.do?method=dettaglioStatoPerizia&appraisalId=4000541
        setTimeout(() => {
          this.positionService.externalLinkParams = params;          
          this.router.navigate([`/generic-task/${params['appraisalId']}/-/-/`], {replaceUrl: true});
        }, 100);
        return;
      case 'creaRichiestaTgp':
        delete params['method'];      
        // link per ingaggiare il crea richiseta da TGP: 
        // https://ubz-uj.collaudo.usinet.it/#/goPrz2.do?method=creaRichiestaTgp&idOrdine=LZZ5Z807&ndg=23041357&regionCode=42012&comune=FI&provincia=FI&indirizzoBene=Via%20Pratese%20162&telefono=05532344&cognome=YDHXOMBE&nome=EUVOLDT&codProduct=007
        // goPrz2.do?method=creaRichiestaTgp&idOrdine=LZZ5Z801&ndg=23041357&regionCode=42012&comune=FI&provincia=FI&indirizzoBene=Via%20Pratese%20162&telefono=05532344&cognome=YDHXOMBE&nome=EUVOLDT&codProduct=007
        // goPrz2.do?method=creaRichiestaTgp&idOrdine=LZZ5Z793&ndg=0000000000002516&regionCode=42012&comune=FI&provincia=FI&indirizzoBene=Via%20Pratese%20162&telefono=05532344&cognome=JJEVRHC&nome=ETCNFLGC&codProduct=SUBC0003
        // Invoca il servizio per creare la nuova richiesta TGP, su esito positivo della chiamata esegue redirect sul wizard di richiesta
        this.customerService.startTgpAppraisalRequest(params).subscribe((response) => {
          if (response) {
            setTimeout(() => {
              this.router.navigate([`landing/WRPE/${response['requestId']}`], {replaceUrl: true});                
            }, 100);
            return;
          } else {
            this.router.navigate(['index']);            
          }
        }, error => {
          this.router.navigate(['index']);          
        });
    }
  }

  // Esegue il redirect in seguito alla rotta experts-langing
  private expertsLandingRedirect() {
    this.registryService.getLandingPage().subscribe(landingPage => {
      if (landingPage) {
        this.router.navigateByUrl(`registry/${landingPage}`);
      }
    });
  }

  // Esegue il redirect in seguito alla rotta task-landing
  // Recupera l'url della landingPage in base al taskCod in input  
  private taskLandingRedirect(params) {
    this.taskLandingService
    .getPageByTaskCod(params['taskCod'])
    .subscribe((landingPage: string) => {
      if (landingPage) {
        this.router.navigate([
          `${landingPage}/${params['positionId']}/${params['taskId']}/${params['taskCod']}`
        ]);
      }
    });
  }
}