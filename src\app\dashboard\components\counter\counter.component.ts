import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

import { CounterInput } from '../../model/counter-input';

@Component({
  selector: 'app-counter',
  templateUrl: './counter.component.html',
  styleUrls: ['./counter.component.css']
})
export class CounterComponent implements OnInit {
  @Input() inputParam: CounterInput;
  @Output() outputEvent = new EventEmitter();
  // elemClass = 'col-sm-6 col-md-';
  elemClass = 'col-xs-4 col-sm-3 col-md-';

  slideConfig = {
    slidesToShow: 6,
    slidesToScroll: 6,
    infinite: false,
    arrows: true,
    autoplay: false,
    prevArrow:
      '<button type="button" class="Carousel__PrevArrow"><i class="fa fa-angle-left"></i></button>',
    nextArrow:
      '<button type="button" class="Carousel__NextArrow"><i class="fa fa-angle-right"></i></button>',
    responsive: [
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 4,
          slidesToScroll: 4,
          infinite: false
        }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 3,
          infinite: false
        }
      }
    ]
  };

  counterColors = [
    '#388bca',
    '#1bacc2',
    '#7757a4',
    '#3db049',
    '#0f796b',
    '#bfcb30',
    '#f58523',
    '#dd1860',
    '#a33694',
    '#004c3d',
    '#faaa18',
    '#9e9f36',
    '#e01e25'
  ];
  counterBackgroundStyles = [
    'lavorazione',
    'sospese',
    'second-opinion',
    'third-opinion',
    'concluse',
    'pratiche',
    'annullata',
    'attiva',
    'richiesta',
    'terminata',
    'richiesta-perizia',
    'riaperta',
    'first-opinion'
  ];
  counterTextColor = [
    'ticket-blue',
    'ticket-lightblue',
    'ticket-purple',
    'ticket-green',
    'ticket-darkgreen',
    'ticket-yellow',
    'ticket-orange',
    'ticket-strawberry',
    'ticket-lightpurple',
    'ticket-deepgreen',
    'ticket-lightorange',
    'ticket-olivegreen',
    'ticket-red'
  ];

  constructor() {}

  ngOnInit() {
    let elemSize = 1;
    if (this.inputParam.numElem <= 12) {
      elemSize = Math.floor(12 / this.inputParam.numElem);
    }
    this.elemClass += elemSize;
    for (const ind in this.inputParam.counter) {
      if (this.inputParam.counter.hasOwnProperty(ind)) {
        this.timeoutLoop(0, this.inputParam.counter[ind].num, ind, 5);
      }
    }
  }

  clickFunction(index, counterId) {
    for (const counter of this.inputParam.counter) {
      if (counter.id === counterId) {
        counter.isActive = true;
      } else {
        counter.isActive = false;
      }
    }
    this.outputEvent.emit({
      obj: this.inputParam,
      index: index,
      counterId: counterId
    });
  }

  getCounterBackgroundClass(index: number) {
    let active = '';
    if (this.inputParam.counter[index].isActive) {
      active = 'active';
    }
    if (index >= this.counterBackgroundStyles.length) {
      index -= this.counterBackgroundStyles.length;
    }
    return (
      'custom-chart ' +
      this.counterBackgroundStyles[index] +
      '-counter ' +
      active
    );
  }

  getCounterTextClass(index: number) {
    if (index >= this.counterTextColor.length) {
      index -= this.counterTextColor.length;
    }
    return 'tickets-label text-center ' + this.counterTextColor[index];
  }

  public getCounterColors(index: number) {
    if (index >= this.counterColors.length) {
      index -= this.counterColors.length;
    }
    return this.counterColors[index];
  }

  private timeoutLoop(
    num: number,
    max: number,
    counterIndex: string,
    loopCounter: number
  ) {
    setTimeout(() => {
      if (num !== max && loopCounter !== 0) {
        num = Math.floor(max / 6) * (6 - loopCounter);
        this.inputParam.counter[counterIndex].num = num;
        loopCounter--;
        this.timeoutLoop(num, max, counterIndex, loopCounter);
      } else {
        this.inputParam.counter[counterIndex].num = max;
      }
    }, 200);
  }
}
