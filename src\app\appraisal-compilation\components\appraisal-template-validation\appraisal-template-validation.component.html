<form #f="ngForm" novalidate (click)="accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>{{'UBZ.SITE_CONTENT.1010001001' | translate }}
          <span class="state" [ngClass]="{'green': f.valid, 'red': !f.valid}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <div class="col-sm-12 form-group">
            <label>{{'UBZ.SITE_CONTENT.1010001010' | translate }}*</label>
            <div class="radio-buttons">
              <div class="custom-radio">
                <input type="radio" name="validation" id="yes" class="radio" required [value]="true" [(ngModel)]="appValidation.validate" [disabled]="haveDisabledFields">
                <label for="yes">{{'UBZ.SITE_CONTENT.101111011' | translate }}</label>
              </div>
              <div class="custom-radio">
                <input type="radio" name="validation" id="no" class="radio" required [value]="false" [(ngModel)]="appValidation.validate" [disabled]="haveDisabledFields">
                <label for="no">{{'UBZ.SITE_CONTENT.110011101' | translate }}</label>
              </div>
            </div>
          </div>
          <div class="col-sm-12 form-group">
            <label class="active">
              <span><i class="icon-search note-tooltip" [tooltip]="appValidation.note" triggers="click"></i></span>
              {{'UBZ.SITE_CONTENT.11001011' | translate }}*
            </label>
            <textarea class="form-control" name="note" placeholder="{{'UBZ.SITE_CONTENT.10001011' | translate }}..." [(ngModel)]="appValidation.note" required
              [disabled]="haveDisabledFields"></textarea>
          </div>
        </div>
      </div>
    </div>
  </accordion-group>
</form>
