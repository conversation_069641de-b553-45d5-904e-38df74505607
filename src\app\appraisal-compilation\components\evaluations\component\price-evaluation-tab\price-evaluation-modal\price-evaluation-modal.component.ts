import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { Rilevation } from '../../../model/evaluations.models';

@Component({
  selector: 'app-price-evaluation-modal',
  templateUrl: './price-evaluation-modal.component.html',
  styleUrls: ['./price-evaluation-modal.component.css']
})
export class PriceEvaluationModalComponent implements OnInit {
  @Input() modalType: string;               // add / modify  
  @Input() isOpen: boolean;
  @Input() model: Rilevation;         // contiene l'oggetto in modifica, new Rilevation() in caso di modal di aggiunta  
  @Input() domains;
  @Output() modalClose = new EventEmitter();  
  @Output() modalSubmit = new EventEmitter();    
  modelCopy: Rilevation; // Copia dell'oggett originale per permettere la modifica dei valori
  
  constructor() {}

  ngOnInit() {
    this.modelCopy = JSON.parse(JSON.stringify(this.model));    
  }

  hide() {
    this.modalClose.emit();    
  }

  submit() {
    this.modalSubmit.emit(this.modelCopy);
  }
}
