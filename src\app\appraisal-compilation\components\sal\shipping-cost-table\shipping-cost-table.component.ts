import { Component, OnInit, On<PERSON><PERSON>roy, Input, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { AccordionAPFService } from '../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { ShippingCostItem } from '../model/sal.models';
import { DomainService } from '../../../../shared/domain/domain.service';
import { Domain } from '../../../../shared/domain/domain';
@Component({
  selector: 'app-shipping-cost-table',
  templateUrl: './shipping-cost-table.component.html',
  styleUrls: ['./shipping-cost-table.component.css'],
  providers: [DomainService]
})
export class ShippingCostTableComponent implements OnInit, OnDestroy {
  @Input() model: any = {};
  expenseTypes: Domain[] = [];
  private _subscription: any;
  @ViewChild(NgForm) form: NgForm;

  constructor(
    public _accordionAPFService: AccordionAPFService,
    private _domainService: DomainService
  ) {}

  ngOnInit() {
    this._subscription = this._domainService
      .newGetDomain('UBZ_DOM_EXPENSE_TYPE')
      .subscribe(res => {
        this.expenseTypes = res;
      });
  }

  ngOnDestroy() {
    if (this._subscription) {
      this._subscription.unsubscribe();
    }
  }

  public isValid(): boolean {
    return this.form && this.form.valid;
  }

  public getModel(): any {
    return this.model;
  }

  calculateTotal() {
    let total = 0;
    // this.model.datiSalComplessivo.mobAppExec
    if (this.model.mobAppExec) {
      let num: number;
      for (const key in this.model.mobAppExec) {
        if (
          // this.model.mobAppExec.hasOwnProperty(key) &&
          this.model.mobAppExec[key]
        ) {
          num = this.parseNumber(this.model.mobAppExec[key].totCost);
          total += num;
        }
      }
    }
    return total;
  }
  calculateAlreadyGiven() {
    let total = 0;
    if (this.model.mobAppExec) {
      let num: number;
      for (const key in this.model.mobAppExec) {
        if (
          // this.model.mobAppExec.hasOwnProperty(key) &&
          this.model.mobAppExec[key]
        ) {
          num = this.parseNumber(this.model.mobAppExec[key].executedCost);
          total += num;
        }
      }
    }
    return total;
  }

  calculateDiff(index: number): number {
    let difference = 0;
    if (this.model.mobAppExec && this.model.mobAppExec[index]) {
      let tCost: number;
      let execCost: number;
      tCost = this.parseNumber(this.model.mobAppExec[index].totCost);
      execCost = this.parseNumber(this.model.mobAppExec[index].executedCost);
      difference = tCost - execCost;
    }
    return difference;
  }
  calculateToGive() {
    let total = 0;
    if (this.model.mobAppExec) {
      let num: number;
      for (const key in this.model.mobAppExec) {
        if (this.model.mobAppExec.hasOwnProperty(key)) {
          num =
            this.parseNumber(this.model.mobAppExec[key].totCost) -
            this.parseNumber(this.model.mobAppExec[key].executedCost);
          total += num;
        }
      }
    }
    return total;
  }

  private parseNumber(toParse: string): number {
    let num = Number.parseFloat(Number.parseFloat(toParse).toFixed(2));
    if (Number.isNaN(num)) {
      num = 0;
    }
    return num;
  }
}
