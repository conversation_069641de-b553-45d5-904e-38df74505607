<div *ngIf="isOpen" class="modal fade" id="add-society" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
  bsModal #stateBox="bs-modal" (onHidden)="closeModal(false)" [config]="{show: 'true'}">
  <div class="modal-dialog modal-md" role="document">
    <div class="modal-content text-center">
      <div class="modal-header">
        <h2>{{ 'UBZ.SITE_CONTENT.11111001111' | translate }}</h2>
        <button type="button" class="close pull-right" aria-label="Close" (click)="closeModal(false)">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <h4>{{ 'UBZ.SITE_CONTENT.11111010000' | translate }}</h4>
        <form [formGroup]="form" (ngSubmit)="submit()">
          <div class="form-container">
            <label formArrayName="societies" *ngFor="let society of societiesFormArray.controls; let i = index">
              <input type="checkbox" [formControlName]="i">
              {{inactiveList[i].heading | uppercase }}              
            </label>
          </div>
          <div class="modal-footer">
            <button class="btn btn-primary" type="submit" [disabled]="!form.valid">AGGIUNGI</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>