import { Component, OnInit, Inject } from "@angular/core";
import { Router, ActivatedRoute } from "@angular/router";
import { Observable } from "rxjs/Observable";
import "rxjs/add/observable/forkJoin";
import "rxjs/add/operator/pairwise";

// Models
import { CustomerResponse } from "../../../wizard-detail/model/customer-response";
import { AppraisalResponse } from "../../../wizard-detail/model/appraisal-response";
import { Domain } from "../../../shared/domain/domain";
import { APP_CONSTANTS, IAppConstants } from "../../../app.constants";
import { Collateral } from "../../../wizard-detail/model/collateral";
import { WizardElement } from "../../../shared/wizard/model/wizard-element";

// Services
import { WizardDetailService } from "../../../wizard-detail/services/wizard-detail.service";
import { GenericTaskService } from "../../services/generic-task/generic-task.service";
import { PositionService } from "../../../shared/position/position.service";
import { WizardService } from "../../../shared/wizard/services/wizard.service";
import { LandingService } from "../../../simulation/services/landing/landing.service";

@Component({
  selector: "app-generic-task",
  templateUrl: "./generic-task.component.html",
})
export class GenericTaskComponent implements OnInit {
  positionId: string;
  opinionType: string;
  wizardCode = this.constants.wizardCodes["PER"];
  taskId: string;
  taskCod: string;
  requestId: string;
  public activeSectionEnum = {
    STO: "storicoSopraluogo",
    ASS: "assegnazione",
    SIN: "sintesi",
    CHE: "checklist",
    NOT: "note",
    HIS: "history",
    DET: "detail",
    CON: "esitoControlli",
    SAM: "controlliACampione",
    SUR: "esitoQuestionario",
    ALS: "apiLogStatus",
    CKA: "assignCollateralsToAssets",
    WTI: "waitInspection",
    CHE_SERV: "checklist-servicing",
  };
  activeSection: string;
  customerResp: CustomerResponse = new CustomerResponse();
  appraisalResp: AppraisalResponse = new AppraisalResponse();
  private _ndgTypes: Domain[] = [];
  private _scopeTypes: Domain[] = [];
  private _appraisalTypes: Domain[] = [];
  private _macroProcesses: Domain[] = [];
  private _appraisalOwners: Domain[] = [];
  private _opinionTypes: Domain[] = [];
  private _updateTypes: Domain[] = [];
  isAppraisalConvalidated: boolean;
  expert: string;
  reviser: string;
  socHeading: string;
  appraisalList: any;
  guaranteeList: Collateral[] = [];
  flagExternalAppraisal: boolean;
  showTaskButtons: boolean;
  appraisalCompiled: boolean;
  appraisalOwner: string;
  originMigration: string;
  originationProcess: string;
  isSecondOpinion: boolean;
  appraisalParentId: string;
  massiveUpload: boolean;
  externalPositionId: any;
  assignmentType: string;
  hasQuestionario = false;
  isCtu = false;
  updateType: string = "";
  isTemplateUpdate: boolean;
  bankCod: string = "";
  sendUcscFlag: string;
  statusCode: string;
  phaseCode: string;
  constructor(
    private _activatedRoute: ActivatedRoute,
    private _router: Router,
    private _wizardDetailService: WizardDetailService,
    private _positionService: PositionService,
    public _genericTaskService: GenericTaskService,
    private _wizardService: WizardService,
    private _landingService: LandingService,
    @Inject(APP_CONSTANTS) public constants: IAppConstants
  ) {}

  ngOnInit() {
    this._activatedRoute.params
      .switchMap((params) => {
        this.positionId = params["positionId"];
        this.taskId = params["taskId"];
        this.taskCod = params["taskCod"];
        this.showTaskButtons =
          !this.taskId ||
          this.taskId === "-" ||
          !this.taskCod ||
          this.taskCod === "-";
        if (this.showTaskButtons) {
          this.activeSection = this.activeSectionEnum.SIN;
        } else {
          switch (this.taskCod) {
            case this.constants.SAMPLE_CHECKS:
              this.activeSection = this.activeSectionEnum.SAM;
              break;
            case this.constants.tasks.manualAssignment:
              this.activeSection = this.activeSectionEnum.ASS;
              break;
            case this.constants.tasks.inspectionOutcome:
              this.activeSection = this.activeSectionEnum.STO;
              break;
            case this.constants.tasks.fixAppointment:
              this.activeSection = this.activeSectionEnum.HIS;
              break;
            case this.constants.tasks.checkAsset:
              this.activeSection = this.activeSectionEnum.CKA;
              break;
            case this.constants.tasks.waitInspection:
              this.activeSection = this.activeSectionEnum.WTI;
              break;
            default:
              this.activeSection = this.activeSectionEnum.DET;
          }
        }
        return this._positionService.getAppraisalInfo(this.positionId);
      })
      .subscribe((res: any) => {
        this.statusCode = res.appraisal.statusCod;
        this.phaseCode = res.appraisal.phaseCod;

        if (typeof res.appraisal.bankCod !== "undefined") {
          this.bankCod = res.appraisal.bankCod;
        }
        this.requestId = res.requestId;
        this.originMigration = res.originMigration;
        this.originationProcess = res.appraisal.originProcess;
        this._landingService.originationProcess = "";
        this.externalPositionId = res.externalPositionId;
        this.isSecondOpinion = res.opinionType === "SO";
        this.opinionType = res.opinionType;
        if (
          res.appraisal.loanScope === "CTU" &&
          (res.appraisal.macroProcess === "MLC" ||
            res.appraisal.macroProcess === "MLR")
        ) {
          this.isCtu = true;
        }
        this.massiveUpload =
          res.appraisal.macroProcess === "MLP" ||
          res.appraisal.macroProcess === "FIP";
        this.isAppraisalConvalidated = res.isConvalidated;

        // Check if is template update (template di aggiornamento perizia)
        this.isTemplateUpdate = res.templateType && res.templateType === "AGL";
        if (this.isTemplateUpdate) {
          Observable.forkJoin(
            this._wizardDetailService.getUpdateTypes(),
            this._positionService.getAppraisalUpdateType(this.positionId)
          ).subscribe((res: any) => {
            this._updateTypes = res[0];
            if (
              typeof res[1].updateType !== "undefined" &&
              res[1].updateType &&
              typeof this._updateTypes[res[1].updateType] !== "undefined" &&
              typeof this._updateTypes[res[1].updateType].translationCod !==
                "undefined"
            ) {
              this.updateType =
                this._updateTypes[res[1].updateType].translationCod;
            }
          });
        }

        this._wizardDetailService.getOpinionTypes().subscribe((res) => {
          this._opinionTypes = res;
          if (this.opinionType && this._opinionTypes[this.opinionType]) {
            this.opinionType =
              this._opinionTypes[this.opinionType].translationCod;
          }
        });
        if (this.activeSection === this.activeSectionEnum.SIN) {
          this.fetchSynthesis(res);
        }
        if (this.showTaskButtons) {
          this.checkAppraisalFIllingStatus();
        }
        this._genericTaskService
          .getSurveyDetails(this.positionId)
          .subscribe((response) => {
            if (response["quest"] && response["quest"].length > 0) {
              this.hasQuestionario = true;
            }
          });
      });
  }

  checkAppraisalFIllingStatus() {
    this._wizardService.getWizard(this.positionId).subscribe((res) => {
      const wizard: WizardElement[] = res;

      let wizardComplete = false;
      if (wizard) {
        for (const task of wizard) {
          if (
            task.taskList.taskCod ===
              `${this.constants.processCode}-${this.constants.wizardCodes["PER-COM"]}` &&
            task.taskList.outcomeCod === "2"
          ) {
            wizardComplete = true;
            break;
          }
        }
        this.appraisalCompiled = wizardComplete;
      }
    });
  }

  selezionaTab(enumValue) {
    this.activeSection = enumValue;
    if (this.activeSection === this.activeSectionEnum.SIN) {
      this.retrieveAppraisalData();
    }
  }

  private retrieveAppraisalData() {
    this._positionService
      .getAppraisalInfo(this.positionId)
      .subscribe((resp) => this.fetchSynthesis(resp));
  }

  fetchSynthesis(appraisalInfo: any) {
    this.appraisalParentId = appraisalInfo.appraisalParentId;
    this.appraisalResp = appraisalInfo.appraisal;
    this.customerResp = appraisalInfo.customer;
    this.flagExternalAppraisal = appraisalInfo.flagExternalAppraisal;
    this.appraisalOwner = appraisalInfo.appraisalOwner;
    this.appraisalList = this.sortAssetsByDisabledStatus(
      appraisalInfo.appraisalObject,
      appraisalInfo.isConvalidated
    );
    this.fetchGuarantees(appraisalInfo.lstJoint);
    Observable.forkJoin(
      this._wizardDetailService.getNDGTypes(),
      this._wizardDetailService.getScopeTypes(),
      this._wizardDetailService.getAppraisalTypes(),
      this._wizardDetailService.getAppraisalOwners(),
      this._wizardDetailService.getMacroprocesses(),
      this._positionService.getAppraisalAssignment(this.positionId),
      this._wizardDetailService.getAssignmentType(this.positionId, "PER"),
      this._positionService.getAppraisalInfo(this.positionId)
    ).subscribe((res) => {
      this._ndgTypes = res[0];
      this._scopeTypes = res[1];
      this._appraisalTypes = res[2];
      this._appraisalOwners = res[3];
      this._macroProcesses = res[4];
      this.assignmentType = res[6].appEmpType ? res[6].appEmpType : null;
      this.expert = res[5].expertName ? res[5].expertName : "";
      this.expert += res[5].expertSurname ? " " + res[5].expertSurname : "";
      this.reviser = res[5].reviserName ? res[5].reviserName : "";
      this.reviser += res[5].reviserSurname ? " " + res[5].reviserSurname : "";
      this.socHeading = res[5].socHeading;
      this.sendUcscFlag = res[7].invoiceUcscFlag;
      this.solvePrintableWords();
    });
  }
  private fetchGuarantees(respArray: any) {
    this.guaranteeList = [];
    for (const joint of respArray) {
      const coll = new Collateral();
      coll.objectCod = joint.objectCod;
      coll.progCollateral = joint.jointCod;
      coll.collateralTecForm = joint.collatTecForm;
      coll.collateralDesc = joint.collatDesc;
      coll.collateralAmmount = joint.collatAmount;
      coll.percType = joint.poolType;
      coll.poolPerc = joint.poolPerc;
      this.guaranteeList.push(coll);
    }
  }

  private solvePrintableWords() {
    if (
      this.customerResp.ndgType &&
      this._ndgTypes[this.customerResp.ndgType]
    ) {
      this.customerResp.ndgType =
        this._ndgTypes[this.customerResp.ndgType].translationCod;
    }
    if (
      this.appraisalResp.appraisalType &&
      this._appraisalTypes[this.appraisalResp.appraisalType]
    ) {
      this.appraisalResp.appraisalType =
        this._appraisalTypes[this.appraisalResp.appraisalType].translationCod;
    }
    if (
      this.appraisalResp.macroProcess &&
      this._macroProcesses[this.appraisalResp.macroProcess]
    ) {
      this.appraisalResp.macroProcess =
        this._macroProcesses[this.appraisalResp.macroProcess].translationCod;
    }
    if (this.opinionType && this._opinionTypes[this.opinionType]) {
      this.opinionType = this._opinionTypes[this.opinionType].translationCod;
    }
    if (
      this.appraisalResp.appraisalScope &&
      this._scopeTypes[this.appraisalResp.appraisalScope]
    ) {
      this.appraisalResp.appraisalScope =
        this._scopeTypes[this.appraisalResp.appraisalScope].translationCod;
    }
    if (
      this.appraisalResp.loanScope &&
      this._scopeTypes[this.appraisalResp.loanScope]
    ) {
      this.appraisalResp.loanScope =
        this._scopeTypes[this.appraisalResp.loanScope].translationCod;
    }
    if (this.appraisalOwner && this._appraisalOwners[this.appraisalOwner]) {
      this.appraisalOwner =
        this._appraisalOwners[this.appraisalOwner].translationCod;
    }
  }

  goToDashboard() {
    this._router.navigateByUrl("index");
  }

  goToAppraisalCompile() {
    if (
      this.originationProcess !== "MIG" &&
      this.originationProcess !== "FRA" &&
      !this.massiveUpload &&
      !this.isCtu
    ) {
      this._router.navigateByUrl(
        `landing/${this.constants.wizardCodes["PER-COM"]}/${this.positionId}/${this.taskId}/${this.taskCod}/true`
      );
    } else if (this.isCtu) {
      let taskLockingUser = "-";
      if (this._genericTaskService.taskLockingUser) {
        taskLockingUser = this._genericTaskService.taskLockingUser;
      }
      this._router.navigateByUrl(
        `appraisal-ctu/${this.positionId}/${this.wizardCode}/${taskLockingUser}`
      );
    } else {
      let taskLockingUser = "-";
      if (this._genericTaskService.taskLockingUser) {
        taskLockingUser = this._genericTaskService.taskLockingUser;
      }
      this._router.navigateByUrl(
        `appraisal-migration/${this.positionId}/${this.wizardCode}/${taskLockingUser}`
      );
    }
  }

  goToSampleCheck() {
    this.taskId = this.constants.SAMPLE_CHECKS;
    this.taskCod = this.constants.SAMPLE_CHECKS;
    this.showTaskButtons = false;
    this.activeSection = this.activeSectionEnum.SAM;
  }

  sortAssetsByDisabledStatus(
    appraisalObjectList: any[],
    isAppraisalConvalidated: boolean
  ): any[] {
    if (
      isAppraisalConvalidated &&
      appraisalObjectList &&
      appraisalObjectList.length > 0
    ) {
      const multipleAssetsPerObject: any[] = [];
      const multipleAssetsPerObjectAllDisabled: any[] = [];
      const singleActiveAssets: any[] = [];
      const singleDisabledAssets: any[] = [];
      const statelessAssets: any[] = [];

      appraisalObjectList.forEach((currentAsset) => {
        const assetDetail: any[] =
          currentAsset &&
          currentAsset.assetDetail &&
          currentAsset.assetDetail.length > 0
            ? currentAsset.assetDetail
            : [];

        // CASE: a single asset per object
        if (
          !currentAsset.isMultipleAssetPerObject &&
          assetDetail.length === 1
        ) {
          // group list by asset state (case: disabled assets || case: active assets)
          if (assetDetail[0].disabledStatus === true) {
            singleDisabledAssets.push(currentAsset);
          } else if (assetDetail[0].disabledStatus === false) {
            singleActiveAssets.push(currentAsset);
          } else {
            statelessAssets.push(currentAsset);
          }

          // CASE: multiple assets per object
        } else if (
          currentAsset.isMultipleAssetPerObject &&
          assetDetail.length > 1
        ) {
          const areAllAssetsDisabled: boolean = assetDetail.every(
            (detail) => detail.disabledStatus === true
          );
          // group by asset state (case: all assets are disabled || case: not all assets are disabled)
          if (areAllAssetsDisabled) {
            multipleAssetsPerObjectAllDisabled.push(currentAsset);
          } else {
            multipleAssetsPerObject.push(currentAsset);
          }
        }

        // CASE: missing information
        else {
          statelessAssets.push(currentAsset);
        }
      });

      // recreate appraisalObjectList so that it displays the assets in the following order:
      const sortedAppraisalObjectList: any[] = [];
      sortedAppraisalObjectList.push(...singleActiveAssets);
      sortedAppraisalObjectList.push(...multipleAssetsPerObject);
      sortedAppraisalObjectList.push(...singleDisabledAssets);
      sortedAppraisalObjectList.push(...multipleAssetsPerObjectAllDisabled);
      sortedAppraisalObjectList.push(...statelessAssets);

      appraisalObjectList = sortedAppraisalObjectList;

      // sort the inner assetDetail list for each one of the appraisalObjectList assets
      this.sortAssetDetailsByDisabledAssetLast(appraisalObjectList);
    }
    return appraisalObjectList;
  }

  private sortAssetDetailsByDisabledAssetLast(
    appraisalObjectList: any[]
  ): void {
    appraisalObjectList.forEach((asset) => {
      if (asset && asset.assetDetail && asset.assetDetail.length > 0) {
        asset.assetDetail.sort((currentAssetDetail, nextAssetDetail) => {
          return currentAssetDetail.disabledStatus === true ? 1 : -1;
        });
      }
    });
  }
}
