<nav class="navbar navbar-default navbar-fixed-top">
  <div class="navbar-header">
    <a (click)="changeSideMenuPropriety()" class="menu-toggle" id="menu-toggle">
      <i [class]="menuIconClass"></i>
    </a>
    <a class="navbar-brand" href="#"><img class="img-responsive" src="assets/img/logo.png" alt="Unicredit"></a>
  </div>

  <div class="navbar-collapse" id="menu-collapse">
    <ul *ngFor="let element of menuService.menu ; let i = index; let la = last" class="nav navbar-nav hidden-xs hidden-sm">
      <li [ngClass]="{'active': i == menuService.menuStatus[0], '': i != menuService.menuStatus[0]}">
        <a *ngIf="element.target !== 'EXT'" (click)="menuService.changeHeadPage( element.action , i)"><i [class]="menuService.getIconClass(element.name)"></i>{{element.title | translate}}</a>
        <a *ngIf="element.target === 'EXT'" [href]="element.action">
          <i [class]="menuService.getIconClass(element.name)"></i>
          {{element.title | translate}}
        </a>
      </li>
    </ul>
    <ul class="nav navbar-nav navbar-user navbar-right">
      <li class="dropdown user">
        <div [class]="userMenuClass" style="padding-left: 40px; padding-top: 20px;">
          <span><strong>{{userDetails?.username}}</strong> {{userDetails?.profile}}</span>
          <span><strong>{{'UBZ.SITE_CONTENT.1' | translate }}</strong> {{userDetails?.branch}}</span>
        </div>
      </li>
    </ul>
  </div>
</nav>
