import {
  Component,
  ViewChildren,
  QueryList,
  ChangeDetectorRef,
  Inject,
  AfterViewChecked,
  Input,
  Output,
  EventEmitter
} from '@angular/core';
import { DOCUMENT } from '@angular/platform-browser';

// External Modules
import { NgForm } from '@angular/forms';

// Models
import { IAppConstants, APP_CONSTANTS } from '../../../../app.constants';

// Components
import { ApplicationFormComponent } from '../application-form.component';

// Services
import { ApplicationFormService, AccordionAPFService } from '../../services';
import { DomainService } from '../../../domain';
import { SharedService } from '../../../services/shared.service';


@Component({
  selector: 'app-accordion-application-form',
  templateUrl: './accordion-application-form.component.html',
  styleUrls: ['./accordion-application-form.component.css']
})
export class AccordionApplicationFormComponent extends ApplicationFormComponent implements AfterViewChecked {
  @ViewChildren(NgForm) listaForm: QueryList<NgForm>;
  @Input() formDisabled: boolean; // forza il disabled sui campi del form, utilizzato su textarea per bloccarlo ma permetterne la lettura
  @Input() mapApf: any;
  @Output() calendarChange: EventEmitter<any> = new EventEmitter(); // Risolve il problema del calendar che non scatena automaticamente l'evento change del form
  createValuationRules = false;
  condition: boolean;

  constructor(
    protected appFormService: ApplicationFormService,
    protected domainService: DomainService,
    public accordionAPFService: AccordionAPFService,
    protected sharedService: SharedService,
    protected _cdRef: ChangeDetectorRef,
    @Inject(APP_CONSTANTS) protected constants: IAppConstants,
    @Inject(DOCUMENT) private document: any
  ) {
    super(appFormService, domainService, sharedService, _cdRef, constants);
  }

  ngAfterViewChecked() {
    if (!this.createValuationRules && this.applicationForms.length > 0 && this.listaForm.toArray().length > 0) {
      this.applyValidationRules();
      this.createValuationRules = true;
    }
    this._cdRef.detectChanges();
  }

  getCurrentForm(index: number) {
    return this.listaForm.toArray()[index];
  }

  isNewFieldError(fieldCod, index): boolean {
    if(!this.listaForm) return;
    const form = this.listaForm.toArray()[index];
    return (
      form &&
      form.controls &&
      form.controls[fieldCod] &&
      form.submitted &&
      !form.controls[fieldCod].valid
    );
  }

  triggerChangeEvent(value, form: NgForm, controlName) {
    form.controls[controlName].setValue(value);
    this.calendarChange.emit(true);
  }

  isAllValid(): boolean {
    if (this.listaForm) {
      for (const form of this.listaForm.toArray()) {
        if (form.invalid) {
          return false;
        }
      }
      return true;
    } else {
      return false;
    }
  }

  getFieldsAtOnce(): any {
    const values = {};
    for (const aform of this.listaForm.toArray()) {
      for (const key in aform.controls) {
        if (key) {
          values[key] = aform.controls[key].value;
        }
      }
    }
    return values;
  }

  getFormsWithModel(): any {
    return this.model;
  }

  getModelForField(formCode, fieldCode) {
    return this.model[formCode][fieldCode];
  }

  protected applyValidationRules() {
    let index1 = 0;
    if(!this.listaForm) return;
    for (const htmlForm of this.listaForm.toArray()) {
      this.validationForm[index1] = htmlForm.form;
      let validators = [];
      let index2 = 0;
      for (const form of this.applicationForms) {
        if (index1 === index2) {
          for (const section of form.sections) {
            for (const row of section.rows) {
              for (const field of row.fields) {
                if (field.validationRule) {
                  if (field.type === 'Number' && !field.validationRule.includes('Number')) {
                    field.validationRule = this.formatNumberEvaluation(field.validationRule);
                  }
                  field.validationRule = `!(${field.renderIf}) || \$${field.code} === null || \$${field.code} === '' || (${field.validationRule})`;
                  validators.push(this.evalValidationRule(section.formCode, field.validationRule));
                }
              }
            }
          }
        }
        index2++;
      }
      this.validationForm[index1].setValidators(validators);
      validators = [];
      index1++;
    }
  }
}