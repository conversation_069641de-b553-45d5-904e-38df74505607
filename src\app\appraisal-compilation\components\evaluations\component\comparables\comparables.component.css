.uc-table tr td, .uc-table tr th, .table-fixed tr td, .table-fixed tr th {
  text-align: center;
}

/* .uc-table tr td:nth-child(-n + 2),
.uc-table tr th:nth-child(-n + 2),
.table-fixed tr td:nth-child(-n + 2),
.table-fixed tr th:nth-child(-n + 2) {
  text-align: left;
} */
.uc-table tr td:first-child,
.uc-table tr th:first-child {
  text-align: left;
}

.uc-table tr td input {
  padding-right: 20px;
  text-align: right;
}

.col-sm-8 {
  padding-right: 0;
}

.col-sm-4 {
  padding-left: 0;
}

.input-percentage {
  background: aliceblue;
  border-collapse: lightblue;
}

.separator {
  height: 50px ;
}

.result-separator-row {
  border: 0;
  border-top: 1px solid #666464;
}

.percentage-label {
  position: absolute;
  top: 11px;
  right: 20px;
}

.outRange {
  color: red;
}
