import {
  Component,
  OnInit,
  Input,
  ViewChild,
  Output,
  EventEmitter,
  OnDestroy,
  AfterViewInit,
  AfterContentInit,
} from '@angular/core';
import { NgForm } from '@angular/forms';
import { Subscription } from 'rxjs/Subscription';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';

@Component({
  selector: 'app-construction-site-security',
  templateUrl: './construction-site-security.component.html',
  styleUrls: ['./construction-site-security.component.css']
})
export class ConstructionSiteSecurityComponent
  implements OnInit, OnDestroy, AfterViewInit, AfterContentInit {
  @Input() pageContent: any;
  @Input() positionId: string;
  @Input() assetId: string;
  @Input() domainResp: any; // UBZ_DOM_PERSON_ROLE
  @Output() statusChange: EventEmitter<boolean> = new EventEmitter<boolean>();

  @ViewChild(NgForm) form: NgForm;
  pageIsValid: boolean;
  deleteModalIsOpen = false;
  modalType: string; // indica che tipo di modale aprire tra aggiunta e modifica
  modalIsOpen: boolean = false;  // boolean che gestisce visualizzazione modal aggiunta/modifica
  public todayDate = new Date();
  public startDate = new Date(1800, 1, 1);
  private subscription: Subscription;
  public minSelectableDate: Date = new Date(1850, 1, 1);

  selectedProperty: any;
  selectedPropertyIndex;
  personRoleDomain: any[];
  public requiredFields: boolean;

  constructor(
    public _accordionAPFService: AccordionAPFService,
  ) {}

  ngOnInit() {
    if(this.domainResp) {
      this.personRoleDomain = this.domainResp;
    }

  }

  ngAfterViewInit() {
    this.form.statusChanges.subscribe(() => {
      this.checkIfComplete();
    });
  }

  ngOnDestroy() {
    if (this.subscription && !this.subscription.closed) {
      this.subscription.unsubscribe();
    }
  }

  ngAfterContentInit() {
    if (this.pageContent) {
      if (!this.pageContent.securityData['DIL']) {
        this.pageContent.securityData['DIL'] = {};
      }
      if (!this.pageContent.securityData['ASL']) {
        this.pageContent.securityData['ASL'] = {};
      }
      if (!this.pageContent.securityData['IPL']) {
        this.pageContent.securityData['IPL'] = {};
      }
      if (!this.pageContent.securityData['DPS']) {
        this.pageContent.securityData['DPS'] = {};
      }
    }
  }

  checkIfComplete() {
    setTimeout(() => {
      const prevValidStatus: boolean = this.pageIsValid;
      this.pageIsValid = this.form.valid;
      if (prevValidStatus !== this.pageIsValid) {
        this.statusChange.emit(this.pageIsValid);
      }
    }, 0);
  }

  delete(index) {
    this.selectedPropertyIndex = index;
    this.deleteModalIsOpen = true;
  }

  closeDeleteModal() {
    this.selectedPropertyIndex = null;
    this.deleteModalIsOpen = false;
    this.checkDependency();
  }

  submitDeleteModal() {
    const newProperties = [];
    for (const index in this.pageContent.securityStaff) {
      if (index !== String(this.selectedPropertyIndex)) {
        newProperties.push(this.pageContent.securityStaff[index]);
      }
    }
    this.pageContent.securityStaff = newProperties;
    this.closeDeleteModal();
  }

  // Apre la modale specificandone il modalType (add / modify)
  // Property e Index parametri opzionali, passati in caso di modale di modifica.
  openModal(modalType: string, property?: any, index?: number) {
    // Se siamo in modifica, si recupera l'oggetto esatto tramite index, altrimenti si passa un nuovo oggetto per l'aggiunta    
    if (property) {
      this.selectedPropertyIndex = index;
      this.selectedProperty = Object.assign({}, property);
    } else {
      this.selectedPropertyIndex = null;   
      this.selectedProperty = {
        buildSiteId: null,
        personId: null,
        personRole: '',
        firstName: null,
        lastName: null,
        email: null,
        phoneNumber: null
      };   
    }
    this.modalType = modalType;    
    this.modalIsOpen = true;
  }

  // Metodo scatenato dal submit all'interno della modal per aggiunta/modifica
  submitModal(modelObj) {
    switch (this.modalType) {
      case 'add':
        if (!this.pageContent.securityStaff) {
          this.pageContent.securityStaff = [];
        }
        this.pageContent.securityStaff.push(modelObj.property);
        this.checkDependency();
        break;
      case 'modify':
        this.pageContent.securityStaff[modelObj.index] = modelObj.property;
        this.selectedProperty = null;
        this.selectedPropertyIndex = null;
        break;
    }
    this.modalIsOpen = false;
  }

  public checkDependency() {
    if (
      this.pageContent.securityData['DIL'].securDataNum ||
      this.pageContent.securityData['DIL'].securDataDate ||
      this.pageContent.securityData['DIL'].protocol ||
      this.pageContent.securityData['ASL'].securDataNum ||
      this.pageContent.securityData['ASL'].securDataDate ||
      this.pageContent.securityData['ASL'].protocol ||
      this.pageContent.securityData['IPL'].securDataNum ||
      this.pageContent.securityData['IPL'].securDataDate ||
      this.pageContent.securityData['IPL'].protocol ||
      this.pageContent.securityData['DPS'].securDataNum ||
      this.pageContent.securityData['DPS'].securDataDate ||
      this.pageContent.securityData['DPS'].protocol ||
      this.pageContent.securityStaff.length > 0
    ) {
      this.requiredFields = true;
      this.pageContent.planningStartDate = new Date();
      this.pageContent.planningEndDate = new Date();
      this.pageContent.planningStartProt = '';
    } else {
      this.requiredFields = false;
      this.pageContent.planningStartDate = null;
      this.pageContent.planningEndDate = null;
      this.pageContent.planningStartProt = null;
    }
  }
}
