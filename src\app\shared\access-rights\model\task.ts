export class Task {
  taskId: number;
  taskCod: string;
  positionId: string;
  queueCod: string;
  creationDate: number;
  creationUser: string;
  outcomeCod: number;
  outcomeUser: string;
  outcomeDate: number;
  slaDueDate: number;
  timeout: number;
  lockingUser: string;
  branch: string;
  taskType: string;
  externalTaskId: string;
  translationCod: string;
  processCod: string;
  statusCod: string;
  ndg: string;
  headingNdg: string;
  attributeCod: string;
  statusDescription: string;
  queueDesc: string;
}
