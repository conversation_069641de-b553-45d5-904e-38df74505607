<fieldset [disabled]="!_landingService.positionLocked ">
  <accordion class="panel-group" id="accordion">
    <fieldset [disabled]="_landingService.isLockedTask[currentTask]">
      <app-accordion-application-form [overwriteAPFData]="true" [positionId]="positionId" [idCode]="positionId" page="INFO_DATI_GENERALI"
        [formDisabled]="_landingService.isLockedTask[currentTask]">
      </app-accordion-application-form>

         <app-physical-risk [positionId]="positionId"
 (formDataCollection)="passData($event)" (activeForm)="checkTrafficLights($event)"></app-physical-risk>

      <app-job-assignment [positionId]="positionId" [socId]="jobAssignmentData.socId" (experts)="saveExperts($event)" (formStatusChange)="jobAssignmentFormStatusChanges($event)"
        *ngIf="!(isTemplateLight && _landingService.posSegment === 'IND')"></app-job-assignment>
    </fieldset>
    <fieldset class="validation-sect">
      <app-appraisal-template-validation *ngIf="_positionService.isInternalSecondOpinion && !(isTemplateLight && _landingService.posSegment === 'IND')"
        [positionId]="positionId" templateName="INFO_DATI_GENERALI"></app-appraisal-template-validation>
    </fieldset>
  </accordion>
  <app-navigation-footer showSaveDraft="true"  [footerClass]="menuService.footerProperty" [saveIsEnable]="saveIsEnabled" [showCancelButton]="false" 
    (saveButtonClick)="saveData()" [saveDraftCallback]="saveDraftCallback" (closeDraftButtonClick)="goToGenericTask()" [activeTaskCode]="currentTask"></app-navigation-footer>
</fieldset>
