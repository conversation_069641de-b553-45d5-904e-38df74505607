import { Component, OnInit, OnD<PERSON>roy, Inject } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { IAppConstants, APP_CONSTANTS } from '../../app.constants';
import { ConfigurationService } from '../service/configuration.service';
import { Subscription } from 'rxjs/Subscription';
import { SocietyConf } from '../configuration.models';
import { TranslateService } from '@ngx-translate/core';
import { MessageService } from '../../shared/messages/services/message.service';

@Component({
  selector: 'app-configuration',
  templateUrl: './configuration.component.html',
  styleUrls: ['./configuration.component.css'],
  providers: [ConfigurationService],
})
export class ConfigurationComponent implements OnInit, OnDestroy {
  public configurationType: string;
  configurations: any;
  activeList: SocietyConf[];
  inactiveList: SocietyConf[];
  configurationInfo: any;
  length: number;
  private _subscription: Subscription;
  confirmModalOpen = false;
  showInfoPopup: boolean = true;

  constructor(
    public _configurationService: ConfigurationService,
    private _activatedRoute: ActivatedRoute,
    private messageService: MessageService,
    private translateService: TranslateService,
    private _router: Router,
    @Inject(APP_CONSTANTS) public _constants: IAppConstants
  ) {}

  ngOnInit() {
    if (sessionStorage.getItem('alreadyLoaded') === 'true') {
      this.showInfoPopup = false;
    }
    this.refreshPage();
  }

  ngOnDestroy() {
    if (this._subscription && !this._subscription.closed) {
      this._subscription.unsubscribe();
    }
  }

  public saveConfigurationRules(): void {
    if (
      this.configurationType ===
      this._constants.ConfigurationType.INDIVIDUAL_ASSIGNMENT
    ) {
      this.configurations.activeList = this.activeList;
      this.configurations.configurationInfo = this.configurationInfo;
      this.configurations.inactiveList = this.inactiveList;
    }
    this._configurationService
      .saveConfigurationRules(this.configurationType, this.configurations)
      .subscribe((res) => {
        this.confirmModalOpen = true;
      });
  }

  saveOnIndividualAssignment(event) {
    this.activeList = event;
    this.saveConfigurationRules();
  }

  public hide(): void {
    this.confirmModalOpen = false;
    this._activatedRoute.params.subscribe((params: Params) => {
      if (params['configurationCode'] === 'IND') {
        this.redirectTo('configuration/IND');
      } else this.refreshPage();
    });
  }

  public refreshPage(): void {
    this._subscription = this._activatedRoute.params
      .switchMap((params: Params) => {
        this.configurationType = params['configurationCode'];
        return this._configurationService.getConfigurationRules(
          this.configurationType
        );
      })
      .subscribe((res) => {
        this.configurations = res;
        this._configurationService.undoIsEnabled = false;
        if (
          this.configurationType ===
          this._constants.ConfigurationType.INDIVIDUAL_ASSIGNMENT
        ) {
          this.configurationInfo = res.configurationInfo;
          this.inactiveList = res.inactiveList;
          this.activeList = res.activeList;
          this._configurationService.configurationInfoStore =
            res.configurationInfo;
          if (
            this.configurationInfo &&
            !this.configurationInfo.saved &&
            this.showInfoPopup
          ) {
            this.messageService.showInfo(
              this.translateService.instant('UBZ.SITE_CONTENT.11111010110'),
              this.translateService.instant('UB1.TITLE.INFO')
            );
          }
          sessionStorage.setItem('alreadyLoaded', null);
        } else {
          this.length = this.configurations.length;
        }
      });
  }

  public refreshPageAddedDeleted(refreshPageEvent): void {
    sessionStorage.setItem('alreadyLoaded', 'true');
    if (refreshPageEvent) {
      this.redirectTo('configuration/IND');
    }
  }
  redirectTo(uri: string) {
    this._router
      .navigateByUrl('/', { skipLocationChange: true })
      .then(() => this._router.navigate([uri]));
  }
  undoModifications() {
    this._configurationService.undoConfiguration().subscribe(() => {
      this.refreshPageAddedDeleted(true);
    });
  }

  restoreModifications() {
    this._configurationService.restoreConfiguration().subscribe((res) => {
      this.refreshPageAddedDeleted(true);
    });
  }
  public goToDashboard(): void {
    this._router.navigateByUrl('index');
  }
}
