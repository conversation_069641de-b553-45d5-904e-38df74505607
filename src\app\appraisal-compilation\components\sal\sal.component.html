<fieldset [disabled]="!_landingService.positionLocked">
  <fieldset [disabled]="_landingService.isLockedTask[currentTask]">
    <accordion class="panel-group" id="accordion">
      <app-overall-job-statistics *ngIf="appraisalType !== constants.appraisalTypes.BENI_INDUSTRIALI &&
      mobileAppraisalType !== constants.appraisalTypes.SHIPPING &&
      mobileAppraisalType !== constants.appraisalTypes.AEREOMOBILE" [tableRows]="salComplessivo.totalActivCost"></app-overall-job-statistics>

      <app-cost-prevision-tab [disable]="_landingService.isLockedTask[currentTask]" [salComplessivo]="salComplessivo" [appraisalType]="appraisalType"
        [costiDiRealizzazione]="costiDiRealizzazione" [mobileAppraisalType]="mobileAppraisalType"></app-cost-prevision-tab>

      <app-end-job-evaluation *ngIf="appraisalType === constants.appraisalTypes.EDILIZIA_AGEVOLATA" [list]="endJobEvaluationList"></app-end-job-evaluation>

      <app-construction-info *ngIf="appraisalType === constants.appraisalTypes.BENI_INDUSTRIALI" [salComplessivo]="salComplessivo"></app-construction-info>

      <app-shipping-cost-table *ngIf="mobileAppraisalType === constants.appraisalTypes.SHIPPING || mobileAppraisalType === constants.appraisalTypes.AEREOMOBILE"
        [model]="salComplessivo"></app-shipping-cost-table>

      <app-appraisal-template-validation *ngIf="_positionService.isInternalSecondOpinion" [positionId]="positionId" templateName="SAL"></app-appraisal-template-validation>
    </accordion>
  </fieldset>

  <app-navigation-footer showSaveDraft="true" showPrevious="true" confirmButtonString="{{ appraisalType !== 'MUT' ? ('UBZ.SITE_CONTENT.110001110' | translate) : '' }}"
    [showCancelButton]="false" (saveButtonClick)="saveData()" (previousButtonClick)="goToPreviousTask()" (closeDraftButtonClick)="goToGenericTask()"
    [saveIsEnable]="isPageValid()" [saveDraftCallback]="saveDraftCallback" [activeTaskCode]="currentTask">
  </app-navigation-footer>
</fieldset>
