import { Injectable, Inject, EventEmitter } from '@angular/core';
import { Http, Response, Headers } from '@angular/http';
import { ReplaySubject } from 'rxjs/ReplaySubject';
import { Observable } from 'rxjs/Observable';
import { Router, ActivatedRoute } from '@angular/router';
import { WizardElement } from '../../../shared/wizard/model/wizard-element';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';

// Services
import { ApiService } from '../../../shared/api/api.service';
import { WizardService } from '../../../shared/wizard/services/wizard.service';

@Injectable()
export class LandingService {
  isLockedTask: {} = {};
  wizardRefreshed = new EventEmitter();
  positionLocked = false;
  // codice identificativo processo di origine, in base al valore contenuto può forzare la disabilitazione dei componenti del wizard
  originationProcess: string;
  // Codice identificativo tipologia NDG (Corporate = COR / Individual = IND)
  posSegment: string;

  constructor(
    private http: Http,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private apiService: ApiService,
    private wizardService: WizardService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) { }

  getLandingPage(
    positionId: string,
    wizardCode: string,
    wizard: WizardElement[],
    originationProcess: string,
    bpmTaskId: string,
    bpmTaskCod: string,
    readOnly: string,
    requestOnAir: boolean,
    migrAddInfo: string,
    macroProcess: string,
    statusCode: string
  ): string {
    let landingPage: string;

    for (const j in wizard) {
      if (
        wizard.hasOwnProperty(j) &&
        wizard[j] &&
        wizard[j].taskList &&
        wizard[j].taskList.taskCod === `UBZ-${wizardCode}`
      ) {
        if (
          (wizard[j].taskList.outcomeCod === '2' &&
            (wizardCode === this.constants.wizardCodes.SIM ||
              wizardCode === this.constants.wizardCodes.REQ)) ||
          statusCode === 'RIC-CON' ||
          statusCode === 'RIC-ANN' ||
          originationProcess === 'MIG' ||
          originationProcess === 'FRA' ||
          migrAddInfo === 'MIG' ||
          requestOnAir === true ||
          macroProcess === 'NPE' ||
          macroProcess === 'REF' ||
          macroProcess === 'MLP' ||
          macroProcess === 'MLC'
        ) {
          landingPage = `/wizard-detail/${wizardCode}/${positionId}`;
          break;
        } else if (
          wizard[j].taskList.outcomeCod === '3' ||
          wizard[j].taskList.outcomeCod === '2'
        ) {
          let firstActiveChild = -1;
          let index = 0;
          for (const i in wizard[j].childs) {
            if (
              wizard[j].childs.hasOwnProperty(i) &&
              wizard[j].childs[i] &&
              wizard[j].childs[i].taskList
            ) {
              if (
                (wizard[j].childs[i].taskList.outcomeCod === '2' ||
                  wizard[j].childs[i].taskList.outcomeCod === '3') &&
                firstActiveChild === -1
              ) {
                firstActiveChild = index;
              }
              if (wizard[j].childs[i].taskList.outcomeCod === '3') {
                if (bpmTaskId && bpmTaskCod) {
                  landingPage = `/task-wizard/${wizardCode}/${positionId}/${bpmTaskId}/${bpmTaskCod}/${readOnly}/${this.constants.landingMap[
                    wizard[j].childs[i].taskList.taskCod
                    ]
                    }`;
                } else {
                  landingPage = `/wizard/${wizardCode}/${positionId}/${this.constants.landingMap[
                    wizard[j].childs[i].taskList.taskCod
                    ]
                    }`;
                }
                break;
              }
              index++;
            }
          }
          if (!landingPage) {
            if (bpmTaskId && bpmTaskCod) {
              landingPage = `/task-wizard/${wizardCode}/${positionId}/${bpmTaskId}/${bpmTaskCod}/${readOnly}/${this.constants.landingMap[
                wizard[j].childs[firstActiveChild].taskList.taskCod
                ]
                }`;
            } else {
              landingPage = `/wizard/${wizardCode}/${positionId}/${this.constants.landingMap[wizard[j].childs[0].taskList.taskCod]
                }`;
            }
          }
          break;
        }
      }
    }
    return landingPage;
  }

  getNextTask(
    positionId: string,
    currentTask: string,
    wizardCode: string
  ): Observable<string> {
    positionId = encodeURIComponent(positionId);
    currentTask = encodeURIComponent(currentTask);
    wizardCode = encodeURIComponent(wizardCode);
    const url = `/UBZ-ESA-RS/service/wizard/v1/wizards/${positionId}/next/${wizardCode}/${currentTask}`;
    return this.http.get(url).map((resp: Response) => resp.text());
  }

  goToPreviousTask(positionId, wizardCode, activatedRoute) {
    let previousTaskCode = this.wizardService.getPreviousValidTask(
      positionId,
      wizardCode
    );
    this.wizardService
      .invalidateTask(positionId, wizardCode, previousTaskCode)
      .subscribe(res => {
        this.refreshWizard();
        this.goToTask(previousTaskCode, activatedRoute);
      });
  }

  goToPreviousTargetedTask(targetTask, positionId, wizardCode, activatedRoute) {
    this.wizardService
      .invalidateTask(positionId, wizardCode, targetTask)
      .subscribe(res => {
        this.refreshWizard();
        this.goToTask(targetTask, activatedRoute);
      });
  }

  goNextPage(positionId, currentTask, wizardCode, activatedRoute) {
    this.getNextTask(positionId, currentTask, wizardCode).subscribe(res => {
      this.refreshWizard();
      this.router.navigate([`../${this.constants.landingMap[res]}`], {
        relativeTo: activatedRoute
      });
    });
  }

  goToGenericTask(positionId, taskId, taskCod) {
    this.router.navigateByUrl(
      `generic-task/${positionId}/${taskId}/${taskCod}`
    );
  }

  goToTask(taskCode: string, activatedRoute: ActivatedRoute) {
    this.router.navigate([`../${this.constants.landingMap[taskCode]}`], {
      relativeTo: activatedRoute
    });
  }

  cancelPosition(positionId: string, wizardCode: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const urlMap = {
      WSIM: `/UBZ-ESA-RS/service/simulation/v1/simulation/cancelSimulation/${positionId}`,
      WRPE: `/UBZ-ESA-RS/service/reqPer/v1/reqAppraisals/${positionId}`
    };
    return this.http
      .delete(urlMap[wizardCode])
      .map((resp: Response) => resp.text());
  }

  refreshWizard() {
    this.wizardRefreshed.emit();
  }

  getTaskListFromUMF(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const headers = new Headers({
      'Cache-Control': 'no-cache',
      Pragma: 'no-cache',
      Expires: 'Sat, 01 Jan 2000 00:00:00 GMT',
      'Content-type': 'application/json'
    });
    const url = `/UBZ-ESA-RS/service/umfService/wizard/V1/tasks/current?positionId=UBZ${positionId}`;
    return this.http
      .get(url, { withCredentials: true, headers: headers })
      .map(res => res.json());
  }

  // Recupera la lista dei task, se il task corrente per il positionId è completato ne setta la property isLockedTask per disabilitarlo
  // Nel caso in cui originationProcess === "EMP", il wizard di richiesta è in sola lettura e si setta immediatamente isLockedTask
  checkIfCompletedTask(positionId: string, taskId: string): void {
    if (
      this.originationProcess === 'EMP' ||
      this.originationProcess === 'UB6'
    ) {
      this.isLockedTask[taskId] = true;
      return;
    }
    this.getTaskListFromUMF(positionId).subscribe(taskList => {
      for (const task of taskList) {
        if (task.taskCod === taskId) {
          delete this.isLockedTask[taskId];
          return;
        }
      }
      this.isLockedTask[taskId] = true;
    });
  }

  saveDraft(
    positionId: string,
    staticSave: string,
    apfList?: any,
    page?: string,
    objectId?: string
  ): Observable<any> {
    const input = {
      positionId: positionId,
      objectId: objectId,
      page: page,
      sections: apfList,
      staticSave: staticSave
    };
    const url = '/UBZ-ESA-RS/service/appraisal/v1/appraisals/partialSave';
    return this.http.post(url, input).map((resp: Response) => resp.text());
  }
}
