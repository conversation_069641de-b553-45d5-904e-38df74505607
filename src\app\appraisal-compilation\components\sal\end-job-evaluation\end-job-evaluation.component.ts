import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { EndJobEvaluationModel } from '../model/sal.models';
import { AccordionAPFService } from '../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';

@Component({
  selector: 'app-end-job-evaluation',
  templateUrl: './end-job-evaluation.component.html',
  styleUrls: ['./end-job-evaluation.component.css']
})
export class EndJobEvaluationComponent implements OnInit {
  @ViewChild(NgForm) f: NgForm;
  @Input() list: EndJobEvaluationModel[] = [];

  constructor(public _accordionAPFService: AccordionAPFService) {}

  ngOnInit() {}

  calculateTotal(): number {
    if (!this.list) {
      return 0;
    }
    let sum = 0;
    for (const item of this.list) {
      sum += item.maxEuroTransfer;
    }
    return sum;
  }

  getAllObjects(): EndJobEvaluationModel[] {
    return this.list;
  }

  isValid(): boolean {
    if (!this.f) {
      return false;
    }
    return this.f.valid;
  }
}
