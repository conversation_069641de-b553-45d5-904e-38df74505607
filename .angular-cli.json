{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "project": {"name": "ubz"}, "apps": [{"root": "src", "outDir": "dist", "assets": ["assets", "favicon.ico", {"glob": "**/*", "input": "assets/WEB-INF", "output": "WEB-INF"}], "index": "index.html", "main": "main.ts", "polyfills": "polyfills.ts", "test": "test.ts", "tsconfig": "tsconfig.app.json", "testTsconfig": "tsconfig.spec.json", "prefix": "app", "styles": ["../node_modules/bootstrap/dist/css/bootstrap.min.css", "../node_modules/font-awesome/css/font-awesome.min.css", "../node_modules/ng2-toastr/bundles/ng2-toastr.min.css", "../node_modules/slick-carousel/slick/slick.css", "../node_modules/slick-carousel/slick/slick-theme.css", "../node_modules/dragula/dist/dragula.min.css", "styles.css", "custom.css"], "scripts": ["../node_modules/jquery/dist/jquery.js", "../node_modules/slick-carousel/slick/slick.js"], "environmentSource": "environments/environment.ts", "environments": {"dev": "environments/environment.ts", "prod": "environments/environment.prod.ts"}}], "e2e": {"protractor": {"config": "./protractor.conf.js"}}, "lint": [{"project": "src/tsconfig.app.json"}, {"project": "src/tsconfig.spec.json"}, {"project": "e2e/tsconfig.e2e.json"}], "test": {"karma": {"config": "./karma.conf.js"}}, "defaults": {"styleExt": "css", "component": {}}}