import {
  Component,
  AfterContentInit,
  Input,
  Output,
  EventEmitter,
  ViewChild
} from '@angular/core';
import { NgForm } from '@angular/forms';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { QteResponse } from '../../model/qte.models';

@Component({
  selector: 'app-total-costs',
  templateUrl: './total-costs.component.html',
  styleUrls: ['./total-costs.component.css']
})
export class TotalCostsComponent implements AfterContentInit {
  @Input() model: QteResponse;
  @Output() formStateChanged: EventEmitter<void> = new EventEmitter<void>();
  @ViewChild(NgForm) private form: NgForm;

  constructor(public _accordionAPFService: AccordionAPFService) {}

  ngAfterContentInit() {
    setTimeout(() => {
      this.form.statusChanges.subscribe(() => {
        this.formStateChanged.emit();
      });
    }, 0);
  }

  public isSectionValid(): boolean {
    if (this.form) {
      return this.form.valid;
    }
  }
}
