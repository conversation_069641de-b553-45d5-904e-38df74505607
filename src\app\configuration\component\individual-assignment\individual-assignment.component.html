<div class="container">
  <form [formGroup]="societiesForm" (ngSubmit)="save()">
    <div class="cards-container">
      <div formArrayName="societies" class="societies-array">
        <div *ngFor="let soc of societiesArrayControls.controls; let i = index">
          <div [formGroupName]="i">
            <app-individual-assignment-card [singleSociety]="societiesArrayControls.controls[i]"
              [prioritiesList]="getPriorities(activeList)" (deleteRefresh)="refreshIfDeleted($event)">
            </app-individual-assignment-card>
          </div>
        </div>
      </div>
    </div>
  </form>
  <button type="button" class="btn btn-empty" id="addSocietyBtn" (click)="openAggiungiModalIfCase()">
    <i class="icon-add"></i> {{ 'UBZ.SITE_CONTENT.111100001' | translate }}
  </button>
</div>
<app-aggiungi-modal *ngIf="aggiungiModalIsOpen === true" [isOpen]="aggiungiModalIsOpen"
  (modalClose)="closeAggiungiModal($event)" [inactiveList]="inactiveList" [activeList]="activeList">
</app-aggiungi-modal>
