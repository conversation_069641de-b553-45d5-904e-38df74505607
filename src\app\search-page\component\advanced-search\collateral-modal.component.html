<div *ngIf="isOpen" class="modal fade" id="id-garanzie" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
  bsModal #collateralsBox="bs-modal" (onHidden)="closeModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.11110011' | translate }}</h2>
        <button type="button" class="close pull-right" data-dismiss="modal" aria-label="Close" (click)="closeModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <table class="uc-table">
              <thead>
                <tr>
                  <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10011101' | translate }}</th>
                  <th *ngIf="searchType==='PE'" scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10100111001' | translate }}</th>
                  <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10010101' | translate }}</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let c of collaterals">
                  <td data-label="Progressivo">
                    <span *ngIf="searchType !== 'PE'">{{c.progCollateral}}</span>
                    <span *ngIf="searchType === 'PE'">{{c.jointCod}}</span>
                  </td>
                  <td *ngIf="searchType === 'PE'"  data-label="Numero asset">
                    <span>{{c.assetNumber}}</span>                    
                  </td>
                  <td data-label="Forma tecnica">
                    <span *ngIf="searchType !== 'PE'">{{c.collateralTecForm}} - {{c.collateralDesc}}</span>
                    <span *ngIf="searchType === 'PE'">{{c.collatTecForm}} - {{c.collatDesc}}</span>
                  </td>

                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>