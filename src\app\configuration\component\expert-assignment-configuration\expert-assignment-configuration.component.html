<div class="row step-navigation">
  <div class="col-sm-12 btn-set">
    <ng-container *ngIf="!editAction">
      <ng-container *appAuthKey="'UBZ_CONFIGURATION.EXPERT_ASSIGNMENT_NEW'">
        <button type="button" class="btn btn-empty waves-effect waves-secondary pull-right" (click)="goToNewRule()" [disabled]="editAction">
          <i class="icon-add"></i> {{'UBZ.SITE_CONTENT.1010100100' | translate }}
        </button>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="editAction">
      <ng-container *appAuthKey="'UBZ_CONFIGURATION.EXPERT_ASSIGNMENT_ABORT'">
        <button class="btn btn-empty waves-effect waves-secondary pull-right" (click)="abortEditAction()" type="button">
          <i aria-hidden="true" class="fa fa-times"></i> {{'UBZ.SITE_CONTENT.100000' | translate }}
        </button>
      </ng-container>
    </ng-container>

  </div>
</div>

<div class="row" *ngIf="!editAction">
  <div class="col-sm-12">
    <h3>{{'UBZ.SITE_CONTENT.101101001' | translate }}</h3>
    <table class="uc-table">
      <thead>
        <tr>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1010001' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1010100101' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1010100110' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.11101101' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.100101' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.11110111' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1011010' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1010011101' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1110101' | translate }}</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngIf="configurations">
          <ng-container *ngFor="let item of configurations">
            <tr>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1010001' | translate }}">{{ macroProcessTypes && macroProcessTypes[item.macroProcess] ? (macroProcessTypes[item.macroProcess].translationCod
                | translate) : '' }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1010100101' | translate }}">{{ posSegments && posSegments[item.posSegment] ? (posSegments[item.posSegment].translationCod | translate)
                : '' }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1010100110' | translate }}">{{ item.locationArea }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.11101101' | translate }}">{{ appraisalTypes && appraisalTypes[item.appraisalType] ? (appraisalTypes[item.appraisalType].translationCod
                | translate) : '' }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.100101' | translate }}">{{ resItemTypes && resItemTypes[item.resItemType] ? (resItemTypes[item.resItemType].translationCod | translate)
                : '' }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.11110111' | translate }}">{{ item.resItemCategory }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1011010' | translate }}">{{ item.credlineAmount | currency:'EUR':true:'1.2-2' }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1010011101' | translate }}">
                <i class="icon-check" [ngClass]="{'icon-lightblue': item.activeFlag, 'light-grey': !item.activeFlag }"></i>
              </td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1110101' | translate }}">
                <ng-container *appAuthKey="'UBZ_CONFIGURATION.EXPERT_ASSIGNMENT_MODIFY'">
                  <button type="button" class="btn btn-empty" (click)="modifySelectedRule(item)">
                    <i class="fa fa-pencil-square-o"></i>
                  </button>
                </ng-container>
                <ng-container *appAuthKey="'UBZ_CONFIGURATION.EXPERT_ASSIGNMENT_REMOVE'">
                  <button type="button" class="btn btn-empty" (click)="removeRule(item)">
                    <i class="fa fa-trash-o" aria-hidden="true"></i>
                  </button>
                </ng-container>
                <button type="button" class="btn btn-empty" (click)="selectRuleToExpand(item)">
                  <i class="fa" [ngClass]="{'fa-plus-square-o': !(ruleToExpand.ruleId === item.ruleId), 'fa-minus-square-o': (ruleToExpand.ruleId === item.ruleId)}"
                    aria-hidden="true"></i>
                </button>
              </td>
            </tr>
            <tr *ngIf="ruleToExpand && (ruleToExpand.ruleId === item.ruleId)">
              <td colspan="9" class="row">
                <div class="col-sm-12 text-left">
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.11110111' | translate }}: </strong>
                    {{ expiredCategoryTypes && expiredCategoryTypes[item.resItemCategory] ? (expiredCategoryTypes[item.resItemCategory].translationCod | translate)
                    : '' }}
                  </p>
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.1010101000' | translate }}: </strong>
                    {{ appraisalOwners && appraisalOwners[item.appraisalOwner] ? (appraisalOwners[item.appraisalOwner].translationCod | translate)
                    : '' }}
                  </p>
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.1010110' | translate }}: </strong>
                    <i class="icon-check" [ngClass]="{'icon-lightblue': item.externalAppraisalFlag, 'light-grey': !item.externalAppraisalFlag}"></i>
                  </p>
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.1010101001' | translate }}: </strong>
                    {{ expertTypes && expertTypes[item.expertType] ? (expertTypes[item.expertType].translationCod | translate) : '' }}
                  </p>
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.110000110' | translate }}: </strong>
                    {{ getAssignedExpertHeading(item.expertSocAnagId) }}
                  </p>
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.1010101010' | translate }}: </strong>
                    {{ item.priority }}
                  </p>
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.1010101111' | translate }}: </strong>
                    {{ item.bornDate | date:'shortDate' }} - {{ item.deadDate | date:'shortDate' }}
                  </p>
                </div>
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </table>
  </div>
</div>

<ng-container *ngIf="editAction">
  <form #f="ngForm" (ngSubmit)="confirmEditAction()">
    <div class="row">
      <div class="col-sm-12">
        <h3>{{'UBZ.SITE_CONTENT.1010100111' | translate }}</h3>
      </div>
    </div>
    <div class="row">
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1010001' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="selectedRule.macroProcess" required name="macroProcess">
            <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let row of (macroProcessTypes | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1010100101' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="selectedRule.posSegment" required name="posSegment">
            <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let row of (posSegments | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1010100110' | translate }}*</label>
        <input type="text" name="locationArea" [(ngModel)]="selectedRule.locationArea" class="form-control" required>
      </div>
    </div>
    <div class="row">
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.11101101' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="selectedRule.appraisalType" required name="appraisalType">
            <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let row of (appraisalTypes | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.100101' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="selectedRule.resItemType" required name="resItemType" (ngModelChange)="calculateNewItemCategories()">
            <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let row of (resItemTypes | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.11110111' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="selectedRule.resItemCategory" required name="resItemCategory">
            <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let row of (categoryTypes | domainMapToDomainArray | sortCategoryType)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1011010' | translate }}*</label>
        <input appOnlyNumbers type="text" name="credlineAmount" [(ngModel)]="selectedRule.credlineAmount" class="form-control" required>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1010101000' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="selectedRule.appraisalOwner" required name="appraisalOwner">
            <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let row of (appraisalOwners | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label class="hidden-sm"></label>
        <div class="custom-checkbox">
          <input div="custom-checkbox" type="checkbox" id="externalAppraisalFlag" name="externalAppraisalFlag" [(ngModel)]="selectedRule.externalAppraisalFlag">
          <label for="externalAppraisalFlag" data-toggle="tooltip" attr.title="{{'UBZ.SITE_CONTENT.1010110' | translate }}">{{'UBZ.SITE_CONTENT.1010110' | translate }}</label>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1010101001' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="selectedRule.expertType" required name="expertType">
            <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let row of (expertTypes | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.110000110' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="selectedRule.expertSocAnagId" required name="expertSocAnagId">
            <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let row of expertList" value="{{ row.idSocPer }}">{{ row.heading }}</option>
          </select>
        </div>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label class="hidden-sm"></label>
        <div class="custom-checkbox">
          <input div="custom-checkbox" type="checkbox" id="activeFlag" name="activeFlag" [(ngModel)]="selectedRule.activeFlag">
          <label for="activeFlag" data-toggle="tooltip" attr.title="{{'UBZ.SITE_CONTENT.1010011101' | translate }}">{{'UBZ.SITE_CONTENT.1010011101' | translate }}</label>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-4 col-sm-12 form-group">
        <label for="bornDate">{{'UBZ.SITE_CONTENT.1010100001' | translate }}*</label>
        <app-calendario [name]="'bornDate'" [(ngModel)]="selectedRule.bornDate" [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
          [required]="true">
        </app-calendario>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label for="deadDate">{{'UBZ.SITE_CONTENT.1010100010' | translate }}*</label>
        <app-calendario [name]="'deadDate'" [(ngModel)]="selectedRule.deadDate" [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
          [required]="true">
        </app-calendario>
      </div>
      <div class="col-md-4 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1010101010' | translate }}*</label>
        <input appOnlyNumbers type="text" name="priority" [(ngModel)]="selectedRule.priority" class="form-control" required>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12 btn-set">
        <ng-container *appAuthKey="'UBZ_CONFIGURATION.EXPERT_ASSIGNMENT_CONFIRM'">
          <button class="btn btn-primary waves-effect pull-right" [disabled]="f.invalid" type="submit">{{'UBZ.SITE_CONTENT.1100010' | translate }}</button>
        </ng-container>
      </div>
    </div>
  </form>
</ng-container>
