import { Component, OnIni<PERSON>, Inject, <PERSON><PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { Subscription } from 'rxjs/Subscription';
import { DomainService } from '../../../shared/domain/domain.service';
import { RegistryService } from '../../service/registry.service';
import { MessageService } from '../../../shared/messages/services/message.service';
import { ExpertFirm } from '../../model/registry.models';
import { ExpertFirmFilter } from '../../model/registry.filters.models';
import { IAppConstants, APP_CONSTANTS } from '../../../app.constants';

@Component({
  selector: 'app-expert-firm-registry',
  templateUrl: './expert-firm-registry.component.html',
  styleUrls: ['./expert-firm-registry.component.css']
})
export class ExpertFirmRegistryComponent implements OnInit, OnD<PERSON>roy {
  private USE_MOCK = false;
  private _subscription: Subscription;
  private searchActivated: boolean;

  public addExpertFirm = false;
  public filtersOpen = false;
  public newExpertFirm: ExpertFirm;
  public searchFilter: ExpertFirmFilter;

  public provinces: any[] = [];
  public cities: any[] = [];

  public societies: ExpertFirm[] = [];
  public societiesNumber: number;
  public listSize = 10;
  public pageNumber = 1;

  public contractChosen: boolean;
  public isValidIban: boolean = true;

  @ViewChild('fileToUpload') fileToUpload: any;
  fileName = '';
  sizeLimit: number;

  constructor(
    private _router: Router,
    private _registryService: RegistryService,
    private _domainService: DomainService,
    @Inject(APP_CONSTANTS) private _constants: IAppConstants,
    private _messageService: MessageService
  ) {}

  ngOnInit() {
    this.refreshTableData();
  }

  ngOnDestroy() {
    if (this._subscription && !this._subscription.closed) {
      this._subscription.unsubscribe();
    }
  }

  refreshTableData() {
    this._subscription = this._registryService
      .getExpertFirmList(this.pageNumber, this.listSize)
      .subscribe(res => {
        this.parseListResponse(res);
      });
  }

  private parseListResponse(httpResponse: any) {
    this.societies = httpResponse.listSoc;
    this.societiesNumber = httpResponse.totalSocieties;
  }

  public goToNewExpertFirm(): void {
    this.newExpertFirm = new ExpertFirm();
    this.addExpertFirm = true;
    this._domainService.newGetDomain('UBZ_DOM_PROVINCE').subscribe(res => {
      this.provinces = res;
    });
  }

  public openFilters(): void {
    this.searchFilter = new ExpertFirmFilter();
    this.filtersOpen = true;
  }

  public cleanSearchFilter(): void {
    this.searchFilter = new ExpertFirmFilter();
  }

  public goToExpertFirm(id: number): void {
    this._router.navigateByUrl(
      `/registry/${this._constants.SubjectType['SOC']}/${id}`
    );
  }

  public calculateCities(event: any) {
    this._domainService.getDomainCity(event.target.value).subscribe(res => {
      this.cities = res;
    });
  }

  public saveNewExpert() {
    const toSave = JSON.parse(JSON.stringify(this.newExpertFirm));
    toSave.startAbilitation = Date.parse(toSave.startAbilitation);
    toSave.endAbilitation = Date.parse(toSave.endAbilitation);
    this._registryService
      .addNewExpertFirm(toSave, this.fileToUpload.nativeElement.files[0])
      .subscribe(res => {
        this.addExpertFirm = false;
        this.refreshTableData();
      });
  }

  public filter() {
    this.searchActivated = true;
    this.searchFilter.heading =
      this.searchFilter.heading === '' ? null : this.searchFilter.heading;
    this.searchFilter.ndg =
      this.searchFilter.ndg === '' ? null : this.searchFilter.ndg;
    this.searchFilter.page = this.pageNumber;
    this.searchFilter.pageSize = this.listSize;
    this._registryService.filter('SOC', this.searchFilter).subscribe(res => {
      this.parseListResponse(res);
    });
  }

  public changePage(event: any) {
    this.pageNumber = event.page;
    this.getSocList();
  }

  public pageSizeChanged(): void {
    this.getSocList();
  }

  private getSocList() {
    if (this.searchActivated) {
      this.filter();
    } else {
      this.refreshTableData();
    }
  }

  public closeFilters() {
    this.getSocList();
    this.filtersOpen = false;
    this.searchActivated = false;
  }

  setFile() {
    if (this.fileToUpload.nativeElement.files[0].size > this.sizeLimit) {
      // this.fileToUpload.nativeElement.files = null;
      this._messageService.showError(
        `LA DIMENSIONE DEL FILE NON PUO\' SUPERARE I ${this.sizeLimit /
        1048576} MEGABYTE`,
        'DIMENSIONE FILE NON CONSENTITA'
      );
      this.setFileName('');
      this.contractChosen = false;
    } else {
      this.setFileName();
      this.contractChosen = true;
    }
  }

  setFileName(fileName?: string) {
    if (!fileName && fileName !== '') {
      fileName = this.fileToUpload.nativeElement.files[0].name;
    }
    this.fileName = fileName;
  }

  checkLenghtIban(input: string){
    if (input.length == 27){
      this.isValidIban = false;
    }else{
      this.isValidIban = true;
    }
  }

  getStatus(res: any): string {
    const currentDate = new Date();
    const endAbilitationDate = new Date(res.endAbilitation);
    endAbilitationDate.setHours(23, 59, 59);
    const datePlusNumOfDays = new Date(new Date().setDate(currentDate.getDate() + 30));
    datePlusNumOfDays.setHours(23, 59, 59);

    //Scaduta
    if (endAbilitationDate < currentDate) {
      return "red";
    }

    //Sta per scadere 
    else if (endAbilitationDate <= datePlusNumOfDays) {
      return "yellow";
    }

    //Attiva
    else if (endAbilitationDate > datePlusNumOfDays) {
      return "green";
    }
  }
}
