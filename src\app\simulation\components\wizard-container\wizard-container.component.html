<app-position-header [positionId]="positionId" [wizardCode]="wizardCode" [lockingUser]="lockingUser"
  (positionLocked)="landingService.positionLocked = true" (positionUnlocked)="landingService.positionLocked = false"></app-position-header>
<div class="row">
  <div class="col-sm-12 section-headline">
    <h1 *ngIf="wizardCode === 'WSIM'"><i class="icon-richiesta_perizia"></i>{{'UBZ.SITE_CONTENT.111' | translate }}</h1>
    <h2 *ngIf="wizardCode === 'WSIM'">{{'UBZ.SITE_CONTENT.1011011' | translate }}</h2>
    <h1 *ngIf="wizardCode === 'WRPE'"><i class="icon-richiesta_perizia"></i>{{'UBZ.SITE_CONTENT.11011100' | translate }}</h1>
    <h2 *ngIf="wizardCode === 'WRPE'">{{'UBZ.SITE_CONTENT.11101000' | translate }}</h2>
    <ng-container *ngIf="wizardCode === 'PER'">
      <h1><i class="icon-template_perizia"></i>{{'UBZ.SITE_CONTENT.101100110' | translate }}</h1>
      <h2>{{'UBZ.SITE_CONTENT.101100111' | translate }} {{positionId}} - {{ opinionType | translate }}</h2>
      <section id="breadcrumbs" class="breadcrumbs">
        <div class="row">
          <div class="col-sm-12">
            <ul>
              <li><a role="button" (click)="goToDashboard()">{{'UBZ.SITE_CONTENT.11' | translate }}</a></li>
              <li><a role="button" (click)="goToAppraisalDetail()">{{'UBZ.SITE_CONTENT.101101000' | translate }}</a></li>
              <li>{{'UBZ.SITE_CONTENT.101100110' | translate }}</li>
            </ul>
          </div>
        </div>
      </section>
    </ng-container>
  </div>
</div>
<app-wizard [positionId]="positionId" [wizardCode]="wizardCode" [landingMap]="constants.landingMap"></app-wizard>

<router-outlet></router-outlet>
