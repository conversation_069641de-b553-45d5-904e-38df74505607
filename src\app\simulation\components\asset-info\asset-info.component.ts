import { Component, OnInit, Inject, OnDestroy } from '@angular/core';
import { ActivatedRoute, Params, Router, NavigationEnd } from '@angular/router';
import { Subscription } from 'rxjs/Subscription';
import { Observable } from 'rxjs/Observable';
import { AssetService } from '../../services/asset/asset.service';
import { MenuService } from '../../../shared/menu/services/menu.service';
import { LandingService } from '../../services/landing/landing.service';
import { WizardService } from '../../../shared/wizard/services/wizard.service';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';
import { MessageService } from '../../../shared/messages/services/message.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-asset-info',
  templateUrl: './asset-info.component.html',
  styleUrls: ['./asset-info.component.css']
})
export class AssetInfoComponent implements OnInit, OnDestroy {
  private simulationId: string;
  wizardCode: string;
  currentTask: string;
  isModalOpen = false;
  assetsChecks: any[] = [];

  private assetSubscription: Subscription;
  private routerSubcription: Subscription;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    public assetService: AssetService,
    public menuService: MenuService,
    public landingService: LandingService,
    public messageService: MessageService,
    public translateService: TranslateService,
    private wizardService: WizardService,
    @Inject(APP_CONSTANTS) public constants: IAppConstants
  ) { }

  ngOnInit() {
    this.getAssetList();
    this.routerSubcription = this.router.events.subscribe(val => {
      if (val instanceof NavigationEnd) {
        this.getAssetList();
      }
    });
  }

  ngOnDestroy() {
    if (this.routerSubcription && !this.routerSubcription.closed) {
      this.routerSubcription.unsubscribe();
    }
    if (this.assetSubscription && !this.assetSubscription.closed) {
      this.assetSubscription.unsubscribe();
    }
  }

  getAssetList() {
    this.assetSubscription = this.activatedRoute.parent.params.subscribe(
      (params: Params) => {
        if (
          this.router.url.includes('/asset-search') ||
          this.router.url.includes('/new-asset')
        ) {
          return;
        }
        this.simulationId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        this.currentTask =
          this.wizardCode === 'WSIM' ? 'UBZ-SIM-AST' : 'UBZ-REQ-AST';
        if (params['wizardCode'] === 'WRPE') {
          this.router.navigate(['./appraisal-list'], {
            relativeTo: this.activatedRoute
          });
        } else {
          this.router.navigate(['./asset-list'], {
            relativeTo: this.activatedRoute
          });
        }
        this.landingService.checkIfCompletedTask(
          this.simulationId,
          this.currentTask
        );
      }
    );
  }

  private handleRestrictionServiceCall(): Observable<any> {
    if (this.assetService.showRestrictionsCheckboxes) {
      return this.assetService.saveRestrictionObjects(this.simulationId);
    } else {
      return Observable.of(true);
    }
  }

  private checkAssetsStatus() {
    return this.assetService.checkAssetsStatus(this.simulationId);
  }

  // Esegue l'avanzamento del wizard di richiesta perizia
  // Se landingService.originationProcess === 'TGP' non bisogna eseguire il metodo verifyAppraisalRequest()
  goToNextPage() {
    if (this.wizardCode === 'WRPE') {
      this.checkAssetsStatus()
        .subscribe((response: any) => {
          if (typeof response !== 'undefined' && this.isEmpty(response)) {
            this.handleRestrictionServiceCall()
              .subscribe(() => {
                if (this.landingService.originationProcess !== 'TGP') {
                  // chiamare il servizio per il controllo che ha dato Matteo
                  this.assetService.verifyAppraisalRequest(this.simulationId)
                    .subscribe(canGoToNextPage => {
                      // Copia del codice sotto (nel caso sia una simulazione)
                      if (canGoToNextPage) {
                        if (canGoToNextPage.warning === null) {
                          this.progressWizard();
                        }
                        else {
                          this.messageService.showWarning(
                            canGoToNextPage.warning.message,
                            this.translateService.instant('UBZ.SITE_CONTENT.11110000000'));
                          this.landingService.goToPreviousTargetedTask('UBZ-REQ-GEN', this.simulationId, this.wizardCode, this.activatedRoute);
                        }
                        return;
                      }
                    });
                } else {
                  this.progressWizard();
                  return;
                }
              });
          } else {
            this.showAssetsChecks(response);
            this.openModal();
          }
        });
    }
    if (this.wizardCode === 'WSIM') {
      this.landingService.goNextPage(
        this.simulationId,
        'UBZ-SIM-AST',
        this.wizardCode,
        this.activatedRoute
      );
    }
  }

  // Recupera il prossimo step ed esegue l'avanzamento su di esso
  progressWizard() {
    this.landingService.goNextPage(
      this.simulationId,
      this.currentTask,
      this.wizardCode,
      this.activatedRoute);
  }

  previous() {
    this.landingService.goToPreviousTask(this.simulationId, this.wizardCode, this.activatedRoute);
  }

  exit() {
    this.router.navigate(['/']);
  }

  cancelPosition() {
    this.landingService
      .cancelPosition(this.simulationId, this.wizardCode)
      .subscribe(res => this.router.navigate(['/']));
  }

  openModal() {
    this.isModalOpen = true;
  }

  hideModal() {
    this.isModalOpen = false;
  }

  showAssetsChecks(assets: any) {
    this.assetsChecks = [];
    for (let key in assets) {
      if (assets.hasOwnProperty(key)) {
        this.assetsChecks.push({
          assetId: key,
          assets: assets[key]
        });
      }
    }
  }

  isEmpty(obj: any) {
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        return false;
      }
    }
    return JSON.stringify(obj) === JSON.stringify({});
  }
}
