import { Injectable, Inject} from "@angular/core";
import { <PERSON><PERSON>, Http, RequestOptions } from "@angular/http";
import { DOCUMENT } from "@angular/platform-browser";
import { Observable } from "rxjs/Observable";
import { UserDataService } from "../shared/user-data/user-data.service";
import { CookieService } from "ngx-cookie";
import { UserData } from "../shared/user-data/user-data";
declare const window: any;
@Injectable()
export class SilosService {
  appCode: any;
  userId: any;
  branch: any;
  
  constructor(
    private http: Http,
    @Inject(DOCUMENT) private document: any,
    private userDataService: UserDataService,
   // private cookieService: CookieService
  ) {

   
    // this.userDataService.getAll().subscribe((res: any) => {
    //   this.userId = res.username;
    //   this.branch = res.branch;
 
    // });
  }

  // shared service
  // setApplicationCode(code: string) {
  //   this.appCode = code;
  //   console.log(this.appCode);
  // }
  // shared service

  // public getAuthData(
  //   userId: any,
  //   applicationCode: any,
  //   branchCode: any
  // ): Observable<any> {
  //   userId = this.userId;
  //   console.log(userId, "userId from umf s");
  //   branchCode = this.branch;
  //   console.log(branchCode, "branchCookie from umf s");
  //   applicationCode = this.appCode;
  //   console.log(applicationCode);

  //   const url = `UBZ-ESA-RS/service/userService/v1/users/${userId}/${applicationCode}/${branchCode}`;

  //   return this.http.get(url).map((res) => res.json());
  // }











 // public dataToSilos(dataSilos: any) {



   

  //   const url =
  //     "https://collcreditdesk-auth.cervedgroup.com/api/v1/hash-authentication";
  //   console.log(JSON.stringify(dataSilos));

  //   const headers = new Headers({ "Content-Type": "application/json" });
  //   const options = new RequestOptions({
  //     headers: headers,
  //     withCredentials: true,
  //   });

  //   this.http.post(url, JSON.stringify(dataSilos), options).subscribe(
  //     (response) => {
  //       console.log(response.status);
  //       console.log(response.url);
        

  //       if (response.url && response.status === 200) {
  //         this.document.location.href = response.url;

   
  //       }
  //     },
  //     (error) => {
  //       console.error("Authentication failed:", error);
  //     }
  //   );
  // }
//}


}