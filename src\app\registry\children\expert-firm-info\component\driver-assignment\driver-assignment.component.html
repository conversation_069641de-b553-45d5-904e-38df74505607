<form #f="ngForm">
  <accordion-group #group class="panel" [ngClass]="{'empty-accordion': !aRuleExists}" [isOpen]="isOpen" [isDisabled]="!aRuleExists">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          {{'UBZ.SITE_CONTENT.1011110100' | translate }}
          <div class="accordion-button">
            <ng-container *appAuthKey="'UBZ_REGISTY.ENABLE_APPRAISALS_NEW'">
              <button *ngIf="!isEditable && !aRuleExists" type="button" class="btn btn-empty" (click)="modify(true) ; $event.stopPropagation()">
                <i class="fa icon-add"></i>{{'UBZ.SITE_CONTENT.111100001' | translate }}
              </button>
            </ng-container>
            <ng-container *appAuthKey="'UBZ_REGISTY.ENABLE_APPRAISALS_MODIFY'">
              <button *ngIf="!isEditable && aRuleExists" type="button" class="btn btn-empty" (click)="modify(false) ; $event.stopPropagation()">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.11111' | translate }}
              </button>
            </ng-container>
            <ng-container *appAuthKey="'UBZ_REGISTY.ENABLE_APPRAISALS_ABORT'">
              <button *ngIf="isEditable" type="button" class="btn btn-empty" (click)="undo() ; $event.stopPropagation()">
                <i class="fa fa-times"></i>{{'UBZ.SITE_CONTENT.100000' | translate }}
              </button>
            </ng-container>
            <ng-container *appAuthKey="'UBZ_REGISTY.ENABLE_APPRAISALS_SAVE'">
              <button *ngIf="isEditable" type="button" class="btn btn-empty" (click)="save() ; $event.stopPropagation()" [disabled]="f.invalid">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.100001' | translate }}
              </button>
            </ng-container>
          </div>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <form #f="ngForm">
            <ng-container *ngIf="isEditable; else only_visualization">
              <ng-container *ngFor="let row of pageContent; let l = last; let index = index">
                <div class="row">
                  <div class="col-md-3 col-sm-12 form-group">
                    <label for="expertType">{{'UBZ.SITE_CONTENT.1011110101' | translate }}*</label>
                    <div class="custom-select">
                      <select class="form-control" name="macroProcess-{{index}}" [(ngModel)]="row.macroProcess" required>
                        <option value="" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                        <option *ngFor="let item of (macroProcesses | domainMapToDomainArray)" value="{{item.domCode}}">{{item.translationCod | translate}}</option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-3 col-sm-12 form-group">
                    <label for="expertType">{{'UBZ.SITE_CONTENT.11110001' | translate }}*</label>
                    <div class="custom-select">
                      <select class="form-control" name="creditSegment-{{index}}" required [(ngModel)]="row.creditSegment">
                        <option value="" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                        <option *ngFor="let item of (creditSegments | domainMapToDomainArray)" value="{{item.domCode}}">{{item.translationCod | translate}}</option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-3 col-sm-12 form-group">
                    <label for="expertType">{{'UBZ.SITE_CONTENT.1011001' | translate }}*</label>
                    <div class="custom-select">
                      <select class="form-control" name="appraisalType-{{index}}" [(ngModel)]="row.appraisalType" required>
                        <option value="" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                        <option *ngFor="let item of (appraisalType | domainMapToDomainArray)" value="{{item.domCode}}">{{item.translationCod | translate}}</option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-3 col-sm-12 form-group">
                    <label for="expertType">{{'UBZ.SITE_CONTENT.1011110110' | translate }}*</label>
                    <div class="custom-select">
                      <select class="form-control" name="expertType-{{index}}" [(ngModel)]="row.goodType" required>
                        <option value="" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                        <option *ngFor="let item of (goodTypes | domainMapToDomainArray)" value="{{item.domCode}}">{{item.translationCod | translate}}</option>
                      </select>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-3 col-sm-12 form-group" *ngIf="row.goodType === 'IMM'">
                    <label for="expertType">{{'UBZ.SITE_CONTENT.1011110111' | translate }}*</label>
                    <div class="custom-select">
                      <select class="form-control" name="specificCategory-{{index}}" required [(ngModel)]="row.specificCategory">
                        <option value="" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                        <option *ngFor="let item of (categoryTypes | domainMapToDomainArray)" value="{{item.domCode}}">{{item.translationCod | translate}}</option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-3 col-sm-12 form-group">
                    <label>{{'UBZ.SITE_CONTENT.1011111000' | translate }}*</label>
                    <app-importo [name]="'minEuro-' + index" [required]="true" [(ngModel)]="row.minEuro">
                    </app-importo>
                  </div>
                  <div class="col-md-3 col-sm-12 form-group">
                    <label>{{'UBZ.SITE_CONTENT.1011111001' | translate }}*</label>
                    <app-importo [name]="'maxEuro-' + index" [required]="true" [(ngModel)]="row.maxEuro">
                    </app-importo>
                  </div>
                  <div class="col-sm-3 form-group">
                    <label>{{'UBZ.SITE_CONTENT.1011111010' | translate }}</label>
                    <app-calendario [name]="'startAbilitation-' + index" [(ngModel)]="row.startAbilitation" [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                      [required]="true">
                    </app-calendario>
                  </div>
                  <div class="col-sm-3 form-group">
                    <label>{{'UBZ.SITE_CONTENT.1011111011' | translate }}</label>
                    <app-calendario [name]="'endAbilitation-' + index" [(ngModel)]="row.endAbilitation" [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                      [required]="true">
                    </app-calendario>
                  </div>
                </div>
                <div class="row" *ngIf="subjectType != 'SOC'">
                  <div class="col-md-3 col-sm-12 form-group">
                    <label for="expertType">{{'UBZ.SITE_CONTENT.11110010' | translate }}*</label>
                    <div class="custom-select">
                      <select class="form-control" name="region-{{index}}" required [(ngModel)]="row.competenceRegion">
                        <option [ngValue]="null" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                        <option *ngFor="let item of (regions | domainMapToDomainArray)" value="{{item.domCode}}">{{item.translationCod | translate}}</option>
                      </select>
                    </div>
                  </div>
                </div>
                <hr *ngIf="!l" />
              </ng-container>
              <div class="row">
                <div class="col-sm-12 btn-set">
                  <!-- *appAuthKey="''" -->
                  <button class="btn btn-primary waves-effect pull-right" [disabled]="f.invalid" type="button" (click)="addNewRule()">{{'UBZ.SITE_CONTENT.1011111100' | translate }}</button>
                </div>
              </div>
            </ng-container>
          </form>
        </div>
      </div>
    </div>
  </accordion-group>
</form>

<ng-template #only_visualization>
  <ng-container *ngFor="let row of pageContent; let l = last">
    <div class="row">
      <div class="col-md-3 col-sm-12 form-group">
        <label for="expertType">{{'UBZ.SITE_CONTENT.1011110101' | translate }}</label>
        {{ macroProcesses[row.macroProcess]?.translationCod | translate }}
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label for="expertType">{{'UBZ.SITE_CONTENT.11110001' | translate }}</label>
        {{ creditSegments[row.creditSegment]?.translationCod | translate }}
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label for="expertType">{{'UBZ.SITE_CONTENT.1011111100' | translate }}</label>
        {{ appraisalType[row.appraisalType]?.translationCod | translate }}
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label for="expertType">{{'UBZ.SITE_CONTENT.1011110110' | translate }}</label>
        {{ goodTypes[row.goodType]?.translationCod | translate }}
      </div>
    </div>
    <div class="row">
      <div class="col-md-3 col-sm-12 form-group" *ngIf="row.goodType === 'IMM'">
        <label for="expertType">{{'UBZ.SITE_CONTENT.1011110111' | translate }}</label>
        {{ expiredCategoryTypes[row.specificCategory]?.translationCod | translate }}
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1011111000' | translate }}</label>
        {{ row.minEuro | number:'1.2-2' }}
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1011111001' | translate }}</label>
        {{ row.maxEuro | number:'1.2-2' }}
      </div>
      <div class="col-sm-3 form-group">
        <label>{{'UBZ.SITE_CONTENT.1011111010' | translate }}</label>
        {{ row.startAbilitation | date:'dd/MM/yyyy' }}
      </div>
      <div class="col-sm-3 form-group">
        <label>{{'UBZ.SITE_CONTENT.1011111011' | translate }}</label>
        {{ row.endAbilitation | date:'dd/MM/yyyy' }}
      </div>
    </div>
    <div class="row" *ngIf="subjectType != 'SOC'">
      <div class="col-md-3 col-sm-12 form-group">
        <label for="expertType">{{'UBZ.SITE_CONTENT.11110010' | translate }}</label>
        {{ regions[row.competenceRegion]?.translationCod | translate }}
      </div>
    </div>
    <hr *ngIf="!l" />
  </ng-container>
</ng-template>
