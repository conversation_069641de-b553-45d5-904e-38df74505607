<ng-container *ngIf="table">
  <div class="col-sm-12 section-headline">
    <h1 *ngIf="title">
      <i class="icon-report"></i>
      {{title}}
    </h1>
    <h2 *ngIf="subTitle">{{subTitle}}</h2>
  </div>
  <div class="row">
    <div class="col-sm-1 text-right pull-right">
      <button class="btn btn-clean" type="button" (click)="exctractReport()">
        <i class="icon-report"></i>
      </button>
      <button class="btn btn-clean" type="button" (click)="filtersVisible = !filtersVisible">
        <i class="icon-search"></i>
      </button>
    </div>
  </div>
  <div class="row">
    <ng-content *ngIf="filtersVisible">
    </ng-content>
  </div>
  <br/>
  <div class="row">
    <div class="col-sm-12">
      <table class="uc-table">
        <thead *ngIf="table.headerRow">
          <tr>
            <th *ngFor="let he of table.headerRow.fields" scope="col" class="col-sm-2">{{he.fieldValue}}</th>
          </tr>
        </thead>
        <tbody *ngIf="table.rows">
          <tr *ngFor="let row of table.rows" class="invalid no-expert" data-index="1">
            <ng-container *ngFor="let field of row.fields">
              <td *ngIf="clickOnRowActions[reportCode] && clickOnRowActions[reportCode][field.fieldCod]" role="button" data-label="Descrizione" (click)="actionOnClick(field)">{{field.fieldValue}}</td>
              <td *ngIf="!clickOnRowActions[reportCode] || !clickOnRowActions[reportCode][field.fieldCod]" data-label="Descrizione">{{field.fieldValue}}</td>
            </ng-container>
          </tr>
        </tbody>
        <tfoot>
          <tr *ngIf="table.aggregatesRow && table.aggregatesRow.fields" class="invalid no-expert" data-index="1">
            <td *ngFor="let field of table.aggregatesRow.fields" data-label="Descrizione">{{field.fieldValue}}</td>
          </tr>
        </tfoot>
      </table>
    </div>
  </div>
<br/>
  <div class="row">
    <div *ngIf="table.count > 10" class="col-sm-6">
      <div class="results">
        <span>{{'UBZ.SITE_CONTENT.1111111' | translate }}</span>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="pageSize" (change)="changePageSize()">
            <option value="10" selected>10 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{table.count}}</option>
            <option *ngIf="table.count > 20" value="20">20 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{table.count}}</option>
            <option *ngIf="table.count > 30" value="30">30 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{table.count}}</option>
            <option *ngIf="table.count > 40" value="40">40 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{table.count}}</option>
            <option *ngIf="table.count > 50" value="50">50 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{table.count}}</option>
            <option *ngIf="table.count <= 50" [ngValue]="table.count"> {{table.count}} {{'UBZ.SITE_CONTENT.10000000' | translate }} {{table.count}}</option>
          </select>
        </div>
      </div>
    </div>
    <div class="col-sm-6" class="pull-right">
      <pagination [boundaryLinks]="true" [directionLinks]="false" [totalItems]="table.count" [ngModel]="page" [itemsPerPage]="pageSize" [maxSize]="10"
        (pageChanged)="changePage($event)" class="pagination" previousText="&lsaquo;" nextText="&rsaquo;" firstText="&laquo;" lastText="&raquo;"></pagination>
    </div>
  </div>
</ng-container>
