import { EventEmitter, Component, OnInit, Input, Output } from '@angular/core';
import { AmeoriService } from '../../service/ameori.service';

@Component({
  selector: 'app-ameori-list',
  templateUrl: './ameori-list.component.html',
  styleUrls: ['./ameori-list.component.css']
})
export class AmeoriListComponent implements OnInit {
  @Input()
  ameoriList: any[] = [];
  @Input()
  reportType: string = '';
  @Input()
  reportNumber: number = 0;
  @Output()
  openModal = new EventEmitter<boolean>();
  ameori: any;
  constructor(private ameoriService: AmeoriService) {}

  ngOnInit() {}

  openUpdateModal(appraisalId: number) {
    this.search(appraisalId);
  }

  search(appraisalId: number) {
    this.ameoriService
      .searchUnique(this.reportType, this.reportNumber, appraisalId)
      .subscribe(res => {
        if (typeof res.list !== 'undefined' && res.list.length > 0) {
          this.ameori = res.list[0];
        }
      });
  }

  openPopup(ameori: any) {
    this.openModal.emit(ameori);
  }
}
