import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'customDateTime'
})
export class CustomDateTimePipe implements PipeTransform {
  transform(value: any, args?: any): any {
    const date = new Date(parseInt(value, 10));
    const day =
      date.getDate() < 10 ? `0${date.getDate()}` : `${date.getDate()}`;
    const month =
      date.getMonth() + 1 < 10
        ? `0${date.getMonth() + 1}`
        : `${date.getMonth() + 1}`;
    const hours =
      date.getHours() < 10 ? `0${date.getHours()}` : `${date.getHours()}`;
    const mins =
      date.getMinutes() < 10 ? `0${date.getMinutes()}` : `${date.getMinutes()}`;
    const secs =
      date.getSeconds() < 10 ? `0${date.getSeconds()}` : `${date.getSeconds()}`;
    return `${day}-${month}-${date.getFullYear()} ${hours}:${mins}:${secs}`;
  }
}
