import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { NgForm } from '@angular/forms';
import { Observable } from 'rxjs/Observable';
import { TranslateService } from '@ngx-translate/core';
// Servizi interni
import { MessageService } from '../../messages/services/message.service';
import { DomainService } from '../../domain/domain.service';
import { NotesService } from '../services/notes.service';
// Modelli
import { Note } from '../model/note';
import { UserDataService } from '../../user-data/user-data.service';
import { UserData } from '../../user-data/user-data';

@Component({
  selector: 'app-notes',
  templateUrl: './notes.component.html',
  styleUrls: ['./notes.component.css'],
  providers: [NotesService]
})
export class NotesComponent implements OnInit {
  @Input() positionId: string;
  @Input() positionType: string;
  @ViewChild('addNoteModal') addNoteModal: ModalDirective;
  @ViewChild('f') f: NgForm;
  notesHistory: Note[] = [];
  newNote: Note;
  noteTypeDomain: any;
  reasonTypeDomain: any;
  userDetails: UserData;

  constructor(
    private notesService: NotesService,
    private domainService: DomainService,
    private messageService: MessageService,
    private translateService: TranslateService,
    private userDataService: UserDataService
  ) {}

  ngOnInit() {
    this.newNote = new Note(this.positionId, this.positionType);
    this.retrieveData();
   this.userDataService.getAll().subscribe((res:UserData)=>{
    this.userDetails=res
   })
  }

  retrieveData() {
    Observable.forkJoin(
      this.notesService.getNotes(this.positionId, this.positionType),
      this.domainService.newGetDomain('UBZ_DOM_NOTE_TYPE'),
      this.domainService.newGetDomain('UBZ_DOM_REASON_TYPE')
    ).subscribe(res => {
      this.notesHistory = res[0];
      this.noteTypeDomain = res[1];
      this.reasonTypeDomain = res[2];
    });
  }

  refreshPage() {
    this.notesService
      .getNotes(this.positionId, this.positionType)
      .subscribe(res => (this.notesHistory = res));
  }

  saveNote() {
    if (this.f.valid) {
      this.notesService.saveNote(this.newNote).subscribe(() => {
        this.addNoteModal.hide();
        this.messageService.showSuccess(
          this.translateService.instant('UBZ.SITE_CONTENT.10001100'),
          this.translateService.instant('UBZ.SITE_CONTENT.10001101')
        );
        this.refreshPage();
        this.resetNewNoteForm();
      });
    }
  }
  
  private resetNewNoteForm() {
    this.f.resetForm();
    this.newNote = new Note(this.positionId, this.positionType);
  }
}
