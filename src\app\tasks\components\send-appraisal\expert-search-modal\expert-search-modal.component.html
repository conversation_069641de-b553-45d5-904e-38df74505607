<div *ngIf="isOpen" class="modal fade" bsModal #modalBox="bs-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" (onHidden)="hide()" [config]="{show: 'true'}">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{ title[searchType] | translate }}</h2>
        <button type="reset" class="close pull-right" aria-label="Close" (click)="hide()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <form>
          <div class="row">
            <div class="col-md-4 form-group">
              <label for="ndg">{{'UBZ.SITE_CONTENT.1001110' | translate }}</label>
              <input type="text" name="ndg" class="form-control" [(ngModel)]="searchObj.ndg">
            </div>
            <div class="col-md-4 from-group">
              <label for="firstName">{{'UBZ.SITE_CONTENT.101111110' | translate }}</label>
              <input type="text" name="firstName" class="form-control" [(ngModel)]="searchObj.firstname">
            </div>
            <div class="col-md-4 form-group">
              <label for="lastName">{{'UBZ.SITE_CONTENT.101111111' | translate }}</label>
              <input type="text" name="lastName" class="form-control" [(ngModel)]="searchObj.lastName">
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <button type="button" class="btn btn-primary waves-effect pull-right" (click)="search()">{{'UBZ.SITE_CONTENT.10010111' | translate }}</button>
            </div>
          </div>
        </form>
        <!-- Tabella risultati -->
        <table class="table" *ngIf="searchResults.length > 0">
          <thead>
            <tr class="row">
              <th scope="col" class="col-sm-1 checkbox-col"></th>
              <th scope="col">{{'UBZ.SITE_CONTENT.110000000' | translate }}</th>
              <th scope="col">{{'UBZ.SITE_CONTENT.1001110' | translate }}</th>
              <th scope="col">{{'UBZ.SITE_CONTENT.101111110' | translate }}</th>
              <th scope="col">{{'UBZ.SITE_CONTENT.101111111' | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <tr class="row" *ngFor="let perito of searchResults; let i = index">
              <td>
                <div class="custom-radio">
                  <input type="radio" id="{{i}}" class="radio" [(ngModel)]="selectedExpert" [value]="perito" name="expert">
                  <label for="{{i}}"></label>
                </div>
              </td>
              <td>{{ perito.idAnag }}</td>
              <td>{{ perito.ndg }}</td>
              <td>{{ perito.firstName }}</td>
              <td>{{ perito.lastname }}</td>
            </tr>
          </tbody>
        </table>
        <div *ngIf="searchResults.length === 0 && searchDone" class="row text-center">
          <div class="Search__NoResults__Text">
            <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.10000010110' | translate }}</h3>
            <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.10000010111' | translate }}</p>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary waves-effect" (click)="assign()" [disabled]="!selectedExpert">{{'UBZ.SITE_CONTENT.110000001' | translate }}</button>
      </div>
    </div>
  </div>
</div>
