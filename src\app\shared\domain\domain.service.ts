import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';
import { Observable } from 'rxjs/Observable';
import { SharedService } from '../services/shared.service';
import { Domain } from './domain';

@Injectable()
export class DomainService {
  base_url = '/UBZ-ESA-RS/service/domain/v1/domains';
  newDomains: any = {};
  newDomainsIncludingExpired: any = {};
  cachedDomains: { [name: string]: Observable<any> } = {};

  constructor(private http: Http, private sharedService: SharedService) { }

  public newGetDomain(domainName: string, rootName = '-', includeExpiredValues = false) {
    domainName = encodeURIComponent(domainName);
    rootName = encodeURIComponent(rootName);
    let observablesDomain: Observable<any>;
    const stableHashVal = this.sharedService.stableHash({
      domainName,
      rootName,
      includeExpiredValues,
    });
    const cachedVal = this.cachedDomains[stableHashVal];
    if (cachedVal) {
      observablesDomain = cachedVal;
    }
    else {
      let tmpUrl = this.buildUrl(domainName, rootName, includeExpiredValues);
      observablesDomain = this.http.get(tmpUrl).map(
        (value) => {
          let js = value.json();
          if (domainName === 'UBZ_DOM_STATUS') {
            js = this.convertStatusToDomain(value.json());
          }
          this.getDomainMap(includeExpiredValues)[domainName + ';' + rootName + ';' + includeExpiredValues] = js;
          this.cachedDomains[stableHashVal] = Observable.of(js);
          return js;
        },
        (error) => {
          console.error(error);
        }
      );
    }

    return observablesDomain;
  }

  private buildUrl(domainName: string, rootName: string, includeExpiredValues: boolean) {
    let tmpUrl = `${this.base_url}/${domainName}/${rootName}/map`;
    if (includeExpiredValues) {
      tmpUrl = `${tmpUrl}?expired=true`;
    }
    if (domainName === 'UBZ_DOM_STATUS') {
      tmpUrl = '/UBZ-ESA-RS/service/domain/v1/domains/status/all';
    }
    return tmpUrl;
  }

  public getDomainList(domainList: string[]) {
    const url = '/UBZ-ESA-RS/service/domain/v1/domains/list';
    const ret = {};
    const domainsToRequire = [];

    for (const el of domainList) {
      if (this.newDomains && this.newDomains[el + ';-;false']) {
        ret[el] = this.newDomains[el + ';-;false'];
      } else {
        domainsToRequire.push(el);
      }
    }
    return this.http.post(url, domainsToRequire).map(value => {
      const js = value.json();

      for (const ind in js) {
        this.newDomains[ind + ';-;false'] = js[ind];
        ret[ind] = js[ind];
      }
      return ret;
    });
  }

  public getAllDomainList(domainList: Object[]) {
    const url = '/UBZ-ESA-RS/service/domain/v1/domains/list/allDomain';
    const ret = {};
    const domainsToRequire = [];
    let reqList: Object[] = [];

    for (const el of domainList) {
      let ind = el['domainName'] + ';' + el['parentCode'] + ';' + el['expired'];
      if (this.newDomains && this.newDomains[ind]) {
        ret[ind] = this.newDomains[ind];
      } else {
        domainsToRequire.push(el);
      }
    }

    if (domainsToRequire.length === 0) {
      return Observable.of(this.sortDomainsAndRemoveKeys(ret, domainsToRequire));
    }

    return this.http.post(url, domainsToRequire).map(value => {
      const js = value.json();

      for (const ind in js) {
        this.newDomains[ind] = js[ind];
        ret[ind] = js[ind];
      }

      domainList.forEach(domainObj => {
        let key = domainObj['domainName'] + ';' + domainObj['parentCode'] + ';' + domainObj['expired'];
        reqList.push(key);
      });

      return this.sortDomainsAndRemoveKeys(ret, reqList);
    });
  }

  private sortDomainsAndRemoveKeys(ret: Object, domainsToRequire: any[]) {
    let domainsValues = Object.keys(ret).map((k) => [k, ret[k]]);
    domainsValues.sort((a, b) => {
      return domainsToRequire.indexOf(a[0]) - domainsToRequire.indexOf(b[0])
    });

    let sortedDomainsAsReq = domainsValues.map(dom => dom[1]);
    return sortedDomainsAsReq;
  }

  private convertStatusToDomain(status) {
    for (const ind in status) {
      if (true) {
        status[ind].translationCod = status[ind].translationStatusDescCod;
      }
    }
    return status;
  }

  getSingleDomainItem(domainName: string, domainCod: string): Observable<any> {
    domainName = encodeURIComponent(domainName);
    domainCod = encodeURIComponent(domainCod);
    const url = `${this.base_url}/singleItem/${domainName}/${domainCod}`;
    return this.http.get(url).map(resp => resp.json());
  }

  public getDomainCity(provinceName: string) {
    provinceName = encodeURIComponent(provinceName);
    const url = `/UBZ-ESA-RS/service/conMap/v1/cities/${provinceName}`;
    return this.http.get(url).map(resp => resp.json());
  }

  public getDomainCityMap(provinceName: string) {
    provinceName = encodeURIComponent(provinceName);
    const url = `/UBZ-ESA-RS/service/conMap/v1/cities/${provinceName}/Map`;
    return this.http.get(url).map(resp => resp.json());
  }

  getTranslationCodFromDomains(domains: Domain[], value: string): string {
    for (const i1 in domains) {
      if (domains[i1].domCode === value) {
        return domains[i1].translationCod;
      }
    }
    return null;
  }

  getStatusName(): Observable<any> {
    const url = `/UBZ-ESA-RS/service/domain/v1/domains/status/all`;
    return this.http.get(url).map((resp: Response) => resp.json());
  }

  private getDomainMap(includeExpired: boolean) {
    return (includeExpired ? this.newDomainsIncludingExpired : this.newDomains);
  }

  getCachedDomains() {
    return this.cachedDomains;
  }

  transform(value: any, args?: any): any {
    const keys = [];
    for (const key in value) {
      if (value.hasOwnProperty(key)) {
        keys.push({
          domCode: value[key].domCode,
          translationCod: value[key].translationCod
        });
      }
    }
    return keys;
  }

  getObjectCode(appraisalId: string): Observable<any> {
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisals/${appraisalId}`
    return this.http.get(url).map((res: Response) => res.json());

  }
  getCategoryDoc(parentCode: string) {
    const url = `/UBZ-ESA-RS/service/domain/v1/domains/checklist/category?parentCode=${parentCode}`
    return this.http.get(url).map((res: Response) => res.json());


  }

}
