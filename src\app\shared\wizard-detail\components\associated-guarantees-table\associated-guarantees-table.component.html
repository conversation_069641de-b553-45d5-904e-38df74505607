<h4 class="section-heading">{{'UBZ.SITE_CONTENT.11110011' | translate }}</h4>
<table class="uc-table">
  <thead>
    <tr>
      <th *ngIf="renderIdAsset" scope="col">Id oggetto perizia</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.10010100' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.10010101' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.10010110' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.11110100' | translate }} €</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.1111110111' | translate }}</th>
      <th scope="col" style="text-align: center">{{'UBZ.SITE_CONTENT.1111111000' | translate }}</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let garanzia of list">
      <td *ngIf="renderIdAsset && garanzia.objectCod" attr.data-label="{{ garanzia.objectCod }}">{{ garanzia.objectCod }}</td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.10010100' | translate }}">{{ garanzia.progCollateral }}</td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.10010101' | translate }}">{{ garanzia.collateralTecForm }}</td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.10010110' | translate }}">{{ garanzia.collateralDesc }}</td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.110011010' | translate }}">{{ garanzia.collateralAmmount | currency:'EUR':true }}</td>
      <td>{{ ( garanzia.percType && percentageDomain[garanzia.percType]) ? ( percentageDomain[garanzia.percType].translationCod | translate) : '---' }}</td>
      <td style="text-align: center">{{ ( garanzia.poolPerc || garanzia.poolPerc === 0) ? garanzia.poolPerc : '---' }}</td>
    </tr>
  </tbody>
</table>
