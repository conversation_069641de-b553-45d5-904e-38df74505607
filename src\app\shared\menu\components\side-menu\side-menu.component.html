<section id="sidebar-wrapper">
  <app-search-menu *ngIf="menuService.isSideSearchOpen" [isSide]="true" [isOpened]="menuService.eventSideMenuOpen"></app-search-menu>
  <ul *ngIf="!menuService.isSideSearchOpen" class="sidebar-nav">
    <li class="hidden-md hidden-lg">
      <div class="sidebar-search">
        <div class="search-wrap">
          <div class="input-group search">
            <input class="form-control" placeholder="{{'UBZ.SITE_CONTENT.10010111' | translate }}" type="text">
            <span class="input-group-btn">
              <button class="btn btn-default" type="button">
                <i class="icon-search"></i>
              </button>
            </span>
          </div>
        </div>
        <button type="button" id="show-search-filters" class="btn btn-default btn-filters" (click)="openSideSearch()">
          <i class="icon-settings"></i>
        </button>
      </div>
    </li>

    <ng-container *ngFor="let firstElem of menuService.menu ; let firstIndex = index">

      <ng-container *ngIf="menuService.menuStatus[0] == firstIndex">
        <li class="drop-main-menu active hidden-sm hidden-md hidden-lg"><a (click)="menuService.changeHeadPage(firstElem.action , firstIndex)">{{firstElem.title | translate}}</a></li>
          <ng-container *ngFor="let secondElem of firstElem.childs ; let secondIndex = index">
          <!-- SECONDA VOCE DI MENU SELEZIONATA E CON FIGLI APERTI -->
          <li *ngIf=" menuService.menuStatus[1] == secondIndex && secondElem.childs.length > 0 && menuService.indexOpenedVoice == secondIndex" class="drop-menu active">
            <a (click)="changePageAndProperty(secondElem.action , secondIndex , true)"><i [class]="menuService.getIconClass(secondElem.name)"></i>{{secondElem.title | translate}}<i class="switch icon-arrow_up"></i></a>
            <ul class="drop-switch" style="display: block">
              <ng-container *ngFor="let thirdElem of secondElem.childs ; let thirdIndex = index">
                <li [ngClass]="menuService.menuStatus[2] === thirdIndex ? 'active' : ''"><a role="button" (click)="clickOnThirdLevelElement(thirdElem.action , thirdIndex)">{{thirdElem.title | translate}}</a></li>
                <!-- FIXME - TOGLIERE?
                  <li *ngIf="menuService.menuStatus[2] != thirdIndex"><a (click)="clickOnThirdLevelElement(thirdElem.action , thirdIndex)">{{thirdElem.title}}</a></li> -->
              </ng-container>
            </ul>
          </li>
          <!-- SECONDA VOCE DI MENU SELEZIONATA E CON FIGLI CHIUSI-->
          <li *ngIf=" menuService.menuStatus[1] == secondIndex && secondElem.childs.length > 0 && menuService.indexOpenedVoice != secondIndex" class="drop-menu active">
            <a (click)="changePageAndProperty(secondElem.action , secondIndex , true)"><i [class]="menuService.getIconClass(secondElem.name)"></i>{{secondElem.title | translate}}<i class="switch icon-arrow_down"></i></a>
            <ul class="drop-switch" style="display: none">
              <li *ngFor="let thirdElem of secondElem.childs ; let thirdIndex = index"><a (click)="changePage(thirdElem.action)">{{thirdElem.title | translate}}</a></li>
            </ul>
          </li>
          <!-- SECONDA VOCE DI MENU SELEZIONATA E SENZA FIGLI -->
          <li *ngIf=" menuService.menuStatus[1] == secondIndex && secondElem.childs.length == 0 " class="active">
            <a (click)="changePageAndProperty(secondElem.action , secondIndex , false)"><i [class]="menuService.getIconClass(secondElem.name)"></i>{{secondElem.title | translate}}</a>
          </li>
          <!-- SECONDA VOCE DI MENU NON SELEZIONATA E CON FIGLI -->
          <li *ngIf=" menuService.menuStatus[1] != secondIndex && secondElem.childs.length > 0" class="drop-menu">
            <a (click)="changePageAndProperty(secondElem.action , secondIndex , true)"><i [class]="menuService.getIconClass(secondElem.name)"></i>{{secondElem.title | translate}}<i class="switch icon-arrow_down"></i></a>
            <ul class="drop-switch" style="display: none">
              <li *ngFor="let thirdElem of secondElem.childs ; let thirdIndex = index"><a (click)="changePage(thirdElem.action)">{{thirdElem.title | translate}}</a></li>
            </ul>
          </li>
          <!-- SECONDA VOCE DI MENU NON SELEZIONATA E SENZA FIGLI -->
          <li *ngIf=" menuService.menuStatus[1] != secondIndex && secondElem.childs.length == 0">
            <a (click)="changePageAndProperty(secondElem.action , secondIndex , false)"><i [class]="menuService.getIconClass(secondElem.name)"></i>{{secondElem.title | translate}}</a>
          </li>
        </ng-container>
      </ng-container>

      <li *ngIf="menuService.menuStatus[0] !== firstIndex" class="hidden-sm hidden-md hidden-lg"><a (click)="menuService.changeHeadPage(firstElem.action , firstIndex)">{{firstElem.title | translate}}</a></li>



    </ng-container>
  </ul>
</section>
