<div class="row">
  <div class="col-sm-12 section-headline">
    <h1><i class="icon-richiesta_perizia"></i>{{'UBZ.SITE_CONTENT.1100011010' | translate }}</h1>
    <h2>{{'UBZ.SITE_CONTENT.1100101010' | translate }}</h2>
  </div>
</div>

<section id="details" class="details">
  <div class="row">
    <div class="uc-datatabs">
      <!-- Nav tabs -->
      <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" [ngClass]="{active: activeSection === ACTIVABLE_SECTIONS.SOC}"><a (click)="activeSection = ACTIVABLE_SECTIONS.SOC" aria-controls="Società peritali" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.1100101011' | translate }}</a></li>
        <li role="presentation" [ngClass]="{active: activeSection === ACTIVABLE_SECTIONS.INT}"><a (click)="activeSection = ACTIVABLE_SECTIONS.INT" aria-controls="Periti interni" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.1100101100' | translate }}</a></li>
        <li role="presentation" [ngClass]="{active: activeSection === ACTIVABLE_SECTIONS.BEN}"><a (click)="activeSection = ACTIVABLE_SECTIONS.BEN" aria-controls="Periti benevisi" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.1100101101' | translate }}</a></li>
      </ul>
    </div>
  </div>

<app-expert-firm-registry *ngIf="activeSection === ACTIVABLE_SECTIONS.SOC"></app-expert-firm-registry>
<app-expert-registry *ngIf="activeSection === ACTIVABLE_SECTIONS.INT || activeSection === ACTIVABLE_SECTIONS.BEN" [chosenSection]="activeSection"></app-expert-registry>

</section>
