import {
  Component,
  Output,
  Input,
  EventEmitter,
  ElementRef,
  OnChanges,
  AfterViewChecked,
  SimpleChanges,
  Renderer2,
  OnDestroy
} from '@angular/core';

@Component({
  selector: 'app-tab-manager',
  templateUrl: './tab-manager.component.html',
  styleUrls: ['./tab-manager.component.css']
})
export class TabManagerComponent
  implements AfterViewChecked, OnChanges, OnDestroy {
  @Input() assets: any[] = [];
  @Input() selectedAsset: any = {};
  @Input() readOnly = false;
  @Input() canAddAsset = true;
  @Input() isAppraisalConvalidated = false;
  @Input() activeCategories;
  @Output() onSelectAsset = new EventEmitter();
  @Output() onNewAsset = new EventEmitter();

  layoutDone = false;
  shouldShowDropdown = true;
  hiddenAssets: any[];
  maxVisibleTabs = 0;

  private onResizeFunction: any;

  constructor(private elementRef: ElementRef, private _renderer: Renderer2) {
    this.onResizeFunction = _renderer.listen(
      'window',
      'resize',
      this.debounce(() => {
        this.layout();
      })
    );
  }

  private debounce(func: () => void, wait = 70) {
    let h: any;
    return () => {
      clearTimeout(h);
      h = setTimeout(() => func(), wait);
    };
  }

  ngAfterViewChecked() {
    if (!this.assets || this.layoutDone) {
      return;
    }
    setTimeout(() => this.layout(), 0);
  }



  ngOnChanges(changes: SimpleChanges) {
    // Se non è cambiato nulla devo ritornare
    setTimeout(() => this.layout(), 0);
  }

  ngOnDestroy() {
    this.onResizeFunction(); // Remove the listener on the resize event
  }

  getAssetAddress(asset): string {
    return `Indirizzo \n` +
      `${asset.adress ? asset.adress : 'N/A'}, ${asset.numAddress ? asset.numAddress : ''} \n` +
      `${asset.cap ? asset.cap : ''} ${asset.city ? asset.city : ''} ${asset.province ? asset.province : ''} \n` +
      `Foglio ${asset.tabRegSheet ? asset.tabRegSheet : 'N/A'} \n` +
      `Mappale ${asset.tabRegPart ? asset.tabRegPart : 'N/A'} \n` +
      `Subalterno ${asset.tabRegSub ? asset.tabRegSub : 'N/A'}`;
  }

  triggerSelectAsset(asset) {
    if (asset !== this.selectedAsset) {
      this.onSelectAsset.emit(asset);
    }
  }

  triggerNewAssetModal() {
    this.onNewAsset.emit();
  }

  // Calcola il numero massimo di tab visibili in base alla larghezza della
  // finestra, e nasconde i tab restanti dentro a un menu a tendina
  layout() {
    if (!this.assets) {
      return;
    }

    const domElement = this.elementRef.nativeElement.querySelector('.MyTabs');
    const tabContainerWidth = domElement.offsetWidth;
    let tabWidth = 160; // larghezza ideale di un tab
    const someTab = domElement.querySelector('.MyTabs__Tab');

    const dropdownToggleWidth = domElement.querySelector('#addAssetButton')
      .offsetWidth;

    if (someTab) {
      tabWidth = someTab.offsetWidth;
    }

    this.maxVisibleTabs = Math.floor(
      (tabContainerWidth - 2 * dropdownToggleWidth) / tabWidth
    );
    this.layoutDone = true;
    this.shouldShowDropdown = this.assets.length > this.maxVisibleTabs;
  }
}
