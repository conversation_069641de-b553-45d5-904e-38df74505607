import { Component, OnInit, Inject } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';
import { GenericTaskService } from '../../services/generic-task/generic-task.service';
import { PositionService } from '../../../shared/position/position.service';
import { UserDataService } from '../../../shared/user-data/user-data.service';
import { UserData } from '../../../shared/user-data/user-data'; 

 @Component({
  selector: 'app-upload-docs',
  templateUrl: './upload-docs.component.html',
  styleUrls: ['./upload-docs.component.css']
})

export class UploadDocsComponent implements OnInit {
  positionId: string;
  wizardCode = this.constants.wizardCodes['PER'];
  taskId: string;
  taskCod: string;
  lockingUser: string;
  isChecklistComplete = this.checkChecklistCompleteness.bind(this);

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private genericTaskService: GenericTaskService,
    private positionService: PositionService,
    private userDataService: UserDataService,
    @Inject(APP_CONSTANTS) public constants: IAppConstants) 
  {}

  ngOnInit() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.positionId = params['positionId'];
      this.taskId = params['taskId'];
      this.taskCod = params['taskCod'];
    });
     
  }

  checkChecklistCompleteness(): Observable<boolean> {
    return this.genericTaskService
      .checklistAcquiredCheck(this.positionId)
      .switchMap((items: number[]) => {
        return Observable.from([items.length === 0]);
      });
  }

  goOnDashboard() {
    this.router.navigateByUrl('dashboard/LAA');
  }

  taskLocked(){
   this.userDataService.getAll().subscribe((res:UserData)=>{
    this.lockingUser=res.username
   })
  }

  taskLockedByOtherUser(user: string) {
    this.lockingUser = user;
  }

  taskUnlocked() {
    this.lockingUser = undefined;
  }
}
