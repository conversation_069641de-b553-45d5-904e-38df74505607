import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  Inject
} from '@angular/core';
import { AppraisalCompilationService } from '../../../../../service/appraisal-compilation.service';
import { DomainService } from '../../../../../../shared/domain/domain.service';
import { APP_CONSTANTS, IAppConstants } from '../../../../../../app.constants';
@Component({
  selector: 'app-construction-site-security-modal',
  templateUrl: './construction-site-security-modal.component.html',
  styleUrls: ['./construction-site-security-modal.component.css']
})
export class ConstructionSiteSecurityModalComponent implements OnInit {
  @Input() modalType: string;               // add / modify  
  @Input() isOpen: boolean;
  @Input() property;                        // contiene l'oggetto per l'aggiunta o modifica
  @Input() propertyIndex;                   // indice dell'oggetto in modifica, null quando modal aggiunta
  @Output() modalClose = new EventEmitter();  
  @Output() modalSubmit = new EventEmitter();  
  propertyCopy; // Copia dell'oggett originale per permettere la modifica dei valori  
  personRoleDomain: any[];

  constructor(
    private appraisalCompilationService: AppraisalCompilationService,
    private domainService: DomainService,
    @Inject(APP_CONSTANTS) public _constants: IAppConstants
  ) {}

  ngOnInit() {
    this.propertyCopy = JSON.parse(JSON.stringify(this.property));        
    this.domainService.newGetDomain('UBZ_DOM_PERSON_ROLE').subscribe(x => {
      this.personRoleDomain = x;
    });
  }

  closeModal() {
    this.modalClose.emit();
  }

  submit() {
    this.modalSubmit.emit({
      property: this.propertyCopy,
      index: this.propertyIndex
    });
  }
}
