<form #f="ngForm" (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
          {{'UBZ.SITE_CONTENT.111010001' | translate }}
          <span class="state" [ngClass]="{'red': f.invalid, 'green': f.valid}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <table class="uc-table">
          <thead>
            <tr>
              <th scope="col" class="col-sm-4 text-center" colspan="2">{{'UBZ.SITE_CONTENT.111010010' | translate }}</th>
            </tr>
            <tr>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.110111000' | translate }}</th>
              <th scope="col" class="col-sm-2" style="text-align: left">{{'UBZ.SITE_CONTENT.111010011' | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let domain of (expenseTypes | domainMapToDomainArray); let index = index">
              <ng-container *ngIf="model && model.appFarmLoss && model.appFarmLoss[domain.domCode]">
                <td>{{ domain.translationCod | translate }}</td>
                <td *ngIf="domain.domCode !== 'ALE'">
                  <app-importo [name]="'interessi-' + index" [required]="false" [(ngModel)]="model.appFarmLoss[domain.domCode].lossAmount"
                    (ngModelChange)="calculateTotal(domain.domCode, $event)">
                  </app-importo>
                </td>
                <td *ngIf="domain.domCode === 'ALE'">{{ (model && model.appFarmLoss && model.appFarmLoss[domain.domCode]) ? (model.appFarmLoss[domain.domCode].lossAmount
                  | currency:'EUR':'1.2-2') : ''}}</td>
              </ng-container>
            </tr>
          </tbody>
        </table>
        <div class="panel-box">
          <div class="row">
            <div class="col-sm-12 form-group">
              <label>
                <span>
                  <i class="icon-search note-tooltip" [tooltip]="model.docNotes" triggers="click"></i>
                </span>
                {{'UBZ.SITE_CONTENT.111010100' | translate }}
              </label>
              <textarea class="form-control" name="documentazione" [(ngModel)]="model.docNotes"></textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
  </accordion-group>
</form>
