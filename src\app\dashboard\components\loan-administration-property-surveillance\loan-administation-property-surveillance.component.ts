import { Component, OnInit } from '@angular/core';
import { Observable } from 'rxjs/Observable';

import { DashboardDetailComponent } from '../dashboard-detail.component';
import { SearchService } from '../../../shared/search/search.service';
import { DomainService } from '../../../shared/domain/domain.service';

@Component({
  selector: 'app-loan-administation-property-surveillance',
  templateUrl: '../dashboard-detail.component.html',
  styleUrls: ['./loan-administation-property-surveillance.component.css']
})
export class LoanAdministationPropertySurveillanceComponent extends DashboardDetailComponent
  implements OnInit {
  headFields = this.pageHeaders['LAPS'];
  fieldsConditions = this.pageFieldConditions['LAPS'];

  constructor(
    public searchService: SearchService,
    public domainService: DomainService
  ) {
    super(domainService, searchService);
  }

  ngOnInit() {
    super.ngOnInit();
  }

  getPositionsCounts(
    inChargeUser: boolean,
    inChargeBranch: boolean
  ): Observable<any> {
    return this.searchService.getRequestsRESCounts(
      inChargeUser,
      inChargeBranch
    );
  }

  getPositionsData(
    excel: boolean,
    page: number,
    pageSize: number,
    orderBy: string,
    asc: boolean,
    filter: any
  ): Observable<any> {
    return this.searchService.getRequestsRESData(
      excel,
      page,
      pageSize,
      orderBy,
      asc,
      filter
    );
  }
}
