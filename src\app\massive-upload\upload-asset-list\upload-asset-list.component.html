<div class="col-sm-12 section-headline">
  <h1>{{'UBZ.SITE_CONTENT.11111100100' | translate}}</h1>
  <section id="breadcrumbs" class="breadcrumbs">
    <div class="row">
      <div class="col-sm-12">
        <ul>
          <li><a [routerLink]="['/massive-upload']">{{'UBZ.SITE_CONTENT.10001111001' | translate}}</a></li>
          <li>{{'UBZ.SITE_CONTENT.11111100100' | translate}}</li>
        </ul>
      </div>
    </div>
  </section>
</div>

<section id="funds">
  <table class="table table-hover">
    <thead>
      <tr>
        <th scope="col">{{'UBZ.SITE_CONTENT.11110100011' | translate}}</th>
        <th scope="col">{{'UBZ.SITE_CONTENT.100000000100' | translate}}</th>
        <th scope="col">{{'UBZ.SITE_CONTENT.11111111101' | translate}}</th>
         <th scope="colgroup" colspan="2">{{'UBZ.SITE_CONTENT.10101011011' | translate}}</th>
        <th scope="col"></th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngFor="let row of assetList; let rowIndex = index">
        <tr>
          <td data-label="">{{ row.resItemId }}</td>
          <td data-label="">{{ row.esgFlag }}</td>
          <td data-label="">{{ row.riskFlag }}</td>
           <td data-label=""><span class="state"
              [ngClass]="{ 'red': row.outcome === 'KO', 'green': row.outcome === 'OK', 'yellow': row.outcome === 'W'}"></span></td>
          <td>
              <a *ngIf="!row.errorDescription" role="button" (click)="goToAssetPage(row.resItemId)"><i class="icon-angle-double-right"></i></a>
              <button *ngIf="row.errorDescription"
                class="btn btn-empty" (click)="handleLogModal(row)"><i class="icon-note"></i></button>
          </td>
        </tr>
      </ng-container>
    </tbody>
  </table>

  <div class="row" *ngIf="listSize > 10">
    <div class="col-sm-6">
      <div class="results">
        <span>{{'UBZ.SITE_CONTENT.1111111' | translate }}</span>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="pageSize" (change)="changePageSize()">
            <option value="10" selected>10 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}</option>
            <option *ngIf="listSize > 20" value="20">20 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}
            </option>
            <option *ngIf="listSize > 30" value="30">30 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}
            </option>
            <option *ngIf="listSize > 40" value="40">40 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}
            </option>
            <option *ngIf="listSize > 50" value="50">50 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}
            </option>
            <option *ngIf="listSize <= 50" [ngValue]="listSize">{{listSize}}
              {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}</option>
          </select>
        </div>
      </div>
    </div>
    <div class="col-sm-6">
      <nav aria-label="Page navigation" class="pull-right">
        <pagination [boundaryLinks]="true" [directionLinks]="false" [totalItems]="listSize" [(ngModel)]="page"
          [itemsPerPage]="pageSize" (pageChanged)="changePage($event)" class="pagination" previousText="&lsaquo;"
          nextText="&rsaquo;" firstText="&laquo;" lastText="&raquo;" [maxSize]="10"></pagination>
      </nav>
    </div>
  </div>
</section>

<div *ngIf="showLogError" class="modal fade" id="assetLog" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #assetLogError="bs-modal" (onHidden)="handleLogModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.10000010100' | translate }}</h2>
        <button type="button" class="close pull-right" aria-label="Close" (click)="handleLogModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">                                    
        <div class="row" style="padding-right: 20px;padding-left: 20px;">
          <div *ngIf="selectedAsset?.errorDescription">
            <div class="col-sm-6 form-group">
              <label>{{'UBZ.SITE_CONTENT.10011010100' | translate }} </label>
            </div>
            <div class="col-sm-6 form-group">
              <label> {{selectedAsset?.errorDescription}} </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>