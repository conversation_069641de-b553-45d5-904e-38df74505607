// Angular modules
import { BrowserModule } from '@angular/platform-browser';
import { NgModule, LOCALE_ID } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpModule, Http } from '@angular/http';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RoundProgressModule } from 'angular-svg-round-progressbar';
import { CurrencyPipe } from '@angular/common';
import { PdfViewerModule } from 'ng2-pdf-viewer';

// Externals modules
import { ToastModule } from 'ng2-toastr/ng2-toastr';
import { NguiDatetimePickerModule } from '@ngui/datetime-picker';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { AccordionModule } from 'ngx-bootstrap/accordion';
import { ModalModule } from 'ngx-bootstrap/modal';
import { PaginationModule } from 'ngx-bootstrap/pagination';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { ButtonsModule } from 'ngx-bootstrap/buttons';
import { TooltipModule, TooltipConfig } from 'ngx-bootstrap/tooltip';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { SlickModule } from 'ngx-slick';
import { CookieModule } from 'ngx-cookie';
import { DragulaModule } from 'ng2-dragula/ng2-dragula';
import { CurrencyMaskModule } from 'ng2-currency-mask';

// Model
import { APP_CONSTANTS, AppConstants } from './app.constants';

// Application modules
import { AppRoutingModule } from './app-routing.module';
import {
  AuthGuard,
  AccessRightsGuard,
  DashboardAccessRightsGuard,
  DeactivateGuard,
} from './guards';

// Components
import { AppComponent } from './app.component';
import {
  CustomerSearchComponent,
  CustomerInfoComponent,
  LandingComponent,
  AssetInfoComponent,
  AssetListComponent,
  AssetSearchComponent,
  NewAssetComponent,
  GenericInfoComponent,
  ConfirmComponent,
  SummaryComponent,
  WizardContainerComponent,
  NdgListManagerComponent,
} from './simulation';
import { ApplicationFormComponent } from './shared/application-form/components/application-form.component';
import { NavigationFooterComponent } from './shared/navigation-footer/navigation-footer.component';
import { WizardComponent } from './shared/wizard/components/wizard.component';
import { HeadMenuComponent } from './shared/menu/components/head-menu/head-menu.component';
import { SideMenuComponent } from './shared/menu/components/side-menu/side-menu.component';
import { SpinnerComponent } from './shared/spinner/spinner.component';
import { PositionHeaderComponent } from './shared/position/position-header/position-header.component';
import { GuaranteeInfoComponent } from './appraisal-request/components/guarantee-info/guarantee-info.component';
import { DashboardComponent } from './dashboard/components/dashboard.component';
import { DashboardDetailComponent } from './dashboard/components/dashboard-detail.component';
import { SimulationsDashboardComponent } from './dashboard/components/simulation/simulations-dashboard.component';
import { RequestDashboardComponent } from './dashboard/components/request/request-dashboard.component';
import { SearchMenuComponent } from './shared/menu/components/search-menu/search-menu.component';
import { NotesComponent } from './shared/notes/components/notes.component';
import { WizardDetailComponent } from './wizard-detail/components/wizard-detail.component';
import { AllDashboardComponent } from './dashboard/components/all/all-dashboard.component';
import { CounterComponent } from './dashboard/components/counter/counter.component';
import { LoanAdministationPropertySurveillanceComponent } from './dashboard/components/loan-administration-property-surveillance/loan-administation-property-surveillance.component';
import { LoanAdministationSampleChecksComponent } from './dashboard/components/loan-administration-sample-checks/loan-administation-sample-checks.component';
import { LoanAdministationShippingSurveillanceComponent } from './dashboard/components/loan-administration-shipping-surveillance/loan-administation-shipping-surveillance.component';
import { PeritalCompanyMonitoringComponent } from './dashboard/components/peritial-company-monitoring/perital-company-monitoring.component';
import { WellAcquaintedMonitoringComponent } from './dashboard/components/well-acquainted-monitoring/well-acquainted-monitoring.component';
import { ChecklistComponent } from './shared/checklist/components/checklist.component';
import { AppraisalInfoComponent } from './appraisal-request/components/appraisal-info/appraisal-info.component';
import { LoanAdministrationAllComponent } from './dashboard/components/loan-administration-all/loan-administration-all.component';
import { ModalFileAssociatiComponent } from './shared/checklist/components/modals/modal-file-associati/modal-file-associati.component';
import { ModalUploadSingleComponent } from './shared/checklist/components/modals/modal-upload-single/modal-upload-single.component';
import { ModalNuovoDocumentoComponent } from './shared/checklist/components/modals/modal-nuovo-documento/modal-nuovo-documento.component';
import { ModalNoteComponent } from './shared/checklist/components/modals/modal-note/modal-note.component';
import { ActivitiesDetailComponent } from './shared/activities-detail/component/activities-detail.component';
import { AssociatedGuaranteesTableComponent } from './shared/wizard-detail/components/associated-guarantees-table/associated-guarantees-table.component';
import { AppraisalsTableComponent } from './shared/wizard-detail/components/appraisals-table/appraisals-table.component';
import { CustomerGeneralDataComponent } from './shared/wizard-detail/components/customer-general-data/customer-general-data.component';
import { ModalHistoryComponent } from './shared/checklist/components/modals/modal-history/modal-history.component';
import { ModalInvalidComponent } from './shared/checklist/components/modals/modal-invalid/modal-invalid.component';
import { ActivityHistoryTableComponent } from './shared/wizard-detail/components/activity-history-table/activity-history-table.component';
import { AccordionApplicationFormComponent } from './shared/application-form/components/accordion-application-form/accordion-application-form.component';
import {
  GenericTaskComponent,
  TaskSynthesisComponent,
  TaskAssignmentComponent,
  ExpertTableComponent,
  SendAppraisalComponent,
  ExpertSearchModalComponent,
  UploadDocsComponent,
  AppraisalDetailComponent,
  SiteInspectionResultComponent,
  SiteInspectionDateComponent,
  ControlResultComponent,
  SampleChecksAppraisalComponent,
  UploadXmlComponent,
} from './tasks';
import {
  GenericDataComponent,
  JobAssignmentComponent,
} from './appraisal-compilation/components/generic-data';
import { GuaranteesComponent } from './appraisal-compilation/components/guarantees/guarantees.component';
import {
  EvaluationsComponent,
  EstimationTabComponent,
  PriceEvaluationTabComponent,
  PriceEvaluationModalComponent,
  MerchantabilityComponent,
  PhotovoltaicComponent,
  MarketConsiderationComponent,
  ReportSummaryComponent,
  ComparablesComponent,
} from './appraisal-compilation/components/evaluations';
import { SALComponent } from './appraisal-compilation/components/sal/sal.component';
import { AppraisalFillConfirmComponent } from './appraisal-compilation/components/appraisal-fill-confirm/appraisal-fill-confirm.component';
import { TaskAccessRightsComponent } from './shared/access-rights/task-access-rights.component';
import { TaskButtonComponent } from './shared/access-rights/task-button.component';
import { ModalLockConfirmComponent } from './shared/position/modal-lock-confirm/modal-lock-confirm.component';
import { DropAssignmentButtonComponent } from './shared/drop-assignment-button/drop-assignment-button.component';
import { BuildingAmnestyComponent } from './appraisal-compilation/components/guarantees/accordions/building-amnesty/building-amnesty.component';
import { ConsistencyComponent } from './appraisal-compilation/components/guarantees/accordions/consistency/consistency.component';
import { RenovationManufacturedComponent } from './appraisal-compilation/components/guarantees/accordions/renovation-manufactured/renovation-manufactured.component';
import { InstallationsComponent } from './appraisal-compilation/components/guarantees/accordions/installations/installations.component';
import { PropertyComponent } from './appraisal-compilation/components/guarantees/accordions/property/property.component';
import { OriginComponent } from './appraisal-compilation/components/guarantees/accordions/origin/origin.component';
import { ConstructionSiteSecurityComponent } from './appraisal-compilation/components/guarantees/accordions/construction-site-security/construction-site-security.component';
import { BuildingMeasuresComponent } from './appraisal-compilation/components/guarantees/accordions/building-measures/building-measures.component';
import { OverallJobStatisticsComponent } from './appraisal-compilation/components/sal/overall-job-statistics/overall-job-statistics.component';
import { EndJobEvaluationComponent } from './appraisal-compilation/components/sal/end-job-evaluation/end-job-evaluation.component';
import { CostPrevisionTabComponent } from './appraisal-compilation/components/sal/cost-prevision-tab/cost-prevision-tab.component';
import { PropertyModalComponent } from './appraisal-compilation/components/guarantees/accordions/property/property-modal/property-modal.component';
import { NewAssetModalComponent } from './appraisal-compilation/components/guarantees/modal/new-asset/new-asset-modal.component';
import { ConstructionSiteSecurityModalComponent } from './appraisal-compilation/components/guarantees/accordions/construction-site-security/construction-site-security-modal/construction-site-security-modal.component';
import { QteComponent } from './appraisal-compilation/components/qte/qte.component';
import { FinancialPlanComponent } from './appraisal-compilation/components/qte/accordions/financial-plan/financial-plan.component';
import { TotalCostsComponent } from './appraisal-compilation/components/qte/accordions/total-costs/total-costs.component';
import { AgrarianAgencyComponent } from './appraisal-compilation/components/agrarian-agency/agrarian-agency.component';
import { SalaryCalculationComponent } from './appraisal-compilation/components/agrarian-agency/components/salary-calculation/salary-calculation.component';
import { GrossProductionComponent } from './appraisal-compilation/components/agrarian-agency/components/gross-production/gross-production.component';
import { AdvancedSearchComponent } from './search-page/component/advanced-search/advanced-search.component';
import { FastSearchComponent } from './search-page/component/fast-search/fast-search.component';
import { RegisterComponent } from './appraisal-compilation/components/register/register.component';
import { GoodDescriptionComponent } from './appraisal-compilation/components/good-description/good-description.component';
import { ConstructionInfoComponent } from './appraisal-compilation/components/sal/construction-info/construction-info.component';
import { ShippingCostTableComponent } from './appraisal-compilation/components/sal/shipping-cost-table/shipping-cost-table.component';
import { DifferencesModalComponent } from './shared/differences-modal/differences-modal.component';
import { ThirdOpinionComponent } from './appraisal-compilation/components/third-opinion/third-opinion.component';
import { AppraisalTemplateValidationComponent } from './appraisal-compilation/components/appraisal-template-validation/appraisal-template-validation.component';
import { TaskListAccessRightsComponent } from './shared/access-rights/task-list-access-rights.component';
import { ReportComponent } from './report';
import { RestrictionsComponent } from './appraisal-compilation/components/restrictions/restrictions.component';
import { ConfigurationComponent } from './configuration/component/configuration.component';
import { SampleChecksComponent } from './configuration/component/sample-checks/sample-checks.component';
import { ExpertAssignmentConfigurationComponent } from './configuration/component/expert-assignment-configuration/expert-assignment-configuration.component';
import { AppraisalConfigurationComponent } from './configuration/component/appraisal-configuration/appraisal-configuration.component';
import { RegistryComponent } from './registry/registry.component';
import { ExpertFirmRegistryComponent } from './registry/component/expert-firm-registry/expert-firm-registry.component';
import { ExpertFirmInfoComponent } from './registry/children/expert-firm-info/expert-firm-info.component';
import { ExpertFirmGenericInfoComponent } from './registry/children/expert-firm-info/component/expert-firm-generic-info/expert-firm-generic-info.component';
import { DriverAssignmentComponent } from './registry/children/expert-firm-info/component/driver-assignment/driver-assignment.component';
import { TabManagerComponent } from './appraisal-compilation/components/guarantees/tab-manager/tab-manager.component';
import { AssociatedExpertComponent } from './registry/children/expert-firm-info/component/associated-expert/associated-expert.component';
import { InsurancePolicyComponent } from './registry/children/expert-firm-info/component/insurance-policy/insurance-policy.component';
import { BelongingBoardComponent } from './registry/children/expert-firm-info/component/belonging-board/belonging-board.component';
import { ExpertCertificationComponent } from './registry/children/expert-firm-info/component/expert-certification/expert-certification.component';
import { ExpertIdCardComponent } from './registry/children/expert-firm-info/component/expert-id-card/expert-id-card.component';
import { JudicialRecordComponent } from './registry/children/expert-firm-info/component/judicial-record/judicial-record.component';
import { ExpertRegistryComponent } from './registry/component/expert-registry/expert-registry.component';
import { CompetenceProvincesComponent } from './registry/children/expert-firm-info/component/competence-provinces/competence-provinces.component';
import { ExpertActionsHandlerComponent } from './registry/children/expert-firm-info/component/expert-actions-handler/expert-actions-handler.component';
import { ExpertAppraisalsComponent } from './registry/children/expert-firm-info/component/completed-expert-appraisals/expert-appraisals.component';
import { SampleChecksTableComponent } from './configuration/component/sample-checks-table/sample-checks-table.component';
import { MassiveUploadComponent } from './massive-upload/massive-upload.component';
import {
  GuaranteeTransferWizardComponent,
  GuaranteeTransferConfirmComponent,
  StartGuaranteesComponent,
  TargetGuaranteesComponent,
  NdgSearchComponent,
} from './guarantee-transfer';
import { AppraisalMigrationComponent } from './appraisal-migration/components/appraisal-migration.component';
import { AppraisalMigrationInfoComponent } from './appraisal-migration/components/appraisal-info/appraisal-migration-info.component';
import { AppraisalMigrationAssetInfoComponent } from './appraisal-migration/components/asset-info/appraisal-migration-asset-info.component';
import { MigrationTabManagerComponent } from './appraisal-migration/components/migration-tab-manager/migration-tab-manager.component';
import { AppraisalCtuComponent } from './appraisal-ctu/components/appraisal-ctu.component';
import { CollateralModalComponent } from './search-page/component/advanced-search/collateral-modal.component';
import { UploadListComponent } from './massive-upload/upload-list/upload-list.component';
import { RedirectComponent } from './redirect';
import { HomeComponent } from './home/<USER>/home.component';
import { CustomModalComponent } from './shared/custom-modal/custom-modal.component';
import { GuaranteeAssetListComponent } from './guarantee-asset-list/guarantee-asset-list.component';
import { ExternalMortageComponent } from './external-mortage/external-mortage.component';
import { ExternalMortageProcessComponent } from './external-mortage/components/external-mortage-process/external-mortage-process.component';

import {
  GuaranteeFractionationComponent,
  GuaranteeFractionationWizardComponent,
  GuaranteeOnArrivalComponent,
  GuaranteeSummaryComponent,
} from './guarantee-fractionation';
import { SelectAppraisalsComponent } from './guarantee-transfer/components/select-appraisals/select-appraisals.component';
import { SurveyDetailComponent } from './tasks/components/survey-detail/survey-detail.component';
import { DocumentSuspendComponent } from './document-suspend/components/document-suspend.component';
import { ApiLogStatusComponent } from './tasks/components/api-log-status/api-log-status.component';
import { AssetTaskComponent } from './tasks/components/asset-task/asset-task.component';
import { AssetWithCollateralComponent } from './tasks/components/asset-task/asset-with-collateral/asset-with-collateral.component';
import { AssetNoCollateralComponent } from './tasks/components/asset-task/asset-no-collateral/asset-no-collateral.component';
import { AmeoriListComponent } from './ameori/component/ameori-list/ameori-list.component';
import { AmeoriResearchComponent } from './ameori/component/ameori-research/ameori-research.component';
import { AmeoriUpdateComponent } from './ameori/component/ameori-update/ameori-update.component';

// Services
import { AmeoriService } from './ameori/service/ameori.service';
import { CustomHttpService } from './shared/http/custom-http.service';
import { MessageService } from './shared/messages/services/message.service';
import { TranslationsLoader } from './shared/translations/translations-loader.service';
import {
  CustomerService,
  GenericInfoService,
  LandingService,
  AssetService,
} from './simulation';
import { MenuService } from './shared/menu/services/menu.service';
import { PropertiesService } from './shared/properties/properties.service';
import { PositionService } from './shared/position/position.service';
import { AccessRightsService } from './shared/access-rights/services/access-rights.service';
import { UserDataService } from './shared/user-data/user-data.service';
import { AuthKeyDirective } from './shared/access-rights/auth-key.directive';
import { DomainService } from './shared/domain/domain.service';
import { SearchPageService } from './search-page/service/search-page.service';
import { WizardDetailService } from './wizard-detail/services/wizard-detail.service';
import { ChecklistService } from './shared/checklist/service/checklist.service';
import { ModalButtonService } from './shared/drop-assignment-button/service/modal-button.service';
import { WizardService } from './shared/wizard/services/wizard.service';
import { ActivitiesDetailService } from './shared/activities-detail/service/activities-detail.service';
import { ApiService } from './shared/api/api.service';
import { GenericTaskService } from './tasks';
import { AccordionAPFService } from './shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { GuaranteeTransferService } from './guarantee-transfer/services/guarantee-transfer.service';

// Directives
import { OnlyNumbersDirective } from './shared/directives/only-numbers/only-numbers.directive';
import { ForcePatternDirective } from './shared/directives/force-pattern/force-pattern.directive';
import { ValidatorDirective } from './shared/directives/validator/validator.directive';
import { DateReadOnlyDirective } from './shared/directives/date-read-only/date-read-only.directive';
import { ValidatorPercentageDirective } from './shared/directives/validatorPercentage/validator-percentage.directive';
import { MinValidatorDirective } from './shared/directives/min-validator/min-validator.directive';
import { MaxValidatorDirective } from './shared/directives/max-validator/max-validator.directive';

// Pipes
import { FilterFieldsPipe } from './shared/pipes/filter-fields/filter-fields.pipe';
import { DomainMapToDomainArrayPipe } from './shared/pipes/domain-map-to-domain-array/domain-map-to-domain-array.pipe';
import { StatusMapToStatusArrayPipe } from './shared/pipes/status-map-to-status-array/status-map-to-status-array.pipe';
import { CustomTimePipe } from './shared/pipes/custom-time/custom-time.pipe';
import { ObjectMapToObjectArrayPipe } from './shared/pipes/object-map-to-object-array/object-map-to-object-array.pipe';
import { SortCategoryTypePipe } from './shared/pipes/sort-category-type/sort-category-type.pipe';
import { ReplaceStringPipe } from './shared/pipes/replace-string/replace-string.pipe';
import { SortRegCategoryTypePipe } from './shared/pipes/sort-reg-category-type/sort-reg-category-type.pipe';
import { RemoveFromDomainPipe } from './shared/pipes/remove-from-domain/remove-from-domain.pipe';
import { TextMaskModule } from 'angular2-text-mask';

// Shared custom component
import { ImportoComponent } from './shared/importo/importo.component';
import { CalendarioComponent } from './shared/calendario/calendario.component';
import { GuaranteeFractionationService } from './guarantee-fractionation/services/guarantee-fractionation.service';
import { GuaranteeTransferComponent } from './guarantee-transfer/components/guarantee-transfer.component';
import { AmeoriPageComponent } from './ameori/ameori-page.component';
import { TabManagedAssetDetailsComponent } from './shared/asset/tab-managed-asset-details/tab-managed-asset-details.component';
import { IndividualAssignmentComponent } from './configuration/component/individual-assignment/individual-assignment.component';
import { ConfigurationService } from './configuration/service/configuration.service';
import { IndividualAssignmentCardComponent } from './configuration/component/individual-assignment/individual-assignment-card/individual-assignment-card.component';
import { AggiungiModalComponent } from './configuration/component/individual-assignment/aggiungi-modal/aggiungi-modal.component';
import { CustomDateTimePipe } from './shared/pipes/custom-date-time/custom-date-time.pipe';
import { UploadAssetListComponent } from './massive-upload/upload-asset-list/upload-asset-list.component';
import { SortDomainByLengthPipe } from './shared/pipes/sort-domain-by-length/sort-domain-by-length.pipe';
import { SharedService } from './shared/services/shared.service';
import { ApplicationFormService } from './shared/application-form/services';
import { EvaluateConditionPipe } from './shared/pipes/evaluate-condition/evaluate-condition.pipe';
import { ChecklistServicingComponent } from './search-servicing/components/checklist-servicing/checklist-servicing.component';
import { SearchServicingComponent } from './search-servicing/search-servicing.component';
import { DocumentComponent } from './search-servicing/components/document/document.component';
import { PhysicalRiskComponent } from './appraisal-compilation/components/generic-data/physical-risk/physical-risk.component';
import { PhysicalRiskService } from './appraisal-compilation/service/physical-risk.service';
import { PaymentComponent } from './simulation/components/payment/payment.component';
import { PaymentService } from './simulation/services/payment/payment.service';
import { CustomerPaymentDataComponent } from './shared/wizard-detail/components/customer-payment-data/customer-payment-data.component';
import { AppraisalValuesComponent } from './shared/wizard-detail/components/appraisal-values/appraisal-values.component';
import { SilosInterfaceComponent } from './new-Silos/silos-interface/silos-interface.component';
import { SilosService } from './new-Silos/silos.service';


export function getAlertConfig(): TooltipConfig {
  return Object.assign(new TooltipConfig(), {
    placement: 'right',
  });
}

@NgModule({
  declarations: [
    AssetWithCollateralComponent,
    AssetNoCollateralComponent,
    AssetTaskComponent,
    AmeoriPageComponent,
    AmeoriUpdateComponent,
    AmeoriResearchComponent,
    AmeoriListComponent,
    GuaranteeTransferComponent,
    SelectAppraisalsComponent,
    DocumentSuspendComponent,
    ImportoComponent,
    CalendarioComponent,
    AppComponent,
    CustomerSearchComponent,
    CustomerInfoComponent,
    AssetInfoComponent,
    AssetListComponent,
    AssetSearchComponent,
    NewAssetComponent,
    LandingComponent,
    HomeComponent,
    ApplicationFormComponent,
    OnlyNumbersDirective,
    ForcePatternDirective,
    NavigationFooterComponent,
    WizardComponent,
    GenericInfoComponent,
    SummaryComponent,
    ConfirmComponent,
    HeadMenuComponent,
    SideMenuComponent,
    AuthKeyDirective,
    SpinnerComponent,
    PositionHeaderComponent,
    FilterFieldsPipe,
    DomainMapToDomainArrayPipe,
    StatusMapToStatusArrayPipe,
    GuaranteeInfoComponent,
    DashboardComponent,
    DashboardDetailComponent,
    SimulationsDashboardComponent,
    SearchMenuComponent,
    RequestDashboardComponent,
    NotesComponent,
    WizardDetailComponent,
    AllDashboardComponent,
    CounterComponent,
    LoanAdministationPropertySurveillanceComponent,
    LoanAdministationSampleChecksComponent,
    LoanAdministationShippingSurveillanceComponent,
    PeritalCompanyMonitoringComponent,
    WellAcquaintedMonitoringComponent,
    ChecklistComponent,
    AppraisalInfoComponent,
    LoanAdministrationAllComponent,
    ModalFileAssociatiComponent,
    ModalUploadSingleComponent,
    ModalNuovoDocumentoComponent,
    ModalNoteComponent,
    ActivitiesDetailComponent,
    AssociatedGuaranteesTableComponent,
    AppraisalsTableComponent,
    CustomerGeneralDataComponent,
    ModalHistoryComponent,
    ModalInvalidComponent,
    ActivityHistoryTableComponent,
    AccordionApplicationFormComponent,
    WizardContainerComponent,
    GenericTaskComponent,
    TaskSynthesisComponent,
    TaskAssignmentComponent,
    ExpertTableComponent,
    GenericDataComponent,
    GuaranteesComponent,
    EvaluationsComponent,
    SALComponent,
    AppraisalFillConfirmComponent,
    TaskAccessRightsComponent,
    TaskButtonComponent,
    ModalLockConfirmComponent,
    DropAssignmentButtonComponent,
    SendAppraisalComponent,
    EstimationTabComponent,
    PriceEvaluationTabComponent,
    BuildingAmnestyComponent,
    ConsistencyComponent,
    RenovationManufacturedComponent,
    InstallationsComponent,
    PropertyComponent,
    OriginComponent,
    ConstructionSiteSecurityComponent,
    BuildingMeasuresComponent,
    OverallJobStatisticsComponent,
    EndJobEvaluationComponent,
    CostPrevisionTabComponent,
    PropertyModalComponent,
    ExpertSearchModalComponent,
    UploadDocsComponent,
    NewAssetModalComponent,
    PriceEvaluationModalComponent,
    AppraisalDetailComponent,
    CustomTimePipe,
    ConstructionSiteSecurityModalComponent,
    QteComponent,
    FinancialPlanComponent,
    TotalCostsComponent,
    MerchantabilityComponent,
    AgrarianAgencyComponent,
    SalaryCalculationComponent,
    GrossProductionComponent,
    DateReadOnlyDirective,
    AdvancedSearchComponent,
    FastSearchComponent,
    RegisterComponent,
    PhotovoltaicComponent,
    GoodDescriptionComponent,
    ObjectMapToObjectArrayPipe,
    SortCategoryTypePipe,
    SiteInspectionDateComponent,
    SiteInspectionResultComponent,
    ControlResultComponent,
    ConstructionInfoComponent,
    ShippingCostTableComponent,
    DifferencesModalComponent,
    ThirdOpinionComponent,
    AppraisalTemplateValidationComponent,
    TaskListAccessRightsComponent,
    ReportComponent,
    RestrictionsComponent,
    ConfigurationComponent,
    ReplaceStringPipe,
    SampleChecksComponent,
    ExpertAssignmentConfigurationComponent,
    AppraisalConfigurationComponent,
    RegistryComponent,
    ExpertFirmRegistryComponent,
    ExpertFirmInfoComponent,
    ExpertFirmGenericInfoComponent,
    DriverAssignmentComponent,
    TabManagerComponent,
    AssociatedExpertComponent,
    InsurancePolicyComponent,
    BelongingBoardComponent,
    ExpertCertificationComponent,
    ExpertIdCardComponent,
    JudicialRecordComponent,
    ExpertRegistryComponent,
    CompetenceProvincesComponent,
    ExpertActionsHandlerComponent,
    ExpertAppraisalsComponent,
    SampleChecksAppraisalComponent,
    SampleChecksTableComponent,
    ValidatorDirective,
    JobAssignmentComponent,
    SortRegCategoryTypePipe,
    SortDomainByLengthPipe,
    RemoveFromDomainPipe,
    ValidatorPercentageDirective,
    MinValidatorDirective,
    MaxValidatorDirective,
    MassiveUploadComponent,
    GuaranteeTransferWizardComponent,
    GuaranteeTransferConfirmComponent,
    StartGuaranteesComponent,
    TargetGuaranteesComponent,
    NdgSearchComponent,
    AppraisalMigrationComponent,
    AppraisalMigrationInfoComponent,
    AppraisalMigrationAssetInfoComponent,
    MigrationTabManagerComponent,
    AppraisalCtuComponent,
    CollateralModalComponent,
    UploadXmlComponent,
    UploadListComponent,
    RedirectComponent,
    NdgListManagerComponent,
    MarketConsiderationComponent,
    ReportSummaryComponent,
    ComparablesComponent,
    CustomModalComponent,
    GuaranteeAssetListComponent,
    GuaranteeFractionationComponent,
    GuaranteeFractionationWizardComponent,
    GuaranteeOnArrivalComponent,
    GuaranteeSummaryComponent,
    ExternalMortageComponent,
    ExternalMortageProcessComponent,
    SurveyDetailComponent,
    ApiLogStatusComponent,
    TabManagedAssetDetailsComponent,
    IndividualAssignmentComponent,
    IndividualAssignmentCardComponent,
    AggiungiModalComponent,
    CustomDateTimePipe,
    UploadAssetListComponent,
    EvaluateConditionPipe,
    ChecklistServicingComponent,
    SearchServicingComponent,
    DocumentComponent,
    PhysicalRiskComponent,
    PaymentComponent,
    CustomerPaymentDataComponent,
    AppraisalValuesComponent,
    SilosInterfaceComponent,
   
 
  ],
  imports: [
    BrowserModule,
    PdfViewerModule,
    FormsModule,
    ReactiveFormsModule,
    HttpModule,
    AppRoutingModule,
    RoundProgressModule,
    ToastModule.forRoot(),
    NguiDatetimePickerModule,
    BrowserAnimationsModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useClass: TranslationsLoader,
      },
    }),
    AccordionModule.forRoot(),
    ModalModule.forRoot(),
    PaginationModule.forRoot(),
    BsDropdownModule.forRoot(),
    TabsModule.forRoot(),
    ButtonsModule.forRoot(),
    CookieModule.forRoot(),
    SlickModule.forRoot(),
    DragulaModule,
    TooltipModule.forRoot(),
    CurrencyMaskModule,
    TextMaskModule,
  ],
  exports: [ImportoComponent, CalendarioComponent],
  providers: [
    AmeoriService,
    AccordionAPFService,
    GenericTaskService,
    AssetService,
    LandingService,
    ConfigurationService,
    GenericInfoService,
    MessageService,
    CustomerService,
    MenuService,
    DomainService,
    AuthGuard,
    AccessRightsGuard,
    DashboardAccessRightsGuard,
    DeactivateGuard,
    AccessRightsService,
    PropertiesService,
    PositionService,
    UserDataService,
    SearchPageService,
    WizardDetailService,
    ChecklistService,
    WizardService,
    ActivitiesDetailService,
    ApiService,
    CurrencyPipe,
    GuaranteeFractionationService,
    GuaranteeTransferService,
    ModalButtonService,
    SharedService,
    PhysicalRiskService,
    ApplicationFormService,
    PaymentService,
    SilosService,
    { provide: Http, useClass: CustomHttpService },
    { provide: APP_CONSTANTS, useValue: AppConstants },
    { provide: LOCALE_ID, useValue: 'it-IT' },
    { provide: TooltipConfig, useFactory: getAlertConfig },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
