import {
  Component,
  OnInit,
  Input,
  HostListener,
  ViewChild,
  EventEmitter
} from '@angular/core';
import { Router } from '@angular/router';
import { NguiDatetime } from '@ngui/datetime-picker';

// Servizi
import { MenuService } from '../../services/menu.service';
import { SearchPageService } from '../../../../search-page/service/search-page.service';
import { DomainService } from '../../../domain/domain.service';
import { PositionService } from '../../../position/position.service';
import { AccessRightsService } from '../../../access-rights/services/access-rights.service';

// Modelli
import { SearchFieldsMap } from '../../model/search-fields-map';
import { Domain } from '../../../domain/domain';

NguiDatetime.formatDate = (date: Date): string => {
  let day = date.getDate().toString();
  let month = (date.getMonth() + 1).toString();
  const year = date.getFullYear().toString();

  if (day.length === 1) {
    day = `0${day}`;
  }
  if (month.length === 1) {
    month = `0${month}`;
  }
  return `${day}-${month}-${year}`;
};
NguiDatetime.parseDate = (str: any): Date => {
  const day = parseInt(str.substring(0, 2), 10);
  const month = parseInt(str.substring(3, 5), 10) - 1;
  const year = parseInt(str.substring(6), 10);
  return new Date(year, month, day);
};
NguiDatetime.firstDayOfWeek = 1;

@Component({
  selector: 'app-search-menu',
  templateUrl: './search-menu.component.html',
  styleUrls: ['./search-menu.component.css']
})
export class SearchMenuComponent implements OnInit {
  @Input() isSide: boolean;
  @Input() isOpened: EventEmitter<boolean>;
  @ViewChild('fastInput') fastInput;

  iconArrowStyle = { true: 'icon-arrow_up', false: 'icon-arrow_down' };
  fastSearch = '';
  searchType: string;
  fieldValues = {};
  searchFieldsMap = new SearchFieldsMap();
  public domainsMap: any = {};
  ExpSocList: any;
  arrayList = [];
  searchTypes: { key: string; value: string }[] = [];

  constructor(
    public menuService: MenuService,
    public searchPageService: SearchPageService,
    private router: Router,
    private domainService: DomainService,
    private positionService: PositionService,
    private accessRightsService: AccessRightsService
  ) { }

  ngOnInit() {
    const subscription = this.accessRightsService.authKeysSource.subscribe(
      (functions: string[]) => {
        if (functions.indexOf('UBZ_SEARCH_APPRAISAL_MODE') > -1) {
          this.searchTypes.push({
            key: 'PE',
            value: 'UBZ.SITE_CONTENT.101100111'
          });
          if (!this.searchType) {
            this.searchType = 'PE';
          }
        }
        if (functions.indexOf('UBZ_SEARCH_REQUEST_MODE') > -1) {
          this.searchTypes.push({
            key: 'RI',
            value: 'UBZ.SITE_CONTENT.10100011'
          });
          this.searchType = 'RI';
        }
        if (functions.indexOf('UBZ_SEARCH_ASSOCIATED_ASSET_MODE') > -1) {
          this.searchTypes.push({
            key: 'ASAS',
            value: 'UBZ.SITE_CONTENT.11101'
          });
          if (!this.searchType) {
            this.searchType = 'ASAS';
          }
        }
        if (functions.indexOf('UBZ_SEARCH_ASSET_MODE') > -1) {
          this.searchTypes.push({
            key: 'AS',
            value: 'UBZ.SITE_CONTENT.10001111'
          });
          if (!this.searchType) {
            this.searchType = 'AS';
          }
        }
        subscription.unsubscribe();
      }
    );
    this.isOpened.subscribe(x => {
      if (x === true) {
        setTimeout(() => {
          this.resetSearch();
          this.focusOnInputText();
        }, 0);
      }
    });
    this.searchPageService.getExpSocList().subscribe(x => {
      x.forEach(item => {
        this.arrayList.push(item);
      });
    });
  }

  focusOnInputText() {
    if (this.fastInput && this.fastInput.nativeElement) {
      this.fastInput.nativeElement.focus();
    }
  }

  resetSearch() {
    this.fastSearch = '';
    this.searchType = this.searchTypes[0].key;
    this.calculateDomains();
  }

  isSearchButtonDisabled(): boolean {
    if (!this.fieldValues) {
      return true;
    }
    let activeFilters = 0;
    for (const key in this.fieldValues) {
      if (this.fieldValues.hasOwnProperty(key)) {
        if (this.fieldValues[key] && this.fieldValues[key] !== '') {
          activeFilters++;
        }
      }
    }
    if (activeFilters < 1) {
      return true;
    }
    return false;
  }

  doNewAdvancedSearch() {
    this.searchPageService
      .doNewAdvancedSearch(1, 10, this.fieldValues, this.searchType)
      .subscribe(result => {
        this.searchPageService.advancedSearch['searchResults'] = result.positions;
        this.searchPageService.advancedSearch['count'] = result.count;
        this.menuService.isSearchBoxOpen = false;
        this.resetSearch();
        this.menuService.isAdvancedSearchOpen = false;
        this.menuService.isSideSearchOpen = false;
        this.menuService.sideMenuPropriety = 'toggled';
        // FIXME - HA SENSO PASSARE DA EMPTYREDIRECT ?
        // 'Trucco' neccessario per triggare ngOnInit() del componente nel caso in cui vogliamo reindirizzare allo stesso URL
        this.router
          .navigateByUrl('/emptyRedirect', { skipLocationChange: true })
          .then(() => this.router.navigate(['/advancedSearch']));
        // this.router.navigate(['/advancedSearch']);
      });
  }

  doNewFastSearch() {
    this.searchPageService.doNewFastSearch(this.fastSearch).subscribe(result => {
      this.searchPageService.fastSearch['searchResults'] = result;
      this.menuService.isSearchBoxOpen = false;
      this.resetSearch();
      // FIXME - HA SENSO PASSARE DA EMPTYREDIRECT ?
      this.router
        .navigateByUrl('/emptyRedirect', { skipLocationChange: true })
        .then(() => this.router.navigate(['/fastSearch']));
      // this.router.navigate(['/fastSearch']);
    });
  }

  private transform(value: any): any {
    const keys = [];
    for (const key in value) {
      if (value.hasOwnProperty(key)) {
        keys.push({
          domCode: value[key].domCode,
          translationCod: value[key].translationStatusCod
        });
      }
    }
    return keys;
  }

  calculateDomains() {
    this.fieldValues = {};
    this.domainsMap = {};
    for (const el of this.searchFieldsMap[this.searchType]) {
      this.fieldValues[el.name] = '';
      if ((el.type === 'select' || el.type==='selectOrigProcess') && el.domTableName) {
        if (el.domTableName === 'UBZ_DOM_STATUS') {
          this.domainService.getStatusName().subscribe(x => {
            this.domainsMap[el.name] = this.transform(x);
          });
        } else {
          this.domainService.newGetDomain(el.domTableName, el.domTableParentCode).subscribe(x => {
            this.domainsMap[el.name] = x;
          });
        }
      }
    }
  }

  closeSearchBox() {
    this.menuService.isSearchBoxOpen = false;
  }

  // Riporta l'utente all'ultimo elenco risultati ricercati
  backToSearch() {
    this.closeSearchBox();
    this.searchPageService.toggleBackSearchButton();
    this.router.navigate([this.searchPageService.searchUrlBack]);
  }

  resetExtPositionId() {
    this.fieldValues['externalPositionId'] = '';
  }
}
