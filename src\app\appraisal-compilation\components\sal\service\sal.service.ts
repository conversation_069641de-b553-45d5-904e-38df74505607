import { Injectable } from '@angular/core';
import { Http } from '@angular/http';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class SALService {
  constructor(private _http: Http) {}

  public getSalData(
    positionId: string,
    appraisalType: string
  ): Observable<any> {
    positionId = encodeURIComponent(positionId);
    if (appraisalType !== 'IND' && appraisalType !== 'SHI') {
      appraisalType = 'ALL';
    }
    const urlMap = {
      IND: '/UBZ-ESA-RS/service/salService/salIndustStatic/',
      SHI: '/UBZ-ESA-RS/service/salService/salMobStatic/',
      ALL: '/UBZ-ESA-RS/service/salService/salgetdata/'
    };
    const url = urlMap[appraisalType] + positionId;
    return this._http.get(url).map(res => res.json());
  }

  public saveSalData(positionId: string, statics: any): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = '/UBZ-ESA-RS/service/salService/salsavedata/' + positionId;
    const objToSave = {};
    objToSave['staticsave'] = statics;
    return this._http.post(url, objToSave).map(res => res.json());
  }

  public saveSalIndustrialData(
    positionId: string,
    toSave: any
  ): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = '/UBZ-ESA-RS/service/salService/salIndustStatic/' + positionId;
    return this._http.post(url, toSave).map(res => res.json());
  }

  public saveSalShippingData(positionId: string, toSave: any): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = '/UBZ-ESA-RS/service/salService/salMobStatic/' + positionId;
    return this._http.post(url, toSave).map(res => res.json());
  }
}
