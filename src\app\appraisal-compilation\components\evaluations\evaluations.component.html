<!-- FIXME - IN DEV MODE ABBIAMO UN ECCEZIONE DOVUTO AL FATTO CHE CHECKSAVEENABLE è BINDATO DIRETTAMENTE AL BUTTON DI PROSEGUI
 PRIMA IL METODO ERA INVOCATO QUANDO I COMPONENTI IN PAGINA SCATENAVANO GLI EVENTI CHANGE E CALENDARCHANGE, BISOGNA TROVARE UNA SOLUZIONE-->
<fieldset [disabled]="!_landingService.positionLocked">
  <fieldset [disabled]="_landingService.isLockedTask[currentTask] || haveDisabledFields" *ngIf="isParsed">
    <accordion class="panel-group" id="accordion" *ngIf="!isShipping && !isAereomobile && !industrialLoan">
      <fieldset>
				<!-- Sezione visibile solo per perizie individuals -->
				<ng-container *ngIf="_landingService.posSegment === 'IND' && retrivedIndData && !isTemplateLight && !isTemplateUpdate">
					<app-market-consideration [marketConsideration] = "marketConsideration"></app-market-consideration>
					<app-report-summary [reportSummaryObject] = "reportSummary"></app-report-summary>
					<app-comparables [comparablesList] = "comparables" ></app-comparables>
				</ng-container>
				<!--  -->
				<app-accordion-application-form
					[overwriteAPFData]="true"
					[idCode]="positionId"
					page="VALUTAZIONI"
					[positionId]="positionId"
					[drivers]="{'DD_ORD_PAG': 1}"
					[formDisabled] = "_landingService.isLockedTask[currentTask] || haveDisabledFields">
				</app-accordion-application-form>

				<app-estimation-tab
					[list]="consistencyList"
					[fields]="singleFields"
					[consistencyTypes]="consistencyTypes"
					[isTemplateUpdate]="isTemplateUpdate"
					[opType]="opType">
				</app-estimation-tab>

				<app-price-evaluation-tab  *ngIf="!(isTemplateLight && _landingService.posSegment === 'IND') && !isTemplateUpdate"
					[objectList]="rilevazioni"
					[noteRilevazione]="noteRilevazionePrezzi"
					[evalObj]="objAppEval">
				</app-price-evaluation-tab>

				<app-accordion-application-form
					[overwriteAPFData]="true"
					[idCode]="positionId"
					page="VALUTAZIONI"
					[positionId]="positionId"
					[drivers]="{'DD_ORD_PAG': 3}"
					[formDisabled] = "_landingService.isLockedTask[currentTask] || haveDisabledFields">
				</app-accordion-application-form>

				<app-merchantability
					*ngIf="agrarianLoan && !isTemplateUpdate"
					[model]="merchantabilityModel"
					[investmentSummaryString]="riepilogoInvestimenti">
				</app-merchantability>
      </fieldset>
      <fieldset class="validation-sect">
				<app-appraisal-template-validation
					*ngIf="_positionService.isInternalSecondOpinion && !(isTemplateLight && _landingService.posSegment === 'IND')"
					[positionId]="positionId"
					templateName="VALUTAZIONI"
					[haveDisabledFields]="haveDisabledFields">
				</app-appraisal-template-validation>
      </fieldset>
    </accordion>

    <accordion class="panel-group" id="accordion" *ngIf="industrialLoan">
			<fieldset>
				<app-accordion-application-form
					[overwriteAPFData]="true"
					[idCode]="positionId"
					page="VALUTAZIONI"
					[positionId]="positionId"
					[formDisabled] = "_landingService.isLockedTask[currentTask] || haveDisabledFields">
				</app-accordion-application-form>

				<app-photovoltaic
					*ngIf="photovoltaicData && photovoltaicData.fotoAppraisal && !isTemplateUpdate"
					[model]="photovoltaicData">
				</app-photovoltaic>
			</fieldset>

			<fieldset class="validation-sect">
				<app-appraisal-template-validation
					*ngIf="_positionService.isInternalSecondOpinion"
					[positionId]="positionId"
					templateName="VALUTAZIONI"
					[haveDisabledFields]="haveDisabledFields">
				</app-appraisal-template-validation>
			</fieldset>
    </accordion>

    <accordion class="panel-group" id="accordion" *ngIf="isShipping || isAereomobile">
      <fieldset>
				<app-accordion-application-form
					[overwriteAPFData]="true"
					[idCode]="positionId"
					page="VALUTAZIONI"
					[positionId]="positionId"
					[drivers]="{'DD_ORD_PAG': 1}"
					[formDisabled] = "_landingService.isLockedTask[currentTask] || haveDisabledFields">
        </app-accordion-application-form>
      </fieldset>
      <fieldset class="validation-sect">
				<app-appraisal-template-validation *ngIf="_positionService.isInternalSecondOpinion" [positionId]="positionId" templateName="VALUTAZIONI" [haveDisabledFields]="haveDisabledFields"></app-appraisal-template-validation>
      </fieldset>
    </accordion>
  </fieldset>

  <app-navigation-footer showSaveDraft="true" showPrevious="true" [saveIsEnable]="checkSaveEnable()" [showCancelButton]="false"
  (saveButtonClick)="saveData()" (previousButtonClick)="goToPreviousTask()" (closeDraftButtonClick)="goToGenericTask()"
  [saveDraftCallback]="saveDraftCallback" [activeTaskCode]="currentTask"></app-navigation-footer>
</fieldset>
