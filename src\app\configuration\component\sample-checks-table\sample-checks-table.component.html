<div class="row controls">
  <div class="col-sm-12 section-headline">
    <h1><i class="icon-dashboard"></i> {{'UBZ.SITE_CONTENT.1011011000' | translate }}</h1>
    <h2>{{'UBZ.SITE_CONTENT.1011011001' | translate }}</h2>
  </div>
  <div class="col-sm-12 text-right">
    <button  class="btn btn-empty pull-right" type="button" (click)="saveConfiguration()">{{'UBZ.SITE_CONTENT.100001' | translate | uppercase}}</button>
  </div>
</div>
<div *ngIf="page" class="row">
  <div class="col-sm-12 form-group">
    <br>
    <table class="uc-table" id="table-1">
      <thead>
        <tr>
          <th scope="col" class="col-sm-2 text-center">{{'UBZ.SITE_CONTENT.1011011010' | translate }}</th>
          <th scope="col" class="col-sm-2 text-center">{{'UBZ.SITE_CONTENT.1011011011' | translate }}</th>
          <th scope="col" class="col-sm-2 text-center">{{'UBZ.SITE_CONTENT.1011011100' | translate }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let row of page">
          <td data-label="Progressivo">{{row.questionDomTranslationCode | translate}}</td>
          <td data-label="Forma">{{ row.value}}</td>
          <td data-label="Descrizione">
            <input appOnlyNumbers type="text" class="form-control" [(ngModel)]="row.percentage">
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
