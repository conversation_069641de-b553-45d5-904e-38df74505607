<app-ameori-research  [pageSize]="pageSize" (getSearchResult)="fillAmeori($event)"></app-ameori-research>
<div style="margin-top: 1%; margin-left: 1%; position: absolute" *ngIf="(isSearchClicked && isEmptyList)">
  <p>{{ 'UBZ.SITE_CONTENT.11101101011' | translate }}</p>
</div>

<div *ngIf="isPopUpOpen" class="modal fade" id="aggiorna-importo" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #stateBox="bs-modal" (onHidden)="closeModal()" [config]="{show: 'true'}">
  <app-ameori-update (modalClose)="closeModal()" (onConfirm)="closeModal()" [ameori]="selectedAmeori"></app-ameori-update>
</div>

<div class="row" >
  <div *ngIf="(isSearchClicked && !isEmptyList)">
  <app-ameori-list (openModal)="openModal($event)" [ameoriList]="ameoriList" [reportType]="reportType" [reportNumber]="reportNumber"></app-ameori-list>
  </div>
  <div style="margin-top: 1%" *ngIf="count > 10" class="col-sm-6">
    <div class="results">
      <span>{{'UBZ.SITE_CONTENT.1111111' | translate }}</span>
      <div class="custom-select">
        <select class="form-control" [(ngModel)]="pageSize" (change)="changePageSize()">
          <option value="10" selected>10 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{ count }}</option>
          <option *ngIf="count > 20" value="20">20 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{ count }}</option>
          <option *ngIf="count > 30" value="30">30 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{ count }}</option>
          <option *ngIf="count > 40" value="40">40 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{ count }}</option>
          <option *ngIf="count > 50" value="50">50 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{ count }}</option>
          <option *ngIf="count <= 50" value="count"> {{ count }}
            {{'UBZ.SITE_CONTENT.10000000' | translate }} {{ count }}</option>
        </select>
      </div>
    </div>
  </div>
  <div style="margin-top: 1%" class="col-sm-6" class="pull-right">
    <pagination [boundaryLinks]="true" [directionLinks]="false" [totalItems]="count" [ngModel]="page"
      [itemsPerPage]="pageSize" [maxSize]="10" (pageChanged)="changePage($event)" class="pagination" previousText="&lsaquo;"
      nextText="&rsaquo;" firstText="&laquo;" lastText="&raquo;"></pagination>
  </div>
</div>
