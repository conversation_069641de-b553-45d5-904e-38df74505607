import {
  Component,
  OnInit,
  Input,
  ViewChild,
  Output,
  EventEmitter
} from '@angular/core';
import { NgForm } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';

import { Domain } from '../domain/domain';
import { ModalButtonService } from './service/modal-button.service';
import { Note } from '../notes/model/note';
import { NotesService } from '../notes/services/notes.service';
import { MessageService } from '../../shared/messages/services/message.service';

@Component({
  selector: 'app-drop-assignment-button',
  templateUrl: './drop-assignment-button.component.html',
  styleUrls: ['./drop-assignment-button.component.css'],
  providers: [NotesService]
})
export class DropAssignmentButtonComponent implements OnInit {
  @Input() positionId: string;
  // type can be 'SOS' (for "Sospensione incarico") and 'REV' (for "Revoca incarico") only
  @Input() type: string;
  titles = {
    SOS: 'UBZ.SITE_CONTENT.11111100',
    REV: 'UBZ.SITE_CONTENT.11111101',
    RIA: 'UBZ.SITE_CONTENT.**********',
    ABB: 'UBZ.SITE_CONTENT.10010001000'
  };
  selectLabels = {
    SOS: 'UBZ.SITE_CONTENT.11111110',
    REV: 'UBZ.SITE_CONTENT.11111111',
    ABB: 'UBZ.SITE_CONTENT.10010001001'
  };
  buttonLabels = {
    SOS: 'UBZ.SITE_CONTENT.11111100',
    REV: 'UBZ.SITE_CONTENT.11111101',
    RIA: 'UBZ.SITE_CONTENT.**********',
    ABB: 'UBZ.SITE_CONTENT.10010001000'
  };
  noteTitle = {
    SOS: 'UBZ.SITE_CONTENT.**********',
    REV: 'UBZ.SITE_CONTENT.**********',
    RIA: 'UBZ.SITE_CONTENT.**********',
    ABB: 'UBZ.SITE_CONTENT.10010001010'
  };
  isOpen = false;
  reasonTypesDomain: Domain[] = [];
  model: Note = new Note();
  @ViewChild(NgForm) form: NgForm;
  @Output() saveSuccessful = new EventEmitter();
  @Output() saveFailure = new EventEmitter();

  constructor(
    private _modalButtonService: ModalButtonService,
    private _noteService: NotesService,
    private _translateService: TranslateService,
    private _messageService: MessageService
  ) { }

  ngOnInit() {
    this.createDefaultNote();
  }

  createDefaultNote() {
    this.model.positionId = this.positionId;
    this.model.noteType = this.type;
    this._translateService.get(this.noteTitle[this.type]).subscribe(trad => {
      this.model.noteTitle = trad;
    });
    this.model.positionType = 'PER';
  }

  hide() {
    this.isOpen = false;
  }

  open() {
    this._modalButtonService.getReasons(this.type).subscribe(res => {
      this.reasonTypesDomain = res;
      if (this.type === 'RIA') {
        this.model.reasonCode = res['RIA'].domCode;
      }
    });
    this.isOpen = true;
  }

  // Centralizza la gestione delle response per i servizi nel metodo submitted
  // Per unificare, la response viene letta e restituita come stringa
  checkResponse(response): string {
    if (response.toString() === 'true') {
      this.saveSuccessful.emit();
    } else {
      this.saveFailure.emit();
    }
    this.hide();
    return response.toString();
  }

  submitted() {
    if (!this.form.form.valid) {
      return;
    }
    // In caso di sospensione si emette immediatamente l'output con l'oggetto note da salvare.
    // Il componente padre richiama il servizio specifico che include salvataggio della nota e sospensione della pratica
    if (this.type === 'SOS') {
      return this.saveSuccessful.emit(this.model);
    } else {
      if(this.type ==='RIA') {
          this._modalButtonService.reopenAppraisal(this.positionId, this.model).subscribe(() => {
            this.saveSuccessful.emit();
            this.hide();
          }, () => {
            this.saveFailure.emit();
          }
        );
      }
      else this._noteService.saveNote(this.model).subscribe(res => {
        if(this.type === 'REV') {
            this._modalButtonService.dropAssignment(this.type, this.positionId).subscribe(response => {
              this.checkResponse(response);
            });
        }
        if(this.type === 'ABB') {
          this._modalButtonService
            .abandonAppraisal(this.positionId)
            .subscribe(response => {
              if (this.checkResponse(response) === 'true') {
                this._messageService.showSuccess(
                  this._translateService.instant('UBZ.SITE_CONTENT.1001100000'),
                  this._translateService.instant('UBZ.SITE_CONTENT.1001100001')
                );
              }
            });
        }
      });
    }
  }

  isSopensione(): boolean {
    return this.type === 'SOS';
  }
}
