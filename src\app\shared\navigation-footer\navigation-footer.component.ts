import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { WizardService } from '../wizard/services/wizard.service';
import { Observable } from 'rxjs/Observable';
import { LandingService } from '../../simulation/services/landing/landing.service';
import { PositionService } from '../../shared/position/position.service';
import { ConfigurationService } from '../../configuration/service/configuration.service';

@Component({
  selector: 'app-navigation-footer',
  templateUrl: './navigation-footer.component.html',
  styleUrls: ['./navigation-footer.component.css'],
})

export class NavigationFooterComponent implements OnInit {
  private positionId: string;
  private wizardCode: string;
  private _saveIsEnable = true;
  private _undoIsEnabled = false;
  _valConf: any;
  messagesArray: string[] = [];
  isApiAppraisal: boolean = false;
  showDraftModal = false;
  isBackTask: boolean; // Se è true, rimane abilitato solo il bottone "modifica"
  modifyModalIsOpen = false;
  onIndividualAssignment = false;
  lastModificationInfo: any;
  modificationsUser: string;
  modificationsDate: string;
  appraisalId: string;

  @Input() showPrevious: boolean;
  @Input() showSaveDraft: boolean;
  @Input() showCancelButton = true;
  @Input() footerClass: string;
  @Input() confirmButtonString: string;
  @Input() cancelButtonString: string;
  @Input() activeTaskCode: string;
  @Input() showSaveButton = true;
  @Input() saveDraftCallback: () => Observable<boolean>;
  @Input() saveDraftDisabled = false;
  @Output() saveButtonClick = new EventEmitter();
  @Output() undoButtonClick = new EventEmitter();
  @Output() restoreButtonClick = new EventEmitter();
  @Output() closeDraftButtonClick = new EventEmitter();
  @Output() previousButtonClick = new EventEmitter();
  @Output() cancelButtonClick = new EventEmitter();
  @Input() isFrazionamento = false;
  @Input() isModifyActive = false;
  @Output() frazionamentoModify = new EventEmitter();


  constructor(
    private _wizardService: WizardService,
    private _activatedRoute: ActivatedRoute,
    public _positionService: PositionService,
    public _landingService: LandingService,
    private _configurationService: ConfigurationService,
  ) { }
  ngOnInit() {
    this._activatedRoute.params.subscribe((params: Params) => {
      if (params['configurationCode'] === 'IND') {
        this.onIndividualAssignment = true;

      }
      this.positionId = params['positionId'];
      this.wizardCode = params['wizardCode'];
      this.appraisalId = this.positionId;
      this._positionService.$appraisalInfoObject.subscribe((appraisalInfo) => {
        this.messagesArray = new Array();
        if (appraisalInfo && appraisalInfo.appraisal.lastFromApiFlag === 'Y') {
          this.isApiAppraisal = true;
          this.messagesArray.push('UBZ.SITE_CONTENT.1001101101');
          this.messagesArray.push('UBZ.SITE_CONTENT.10010101100');
        } else {
          this.messagesArray.push('UBZ.SITE_CONTENT.1001101101');
          this.messagesArray.push('UBZ.SITE_CONTENT.1001101110');
        }
      });

    });


  }

  @Input()
  set saveIsEnable(value: boolean) {
    this._saveIsEnable = value;
  }
  @Input()
  set undoIsEnable(value: boolean) {
    this._undoIsEnabled = value;
  }

  @Input()
  set configurationInfo(value: any) {
    this._valConf = value;
  }

  get getConfigurationInfo(): any {
    return this._valConf;
  }
  get saveIsEnable(): boolean {
    return this._saveIsEnable;
  }
  get undoIsEnable(): boolean {
    return this._undoIsEnabled;
  }

  saveAndGoAhead() {

    this._activatedRoute.params.subscribe((params: Params) => {
      if (params['configurationCode'] === 'IND') {
        this._configurationService.onIndividualAssignmentSave();
       
      } else {
        this.saveButtonClick.emit();
      }
    });

  }

  undoModification() {
    this.undoButtonClick.emit();
  }

  restoreModification() {
    this.restoreButtonClick.emit();
  }

  saveDraft() {
    this.closeDraftButtonClick.emit();
  }

  previous() {

    this.previousButtonClick.emit();

  }

  cancelPosition() {
    this.cancelButtonClick.emit();
  }

  callSaveDraftFunction() {
    if (this.saveDraftCallback) {
      this.saveDraftCallback().subscribe((res) => {
        this.showDraftModal = true;
      });
    } else {
      this.showDraftModal = true;
    }
  }

  hideDraftModal() {
    this.showDraftModal = false;
  }

  /**
   * @function
   * @name modifify
   * @description Metodo invocato su azione "modifica", invalida il task corrente per permetterne
   * la ri-lavorazione. Nel caso di perizia API il modifica invalida tutto il wizard e riporta sul
   * primo step
   */
  modify() {
    this._wizardService
      .invalidateTask(
        this.positionId,
        this.wizardCode,
        this.isApiAppraisal ? 'UBZ-PER-DGE' : this.activeTaskCode
      )
      .switchMap(() => {
        return Observable.of(this._landingService.refreshWizard());
      })
      .subscribe(() => {
        if (this.isApiAppraisal) {
          this._landingService.goToTask('UBZ-PER-DGE', this._activatedRoute);
        } else {
          delete this._landingService.isLockedTask[this.activeTaskCode];
        }
      });
    return;
  }

  openModifyModal() {
    this.modifyModalIsOpen = true;

  }

  submitModifyModal() {
    if (this.isFrazionamento) {
      this.frazionamentoModify.emit();
    } else {
      this.modify();
    }
    this.closeModifyModal();
  }

  closeModifyModal() {
    this.modifyModalIsOpen = false;
  }
}

