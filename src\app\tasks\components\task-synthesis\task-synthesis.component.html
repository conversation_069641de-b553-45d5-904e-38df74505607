<div class="box">
  <h4>{{'UBZ.SITE_CONTENT.110000010' | translate }}</h4>
  <div class="row">
    <div class="col-sm-6">
      <label>{{'UBZ.SITE_CONTENT.10010001' | translate }}</label>
    </div>
    <div class="col-sm-6">
      {{positionId}}
    </div>
  </div>
  <div class="row">
    <div class="col-sm-6">
      <label>{{'UBZ.SITE_CONTENT.1010001' | translate}}</label>
    </div>
    <div class="col-sm-6">
      {{translatedMacroProcess}}
    </div>
  </div>
  <div class="row">
    <div class="col-sm-6">
      <label>{{'UBZ.SITE_CONTENT.1011001' | translate }}</label>
    </div>
    <div class="col-sm-6">
      {{translatedAppraisalType}}
    </div>
  </div>
  <div class="row">
    <div class="col-sm-6">
      <label>{{'UBZ.SITE_CONTENT.110000011' | translate }}</label>
    </div>
    <div class="col-sm-6">
      {{ (flagExternalAppraisal ? 'UBZ.SITE_CONTENT.100011011' : 'UBZ.SITE_CONTENT.100011100') | translate }}
    </div>
  </div>
  <div class="row">
    <div class="col-sm-6">
      <label>{{'UBZ.SITE_CONTENT.1110001' | translate }}</label>
    </div>
    <div class="col-sm-6">
      {{ translatedLoanScope }}
    </div>
  </div>
  <div class="row">
    <div class="col-sm-6">
      <label>{{'UBZ.SITE_CONTENT.1011010' | translate }}</label>
    </div>
    <div class="col-sm-6">
      {{ credlineAmount | currency:'EUR':true:'1.2-2' }}
    </div>
  </div>
  <div class="row">
    <div class="col-sm-6">
      <label>{{'UBZ.SITE_CONTENT.1101000101' | translate }}</label>
    </div>
    <div class="col-sm-6">
      {{ translatedAppraisalOwner }}
    </div>
  </div>
  <div class="row" *ngIf="appraisalParentId !== positionId">
    <div class="col-sm-6">
      <label>ID perizia vecchio</label>
    </div>
    <div class="col-sm-6">
      {{ appraisalParentId }}
    </div>
  </div>
  <div *ngIf="originationProcess === 'TGP' || originMigration === 'TGP'" class="row">
    <div class="col-sm-6">
      <label>{{'UBZ.SITE_CONTENT.10011101000' | translate}}</label>
    </div>
    <div class="col-sm-6">
      {{ externalPositionId }}
    </div>
  </div>
  <div *ngIf="originationProcess === 'EMP' || originMigration === 'EMP'" class="row">
    <div class="col-sm-6">
      <label>{{'UBZ.SITE_CONTENT.10011101001' | translate}}</label>
    </div>
    <div class="col-sm-6">
      {{ externalPositionId }}
    </div>
  </div>
  <div *ngIf="originationProcess === 'UB6' || originMigration === 'UB6'" class="row">
    <div class="col-sm-6">
      <label>{{'UBZ.SITE_CONTENT.10100101110' | translate}}</label>
    </div>
    <div class="col-sm-6">
      {{ externalPositionId }}
    </div>
  </div>
  <div  *ngIf="assignmentType !== null" class="row">
    <div class="col-sm-6">
      <label>{{'UBZ.SITE_CONTENT.10010110101' | translate }}</label>
    </div>
    <div class="col-sm-6">
      {{ assignmentType }}
    </div>
  </div>
  <div *ngIf='isTemplateUpdate' class="row">
    <div class="col-sm-6">
      <label>{{'UBZ.SITE_CONTENT.10100101111' | translate }}</label>
    </div>
    <div class="col-sm-6">
      {{updateType ? (updateType | translate) : '---'}}
    </div>
  </div>  
</div>
