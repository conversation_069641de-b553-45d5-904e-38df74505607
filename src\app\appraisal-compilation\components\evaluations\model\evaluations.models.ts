export class ConsistencyTableModel {
  destinazioneUso: string;
  num: number;
  supLorda: number;
  supCommerciale: number;
  volumeVxP: number;
  coefficRagguaglio: number;
  valoreMq: number;
  valUniCom: number;
  supefici: number;
  valComStatFin: number;
  valUniCau: number;
  valCau: number;
}

export class SingleFieldModel {
  totCom: number;
  totCau: number;
  valReal: number;
  valCos: number;
  valActualStat: number;
}

export class Rilevation {
  destination: string;
  omiMin: number;
  omiMax: number;
  nomismaMin: number;
  nomismaMax: number;
  scenariMin: number;
  scenariMax: number;
  otherMin: number;
  otherMax: number;
}

export class MerchantabilityStaticItem {
  amount = 0;
  evaluationId: number;
  investmentId: number;
  investmentType: string;
}

export class MerchantabilityStaticModel {
  ISP: MerchantabilityStaticItem = new MerchantabilityStaticItem();
  MEA: MerchantabilityStaticItem = new MerchantabilityStaticItem();
  OPE: MerchantabilityStaticItem = new MerchantabilityStaticItem();
  OPM: MerchantabilityStaticItem = new MerchantabilityStaticItem();
  STC: MerchantabilityStaticItem = new MerchantabilityStaticItem();
  TOT: MerchantabilityStaticItem = new MerchantabilityStaticItem();
}

export class PriceEvaluationModel {
  appropriateFlag: boolean;
  appropriateOperat: string;
  benchmarkDeviation: string;  
}
