<form #f="ngForm" novalidate (click)="accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
          {{'UBZ.SITE_CONTENT.10010010101' | translate }}
          <span class="state" [ngClass]="{'green': f.valid, 'red': !f.valid}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <textarea class="form-control" name="considerazione" [(ngModel)] = "marketConsideration" required></textarea>
        </div>
      </div>
    </div>
  </accordion-group>
</form>