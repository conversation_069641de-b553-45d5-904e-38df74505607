import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';
import { Observable } from 'rxjs';

@Injectable()
export class PhysicalRiskService {

  constructor(private _http: Http) { }

  getConfigurationTable(): Observable<any> {
    const url = '/UBZ-ESA-RS/service/configuration/v1/physicalRisks';
    return this._http.get(url).map((res) => res.json());
  }

  getUpdateData(appraisalId: string): Observable<any> {
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisals/${appraisalId}/physicalRisk`
    return this._http.get(url).map((res: Response) => res.json());

  }

  updateTableData(appraisalId: string, dataobj: { [key: string]: string }): Observable<any> {
    appraisalId = encodeURIComponent(appraisalId);
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisals/${appraisalId}/physicalRisk`;
    return this._http.put(url, dataobj).map((res) => res.json())

  }

}
