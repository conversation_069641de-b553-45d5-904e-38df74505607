import {
  Compo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Inject,
  ViewChild
} from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { WizardService } from '../../../shared/wizard/services/wizard.service';
import { LandingService } from '../../../simulation/services/landing/landing.service';
import { AgrarianLoanService } from './service/agrarian-loan.service';
import { GrossProductionComponent } from './components/gross-production/gross-production.component';
import { SalaryCalculationComponent } from './components/salary-calculation/salary-calculation.component';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';
import { AgrarianAgencyModel } from './model/agrarian-agency.models';
import { PositionService } from '../../../shared/position/position.service';
import { AppraisalTemplateValidationComponent } from '../appraisal-template-validation/appraisal-template-validation.component';
import { AppraisalCompilationService } from '../../service/appraisal-compilation.service';
import { AccordionApplicationFormComponent } from '../../../shared/application-form/components/accordion-application-form/accordion-application-form.component';

@Component({
  selector: 'app-agrarian-agency',
  templateUrl: './agrarian-agency.component.html',
  styleUrls: ['./agrarian-agency.component.css'],
  providers: [
    AgrarianLoanService,
    AppraisalCompilationService
  ]
})
export class AgrarianAgencyComponent implements OnInit, OnDestroy {
  positionId: string;
  private wizardCode: string;
  currentTask = 'UBZ-PER-AAC';
  private bpmTaskId: string;
  bpmTaskCod: string;
  private appraisalType: string;
  @ViewChild(GrossProductionComponent)
  private grossComponent: GrossProductionComponent;
  @ViewChild(SalaryCalculationComponent)
  private salaryComponent: SalaryCalculationComponent;
  @ViewChild(AppraisalTemplateValidationComponent)
  templateValidationComponent: AppraisalTemplateValidationComponent;
  @ViewChild(AccordionApplicationFormComponent)
  private accApf: AccordionApplicationFormComponent;
  private _subscription;
  saveDraftCallback = this.saveDraft.bind(this);
  saveIsEnable = false;
  haveDisabledFields = false; // Quando true i campi sono disabilitati (ad esempio per perizie SAL o Fine lavori)
  model: AgrarianAgencyModel = new AgrarianAgencyModel();

  constructor(
    private _activatedRoute: ActivatedRoute,
    public _landingService: LandingService,
    private _agrarianLoanService: AgrarianLoanService,
    public _positionService: PositionService,
    private _appraisalCompilationService: AppraisalCompilationService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) { }

  ngOnInit() {
    this._subscription = this._activatedRoute.params
      .switchMap((params: Params) => {
        this.positionId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        this.bpmTaskId = params['taskId'];
        this.bpmTaskCod = params['taskCod'];
        this._landingService.checkIfCompletedTask(
          this.positionId,
          this.currentTask
        );
        return Observable.forkJoin(
          this._agrarianLoanService.getAgrarianData(this.positionId),
          this._positionService.getAppraisalInfo(this.positionId, false)
        );
      })
      .subscribe(res => {
        if (!res[0].partialSaveData) {
          this.model = res[0];
        } else {
          this.model = JSON.parse(res[0].partialSaveData);
        }
        this.setAppraisalInfo(res[1]);
        setTimeout(() => {
          this.getSaveEnableState();
        }, 0);
      });
  }

  /**
   * @name setAppraisalInfo
   * @description Metodo invocato sul recupero dell'oggetto perizia, imposta le variabili ad esso connesse
   * @param appraisalInfo Oggetto perizia recuperto dal wizard container
   */
  setAppraisalInfo(appraisalInfo) {
    this.appraisalType = appraisalInfo.appraisal.appraisalType;

    // Alcune perizie non migrate hanno campi disabilitati
    if (
      appraisalInfo.appraisal.originProcess !== 'MIG' &&
      !appraisalInfo.migParent
    ) {
      // Beni mobili SAL/FLA hanno alcuni campi disabilitati
      this.haveDisabledFields = this.isBeneMobile(appraisalInfo) &&
        (
          this.appraisalType === this.constants.appraisalTypes.FINE_LAVORI ||
          this.appraisalType === this.constants.appraisalTypes.SAL
        );
      // Frazionamento ha alcuni campi disabilitati
      this.haveDisabledFields = this.haveDisabledFields || (this.appraisalType === this.constants.appraisalTypes.FRAZIONAMENTO);
    }
  }

  isBeneMobile(appraisalInfo) {
    return appraisalInfo.appraisal
      && appraisalInfo.appraisal.resItemType
      && appraisalInfo.appraisal.resItemType === 'MOB';
  }

  ngOnDestroy() {
    this._subscription.unsubscribe();
  }

  getSaveEnableState() {
    let val;
    if (!this.grossComponent || !this.salaryComponent || !this.accApf) {
      val = false;
    } else {
      val =
        this.grossComponent.isValid() &&
        this.salaryComponent.isValid() &&
        this.accApf.isAllValid() &&
        (!this.templateValidationComponent ||
          this.templateValidationComponent.form.valid);
    }
    setTimeout(() => {
      this.saveIsEnable = val;
    }, 0);
  }

  goToGenericTask() {
    this._landingService.goToGenericTask(this.positionId, this.bpmTaskId, this.bpmTaskCod);
  }

  goToPreviousTask() {
    this._landingService.goToPreviousTask(this.positionId, this.wizardCode, this._activatedRoute);
  }

  retrieveModel() {
    return this.model;
  }

  saveData() {
    this.savePageData().subscribe(res => {
      this._landingService.goNextPage(
        this.positionId,
        this.currentTask,
        this.wizardCode,
        this._activatedRoute
      );
    });
  }

  savePageData(): Observable<any> {
    const apfs = {};
    for (const key in this.accApf.model) {
      if (this.accApf.model.hasOwnProperty(key)) {
        apfs[key] = this.accApf.model[key];
      }
    }
    return this._agrarianLoanService
      .saveAgrarianData(this.positionId, this.model, apfs)
      .switchMap(() => {
        if (this.templateValidationComponent) {
          return this._appraisalCompilationService.saveTemplateValidation(
            this.templateValidationComponent.appValidation
          );
        } else {
          return Observable.of(true);
        }
      });
  }

  saveDraft(): Observable<any> {
    const apfMap = {};
    apfMap[1] = JSON.stringify(this.accApf.model);
    return this._landingService
      .saveDraft(
        this.positionId,
        JSON.stringify(this.model),
        apfMap,
        'AZ_AGR_COMPLESSO'
      )
      .switchMap(() => {
        if (this.templateValidationComponent) {
          return this._appraisalCompilationService.saveTemplateValidation(
            this.templateValidationComponent.appValidation
          );
        } else {
          return Observable.of(true);
        }
      });
  }
}
