<div *ngIf="isOpen" class="modal fade" id="aggiungi-documento" tabindex="-1" role="dialog"
  aria-labelledby="myModalLabel" bsModal #stateBox="bs-modal" (onHidden)="closeModal(false)" [config]="{show: 'true'}">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2 *ngIf="isUpload === true">{{'UBZ.SITE_CONTENT.11001100' | translate }}</h2>
        <h2 *ngIf="isUpload === false">{{'UBZ.SITE_CONTENT.110100110' | translate }}</h2>
        <button type="button" class="close pull-right" aria-label="Close" (click)="closeModal(false)">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <h4>{{'UBZ.SITE_CONTENT.11001101' | translate }}</h4>
          </div>
          <div class="col-sm-6 form-group">
            <label>{{'UBZ.SITE_CONTENT.1110100' | translate }}</label>
            <input type="text" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.110100100' | translate }}..."
              [(ngModel)]="documentDesc" (change)="checkIfCanUpload()" />
          </div>
          <div class="col-sm-6 form-group">
            <label>{{'UBZ.SITE_CONTENT.11001111' | translate }}*</label>
            <div class="custom-select">
              <select *ngIf="entityTypeDom" class="form-control" name="entita"
                (change)="setSelectedAccordion(); checkIfCanUpload()" [(ngModel)]="entityType">
                <ng-container *ngIf="isDocumentForEntityTypeAppraisal()">
                  <option value="" selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option value="PER">{{'UBZ.CHK_ENTITY_TYPE.PER' | translate }}</option>
                  <option value="ASS">{{'UBZ.CHK_ENTITY_TYPE.ASS' | translate }}</option>
                </ng-container>
                <ng-container *ngIf="!isDocumentForEntityTypeAppraisal()">
                  <option value="" selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <ng-container *ngFor="let accord of accordions"> 
                 <option [value]="accord.offerId" *ngIf="entityTypeDom[accord.entityType] && accord.entityType === 'ASS'">
                      {{ entityTypeDom[accord.entityType].translationCod | translate }} {{
                      accord.resItemCategory }} {{accord.offerId}}
                    </option>
                    <option [value]="accord.offerId" *ngIf="entityTypeDom[accord.entityType] && accord.entityType === 'PER'">
                      {{ entityTypeDom[accord.entityType].translationCod | translate }}
                    </option>
                    <option [value]="accord.offerId" *ngIf="entityTypeDom[accord.entityType] && accord.entityType === 'RIC'">
                      {{ entityTypeDom[accord.entityType].translationCod | translate }}
                    </option>
                   </ng-container>
                   </ng-container>
                
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <ng-container *ngIf="isDocumentForEntityTypeAppraisal()">
            <div class="col-sm-6">
              <label>{{'UBZ.SITE_CONTENT.100000000101' | translate }}*</label>
              <div class="custom-select">
                <select class="form-control" name="categoria documento"
                  (change)="assignDocumentCategory($event);checkIfCanUpload()" [(ngModel)]="selectedOption3">
                  <option value="" selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <ng-container *ngIf="entityType === 'PER'">
                    <option *ngFor="let elem of (allDocCategory | domainMapToDomainArray)" value="{{elem.domCode}}">
                      {{elem.translationCod | translate}}
                    </option>
                  </ng-container>
                  <ng-container *ngIf="entityType === 'ASS'">
                    <option *ngFor="let elem of (allDocCategoryTwo | domainMapToDomainArray)" value="{{elem.domCode}}">
                      {{elem.translationCod | translate}}
                    </option>
                  </ng-container>
                </select>
              </div>
            </div>
          </ng-container>
          <ng-container *ngIf="entityType ==='ASS' && isDocumentForEntityTypeAppraisal()">
            <div class="col-sm-6">
              <label>{{'UBZ.SITE_CONTENT.10001111' | translate }}*</label>
              <div class="custom-select">
                <select class="form-control" name="asset" (change)="assignObjectCode($event);checkIfCanUpload()"
                  [(ngModel)]="selectedOption2">
                  <option value="" selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let obj of allobjectCod" value="{{obj.objectCod }}">
                    {{obj.objectCod}}
                  </option>
                </select>
              </div>
            </div>
          </ng-container>
        </div>
        <ng-container>
          <div class="row">
            <div *ngIf="!isUpload" class="col-sm-4">
              <label></label>
              <div class="custom-checkbox">
                <input type="checkbox" id="document-required" class="checkbox" [(ngModel)]="requiredDocument"
                  (change)="checkIfCanUpload()">
                <label for="document-required">{{'UBZ.SITE_CONTENT.11010001' | translate }}</label>
              </div>
            </div>
            <div *ngIf="!isUpload" class="col-sm-4">
              <label>{{'UBZ.SITE_CONTENT.11010010' | translate }}</label>
              <div class="custom-select">
                <select class="form-control" id="fase-in-cui" [disabled]="!requiredDocument" [(ngModel)]="requiredPhase"
                  (change)="checkIfCanUpload()">
                  <option value="" selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let el of (domains | statusMapToStatusArray)" value="{{el.domCode}}">
                    {{el.translationCod
                    | translate}}</option>
                </select>
              </div>
            </div>
            <div *ngIf="isUpload" class="col-sm-12">
              <h4>{{'UBZ.SITE_CONTENT.11000001' | translate }}</h4>
              <div class="input-group">
                <input type="text" class="form-control" readonly [value]="fileName">
                <label class="input-group-btn">
                  <span class="btn btn-primary waves-effect">
                    {{'UBZ.SITE_CONTENT.11000011' | translate }}&hellip; <input #fileToUpload type="file"
                      style="display: none;" multiple (change)="setFileName(); checkIfCanUpload()">
                  </span>
                </label>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
      <div class="modal-footer">
        <button *ngIf="isUpload" type="button" class="btn btn-primary waves-effect" (click)="uploadFile()"
          [disabled]="!canUpload">{{'UBZ.SITE_CONTENT.11000100' | translate }}</button>
        <button *ngIf="!isUpload" type="button" class="btn btn-primary waves-effect" (click)="requireDocument()"
          [disabled]="!canUpload">{{'UBZ.SITE_CONTENT.110100111' | translate }}</button>
      </div>
    </div>
  </div>
</div>