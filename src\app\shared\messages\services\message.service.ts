import { Injectable } from '@angular/core';
import { ToastsManager } from 'ng2-toastr/ng2-toastr';

@Injectable()
export class MessageService {
  options = {
    dismiss: 'controlled',
    showCloseButton: true,
    enableHTML: true
  };

  constructor(public toaster: ToastsManager) {}

  showSuccess(message: string, title: string) {
    this.toaster.custom(
      `<div class="CustomToast CustomToast--success">
        <div class="CustomToast__IconContainer">
          <i class="icon-round-done-button"></i>
        </div>
        <div class="CustomToast__TextContainer">
          <div class="CustomToast__Title">${title}</div>
          <div class="CustomToast__Message">${message}</div>
        </div>
      </div>`,
      null,
      this.options
    );
  }

  showError(message: string, title: string) {
    this.toaster.custom(
      `<div class="CustomToast CustomToast--error">
        <div class="CustomToast__IconContainer">
          <i class="icon-error"></i>
        </div>
        <div class="CustomToast__TextContainer">
          <div class="CustomToast__Title">${title}</div>
          <div class="CustomToast__Message">${message}</div>
        </div>
      </div>`,
      null,
      this.options
    );
  }

  showWarning(message: string, title: string) {
    this.toaster.custom(
      `<div class="CustomToast CustomToast--warning">
        <div class="CustomToast__IconContainer">
          <i class="icon-warning"></i>
        </div>
        <div class="CustomToast__TextContainer">
          <div class="CustomToast__Title">${title}</div>
          <div class="CustomToast__Message">${message}</div>
        </div>
      </div>`,
      null,
      this.options
    );
  }

  showInfo(message: string, title: string) {
    this.toaster.custom(
      `<div class="CustomToast CustomToast--info">
        <div class="CustomToast__IconContainer">
          <i class="icon-info_message"></i>
        </div>
        <div class="CustomToast__TextContainer">
          <div class="CustomToast__Title">${title}</div>
          <div class="CustomToast__Message">${message}</div>
        </div>
      </div>`,
      null,
      this.options
    );
  }
}
