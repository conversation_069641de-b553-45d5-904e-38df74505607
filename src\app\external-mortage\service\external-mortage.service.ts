import { Injectable } from '@angular/core';
import { Http, Response, ResponseContentType } from '@angular/http';
import { Observable } from 'rxjs/Observable';
import { IFilter } from '../model/external-mortage.filters.models';


@Injectable()
export class ExternalMortageService {

  private urlAntergateAppraisalsList = `/UBZ-ESA-RS/service/anacredit/v1/list/filter`;
  private urlAntergateAppraisalEndpoint = `/UBZ-ESA-RS/service/anacredit/v1/position/`;

  constructor(private _http: Http) {}

  public filter(filter: IFilter): Observable<any> {
    return this._http.post(this.urlAntergateAppraisalsList, filter).map(res => res.json());
    //FIXME: set service when available
    // return this.getAntergateAppraisalList(1,10);
  }

  public getAntergateAppraisalList(
    page: number,
    numberOfResults: number
  ): Observable<any> {
    const filter = {
      ndg: null,
      requestId: null,
      isDraft: null,
      page: page,
      pageSize: numberOfResults
    };
    return this._http.post(this.urlAntergateAppraisalsList, filter).map(res => res.json());
    //FIXME: set service when available
    // return Observable.of(this.getMockedList());
  }

  public getAnacreditRequestData(
    requestId: string
  ): Observable<any> {
    const url = this.urlAntergateAppraisalEndpoint + requestId;
    return this._http.get(url).map(res => res.json());
    // return Observable.of(this.getMockedReqData(requestId));
  }


  public saveAnacreditRequestData(
    requestId: string,
    requestData: any
  ): Observable<boolean> {
    const url = this.urlAntergateAppraisalEndpoint + requestId;
    return this._http.post(url, requestData).map(res => res.json());
  }


  //FIXME: remove when services are available
  public getMockedReqData(requestId: string) {
    return {
      isDraft: true,
      collaterals: [
        {
          prog: 123,
          collatDesc: "Ipoteca di grado superiore al primo",
          antergateValue: 100000,
          assets: [
            {
              resItemId: 123,
              familyAsset: "A",
              categoryAsset: "B",
              antergateSplittedValue: 1200
            },
            {
              resItemId: 2123,
              familyAsset: "C",
              categoryAsset: "D",
              antergateSplittedValue: 2300

            }
          ]
        },
        {
          prog: 234,
          collatDesc: "Ipoteca di grado superiore al primo",
          antergateValue: 100001,
          assets: [
            {
              resItemId: 3123,
              familyAsset: "A1",
              categoryAsset: "B1",
              antergateSplittedValue: 1200
            },
            {
              resItemId: 42123,
              familyAsset: "C2",
              categoryAsset: "D2",
              antergateSplittedValue: 2300

            }
          ]
        }
      ]
    };
  }

  //FIXME: remove when services are available
  getMockedList() : any {
    return {
      list: [
        {
        ndg: '505083',
        requestId: 1233,
        antergateTotalValue: 100000000,
        creationDate: '2018-06-30',
        isDraft: 'Y'
        },
        {
          ndg: '505083',
          requestId: 12433,
          antergateTotalValue: 100000001,
          creationDate: '2018-06-11',
          isDraft: 'N'
        },
        {
          ndg: '505083',
          requestId: 12433,
          antergateTotalValue: 100000001,
          creationDate: '2018-06-11',
          isDraft: 'N'
        },
        {
          ndg: '505083',
          requestId: 12433,
          antergateTotalValue: 100000001,
          creationDate: '2018-06-11',
          isDraft: 'N'
        },
        {
          ndg: '505083',
          requestId: 12433,
          antergateTotalValue: 100000001,
          creationDate: '2018-06-11',
          isDraft: 'N'
        },
        {
          ndg: '505083',
          requestId: 12433,
          antergateTotalValue: 100000001,
          creationDate: '2018-06-11',
          isDraft: 'N'
        },
        {
          ndg: '505083',
          requestId: 12433,
          antergateTotalValue: 100000001,
          creationDate: '2018-06-11',
          isDraft: 'N'
        },
        {
          ndg: '505083',
          requestId: 12433,
          antergateTotalValue: 100000001,
          creationDate: '2018-06-11',
          isDraft: 'N'
        },
        {
          ndg: '505083',
          requestId: 12433,
          antergateTotalValue: 100000001,
          creationDate: '2018-06-11',
          isDraft: 'N'
        },
        {
          ndg: '505083',
          requestId: 12433,
          antergateTotalValue: 100000001,
          creationDate: '2018-06-11',
          isDraft: 'N'
        }
      ]
    };
  }
}


