:host>>>.collapse.in {
  background: #fcfcfc;
}

:host>>>.panel {
  border-width: 0px;
  box-shadow: unset;
}

:host>>>.panel-heading {
  border: 1px solid #ccc;
  border-radius: 3px;
}

:host>>>.panel-default > .panel-heading + .panel-collapse > .panel-body {
  border-width: 0px;
}

:host>>>.accordion-button {
  position: absolute;
  right: 15px;
  top: 5px;
}
:host>>>.panel-default > .panel-heading .panel-title a i {
  color: inherit;
}
:host>>>.empty-accordion .panel-title a {
  color: #aaa;
}
.lightgrey {
  background-color: #fcfcfc;
}

.number-ball {
  background-color: rgb(254, 184, 105);
  border: 3px solid rgb(252, 145, 0);
  border-radius: 50%;
  height: 40px;
  width: 40px;
  float: left;
}
.number-ball > .content {
  vertical-align: middle;
  text-align: center;
  color: #fff;
  font-weight: bold;
  font-size: 25px;
}

/* FIXME - TOGLIERE SE CENTRALIZZAZIONE FUNZIONA CORRETTAMENTE
.Search__NoResults {
  display: flex;
  align-items: center;
}

.Search__NoResults__Icon {
  padding: 0px 20px 0px 0px;
  color: #bbb;
  font-size: 30px;
}

.Search__NoResults__Text {
  display: flex;
  flex-direction: column;
}

.Search__NoResults__Title {
  font-size: 24px;
  color: #999;
  text-transform: none;
  padding: 0;
  margin: 0 0 5px 0;
}

.Search__NoResults__Subtitle {
  font-size: 18px;
  color: #bbb;
  margin: 0;
}

.Search__ResultsTable {
  margin-top: 20px;
}

.Search__Tab__ResultCount {
  font-family: 'unicreditbold';
  font-weight: 700;
  font-size: 12px;
  color: #535453;
}

.uc-datatabs .nav-tabs > li.active .Search__Tab__ResultCount {
  color: #388bca;
}
*/