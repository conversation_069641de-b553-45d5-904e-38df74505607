import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class ApiService {
  private _apiUrl: string;

  constructor(private http: Http) {}

  get apiUrl(): Observable<string> {
    if (!this._apiUrl) {
      return this.getApiUrl().map(url => {
        this._apiUrl = url;
        return this._apiUrl;
      });
      // this._apiUrl = environment.apiUrl;
    } else {
      return Observable.of(this._apiUrl);
    }
  }

  private getApiUrl(): Observable<string> {
    const url = '/UBZ-ESA-RS/service/property/v1/properties/getUb1EndPoint';
    // FIXME - SCOMMENTARE ISTRUZIONE SOTTO E COMMENTARE MOCK
    return this.http.get(url).map((resp: Response) => resp.text());
    // FIXME - TOGLIERE CODICE MOCK
    // return this.http.get(url).map((resp: Response) => resp.text().concat('/'));
    // return Observable.of('http://localhost:4200/');
  }
}
