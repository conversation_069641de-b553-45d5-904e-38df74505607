import {
  Component,
  OnInit,
  Input,
  OnChanges,
  SimpleChanges,
  Output,
  EventEmitter,
  ViewChild
} from '@angular/core';
import { NgForm } from '@angular/forms';
import { Observable } from 'rxjs/Observable';

import { Rilevation } from '../../model/evaluations.models';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { DomainService } from '../../../../../shared/domain/domain.service';
import { Domain } from '../../../../../shared/domain/domain';
import { MessageService } from '../../../../../shared/messages/services/message.service';

@Component({
  selector: 'app-price-evaluation-tab',
  templateUrl: './price-evaluation-tab.component.html',
  styleUrls: ['./price-evaluation-tab.component.css']
})
export class PriceEvaluationTabComponent implements OnInit, OnChanges {
  @Input() objectList: Rilevation[];
  @Input() noteRilevazione = '';
  @Input() evalObj: any;
  @Output() statusChange: EventEmitter<boolean> = new EventEmitter<boolean>();

  @ViewChild('f') priceForm: NgForm;

  consistencyTypes: Domain[] = [];
  statusTypeDomain: Domain[] = [];
  deleteModalIsOpen = false;
  modalType: string; // indica che tipo di modale aprire tra aggiunta e modifica
  modalIsOpen: boolean = false;  // boolean che gestisce visualizzazione modal aggiunta/modifica
  selectedItem: any;
  selectedIndex: number;

  public sectionIsValid = false;

  constructor(
    public _accordionAPFService: AccordionAPFService,
    private _domainService: DomainService,
    public messageService: MessageService
  ) {}

  ngOnInit() {
    Observable.forkJoin(
      this._domainService.newGetDomain('UBZ_DOM_CONSISTENCE_TYPE'),
      this._domainService.newGetDomain('UBZ_DOM_STATUS_TYPE')
    ).subscribe(res => {
      this.consistencyTypes = res[0];
      this.statusTypeDomain = res[1];
    });
    this.priceForm.statusChanges.subscribe(() => {
      this.checkSectionStatus(this.objectList);
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['objectList'].currentValue) {
      this.checkSectionStatus(changes['objectList'].currentValue);
    }
  }

  private checkSectionStatus(items: Rilevation[]): void {
    const prevValue: boolean = this.sectionIsValid;
    // Check items length
    if (items.length === 0 || !this.priceForm.valid) {
      this.sectionIsValid = false;
      this.fireStatusChangeEvent(prevValue, this.sectionIsValid);
      return;
    }
    // Omi and destination required
    for (const item of items) {
      // omiMax e omiMin vengono restituiti come number, aggiunti controlli specifici
      if (!item.destination ||
        ((item.omiMax === undefined) || (item.omiMax === null)) ||
        !item.omiMax.toString() ||
        item.omiMax.toString() === '' ||
        ((item.omiMin === undefined) || (item.omiMin === null)) ||
        !item.omiMin.toString() ||
        item.omiMin.toString() === '') {
          this.sectionIsValid = false;
          this.fireStatusChangeEvent(prevValue, this.sectionIsValid);
          return;
      }
    }
    this.sectionIsValid = true;
    this.fireStatusChangeEvent(prevValue, this.sectionIsValid);
  }

  private fireStatusChangeEvent(
    previousValue: boolean,
    currentValue: boolean
  ): void {
    if (previousValue !== currentValue) {
      this.statusChange.emit(currentValue);
    }
  }

  public getData(): Rilevation[] {
    return this.objectList;
  }

  public getEvalObj(): any {
    return this.evalObj;
  }
  
  openDeleteModal(index) {
    this.selectedIndex = index;
    this.deleteModalIsOpen = true;
  }

  // Gestisce il submit della modale di cancellazione utente
  submitDeleteModal() {
    this.objectList.splice(this.selectedIndex, 1);
    this.checkSectionStatus(this.objectList);
    this.deleteModalIsOpen = false
  }

  // Apre la modale specificandone il modalType (add / modify)
  // Index parametro opzionale, passato in caso di modale di modifica.
  openModal(modalType: string, index?: number) {
    // Se siamo in modifica, si recupera l'oggetto esatto tramite index, altrimenti si passa un nuovo oggetto per l'aggiunta
    if (index !== undefined) {
      this.selectedItem = this.objectList[index];
      this.selectedIndex = index;
    } else {
      this.selectedIndex = null;
      this.selectedItem = new Rilevation();
    }
    this.modalType = modalType;
    this.modalIsOpen = true;
  }

  // Metodo scatenato dal submit all'interno della modal per aggiunta/modifica
  submitModal(model: Rilevation) {
    switch (this.modalType) {
      case 'add':
        this.objectList.push(model);
        break;
      case 'modify':
        this.objectList[this.selectedIndex] = model;
        break;
    }
    this.modalIsOpen = false;
    this.checkSectionStatus(this.objectList);
  }
}
