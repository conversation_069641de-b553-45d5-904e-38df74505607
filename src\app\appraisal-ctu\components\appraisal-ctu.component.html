<app-position-header [positionId]="appraisalId" [wizardCode]="wizardCode" [lockingUser]="taskLockingUser">
</app-position-header>

<div class="col-sm-12 section-headline">
  <h1><i class="icon-dashboard"></i>{{'UBZ.SITE_CONTENT.101101000' | translate }}</h1>
  <h2>{{'UBZ.SITE_CONTENT.101101000' | translate }} CTU</h2>
  <section id="breadcrumbs" class="breadcrumbs">
    <div class="row">
      <div class="col-sm-12">
        <ul>
          <li><a role="button" (click)="navigateBack()">{{'UBZ.SITE_CONTENT.101101000' | translate }}</a></li>
          <li>{{'UBZ.SITE_CONTENT.101101000' | translate }} CTU</li>
        </ul>
      </div>
    </div>
  </section>
</div>

<div class="row">
  <div class="MyTabs nav-wrap nav-controls col-sm-12">
    <ul class="nav nav-tabs pull-left">
      <li role="presentation" [ngClass]="{'active': true, 'MyTabs__Tab': true}" id="first-tab">
        <a role="button" data-toggle="tab" role="tab">{{'UBZ.SITE_CONTENT.10001011010' | translate }}</a>
      </li>
    </ul>
  </div>
</div>
<div class="tab-content">
  <br />


  <div class="col-sm-12">
    <div class="box">
      <h2>{{'UBZ.SITE_CONTENT.1110000000' | translate }}</h2>
      <div class="row">
        <div class="col-sm-6">
          <label>{{'UBZ.SITE_CONTENT.10011110001' | translate }}</label>
          {{ appraisal.totValueCtu | currency:'EUR':true:'1.2-2' }}
        </div>
        <div class="col-sm-6">
          <label>{{'UBZ.SITE_CONTENT.10011110010' | translate }}</label>
          {{ appraisal.valueMarket | currency:'EUR':true:'1.2-2' }}
        </div>
        <div class="col-sm-6">
          <label>{{'UBZ.SITE_CONTENT.10011110011' | translate }}</label>
          {{ appraisal.valoreCtuExtraUci | currency:'EUR':true:'1.2-2' }}
        </div>
        <div class="col-sm-6">
          <label>{{'UBZ.SITE_CONTENT.10011110100' | translate }}</label>
          {{ appraisal.valuePrudenzial | currency:'EUR':true:'1.2-2' }}
        </div>
        <div class="col-sm-6">
          <label>{{'UBZ.SITE_CONTENT.10011110101' | translate }}</label>
          {{ appraisal.valueCtuCompany | currency:'EUR':true:'1.2-2' }}
        </div>
        <div class="col-sm-6">
          <label>{{'UBZ.SITE_CONTENT.10011110110' | translate }}</label>
          {{ appraisal.valueBook | currency:'EUR':true:'1.2-2' }}
        </div>
        <div class="col-sm-6">
          <label>{{'UBZ.SITE_CONTENT.10011110111' | translate }}</label>
          {{ appraisal.valueInsurance | currency:'EUR':true:'1.2-2' }}
        </div>
      </div>
    </div>
 </div>
  <br />
 <div class="col-sm-12">
    <div class="box">
      <h2>{{'Lotti associati' | translate }}</h2>
      <table *ngIf="appraisal.lotto && appraisal.lotto.length > 0" class="uc-table">
        <thead>
          <tr>
            <th>{{'UBZ.SITE_CONTENT.10011111000' | translate}}</th>
            <th>{{'UBZ.SITE_CONTENT.10011111001' | translate}}</th>
            <th>€ {{'UBZ.SITE_CONTENT.10011111010' | translate}} </th>
            <th>€ {{'UBZ.SITE_CONTENT.10011111011' | translate}} </th>
            <th>€ {{'UBZ.SITE_CONTENT.10011111100' | translate}} </th>
            <th>{{'UBZ.SITE_CONTENT.10011111101' | translate}}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let lotto of appraisal.lotto">
            <td>{{ lotto.progLottoEpc }}</td>
            <td>{{ lotto.progLottoWpm }}</td>
            <td>{{ lotto.totValueLotto | currency:'EUR':true:'1.2-2' }}</td>
            <td>{{ lotto.valueLottoExtraUci | currency:'EUR':true:'1.2-2' }}</td>
            <td>{{ lotto.valueLottoCompany | currency:'EUR':true:'1.2-2' }}</td>
            <td>
              <i class="icon-plus_open_lightbox cursor" (click)="showListAssetAssociated(lotto.ctuDetailAsset)"></i>
            </td>
          </tr>
        </tbody>
      </table>
      <div *ngIf="appraisal.lotto && appraisal.lotto.length === 0" class="Search__NoResults">
            <div class="Search__NoResults__Icon">
              <i class="icon-search"></i>
            </div>
            <div class="Search__NoResults__Text">
              <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1001011001' | translate }}</h3>
              <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011010' | translate }}</p>
            </div>
          </div>
    </div>
 </div>
</div>

<!-- Modale che mostra la lista degli asset associati -->
<div bsModal #assetAssociatedListModal="bs-modal" class="modal" tabindex="-1" role="dialog"
  aria-labelledby="myModalLabel" (onHidden)="assetAssociatedListModal.hide()">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <div class="col-sm-6">
          <h3 class="modal-title">{{'UBZ.SITE_CONTENT.11011' | translate}}</h3>
        </div>
        <button type="button" role="button" class="close pull-right" aria-label="Close"
          (click)="assetAssociatedListModal.hide()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <!-- Tabella File -->
        <div class="margin_top_30">
          <table *ngIf="ctuDetailAsset && ctuDetailAsset.length > 0" class="uc-table">
            <thead>
              <tr>
                <th>{{'UBZ.SITE_CONTENT.10011111110' | translate}}</th>
                <th>{{'UBZ.SITE_CONTENT.10011111111' | translate}}</th>
                <th>{{'UBZ.SITE_CONTENT.10100000000' | translate}}</th>
                <ng-template
                  [ngIf]="(assetsType && assetsType.length > 1) || (assetsType && assetsType.length === 1 && assetsType[0] === 'IMM')"
                  [ngIfElse]="MOB">
                    <th>{{'UBZ.SITE_CONTENT.101001' | translate}}</th>
                    <th>{{'UBZ.SITE_CONTENT.10101011101' | translate}}</th>
                    <th>{{'UBZ.SITE_CONTENT.101010' | translate}}</th>
                    <th>{{'UBZ.SITE_CONTENT.100110' | translate}}</th>
                  <!-- ngIf="isTavolare" -->
                  <ng-container *ngIf="!isTavolare">
                    <th>{{'UBZ.SITE_CONTENT.110001' | translate}}</th>
                    <th>{{'UBZ.SITE_CONTENT.110010' | translate}}</th>
                    <th>{{'UBZ.SITE_CONTENT.110011' | translate}}</th>
                  </ng-container>
                  <ng-container *ngIf="isTavolare">
                    <th>{{'UBZ.SITE_CONTENT.10101100001' | translate}}</th>
                    <th>{{'UBZ.SITE_CONTENT.10101100010' | translate}}</th>
                    <th>{{'UBZ.SITE_CONTENT.10101011111' | translate}}</th>
                  </ng-container>

                </ng-template>
                <ng-template #MOB>
                    <th>{{'UBZ.SITE_CONTENT.10001010011' | translate}}</th>
                    <th>{{'UBZ.SITE_CONTENT.10001010111' | translate}}</th>
                </ng-template>
                <th>€ {{'UBZ.SITE_CONTENT.10011111010' | translate}} </th>
                <th>€ {{'UBZ.SITE_CONTENT.10100000001' | translate}} </th>
                <th>{{'UBZ.SITE_CONTENT.10011111101' | translate}}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let asset of ctuDetailAsset">
                <td>{{ asset.assetIdEcp }}</td>
                <td>{{ asset.assetIdWpm }}</td>
                <td>{{ asset.assetIdPrz }}</td>
                <ng-template
                  [ngIf]="(assetsType && assetsType.length > 1) || (assetsType && assetsType.length === 1 && assetsType[0] === 'IMM')"
                  [ngIfElse]="MOB">
                    <td>{{ asset.cityPrz }}</td>
                    <td>{{ asset.tavolare ? 'Si': 'No'}}</td>
                    <td>{{ asset.zipCod }}</td>
                    <td>{{ asset.assetCategory }}</td>
                  <ng-container *ngIf="!isTavolare">
                    <td>{{ asset.sheetPrz }}</td>
                    <td>{{ asset.portionPrz }} </td>
                    <td>{{ asset.subPrz }}</td>
                  </ng-container>
                  <ng-container *ngIf="isTavolare">
                    <td>{{ asset.tableRegistryPart }}</td>
                    <td>{{ asset.tableRegistryBody }}</td>
                    <td>{{ asset.landParcel }}</td>
                  </ng-container>
                </ng-template>
                <ng-template #MOB>
                    <td *ngIf="asset.imoNum">{{ asset.imoNum }} </td>
                    <td *ngIf="asset.msnNum">{{ asset.msnNum }}</td>
                </ng-template>
                <td>{{ asset.totValuLotto | currency:'EUR':true:'1.2-2' }}</td>
                <td>{{ asset.totValueCtu | currency:'EUR':true:'1.2-2' }}</td>
                <td><i *ngIf="asset.assetIdPrz && asset.assetIdPrz !== ''" class="icon-angle-double-right cursor"
                    (click)="goToAssetPage(asset.assetIdPrz)"></i></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>