import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { UploadFileService } from './service/upload-file.service';
import { Http, Response } from '@angular/http';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { DomainService } from '../shared/domain/domain.service';
import { Domain } from '../shared/domain/domain';
import { MessageService } from '../shared/messages/services/message.service';
import { Observable } from 'rxjs/Observable';
import { PropertiesService } from '../shared/properties/properties.service';
@Component({
  selector: 'app-massive-upload',
  templateUrl: './massive-upload.component.html',
  styleUrls: ['./massive-upload.component.css'],
  providers: [UploadFileService]
})
export class MassiveUploadComponent implements OnInit {
  data: any[];
  logError: any[];
  selectedPhase = '';
  domains: Domain[] = [];
  descrizione = '';
  isSuccess: boolean = true;
  isAcquired: boolean;
  logErrorList: any;
  fileType: string;
  loadId: number;
  res: any;
  page = 1;
  listSize = 0
  pageSize = 10
  @ViewChild('fileToUpload') fileToUpload: any;
  // @ViewChild('uploadModal') modal: ModalDirective;
  modalIsOpen = false;
  fileName = '';
  sizeLimit: number;
  openLogModal = false;
  constructor(
    public uploadService: UploadFileService,
    public http: Http,
    private propertiesService: PropertiesService,
    public domainService: DomainService,
    public messageService: MessageService,
  ) { }
  ngOnInit() {
    this.refreshData();
    this.domainService
      .newGetDomain('UBZ_DOM_MASSIVE_FILE_TYPE')
      .subscribe(res => {
        this.domains = res;
      });
    this.propertiesService.getProperty('UBZ', 'max.file.size').subscribe(x => {
      this.sizeLimit = Number(x);
    });
  }
  refreshData() {
    this.uploadService.getDataList(this.page, this.pageSize).subscribe(res => {
      this.data = res.listHistoryTecLoad;
      this.listSize = res.numberOfResults;
    })
  }
  deleteData() {
    this.data = null;
    this.refreshData();
  }
  creaItemLog(item: any) {
    this.logErrorList = item.logErrorList;
    this.openLogModal = true;
  }
  getErrorList(): any[] {
    const tempArray = [];
    for (const ind in this.logErrorList) {
      if (this.logErrorList.hasOwnProperty(ind)) {
        tempArray.push({ key: ind, value: this.logErrorList[ind] });
      }
    }
    return tempArray;
  }
  setFile() {
    // this.setFileName();
    if (this.fileToUpload.nativeElement.files[0].size > this.sizeLimit) {
      this.messageService.showError(
        `LA DIMENSIONE DEL FILE NON PUO\' SUPERARE I ${this.sizeLimit /
        1048576} MEGABYTE`,
        'DIMENSIONE FILE NON CONSENTITA'
      );
      this.setFileName('');
    } else {
      this.setFileName();
    }
  }
  clickOnUploadButton() {
    this.uploadService
      .UploadDocument(
        this.fileToUpload.nativeElement.files[0],
        this.selectedPhase,
        this.descrizione
      )
      .subscribe(
        succ => {
          this.modalIsOpen = false;
          this.fileName = '';
          this.selectedPhase = '';
          this.descrizione = '';
          this.isSuccess = true;
          this.refreshData();
        },
        err => {
          this.messageService.showError('File upload failed', 'Error upload');
          this.isSuccess = false;
          this.fileName = '';
          this.selectedPhase = '';
          this.descrizione = '';
        }
      );
  }
  carica(index: number) {
    this.fileType = this.data[index].fileType;
    this.descrizione = this.data[index].description;
    this.loadId = this.data[index].progTec;
    this.uploadService
      .acquireUpload(this.fileType, this.descrizione, this.loadId)
      .subscribe(() => this.refreshData());
  }
  closeLogModal() {
    this.openLogModal = false;
  }
  closeUploadModal() {
    this.fileName = '';
    this.selectedPhase = '';
    this.descrizione = '';
    this.modalIsOpen = false;
  }
  isCurrentlyLoading(item: any) {
    return (item.acquired === 'L' && item.result === true);
  }
  showLogModal(item: any) {
    return (!item.result || item.acquired === 'E' || (item.acquired === 'W' && item.fileType === 'MASSBCE1'));
  }
  goToUploadedAppraisalPage(item: any) {
    return (item.fileType === 'MASSLOAP3' && item.result &&
      (item.acquired === 'Y' || item.acquired === 'W' || item.acquired === 'E'));
  }
  goToUploadedAssetPage(item: any) {
    return (item.fileType === 'MASSBCE1' && item.result &&
      (item.acquired === 'Y' || item.acquired === 'W' || item.acquired === 'E'));
  }
  setFileName(fileName?: string) {
    if (!fileName && fileName !== '') {
      fileName = this.fileToUpload.nativeElement.files[0].name;
    }
    this.fileName = fileName;
  }
  changePage(event: any) {
    this.page = event.page;
    this.refreshData();
  }
 
  changePageSize() {
    this.page = 1;
    this.refreshData();
  }
 
}
