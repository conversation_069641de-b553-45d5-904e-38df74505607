<ng-container *appAuthKey="'UBZ_NOTES_ADD'">
  <button type="button" class="btn btn-empty pull-right" data-toggle="modal" data-target="#nota" (click)="addNoteModal.show()">
    <i class="icon-plus"></i> {{'UBZ.SITE_CONTENT.10000010' | translate }}</button>
</ng-container>
<h4 class="section-heading">{{'UBZ.SITE_CONTENT.10000011' | translate }}</h4>

<div *ngIf="notesHistory.length === 0" class="Search__NoResults">
  <div class="Search__NoResults__Icon">
    <i class="icon-placeholder_note"></i>
  </div>
  <div class="Search__NoResults__Text">
    <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1001011001' | translate }}</h3>
    <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011010' | translate }}</p>
  </div>
</div>

<accordion class="panel-group" id="accordion">
  <ng-container *ngFor="let note of notesHistory">
    <accordion-group #group class="panel">
      <div accordion-heading class="acc-note-headline" role="tab">
        <h4 class="panel-title">
          <a role="button">
            <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i> {{ note.noteTitle }}
            <span class="acc-note-state">- {{ noteTypeDomain[note.noteType] ? (noteTypeDomain[note.noteType].translationCod | translate) : noteTypeDomain[note.noteType]
              }}</span>
          </a>
        </h4>
      </div>
      <div class="panel-collapse collapse in" role="tabpanel">
        <div class="panel-body">
          <div class="panel-box">
            <div class="row form-group">
              <div class="col-sm-4">
                <label>{{'UB1.VOUCHERSTATUSLOG.User' | translate}}</label>
                {{ note.userId }}
              </div>
              <div class="col-sm-4">
                <label>{{'UB3.DATA_INSERIMENTO' | translate}}</label>
                {{ note.creationDate | date: 'dd/MM/y' }} {{ note.creationDate | customTime }}
              </div>
              <div class="col-sm-4" *ngIf="note.reasonCode">
                <label>{{'UBZ.SITE_CONTENT.11111110' | translate}}</label>
                {{ (reasonTypeDomain && reasonTypeDomain[note.reasonCode]) ? (reasonTypeDomain[note.reasonCode].translationCod | translate) : reasonTypeDomain[note.reasonCode] }}
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12">
                <label>{{'UBZ.SITE_CONTENT.11001011' | translate}}</label>
                {{ note.noteDesc }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </accordion-group>
  </ng-container>
</accordion>

<div class="modal fade" bsModal #addNoteModal="bs-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <form #f="ngForm" (ngSubmit)="saveNote()" novalidate>
        <div class="modal-header">
          <h2>{{'UBZ.SITE_CONTENT.10000010' | translate }}</h2>
          <button type="reset" class="close pull-right" aria-label="Close" (click)="addNoteModal.hide()">
            <i class="icon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-sm-12">
              <p>{{'UBZ.SITE_CONTENT.10000110' | translate }}.</p>
            </div>
            <div class="col-sm-6 form-group">
              <label>{{'UBZ.SITE_CONTENT.10000111' | translate }}*</label>
              <div class="custom-select">
                <select class="form-control" name="macroprocess" [(ngModel)]="newNote.noteType" name="noteType" #noteType="ngModel" required
                  [ngClass]="(f.submitted && !noteType.valid) ? 'error' : 'valid'">
                  <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let row of (noteTypeDomain | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod ? (row.translationCod | translate) : row.domCode}}</option>
                </select>
              </div>
              <div *ngIf="f.submitted && !noteType.valid" class="tooltip fade bottom in" role="tooltip" style="top: 67px; left: 127px;">
                <div class="tooltip-arrow" style="left: 50%;"></div>
                <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
              </div>
            </div>
            <div class="col-sm-6 form-group">
              <label>{{'UBZ.SITE_CONTENT.10001000' | translate }}*</label>
              <input type="text" class="form-control required" data-placement="bottom" [(ngModel)]="newNote.noteTitle" name="noteTitle"
                #noteTitle="ngModel" required [ngClass]="(f.submitted && !noteTitle.valid) ? 'error' : 'valid'" placeholder="{{'UBZ.SITE_CONTENT.10001010' | translate }}"
                maxlength="50" />
              <div *ngIf="f.submitted && !noteTitle.valid" class="tooltip fade bottom in" role="tooltip" style="top: 67px; left: 127px;">
                <div class="tooltip-arrow" style="left: 50%;"></div>
                <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12 form-group">
              <label>{{'UBZ.SITE_CONTENT.10001001' | translate }}*</label>
              <textarea rows="4" class="form-control required" data-placement="bottom" [(ngModel)]="newNote.noteDesc" name="noteDesc" #noteDesc="ngModel"
                required [ngClass]="(f.submitted && !noteDesc.valid) ? 'error' : 'valid'" placeholder="{{'UBZ.SITE_CONTENT.10001011' | translate }}..."
                required></textarea>
              <div *ngIf="f.submitted && !noteDesc.valid" class="tooltip fade bottom in" role="tooltip" style="top: 67px; left: 127px;">
                <div class="tooltip-arrow" style="left: 50%;"></div>
                <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-primary waves-effect">{{'UBZ.SITE_CONTENT.10000010' | translate }}</button>
        </div>
      </form>
    </div>
  </div>
</div>
