<form #f="ngForm">
  <accordion-group #group class="panel" [ngClass]="{'empty-accordion': !anInsuranceExists}" [isOpen]="modifyMod" [isDisabled]="!anInsuranceExists">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          {{'UBZ.SITE_CONTENT.1100010001' | translate }}
          <div class="accordion-button">
            <ng-container *appAuthKey="'UBZ_REGISTRY.INSURANCE_POLICY_OPEN_NEW'">
              <button *ngIf="!modifyMod && !anInsuranceExists" type="button" class="btn btn-empty" (click)="startModify($event)">
                <i class="fa icon-add"></i>{{'UBZ.SITE_CONTENT.111100001' | translate }}
              </button>
            </ng-container>
            <ng-container *appAuthKey="'UBZ_REGISTRY.INSURANCE_POLICY_MODIFY'">
              <button *ngIf="!modifyMod && anInsuranceExists" type="button" class="btn btn-empty" (click)="startModify($event)">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.11111' | translate }}
              </button>
            </ng-container>
            <ng-container *ngIf="modifyMod">
              <button *appAuthKey="'UBZ_REGISTRY.INSURANCE_POLICY_CANCEL'" type="button" class="btn btn-empty" (click)="abortAdding($event)">
                <i class="fa fa-times"></i>{{'UBZ.SITE_CONTENT.100000' | translate }}
              </button>
              <button *appAuthKey="'UBZ_REGISTRY.INSURANCE_POLICY_SAVE'" type="button" class="btn btn-empty" (click)="saveAddedData($event)"
                [disabled]="f.invalid">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.100001' | translate }}
              </button>
            </ng-container>
          </div>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <form #f="ngForm" novalidate>
            <ng-container *ngIf="modifyMod; else only_visualization">
              <div class="row" *ngFor="let insurance of insuranceList; let index = index">
                <div class="col-sm-12 col-md-4 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1100010010' | translate }}</label>
                  <input type="text" name="insurance-{{index}}" class="form-control" [(ngModel)]="insurance.policyName">
                </div>
                <div class="col-sm-12 col-md-4 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1100010011' | translate }}</label>
                  <input type="text" name="policyMaxPrice-{{index}}" class="form-control" appOnlyNumbers [(ngModel)]="insurance.policyMaxPrice">
                </div>
                <div class="col-sm-12 col-md-4 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1100010100' | translate }}</label>
                  <app-calendario [name]="'endPolicy-' + index" [(ngModel)]="insurance.endPolicy" [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                    [required]="true">
                  </app-calendario>
                </div>
              </div>
              <div class="row">
                <div class="col-sm-12 btn-set">
                  <button *appAuthKey="'UBZ_REGISTRY.INSURANCE_POLICY_ADD_NEW'" class="btn btn-primary waves-effect pull-right" [disabled]="f.invalid"
                    type="button" (click)="createNewInsurance()">{{'UBZ.SITE_CONTENT.1100010101' | translate }}</button>
                </div>
              </div>
            </ng-container>
          </form>
        </div>
      </div>
    </div>
  </accordion-group>
</form>

<ng-template #only_visualization>
  <div class="row" *ngFor="let insurance of insuranceList; let index = index">
    <div class="col-sm-12 col-md-4 form-group">
      <label>{{'UBZ.SITE_CONTENT.1100010010' | translate }}</label>
      {{ insurance.policyName }}
    </div>
    <div class="col-sm-12 col-md-4 form-group">
      <label>{{'UBZ.SITE_CONTENT.1100010011' | translate }}</label>
      {{ insurance.policyMaxPrice | number:'1.2-2' }}
    </div>
    <div class="col-sm-12 col-md-4 form-group">
      <label>{{'UBZ.SITE_CONTENT.1100010100' | translate }}</label>
      {{ insurance.endPolicy | date:'dd-MM-yyyy' }}
    </div>
  </div>
</ng-template>
