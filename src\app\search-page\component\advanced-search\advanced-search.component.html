<div class="row" *ngIf="searchPageService && searchPageService.advancedSearch">
  <div class="col-sm-12 form-group section-headline">
    <h1>{{'UBZ.SITE_CONTENT.10111111' | translate }} per</h1>
    <div class="row">
      <ng-container *ngIf="filters">
        <span *ngFor="let filter of filters" class="badge search-tag">{{filter}}</span>
      </ng-container>
      <div class="pull-right"
        *ngIf="searchPageService && searchPageService.advancedSearch && (searchPageService.advancedSearch['count'] > 0)">
        <button class="btn btn-empty" type="button" (click)="exctractReport()" data-toggle="tooltip"
          title="ESTRAI REPORT">
          <i class="icon-xls_export" aria-hidden="true"></i>
          {{'UBZ.SITE_CONTENT.1100101110' | translate }}
        </button>
      </div>
    </div>
    <div class="uc-datatabs">

      <!-- Tab panes -->
      <div class="tab-content">
        <div *ngIf="searchType === 'RI'" role="tabpanel" class="tab-pane active" id="richiesta">
          <div *ngIf="searchPageService.advancedSearch['count'] === 0" class="Search__NoResults">
            <div class="Search__NoResults__Icon">
              <i class="icon-search" aria-hidden="true"></i>
            </div>
            <div class="Search__NoResults__Text">
              <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1001011001' | translate }}</h3>
              <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011010' | translate }}</p>
            </div>
          </div>

          <ng-container *ngIf="searchPageService.advancedSearch['count'] > 0">
            <div class="table-responsive">
            <table class="Search__ResultsTable table table-hover">
              <thead>
                <tr>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10100101' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.1001110' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.1101101' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10000001' | translate }}</th>
                    <th scope="col">{{'UBZ.SITE_CONTENT.10011000100' | translate }}</th>
                    <th scope="col">{{'UBZ.SITE_CONTENT.10011000101' | translate }}</th>
                    <th scope="col">{{'UBZ.SITE_CONTENT.1110001' | translate }}</th>
                    <th scope="col">{{'UBZ.SITE_CONTENT.11110011' | translate }}</th>
                  <th scope="col"></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let row of searchPageService.advancedSearch['searchResults']">
                  <td data-label="A">{{row.id}}</td>
                  <td data-label="B">{{row.ndg}}</td>
                  <td data-label="C">{{row.heading}}</td>
                    <td data-label="E">{{(statusPhase && statusPhase[row.phase + row.status]) ? (statusPhase[row.phase + row.status].translationStatusCod
                    | translate) : ('' | translate)}}</td>
                  <td data-label="G">{{(macroProcessDomain && macroProcessDomain[row.macroprocess]) ? (macroProcessDomain[row.macroprocess].translationCod
                    | translate) : ''}}</td>
                  <td data-label="H" *ngIf="appraisalTypeDomain && appraisalTypeDomain[row.appraisalType]">
                    {{appraisalTypeDomain[row.appraisalType].translationCod | translate}}
                  </td>
                  <td data-label="H" *ngIf="appraisalTypeDomain && !appraisalTypeDomain[row.appraisalType]">
                    {{row.appraisalType}}
                  </td>
                  <td data-label="I">{{(scopeTypeDomain && scopeTypeDomain[row.finOperation]) ? (scopeTypeDomain[row.finOperation].translationCod
                    | translate) : ''}}</td>
                  <td data-label="L">
                      <button class="btn btn-clean waves-effect waves-secondary" type="button"
                        (click)="openCollateralModal(row)">
                        <i class="icon-plus_open_lightbox" aria-hidden="true"></i>
                    </button>
                      <span class="align-middle">{{getCollateralIds(row)}}</span>
                  </td>
                  <ng-container *appAuthKey="'UBZ_ADVANCED.SEARCH_GO.REQUEST'">
                      <td data-label="">
                        <a role="button" (click)="goToAppraisalRequest(row.id) ; $event.stopPropagation()">
                          <i class="icon-angle-double-right" aria-hidden="true"></i>
                        </a>
                      </td>
                  </ng-container>
                </tr>
              </tbody>
            </table>
            </div>
          </ng-container>
        </div>

        <div *ngIf="searchType === 'PE'" role="tabpanel" class="tab-pane active" id="perizie">
          <div *ngIf="searchPageService.advancedSearch['count'] === 0" class="Search__NoResults">
            <div class="Search__NoResults__Icon">
              <i class="icon-search" aria-hidden="true"></i>
            </div>
            <div class="Search__NoResults__Text">
              <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1001011001' | translate }}</h3>
              <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011010' | translate }}</p>
            </div>
          </div>

          <ng-container *ngIf="searchPageService.advancedSearch['count'] > 0">
            <div class="table-responsive">
            <table class="Search__ResultsTable table table-hover">
              <thead>
                <tr>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10010001' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10100101' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.1001110' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.1101101' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10000001' | translate }}</th>
                    <th scope="col">{{'UBZ.SITE_CONTENT.10011001110' | translate }}</th>
                    <th scope="col">{{'UBZ.SITE_CONTENT.10011000100' | translate }}</th>
                    <th scope="col">{{'UBZ.SITE_CONTENT.1110010' | translate }}</th>
                    <th scope="col">{{'UBZ.SITE_CONTENT.1110001' | translate }}</th>
                    <th scope="col">{{ 'UBZ.SITE_CONTENT.11110001001' | translate }}</th>
                    <th scope="col">{{'UBZ.SITE_CONTENT.11110011' | translate }}</th>
                  <th scope="col"></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let row of searchPageService.advancedSearch['searchResults']">
                  <td data-label="A">{{row.appraisalId}}</td>
                  <td data-label="A">{{row.id}}</td>
                  <td data-label="B">{{row.ndg}}</td>
                  <td data-label="C">{{row.heading}}</td>
                  <td data-label="E">{{ (statusPhase && statusPhase[row.phaseApp + row.statusApp]) ? (statusPhase[row.phaseApp
                    + row.statusApp].translationStatusCod | translate) : ''}}</td>
                  <td data-label="F">{{row.insertDateApp ? ((row.insertDateApp | date: 'dd-MM-y') + ' ' + (row.insertDateApp
                    | customTime)) : ''}}</td>
                  <td data-label="G">{{(macroProcessDomain && macroProcessDomain[row.macroprocess]) ? (macroProcessDomain[row.macroprocess].translationCod
                    | translate) : ''}}</td>
                  <td data-label="H" *ngIf="appraisalTypeDomain && appraisalTypeDomain[row.appraisalType]">
                    {{appraisalTypeDomain[row.appraisalType].translationCod | translate}}
                  </td>
                  <td data-label="H" *ngIf="appraisalTypeDomain && !appraisalTypeDomain[row.appraisalType]">
                    {{row.appraisalType}}
                  </td>
                  <td data-label="I">{{(scopeTypeDomain && scopeTypeDomain[row.finOperation]) ? (scopeTypeDomain[row.finOperation].translationCod
                    | translate) : ''}}</td>
                    <td data-label="M">{{row.totalAssets}} </td>
                  <td data-label="L">
                      <button class="btn btn-clean waves-effect waves-secondary" type="button"
                        (click)="openCollateralModal(row)">
                        <i class="icon-plus_open_lightbox" aria-hidden="true"></i>
                    </button>
                      <span class="align-middle">{{getCollateralIds(row)}}</span>
                  </td>
                  <ng-container *appAuthKey="'UBZ_ADVANCED.SEARCH_GO.DETAILS'">
                      <td data-label="">
                        <a role="button" (click)="goToDetailsPage(row.appraisalId) ; $event.stopPropagation()">
                          <i class="icon-angle-double-right" aria-hidden="true"></i>
                        </a>
                      </td>
                  </ng-container>
                </tr>
              </tbody>
            </table>
            </div>
          </ng-container>
        </div>

        <div *ngIf="searchType === 'ASAS'" role="tabpanel" class="tab-pane active" id="asset">
          <div *ngIf="searchPageService.advancedSearch['count'] === 0" class="Search__NoResults">
            <div class="Search__NoResults__Icon">
              <i class="icon-search" aria-hidden="true"></i>
            </div>
            <div class="Search__NoResults__Text">
              <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1001011001' | translate }}</h3>
              <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011010' | translate }}</p>
            </div>
          </div>

          <ng-container *ngIf="searchPageService.advancedSearch['count'] > 0">
            <div class="table-responsive">
            <table class="Search__ResultsTable table table-hover">
              <thead>
                <tr>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10100101' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10010001' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.100100' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.100101' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10010011' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.101011' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10010110' | translate }}</th>
                  <th scope="col"></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let row of searchPageService.advancedSearch['searchResults']">
                  <td data-label="A">{{row.requestId}}</td>
                  <td data-label="A">{{row.appraisalId}}</td>
                  <td data-label="A">{{row.idAsset}}</td>
                  <td data-label="B">{{(resItemTypeDomain && resItemTypeDomain[row.familyAsset]) ? (resItemTypeDomain[row.familyAsset].translationCod
                    | translate) : ('') }}</td>
                  <td data-label="C">{{(categoryTypeDomain && categoryTypeDomain[row.categoryAsset]) ? (categoryTypeDomain[row.categoryAsset].translationCod
                    | translate) : ('') }}</td>
                  <td data-label="A">{{row.address}}</td>
                  <td data-label="D">{{row.itemDesc}}</td>
                  <ng-container *appAuthKey="'UBZ_ADVANCED.SEARCH_GO.ASSET'">
                      <td data-label="">
                        <a (click)="goToAssetPage(row.idAsset) ; $event.stopPropagation()">
                          <i class="icon-angle-double-right" aria-hidden="true"></i>
                        </a>
                      </td>
                  </ng-container>
                </tr>
              </tbody>
            </table>
            </div>
          </ng-container>
        </div>

        <div *ngIf="searchType === 'AS'" role="tabpanel" class="tab-pane active" id="asset">
          <div *ngIf="searchPageService.advancedSearch['count'] === 0" class="Search__NoResults">
            <div class="Search__NoResults__Icon">
              <i class="icon-search" aria-hidden="true"></i>
            </div>
            <div class="Search__NoResults__Text">
              <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1001011001' | translate }}</h3>
              <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011010' | translate }}</p>
            </div>
          </div>

          <ng-container *ngIf="searchPageService.advancedSearch['count'] > 0">
            <div class="table-responsive">
            <table class="Search__ResultsTable table table-hover">
              <thead>
                <tr>
                  <th scope="col">{{'UBZ.SITE_CONTENT.100100' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.100101' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10010011' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.101011' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10010110' | translate }}</th>
                  <th scope="col"></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let row of searchPageService.advancedSearch['searchResults']">
                  <td data-label="A">{{row.resItemId}}</td>
                  <td data-label="B">{{ row.familyAsset }}</td>
                  <td data-label="C">{{(categoryTypeDomain && categoryTypeDomain[row.categoryAsset]) ? (categoryTypeDomain[row.categoryAsset].translationCod
                    | translate) : ('') }}</td>
                  <td data-label="A">{{row.address}}</td>
                  <td data-label="A">{{row.description}}</td>
                  <ng-container *appAuthKey="'UBZ_ADVANCED.SEARCH_GO.ASSET'">
                      <td data-label="">
                        <a (click)="goToAssetPage(row.resItemId) ; $event.stopPropagation()">
                          <i class="icon-angle-double-right" aria-hidden="true"></i>
                        </a>
                      </td>
                  </ng-container>
                </tr>
              </tbody>
            </table>
            </div>
          </ng-container>
        </div>

        <div *ngIf="searchType === 'GA'" role="tabpanel" class="tab-pane active" id="garanzie">
          <div *ngIf="searchPageService.advancedSearch['count'] === 0" class="Search__NoResults">
            <div class="Search__NoResults__Icon">
              <i class="icon-search" aria-hidden="true"></i>
            </div>
            <div class="Search__NoResults__Text">
              <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1001011001' | translate }}</h3>
              <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011010' | translate }}</p>
            </div>
          </div>

          <ng-container *ngIf="searchPageService.advancedSearch['count'] > 0">
            <div class="table-responsive">
            <table class="Search__ResultsTable table table-hover">
              <thead>
                <tr>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10010001' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10100101' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.100100' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10010100' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10010101' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10010110' | translate }}</th>
                  <th scope="col"></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let row of searchPageService.advancedSearch['searchResults']">
                  <td data-label="A">{{row.appraisalId}}</td>
                  <td data-label="B">{{row.positionId}}</td>
                  <td data-label="B">{{row.resItemId}}</td>
                  <td data-label="C">{{row.collateralId}}</td>
                  <td data-label="D">{{row.technicalForm}}</td>
                  <td data-label="E">{{row.desc}}</td>
                  <ng-container *appAuthKey="'UBZ_ADVANCED.SEARCH_GO.WARRENT'">
                      <td data-label="">
                        <a (click)="goToWarrentPage(row.resItemId) ; $event.stopPropagation()">
                          <i class="icon-angle-double-right" aria-hidden="true"></i>
                        </a>
                      </td>
                  </ng-container>
                </tr>
              </tbody>
            </table>
            </div>
          </ng-container>
        </div>

      </div>

    </div>
  </div>
</div>

<div class="row"
  *ngIf="searchPageService && searchPageService.advancedSearch && searchPageService.advancedSearch['count'] && (searchPageService.advancedSearch['count'] > 10)">
  <div class="col-sm-6">
    <div class="results">
      <span>{{'UBZ.SITE_CONTENT.1111111' | translate }}</span>
      <div class="custom-select">
        <select class="form-control" [(ngModel)]="pageSize" (change)="changePageSize()">
          <option value="10" selected>10 {{'UBZ.SITE_CONTENT.10000000' | translate }}
            {{searchPageService.advancedSearch['count']}}</option>
          <option *ngIf="searchPageService.advancedSearch['count'] > 20" value="20">20 {{'UBZ.SITE_CONTENT.10000000' | translate
            }} {{searchPageService.advancedSearch['count']}}</option>
          <option *ngIf="searchPageService.advancedSearch['count'] > 30" value="30">30 {{'UBZ.SITE_CONTENT.10000000' | translate
            }} {{searchPageService.advancedSearch['count']}}</option>
          <option *ngIf="searchPageService.advancedSearch['count'] > 40" value="40">40 {{'UBZ.SITE_CONTENT.10000000' | translate
            }} {{searchPageService.advancedSearch['count']}}</option>
          <option *ngIf="searchPageService.advancedSearch['count'] > 50" value="50">50 {{'UBZ.SITE_CONTENT.10000000' | translate
            }} {{searchPageService.advancedSearch['count']}}</option>
          <option *ngIf="searchPageService.advancedSearch['count'] <= 50"
            [ngValue]="searchPageService.advancedSearch['count']"> {{searchPageService.advancedSearch['count']}}
            {{'UBZ.SITE_CONTENT.10000000' | translate }} {{searchPageService.advancedSearch['count']}}</option>
        </select>
      </div>
    </div>
  </div>
  <div class="col-sm-6" class="pull-right">
    <pagination [boundaryLinks]="true" [directionLinks]="false" [totalItems]="searchPageService.advancedSearch['count']"
      [ngModel]="pageNum" [itemsPerPage]="pageSize" [maxSize]="10" (pageChanged)="changePage($event)" class="pagination"
      previousText="&lsaquo;" nextText="&rsaquo;" firstText="&laquo;" lastText="&raquo;"></pagination>
  </div>
</div>


<app-collateral-modal *ngIf="collateralModalIsOpen" [isOpen]="collateralModalIsOpen" [collaterals]="collateralsToView"
  [searchType]="searchType" (modalClose)="closeCollateralModal()"></app-collateral-modal>