class ConfigurationRule {
  ruleId: number;
  macroProcess = '';
  activeFlag = false;
  bornDate: Date;
  deadDate: Date;
}

export class SampleChecksConfigurationRule extends ConfigurationRule {
  debtOperator: string;
  debtValue: number;
  salFlag: boolean;
  ruleDesc: string;
  minValue: number;
  maxValue: number;
  checkType: string;
  ruleOutcome: boolean;
}

export class ExpertAssignmentConfigurationRule extends ConfigurationRule {
  posSegment = '';
  locationArea: string;
  appraisalType = '';
  resItemType = '';
  resItemCategory = '';
  credlineAmount: number;
  appraisalOwner = '';
  externalAppraisalFlag = false;
  expertType = '';
  expertSocAnagId = '';
  priority: number;
}

export class AppraisalConfigurationRule extends ConfigurationRule {
  appraisalStruct = '';
  posSegment = '';
  goodType = '';
  appraisalScope = '';
  externalAppraisalFlag: false;
  autoOrigination: string;
  appraisalType = '';
  appraisalOwner = '';
  priority: number;
}

export class SocietyConf {
  //for activeList and inactiveList
  anagId: number;
  priority: number;
  distributionPercentage: number;
  heading: string;
  activationEndDate: Date;
  activationStartDate: Date;
  email: string;
  iban: string;
  mobileNum: string;
  ndg: string;
  phoneNum: string;
  vatNum: string;
}

export class IndividualAssignmentConfig {
  //for AssignmentConfInfo
  sessionId: number;
  user: string;
  saved: boolean;
  processed: boolean;
  insertDate: Date;
  startDate: Date;
  activeDate: Date;
  lastSavedTs: Date;
}
