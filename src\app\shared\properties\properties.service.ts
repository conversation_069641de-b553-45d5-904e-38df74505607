import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class PropertiesService {
  constructor(private http: Http) { }

  getProperty(processCod: string, propertyCod: string): Observable<string> {
    processCod = encodeURIComponent(processCod);
    propertyCod = encodeURIComponent(propertyCod);
    return this.http
      .get(
        `/UBZ-ESA-RS/service/umfService/properties/${processCod}/${propertyCod}`,
        { withCredentials: true }
      )
      .map((resp: Response) => resp.text());
  }

  getInternalProperty(
    propertyCod: string,
    positionId?: string
  ): Observable<string> {
    if (!positionId) {
      positionId = '-';
    }
    propertyCod = encodeURIComponent(propertyCod);
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/property/v1/properties/${positionId}/${propertyCod}`;
    return this.http.get(url).map((resp: Response) => resp.text());
  }
}
