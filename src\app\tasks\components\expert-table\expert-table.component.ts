import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  Inject,
} from "@angular/core";
import { ActivatedRoute, Router, Params } from "@angular/router";
import { TranslateService } from "@ngx-translate/core";
import { Observable } from "rxjs/Observable";
import { Domain } from "../../../shared/domain/domain";
import { DomainService } from "../../../shared/domain/domain.service";
import { Expert } from "../../model/expert";
import { GenericTaskService } from "../../services/generic-task/generic-task.service";
import { SearchResultsData } from "../../../shared/search/search-results-data";
import { AccessRightsService } from "../../../shared/access-rights/services/access-rights.service";
import { LockUnlockTask } from "../../../shared/access-rights/model/lock-unlock-task";
import { Parameter } from "../../../shared/access-rights/model/parameter";
import { MessageService } from "../../../shared/messages/services/message.service";
import { PositionService } from "../../../shared/position/position.service";
// Models
import { APP_CONSTANTS, IAppConstants } from "../../../app.constants";
import { UserDataService } from "../../../shared/user-data/user-data.service";
import { UserData } from "../../../shared/user-data/user-data";

@Component({
  selector: "app-expert-table",
  templateUrl: "./expert-table.component.html",
  styleUrls: ["./expert-table.component.css"],
})
export class ExpertTableComponent implements OnInit {
  @Input() positionId: string;
  @Input() isSecondOpinion: boolean;
  @Input() isOperCTEAppraisalRequest: boolean = false;
  @Input() idExpert: number;
  @Output() isCompiled: EventEmitter<any> = new EventEmitter();
  @Output() isNotCompiled: EventEmitter<any> = new EventEmitter();
  isPreliosSelected: boolean = false;
  taskId: string;
  taskCod: string;
  _chosenExpert: Expert;
  _expertType: string;
  _expertTypes: Domain[] = [];
  seeAll = false;
  private selectionMade = false;
  searchResults: SearchResultsData = new SearchResultsData();
  _pageSize = 10;
  _pageNumber = 1;
  isTaskLocked = false;
  surveyTypeDomain: any;
  surveyType: string;
  private prevSelectedData: any;
  isTechnical: boolean;
  // Array filtrato di valori da mostrare in tabella esperti
  expertListForPrinting: any[] = [];
  isSecondOpinionIndividual = false;
  lockExpertTypePer = false;
  invoiceFlag: any;
  opinionType: any;
  constructor(
    private _activatedRoute: ActivatedRoute,
    private _router: Router,
    private _expertAssignmentService: GenericTaskService,
    private _accessRightsService: AccessRightsService,
    private _userDataService: UserDataService,
    private _messageService: MessageService,
    private _translateService: TranslateService,
    private _positionService: PositionService,
    @Inject(APP_CONSTANTS) public constants: IAppConstants,
    private _domainService: DomainService
  ) {}

  ngOnInit() {
 if (this.isOperCTEAppraisalRequest) {
      // Siamo in wizard di richiesta perizia
      this.settingForAppraisalRequest();
    } else {
      // Siamo in wizard di compilazione perizia
      this.settingForAppraisalCompilation();
    }
  }

  /**
   * @function
   * @name settingForAppraisalRequest
   * @description Imposta le variabili ed esegue le chiamate necessarie per l'utlizzo del componente
   * all'interno del wizard di rchiesta
   */
  settingForAppraisalRequest() {
    this.taskId = "0";
    this.taskCod = "UBZ_MANUALASSIGNMENT";
    this.isTechnical = true;
    // Recupera elenco tipologia esperti
    this._expertAssignmentService.getExpertTypes().subscribe((res) => {
      this._expertTypes = res;
      this.selectionMade = true;
      this._domainService
        .newGetDomain("UBZ_DOM_SURVEY_TYPE")
        .subscribe((surveyTypeDomain) => {
          this.surveyTypeDomain = surveyTypeDomain;
        });
      // Se idExpert è stato passato in input bisogna recupera il
      // dettaglio dei periti per prevalorizzare la scelta a video
      if (this.idExpert) {
        this.retrieveSelectedExpert(0);
      }
    });
  }
  /**
   * @function
   * @name retrieveSelectedExpert
   * @description Invocato per recuperare il dettaglio dell'esperto selezionato in precedenza e mostrarlo a video
   * @param {number} indexToSelect - Indice dell'oggetto _expertTypes per il quale recuperare la lista degli esperti
   */
  retrieveSelectedExpert(indexToSelect) {
    let index = 0;
    for (const domCode in this._expertTypes) {
      if (index === indexToSelect) {
        // Recupero la lista degli esperti di tipo domCode
        this._expertAssignmentService
          .getExpertList(
            this.positionId,
            domCode,
            this._pageNumber,
            this._pageSize
          )
          .subscribe((expertList) => {
            if (
              expertList &&
              expertList.listExpSocPer &&
              expertList.listExpSocPer.length
            ) {
              // Se è presente l'elenco degli esperti lo scorro per trovare quello selezionato in precedenza
              // e per settare le variabili ad esso associate
              for (const expert of expertList.listExpSocPer) {
                if (expert.idExpert === this.idExpert) {
                  this.searchResults.count = expertList.sizeExpSocPer;
                  this.searchResults.positions = expertList.listExpSocPer;
                  this._expertType = domCode;
                  this._chosenExpert = expert;
                  this.getExpertListForPrinting();
                  this.checkIsValid();
                  return;
                }
              }
            }
            this.retrieveSelectedExpert(indexToSelect + 1);
          });
      }
      index++;
    }
  }
  /**
   * @function
   * @name settingForAppraisalCompilation
   * @description Imposta le variabili ed esegue le chiamate necessarie per l'utlizzo del componente
   * all'interno del wizard di compilazione
   */
  settingForAppraisalCompilation() {
    this._activatedRoute.params.subscribe((params: Params) => {
      this.taskId = params["taskId"];
      this.taskCod = params["taskCod"];
    });
    this._expertAssignmentService
      .getExpertTypes()
      .switchMap((res) => {
        this._expertTypes = res;
        return this._positionService.getAppraisalInfo(this.positionId);
      })
      .switchMap((res) => {
        this.opinionType=res.opinionType
       this.invoiceFlag = res.invoiceUcscFlag;
        this.checkInvoiceFlagWarning();

        if (res.appraisal.macroProcess === "CTE") {
          this.isTechnical = true;
          this._expertType = "PER";
        } else {
          this._expertType = "SOC";
        }
        if (
          res.appraisal.appraisalType &&
          (res.appraisal.appraisalType ===
            this.constants.appraisalTypes.FINE_LAVORI ||
            res.appraisal.appraisalType ===
              this.constants.appraisalTypes.SAL) &&
          this.isSecondOpinion === true &&
          res.appraisal.resItemType === "IMM"
        ) {
          this._expertType = "PER";
          this.lockExpertTypePer = true;
        }
        if (
          this.isSecondOpinion === true &&
          res.appraisal.posSegment === "IND"
        ) {
          this._expertType = "PER";
          this.lockExpertTypePer = true;
          this.surveyType = "NES";
          this.isSecondOpinionIndividual = true;
        }
        this.selectionMade = true;
        return Observable.forkJoin(
          this._expertAssignmentService.getExpertList(
            this.positionId,
            this._expertType,
            this._pageNumber,
            this._pageSize
          ),
          this._positionService.getAppraisalAssignment(this.positionId),
          this._domainService.newGetDomain("UBZ_DOM_SURVEY_TYPE")
        );
      })
      .subscribe((res) => {
        this.searchResults.count = res[0].sizeExpSocPer;
        this.searchResults.positions = res[0].listExpSocPer;
        this.getExpertListForPrinting();
        this.prevSelectedData = res[1];
        if (
          this.searchResults.positions &&
          this.searchResults.positions.length > 0 &&
          !this.isTechnical
        ) {
          this._chosenExpert = this.searchResults.positions[0];
        }
        this.surveyTypeDomain = res[2];
      });
  }

  isExpertListLocked(item: any) {
    return (
      (this.isPreliosSelected === true && item.domCode !== "SOC") ||
      (this.lockExpertTypePer && item.domCode !== "PER")
    );
  }

  /**
   * @function
   * @name getExpertList
   * @description Recupera la lista degli esperti e il codice Prelios a seconda dell'ambiente nel quale
   * ci troviamo, seleziona l'esperto scelto
   * @param {string} expertType
   */
  private getExpertList(expertType: string) {
    Observable.forkJoin(
      this._expertAssignmentService.getExpertList(
        this.positionId,
        expertType,
        this._pageNumber,
        this._pageSize
      ),
      this._expertAssignmentService.getThirdNetworkProperty()
    ).subscribe((forkResponse) => {
      // forkResponse[1] contiene il codice di Prelios
      if (forkResponse[0] && forkResponse[1]) {
        this.searchResults.count = forkResponse[0].sizeExpSocPer;
        this.searchResults.positions = forkResponse[0].listExpSocPer;
        for (const expert of this.searchResults.positions) {
          const idProp = expert.idExpert ? "idExpert" : "idSocPer";
          if (this.isPreliosSelected === true) {
            // select Prelios
            if (expert[idProp] === forkResponse[1]) {
              this._chosenExpert = expert;
            }
          } else if (
            this._chosenExpert &&
            this._chosenExpert[idProp] === expert[idProp]
          ) {
            this._chosenExpert = expert;
            break;
          }
        }
        if (this.isPreliosSelected === true) {
          // remove all but Prelios
          this.searchResults.positions = this.searchResults.positions.filter(
            (currentCodeObj) => {
              const idProp = currentCodeObj.idExpert ? "idExpert" : "idSocPer";
              return currentCodeObj[idProp] === forkResponse[1];
            }
          );
          this.searchResults.count = this.searchResults.positions.length;
        }
        this.getExpertListForPrinting();
      }
      this.checkIsValid();
    });
  }

  chooseExpert(index: number) {
    this._chosenExpert = this.searchResults.positions[index];
    this.checkIsValid();
  }
  /**
   * @function
   * @name selectExpertType
   * @description Invoca il metodo di recupero periti in base al tipo selezionato
   */
  selectExpertType() {
    this.seeAll = false;
    this.selectionMade = true;
    this._pageNumber = 1;
    this._chosenExpert = null;
    this.getExpertList(this._expertType);
  }
  /**
   * @function
   * @name getExpertListForPrinting
   * @description In base al valore di seeAll che indica la flaggatura del toogle
   * restituisce l'elenco completo o scremato di esperti da mostrare in tabella
   */
  getExpertListForPrinting(): Expert[] {
    if (this.seeAll) {
      this.expertListForPrinting = this.searchResults.positions.slice(0);
    } else {
      this.expertListForPrinting = this.searchResults.positions.slice(0, 4);
    }

    return this.expertListForPrinting;
  }

  toogleSeeAll(): void {
    if (this.selectionMade) {
      this.seeAll = !this.seeAll;
      this.getExpertListForPrinting();
    }
  }

  saveExpert() {
    let expertType: string, expertId: string;
    expertType = this._chosenExpert.idExpert ? "PER" : "SOC";
    const idProp = this._chosenExpert.idExpert ? "idExpert" : "idSocPer";
    expertId = this._chosenExpert[idProp];
    const eventCode: string = this.getEventCode(idProp);
    //se chiamata da perizia tecnica da utente OPER
    if (this.isOperCTEAppraisalRequest) {
      this._expertAssignmentService
        .saveExpert(this.positionId, expertType, expertId, this.surveyType)
        .subscribe(() => {});
      return;
    }
    this._expertAssignmentService
      .saveExpert(this.positionId, expertType, expertId, this.surveyType)
      .switchMap((res) => {
        const lockUnlockObj = new LockUnlockTask(this.taskId, this.taskCod);
        lockUnlockObj.eventCode = eventCode;
        lockUnlockObj.parametersList.push(
          new Parameter(
            "UBZ_MANUALASSIGNMENT_newAgentIdentifier",
            this._chosenExpert.ndg
          )
        );
        lockUnlockObj.parametersList.push(
          new Parameter(
            "UBZ_MANUALASSIGNMENT_isInternalAgent",
            this._chosenExpert.internal.toString()
          )
        );
        return this._accessRightsService.closeTask(lockUnlockObj);
      })
      .subscribe(() => {
        this._messageService.showSuccess(
          this._translateService.instant("UBZ.SITE_CONTENT.1001100000"),
          this._translateService.instant("UBZ.SITE_CONTENT.1001100001")
        );
        this._router.navigateByUrl("dashboard/LAA");
      });
  }

  private getEventCode(idProp: string): string {
    const propertyName = idProp === "idSocPer" ? "socId" : "expertId";
    if (!this.prevSelectedData || !this.prevSelectedData[propertyName]) {
      return "UBZ_MAS_CHGD";
    }
    if (this.prevSelectedData[propertyName] === this._chosenExpert[idProp]) {
      return "UBZ_MAS_CONF";
    } else {
      return "UBZ_MAS_CHGD";
    }
  }

  getButtonDisableState(): boolean {
    if (
      this._chosenExpert &&
      this.searchResults.positions.length > 0 &&
      (!this.isSecondOpinion || this.surveyType)
    ) {
      return false;
    } else {
      return true;
    }
  }

  changePage(event: any) {
    this.seeAll = false;
    this._pageNumber = event.page;
    this.getExpertList(this._expertType);
  }

  changePageSize(event: any) {
    this._pageNumber = 1;
    this._pageSize = event.target.value;
    this.getExpertList(this._expertType);
  }

  taskLocked() {
    this.isTaskLocked = true;
    this._userDataService.getAll().subscribe((res: UserData) => {
      this._expertAssignmentService.taskLockingUser = res.username;
    });
  }

  tasklockedByOtherUser(user: string) {
    this._expertAssignmentService.taskLockingUser = user;
    this.isTaskLocked = false;
  }

  taskUnlocked() {
    this._expertAssignmentService.taskLockingUser = undefined;
    this.isTaskLocked = false;
  }

  lockPrelios() {
    this.isPreliosSelected = true;
    if (this._expertType !== "SOC") this._expertType = "SOC";
    this.selectExpertType();
  }

  unlockPrelios() {
    this.isPreliosSelected = false;
    this._chosenExpert = null;
    if (this._expertType !== "SOC") this._expertType = "SOC";
    this.selectExpertType();
  }

  checkIsValid() {
    if (this.isOperCTEAppraisalRequest) {
      if (!this._expertType || this._expertType === "" || !this._chosenExpert) {
        this.emitIsNotCompiled(null);
        return;
      }
      const idProp = this._chosenExpert.idExpert ? "idExpert" : "idSocPer";
      if (!this._chosenExpert[idProp]) {
        this.emitIsNotCompiled(null);
        return;
      }
      this.emitIsCompiled(null);
    }
  }

  public emitIsCompiled(event: any) {
    this.isCompiled.emit(null);
  }

  public emitIsNotCompiled(event: any) {
    this.isNotCompiled.emit(null);
  }

  private checkInvoiceFlagWarning() {
    if (this.invoiceFlag === "Y" && this.opinionType === "PRI") {
      this._messageService.showWarning(
        this._translateService.instant("UBZ.SITE_CONTENT.11111111001"),
        this._translateService.instant("UBZ.SITE_CONTENT.11110000000")
      );
    }
  }
}
