import {
  Component,
  Inject,
  OnInit,
  ViewChild
} from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ExternalMortageService } from './service/external-mortage.service';
import { ExternalMortageFilterModel } from './model/external-mortage.filters.models';


@Component({
  selector: 'app-external-mortage',
  templateUrl: './external-mortage.component.html',
  styleUrls: ['./external-mortage.component.css'],
  providers: [ExternalMortageService, ExternalMortageFilterModel]
})

export class ExternalMortageComponent implements OnInit {
  @ViewChild('paginator') paginator: any;
  public filtersOpen: boolean;
  public pageSize = 10;
  private pageNumber = 1;
  public searchFilter: ExternalMortageFilterModel = new ExternalMortageFilterModel();
  private searchActive: boolean;

  public reqAppraisals: any[] = [];
  public reqAppraisalsNumber: number;

  constructor(
    private _externalMortageService: ExternalMortageService,
    private _router: Router
  ) { }

  ngOnInit() {
    this.getList();
  }

  private getList() {
    if (!this.searchActive) {
      this.getReqAppraisalsList();
    } else {
      this.filter();
    }
  }

  private getReqAppraisalsList(): void {
    this._externalMortageService
      .getAntergateAppraisalList(this.pageNumber, this.pageSize)
      .subscribe(res => {
        this.parseResults(res);
      });
  }

  public filter() {
    this.searchActive = true;
    this.searchFilter.ndg =
      this.searchFilter.ndg === '' ? null : this.searchFilter.ndg;
    this.searchFilter.requestId =
      this.searchFilter.requestId === '' ? null : this.searchFilter.requestId;
    this.searchFilter.isDraft = typeof this.searchFilter.isDraft !== 'undefined' ? this.searchFilter.isDraft : null;
    this.searchFilter.page = this.pageNumber;
    this.searchFilter.pageSize = this.pageSize;

    this._externalMortageService
      .filter(this.searchFilter)
      .subscribe(res => {
        this.parseResults(res);
      });
  }

  public startFilter() {
    //inizializzo la ricerca
    //i parametri sono già impostati
    this.searchActive = true;
    if (this.pageNumber > 1) {
      //cambiare pagina fa scattare il get list
      this.paginator.page = 1;
    } else {
      //se non cambio pagina invoco la funzione
      this.filter();
    }
  }

  private parseResults(httpResults: any): void {
    this.reqAppraisals = httpResults.list;
    // this.reqAppraisalsNumber = httpResults.list.length;
    // fixme: change line when service is updated
    this.reqAppraisalsNumber = httpResults.count;
  }

  public changePage(event: any) {
    this.pageNumber = event.page;
    this.getList();
  }

  public pageSizeChanged() {
    this.getList();
  }


  public goToReqAppraisalsDetail(requestId: string) {
    this._router.navigateByUrl(
      `external-mortage-process/${requestId}`
    );
  }

  public openFilters(): void {
    this.searchFilter = new ExternalMortageFilterModel();
    this.filtersOpen = true;
  }

  public closeFilters(): void {
    this.getReqAppraisalsList();
    this.filtersOpen = false;
    this.searchActive = false;
  }

  public cleanSearchFilter() {
    this.searchFilter = new ExternalMortageFilterModel();
  }

}
