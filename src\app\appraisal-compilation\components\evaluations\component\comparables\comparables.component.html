<form #f="ngForm" novalidate (click)="accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
          {{'UBZ.SITE_CONTENT.10010010000' | translate }}
          <span class="state" [ngClass]="{'green': f.valid, 'red': !f.valid}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <!-- Sezione dinamica -->
          <div class = "row">
            <table class = "uc-table">
              <thead>
                <tr>
                  <th scope="col" class="col-sm-2"></th>
                  <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10010010100' | translate }} 1*</th>
                  <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10010010100' | translate }} 2*</th>
                  <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10010010100' | translate }} 3</th>
                  <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10010010100' | translate }} 4</th>
                </tr>
              </thead>
              <tr *ngFor = "let row of comparablesList; let rowIndex=index">
                <!-- L'ULTIMA RIGA APPARTIENE ALLA PARTE STATICA -->
                <ng-container *ngIf="rowIndex < comparablesList.length - 1">
                  <td>{{ row.translationCod | translate }}</td>
                  <!-- Switch della tipologia di campo da mostrare -->
                  <td [ngSwitch] = "field.type" *ngFor = "let field of row.comparableField; let columnIndex=index">
                    <!-- Campo text di inserimento stringa -->
                    <ng-container  *ngSwitchCase="'Text'">
                      <div class="row">
                        <div class="col-sm-12" [ngClass] = "{'col-sm-8': !field.percentageHide}">
                          <input
                            type="text"
                            class="form-control"
                            name="{{row.fieldId}}-text{{columnIndex}}"
                            value=""
                            [(ngModel)]="field.fieldValue"
                            [required]="field.mandatory === 'Y'">
                        </div>

                        <div class="col-sm-4" *ngIf = "!field.percentageHide">
                          <input
                            type="text"
                            class="form-control input-percentage"
                            name="{{row.fieldId}}-perc{{columnIndex}}"
                            value=""
                            [(ngModel)]="field.percentage"
                            appForcePattern
                            regexPattern="{{ field.pattern }}">
                            <span class="percentage-label">%</span>
                        </div>
                      </div>
                    </ng-container>

                    <!-- Campo text di inserimento numerico -->
                    <ng-container *ngSwitchCase="'Number'">
                      <div class="row">
                        <div class="col-sm-12" [ngClass] = "{'col-sm-8': !field.percentageHide}">
                          <app-importo
                            [name] = "row.fieldId + '-number' + columnIndex"
                            [ngClassAdd]="input-percentage"
                            [required] = "field.mandatory === 'Y'"
                            [(ngModel)] = "field.fieldValue">
                          </app-importo>
                        </div>

                        <div class="col-sm-4" *ngIf = "!field.percentageHide">
                          <input
                            type="text"
                            class="form-control input-percentage"
                            name="{{row.fieldId}}-perc{{columnIndex}}"
                            value=""
                            [(ngModel)]="field.percentage"
                            appForcePattern
                            regexPattern="{{ field.pattern }}">
                            <span class="percentage-label">%</span>
                        </div>
                      </div>
                    </ng-container>

                    <!-- Campo select -->
                    <ng-container *ngSwitchCase="'Combo'">
                      <div class="row">
                        <div class="custom-select" class="col-sm-12" [ngClass] = "{'col-sm-8': !field.percentageHide}">
                          <select class="form-control" [(ngModel)]="field.fieldValue" name="{{row.fieldId}}-combo{{columnIndex}}" [required]="field.mandatory === 'Y'">
                            <option value="" hidden>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                            <option *ngFor="let elem of (domainsObject[field.domainCod] | domainMapToDomainArray)" value="{{elem.domCode}}">{{elem.translationCod | translate}}</option>
                          </select>
                        </div>

                        <div class="col-sm-4" *ngIf = "!field.percentageHide">
                          <input
                            type="text"
                            class="form-control input-percentage"
                            name="{{row.fieldId}}-perc{{columnIndex}}"
                            [(ngModel)]="field.percentage"
                            appForcePattern
                            regexPattern= "{{field.pattern}}"
                            [required]="field.mandatory === 'Y'">
                          <span class="percentage-label">%</span>
                        </div>
                      </div>
                    </ng-container>

                    <!-- Campo text di inserimento stringa con regexp -->
                    <ng-container *ngSwitchCase="'Textregexp'">
                      <div class="row">
                        <div class="col-sm-12">
                          <input
                            type="text"
                            class="form-control"
                            name="{{row.fieldId}}-text{{columnIndex}}"
                            [(ngModel)]="field.fieldValue"
                            value=""
                            appForcePattern
                            regexPattern="{{ field.pattern }}"
                            [required]="field.mandatory === 'Y'">
                        </div>
                      </div>
                    </ng-container>

                    <!-- Campo label in sola lettura -->
                    <ng-container *ngSwitchCase="'Label'">
                      <label *ngIf="rowIndex !== 0" for="{{field.description}}">{{field.fieldValue}}</label>
                      <label *ngIf="rowIndex === 0" for="{{field.description}}">{{unitValue(columnIndex) | number:'1.0-3'}}</label>
                    </ng-container>
                  </td>
                </ng-container>
              </tr>


              <!-- Sezione statica -->
              <tr class="separator"></tr>

              <!-- Totale % ponderazione -->
              <tr class="result-separator-row">
                <td><b>{{ 'UBZ.SITE_CONTENT.10010010001' | translate }}</b></td>
                <td *ngFor = "let discount of [1,2,3,4]; let columnIndex=index" >
                  <label  [ngClass]="{'outRange':  (ponderingTotalPerc(columnIndex) < -35 || ponderingTotalPerc(columnIndex) > 35) }">{{ponderingTotalPerc(columnIndex)| number:'1.0-3' }}</label>
                </td>
              </tr>

              <!-- Valore di mercato ponderato -->
              <tr>
                <td><b>{{ 'UBZ.SITE_CONTENT.10010010010' | translate }}</b></td>
                <td *ngFor = "let value of [1,2,3,4]; let columnIndex=index">
                  <label>{{ponderingMarketValue(columnIndex) | number:'1.0-3'}}</label>
                </td>
              </tr>

              <tr>
                <td><b>{{ 'UBZ.SITE_CONTENT.10010010011' | translate }}</b></td>
                <td><label>{{averageMarketValue() | number:'1.0-3'}}</label></td>
                <td></td>
                <!-- <td><b>{{ 'UBZ.SITE_CONTENT.10010100001' | translate }}</b></td> -->
                <td><b>{{ comparablesList[comparablesList.length-1].translationCod | translate }}</b></td>

                <td>
                  <!-- Valore contenuto nel primo oggetto dell'ultima riga dell'array -->
                  <app-importo
                    [name]="'usedMarketValue'"
                    [ngClassAdd]="'input-percentage'"
                    [ngClassCondition]="true"
                    [required]="true"
                    [(ngModel)]="comparablesList[comparablesList.length-1].comparableField[0].fieldValue">
                  </app-importo>
                </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
  </accordion-group>
</form>
