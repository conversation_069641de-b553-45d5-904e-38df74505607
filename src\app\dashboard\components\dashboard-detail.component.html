<app-counter *ngIf="counterInput && counterInput.counter && renderCounter" [inputParam]="counterInput" (outputEvent)="changeCounter($event)"></app-counter>
<br/>
  <div *ngIf="activeSla" class="row" style="background-color :	#F5F5F5; padding-top: 10px; padding-bottom: 10px">
    <div class="col-sm-3 pull-right">
      <div class="custom-select">
        <select class="form-control" style="border-color : #F5F5F5" [(ngModel)]="selectedItem" name="selectedItem" required (change)=selectSate()>
             <option value="" selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
             <option *ngFor="let item of salArray" value ="{{item.statusCod}}" >{{item.labelCod |translate}}</option>
        </select>
      </div>
    </div>
    <div class="col-sm-1 pull-right text-right" style="margin-top : 10px">
          <label>{{'UBZ.SITE_CONTENT.1111111110' | translate }} :</label>
    </div>

  </div><br />
<table class="table table-hover">
  <thead>
    <tr>
      <ng-container *ngFor="let field of headFields">
        <th *ngIf="evalExpression(field.condition)" scope="col">
          {{field.label | translate }}
          <ng-container *ngIf="field.orderBy !== ''">
            <!-- <i class="icon-sort" (click)="sortPage(field)"></i> -->
            <i role="button" class="icon-sort icon-lightblue" [ngClass]="{'' : (field.orderDesc === null), ' sort-top' : (field.orderDesc === true), ' sort-bottom' : (field.orderDesc === false)}" (click)="sortPage(field)"></i>
          </ng-container>
        </th>
      </ng-container>
      <th scope="col"></th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let position of positionListResults?.positions">
      <td *ngIf="evalExpression(fieldsConditions['id'])" data-label="id">{{position.id}}</td>
      <td *ngIf="evalExpression(fieldsConditions['appraisalId'])" data-label="appraisalId">{{position.appraisalId}}</td>
      <td *ngIf="evalExpression(fieldsConditions['ndg'])" data-label="ndg">{{position.ndg}}</td>
      <td *ngIf="evalExpression(fieldsConditions['heading'])" data-label="heading">{{position.heading}}</td>
      <td *ngIf="evalExpression(fieldsConditions['type'])" data-label="type">{{position.type}}</td>
      <td *ngIf="evalExpression(fieldsConditions['phase'])" data-label="type">{{ ( phaseDom[position.phaseApp] && phaseDom[position.phaseApp].translationCod ) ? ( phaseDom[position.phaseApp].translationCod | translate ) : '---' }}</td>
      <td *ngIf="evalExpression(fieldsConditions['statusPhase'])" data-label="statusPhase">{{ getStatusPhase(position.phase, position.status) | translate}}</td>
      <!-- <td *ngIf="evalExpression(fieldsConditions['statusPhase'])" data-label="statusPhase">{{ (position.phase && position.status) ? (domainList[position.phase + position.status].translationStatusCod | translate) : '' }}</td> -->
      <td *ngIf="evalExpression(fieldsConditions['statusPhaseApp'])" data-label="statusPhase">{{ getStatusPhase(position.phaseApp, position.statusApp) | translate}}</td>
      <td *ngIf="evalExpression(fieldsConditions['task'])" data-label="task">{{position.task | translate}}</td>
      <td *ngIf="evalExpression(fieldsConditions['inChargeUser'])" data-label="inChargeUser">{{position.inChargeUser}}</td>
      <td *ngIf="evalExpression(fieldsConditions['insertDate'])" data-label="insertDate">{{position.insertDate | date: 'dd-MM-yyyy'}}</td>
      <td *ngIf="evalExpression(fieldsConditions['updateDate'])" data-label="updateDate">{{position.updateDate | date: 'dd-MM-yyyy'}}</td>
      <td *ngIf="evalExpression(fieldsConditions['deleteDate'])" data-label="deleteDate">{{position.deleteDate | date: 'dd-MM-yyyy'}}</td>
      <td *ngIf="evalExpression(fieldsConditions['creationUser'])" data-label="creationUser">{{position.creationUser}}</td>
      <td *ngIf="evalExpression(fieldsConditions['creationBranch'])" data-label="creationBranch">{{position.creationBranch}}</td>
      <td *ngIf="evalExpression(fieldsConditions['conclusionChangeDate'])" data-label="conclusionChangeDate">{{position.conclusionChangeDate | date: 'dd-MM-yyyy'}}</td>
      <td *ngIf="evalExpression(fieldsConditions['annulmentChangeDate'])" data-label="annulmentChangeDate">{{position.annulmentChangeDate | date: 'dd-MM-yyyy'}}</td>
      <td data-label><a *appAuthKey="'UBZ_DASHBOARD.DETAIL_GO'" role="button" (click)="goToTarget(position.id, position.appraisalId, position.taskId, position.taskCod)"><i class="icon-angle-double-right"></i></a></td>
    </tr>
  </tbody>
</table>

<div class="row">
  <div *ngIf="selectedCounterElementsNumb > 10" class="col-sm-6">
    <div class="results">
      <span>{{'UBZ.SITE_CONTENT.1111111' | translate }}</span>
      <div class="custom-select">
        <select class="form-control" [(ngModel)]="pageSize" (change)="changePageSize()">
          <option value="10" selected>10 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{selectedCounterElementsNumb}}</option>
          <option *ngIf="selectedCounterElementsNumb > 20" value="20">20 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{selectedCounterElementsNumb}}</option>
          <option *ngIf="selectedCounterElementsNumb > 30" value="30">30 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{selectedCounterElementsNumb}}</option>
          <option *ngIf="selectedCounterElementsNumb > 40" value="40">40 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{selectedCounterElementsNumb}}</option>
          <option *ngIf="selectedCounterElementsNumb > 50" value="50">50 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{selectedCounterElementsNumb}}</option>
          <option *ngIf="selectedCounterElementsNumb <= 50" [ngValue]="selectedCounterElementsNumb"> {{selectedCounterElementsNumb}} {{'UBZ.SITE_CONTENT.10000000' | translate }} {{selectedCounterElementsNumb}}</option>
        </select>
      </div>
    </div>
  </div>
  <div class="col-sm-6" class="pull-right">
    <pagination [boundaryLinks]="true" [directionLinks]="false" [totalItems]="selectedCounterElementsNumb" [ngModel]="page" [itemsPerPage]="pageSize" [maxSize]="10"
      (pageChanged)="changePage($event)" class="pagination" previousText="&lsaquo;" nextText="&rsaquo;" firstText="&laquo;" lastText="&raquo;"></pagination>
  </div>
</div>
