import { Component, Inject, OnInit } from '@angular/core';
import { Http } from '@angular/http';
import { CustomHttpService } from '../http/custom-http.service';

@Component({
  selector: 'app-spinner',
  templateUrl: './spinner.component.html',
  styleUrls: ['./spinner.component.css']
})
export class SpinnerComponent {
  pendingRequests;
  isShowed: boolean;
  constructor(@Inject(Http) public http: CustomHttpService) {
    this.http.getStatusChangeSubj().subscribe(data => {
      this.isShowed = data;
    });
    
  }


}
