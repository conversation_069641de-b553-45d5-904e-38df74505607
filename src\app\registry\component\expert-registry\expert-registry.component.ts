import {
  Component,
  Inject,
  Input,
  On<PERSON>hang<PERSON>,
  <PERSON>Chang<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild
} from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs/Subscription';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';
import { RegistryService } from '../../service/registry.service';
import { DomainService } from '../../../shared/domain/domain.service';
import { SECTIONS } from '../../model/registry.models';
import { ExpertFilterModel } from '../../model/registry.filters.models';
import { MessageService } from '../../../shared/messages/services/message.service';

@Component({
  selector: 'app-expert-registry',
  templateUrl: './expert-registry.component.html',
  styleUrls: ['./expert-registry.component.css']
})
export class ExpertRegistryComponent implements OnChanges, OnD<PERSON>roy {
  @Input() public chosenSection: string;
  private ACTIVABLE_SECTIONS = SECTIONS;
  public experts: any[] = [];
  public addExpert: boolean;
  public filtersOpen: boolean;
  public newExpert;
  public provinces = [];
  public cities = [];
  public pageSize = 10;
  private pageNumber = 1;
  public expertsNumber: number;
  public searchFilter: ExpertFilterModel = new ExpertFilterModel();
  private searchActive: boolean;
  public contractChosen: boolean;

  private listSubscription: Subscription;
  private filterSubscription: Subscription;

  @ViewChild('fileToUpload') fileToUpload: any;
  fileName = '';
  sizeLimit: number;

  constructor(
    private _registryService: RegistryService,
    private _domainService: DomainService,
    private _router: Router,
    @Inject(APP_CONSTANTS) private _constants: IAppConstants,
    private _messageService: MessageService
  ) { }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['chosenSection'] && changes['chosenSection'].currentValue) {
      this.closeFilters();
    }
  }

  ngOnDestroy() {
    if (this.listSubscription && !this.listSubscription.closed) {
      this.listSubscription.unsubscribe();
    }
    if (this.filterSubscription && !this.filterSubscription.closed) {
      this.filterSubscription.unsubscribe();
    }
  }

  private getExpertList(): void {
    const internalExperts = this.getExpertInternalTypeAsBoolean();
    this.listSubscription = this._registryService
      .getExperts(internalExperts, this.pageNumber, this.pageSize)
      .subscribe(res => {
        this.parseResults(res);
      });
  }

  private parseResults(httpResults: any): void {
    this.experts = httpResults.listExperts;
    for (const expert of this.experts) {
      expert.startAbilitation = new Date(expert.startAbilitation);
      expert.endAbilitation = new Date(expert.endAbilitation);
    }
    this.expertsNumber = httpResults.totExperts;
  }

  public goToNewExpert() {
    this.newExpert = {};
    this._domainService.newGetDomain('UBZ_DOM_PROVINCE').subscribe(res => {
      this.provinces = res;
      this.addExpert = true;
    });
  }

  public saveNewExpert() {
    const toSave = JSON.parse(JSON.stringify(this.newExpert));
    toSave.startAbilitation = Date.parse(toSave.startAbilitation);
    toSave.endAbilitation = Date.parse(toSave.endAbilitation);
    toSave['anagSubjectType'] = this.getAnagSubjectType();
    toSave['expertType'] = this.getExpertType();
    toSave['status'] = 'ATT';
    this._registryService
      .saveNewExpert(toSave, this.fileToUpload.nativeElement.files[0])
      .subscribe(res => {
        this.getExpertList();
        this.addExpert = false;
      });
  }

  private getAnagSubjectType(): string {
    const sec: string = this.chosenSection;
    if (sec === SECTIONS['INT']) {
      return this._constants.SubjectType['PER'];
    } else {
      return this._constants.SubjectType['PBN'];
    }
  }

  private getExpertType(): string {
    const sec: string = this.chosenSection;
    if (sec === SECTIONS['INT']) {
      return '001';
    } else if (sec === SECTIONS['BEN']) {
      return '002';
    } else {
      return null;
    }
  }

  public changePage(event: any) {
    this.pageNumber = event.page;
    this.getList();
  }

  public pageSizeChanged() {
    this.getList();
  }

  private getList() {
    if (!this.searchActive) {
      this.getExpertList();
    } else {
      this.filter();
    }
  }

  public goToExpertDetail(idAnag: string) {
    this._router.navigateByUrl(
      `registry/${this._constants.SubjectType['PER']}/${idAnag}`
    );
  }

  private getExpertInternalTypeAsBoolean(): boolean {
    return this.chosenSection === this.ACTIVABLE_SECTIONS['INT'] ? true : false;
  }

  public openFilters(): void {
    this.searchFilter = new ExpertFilterModel();
    this.filtersOpen = true;
  }

  public closeFilters(): void {
    this.getExpertList();
    this.filtersOpen = false;
    this.searchActive = false;
  }

  public filter() {
    this.searchActive = true;

    this.searchFilter.ndg =
      this.searchFilter.ndg === '' ? null : this.searchFilter.ndg;
    this.searchFilter.firstName =
      this.searchFilter.firstName === '' ? null : this.searchFilter.firstName;
    this.searchFilter.lastName =
      this.searchFilter.lastName === '' ? null : this.searchFilter.lastName;
    this.searchFilter.isInternal = this.getExpertInternalTypeAsBoolean();
    this.searchFilter.page = this.pageNumber;
    this.searchFilter.pageSize = this.pageSize;

    this.filterSubscription = this._registryService
      .filter(this.getAnagSubjectType(), this.searchFilter)
      .subscribe(res => {
        this.parseResults(res);
      });
  }

  public cleanSearchFilter() {
    this.searchFilter = new ExpertFilterModel();
  }

  public calculateCities(event: any) {
    this._domainService.getDomainCity(event.target.value).subscribe(res => {
      this.cities = res;
    });
  }

  setFile() {
    if (this.fileToUpload.nativeElement.files[0].size > this.sizeLimit) {
      // this.fileToUpload.nativeElement.files = null;
      this._messageService.showError(
        `LA DIMENSIONE DEL FILE NON PUO\' SUPERARE I ${this.sizeLimit /
        1048576} MEGABYTE`,
        'DIMENSIONE FILE NON CONSENTITA'
      );
      this.contractChosen = false;
      this.setFileName('');
    } else {
      this.contractChosen = true;
      this.setFileName();
    }
  }

  setFileName(fileName?: string) {
    if (!fileName && fileName !== '') {
      fileName = this.fileToUpload.nativeElement.files[0].name;
    }
    this.fileName = fileName;
  }
}
