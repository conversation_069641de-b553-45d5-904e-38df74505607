import {
  Component,
  OnChang<PERSON>,
  Input,
  <PERSON><PERSON><PERSON><PERSON>,
  SimpleChang<PERSON>
} from '@angular/core';
import { Subscription } from 'rxjs/Subscription';
import { RegistryService } from '../../../../service/registry.service';
import { IdentityDocument } from '../../../../model/registry.models';

@Component({
  selector: 'app-expert-id-card',
  templateUrl: './expert-id-card.component.html',
  styleUrls: ['./expert-id-card.component.css']
})
export class ExpertIdCardComponent implements OnChanges, OnDestroy {
  public modify = false;
  @Input() public anagId: string;
  public document = new IdentityDocument();
  public documentExists: boolean;
  private _subscriptions: Subscription[] = [];

  constructor(private _registryService: RegistryService) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes && this.anagId) {
      this.refreshIdCard();
    }
  }

  ngOnDestroy() {
    for (const subscription of this._subscriptions) {
      if (subscription && !subscription.closed) {
        subscription.unsubscribe();
      }
    }
  }

  public cancelModify(event: any): void {
    this.stopPropagation(event);
    this.modify = false;
  }

  public saveData(event: any): void {
    this.stopPropagation(event);
    this._subscriptions[1] = this._registryService
      .saveIdentityDocument(this.anagId, this.document)
      .subscribe(res => {
        this.refreshIdCard();
        this.modify = false;
      });
  }

  private refreshIdCard(): void {
    this._subscriptions[0] = this._registryService
      .getIdentityDocument(this.anagId)
      .subscribe(res => {
        this.parseResponse(res);
        this.checkIfDocumentExists();
      });
  }

  private parseResponse(httpResponse: any) {
    this.document = httpResponse;
    if (this.document.expiryDate) {
      this.document.expiryDate = new Date(this.document.expiryDate);
    }
    if (this.document.releaseDate) {
      this.document.releaseDate = new Date(this.document.releaseDate);
    }
  }

  private checkIfDocumentExists() {
    if (
      this.document.documentType ||
      this.document.documentNumber ||
      this.document.releaseCity ||
      this.document.releaseDate ||
      this.document.expiryDate
    ) {
      this.documentExists = true;
    } else {
      this.documentExists = false;
    }
  }

  public startModify(event: any): void {
    this.stopPropagation(event);
    this.modify = true;
  }

  private stopPropagation(event): void {
    event.stopPropagation();
  }
}
