import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Inject, ViewChild } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { AccordionAPFService } from '../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { QteService } from './service/qte.service';
import { MenuService } from '../../../shared/menu/services/menu.service';
import { LandingService } from '../../../simulation/services/landing/landing.service';
import { WizardService } from '../../../shared/wizard/services/wizard.service';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';
import { QteResponse } from './model/qte.models';
import { FinancialPlanComponent } from './accordions/financial-plan/financial-plan.component';
import { TotalCostsComponent } from './accordions/total-costs/total-costs.component';
import { PositionService } from '../../../shared/position/position.service';
import { AppraisalTemplateValidationComponent } from '../appraisal-template-validation/appraisal-template-validation.component';
import { AppraisalCompilationService } from '../../service/appraisal-compilation.service';

@Component({
  selector: 'app-qte',
  templateUrl: './qte.component.html',
  styleUrls: ['./qte.component.css'],
  providers: [QteService, AppraisalCompilationService]
})
export class QteComponent implements OnInit, OnDestroy {
  private positionId: string;
  private wizardCode: string;
  currentTask = 'UBZ-PER-QTE';
  private bpmTaskId: string;
  bpmTaskCod: string;
  private appraisalType: string;
  @ViewChild(FinancialPlanComponent)
  private totalCostComponent: FinancialPlanComponent;
  @ViewChild(TotalCostsComponent)
  private financialPlanComponent: TotalCostsComponent;
  @ViewChild(AppraisalTemplateValidationComponent)
  templateValidationComponent: AppraisalTemplateValidationComponent;
  response: QteResponse = new QteResponse();
  private _subscription;
  haveDisabledFields = false; // Quando true i campi sono disabilitati (ad esempio per perizie SAL o Fine lavori)
  public _saveIsEnable = false;

  saveDraftCallback = this.saveDraft.bind(this);

  constructor(
    public menuService: MenuService,
    private _qteService: QteService,
    public _landingService: LandingService,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private _wizardService: WizardService,
    public _positionService: PositionService,
    private _appraisalCompilationService: AppraisalCompilationService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) {}

  ngOnInit() {
    this._subscription = this._activatedRoute.params
      .switchMap((params: Params) => {
        this.positionId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        this.bpmTaskId = params['taskId'];
        this.bpmTaskCod = params['taskCod'];
        this._landingService.checkIfCompletedTask(
          this.positionId,
          this.currentTask
        );
        return Observable.forkJoin(
          this._qteService.getData(this.positionId),
          this._positionService.getAppraisalInfo(this.positionId)
        );
      })
      .subscribe(res => {
        this.response = res[0].partialSaveData
          ? JSON.parse(res[0].partialSaveData)
          : res[0];

        this.response.qteDate =
          res[0].qteDate !== 0 ? new Date(res[0].qteDate) : '';
        this.response.qteUpdateDate =
          res[0].qteUpdateDate !== 0 ? new Date(res[0].qteUpdateDate) : '';

        this.response.totalCostUpdateDate =
          res[0].totalCostUpdateDate !== 0
            ? new Date(res[0].totalCostUpdateDate)
            : '';
        this.setAppraisalInfo(res[1]);
      });
  }

  /**
   * @name setAppraisalInfo
   * @description Metodo invocato sul recupero dell'oggetto perizia, imposta le variabili ad esso connesse
   * @param appraisalInfo Oggetto perizia recuperto dal wizard container
   */
  setAppraisalInfo(appraisalInfo) {
    this.appraisalType = appraisalInfo.appraisal.appraisalType;

    // Alcune perizie non migrate hanno campi disabilitati
    if (
      appraisalInfo.appraisal.originProcess !== 'MIG' &&
      !appraisalInfo.migParent
    ) {
      // Beni mobili SAL/FLA hanno alcuni campi disabilitati
      this.haveDisabledFields = this.isBeneMobile(appraisalInfo) &&
        (
          this.appraisalType === this.constants.appraisalTypes.FINE_LAVORI ||
          this.appraisalType === this.constants.appraisalTypes.SAL
        );
      // Frazionamento ha alcuni campi disabilitati
      this.haveDisabledFields = this.haveDisabledFields || (this.appraisalType === this.constants.appraisalTypes.FRAZIONAMENTO);
    }
  }

  isBeneMobile(appraisalInfo) {
    return appraisalInfo.appraisal
      && appraisalInfo.appraisal.resItemType
      && appraisalInfo.appraisal.resItemType === 'MOB';
  }

  ngOnDestroy() {
    this._subscription.unsubscribe();
  }

  goToPreviousTask() {
    this._landingService.goToPreviousTask(this.positionId, this.wizardCode, this._activatedRoute);
  }

  saveData() {
    this.savePageData().subscribe(res => {
      this._landingService.goNextPage(
        this.positionId,
        this.currentTask,
        this.wizardCode,
        this._activatedRoute
      );
    });
  }

  savePageData(): Observable<any> {
    return this._qteService
      .saveData(this.positionId, this.response)
      .switchMap(() => {
        if (this.templateValidationComponent) {
          return this._appraisalCompilationService.saveTemplateValidation(
            this.templateValidationComponent.appValidation
          );
        } else {
          return Observable.of(true);
        }
      });
  }

  saveDraft(): Observable<any> {
    return this._landingService
      .saveDraft(this.positionId, JSON.stringify(this.response), {}, 'QTE')
      .switchMap(() => {
        if (this.templateValidationComponent) {
          return this._appraisalCompilationService.saveTemplateValidation(
            this.templateValidationComponent.appValidation
          );
        } else {
          return Observable.of(true);
        }
      });
  }

  goToGenericTask() {
    this._landingService.goToGenericTask(this.positionId, this.bpmTaskId, this.bpmTaskCod);
  }

  saveIsEnable() {
    if (!this.totalCostComponent || !this.financialPlanComponent) {
      setTimeout(() => {
        this._saveIsEnable = false;
      }, 0);
    }
    setTimeout(() => {
      this._saveIsEnable =
        this.totalCostComponent.isSectionValid() &&
        this.financialPlanComponent.isSectionValid() &&
        (!this.templateValidationComponent ||
          this.templateValidationComponent.form.valid);
    }, 0);
  }
}
