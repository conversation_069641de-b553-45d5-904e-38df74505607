import { Component, OnInit, Input } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { DomainService } from '../../../shared/domain/domain.service';
import { SampleChecksConfigurationRule } from '../../configuration.models';

@Component({
  selector: 'app-sample-checks',
  templateUrl: './sample-checks.component.html',
  styleUrls: ['./sample-checks.component.css']
})
export class SampleChecksComponent implements OnInit {
  @Input() configurations: SampleChecksConfigurationRule[];
  macroProcessTypes: any[] = [];
  checkTypes: any[] = [];
  isModalOpen = false;
  selectedConfiguration: SampleChecksConfigurationRule;
  private selectedItem;
  ruleToExpand: SampleChecksConfigurationRule = new SampleChecksConfigurationRule();

  constructor(private _domainService: DomainService) {}

  ngOnInit() {
    Observable.forkJoin(
      this._domainService.newGetDomain('UBZ_DOM_MACRO_PROCESS'),
      this._domainService.newGetDomain('UBZ_DOM_CHECK_TYPE')
    ).subscribe(res => {
      this.macroProcessTypes = res[0];
      this.checkTypes = res[1];
    });
  }

  public hide() {
    this.isModalOpen = false;
  }

  public openModal(item: any) {
    this.selectedConfiguration = JSON.parse(JSON.stringify(item));
    this.selectedConfiguration.bornDate = new Date(
      this.selectedConfiguration.bornDate
    );
    this.selectedConfiguration.deadDate = new Date(
      this.selectedConfiguration.deadDate
    );
    this.selectedItem = this.configurations.indexOf(item);
    this.isModalOpen = true;
  }

  public saveSelectedConfiguration(): void {
    this.configurations[this.selectedItem] = JSON.parse(
      JSON.stringify(this.selectedConfiguration)
    );
    this.hide();
  }

  public selectRuleToExpand(item: SampleChecksConfigurationRule): void {
    if (this.ruleToExpand.ruleId === item.ruleId) {
      this.ruleToExpand = new SampleChecksConfigurationRule();
    } else {
      this.ruleToExpand = item;
    }
  }
}
