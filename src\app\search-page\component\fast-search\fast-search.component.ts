import { Component, OnInit, On<PERSON><PERSON>roy, Inject } from '@angular/core';
import { Router } from '@angular/router';

import { SearchPageService } from '../../service/search-page.service';
import { DomainService } from '../../../shared/domain/domain.service';
import { AccessRightsService } from '../../../shared/access-rights/services/access-rights.service';
import { Subscription } from 'rxjs/Subscription';
import { Observable } from 'rxjs/Observable';

@Component({
  selector: 'app-fast-search',
  templateUrl: './fast-search.component.html',
  styleUrls: ['./fast-search.component.css']
})
export class FastSearchComponent implements OnInit, OnDestroy {
  activeVoiceMenu = { RI: '', PE: '' };
  visibleVoiceMenu = { RI: false, PE: false };
  appraisalTypes = {};
  statusPhase = {};
  macroProcessDomain = {};
  appraisalTypeDomain = {};
  scopeTypeDomain = {};
  subscription: Subscription;

  constructor(
    public searchPageService: SearchPageService,
    public domainService: DomainService,
    private router: Router,
    private accessRightsService: AccessRightsService,
  ) { }

  ngOnInit() {
    this.subscription = this.accessRightsService.authKeysSource.subscribe(
      (functions: string[]) => {
        if (!this.searchPageService.fastSearch['searchResults']) {
          this.router.navigate(['/index']);
          return;
        }
        this.visibleVoiceMenu.PE =
          functions.indexOf('UBZ_SEARCH_APPRAISAL_MODE') > -1;
        this.visibleVoiceMenu.RI =
          functions.indexOf('UBZ_SEARCH_REQUEST_MODE') > -1;

        this.activeVoiceMenu = { RI: '', PE: '' };
        if (
          this.searchPageService.fastSearch['searchResults'].appraisals.length > 0
          && this.visibleVoiceMenu.PE
        ) {
          this.setActiveVoiceMenu('PE');
        } else if (
          this.searchPageService.fastSearch['searchResults'].requests.length >
            0 &&
          this.visibleVoiceMenu.RI
        ) {
          this.setActiveVoiceMenu('RI');

        } else if (this.visibleVoiceMenu.PE) {
          this.setActiveVoiceMenu('PE');
        } else if (this.visibleVoiceMenu.RI) {
          this.setActiveVoiceMenu('RI');

        }
      }
    );
    Observable.forkJoin(
      this.domainService.newGetDomain('UBZ_DOM_POSITION_TYPE'),
      this.domainService.getStatusName(),
      this.domainService.newGetDomain('UBZ_DOM_MACRO_PROCESS'),
      this.domainService.newGetDomain('UBZ_DOM_APPRAISAL_TYPE'),
      this.domainService.newGetDomain('UBZ_DOM_SCOPE_TYPE')
    ).subscribe(x => {
      this.appraisalTypes = x[0];
      this.statusPhase = x[1];
      this.macroProcessDomain = x[2];
      this.appraisalTypeDomain = x[3];
      this.scopeTypeDomain = x[4];
    });
  }

  ngOnDestroy() {
    this.subscription.unsubscribe();
  }

  setActiveVoiceMenu(searchType: string) {
    this.activeVoiceMenu = { RI: '', PE: '' };
    this.activeVoiceMenu[searchType] = 'active';
  }

  goToDetailsPage(appraisalId) {
    this.searchPageService.goToDetailsPage(appraisalId, '/fastSearch');
    // FIXME - TOGLIERE SE CENTRALIZZAZIONE METODO FUNZIONA CORRETTAMENTE
    // this.searchPageService.toggleBackSearchButton('/fastSearch');
    // this.router.navigate(['/generic-task/' + appraisalId + '/-/-/']);
    // this.menuService.changeHeadMenuStatus(0);
  }

  goToAppraisalRequest(requestId) {
    this.searchPageService.goToAppraisalRequest(requestId, '');
    // FIXME - TOGLIERE SE CENTRALIZZAZIONE METODO FUNZIONA CORRETTAMENTE
    // this.searchPageService.toggleBackSearchButton();
    // this.router.navigate([`landing/WRPE/${requestId}`]);
    // this.menuService.changeHeadMenuStatus(0);
  }


}
