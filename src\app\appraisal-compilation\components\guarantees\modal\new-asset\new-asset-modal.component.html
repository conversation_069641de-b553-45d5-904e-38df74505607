<div *ngIf="isOpen" class="modal fade" id="nuovo-asset" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #addUser="bs-modal" (onHidden)="closeModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.1000100111' | translate }}</h2>
        <button type="button" class="close pull-right" aria-label="Close" (click)="closeModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <ng-container *ngIf="!appraisalScope">
            <div class="col-sm-12">
              <p>{{'UBZ.SITE_CONTENT.1000101000' | translate }}</p>
            </div>
            <div class="col-sm-12 form-group">
              <label>{{'UBZ.SITE_CONTENT.11110111' | translate }}*</label>
              <div class="custom-select">
                <select class="form-control" [(ngModel)]="selectedCategory">
                  <option value="" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let row of (categories | domainMapToDomainArray | sortCategoryType)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
                </select>
              </div>
            </div>
          </ng-container>
          </div>
          <div class="row">
            <div class="col-sm-12">
              <p>{{'UBZ.SITE_CONTENT.1011010000' | translate }}</p>
            </div>
              <div class="col-sm-12 form-group custom-radio" *ngFor="let g of guaranteesList; let index = index">
                <input type="radio" name="guarantee" id="guarantee-{{index}}" class="radio" value="{{g.progCollateral}}" required [(ngModel)]="selectedGuarantee">
                <label for="guarantee-{{index}}">{{g.collateralTecForm}} - {{g.collateralDesc}}</label>
              </div>
          </div>
      </div>
      <div class="modal-footer text-center">
        <button type="button" class="btn btn-primary waves-effect" data-dismiss="modal" (click)="addAsset()" [disabled]="!selectedCategory">{{'UBZ.SITE_CONTENT.111100001' | translate }}</button>
      </div>
    </div>
  </div>
</div>
