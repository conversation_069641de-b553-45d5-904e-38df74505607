<!-- FIXME - TOGLIERE CODICE COMMENTATO -->
<div class="row controls">
  <div id="hide-content">
    <div class="col-sm-12 text-right">
      <app-task-access-rights [positionId]="positionId" [taskId]="taskId" [taskCod]="taskCod" [excludeList]="['UBZ_PRZ_OK']"
        (taskLocked)="taskLocked()" (taskUnlocked)="taskUnlocked()" (taskLockedByOtherUser)="tasklockedByOtherUser($event)"></app-task-access-rights>
    </div>
     <form #f="ngForm" (ngSubmit)="submitForm()">
       <div class="col-sm-6">
         <h4 class="section-heading">{{'UBZ.SITE_CONTENT.1101000000' | translate }}</h4>
         <div class="row">
          <div class="col-sm-6 form-group">
            <label>{{'UBZ.SITE_CONTENT.10111' | translate }}*</label>
              <!-- <div class="calendar">
                <input appDateReadOnly ngui-datetime-picker date-only="true" class="form-control calendar" type="text" class="form-control calendar"
                 placeholder="gg/mm/aaaa" data-provide="datepicker" name="date" [(ngModel)]="date" [min-date]="minDateSelectable"  max-date="new Date()" required/>
              </div> -->
              <app-calendario
                [name] = "'date'"
                [(ngModel)]="date"
                [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                [minDate]="minDateSelectable"
                [maxDate]="today"
                [required]="true">
              </app-calendario>
            </div>
            <div class="col-sm-3 form-group">
            <label>{{'UBZ.SITE_CONTENT.101111000' | translate }}*</label>
            <div class="custom-select">
              <select class="form-control" name="hour" [(ngModel)]="hour" required>
                <option value="" hidden selected>{{'UBZ.SITE_CONTENT.11000' | translate }}</option>
                <option *ngFor="let x of hours" value="{{x}}">{{x}}</option>
              </select>
            </div>
          </div>
          <div class="col-sm-3 form-group">
            <label>&nbsp;</label>
            <div class="custom-select">
              <select class="form-control" name="minute" [(ngModel)]="minute" required>
                <option value="" hidden selected>{{'UBZ.SITE_CONTENT.101111010' | translate }}</option>
                <option *ngFor="let x of minutes" value="{{x}}">{{x}}</option>
              </select>
            </div>
          </div>
        </div>
      </div>
      <div class="col-sm-6">
        <h4 class="section-heading">{{'UBZ.SITE_CONTENT.1101000001' | translate }}</h4>
        <div class="row">
          <div class="col-sm-4 form-group">
            <label>{{'UBZ.SITE_CONTENT.1101000010' | translate }}</label>
            <div class="radio-buttons">
              <div class="custom-radio">
                <input type="radio" name="n1" id="1" class="radio" [value]="true" [(ngModel)]="isPositiveResult" required>
                <label for="1">{{'UBZ.SITE_CONTENT.1101000011' | translate }}</label>
              </div>
              <div class="custom-radio">
                <input type="radio" name="n1" id="negativo" class="radio" [value]="false" [(ngModel)]="isPositiveResult" required>
                <label for="negativo">{{'UBZ.SITE_CONTENT.1101000100' | translate }}</label>
              </div>
            </div>
          </div>
          <div class="col-sm-8">
            <label>&nbsp;</label>
            <div *ngIf="isPositiveResult === false" class="form-group" id="show-textarea">                            
              <label>
                <span><i class="icon-search note-tooltip" [tooltip]="note" triggers="click"></i></span>
                {{'UBZ.SITE_CONTENT.11001011' | translate }}*
              </label>
              <textarea class="form-control" name="note" [(ngModel)]="note" required></textarea>
            </div>
            <button *ngIf="isTaskLocked" type="submit" class="btn btn-primary pull-right" id="valida" [disabled]="!f.valid">{{'UBZ.SITE_CONTENT.101111011' | translate }}</button>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
