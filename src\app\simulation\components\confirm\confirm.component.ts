import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';

import { ConfirmService } from '../../services/confirm/confirm.service';
import { MenuService } from '../../../shared/menu/services/menu.service';
import { LandingService } from '../../services/landing/landing.service';
import { MessageService } from '../../../shared/messages/services/message.service';
import { GenericInfoService } from '../../services/generic-info/generic-info.service';
import { of } from 'rxjs/observable/of';
import { AssetService } from '../../services/asset/asset.service';

@Component({
  selector: 'app-confirm',
  templateUrl: './confirm.component.html',
  styleUrls: ['./confirm.component.css'],
  providers: [ConfirmService]
})
export class ConfirmComponent implements OnInit {
  private simulationId;
  wizardCode: string;
  dataLoaded = false;
  isRestriction = false;
  restrictionAssetMessage = '';
  isModalOpen = false;

  constructor(
    private router: Router,
    private confirmService: ConfirmService,
    private activatedRoute: ActivatedRoute,
    private menuService: MenuService,
    public landingService: LandingService,
    private genericInfoService: GenericInfoService,
    private assetService: AssetService,
    private messageService: MessageService
  ) {}

  ngOnInit() {
    this.activatedRoute.parent.params.subscribe((params: Params) => {
      this.simulationId = params['positionId'];
      this.wizardCode = params['wizardCode'];
      this.genericInfoService.getGenericInfo(
        this.simulationId,
        this.wizardCode
      ).subscribe((requestData: any) => {
        if (requestData.appraisalType && requestData.appraisalType === 'RES') {
          this.isRestriction = true;
        }
        this.dataLoaded = true;
      })
    });
  }

  startAppraisalRequest() {
    this.confirmService
      .startAppraisalRequest(this.simulationId)
      .subscribe(x => {
        this.menuService.setMenuStatus(0, 2);
        if (x.esito) {
          this.landingService.refreshWizard();
          this.router.navigate([
            `wizard/WRPE/${this.simulationId}/customer-info`
          ]);
        } else {
          this.messageService.showError(
            "L'NDG inserito in fase di preventivo risulta essere di tipo prospect. Per proseguire è necessario inserire l'NDG effettivo della controparte",
            'NDG PROSPECT'
          );
          this.router.navigate([
            `customer-search/WRPE/${this.simulationId}/${x.familyAsset}/false`
          ]);
        }
      });
  }

  goToNewAppraisal() {    
    this.checkAssetsCanBeSaved().subscribe((assetResponse: any) => {
      if (assetResponse) {
        this.checkRestrictionAssets().subscribe((response) => {
          if (response['wizardType'] && response['wizardType'] !== '') {
            this.restrictionAssetMessage = response['wizardType'];
            this.openModal();
          } else {
            this.goToNewAppraisalConfirmed();
          }
        });
      }
    });
  }

  checkAssetsCanBeSaved() {
    return this.assetService.checkAssetsCanBeSaved(this.simulationId);
  }

  goToNewAppraisalConfirmed() {
    this.confirmService
      .startBPMProcessFromAppraisal(this.simulationId)
      .switchMap(() =>
        this.landingService.getNextTask(
          this.simulationId,
          'UBZ-REQ-CON',
          this.wizardCode
        )
      )
      .subscribe(() => {
        this.router.navigate([`wizard-detail/WRPE/${this.simulationId}`]); // Redirect when all go well
      });    
  
  }

  checkRestrictionAssets() {
    if (this.isRestriction) {
      return this.confirmService.checkRestrictionAssets(this.simulationId);
    } else {
      return of( { wizardType: ''} );
    }
  }
  
  goToSimulationDetail() {
    this.router.navigate([`wizard-detail/WSIM/${this.simulationId}`]);
  }
  
  openModal() {
    this.isModalOpen = true;
  }

  hideModal() {
    this.isModalOpen = false;
  }
}
