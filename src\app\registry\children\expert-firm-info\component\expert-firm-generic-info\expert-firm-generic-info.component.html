<form #f="ngForm">
  <accordion-group #group class="panel" [isOpen]="true">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          {{'UBZ.SITE_CONTENT.1100000011' | translate }}
          <div class="accordion-button">
            <ng-container *appAuthKey="'UBZ_REGISTRY.GENERIC_DATA_MODIFY'">
              <button *ngIf="!genericInfoEditable" type="button" class="btn btn-empty" (click)="editGenericInfo($event)">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.11111' | translate }}
              </button>
            </ng-container>
            <ng-container *ngIf="genericInfoEditable">
              <button *appAuthKey="'UBZ_REGISTRY.GENERIC_DATA_ABORT'" type="button" class="btn btn-empty" (click)="cancelGenericInfoEdit($event)">
                <i class="fa fa-times"></i>{{'UBZ.SITE_CONTENT.100000' | translate }}
              </button>
              <button *appAuthKey="'UBZ_REGISTRY.GENERIC_DATA_SAVE'" type="button" class="btn btn-empty" (click)="saveGenericInfo($event)"
                [disabled]="genForm.invalid || isValidIban">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.100001' | translate }}
              </button>
            </ng-container>
          </div>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <form #genForm="ngForm" novalidate>
            <div class="row">
              <ng-container *ngIf="!(subjectType === 'PER')">
                <div class="col-md-3 col-sm-12 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1100000100' | translate }}
                    <span *ngIf="genericInfoEditable">*</span>
                  </label>
                  <span *ngIf="!genericInfoEditable">{{ genericData.heading }}</span>
                  <input *ngIf="genericInfoEditable" type="text" name="heading" class="form-control" [(ngModel)]="genericData.heading" required>
                </div>
              </ng-container>
              <ng-container *ngIf="subjectType === 'PER'">
                <div class="col-md-3 col-sm-12 form-group">
                  <label>{{'UBZ.SITE_CONTENT.101111110' | translate }}
                    <span *ngIf="genericInfoEditable">*</span>
                  </label>
                  <span *ngIf="!genericInfoEditable">{{ genericData.firstname }}</span>
                  <input *ngIf="genericInfoEditable" type="text" name="firstname" class="form-control" [(ngModel)]="genericData.firstname"
                    required>
                </div>
                <div class="col-md-3 col-sm-12 form-group">
                  <label>{{'UBZ.SITE_CONTENT.101111111' | translate }}
                    <span *ngIf="genericInfoEditable">*</span>
                  </label>
                  <span *ngIf="!genericInfoEditable">{{ genericData.lastname }}</span>
                  <input *ngIf="genericInfoEditable" type="text" name="lastname" class="form-control" [(ngModel)]="genericData.lastname" required>
                </div>
              </ng-container>
              <div class="col-md-3 col-sm-12 form-group">
                <label>{{'UBZ.SITE_CONTENT.101000' | translate }}
                  <span *ngIf="genericInfoEditable">*</span>
                </label>
                <span *ngIf="!genericInfoEditable">{{ provinces[genericData.province]?.translationCod | translate }}</span>
                <div *ngIf="genericInfoEditable" class="custom-select">
                  <select class="form-control" name="province" [(ngModel)]="genericData.province" required (change)="calculateCities($event)">
                    <option value="" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                    <option *ngFor="let item of (provinces | domainMapToDomainArray)" value="{{ item.domCode }}">{{ item.translationCod | translate }}</option>
                  </select>
                </div>
              </div>
              <div class="col-md-3 col-sm-12 form-group">
                <label>{{'UBZ.SITE_CONTENT.101001' | translate }}
                  <span *ngIf="genericInfoEditable">*</span>
                </label>
                <span *ngIf="!genericInfoEditable">{{ selectedCity | translate }}</span>
                <div *ngIf="genericInfoEditable" class="custom-select">
                  <select class="form-control" name="city" [(ngModel)]="genericData.city" required (change)="findCityTranslationCode()">
                    <option value="" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                    <option *ngFor="let item of (cities | domainMapToDomainArray)" value="{{ item.domCode }}">{{ item.translationCod | translate }}</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-3 col-sm-12 form-group">
                <label>{{'UBZ.SITE_CONTENT.101011' | translate }}
                  <span *ngIf="genericInfoEditable">*</span>
                </label>
                <span *ngIf="!genericInfoEditable">{{ genericData.address }}</span>
                <input *ngIf="genericInfoEditable" type="text" name="address" class="form-control" [(ngModel)]="genericData.address" required>
              </div>
              <div class="col-md-3 col-sm-12 form-group">
                <label>{{'UBZ.SITE_CONTENT.1100000101' | translate }}
                  <span *ngIf="genericInfoEditable">*</span>
                </label>
                <span *ngIf="!genericInfoEditable">{{ genericData.streetNum }}</span>
                <input *ngIf="genericInfoEditable" type="text" name="streetNum" class="form-control" required [(ngModel)]="genericData.streetNum">
              </div>
              <div class="col-md-3 col-sm-12 form-group">
                <label>{{'UBZ.SITE_CONTENT.101010' | translate }}
                  <span *ngIf="genericInfoEditable">*</span>
                </label>
                <span *ngIf="!genericInfoEditable">{{ genericData.postalCode }}</span>
                <input *ngIf="genericInfoEditable" type="text" name="postalCode" class="form-control" [(ngModel)]="genericData.postalCode"
                  required>
              </div>
            </div>
            <div class="row">
              <div class="col-md-3 col-sm-12 form-group">
                <label>{{'UBZ.SITE_CONTENT.1100000110' | translate }}
                  <span *ngIf="genericInfoEditable">*</span>
                </label>
                <span *ngIf="!genericInfoEditable">{{ genericData.email }}</span>
                <input *ngIf="genericInfoEditable" type="text" name="email" class="form-control" [(ngModel)]="genericData.email" required>
              </div>
              <div class="col-md-3 col-sm-12 form-group">
                <label for="mobile">{{'UBZ.SITE_CONTENT.11110000' | translate }}
                  <span *ngIf="genericInfoEditable">*</span>
                </label>
                <span *ngIf="!genericInfoEditable">{{ genericData.phoneNumber }}</span>
                <input *ngIf="genericInfoEditable" type="text" name="phoneNumber" class="form-control" [(ngModel)]="genericData.phoneNumber"
                  required appOnlyNumber maxlength='15'>
              </div>
              <div class="col-md-3 col-sm-12 form-group" *ngIf="subjectType === 'PER'">
                <label>{{'UBZ.SITE_CONTENT.1100000111' | translate }}
                  <span *ngIf="genericInfoEditable">*</span>
                </label>
                <span *ngIf="!genericInfoEditable">{{ genericData.mobileNumber }}</span>
                <input *ngIf="genericInfoEditable" type="text" name="mobileNumber" class="form-control" [(ngModel)]="genericData.mobileNumber"
                  required appOnlyNumber maxlength='15'>
              </div>
              <div class="col-md-3 col-sm-12 form-group">
                <label>{{'UBZ.SITE_CONTENT.1001110' | translate }}
                  <span *ngIf="genericInfoEditable">*</span>
                </label>
                <span *ngIf="!genericInfoEditable">{{ genericData.ndg }}</span>
                <input *ngIf="genericInfoEditable" type="text" name="ndg" class="form-control" [(ngModel)]="genericData.ndg" required>
              </div>
            </div>
            <div class="row">
              <ng-container *ngIf="subSubjectType === 'PBN'">
                <div class="col-md-3 col-sm-12 form-group">
                  <label for="mobile">{{'UBZ.SITE_CONTENT.1011100011' | translate }}
                    <span *ngIf="genericInfoEditable">*</span>
                  </label>
                  <span *ngIf="!genericInfoEditable">{{ genericData.userId }}</span>
                  <input *ngIf="genericInfoEditable" type="text" name="userId" class="form-control" required [(ngModel)]="genericData.userId">
                </div>
              </ng-container>
              <ng-container *ngIf="!(subSubjectType === 'PBN')">
                <div class="col-md-3 col-sm-12 form-group">
                  <label for="mobile">{{'UBZ.SITE_CONTENT.1011100011' | translate }}</label>
                  <span *ngIf="!genericInfoEditable">{{ genericData.userId }}</span>
                  <input *ngIf="genericInfoEditable" type="text" name="userId" class="form-control" [(ngModel)]="genericData.userId">
                </div>
              </ng-container>
              <ng-container *ngIf="!(subjectType === 'PER')">
                <div class="col-md-3 col-sm-12 form-group">
                  <label>{{'UBZ.SITE_CONTENT.10011110000' | translate }}
                    <span *ngIf="genericInfoEditable">*</span>
                  </label>
                  <span *ngIf="!genericInfoEditable">{{ genericData.iban }}</span>
                  <input *ngIf="genericInfoEditable" type="text" name="iban" class="form-control" [(ngModel)]="genericData.iban" (keyup)="checkLenghtIban($event.target.value)" required maxlength="27">
                </div>
              </ng-container>
            </div>
            <div class="row">
              <div class="col-md-3 col-sm-12 form-group" *ngIf="subjectType === 'SOC'">
                <label>{{'UBZ.SITE_CONTENT.11110010' | translate }}</label>
                <span *ngIf="!genericInfoEditable">{{ regions[genericData.competenceRegion]?.translationCod | translate }}</span>
                <div *ngIf="genericInfoEditable" class="custom-select">
                  <select class="form-control" name="competenceRegion" [(ngModel)]="genericData.competenceRegion" disabled>
                    <option value="" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                    <option *ngFor="let item of (regions | domainMapToDomainArray)" value="{{ item.domCode }}">{{ item.translationCod | translate }}</option>
                  </select>
                </div>
              </div>
              <div class="col-md-3 col-sm-12 form-group" *ngIf="subjectType !== 'SOC'">
                <label for="expertType">{{'UBZ.SITE_CONTENT.1100001000' | translate }}</label>
                <span *ngIf="!genericInfoEditable">{{ anagStatus && genericData && anagStatus[genericData.status] ? (anagStatus[genericData.status].translationCod
                  | translate) : '' }}</span>
                <div *ngIf="genericInfoEditable" class="custom-select">
                  <select class="form-control" name="status" [(ngModel)]="genericData.status" required>
                    <option value="" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                    <option *ngFor="let item of (anagStatus | domainMapToDomainArray)" value="{{ item.domCode }}">{{ item.translationCod | translate }}</option>
                  </select>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </accordion-group>
  <accordion-group #group class="panel" [isOpen]="true">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          {{'UBZ.SITE_CONTENT.1100001001' | translate }}
          <div class="accordion-button">
            <ng-container *appAuthKey="'UBZ_REGISTY.ABILITATION_MODIFY'">
              <button *ngIf="!assignmentInfoEditable" type="button" class="btn btn-empty" (click)="editAssignmentInfo($event)">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.11111' | translate }}
              </button>
            </ng-container>
            <ng-container *ngIf="assignmentInfoEditable">
              <button *appAuthKey="'UBZ_REGISTY.ABILITATION_ABORT'" type="button" class="btn btn-empty" (click)="cancelAssignmentInfoEdit($event)">
                <i class="fa fa-times"></i>{{'UBZ.SITE_CONTENT.100000' | translate }}
              </button>
              <button *appAuthKey="'UBZ_REGISTY.ABILITATION_SAVE'" type="button" class="btn btn-empty" (click)="saveAbilitationData($event)"
                [disabled]="incForm.invalid">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.100001' | translate }}
              </button>
            </ng-container>
          </div>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <form #incForm="ngForm" novalidate>
            <div class="row">
              <div class="col-md-3 col-sm-12 form-group">
                <label>{{'UBZ.SITE_CONTENT.1100001010' | translate }}
                  <span *ngIf="assignmentInfoEditable">*</span>
                </label>
                <span *ngIf="!assignmentInfoEditable">{{ abilitationData.startAbilitation | date:'dd/MM/yyyy' }}</span>
                <app-calendario *ngIf="assignmentInfoEditable" [name]="'startAbilitation'" [title]="'UBZ.SITE_CONTENT.1010100001' | translate"
                  [(ngModel)]="abilitationData.startAbilitation" [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                  [required]="true">
                </app-calendario>
              </div>
              <div class="col-md-3 col-sm-12 form-group">
                <label>{{'UBZ.SITE_CONTENT.1011111011' | translate }}
                  <span *ngIf="assignmentInfoEditable">*</span>
                </label>
                <span *ngIf="!assignmentInfoEditable">{{ abilitationData.endAbilitation | date:'dd/MM/yyyy' }}</span>
                <app-calendario *ngIf="assignmentInfoEditable" [name]="'endAbilitation'" [title]="'UBZ.SITE_CONTENT.1010100001' | translate"
                  [(ngModel)]="abilitationData.endAbilitation" [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate" [required]="true">
                </app-calendario>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </accordion-group>
</form>
