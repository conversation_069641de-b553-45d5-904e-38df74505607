import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';

@Injectable()
export class AppraisalCtuService {
  constructor(private http: Http) {}

  public getCtuDetail(appraisalId: string) {
    const url = `/UBZ-ESA-RS/service/appraisal/v1/ctu/getCtuAppraisalData/${appraisalId}`;
    // '/assets/data/appraisal-migration/fake-appraisal-migration.json';
    return this.http.get(url).map((resp: Response) => resp.json());
  }



public getAppraisalValue(appraisalId: string){
  const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisals/${appraisalId}/evaluation`;
  return this.http.get(url).map((resp: Response) => resp.json());

}

}
