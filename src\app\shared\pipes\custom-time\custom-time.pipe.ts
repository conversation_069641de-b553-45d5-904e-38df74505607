import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'customTime'
})
export class CustomTimePipe implements PipeTransform {
  transform(value: string): string {
    const date = new Date(parseInt(value, 10));
    const hours = date.getHours() < 10
      ? `0${date.getHours()}`
      : `${date.getHours()}`;
    const mins = date.getMinutes() < 10
      ? `0${date.getMinutes()}`
      : `${date.getMinutes()}`;
    const secs = date.getSeconds() < 10
      ? `0${date.getSeconds()}`
      : `${date.getSeconds()}`;
    return `${hours}:${mins}:${secs}`;
  }
}
