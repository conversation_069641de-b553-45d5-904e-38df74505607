<fieldset [disabled]="!landingService.positionLocked">
  <form method="post" action="info-asset.html" id="form2">
    <fieldset [disabled]="landingService.isLockedTask[taskCode] || (isFrazionamento && isDisabled)">
      <div class="row">
        <div class="col-sm-12">
          <ng-container *appAuthKey="'UBZ_GUARANTEE.INFO_SEARCH'">
            <!-- BUTTON RICERCA AVANZATA NASCOSTO SE NON CI SONO GARANZIA OPPURE SE NON SI è IN MODALITA RICERCA OPPURE SE SIAMO IN FRAZIONAMENTO -->
            <button *ngIf="guarantees?.length > 0 && !isSearchMode && !isFrazionamento" type="button" class="btn btn-empty waves-effect waves-secondary search-btn pull-right"
              id="open-advanced-search" (click)="changeSearchMode()">
              <i class="fa fa-search" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.10010111' | translate }}
            </button>
          </ng-container>
          <div *ngIf="isSearchMode" id="advanced-search">
            <div class="input-group">
              <input #filterInput type="text" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.110001001' | translate }}" (keyup)="setFilteredText(filterInput.value)">
              <span class="input-group-btn">
                <button class="btn btn-default" type="button" (click)="filter()"><i class="fa fa-search" aria-hidden="true"></i></button>
              </span>
              <button type="button" id="search-close" class="close-search" (click)="changeSearchMode()"><i class="fa fa-times"
                  aria-hidden="true"></i></button>
            </div>

            <div class="row expand-filters">
              <div class="col-sm-2 form-group">
                <button type="button" class="btn btn-secondary waves-effect waves-secondary filters-btn" id="advanced-filters-toggle" (click)="areFiltersOpened = !areFiltersOpened">{{filtersLabel[areFiltersOpened]
                  | translate}}</button>
              </div>
              <div class="col-sm-3 adv-filters-option form-group" [style.display]="displayStyle[areFiltersOpened]">
                <div class="custom-select">
                  <select [(ngModel)]="filteredCollat" class="form-control" name="selezione-forma-tecnica" (change)="filter()">
                    <option value="" disabled selected>{{'UBZ.SITE_CONTENT.10011000' | translate }}</option>
                    <option *ngFor="let opt of (collatTecDomain | domainMapToDomainArray)" value="{{opt.domCode}}">{{opt.translationCod
                      | translate}}</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <h3>{{'UBZ.SITE_CONTENT.10011100' | translate }}</h3>
          <h4 *ngIf="guarantees?.length === 0">{{'UBZ.SITE_CONTENT.1001111111' | translate }}</h4>
        </div>
      </div>


      <!--  LISTA GARANZIE-->
      <div *ngIf="guarantees?.length > 0" class="row step-navigation">
        <div class="col-sm-12">
          <div class="custom-checkbox">
            <input name="progressivo" type="checkbox" id="selectall" class="checkbox" [disabled]="isFrazionamento" [checked]="selectAll"
              (change)="changeAllCheckedStatus()">
            <label for="selectall">{{'UBZ.SITE_CONTENT.111010' | translate }}</label>
          </div>
        </div>
      </div>

      <div *ngIf="guarantees?.length > 0" class="row">
        <div class="col-sm-12 form-group">
          <br/>
          <table class="uc-table" id="table-1">
            <thead>
              <tr>
                <th scope="col" class="col-sm-1 checkbox-col"></th>
                <th *ngIf="isFrazionamento" scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10001110101' | translate }}</th>
                <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10001110110' | translate }}</th>
                <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10010101' | translate }}</th>
                <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.10010110' | translate }}</th>
                <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10011110' | translate }}</th>
                <th *ngIf="!isFrazionamento" scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10011001000' | translate }}</th>
                <th *ngIf="!isFrazionamento" scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10011101011' | translate }}</th>
                <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1111110111' | translate }}</th>
                <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1111111000' | translate }}</th>
                <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10011111' | translate }}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let row of filteredGuarantees; let i = index">
                <td data-label="">
                  <div class="custom-checkbox">
                    <input [disabled]="row['invalid'] === true || isFrazionamento" div="custom-checkbox" type="checkbox" id="check{{i}}" name="progressivo-{{i}}"
                      [checked]="row.alreadyAssigned" (change)="changeCheckedStatus(i)">
                    <label *ngIf="row['invalid'] === false" for="check{{i}}"></label>
                    <label *ngIf="row['invalid'] === true" for="check{{i}}" data-toggle="tooltip" attr.title="{{'UBZ.SITE_CONTENT.1011010111' | translate }}"></label>
                  </div>
                </td>
                <td *ngIf="isFrazionamento" attr.data-label="{{'UBZ.SITE_CONTENT.10001110101' | translate }}" style="text-align:center">{{row.oldProgCollateral}}</td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.10001110110' | translate }}">{{row.progCollateral}}</td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.110001010' | translate }}">{{row.collateralTecForm}}</td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.10010110' | translate }}">{{row.collateralDesc}}</td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.11110100' | translate }}">{{row.collateralAmmount | currency:'EUR':true:'1.2-2'}}</td>
                <td *ngIf="!isFrazionamento" attr.data-label="{{'UBZ.SITE_CONTENT.10011001000' | translate }}">
                  <fieldset *ngIf="row.toShowAntergate">
                    <div class="custom-checkbox center-aligned">
                      <input type="checkbox" id="checkAntergate{{i}}" name="checkAntergate{{i}}" [(ngModel)]="row.antergateFlag" (change)="changeAntergateCheckedStatus(i)">
                      <label for="checkAntergate{{i}}" data-toggle="tooltip" attr.title="{{'UBZ.SITE_CONTENT.10011001000' | translate }}"></label>
                    </div>
                  </fieldset>
                </td>
                <td *ngIf="!isFrazionamento" attr.data-label="{{'UBZ.SITE_CONTENT.10011101011' | translate }}">
                  <fieldset *ngIf="row.antergateFlag">
                    <app-importo [name]="'antergate-importo-' + i" [required]="row.antergateFlag" [(ngModel)]="row.antergateValue" [maxlength]="15"
                      [title]="'UBZ.SITE_CONTENT.10011101011' | translate" (ngModelChange)="changeAntergateCheckedStatus(i)">
                    </app-importo>
                  </fieldset>
                </td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.1111110111' | translate }}">
                  <div class="custom-select">
                    <select class="form-control" [(ngModel)]="row.percType" name="percType-{{i}}" class="form-control" (change)="calculateSaveIsEnable()">
                      <option *ngFor="let row of (percentageDomain | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod
                        | translate}}</option>
                    </select>
                  </div>
                </td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.1111111000' | translate }}">
                  <input *ngIf="row.alreadyAssigned && row.percType && row.percType !== 'NES'" [(ngModel)]="row.poolPerc" name="poolPerc-{{i}}" type="text"
                    class="form-control" (ngModelChange)="calculateSaveIsEnable()" appForcePattern regexPattern="{{constants.REGEX_PATTERN.PERCENTAGE}}"
                  />
                </td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.110001011' | translate }}">{{row.propAssociate}} <button type="button"
                    class="btn btn-clean waves-effect waves-secondary hidden" id="expand-table-1" disabled><i class="fa fa-plus-square-o"
                      aria-hidden="true"></i></button></td>
              </tr>
            </tbody>
          </table>
          <br/>
        </div>
      </div>
    </fieldset>

    <div *ngIf="!isFrazionamento" class="col-sm-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.1011010' | translate }}*</label>
      <app-importo
        [(ngModel)] = "credlineAmount"
        (ngModelChange)="calculateSaveIsEnable()"
        [name] ="'credlineAmount'"
        [required] = "true"
        [disabled] = "landingService.originationProcess === 'TGP'"
        [ngClassAdd]="(landingService.originationProcess !== 'TGP' &&  !credlineAmount) ? 'error' : 'valid'"
        [ngClassCondition]="true">
      </app-importo>
    </div>

    <ng-container *ngIf="!isFrazionamento">
      <app-navigation-footer (saveButtonClick)="goToNextPage()" [showPrevious]="showPreviousButton" (previousButtonClick)="previous()"
        [activeTaskCode]="taskCode" (cancelButtonClick)="cancelPosition()" [saveIsEnable]="saveIsEnable">
      </app-navigation-footer>
    </ng-container>
    <ng-container *ngIf="isFrazionamento">
      <app-navigation-footer (saveButtonClick)="goToNextPage()" [showPrevious]="showPreviousButton" (previousButtonClick)="previous()"
        [activeTaskCode]="taskCode" (cancelButtonClick)="cancelPosition()" [saveIsEnable]="saveIsEnable"
        (frazionamentoModify)="modify()" [isFrazionamento]="true" [isModifyActive]="isDisabled"
        [saveDraftCallback]="draftButtonCallback" (closeDraftButtonClick)="draftSaved()" [showSaveDraft]="!isDisabled">
      </app-navigation-footer>
    </ng-container>
  </form>
</fieldset>
