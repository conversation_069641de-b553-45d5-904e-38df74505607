import { Component, OnInit, Inject } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { Router } from '@angular/router';

import { DashboardDetailComponent } from '../dashboard-detail.component';
import { SearchService } from '../../../shared/search/search.service';
import { DomainService } from '../../../shared/domain/domain.service';
import { SearchResultsData } from '../../../shared/search/search-results-data';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';

@Component({
  selector: 'app-loan-administation-sample-checks',
  templateUrl: './loan-administation-sample-checks.component.html',
  providers: [ SearchService ]
})
export class LoanAdministationSampleChecksComponent implements OnInit {

  page = 1;
  pageSize = 10;
  countCode = '010VL';
  positionListResults: SearchResultsData = new SearchResultsData();

  constructor(
    public searchService: SearchService,
    public domainService: DomainService,
    private router: Router,
    @Inject(APP_CONSTANTS) public constants: IAppConstants
  ) { }

  ngOnInit() {
    this.searchService.getSampleCheckCounts().subscribe( counts => {
      this.positionListResults.count = counts['0'].count;
        this.refreshPositionList();
    });
  }

  refreshPositionList() {
    this.searchService.getSampleCheckData(false, this.page, this.pageSize, 'false', false ).subscribe( res => {
      this.positionListResults.positions = res;
    });
  }

  goToEvaluationCompilation( appraisalId: string ) {
    this.router.navigateByUrl( `generic-task/${appraisalId}/${this.constants.SAMPLE_CHECKS}/${this.constants.SAMPLE_CHECKS}` );
  }

  changePageSize() {
    this.page = 1;
    this.ngOnInit();
  }

  changePage(event: any) {
    this.page = event.page;
    this.refreshPositionList();
  }

}
