<!-- FIXME - TOGLIERE COMMENTI SE IMPORTO FUNZIONA -->
<!-- FIXME - RENDI DINAMICO HTML, CREA ARRAY CON LE STRINGHE TITOLO E CICLA SU DI ESSO -->
<form #f="ngForm" novalidate (click)="_accordionAPFService.handleOpenProperty(group, f)" (ngSubmit)="submitForm()">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>{{'UBZ.SITE_CONTENT.1000100110' | translate }}
          <span class="state" [ngClass]="{'green' : (pageIsValid), 'red' : !(pageIsValid)}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <table *ngIf="pageContent" class="uc-table uc-multirow">
      	  <thead>
        		<tr>
        		  <th scope="col" class="col-sm-2" rowspan="2">{{'UBZ.SITE_CONTENT.100100101' | translate }}</th>
        		  <th scope="col" class="col-sm-2" colspan="2">{{'UBZ.SITE_CONTENT.100100110' | translate }}</th>
        		  <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.100100111' | translate }}</th>
        		</tr>
      		  <tr>
      		  <th scope="col" class="col-sm-2 text-center">%</th>
      		  <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.100101000' | translate }}</th>
      		  <th scope="col" class="col-sm-2">%</th>
      		</tr>
      	  </thead>
      	  <tbody>
      		<tr>
      		  <td data-label="Descrizioni rissuntiva delle opere" colspan="4">
      			<span><strong>{{'UBZ.SITE_CONTENT.100101001' | translate }}</strong></span>
      			<table class="uc-table">
      			  <tr>
      				<td class="col-sm-4"></td>
      				<td class="col-sm-2"></td>
      				<td class="col-sm-2"></td>
      				<td class="col-sm-4"></td>
      			  </tr>
      			  <tr>
      				<td colspan="4" class="col-sm-12">
      				  <span>{{'UBZ.SITE_CONTENT.100101010' | translate }}</span>
      				  <table class="uc-table">
      					<tr>
      					  <td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.100101011' | translate }}</span></td>
      					  <td class="col-sm-2">
                    <input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[0].distributionPerc" (change)="onChangeDistributionPerc(0)" name="pageContent0distributionPerc">
                      <i *ngIf="rowIsModified[0]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[0].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[0].histDistributionPerc}}"></i>
                  </td>
									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[0].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent0distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent0distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[0].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
      					  <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[0].updatePerc" name="pageContent0updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>
      					</tr>
      					<tr>
      					  <td class="col-sm-2"><span>{{'UBZ.SITE_CONTENT.100101100' | translate }}</span></td>
                  <td class="col-sm-2">
                  	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[1].distributionPerc" (change)="onChangeDistributionPerc(1)"  name="pageContent1distributionPerc">
                      <i *ngIf="rowIsModified[1]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[1].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[1].histDistributionPerc}}"></i>
                  </td>
									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[1].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent1distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent1distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[1].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
                  <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[1].updatePerc" name="pageContent1updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>
      					</tr>
      				  </table>
      				</td>
      			  </tr>
      			  <tr>
      				<td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.100101101' | translate }}</span></td>
              <td class="col-sm-2">
              	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[2].distributionPerc" (change)="onChangeDistributionPerc(2)" name="pageContent2distributionPerc">
                  <i *ngIf="rowIsModified[2]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[2].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[2].histDistributionPerc}}"></i>
              </td>
							<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[2].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent2distributionAmount"></td> -->
							<td class="col-sm-2">
								<app-importo               
									[name] ="'pageContent2distributionAmount'"
									[required] = "false"
									[disabled]="haveDisabledFields"
									[(ngModel)] = "pageContent[2].distributionAmount"
									(ngModelChange) = "onChangeDistributionAmount()"> 
								</app-importo>
							</td>
              <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[2].updatePerc" name="pageContent2updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>
      			  </tr>
      			  <tr>
      				<td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.100101110' | translate }}</span></td>
              <td class="col-sm-2">
              	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[3].distributionPerc" (change)="onChangeDistributionPerc(3)" name="pageContent3distributionPerc">
                  <i *ngIf="rowIsModified[3]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[3].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[3].histDistributionPerc}}"></i>
              </td>
							<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[3].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent3distributionAmount"></td> -->
							<td class="col-sm-2">
								<app-importo               
									[name] ="'pageContent3distributionAmount'"
									[required] = "false"
									[disabled]="haveDisabledFields"
									[(ngModel)] = "pageContent[3].distributionAmount"
									(ngModelChange) = "onChangeDistributionAmount()"> 
								</app-importo>
							</td>
              <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[3].updatePerc" name="pageContent3updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>
      			  </tr>
      			  <tr>
      				<td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.100101111' | translate }}</span></td>
              <td class="col-sm-2">
              	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[4].distributionPerc" (change)="onChangeDistributionPerc(4)" name="pageContent4distributionPerc">
                  <i *ngIf="rowIsModified[4]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[4].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[4].histDistributionPerc}}"></i>
              </td>
							<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[4].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent4distributionAmount"></td> -->
							<td class="col-sm-2">
								<app-importo               
									[name] ="'pageContent4distributionAmount'"
									[required] = "false"
									[disabled]="haveDisabledFields"
									[(ngModel)] = "pageContent[4].distributionAmount"
									(ngModelChange) = "onChangeDistributionAmount()"> 
								</app-importo>
							</td>
              <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[4].updatePerc" name="pageContent4updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>
      			  </tr>
      			</table>
      		  </td>
      		</tr>
      		<tr>
      		  <td data-label="Descrizioni rissuntiva delle opere" colspan="4">
      			<span><strong>{{'UBZ.SITE_CONTENT.100110000' | translate }}</strong></span>
      			<table class="uc-table">
      			  <tr>
      				<td class="col-sm-4"></td>
      				<td class="col-sm-2"></td>
      				<td class="col-sm-2"></td>
      				<td class="col-sm-4"></td>
      			  </tr>
      			  <tr>
      				<td colspan="4">
      				  <span>{{'UBZ.SITE_CONTENT.100110001' | translate }}</span>
      				  <table class="uc-table">
      					<tr>
      					  <td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.100110010' | translate }}</span></td>
                  <td class="col-sm-2">
                  	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[5].distributionPerc" (change)="onChangeDistributionPerc(5)" name="pageContent5distributionPerc">
                      <i *ngIf="rowIsModified[5]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[5].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[5].histDistributionPerc}}"></i>
                  </td>
									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[5].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent5distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent5distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[5].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
                  <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[5].updatePerc" name="pageContent5updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>
      					</tr>
      					<tr>
      					  <td class="col-sm-2"><span>{{'UBZ.SITE_CONTENT.100110011' | translate }}</span></td>
                  <td class="col-sm-2">
                  	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[6].distributionPerc" (change)="onChangeDistributionPerc(6)" name="pageContent6distributionPerc">
                      <i *ngIf="rowIsModified[6]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[6].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[6].histDistributionPerc}}"></i>
                  </td>
									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[6].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent6distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent6distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[6].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
                  <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[6].updatePerc" name="pageContent6updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>
      					</tr>
      				  </table>
      				</td>
      			  </tr>
      			  <tr>
      				<td colspan="4">
      				  <span>{{'UBZ.SITE_CONTENT.100110100' | translate }}</span>
      				  <table class="uc-table">
      					<tr>
      					  <td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.100110010' | translate }}</span></td>
                  <td class="col-sm-2">
                  	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[7].distributionPerc" (change)="onChangeDistributionPerc(7)" name="pageContent7distributionPerc">
                      <i *ngIf="rowIsModified[7]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[7].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[7].histDistributionPerc}}"></i>
                  </td>
									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[7].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent7distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent7distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[7].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
                  <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[7].updatePerc" name="pageContent7updatePerc" (change)="checkIfPageIsComplete()" [disabled]="haveDisabledFieldsSpecial"></td>
      					</tr>
      					<tr>
      					  <td class="col-sm-2"><span>{{'UBZ.SITE_CONTENT.100110101' | translate }}</span></td>
                  <td class="col-sm-2">
                  	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[8].distributionPerc" (change)="onChangeDistributionPerc(8)" name="pageContent8distributionPerc">
                      <i *ngIf="rowIsModified[8]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[8].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[8].histDistributionPerc}}"></i>
                  </td>
									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[8].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent8distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent8distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[8].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
                  <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[8].updatePerc" name="pageContent8updatePerc" (change)="checkIfPageIsComplete()" [disabled]="haveDisabledFieldsSpecial"></td>
      					</tr>
      				  </table>
      				</td>
      			  </tr>
      			  <tr>
      				<td colspan="4">
      				  <span>{{'UBZ.SITE_CONTENT.100110110' | translate }}</span>
      				  <table class="uc-table">
      					<tr>
      					  <td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.100110010' | translate }}</span></td>
                  <td class="col-sm-2">
                  	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[9].distributionPerc" (change)="onChangeDistributionPerc(9)" name="pageContent9distributionPerc">
                      <i *ngIf="rowIsModified[9]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[9].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[9].histDistributionPerc}}"></i>
                  </td>
									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[9].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent9distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent9distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[9].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
                  <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[9].updatePerc" name="pageContent9updatePerc" (change)="checkIfPageIsComplete()" [disabled]="haveDisabledFieldsSpecial"></td>
      					</tr>
      					<tr>
      					  <td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.100110111' | translate }}</span></td>
                  <td class="col-sm-2">
                  	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[10].distributionPerc" (change)="onChangeDistributionPerc(10)" name="pageContent10distributionPerc">
                      <i *ngIf="rowIsModified[10]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[10].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[10].histDistributionPerc}}"></i>
                  </td>
									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[10].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent10distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent10distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[10].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
                  <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[10].updatePerc" name="pageContent10updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>
      					</tr>
      				  </table>
      				</td>
      			  </tr>
      			  <tr>
      				<td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.100111000' | translate }}</span></td>
              <td class="col-sm-2">
              	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[11].distributionPerc" (change)="onChangeDistributionPerc(11)" name="pageContent11distributionPerc">
                  <i *ngIf="rowIsModified[11]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[11].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[11].histDistributionPerc}}"></i>
              </td>
							<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[11].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent11distributionAmount"></td> -->
							<td class="col-sm-2">
								<app-importo               
									[name] ="'pageContent11distributionAmount'"
									[required] = "false"
									[disabled]="haveDisabledFields"
									[(ngModel)] = "pageContent[11].distributionAmount"
									(ngModelChange) = "onChangeDistributionAmount()"> 
								</app-importo>
							</td>
              <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[11].updatePerc" name="pageContent11updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>
      			  </tr>
      			  <tr>
      				<td colspan="4">
      				  <span><strong>{{'UBZ.SITE_CONTENT.100111001' | translate }}</strong></span>
      				  <table class="uc-table">
      					<tr>
      					  <td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.100111010' | translate }}</span></td>
                  <td class="col-sm-2">
                  	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[12].distributionPerc" (change)="onChangeDistributionPerc(12)" name="pageContent12distributionPerc">
                      <i *ngIf="rowIsModified[12]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[12].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[12].histDistributionPerc}}"></i>
                  </td>
									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[12].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent12distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent12distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[12].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
                  <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[12].updatePerc" name="pageContent12updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>
      					</tr>
      					<tr>
      					  <td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.100111011' | translate }}</span></td>
                  <td class="col-sm-2">
                  	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[13].distributionPerc" (change)="onChangeDistributionPerc(13)" name="pageContent13distributionPerc">
                      <i *ngIf="rowIsModified[13]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[13].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[13].histDistributionPerc}}"></i>
                  </td>
									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[13].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent13distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent13distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[13].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
                  <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[13].updatePerc" name="pageContent13updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>
      					</tr>
      					<tr>
      					  <td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.100111100' | translate }}</span></td>
                  <td class="col-sm-2">
                  	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[14].distributionPerc" (change)="onChangeDistributionPerc(14)" name="pageContent14distributionPerc">
                      <i *ngIf="rowIsModified[14]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[14].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[14].histDistributionPerc}}"></i>
                  </td>
									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[14].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent14distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent14distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[14].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
                  <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[14].updatePerc" name="pageContent14updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>
      					</tr>
      					<tr>
      					  <td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.100111101' | translate }}</span></td>
                  <td class="col-sm-2">
                  	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[15].distributionPerc" (change)="onChangeDistributionPerc(15)" name="pageContent15distributionPerc">
                      <i *ngIf="rowIsModified[15]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[15].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[15].histDistributionPerc}}"></i>
                  </td>
									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[15].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent15distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent15distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[15].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
                  <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[15].updatePerc" name="pageContent15updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>
      					</tr>
      				  </table>
      				</td>
      			  </tr>
      			  <tr>
      				<td colspan="4">
      				  <span><strong>{{'UBZ.SITE_CONTENT.100111110' | translate }}</strong></span>
      				  <table class="uc-table">
      					<tr>
      					  <td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.100111111' | translate }}</span></td>
                  <td class="col-sm-2">
                  	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[16].distributionPerc" (change)="onChangeDistributionPerc(16)" name="pageContent16distributionPerc">
                      <i *ngIf="rowIsModified[16]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[16].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[16].histDistributionPerc}}"></i>
                  </td>
									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[16].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent16distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent16distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[16].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
                  <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[16].updatePerc" name="pageContent16updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>
      					</tr>
      					<tr>
      					  <td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.101000000' | translate }}</span></td>
                  <td class="col-sm-2">
                  	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[17].distributionPerc" (change)="onChangeDistributionPerc(17)" name="pageContent17distributionPerc">
                      <i *ngIf="rowIsModified[17]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[17].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[17].histDistributionPerc}}"></i>
                  </td>
									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[17].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent17distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent17distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[17].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
                  <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[17].updatePerc" name="pageContent17updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>
      					</tr>
      					<tr>
      					  <td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.101000001' | translate }}</span></td>
                  <td class="col-sm-2">
                  	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[18].distributionPerc" (change)="onChangeDistributionPerc(18)" name="pageContent18distributionPerc">
                      <i *ngIf="rowIsModified[18]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[18].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[18].histDistributionPerc}}"></i>
                  </td>

									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[18].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent18distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent18distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[18].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
                  <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[18].updatePerc" name="pageContent18updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>

      					</tr>
      					<tr>
      					  <td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.101000010' | translate }}</span></td>
                  <td class="col-sm-2">
                  	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[19].distributionPerc" (change)="onChangeDistributionPerc(19)" name="pageContent19distributionPerc">
                      <i *ngIf="rowIsModified[19]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[19].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[19].histDistributionPerc}}"></i>
                  </td>
									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[19].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent19distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent19distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[19].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
                  <td class="col-sm-4"><input appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[19].updatePerc" name="pageContent19updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>

      					</tr>
      					<tr>
      					  <td class="col-sm-4"><span>{{'UBZ.SITE_CONTENT.101000011' | translate }}</span></td>
                  <td class="col-sm-2">
                  	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[20].distributionPerc" (change)="onChangeDistributionPerc(20)" name="pageContent20distributionPerc">
                      <i *ngIf="rowIsModified[20]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[20].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[20].histDistributionPerc}}"></i>
                  </td>
									<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[20].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent20distributionAmount"></td> -->
									<td class="col-sm-2">
										<app-importo               
											[name] ="'pageContent20distributionAmount'"
											[required] = "false"
											[disabled]="haveDisabledFields"
											[(ngModel)] = "pageContent[20].distributionAmount"
											(ngModelChange) = "onChangeDistributionAmount()"> 
										</app-importo>
									</td>
                  <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[20].updatePerc" name="pageContent20updatePerc" (change)="checkIfPageIsComplete()" [disabled]="haveDisabledFieldsSpecial"></td>

      					</tr>
      				  </table>
      				</td>
      			  </tr>
      			  <tr>
      				<td class="col-sm-4">
      				  <span><strong>{{'UBZ.SITE_CONTENT.101000100' | translate }}</strong></span>
      				</td>
              <td class="col-sm-2">
              	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[21].distributionPerc" (change)="onChangeDistributionPerc(21)" name="pageContent21distributionPerc">
                  <i *ngIf="rowIsModified[21]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[21].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[21].histDistributionPerc}}"></i>
              </td>
							<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[21].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent21distributionAmount"></td> -->
							<td class="col-sm-2">
								<app-importo               
									[name] ="'pageContent21distributionAmount'"
									[required] = "false"
									[disabled]="haveDisabledFields"
									[(ngModel)] = "pageContent[21].distributionAmount"
									(ngModelChange) = "onChangeDistributionAmount()"> 
								</app-importo>
							</td>
              <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[21].updatePerc" name="pageContent21updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>

      			  </tr>
      			  <tr>
      				<td class="col-sm-4">
      				  <span><strong>{{'UBZ.SITE_CONTENT.101000101' | translate }}</strong></span>
      				</td>
              <td class="col-sm-2">
              	<input [disabled]="haveDisabledFields" type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control form-tooltip" [(ngModel)]="pageContent[22].distributionPerc" (change)="onChangeDistributionPerc(22)" name="pageContent22distributionPerc">
                  <i *ngIf="rowIsModified[22]" class="fa fa-ellipsis-h td-tooltip" aria-hidden="true" data-placement="bottom" data-toggle="tooltip" title="Valore % di default = {{pageContent[22].histDistributionPerc}}" attr.data-original-title="Valore % di default = {{pageContent[22].histDistributionPerc}}"></i>
              </td>
							<!-- <td class="col-sm-2"><input [disabled]="haveDisabledFields" type="text" currencyMask [options] = "currencyMaskConfig" maxlength="22" class="form-control" [(ngModel)]="pageContent[22].distributionAmount" (change)="onChangeDistributionAmount()" name="pageContent22distributionAmount"></td> -->
							<td class="col-sm-2">
								<app-importo               
									[name] ="'pageContent22distributionAmount'"
									[required] = "false"
									[disabled]="haveDisabledFields"
									[(ngModel)] = "pageContent[22].distributionAmount"
									(ngModelChange) = "onChangeDistributionAmount()"> 
								</app-importo>
							</td>
              <td class="col-sm-4"><input type="text" appForcePattern regexPattern="{{_constant.REGEX_PATTERN.PERCENTAGE}}" class="form-control" [(ngModel)]="pageContent[22].updatePerc" name="pageContent22updatePerc" (change)="checkIfPageIsComplete()"  [disabled]="haveDisabledFieldsSpecial"></td>

      			  </tr>
      			  <tr>
      				<td class="col-sm-4">
      				  <span><strong>{{'UBZ.SITE_CONTENT.101010010' | translate }}</strong></span>
      				</td>
      				<td class="col-sm-2"><strong>{{totDistributionPerc}}%</strong></td>
      				<td class="col-sm-2"><strong>{{totDistributionAmount | number:'1.2-2'}}</strong></td>
      				<td class="col-sm-4"></td>
      			  </tr>
      			</table>
      		  </td>
      		</tr>
      	  </tbody>
      	</table>
      </div>
    </div>
  </accordion-group>
</form>
