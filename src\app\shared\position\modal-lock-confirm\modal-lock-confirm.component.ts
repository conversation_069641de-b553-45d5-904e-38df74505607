import {
  Component,
  OnInit,
  Inject,
  Input,
  Output,
  EventEmitter
} from '@angular/core';
import { PositionService } from '../position.service';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';

@Component({
  selector: 'app-modal-lock-confirm',
  templateUrl: './modal-lock-confirm.component.html',
  styleUrls: ['./modal-lock-confirm.component.css']
})
export class ModalLockConfirmComponent implements OnInit {
  @Input() operationType: string;
  @Input() isOpen;
  @Input() positionId: string;
  @Input() productType: string;
  @Input() locked: boolean;
  @Input() position: any;
  @Input() statusDomain: any[] = [];

  @Output() close = new EventEmitter();
  @Output() positionLocked = new EventEmitter();
  @Output() positionUnlocked = new EventEmitter();

  constructor(
    private positionService: PositionService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) {}

  ngOnInit() {}

  hide() {
    this.isOpen = false;
    this.close.emit();
  }

  isRevoca(): boolean {
    return this.constants['positionHeaderStatus']['REV'] === this.operationType;
  }
  isPresaInCarico(): boolean {
    return this.constants['positionHeaderStatus']['PRE'] === this.operationType;
  }

  buttonPressed() {
    if (this.locked) {
      this.unlockAppraisal();
    } else {
      this.lockAppraisal(true);
    }
  }

  private lockAppraisal(force: boolean) {
    this.positionService
      .assignOwnerPosition(this.positionId, this.productType, force)
      .subscribe(x => {
        if (x) {
          this.locked = true;
          this.positionLocked.emit();
        } else {
          this.locked = false;
          this.positionUnlocked.emit();
        }
        this.hide();
      });
  }

  private unlockAppraisal() {
    this.positionService
      .releaseOwnerPosition(this.positionId, this.productType)
      .subscribe(x => {
        this.locked = false;
        this.positionUnlocked.emit();
        this.hide();
      });
  }

  getStatusCode(): string {
    const status = this.position.phaseCode + this.position.statusCode;
    if (this.statusDomain && this.statusDomain[status]) {
      return this.statusDomain[status].translationStatusCod;
    }
    return '';
  }
}
