<fieldset [disabled]="!landingService.positionLocked">
  <div class="row"> 
    <button [disabled]="landingService.isLockedTask[currentTask]" type="button" class="btn btn-secondary waves-effect waves-secondary pull-right" 
      (click)="backToSearch()">{{'UBZ.SITE_CONTENT.1001000' | translate }}
    </button>  
  </div> 
  <!-- RICHIESTA PERIZIA -->
  <fieldset [disabled]="landingService.isLockedTask[currentTask]" *ngIf="wizardCode === 'WRPE'">
    <app-application-form 
      [idCode]="simulationId" 
      [page]="'INFO_CLIENTE_RIC'" 
      [positionId]="simulationId"
      [formDisabled] = "landingService.isLockedTask[currentTask]">
    </app-application-form>
    <!-- Elenco ndg associati alla coi -->
    <app-ndg-list-manager *ngIf = "showNdgDetails" [positionId] = "simulationId"></app-ndg-list-manager>
  </fieldset>
  <app-navigation-footer 
    *ngIf="wizardCode === 'WRPE'" 
    [activeTaskCode]="currentTask" 
    [footerClass]="menuService.footerProperty" 
    (cancelButtonClick)="cancelPosition()" 
    (saveButtonClick)="newSaveCustomerInfo()">
  </app-navigation-footer>
  <!-- FINE RICHIESTA PERIZIA -->

  <!-- RICHIESTA SIMULAZIONE -->
  <fieldset [disabled]="landingService.isLockedTask[currentTask]" *ngIf=" wizardCode !== 'WRPE'">
    <app-application-form 
      [idCode]="simulationId" 
      [page]="'INFO_CLIENTE'" 
      [positionId]="simulationId"
      [formDisabled] = "landingService.isLockedTask[currentTask]">
    </app-application-form>
  </fieldset>
  <app-navigation-footer 
    *ngIf=" wizardCode !== 'WRPE'" 
    [activeTaskCode]="currentTask" 
    [footerClass]="menuService.footerProperty" 
    (cancelButtonClick)="cancelPosition()" 
    (saveButtonClick)="newSaveCustomerInfo()" 
    cancelButtonString="{{'UBZ.SITE_CONTENT.1001010100' | translate}}">
  </app-navigation-footer>
  <!-- FINE RICHIESTA SIMULAZIONE -->

</fieldset>
