<!-- FIXME - CENTRALIZZA MODAL -->
<div class="row step-navigation">
  <div class="col-sm-12">
    <div class="custom-checkbox">
      <input [disabled]="isDisabled" class="checkbox" id="selectall" name="progressivo" type="checkbox" (change)="selectAll()" [checked]="allSelected">
      <label for="selectall">{{'UBZ.SITE_CONTENT.111010' | translate }}</label>
    </div>
  </div>
</div>
<br/>
<div class="row">
  <table class="uc-table">
    <thead>
      <tr>
        <th></th>
        <th>{{'UBZ.SITE_CONTENT.100100' | translate }}</th>
        <th>{{'UBZ.SITE_CONTENT.111100' | translate }}</th>
        <th>{{'UBZ.SITE_CONTENT.110001' | translate}}</th>
        <th>{{'UBZ.SITE_CONTENT.10000110000' | translate}}</th>
        <th>{{'UBZ.SITE_CONTENT.110011' | translate}}</th>
        <th>{{'UBZ.SITE_CONTENT.101100111' | translate }}</th>
        <th>{{'UBZ.SITE_CONTENT.10000000010' | translate }}</th>
        <th></th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let row of assets; let i = index">
        <td>
          <div class="custom-checkbox">
            <input [disabled]="isDisabled" id="check{{i}}" type="checkbox" (change)="changeSelection(row)" [checked]="row.isSelected && row.isSelected === 'Y'" name="check-{{i}}" class="checkbox">
            <label for="check{{i}}"></label>
          </div>
        </td>
        <td>{{ row.assetId }}</td>
        <td>{{ row.assetDescription }}</td>
        <td>{{ row.reRegistrySheet }}</td>
        <td>{{ row.reRegistryPart }}</td>
        <td>{{ row.reRegistrySub }}</td>
        <td>{{ row.appraisalId }}</td>
        <td>{{ row.lastChange | date:'dd/MM/yyyy' }}</td>
        <td style="text-align: center">
          <i class="icon-detail_page" role="button" (click)="openAssetModal( row )">{{'UBZ.SITE_CONTENT.1000000' | translate }}</i>
        </td>
      </tr>
    </tbody>
  </table>
</div>

<app-navigation-footer [showCancelButton]="false" [showPrevious]="false" [saveIsEnable]="saveIsEnable()" confirmButtonString="{{ 'UBZ.SITE_CONTENT.1011' | translate }}"
  (cancelButtonClick)="guaranteeFractionationService.cancelFractionation()" (saveButtonClick)="save()" (previousButtonClick)="previous()"
  (frazionamentoModify)="modify()" [isFrazionamento]="true" [isModifyActive]="isDisabled"
  [saveDraftCallback]="draftButtonCallback" (closeDraftButtonClick)="draftSaved()" [showSaveDraft]="!isDisabled">
</app-navigation-footer>

<div *ngIf="modalIsOpen" class="modal fade" id="nuovo-asset" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal
  #modal="bs-modal" (onHidden)="closeModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.10000000011' | translate }}</h2>
        <button type="button" role="button" class="close pull-right" aria-label="Close" (click)="closeModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <app-application-form [idCode]="modalAsset.assetId" [page]="'INFO_ASSET_UAM_RIC'" [drivers]="{'DD_AST_TIPO': assetType}"
          [setReadOnly]=true [positionId]="modalAsset.appraisalId">
        </app-application-form>
      </div>
    </div>
  </div>
</div>

<div *ngIf="previousModalIsOpen" class="modal fade" id="nuovo-asset" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
  bsModal #modal="bs-modal" (onHidden)="closePreviousModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.1100010' | translate }}</h2>
        <button type="button" role="button" class="close pull-right" aria-label="Close" (click)="closePreviousModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <p>{{'UBZ.SITE_CONTENT.10000000100' | translate }}.</p>
        <p>{{'UBZ.SITE_CONTENT.1111110110' | translate }}?</p>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" (click)="confirmUndo()">
          {{'UBZ.SITE_CONTENT.110101000' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>
