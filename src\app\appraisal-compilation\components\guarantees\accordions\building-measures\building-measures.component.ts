import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  OnChanges,
  SimpleChanges,
  ViewChild,
  AfterViewInit,
  OnDestroy
} from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { Subscription } from 'rxjs/Subscription';
import { NgForm } from '@angular/forms';

// Components
import { ApplicationFormComponent } from '../../../../../shared/application-form/components/application-form.component';

// Services
import { DomainService } from '../../../../../shared/domain/domain.service';
import { AppraisalCompilationService } from '../../../../service/appraisal-compilation.service';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { LandingService } from '../../../../../simulation';

// Componente utiilzzato nel wizard di compilazione perizia (step Unità a Garanzia)
// Effettuati controlli per distinguere fra NDG CORPORATE/INDIVIDUAL e modificare opportunamente il componente
// per seguire il processo e le indicazioni rispettive
@Component({
  selector: 'app-building-measures',
  templateUrl: './building-measures.component.html',
  styleUrls: ['./building-measures.component.css']
})
export class BuildingMeasuresComponent
  implements OnInit, OnChanges, AfterViewInit, OnDestroy {
  @ViewChild(NgForm) form: NgForm;
  @Input() pageContent: any[];
  @Input() positionId: string;
  @Input() assetId: string;
  @Input() domainResp: any; // UBZ_DOM_PROJECT_CORR, UBZ_DOM_PROVISION_STATUS
  @Output() statusChange: EventEmitter<void> = new EventEmitter<void>();
  private subscription: Subscription;

  pageIsValid = false;
  selectedPageElement = null;
  projectCorrDom: any[] = [];
  titleDomain: any[] = [];
  statusDomain: any[] = [];

  // Variabili di gestione customModal
  modalType: string;
  customModalIsOpen = false;  
  largeModalFlag: boolean;
  headerTitle: string;
  apfString: string;
  messagesArray: string[];
  buttonTitle: string[];
  disabledFlag: boolean;

  constructor(
    private domainService: DomainService,
    private appraisalCompilationService: AppraisalCompilationService,
    public _accordionAPFService: AccordionAPFService,
    private landingService: LandingService
  ) {}

  ngOnInit() {
    this.domainService.newGetDomain('UBZ_DOM_PROVISION_TITLE', this.landingService.posSegment)
    .subscribe(x => {
      if(this.domainResp) {
        this.projectCorrDom = this.domainResp['UBZ_DOM_PROJECT_CORR'];
        this.titleDomain = x;
        this.statusDomain = this.domainResp['UBZ_DOM_PROVISION_STATUS'];
      }
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (
      changes &&
      changes['pageContent'] &&
      changes['pageContent'].currentValue
    ) {
      this.checkIfComplete();
    }
  }

  ngAfterViewInit() { 
    if (!this.form) {
      return;
    }
    this.subscription = this.form.statusChanges.subscribe(() => {
      this.checkIfComplete();
    });
  }

  ngOnDestroy() {
    if (this.subscription && !this.subscription.closed) {
      this.subscription.unsubscribe();
    }
  }

  // Imposta le variabili da passare alla modal e ne forza l'apertura
  // modalType indica il tipo di modal che si sta aprendo (add, delete, modify)
  // SelectedElement contiene l'elemento selezionato nel caso di modify e delete
  openCustomModal(modalType: string, selectedElement?: any) {
    if (selectedElement) {
      this.selectedPageElement = selectedElement;      
    }
    this.modalType = modalType;
    switch (this.modalType) {
      case 'add':
        this.largeModalFlag = false;
        this.headerTitle = 'UBZ.SITE_CONTENT.111100010';
        this.apfString = 'PROV_EDILIZI';
        this.messagesArray = [];
        this.buttonTitle = ['UBZ.SITE_CONTENT.111100010'];
        this.disabledFlag = true;
        break;
      case 'modify':
        this.largeModalFlag = false;
        this.headerTitle = 'UBZ.SITE_CONTENT.111100101';
        this.apfString = 'PROV_EDILIZI';
        this.messagesArray = [];        
        this.buttonTitle = ['UBZ.SITE_CONTENT.111100101'];
        this.disabledFlag = true;
        break;
      case 'delete':
        this.largeModalFlag = false;
        this.headerTitle = 'UBZ.SITE_CONTENT.111100011';
        this.apfString = '';
        this.messagesArray = ['UBZ.SITE_CONTENT.111100100'];
        this.buttonTitle = ['UBZ.SITE_CONTENT.11110', 'UBZ.SITE_CONTENT.100000'];
        this.disabledFlag = false;
        break;
    }
    this.customModalIsOpen = true;
  }

  // Intercetta l'evento submit della customModal ed invoca il metodo appropriato a seconda del modalType.
  // L'objectParams in input contiene l'apForm del componente e il modalType (add, modify, delete)
  handleSubmitCustomModal(objectParams: Object) {
    switch (objectParams['modalType']) {
      case 'add':
        this.submitAddModal(objectParams['apForm']);
        break;
      case 'modify':
        this.submitModifyModal(objectParams['apForm']);
        break;
      case 'delete':
        this.submitDeleteModal(objectParams['apForm']);
        break;
    }
  }

  // Esegue metodo di aggiunta per customModal con modalType === 'add'
  submitAddModal(apForm: ApplicationFormComponent) {
    this.appraisalCompilationService
    .saveNewStaticAccordion(
      'buildingProv',
      this.assetId,
      apForm
    )
    .subscribe(res => {
      this.closeCustomModal();
    });
  }

  // Esegue metodo di aggiunta per customModal con modalType === 'modify'
  submitModifyModal(apForm: ApplicationFormComponent) {
    this.appraisalCompilationService
      .saveExistingStaticAccordion('buildingProv', this.selectedPageElement.provisionId, apForm)
      .subscribe(x => {
        this.closeCustomModal();
      });
  }
  
  // Esegue metodo di aggiunta per customModal con modalType === 'delete'
  submitDeleteModal(apForm: ApplicationFormComponent) {
    this.appraisalCompilationService
      .deleteStaticAccordion('buildingProv', this.selectedPageElement.provisionId)
      .subscribe(x => {
        this.closeCustomModal();
      });
  }

  closeCustomModal() {
    this.refreshPagecontent();
    this.selectedPageElement = null;    
    this.customModalIsOpen = false;
  }

  checkIfComplete() {
    if (this.pageContent[0].length >= 1 && this.form && this.form.valid) {
      setTimeout(() => {
        const prev = this.pageIsValid;
        this.pageIsValid = true;
        if (prev !== this.pageIsValid) {
          this.statusChange.emit();
        }
      }, 0);
    } else {
      setTimeout(() => {
        const prev = this.pageIsValid;
        this.pageIsValid = false;
        if (prev !== this.pageIsValid) {
          this.statusChange.emit();
        }
      }, 0);
    }
  }

  refreshPagecontent() {
    this.appraisalCompilationService
      .getSingleStaticComponentOfPropertyGuarantee(this.assetId, 'buildingProv')
      .subscribe(x => {
        this.pageContent[0] = x['buildingProv'];
        this.checkIfComplete();
      });
  }
}
