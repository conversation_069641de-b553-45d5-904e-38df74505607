import { Injectable } from '@angular/core';
import { Http } from '@angular/http';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class RestrictionsService {
  constructor(private _http: Http) {}

  public getLimitationObjects(positionId: string): Observable<any> {
    const url =
      '/UBZ-ESA-RS/service/appraisal/v1/limitationObject/' + positionId;
    return this._http.get(url).map(res => res.json());
  }

  public saveLimitationObjects(
    positionId: string,
    toSave: any
  ): Observable<any> {
    const url =
      '/UBZ-ESA-RS/service/appraisal/v1/limitationObject/' + positionId;
    return this._http.put(url, toSave).map(res => res.json());
  }
}
