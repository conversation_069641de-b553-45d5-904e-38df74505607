import {
  Component,
  On<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>roy,
  Input,
  SimpleChanges
} from '@angular/core';
import { RegistryService } from '../../../../service/registry.service';
import { Subscription } from 'rxjs/Subscription';
import {
  ExpertAppraisal,
  EXPERT_APPRAISAL_TYPES
} from '../../../../model/registry.models';
import { DomainService } from '../../../../../shared/domain/domain.service';
import { Observable } from 'rxjs/Observable';

@Component({
  selector: 'app-expert-appraisals',
  templateUrl: './expert-appraisals.component.html',
  styleUrls: ['./expert-appraisals.component.css']
})
export class ExpertAppraisalsComponent implements OnChanges, OnDestroy {
  @Input() public anagId: string;
  @Input() public appraisalType: string;
  private subscription: Subscription;
  public appraisals: ExpertAppraisal[] = [];
  public anAppraisalExists: boolean;
  public domainStatus: any[];
  public ExpertAppraisalType = EXPERT_APPRAISAL_TYPES;
  public seeAll = false;
  public readonly MAX_RESULTS_COUNT = 10;

  constructor(
    private _registryService: RegistryService,
    private _domainService: DomainService
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes && this.anagId && this.appraisalType) {
      this.subscription = Observable.forkJoin(
        this._domainService.getStatusName(),
        this._registryService.getExpertAppraisals(
          this.anagId,
          this.appraisalType
        )
      ).subscribe(res => {
        this.domainStatus = res[0];
        this.appraisals = res[1];
        this.anAppraisalExists = this.appraisals.length > 0 ? true : false;
      });
    }
  }

  ngOnDestroy() {
    if (this.subscription && !this.subscription.closed) {
      this.subscription.unsubscribe();
    }
  }

  toogleSeeAll() {
    this.seeAll = !this.seeAll;
  }
}
