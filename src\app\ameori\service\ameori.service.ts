import { Injectable } from '@angular/core';
import { Http } from '@angular/http';

@Injectable()
export class AmeoriService {
  searchUrl: string = '/UAM-ESA-RS/service/ameori/v1/ameori';
  updateUrl: string = '/UAM-ESA-RS/service/ameori/v1/ameori/update';
  constructor(private http: Http) {}

  searchList(reportType: string, reportNumber: number, page: number, pageSize:number){
    const input:any = {
      page : page,
      pageSize : pageSize,
      filter : 	{
          accountType: reportType,
          accountNumber: reportNumber,
          newAppraisalId: null
      }
    }
    return this.http.post(this.searchUrl, input).map(res => res.json());
  }

  searchUnique(reportType: string, reportNumber: number, newAppraisalId: number){
    const input:any = {
      page : 1,
      pageSize : 10,
      filter : 	{
          accountType: reportType,
          accountNumber: reportNumber,
          newAppraisalId: newAppraisalId
      }
    }
   return this.http.post(this.searchUrl, input).map(res => res.json());
  }

  update(ameori: any, newAppValue: number){
    const input:any = {
      ameori: ameori,
      updateParam:{
        newAppValue: newAppValue
      }
    }
    return this.http.post(this.updateUrl, input).map(res => res.text());
  }

}
