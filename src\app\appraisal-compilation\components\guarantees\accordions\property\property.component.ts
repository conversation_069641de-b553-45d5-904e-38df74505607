import {
  Component,
  OnInit,
  Input,
  EventEmitter,
  Output,
  OnChanges,
  SimpleChanges
} from '@angular/core';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';

@Component({
  selector: 'app-property',
  templateUrl: './property.component.html',
  styleUrls: ['./property.component.css']
})
export class PropertyComponent implements OnInit, OnChanges {
  @Input() pageContent: any[];
  @Input() positionId: string;
  @Input() objectCode: string;
  @Input() domainResp: any; // UBZ_DOM_RIGHT_TYPE
  @Output() statusChange: EventEmitter<boolean> = new EventEmitter<boolean>();

  pageIsValid = false;
  deleteModalIsOpen = false;
  modalType: string; // indica che tipo di modale aprire tra aggiunta e modifica
  modalIsOpen: boolean = false;  // boolean che gestisce visualizzazione modal aggiunta/modifica

  selectedProperty: any;
  selectedPropertyIndex;
  propertyAmountsStructure: any[] = [];
  rightTypeDomain: any[] = [];

  constructor(
    public _accordionAPFService: AccordionAPFService,
  ) { }

  ngOnInit() {
    if(this.domainResp) {
      this.rightTypeDomain = this.domainResp;
    }
  }

  ngOnChanges(simpleChanges: SimpleChanges) {
    if (
      simpleChanges['pageContent'] &&
      simpleChanges['pageContent'].currentValue
    ) {
      this.calculateAmount();
    }
  }

  delete(index) {
    this.selectedPropertyIndex = index;
    this.deleteModalIsOpen = true;
  }

  // Gestisce il submit della modale di cancellazione utente
  submitDeleteModal(propertyIndex: number) {
    this.pageContent.splice(propertyIndex, 1);
    this.calculateAmount();
    this.closeDeleteModal();
  }

  // Chiude e resetta le variabili per la modale di cancellazione
  closeDeleteModal() {
    this.selectedPropertyIndex = null;
    this.deleteModalIsOpen = false;
  }

  private calculatePropertyAmountsStructure() {
    this.propertyAmountsStructure = [];
    for (const el of this.pageContent) {
      let set = false;
      for (const el2 of this.propertyAmountsStructure) {
        if (el2.rightType === el.rightType) {
          el2.sum += Number(el.ownershipPerc);
          set = true;
        }
      }
      if (!set) {
        this.propertyAmountsStructure.push({
          rightType: el.rightType,
          sum: Number(el.ownershipPerc)
        });
      }
    }
  }

  calculateAmount() {
    this.calculatePropertyAmountsStructure();
    const prevValue = this.pageIsValid;
    let set = false;
    for (const el of this.propertyAmountsStructure) {
      if (el.sum !== 100) {
        set = true;
        this.pageIsValid = false;
      }
    }
    if (this.propertyAmountsStructure.length === 0) {
      this.pageIsValid = false;
      set = true;
    }
    if (!set) {
      this.pageIsValid = true;
    }

    if (prevValue !== this.pageIsValid) {
      this.statusChange.emit(this.pageIsValid);
    }
  }

  // Apre la modale specificandone il modalType (add / modify)
  // Index parametro opzionale, passato in caso di modale di modifica.
  openModal(modalType: string, property?: any, index?: number) {
    // Se siamo in modifica, si recupera l'oggetto esatto tramite index, altrimenti si passa un nuovo oggetto per l'aggiunta
    if (property) {
      this.selectedPropertyIndex = index;
      this.selectedProperty = Object.assign({}, property);
    } else {
      this.selectedPropertyIndex = null;
      this.selectedProperty = {
        customer: {
          ndgType: 'PF',
          customerId: null,
          ndg: null,
          name: null,
          surname: null,
          heading: null,
          vatNum: null,
          taxNum: null
        },
        objectCode: null,
        ownershipPerc: null,
        rightType: null,
        rightNote: null
      };
    }
    this.modalType = modalType;
    this.modalIsOpen = true;
  }

  // Metodo scatenato dal submit all'interno della modal per aggiunta/modifica
  submitModal(propertyObj) {
    switch (this.modalType) {
      case 'add':
        propertyObj.property.objectCode = this.objectCode;
        this.pageContent.push(propertyObj.property);
        break;
      case 'modify':
        this.pageContent[propertyObj.index] = propertyObj.property;
        this.selectedProperty = null;
        this.selectedPropertyIndex = null;
        break;
    }
    this.modalIsOpen = false;
    this.calculateAmount();
  }
}
