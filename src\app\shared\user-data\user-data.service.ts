import {Injectable } from '@angular/core';
import { Headers, Http } from '@angular/http';
import { CookieService } from 'ngx-cookie';
import { Observable } from 'rxjs';
import { UserData } from './user-data';
@Injectable()
export class UserDataService {
  private regex: RegExp = new RegExp(/([^\=\s]*)\=([^\|]*)\|?/g);
  private userdataMapping = {
    u: 'username',
    to: 'tower',
    pr: 'profile',
    ln: 'lastName',
    lg: 'language',
    fn: 'firstName',
    co: 'country',
    br: 'branch',
    cbc: 'centralBankCode',
    esbc: 'euroSigBankCode',
    pgeTOUT: 'pgeTokenTimeOut'
  };
 
  constructor(private cookieService: CookieService,private http:Http) {
  }

 url = '/UBZ-ESA-RS/service/umfService/accessRights/userData'
 getDataUser(): Observable<any> {
    const headers = new Headers({
     'Accept': 'application/json',
      'Content-type': 'application/json'
    
   });
return this.http.get(this.url, {withCredentials: true,  headers: headers }).map((res:any) => res.text())
}

 getAll():any{
return  this.getDataUser().map((res)=>this.parseKeyValueList(res))
 }

  getUserData():UserData{
  return this.parseKeyValueList(this.cookieService.get('userdata'));  
  }
  
  private parseKeyValueList(userdata:string): UserData {
    let m;
    const result = new UserData();
    while ((m = this.regex.exec(userdata)) !== null) {
      if (m[1] && m[2]) {
        result[this.userdataMapping[m[1]] || m[1]] = m[2];
      }
    }
    return result;
  }
  
}
