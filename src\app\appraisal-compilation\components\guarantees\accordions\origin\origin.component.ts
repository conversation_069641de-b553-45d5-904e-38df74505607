import { Component, OnInit, Input, OnChanges, SimpleChanges } from '@angular/core';
import { AppraisalCompilationService } from '../../../../service/appraisal-compilation.service';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { ApplicationFormComponent } from '../../../../../shared/application-form/components/application-form.component';

// Componente utiilzzato nel wizard di compilazione perizia (step Unità a Garanzia)
// Effettuati controlli per distinguere fra NDG CORPORATE/INDIVIDUAL e modificare opportunamente il componente
// per seguire il processo e le indicazioni rispettive
@Component({
  selector: 'app-origin',
  templateUrl: './origin.component.html',
  styleUrls: ['./origin.component.css']
})

export class OriginComponent implements OnInit, OnChanges {
  @Input() pageContent: any[];
  @Input() positionId: string;
  @Input() assetId: string;
  @Input() domainResp: any; // UBZ_DOM_ORIGIN_TYPE

  pageIsValid = false;
  originTypes: string[] = [];
  selectedPageElement = null;

  // Variabili di gestione customModal
  modalType: string;
  customModalIsOpen = false;  
  largeModalFlag: boolean;
  headerTitle: string;
  apfString: string;
  messagesArray: string[];
  buttonTitle: string[];
  disabledFlag: boolean;

  constructor(
    private appraisalCompilationService: AppraisalCompilationService,
    public _accordionAPFService: AccordionAPFService,
  ) {}

  ngOnInit() {
    if(this.domainResp) {
      this.originTypes = this.domainResp;
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['pageContent'] && changes['pageContent'].currentValue) {
      this.checkIfComplete();
    }
  }

  // Imposta le variabili da passare alla modal e ne forza l'apertura
  // modalType indica il tipo di modal che si sta aprendo (add, delete, modify)
  // SelectedElement contiene l'elemento selezionato nel caso di modify e delete
  openCustomModal(modalType: string, selectedElement?: any) {
    if (selectedElement) {
      this.selectedPageElement = selectedElement;      
    }
    this.modalType = modalType;
    switch (this.modalType) {
      case 'add':
        this.largeModalFlag = false;
        this.headerTitle = 'UBZ.SITE_CONTENT.1000010111';
        this.apfString = 'PROVENIENZA';
        this.messagesArray = [];
        this.buttonTitle = ['UBZ.SITE_CONTENT.1000010111'];
        this.disabledFlag = true;
        break;
      case 'modify':
        this.largeModalFlag = false;
        this.headerTitle = 'UBZ.SITE_CONTENT.1000011010';
        this.apfString = 'PROVENIENZA';
        this.messagesArray = [];        
        this.buttonTitle = ['UBZ.SITE_CONTENT.1000011010'];
        this.disabledFlag = true;
        break;
      case 'delete':
        this.largeModalFlag = false;
        this.headerTitle = 'UBZ.SITE_CONTENT.1000011000';
        this.apfString = '';
        this.messagesArray = ['UBZ.SITE_CONTENT.1000011001'];
        this.buttonTitle = ['UBZ.SITE_CONTENT.11110', 'UBZ.SITE_CONTENT.100000'];
        this.disabledFlag = false;
        break;
    }
    this.customModalIsOpen = true;
  }

  // Intercetta l'evento submit della customModal ed invoca il metodo appropriato a seconda del modalType.
  // L'objectParams in input contiene l'apForm del componente e il modalType (add, modify, delete)
  handleSubmitCustomModal(objectParams: Object) {
    switch (objectParams['modalType']) {
      case 'add':
        this.submitAddModal(objectParams['apForm']);
        break;
      case 'modify':
        this.submitModifyModal(objectParams['apForm']);
        break;
      case 'delete':
        this.submitDeleteModal(objectParams['apForm']);
        break;
    }
  }

  // Esegue metodo di aggiunta per customModal con modalType === 'add'
  submitAddModal(apForm: ApplicationFormComponent) {
    this.appraisalCompilationService
      .saveNewStaticAccordion(
        'originObject',
        this.assetId,
        apForm
      )
      .subscribe(x => {
        this.closeCustomModal();
      });
  }

  // Esegue metodo di aggiunta per customModal con modalType === 'modify'
  submitModifyModal(apForm: ApplicationFormComponent) {
    this.appraisalCompilationService
      .saveExistingStaticAccordion(
        'originObject',
        this.selectedPageElement.originId,
        apForm
      )
      .subscribe(x => {
        this.closeCustomModal();
      });
  }
  
  // Esegue metodo di aggiunta per customModal con modalType === 'delete'
  submitDeleteModal(apForm: ApplicationFormComponent) {
    this.appraisalCompilationService
    .deleteStaticAccordion('originObject', this.selectedPageElement.originId)
    .subscribe(x => {
      this.closeCustomModal();
    });
  }

  closeCustomModal() {
    this.refreshPagecontent();
    this.selectedPageElement = null;    
    this.customModalIsOpen = false;
  }

  checkIfComplete() {
    if (this.pageContent.length >= 1) {
      this.pageIsValid = true;
    } else {
      this.pageIsValid = false;
    }
  }

  refreshPagecontent() {
    this.appraisalCompilationService
      .getSingleStaticComponentOfPropertyGuarantee(this.assetId, 'originObject')
      .subscribe(x => {
        this.pageContent = x['originObject'];
        this.checkIfComplete();
      });
  }
}
