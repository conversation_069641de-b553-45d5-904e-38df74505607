import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hild, Inject } from '@angular/core';
import { Activated<PERSON>oute, Params, Router } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { WizardService } from '../../../shared/wizard/services/wizard.service';
import { LandingService } from '../../../simulation/services/landing/landing.service';
import { AccordionAPFService } from '../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { AccordionApplicationFormComponent } from '../../../shared/application-form/components/accordion-application-form/accordion-application-form.component';
import { AppraisalCompilationService } from '../../service/appraisal-compilation.service';
import { PositionService } from '../../../shared/position/position.service';
import { AppraisalTemplateValidationComponent } from '../appraisal-template-validation/appraisal-template-validation.component';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';

@Component({
  selector: 'app-good-description',
  templateUrl: './good-description.component.html',
  styleUrls: ['./good-description.component.css'],
  providers: [AppraisalCompilationService]
})
export class GoodDescriptionComponent implements OnInit, OnDestroy {
  public positionId: string;
  private wizardCode: string;
  currentTask = 'UBZ-PER-DBE';
  private bpmTaskId: string;
  bpmTaskCod: string;
  private appraisalType: string;
  @ViewChild(AccordionApplicationFormComponent)
  accordionApf: AccordionApplicationFormComponent;
  @ViewChild(AppraisalTemplateValidationComponent)
  templateValidation: AppraisalTemplateValidationComponent;
  private _subscription;
  private _saveSubscription;
  pageCompleted = false;
  saveDraftCallback = this.saveDraft.bind(this);
  haveDisabledFields = false; // Quando true i campi sono disabilitati (ad esempio per perizie SAL o Fine lavori)

  constructor(
    private _activatedRoute: ActivatedRoute,
    private _router: Router,
    private _wizardService: WizardService,
    public _landingService: LandingService,
    private _appraisalCompilationService: AppraisalCompilationService,
    public _positionService: PositionService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) {}

  ngOnInit() {
    this._subscription = this._activatedRoute.params
      .switchMap((params: Params) => {
        this.positionId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        this.bpmTaskId = params['taskId'];
        this.bpmTaskCod = params['taskCod'];
        this._landingService.checkIfCompletedTask(
          this.positionId,
          this.currentTask
        );
        return this._positionService.getAppraisalInfo(this.positionId);
      })
      .subscribe(appraisalInfo => {
        // setAppraisalInfo, imposta le variabili ad esso connesse
        this.appraisalType = appraisalInfo.appraisal.appraisalType;
        // Alcune perizie non migrate hanno campi disabilitati
        if (
          appraisalInfo.appraisal.originProcess !== 'MIG' &&
          !appraisalInfo.migParent
        ) {
          // Beni mobili SAL/FLA hanno alcuni campi disabilitati
          this.haveDisabledFields = this.isBeneMobile(appraisalInfo) &&
            (
              this.appraisalType === this.constants.appraisalTypes.FINE_LAVORI ||
              this.appraisalType === this.constants.appraisalTypes.SAL
            );
          // Frazionamento ha alcuni campi disabilitati
          this.haveDisabledFields = this.haveDisabledFields || (this.appraisalType === this.constants.appraisalTypes.FRAZIONAMENTO);
        }
        setTimeout(() => {
          this.pageIsValid();
        }, 0);
      });
  }

  isBeneMobile(appraisalInfo) {
    return appraisalInfo.appraisal
      && appraisalInfo.appraisal.resItemType
      && appraisalInfo.appraisal.resItemType === 'MOB';
  }

  ngOnDestroy() {
    this._subscription.unsubscribe();
    if (this._saveSubscription) {
      this._saveSubscription.unsubscribe();
    }
  }

  public save(): Observable<any> {
    const model = {};
    model['apfmap'] = this.accordionApf.getFormsWithModel();
    model['page'] = 'DESC_BENE';
    return this._appraisalCompilationService
      .saveGoodDescriptionData(this.positionId, model)
      .switchMap(() => {
        if (this.templateValidation) {
          return this._appraisalCompilationService.saveTemplateValidation(
            this.templateValidation.appValidation
          );
        } else {
          return Observable.of(true);
        }
      });
  }

  saveDraft(): Observable<any> {
    const apfMap = {};
    apfMap[1] = JSON.stringify(this.accordionApf.model);
    return this._landingService
      .saveDraft(this.positionId, JSON.stringify({}), apfMap, 'DESC_BENE')
      .switchMap(() => {
        if (this.templateValidation) {
          return this._appraisalCompilationService.saveTemplateValidation(
            this.templateValidation.appValidation
          );
        } else {
          return Observable.of(true);
        }
      });
  }

  goNextPage() {
    this._landingService.goNextPage(this.positionId, this.currentTask, this.wizardCode, this._activatedRoute);
  }

  public saveData() {
    this._saveSubscription = this.save().subscribe(res => {
      this.goNextPage();
    });
  }

  goToGenericTask() {
    this._landingService.goToGenericTask(this.positionId, this.bpmTaskId, this.bpmTaskCod);
  }

  goToPreviousTask() {
    this._landingService.goToPreviousTask(this.positionId, this.wizardCode, this._activatedRoute);
  }

  pageIsValid() {
    if (
      this.areDynamicsAllValid() &&
      (!this.templateValidation || this.templateValidation.form.valid)
    ) {
      setTimeout(() => {
        this.pageCompleted = true;
        return this.pageCompleted;
      }, 100);
    } else {
      setTimeout(() => {
        this.pageCompleted = false;
        return this.pageCompleted;        
      }, 100);
    }
  }

  private areDynamicsAllValid(): boolean {
    return this.accordionApf && this.accordionApf.isAllValid();
  }
}