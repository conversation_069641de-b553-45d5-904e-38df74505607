import { Component, OnInit, Inject, ViewChild } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { UploadFileService } from '../service/upload-file.service';
import { DomainService } from '../../shared/domain/domain.service';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { APP_CONSTANTS, IAppConstants } from '../../app.constants';
import { MessageService } from '../../shared/messages/services/message.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-upload-list',
  templateUrl: './upload-list.component.html',
  styleUrls: ['./upload-list.component.css'],
  providers: [UploadFileService]
})
export class UploadListComponent implements OnInit {
  @ViewChild(ModalDirective) modal: ModalDirective;
  loadId: string;
  appraisalList: any[];
  // isRowOpened: boolean[];
  selectedAppraisal: any;
  listSize = 0;
  pageSize = 10;
  page = 1;
  modalSelected = '';
  showLogError: any = false;
  appTypeDomain: any = {};
  scopeTypeDomain: any = {};
  surveyTypeDomain: any = {};
  macroProcOomain: any = {};
  countryDomain: any = {};
  assetCategoryDomain: any = {};
  docCategoryDomain: any = {};
  selectedAsset: any;
  constructor(
    private translateService: TranslateService,
    private messageService: MessageService,
    private activatedRoute: ActivatedRoute,
    private uploadFileService: UploadFileService,
    private domainService: DomainService,
    @Inject(APP_CONSTANTS) public constants: IAppConstants
  ) { }

  ngOnInit() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.loadId = params['loadId'];
      this.retrieveAppraisalList();
    });
    Observable.forkJoin(
      this.domainService.newGetDomain('UBZ_DOM_APPRAISAL_TYPE'),
      this.domainService.newGetDomain('UBZ_DOM_SCOPE_TYPE'),
      this.domainService.newGetDomain('UBZ_DOM_SURVEY_TYPE'),
      this.domainService.newGetDomain('UBZ_DOM_PROCESS_TYPE'),
      this.domainService.newGetDomain('UBZ_DOM_COUNTRY'),
      this.domainService.newGetDomain('UBZ_DOM_REG_CATEGORY_TYPE', '-', true),
      this.domainService.newGetDomain('UBZ_DOM_MALO_DOC_CATEGORY')
    ).subscribe(res => {
      this.appTypeDomain = res[0];
      this.scopeTypeDomain = res[1];
      this.surveyTypeDomain = res[2];
      this.macroProcOomain = res[3];
      this.countryDomain = res[4];
      this.assetCategoryDomain = res[5];
      this.docCategoryDomain = res[6];

    });
  }

  retrieveAppraisalList() {
    this.uploadFileService
      .getUploadDetail(this.loadId, this.page, this.pageSize)
      .subscribe(res => {
        this.appraisalList = res.appraisals;
        this.updateContinueToLoad();
        this.listSize = res.nAppraisals;
        // this.isRowOpened = [];
        // this.appraisalList.forEach(() => {
        //   this.isRowOpened.push(false);
        // });
      });
  }

  updateContinueToLoad() {
    this.appraisalList.forEach(appraisal => {
      let thereAreR = false;
      let thereAreY = false;
      appraisal.documents.forEach(document => {
        if (document.uploadStatus === 'Y') {
          thereAreY = true;
        }
        if (document.uploadStatus === 'R') {
          thereAreR = true;
        }
      });
      appraisal.continueToLoad = thereAreR && thereAreY;
    });
  }

  changePage(event: any) {
    this.page = event.page;
    this.retrieveAppraisalList();
  }

  changePageSize() {
    this.page = 1;
    this.retrieveAppraisalList();
  }

  openAssetModal(appraisal: any) {
    this.modalSelected = 'asset';
    this.selectedAppraisal = appraisal;
    this.modal.show();
  }

  hideAssetModal() {
    this.modal.hide();
  }

  onModalHidden() {
    this.selectedAppraisal = null;
    this.handleLogModal(null);
  }

  openDocumentsModal(appraisal: any) {
    this.modalSelected = 'doc';
    this.selectedAppraisal = appraisal;
    this.modal.show();
  }

  hideDocumentsModal() {
    this.modal.hide();
  }

  handleLogModal(asset: any) {
    this.selectedAsset = asset ? asset : null;
    this.showLogError = asset ? true : false;
  }

  reload(document: any) {
    this.uploadFileService
      .scheduleUpload(this.loadId, document)
      .subscribe(() => {
        this.messageService.showSuccess(
          this.translateService.instant('UBZ.SITE_CONTENT.100111111011'),
          this.translateService.instant('UBZ.SITE_CONTENT.1001100001')
        );
        this.updateSelectedAppraisal();
      });
  }

  updateSelectedAppraisal() {
    this.uploadFileService
      .getUploadDetail(this.loadId, this.page, this.pageSize)
      .subscribe(res => {
        this.appraisalList = res.appraisals;
        this.updateContinueToLoad();
        this.appraisalList.forEach(appr => {
          let isSameAppraisal: boolean =
            appr.appraisalId === this.selectedAppraisal.appraisalId;
          if (isSameAppraisal) {
            this.selectedAppraisal = appr;
          }
        });
      });
  }
}
