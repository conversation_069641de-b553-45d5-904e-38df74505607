<form #f="ngForm" (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
          {{'UBZ.SITE_CONTENT.110101011' | translate }}
          <span class="state" [ngClass]="{'red': f.invalid, 'green': f.valid}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <div class="row form-group">
            <div class="col-sm-4 form-group">
              <label>{{'UBZ.SITE_CONTENT.110101100' | translate }}*</label>
              <app-calendario [(ngModel)]="model.totalCostUpdateDate" [name]="'totalCostUpdateDate'" [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                [required]="true" [maxDate]="today">
              </app-calendario>
            </div>
          </div>
          <div class="row form-group">
            <table class="uc-table">
              <thead>
                <tr>
                  <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.110101101' | translate }}*</th>
                  <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.110101110' | translate }}*</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of model.expenses; let i = index">
                  <td>
                    <input class="form-control" type="text" name="desc-cost-{{i}}" [(ngModel)]="item.description" required="">
                  </td>
                  <td>
                    <input class="form-control" appOnlyNumbers type="text" name="cost-cost-{{i}}" [(ngModel)]="item.amount"
                      required="">
                  </td>
                </tr>
              </tbody>
              <tfoot class="table-foot">
                <tr>
                  <td class="text-center">
                    <strong>{{'UBZ.SITE_CONTENT.101010010' | translate }}</strong>
                  </td>
                  <td class="text-center">
                    <strong>{{ calculateTotalCost() | currency:'EUR':true:'1.2-2' }}</strong>
                  </td>
                </tr>
              </tfoot>
            </table>
            <div class="col-sm-12">
              <ng-container *appAuthKey="'UBZ_QTE_EXPENSE'">
                <button type="button" class="btn btn-empty" (click)="openModal(modalTypes['expense'])">
                  <i class="fa fa-plus"></i> {{'UBZ.SITE_CONTENT.110101111' | translate }}
                </button>
              </ng-container>
            </div>
          </div>
          <div class="row">
            <table class="uc-table">
              <thead>
                <tr>
                  <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.110110000' | translate }}*</th>
                  <th scope="col" class="col-sm-2">€*</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of model.coverages; let i = index">
                  <td>
                    <input class="form-control" type="text" name="desc-cov-{{i}}" [(ngModel)]="item.description" required="">
                  </td>
                  <td>
                    <input class="form-control" appOnlyNumbers type="text" name="cost-cov-{{i}}" [(ngModel)]="item.amount"
                      required="">
                  </td>
                </tr>
              </tbody>
              <tfoot class="table-foot">
                <tr>
                  <td class="text-center">
                    <strong>{{'UBZ.SITE_CONTENT.101010010' | translate }}</strong>
                  </td>
                  <td class="text-center">
                    <strong>{{ calculateTotalCoverage() | currency:'EUR':true:'1.2-2' }}</strong>
                  </td>
                </tr>
              </tfoot>
            </table>
            <div class="col-sm-12">
              <ng-container *appAuthKey="'UBZ_QTE_COVERAGE'">
                <button type="button" class="btn btn-empty" (click)="openModal(modalTypes['coverage'])">
                  <i class="fa fa-plus"></i> {{'UBZ.SITE_CONTENT.110110001' | translate }}
                </button>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </accordion-group>
</form>

<!-- Modal -->
<div *ngIf="modalOpen" class="modal fade" bsModal #modalBox="bs-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
  (onHidden)="hideModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <form #form="ngForm" (ngSubmit)="itemCreated()" novalidate>
        <div class="modal-header">
          <h2 *ngIf="modalType === modalTypes['expense']">{{'UBZ.SITE_CONTENT.110110010' | translate }}</h2>
          <h2 *ngIf="modalType === modalTypes['coverage']">{{'UBZ.SITE_CONTENT.110110011' | translate }}</h2>
          <button type="reset" class="close pull-right" aria-label="Close" (click)="hideModal()">
            <i class="icon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="row form-group">
            <div class="col-md-12">
              <label for="decrizione">
                <span>
                  <i class="icon-search note-tooltip" [tooltip]="createdItem.description" triggers="click"></i>
                </span>
                {{'UBZ.SITE_CONTENT.10010110' | translate }}*
              </label>
              <textarea name="decrizione" class="form-control" [(ngModel)]="createdItem.description" required=""></textarea >
            </div>
          </div>
          <div class="row form-group">
            <div class="col-md-12">
              <label for="amount">{{'UBZ.SITE_CONTENT.110110100' | translate }}*</label>
              <app-importo
                [name] ="'amount'"
                [required] = "false"
                [(ngModel)] = "createdItem.amount">
              </app-importo>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-primary waves-effect" [disabled]="form.invalid || createdItem.amount===0 " type="submit">{{'UBZ.SITE_CONTENT.110110101' | translate }}</button>
        </div>
      </form>
    </div>
  </div>
</div>
