import { Component, OnInit} from '@angular/core';
import { Router } from '@angular/router';
import { HomeService } from './home.service';


@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css'],
  providers: [HomeService]
})
export class HomeComponent implements OnInit {

  constructor(private router: Router, private homeService: HomeService) {}

  ngOnInit() {
       this.homeService
      .getHomePage()
      .subscribe(url => {
        console.log(url)
        this.router.navigateByUrl(url)
      
      });
  
  }

 
  }
