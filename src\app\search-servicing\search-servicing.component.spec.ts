
import { CommonModule } from '@angular/common';
import { ComponentFixture, TestBed, } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Http, HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { CookieModule, CookieService } from 'ngx-cookie';
import { CUSTOM_ELEMENTS_SCHEMA, LOCALE_ID, NO_ERRORS_SCHEMA } from '@angular/core';
import { ToastModule } from 'ng2-toastr/src/toast.module';
import { CookieOptionsProvider } from 'ngx-cookie/src/cookie-options-provider';
import { of } from 'rxjs/observable/of';
import { SearchServicingComponent } from './search-servicing.component';
import { CustomTimePipe } from '../shared/pipes/custom-time/custom-time.pipe';
import { AuthKeyDirective } from '../shared/access-rights/auth-key.directive';
import { SearchPageService } from '../search-page/service/search-page.service';
import { DomainService } from '../shared/domain/domain.service';
import { MenuService } from '../shared/menu/services/menu.service';
import { MessageService } from '../shared/messages/services/message.service';
import { SharedService } from '../shared/services/shared.service';
import { CustomHttpService } from '../shared/http/custom-http.service';
import { ApiService } from '../shared/api/api.service';
import { PositionService } from '../shared/position/position.service';
import { PropertiesService } from '../shared/properties/properties.service';
import { AppConstants, APP_CONSTANTS } from '../app.constants';
import { UserDataService } from '../shared/user-data/user-data.service';
import { ChecklistService } from '../shared/checklist/service/checklist.service';

describe('SearchServicingComponent ', () => {
  let component: SearchServicingComponent;
  let fixture: ComponentFixture<SearchServicingComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RouterTestingModule, FormsModule, ReactiveFormsModule, CommonModule, HttpModule, TranslateModule.forRoot(), CookieModule.forRoot(), ToastModule.forRoot()],
      declarations: [SearchServicingComponent, CustomTimePipe, AuthKeyDirective],
      providers:
        [
          SearchPageService, DomainService, MenuService, MessageService, SharedService, CookieService, CookieOptionsProvider, PropertiesService, ApiService, 
          PositionService, UserDataService, PropertiesService, ChecklistService,
          { provide: Http, useClass: CustomHttpService },
          { provide: APP_CONSTANTS, useValue: AppConstants },
          { provide: LOCALE_ID, useValue: 'it-IT' }
        ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA]

    }).compileComponents()
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SearchServicingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    spyOn(component.domainService, 'getStatusName').and.returnValue(of({ "400SOP-RCH": { "domCode": "400SOP-RCH", "translationCod": null, "translationStatusCod": "UBZ.STATUS_NAME.400SOP-RCH", "translationStatusDescCod": "UBZ.STATUS_DESC.400SOP-RCH" } }));
    spyOn(component.domainService, 'newGetDomain').and.returnValue(of({ "A": { "domCode": "A", "translationCod": "UBZ.CATEGORY_TYPE.A", "translationStatusCod": null, "translationStatusDescCod": "A - Appartamento" } }));
    spyOn(component.searchPageService, 'doNewSearchServicing').and.returnValue(of({ "positions": [{ "id": "1016209", "appraisalId": "1127954", "ndg": "0000000001758977", "heading": " ", "type": null, "status": null, "phase": null, "task": null, "inChargeUser": null, "insertDate": 1231432691384, "updateDate": 0, "deleteDate": 0, "creationUser": "COR00389", "creationBranch": "2401", "taskId": 0, "taskCod": null, "statusApp": "PER-COV", "phaseApp": "600", "macroprocess": "NER", "appraisalType": "PER", "finOperation": null, "oldAppraisal": null, "insertDateApp": 1231369200000, "totalAssets": 13 }], "count": 1 }));
    component.searchForServicing();
  });

  it('should test changePage', () => {
    component.changePage({ page: 2 });
    expect(component.pageNum).toBe(2);
  });

  it('should test changePageSize', () => {
    component.changePageSize();
  });
})






