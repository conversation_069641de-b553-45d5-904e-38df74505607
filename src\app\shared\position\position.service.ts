import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';

import { Observable } from 'rxjs/Observable';
import { AppConstants } from '../../app.constants';
import { Subject } from 'rxjs';
import { FractionedAppraisalResponse } from './models/fractioned-appraisal-response.model';

@Injectable()
export class PositionService {
  // Contiene i queryParams del link esterno utilizzato per accedere all'applicazione da TGP e EMP
  externalLinkParams: Object = {};
  collateralsArray = [];
  isInternalSecondOpinion: boolean;
  // Variabili utilizzate in caso di compilazione perizia tramite API
  messagesArray: string[] = [];
  isApiAppraisal: boolean = false;
  // Observable su i quali i componenti appartenenti alla compilazione della perizia rimangono in "ascolto"
  $appraisalInfoObject: Subject<any> = new Subject<any>();

  constructor(private http: Http) { }

  getPositionDetail(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/position/v1/positions/${positionId}`;
    return this.http.get(url).map((resp: Response) => {
      return resp.json();
    });
  }

  getStatus(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/statusLog/v1/statusLog/${positionId}`;
    return this.http.get(url).map((resp: Response) => resp.json());
  }

  assignOwnerPosition(
    positionId,
    positionType,
    force: boolean
  ): Observable<any> {
    positionId = encodeURIComponent(positionId);
    if (positionType === AppConstants.wizardCodes['PER-COM']) {
      positionType = AppConstants.wizardCodes['REQ'];
    }
    const url = `/UBZ-ESA-RS/service/position/v1/positions/${positionId}/${positionType}/${force}/assignOwnerPosition`;
    return this.http.put(url, {}).map((resp: Response) => resp.json());
  }

  releaseOwnerPosition(positionId, positionType): Observable<any> {
    positionId = encodeURIComponent(positionId);
    positionType = encodeURIComponent(positionType);
    const url = `/UBZ-ESA-RS/service/position/v1/positions/${positionId}/${positionType}/releaseOwnerPosition`;
    return this.http.put(url, {}).map((resp: Response) => resp.json());
  }

  getCollateralData(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = '/UBZ-ESA-RS/service/collateral/v1/collaterals/' + positionId;
    return this.http.get(url).map(res => {
      this.collateralsArray = res.json();
      return this.collateralsArray;
    });
  }

  getCollateralAssetList(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/collateral/v1/appraisal/collateralAssetList/${positionId}`;
    return this.http.get(url).map(res => res.json());
  }

  getAppraisalInfo(
    positionId: string,
    retrieveAllData?: boolean
  ): Observable<any> {
    positionId = encodeURIComponent(positionId);
    let url = `/UBZ-ESA-RS/service/appraisal/v1/appraisals/${positionId}`;
    if (retrieveAllData !== undefined) {
      url = `${url}/${retrieveAllData}`;
    }
    return this.http.get(url).map((resp: Response) => {
      const appraisalInfoObject = resp.json();
      this.$appraisalInfoObject.next(appraisalInfoObject);
      this.isInternalSecondOpinion =
        appraisalInfoObject['opinionType'] === 'SO' &&
        appraisalInfoObject['internalAgent'];
      return appraisalInfoObject;
    });
  }

  public getAppraisalAssignment(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisals/expertsAndSoc/${positionId}`;
    // IL PATH DEL SERVIZIO DOVREBBE CAMBIARE COME SOTTO
    // const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisals/${positionId}/expertsAndSoc`;
    return this.http.get(url).map(res => {
      return res.json();
    });
  }

  public isAppraisalFractioned(appraisalId: string): Observable<FractionedAppraisalResponse> {
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisals/isOriginFra/${appraisalId}`;
    return this.http.get(url).map((resp: Response) => resp.json());
  }

  public consistencyIsMarketValueMandatory(
    appraisalId: string
  ): Observable<any> {
    appraisalId = encodeURIComponent(appraisalId);
    const url =
      '/UBZ-ESA-RS/service/appraisal/v1/' +
      appraisalId +
      '/mandatory/marketValue';
    return this.http.post(url, {}).map(res => res.json());
  }

  public consistencyValidateEvaluation(
    appraisalId: string,
    assetId: string
  ): Observable<any> {
    appraisalId = encodeURIComponent(appraisalId);
    assetId = encodeURIComponent(assetId);
    const url =
      '/UBZ-ESA-RS/service/appraisal/v1/' +
      appraisalId +
      '/' +
      assetId +
      '/validate/evaluation';
    return this.http.post(url, {}).map(res => res.json());
  }

  public consistencyValidateAppraisalEvaluation(
    appraisalId: string
  ): Observable<any> {
    appraisalId = encodeURIComponent(appraisalId);
    const url =
      '/UBZ-ESA-RS/service/appraisal/v1/' +
      appraisalId +
      '/validate/evaluation';
    return this.http.post(url, {}).map(res => res.json());
  }

  public getAppraisalUpdateType(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisalsUpdateType/${positionId}`;
    return this.http.get(url).map(res => {
      return res.json();
    });
  }

  public getAssetsLists(appraisalId: string): Observable<any> {
    appraisalId = encodeURIComponent(appraisalId);
    const url =
      '/UBZ-ESA-RS/service/collateral/v1/appraisal/' +
      appraisalId +
      '/collateral/verify';
    return this.http.post(url, {}).map(res => res.json());
  }

  public saveAssetsWithNewCollateral(
    appraisalId: string,
    input: any
  ): Observable<any> {
    appraisalId = encodeURIComponent(appraisalId);
    const url =
      '/UBZ-ESA-RS/service/collateral/v1/appraisal/' +
      appraisalId +
      '/collateral/save';
    return this.http.post(url, { assets: input }).map(res => res.text());
  }
}
