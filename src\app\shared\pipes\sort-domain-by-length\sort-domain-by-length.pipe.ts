import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'sortDomainByLength'
})
export class SortDomainByLengthPipe implements PipeTransform {

  transform(value: any, args?: any): any {
    var maximumLength = 0;
    var oneLenght = 1;
    var twoLenght = 2;
    var options = [];
    var firstOptions = [];
    var lastOptions = [];

    for (const key in value) {
      if (value[key].domCode.length > maximumLength) {
        maximumLength = value[key].domCode.length;
      }
    }

    for (const key in value) {

      if (value[key].domCode.length === twoLenght) {
        firstOptions.push(value[key]);
      }

      if (value[key].domCode.length === oneLenght) {
        options.push(value[key]);
      }

      if (value[key].domCode.length === maximumLength) {
        lastOptions.push(value[key]);
      }
}
    const orderedValue = firstOptions.reverse().concat(options, lastOptions.reverse()).filter((v, i, a) => a.indexOf(v) === i);
    return orderedValue;
  }
}