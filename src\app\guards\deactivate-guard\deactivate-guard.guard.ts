import { Injectable } from '@angular/core';
import {
  CanDeactivate,
  Router
} from '@angular/router';
import { Observable } from 'rxjs/Observable';

export interface CanComponentDeactive {
  canDeactivate: () => Observable<boolean> | Promise<boolean> | boolean;
}

/**
 * @class CanDeactivateGuard
 * @description Guardia per il deactivate dei componenti
 * Se dichiarata nella rotta di un componenten bisogna implementare il metodo canDeactivate all'interno di questo
 * e definire all'interno la logica per la quale il componente non deve essere dismesso
 */
@Injectable()
export class DeactivateGuard implements CanDeactivate<CanComponentDeactive> {
  constructor(private router: Router) { }

  canDeactivate(component: CanComponentDeactive): boolean {
    if (component.canDeactivate) {
      if (component.canDeactivate()) {
        return true;
      } else {
        window.history.pushState({}, '', this.router.url);
      }
    }
  }

}