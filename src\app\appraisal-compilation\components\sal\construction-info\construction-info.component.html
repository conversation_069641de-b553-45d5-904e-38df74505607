<form #f="ngForm" (click)="_accordionAPFService.handleOpenProperty(group, f)">
<accordion-group #group class="panel">
  <div accordion-heading class="acc-note-headline" role="tab">
    <h4 class="panel-title">
      <a role="button">
        <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
        {{'UBZ.SITE_CONTENT.1010000001' | translate }}
        <span class="state" [ngClass]="{'red': f.invalid, 'green': f.valid}"></span>
      </a>
    </h4>
  </div>
  <div class="panel-collapse collapse in" role="tabpanel">
    <div class="panel-body">
      <div class="panel-box">
        <div class="row" *ngIf="!constructionNote">
          <div class="col-sm-12">
            <ng-container *appAuthKey="'UBZ_CONSTRUCTION.INFO_ADD_NOTE'">
              <button type="button" class="btn btn-empty" (click)="openAddModal()">
                <i class="fa fa-plus"></i>
                <span>{{'UBZ.SITE_CONTENT.10000010' | translate }}</span>
              </button>
            </ng-container>
          </div>
        </div>
        <div *ngIf="constructionNote">
          {{ constructionNote }}
          <i class="fa fa-pencil-square-o pull-right" aria-hidden="true" (click)="modifyNote()"></i>        
        </div>
      </div>
    </div>
  </div>
</accordion-group>
</form>

<div *ngIf="modalOpen" class="modal fade" id="aggiungi-impianto" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #addInstallation="bs-modal" (onHidden)="closeModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.1000010010' | translate }}</h2>
        <button type="button" class="close pull-right" aria-label="Close" (click)="closeModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">                                    
        <label>{{'UBZ.SITE_CONTENT.1010000010' | translate }}</label>
        <textarea name="info_cantiere" class="form-control" [(ngModel)]="newNote"></textarea>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary waves-effect" (click)="addNote(newNote)">
          {{'UBZ.SITE_CONTENT.10000010' | translate | uppercase }}
        </button>
      </div>
    </div>
  </div>
</div>
