import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';

import { SearchService } from '../../shared/search/search.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css'],
  providers: [SearchService]
})
export class DashboardComponent implements OnInit {
  dashboardType: any;
  activeTabs = [true, false];
  activeClassMap = { true: 'active', false: '' };

  constructor(private activatedRoute: ActivatedRoute) {}

  ngOnInit() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.dashboardType = params['dashboardType'];
    });
  }
}
