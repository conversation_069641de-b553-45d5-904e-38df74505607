import {
  Component,
  OnInit,
  Input,
  Output,
  ViewChild,
  Inject,
  EventEmitter
} from '@angular/core';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { PositionService } from '../position.service';
import { PositionObject } from '../position-object';
import { DomainService } from '../../domain/domain.service';
import { Domain } from '../../domain/domain';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';
import { MessageService } from '../../messages/services/message.service';
import { TranslateService } from '@ngx-translate/core';
import { UserDataService } from '../../user-data/user-data.service';
import { UserData } from '../../user-data/user-data';

@Component({
  selector: 'app-position-header',
  templateUrl: './position-header.component.html',
  styleUrls: ['./position-header.component.css']
})
export class PositionHeaderComponent implements OnInit {
  @ViewChild('stateBox') public stateBox: ModalDirective;
  @ViewChild('collateralsBox') public collateralsBox: ModalDirective = null;
  @ViewChild('lockFraBox') public lockFraBox: ModalDirective = null;
  @Input() positionId: string;
  @Input() wizardCode: string;
  @Input() lockingUser: string;
  @Input() familyAsset: string = '';  
  @Input() avoidLocking = false;
  @Input() staticHeaderArray: Object[] = new Array();
  @Output() positionLocked = new EventEmitter();
  @Output() positionUnlocked = new EventEmitter();
  @Output() positionLockedFrg = new EventEmitter();
  locked = false;
  position: any = {};
  positionObjects: PositionObject[] = [];
  positionsType: String[] = [];
  positionsStatusNames: String[] = [];
  statusDomain: Domain[];
  collaterals: any[] = [];
  appraisalCompany: string;
  isSimulationFinished = false;
  isFrazionamentoHeader = false;
  lockClass = { false: 'fa fa-lock', true: 'fa fa-unlock-alt' };
  lockModalIsOpen = false;
  lockModalOpType: string;
  currentUser='';
 
  constructor(
    public positionService: PositionService,
    private domainService: DomainService,
    private userDataService: UserDataService,
    private messageService: MessageService,
    private translateService: TranslateService,
    @Inject(APP_CONSTANTS) public constants: IAppConstants
  ) { }

  ngOnInit() {
    // Se staticHeaderArray è settato l'header è utilizzato all'interno di wizard statici come frazionamento
    // o sostituzioneGaranzia
    if (!this.staticHeaderArray.length) {
      this.getPosition(true);
      this.getStatus();
    } else {
   this.userDataService.getAll().subscribe((res:UserData)=>{
    this.currentUser=res.username.toUpperCase()
    
   })
 
      this.staticHeaderArray.forEach(elem => {
        if (elem['label'] === 'type' && elem['value'] === 'FRA') {
          this.isFrazionamentoHeader = true;
          if (!this.lockingUser) {
            this.positionLockedFrg.emit();
          }
           else if (this.currentUser !== this.lockingUser.toUpperCase()) {
            const msg = this.translateService.instant(
              'UB1.TASKACCESSRIGHTS.DIRECTIVE.ERROR.ATTIVITAUTENTE'
            );
            this.messageService.showInfo(
              `${msg}${this.lockingUser.toUpperCase()}`,
              this.translateService.instant('UB1.TITLE.INFO')
            );
          }
        }
      });      
    }
    this.checkExternalLinkParams();
  }
  
  // Controlla se l'applicazione è stata ingaggiata tramite link esterno per dettaglioStatoPerizia
  // Apre modal dettaglio stato e ne blocca la visualizzazione
  checkExternalLinkParams() {
    if (this.positionService.externalLinkParams['method'] === 'dettaglioStatoPerizia') {
      this.stateBox.config = {
        backdrop: 'static',
        keyboard: false
      };
      this.showStateBoxDialog();
    }
  }

  refreshData() {
    this.getPosition(false);
    this.getStatus();
  }

  getPosition(firstTime: boolean) {
    if (this.positionId) {
      this.positionService.getPositionDetail(this.positionId).subscribe(pos => {
        this.position = pos;
        if (this.position.phaseCode === '100' && this.position.statusCode === 'SIM-CON') {
          this.isSimulationFinished = true;
        };
        if (
          this.wizardCode !== this.constants.wizardCodes['PER-COM'] &&
          !this.avoidLocking && firstTime && !this.isSimulationFinished
        ) {
          this.lockAppraisal(false);
        }
        this.getCollaterals();
        if (this.wizardCode === this.constants.wizardCodes.PER) {
          this.getAppraisalCompany();
        }
      });
    }
  }

  private getCollaterals() {
    if (this.wizardCode !== this.constants.wizardCodes['PER']) {
      this.positionService
        .getCollateralData(this.positionId)
        .subscribe(x => (this.collaterals = x));
    } else {
      // Se è una compilazione perizia entra qui
      this.positionService
        .getAppraisalInfo(this.positionId)
        .subscribe(x => (this.collaterals = x.lstJoint));
    }
  }

  private getAppraisalCompany() {
    this.positionService
      .getAppraisalAssignment(this.positionId)
      .subscribe(x => (this.appraisalCompany = x.socHeading));
  }

  getCollateralIds() {
    let retStr: string;
    if (this.wizardCode === this.constants.wizardCodes['PER']) {
      retStr = this.parseForPer();
    } else {
      retStr = this.parseForSim();
    }
    return retStr;
  }

  private parseForPer(): string {
    let retStr = '';
    let charCounter = 0;
    for (const col of this.collaterals) {
      const elLength = col.jointCod.length;
      if (elLength + charCounter <= 7) {
        if (charCounter !== 0) {
          retStr += ',';
        }
        retStr += col.jointCod;
        charCounter += elLength;
      } else if (elLength <= 7) {
        const subIndex = 6 - charCounter;
        retStr += ',' + col.jointCod.substring(0, subIndex) + '...';
        break;
      }
    }
    return retStr;
  }

  private parseForSim(): string {
    let retStr = '';
    let charCounter = 0;
    for (const col of this.collaterals) {
      const elLength = col.progCollateral.length;
      if (elLength + charCounter <= 7) {
        if (charCounter !== 0) {
          retStr += ',';
        }
        retStr += col.progCollateral;
        charCounter += elLength;
      } else if (elLength <= 7) {
        const subIndex = 6 - charCounter;
        retStr += ',' + col.progCollateral.substring(0, subIndex) + '...';
        break;
      }
    }
    return retStr;
  }

  showStateBoxDialog(): void {
    this.stateBox.show()
  }

  showCollateralsBoxDialog(): void {
    this.collateralsBox.show();
  }

  hideStateBoxDialog(): void {
    this.stateBox.hide();
  }

  hideCollateralsBoxDialog(): void {
    this.collateralsBox.hide();
  }

  getStatus() {
    this.positionService.getStatus(this.positionId).subscribe(x => {
      this.positionObjects = x;
      this.domainService
        .newGetDomain('UBZ_DOM_POSITION_TYPE')
        .subscribe(ret => {
          this.statusDomain = ret;
        });
        this.domainService.getStatusName().subscribe(res => {
        this.positionsStatusNames = res;
      });
    });
  }

  getPositionStatusName(phaseCode: string, statusCode: string): string {
    if (this.positionsStatusNames[phaseCode + statusCode]) {
      return this.positionsStatusNames[phaseCode + statusCode]
        .translationStatusCod;
    }
    return '--';
  }

  clickOnLock() {
    if (this.locked) {
      this.lockModalOpType = this.constants['positionHeaderStatus']['REV']; // Revoca della pratica e liberazione di essa
    } else {
      this.lockModalOpType = this.constants['positionHeaderStatus']['PRE']; // Presa in carico
    }
    this.lockModalIsOpen = true;
  }

  clickOnLockFrazionamento() {
    this.showLockFraBox();
  }

  private lockAppraisal(force: boolean) {
    this.positionService
      .assignOwnerPosition(this.position.id, this.position.productType, force)
      .subscribe(x => {
        if (x) {
          this.locked = true;
        this.userDataService.getAll().subscribe((res:UserData)=>{
          this.position.userInCharge=res.username
      
        })
          this.positionLocked.emit();
        } else {
          this.locked = false;
          this.positionUnlocked.emit();
        }
      });
  }

  modalClose() {
    this.lockModalIsOpen = false;
    this.refreshData();
  }
  
  hideLockFraBox():void {
    this.lockFraBox.hide();
  }
  
  showLockFraBox(): void {
    this.lockFraBox.show();
  }

  submitLockFraModal() {
    if (this.currentUser !== this.lockingUser.toUpperCase()) {
      this.positionLockedFrg.emit();
      this.lockFraBox.hide();
    }  
  }
}
