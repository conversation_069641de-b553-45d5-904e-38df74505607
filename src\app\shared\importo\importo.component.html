<form #f="ngForm" novalidate>
  <input
    type="text"
    class="form-control" 
    data-toggle="tooltip" 
    #valueModel="ngModel"
    [textMask] = "{mask: numericMaskConfig}" 
    [name]="name" 
    [ngClass] = "ngClassCondition ? ngClassAdd : ''"
    [required] = "required"
    [(ngModel)] = "formattedValue"
    [disabled] = "disabled"
    [title] = "title"
    [maxlength] = "maxlength"/>
</form>