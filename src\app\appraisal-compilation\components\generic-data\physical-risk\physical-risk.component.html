<form #f="ngForm" (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
          <span> {{'UBZ.SITE_CONTENT.11111111101'| translate }}</span>
          <span class="state" [ngClass]="{green:validForm, red:!validForm}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <table class="uc-table uc-multirow">
          <thead>
            <tr>
              <th scope="col" class="col-sm-3" rowspan="2" id="mycol">{{'UBZ.SITE_CONTENT.11111111110'| translate}}</th>
              <th scope="col" class="col-sm-5" [attr.colspan]="total">{{'UBZ.SITE_CONTENT.11111111111'| translate}}</th>
            </tr>
            <tr>
              <th *ngFor="let head of allLivello" id="mycol"><label>{{head.translationCod| translate}}</label></th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let title of  allRischio">
              <td id="txt">{{title.translationCod | translate}}</td>
              <td *ngFor="let head of  allLivello">
                <input type="radio" [name]="title.domCode" [value]="head.domCode" id="title + head"
                  (change)="getData(title.domCode,head.domCode)" [(ngModel)]="allResponseData[title.domCode]" required>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
</accordion-group>
</form>