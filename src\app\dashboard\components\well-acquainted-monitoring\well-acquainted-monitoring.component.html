<app-counter [inputParam]="counterInput" (outputEvent)="changeCounter($event)"></app-counter>
<table class="table table-hover">
  <thead>
    <tr>
      <th scope="col">{{'UBZ.SITE_CONTENT.1101100' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.1001110' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.1101101' | translate }}</th>
      <!-- <th *ngIf="searchTypes === searchService.TYPE.SIM.STRUCT" scope="col" scope="col">{{'UBZ.SITE_CONTENT.1111100' | translate }}</th> -->
      <th scope="col">{{'UBZ.SITE_CONTENT.1111101' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.1111110' | translate }} <i class="icon-sort"></i></th>
      <th scope="col"></th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let position of positionListResults?.positions">
      <td data-label="ID">{{position.id}}</td>
      <td data-label="ndg">{{position.ndg}}</td>
      <td data-label="heading">{{position.heading}}</td>
      <!-- <td *ngIf="searchTypes === searchService.TYPE.SIM.STRUCT" data-label="user">{{position.updateUser}}</td> -->
      <td data-label="branch">{{position.creationBranch}}</td>
      <td data-label="insertDate">{{position.insertDate | date: 'dd-MM-yyyy'}}</td>
      <td data-label><a href=""><i class="icon-angle-double-right"></i></a></td>
    </tr>
  </tbody>
</table>

<div class="row">
  <div *ngIf="positionListResults?.count > 10" class="col-sm-6">
    <div class="results">
      <span>{{'UBZ.SITE_CONTENT.1111111' | translate }}</span>
      <div class="custom-select">
        <select class="form-control" [(ngModel)]="pageSize" (change)="changePageSize()">
          <option value="10" selected>10 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{positionListResults.count}}</option>
          <option *ngIf="positionListResults?.count > 20" value="20">20 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{positionListResults.count}}</option>
          <option *ngIf="positionListResults?.count > 30" value="30">30 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{positionListResults.count}}</option>
          <option *ngIf="positionListResults?.count > 40" value="40">40 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{positionListResults.count}}</option>
          <option *ngIf="positionListResults?.count > 50" value="50">50 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{positionListResults.count}}</option>
          <option *ngIf="positionListResults?.count <= 50" [ngValue]="positionListResults.count"> {{'UBZ.SITE_CONTENT.10000000' | translate }} {{positionListResults.count}}</option>
        </select>
      </div>
    </div>
  </div>
  <div class="col-sm-6" class="pull-right">
    <pagination [boundaryLinks]="true" [directionLinks]="false" [totalItems]="positionListResults?.count" [(ngModel)]="page" [itemsPerPage]="pageSize"
      (pageChanged)="changePage($event)" class="pagination" previousText="&lsaquo;" nextText="&rsaquo;" firstText="&laquo;" lastText="&raquo;"></pagination>
  </div>
</div>
