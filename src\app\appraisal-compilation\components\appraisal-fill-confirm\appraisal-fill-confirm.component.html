<div class="row">
  <div class="col-sm-12">
    <div class="success-confirmation">
      <!-- Sezione riepilogo perizieAPI -->
      <ng-container *ngIf="isApiAppraisal">
        <!-- PeriziaAPI convalidata -->
        <ng-container *ngIf="validatedApiAppraisal">
          <h3>
            <i class="icon-check"></i>
            {{'UBZ.SITE_CONTENT.10011100000' | translate }}</h3>
          <p>{{'UBZ.SITE_CONTENT.10011100001' | translate }}.</p>
        </ng-container>
        <!-- PeriziaAPI non convalidata -->
        <ng-container *ngIf="!validatedApiAppraisal">
          <h3>
            <i class="icon-check"></i>
            {{'UBZ.SITE_CONTENT.10010101010' | translate }}</h3>
          <p>{{'UBZ.SITE_CONTENT.10010101011' | translate }}.</p>
        </ng-container>
      </ng-container>

      <!-- Sezione riepilogo perizie applicativo -->
      <ng-container *ngIf="!isApiAppraisal">
        <h3>
          <i class="icon-check"></i>
          {{'UBZ.SITE_CONTENT.101100011' | translate }}</h3>
        <p>{{'UBZ.SITE_CONTENT.101100100' | translate }}. {{'UBZ.SITE_CONTENT.101100101' | translate }}.</p>
        <ng-container *appAuthKey="'UBZ_APPRAISAL.FILL.CONFIRM_CONFIRM'">
          <button type="button" class="btn btn-primary waves-effect" (click)="confirmPress()" [disabled]="!_landingService.positionLocked">{{'UBZ.SITE_CONTENT.11100111' | translate }}</button>
        </ng-container>
      </ng-container>
    </div>
  </div>
</div>
