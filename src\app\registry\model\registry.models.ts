export const EXPERT_APPRAISAL_TYPES = {
  REVOCATED: 'REVOCATED',
  COMPLETED: 'COMPLETED',
  IN_CHARGE: 'IN_CHARGE'
};

export const SECTIONS = {
  SOC: 'SOCIETA_PERITALI',
  INT: 'INTERNI',
  BEN: 'BENEVISI'
};

export class ExpertFirm {
  idAnag: number;
  heading: string;
  address: string;
  city = '';
  postalCode: string;
  phoneNum: string;
  mobileNumber: string;
  expertType: string;
  geoComp: string;
  ndg: string;
  email: string;
  startAbilitation: Date;
  endAbilitation: Date;
  registerDate: Date;
  tipAlb: number;
  registerData: RegisterData;
  annualGratuitPerc: number;
  insurancePolicyList: InsurancePolicy[] = [];
  ratingHistoryList: RatingHistory[] = [];
  expertList: AssociatedExpert[] = [];
  ratingExpert: number;
  contractId: string;
  iban: string;
  userId: string;
}

export class AssociatedExpert {
  firstName: string;
  lastName: string;
  idAnag: string;
  role = '';
  identificationNum: string;
  ndg: string;
  status = '';
}

export class RegisterData {
  registrationData: Date;
  numberRegistration: string;
  registerType: string;
  registerBoard: string;
  registerBoardDate: Date;
}
export class InsurancePolicy {
  policyName: string;
  policyMaxPrice: number;
  endPolicy: number;
  policyId: number;
}
export class RatingHistory {
  ratingExpert: number;
  insertDate: Date;
  userCreated: string;
}

export class IdentityDocument {
  idAnag: string;
  ndg: string;
  documentType: string;
  documentNumber: string;
  releaseCity: string;
  releaseDate: Date;
  expiryDate: Date;
}

export class JudicialRecord {
  numJudReg: string;
  releaseDate: any;
  expiryDate: any;
}

export class CompetencePronvince {
  provinceName = '';
}

export class Certificate {
  nameCertification = '';
}

export class ExpertAppraisal {
  appraisalId: string;
  statusCode: string;
  phaseCode: string;
  ndg: string;
  heading: string;
  insertDate: Date;
  branch: string;
}
