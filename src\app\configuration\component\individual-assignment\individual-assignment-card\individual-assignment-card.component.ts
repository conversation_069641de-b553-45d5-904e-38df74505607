import { Component, OnInit, Output, EventEmitter, Input } from '@angular/core';
import { ConfigurationService } from '../../../service/configuration.service';
import { SocietyConf } from '../../../configuration.models';
import { FormGroup } from '@angular/forms';

@Component({
  selector: 'app-individual-assignment-card',
  templateUrl: './individual-assignment-card.component.html',
  styleUrls: ['./individual-assignment-card.component.css'],
})
export class IndividualAssignmentCardComponent implements OnInit {
  @Input() prioritiesList;
  @Input() singleSociety: FormGroup;
  @Output() deleteRefresh = new EventEmitter();
  societyToDeleteList: SocietyConf[] = [];
  deleteModalIsOpen: boolean = false;
  societyHeading: string;

  constructor(private _configurationService: ConfigurationService) {}

  ngOnInit() {
    this.societyHeading = this.singleSociety.controls.heading.value;
  }

  submitDeleteModal(societyToDelete) {
    this.societyToDeleteList.push(societyToDelete._value);
    this._configurationService
      .deleteSociety(this.societyToDeleteList)
      .subscribe(() => {
        this.deleteRefresh.emit(true);
      });
  }
}
