import {
  Component,
  OnChanges,
  Input,
  <PERSON><PERSON><PERSON>roy,
  SimpleChanges
} from '@angular/core';
import { Subscription } from 'rxjs/Subscription';
import { DomainService } from '../../../../../shared/domain/domain.service';
import { RegistryService } from '../../../../service/registry.service';
import { Certificate } from '../../../../model/registry.models';

@Component({
  selector: 'app-expert-certification',
  templateUrl: './expert-certification.component.html',
  styleUrls: ['./expert-certification.component.css']
})
export class ExpertCertificationComponent implements OnChanges, OnDestroy {
  @Input() anagId: string;
  public modify = false;
  public certificates: Certificate[] = [];
  public aCertificateExists: boolean;
  public certificatesDomain: any[] = [];
  private _subscriptions: Subscription[] = [];

  constructor(
    private _registryService: RegistryService,
    private _domainService: DomainService
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes && this.anagId) {
      this._subscriptions[1] = this._domainService
        .newGetDomain('UBZ_DOM_CERTIFICATE_VALUE')
        .subscribe(res => {
          this.certificatesDomain = res;
          this.refreshCertificates();
        });
    }
  }

  ngOnDestroy() {
    for (const subscription of this._subscriptions) {
      if (subscription && !subscription.closed) {
        subscription.unsubscribe();
      }
    }
  }

  private refreshCertificates(addIfEmpty?: boolean) {
    addIfEmpty = addIfEmpty || false;
    this._subscriptions[0] = this._registryService
      .getCertifications(this.anagId)
      .subscribe(res => {
        this.certificates = res;
        if (addIfEmpty && this.certificates.length === 0) {
          this.addNewCertificate();
        }
        this.aCertificateExists = this.certificates.length > 0 ? true : false;
      });
  }

  public cancelModify(event: any): void {
    event.stopPropagation();
    this.modify = false;
  }

  public saveData(event: any): void {
    event.stopPropagation();
    this._registryService
      .saveCertifications(this.anagId, this.certificates)
      .subscribe(res => {
        this.modify = false;
      });
  }

  public startModify(event: any): void {
    event.stopPropagation();
    this.refreshCertificates(true);
    this.modify = true;
  }

  public addNewCertificate(): void {
    this.certificates.push(new Certificate());
  }

  public modifyPressed(event: any): void {
    event.stopPropagation();
    this.refreshCertificates(false);
    this.modify = true;
  }
}
