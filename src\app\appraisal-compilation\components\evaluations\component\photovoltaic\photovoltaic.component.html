<form #f="ngForm" novalidate (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
          {{'UBZ.SITE_CONTENT.1001100110' | translate }}
          <span class="state" [ngClass]="{green: f.valid, red: f.invalid}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <table class="uc-table">
          <thead>
            <tr>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1001001100' | translate }}</th>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.111001011' | translate }}</th>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1001001101' | translate }}</th>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1001001110' | translate }}</th>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1001001111' | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>{{ 'n. ' + model.fotoSystems + ' impianti' }}</td>
              <td>{{ model.systemMeasurement }}</td>
              <td><input appOnlyNumbers class="form-control" type="text" name="quantity0" [(ngModel)]="model.fotoSystems" required="" (change)="calculateTotal()"></td>
              <td>
                <app-importo               
                  [name] ="'prudentialUnitVal0'"
                  [required] = "false"
                  [(ngModel)] = "model.prudentialUnitValue"
                  (ngModelChange) = "calculateTotal()"> 
                </app-importo>
              </td>
              <td>{{model.fotoSystems * model.prudentialUnitValue | currency:'EUR':true:'1.2-2' }}</td>
            </tr>
            <tr>
              <td>{{'UBZ.SITE_CONTENT.1001010001' | translate }}</td>
              <td></td>
              <td></td>
              <td></td>
              <td>
                <input 
                currencyMask 
                [options] = "{
                  align: 'left',
                  allowNegative: false,
                  precision: 0,
                  thousands: '.'
                }" 
                readonly 
                disabled 
                class="form-control" 
                type="text" 
                name="prudentialVal2" 
                [(ngModel)]="model.roundPrudentialValue" 
                required="">
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </accordion-group>
</form>
