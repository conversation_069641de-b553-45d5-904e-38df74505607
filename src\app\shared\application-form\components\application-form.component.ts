import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  OnChanges,
  SimpleChanges,
  ChangeDetectorRef,
  Inject,
  OnDestroy
} from '@angular/core';
import { NgForm, ValidatorFn, AbstractControl } from '@angular/forms';
import { Observable } from 'rxjs/Observable';

// External Module
import { NguiDatetime } from '@ngui/datetime-picker';

// Models
import {
  ApplicationForm,
  Section,
  Row,
  Field
} from '../model';
import { Domain } from '../../domain';
import { IAppConstants, APP_CONSTANTS } from '../../../app.constants';

// Services
import { ApplicationFormService } from '../services';
import { DomainService, DomainTable } from '../../domain';
import { Subject } from 'rxjs';
import { SharedService } from '../../services/shared.service';


NguiDatetime.formatDate = (date: Date): string => {
  let day = date.getDate().toString();
  let month = (date.getMonth() + 1).toString();
  const year = date.getFullYear().toString();

  if (day.length === 1) {
    day = `0${day}`;
  }
  if (month.length === 1) {
    month = `0${month}`;
  }
  return `${day}-${month}-${year}`;
};
NguiDatetime.parseDate = (str: any): Date => {
  const day = parseInt(str.substring(0, 2), 10);
  const month = parseInt(str.substring(3, 5), 10) - 1;
  const year = parseInt(str.substring(6), 10);
  return new Date(year, month, day);
};
NguiDatetime.firstDayOfWeek = 1;

export class OperationObject {
  date: Date;
  fieldName: string;
  operator: string;
}

@Component({
  selector: 'app-application-form',
  templateUrl: './application-form.component.html',
  styleUrls: ['./application-form.component.css'],
})
export class ApplicationFormComponent implements OnInit, OnChanges, OnDestroy {
  @Input() idCode: string;
  @Input() ndg: string;
  @Input() page: string;
  @Input() drivers: Object;
  @Input() setReadOnly: boolean;
  @Input() positionId: string;
  @Input() overwriteAPFData: boolean; // settato a true solo quando si vuol permettere la sovrascrittura dei dati
  @Input() formDisabled: boolean; // forza il disabled sui campi del form, utilizzato su textarea per bloccarlo ma permetterne la lettura


  @Output() formSubmit = new EventEmitter();
  @Output() formLoaded = new EventEmitter<boolean>();

  @ViewChild('appForm') appForm: NgForm;

  applicationForms: ApplicationForm[] = [];
  model: Object = {};
  domainTables: DomainTable[] = [];
  expiredDomain: any[] = [];
  expiredRegDomain: any[] = [];
  private readonly MIN_DATE_TYPE = 'MIN_DATE';
  private readonly MAX_DATE_TYPE = 'MAX_DATE';
  public minDate: Date = new Date(1850, 1, 1);
  public maxDate: Date = new Date(3000, 12, 31);
  public NOW: Date = new Date();
  validationForm: any[] = [];
  condition: boolean;

  // Contiene tutte le stringhe dei domini da recuperare per l'application form che si sta esaminando
  domainsStringArray: Object[] = new Array();

  destroyed$: Subject<void> = new Subject<void>();

  constructor(
    protected appFormService: ApplicationFormService,
    protected domainService: DomainService,
    protected sharedService: SharedService,
    protected _cdRef: ChangeDetectorRef,
    @Inject(APP_CONSTANTS) protected constants: IAppConstants
  ) { }

  ngOnInit() {
    this.retrieveApplicationForm();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (
      (changes['idCode'] &&
        changes['idCode'].currentValue !== changes['idCode'].previousValue &&
        !changes['idCode'].isFirstChange()) ||
      (changes['positionId'] &&
        changes['positionId'].currentValue !==
        changes['positionId'].previousValue &&
        !changes['positionId'].isFirstChange()) ||
      (changes['page'] &&
        changes['page'].currentValue !== changes['page'].previousValue &&
        !changes['page'].isFirstChange())
    ) {
      this.retrieveApplicationForm();
    }
    const newModel = { ...this.model };
    this.model = newModel;

    this.deleteCategoryIfExpired();
  }


  // delete categoria asset if is expired and the page is compilazione richiesta(case when readOnly is false)
  deleteCategoryIfExpired() {
    let activeDomain: any[] = [];
    for (const form of this.applicationForms) {
      for (const section of form.sections) {
        for (const row of section.rows) {
          for (const field of row.fields) {
            if (field.domainCod === 'UBZ_DOM_CATEGORY_TYPE' && !this.setReadOnly && this.page === 'INFO_ASSET_RIC') {
              this.domainService.transform(this.domainTables[field.domainCod].domains).forEach(cat => {
                activeDomain.push(cat.domCode);
              });
              if (activeDomain.indexOf(this.model[section.formCode][field.code]) === -1) {
                this.changeModelRefBySectionAndField(section.formCode, field.code, '');
              }
            }
          }
        }
      }
    }
  }



  public retrieveApplicationForm() {
    this.appFormService
      .retrieveApplicationForm(
        this.idCode,
        this.ndg,
        this.page,
        this.drivers,
        this.positionId
      ).takeUntil(this.destroyed$)
      .subscribe((res: any) => {
        const applicationForms = res.afl;
        for (const i in applicationForms) {
          if (applicationForms.hasOwnProperty(i)) {
            const appForm: ApplicationForm = applicationForms[i];

            for (const j in appForm.sections) {
              if (appForm.sections.hasOwnProperty(j)) {
                const sect: Section = appForm.sections[j];
                this.changeModelRef(sect.formCode, {});

                const formattedRows: Row[] = [];
                for (const k in sect.rows) {
                  if (sect.rows.hasOwnProperty(k)) {
                    const row: Row = sect.rows[k];

                    let span = 0;
                    let formattedRow: Row = new Row();
                    for (const l in row.fields) {

                      if (row.fields.hasOwnProperty(l)) {
                        const field: Field = row.fields[l];
                        if (field.domainCod && field.type === 'Text') {

                          let parentValue = '-';
                          if (field.parentFieldValue) {
                            parentValue = field.parentFieldValue;
                          } else if (
                            field.parentFieldCod &&
                            this.model[sect.formCode][field.parentFieldCod]
                          ) {
                            parentValue = this.model[sect.formCode][
                              field.parentFieldCod
                            ];
                          }
                          if (!field.fieldValue) {
                            if (field.value) {
                              field.fieldValue = field.value;
                            } else {
                              field.fieldValue = '';
                            }
                          }
                          this.addDomainString(field, parentValue);

                          // FIXME - TOGLIERE SE riscrittura tramite
                          //  addDomainString(), getAllDomainRequest() e sendAllDomainRequest() funziona correttamente
                          // if (field.domainCod === 'UBZ_CON_CITY') {
                          //   this.domainService
                          //     .getDomainCityMap(parentValue)
                          //     .subscribe(x => {
                          //       this.domainTables[
                          //         field.domainCod
                          //       ] = new DomainTable();
                          //       this.domainTables[field.domainCod].domains = x;
                          //     });
                          // } else {
                          //   this.domainService
                          //     .newGetDomain(field.domainCod, parentValue)
                          //     .subscribe(x => {
                          //       this.domainTables[
                          //         field.domainCod
                          //       ] = new DomainTable();
                          //       this.domainTables[field.domainCod].domains = x;
                          //     });
                          // }
                        }
                        if (field.type === 'Checkbox' && !field.fieldValue) {
                          if (field.value) {
                            field.fieldValue = field.value;
                          } else {
                            field.fieldValue = false;
                          }
                        }
                        if (field.type === 'Timestamp') {
                          if (field.fieldValue) {
                            this.changeModelRefBySectionAndField(sect.formCode, field.code, new Date(field.fieldValue));
                          } else {
                            if (field.value) {
                              this.changeModelRefBySectionAndField(sect.formCode, field.code, new Date(field.value));
                            } else {
                              this.changeModelRefBySectionAndField(sect.formCode, field.code, null);
                            }
                          }
                        } else {
                          this.changeModelRefBySectionAndField(sect.formCode, field.code, field.fieldValue);
                        }
                        span = span + field.span;
                        if (span <= 12) {
                          formattedRow.fields.push(field);
                        } else {
                          formattedRows.push(Object.assign({}, formattedRow));
                          formattedRow = new Row();
                          formattedRow.fields.push(field);
                          span = field.span;
                        }
                      }
                    }
                    formattedRows.push(Object.assign({}, formattedRow));
                  }
                }
                sect.rows = formattedRows;
              }
            }
          }
        }

        this.domainsStringArray = this.sortDomains(this.domainsStringArray);
        this.getAllDomainRequest();
        this.applicationForms = applicationForms;
        this.applyValidationRules();


        if (this.overwriteAPFData && res.partialSaveData) {
          const tempData: any = JSON.parse(res.partialSaveData);
          for (const apfId in this.model) {
            if (this.model.hasOwnProperty(apfId) && tempData[apfId]) {
              this.changeModelRef(apfId, tempData[apfId]);

              for (const i in applicationForms) {
                if (applicationForms.hasOwnProperty(i)) {
                  const appForm: ApplicationForm = applicationForms[i];
                  if (appForm.applicationForm === apfId) {
                    for (const j in appForm.sections) {
                      if (appForm.sections.hasOwnProperty(j)) {
                        const sect: Section = appForm.sections[j];
                        for (const k in sect.rows) {
                          if (sect.rows.hasOwnProperty(k)) {
                            const row: Row = sect.rows[k];
                            for (const l in row.fields) {
                              if (row.fields.hasOwnProperty(l)) {
                                const field: Field = row.fields[l];
                                if (field.type === 'Timestamp') {
                                  if (this.model[apfId][field.code]) {
                                    this.changeModelRefBySectionAndField(apfId, field.code, new Date(this.model[apfId][field.code]));
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
        this.formLoaded.emit(true);
      });


  }

  private changeModelRef(index: any, value: any) {
    const newModel = this.model;
    newModel[index] = value;
    this.model = newModel;
  }

  private changeModelRefBySectionAndField(index1: any, index2: any, value: any) {
    const newModel = { ...this.model };
    newModel[index1][index2] = value;
    this.model = newModel;
  }

  // Aggiunge l'oggetto field a domainsStringArray in maniera univoca, così da effettuare
  // tutte le chiamate successivamente senza duplicare nessuna chiamata
  addDomainString(field, parentValue: string) {
    for (let object of this.domainsStringArray) {
      if (object['domainCod'] === field.domainCod && object['parentValue'] === parentValue) {
        return;
      }
    }

    this.domainsStringArray.push({
      domainCod: field.domainCod,
      parentValue: parentValue,
      domainExpired: field.domainExpired ? field.domainExpired : false
    });
    if (this.checkIfIsCategoryDomain(field.domainCod)) {
      this.domainsStringArray.push({
        domainCod: field.domainCod,
        parentValue: parentValue,
        domainExpired: true
      });
    }
  }

  checkIfIsCategoryDomain(domainCod) {
    return (domainCod === 'UBZ_DOM_CATEGORY_TYPE' || domainCod === 'UBZ_DOM_REG_CATEGORY_TYPE') ? true : false;
  }
  // Effettua la subscribe sull'arrray di observable contenenti tutti i domini necessari nell'apf 
  // e provvede alla valorizzazione del relativo oggetto domainTables per contenerli
  protected getAllDomainRequest() {

    this.sendAllDomainRequest().takeUntil(this.destroyed$).subscribe(resultArray => {
      let domainCod = '';
      let formatResultArray = [];

      this.domainsStringArray = this.sortDomains(this.domainsStringArray);
      if (resultArray && resultArray.length > 0) {
        resultArray.forEach((element) => {
          if (element instanceof Array) {
            element.forEach((el) => {
              formatResultArray.push(el);
            });
          } else formatResultArray.push(element);
        });

        formatResultArray.forEach((res, index) => {
          domainCod = this.domainsStringArray[index]['domainCod'];
          if (this.domainsStringArray[index]['domainExpired'] === true) {
            if (domainCod === 'UBZ_DOM_CATEGORY_TYPE') {
              this.expiredDomain = res;
            } else if (domainCod === 'UBZ_DOM_REG_CATEGORY_TYPE') {
              this.expiredRegDomain = res;
            }

         else{
          this.domainTables[domainCod] = new DomainTable();
          this.domainTables[domainCod].domains = res;

         }

          } else {
            this.domainTables[domainCod] = new DomainTable();
            this.domainTables[domainCod].domains = res;
            if (domainCod === 'UBZ_DOM_CATEGORY_TYPE' && !this.setReadOnly) {
              this.deleteCategoryIfExpired();
            }

          }
         
        });
      }
    });
  }

  sortDomains(domainsStringArray: Object[]) {
    let sortedDomains: Object[] = [];
    let city: Object = {};
    domainsStringArray.forEach(val => {
      if (val['domainCod'] === 'UBZ_CON_CITY') {
        city = val;
      } else {
        sortedDomains.push(val);
      }
    });

    if (Object.keys(city).length !== 0) {
      sortedDomains.push(city);
    }

    return sortedDomains;
  }

  // Per ogni dominio presente in domainsStringArray effettua la chiamata al servizio di recupero valori
  // e inserisce l'observable di risposta in observablesDomain per effettuare una forkJoin
  sendAllDomainRequest(): Observable<any> {
    let observablesDomain: Observable<any>[] = new Array();
    let domainList = [];
    let cityDomainResp: Observable<any>;
    this.domainsStringArray.forEach((domainObject) => {
      if (domainObject['domainCod'] === 'UBZ_CON_CITY') {
        cityDomainResp = this.domainService.getDomainCityMap(domainObject['parentValue']);
      } else {
        let domainInfo = {
          domainName: domainObject['domainCod'],
          parentCode: domainObject['parentValue'],
          expired: domainObject['domainExpired']
        };
        domainList.push(domainInfo);
      }
    });

    if (domainList.length !== 0) {
      observablesDomain.push(this.domainService.getAllDomainList(domainList));
    }

    if (cityDomainResp) {
      observablesDomain.push(cityDomainResp);
    }

    return Observable.forkJoin(observablesDomain);
  }



  protected applyValidationRules() {
    this.validationForm[0] = this.appForm.form;
    const validators = [];
    for (const form of this.applicationForms) {
      for (const section of form.sections) {
        for (const row of section.rows) {
          for (const field of row.fields) {
            if (field.validationRule) {
              if (
                field.type === 'Number' &&
                !field.validationRule.includes('Number')
              ) {
                field.validationRule = this.formatNumberEvaluation(
                  field.validationRule
                );
              }
              // this.validationForm[0].addControl( field.code , new FormControl('' , this.evalValidationRule( section.formCode , field.validationRule )));
              field.validationRule = `!(${field.renderIf}) || (\$${field.code} == null || \$${field.code} === '' || (${field.validationRule}))`;
              validators.push(
                this.evalValidationRule(section.formCode, field.validationRule)
              );
            }
          }
        }
      }
    }
    this.validationForm[0].setValidators(validators);
  }

  protected formatNumberEvaluation(validationRule: string): string {
    const firstDollarIndex = validationRule.indexOf('$');
    const secondDollarIndex = validationRule.lastIndexOf('$');
    const firstSpaceIndex = validationRule.indexOf(' ');
    const firstWord = validationRule.substring(
      firstDollarIndex,
      firstSpaceIndex
    );
    if (firstDollarIndex !== secondDollarIndex) {
      const secondWord = validationRule.substring(secondDollarIndex);
      validationRule = validationRule.replace(
        secondWord,
        'Number(' + secondWord + ')'
      );
    }
    return validationRule.replace(firstWord, 'Number(' + firstWord + ')');
  }

  protected evalValidationRule(formCod: number, rule: string): ValidatorFn {
    return (control: AbstractControl): { [key: string]: any } => {
      return !this.evalExpression(formCod, rule)
        ? { invalidField: { value: control.value } }
        : null;
    };
  }

  evalExpression(formCod: number, expression: string): boolean {
    try {
      if (expression === null) {
        return true;
      } else if (expression.indexOf('$') === -1) {
        return eval(expression);
      } else {
        expression = expression.replace('$', 'this.model[formCod].');
        return this.evalExpression(formCod, expression);
      }
    } catch (e) {
      console.error(
        'EXCEPTION ON APFORM COMPONENT AT EVAL EXPRESSION. FORMCOD: ' +
        formCod +
        ' , EXPRESSION: ' +
        expression
      );
    }
  }

  evalAllExpr(mapWithValues, expression: string): boolean {

    try {

      if (expression === null) {
        return true;
      } else if (expression.indexOf('$') === -1) {
        return eval(expression);
      } else {
        if (mapWithValues) {
          expression = expression.replace('$', 'mapWithValues.');
          return this.evalAllExpr(mapWithValues, expression);
        }
      }
    } catch (e) {
      console.error(
        'EXCEPTION ON APFORM COMPONENT AT EVAL EXPRESSION. FORMCOD: ' +

        ' , EXPRESSION: ' +
        expression
      );
    }
  }

  evaluateAllConditions(mapWithValues, expression, formCode) {
    if (mapWithValues) {
      return this.evalAllExpr(mapWithValues, expression);
    } else {
      return this.evalExpression(formCode, expression);
    }
  }

  evalTimestampExpression(
    formCode: number,
    pattern: string,
    dateType: string
  ): Date {
    if (pattern === null) {
      return this.returnDefaultDate(dateType);
    }
    if (pattern.includes('&&')) {
      return this.getDateFromComplexPattern('&&', pattern, dateType, formCode);
    } else if (pattern.includes('||')) {
      // Multiple conditions, i.e. {#le;(#NOW)}||{#lt;(#NOW)}
      return this.getDateFromComplexPattern('||', pattern, dateType, formCode);
    } else {
      // Single condition, i.e. {#le;(#NOW)}
      const result: RegExpExecArray = this.getDateOperator(pattern, dateType);
      if (result === null) {
        return this.returnDefaultDate(dateType);
      }
      const operator: string = result[1];
      const toTest: string = result[2];
      const identifierToTest: string = result[3];
      return this.getDateForControl(
        formCode,
        operator,
        toTest,
        identifierToTest
      ).date;
    }
  }

  private getDateFromComplexPattern(
    binaryOperator: string,
    pattern: string,
    dateType: string,
    formCode: number
  ): Date {
    const operators: OperationObject[] = [];
    const splittedPattern: string[] = pattern.split(binaryOperator);
    for (const patternPiece of splittedPattern) {
      const result: RegExpExecArray = this.getDateOperator(
        patternPiece.trim(),
        dateType
      );
      if (result) {
        const obj: OperationObject = this.getDateForControl(
          formCode,
          result[1],
          result[2],
          result[3]
        );
        operators.push(obj);
      }
    }
    if (binaryOperator === '&&') {
      return operators[0].date;
    } else {
      // SONO IN UNA CONDIZIONE DI OR
      let dateToReturn: Date;
      for (const op of operators) {
        if (!dateToReturn) {
          dateToReturn = op.date;
        }
        if (dateType === this.MIN_DATE_TYPE) {
          // PRENDO LA DATA MAGGIORE PER INCLUDERE ENTRAMBE
          if (dateToReturn.getTime() < op.date.getTime()) {
            dateToReturn = op.date;
          }
        } else {
          // PRENDO LA DATA MINORE PER INCLUDERE ENTRAMBE
          if (dateToReturn.getTime() > op.date.getTime()) {
            dateToReturn = op.date;
          }
        }
      }
      return dateToReturn;
    }
  }

  private getDateForControl(
    formCode: number,
    operator: string,
    toTest: string,
    identifierToTest: string
  ): OperationObject {
    let dateToCompare: Date;
    if (toTest === '#') {
      dateToCompare = new Date();
      dateToCompare.setHours(0, 0, 0, 0);
    } else {
      if (this.model[formCode][identifierToTest]) {
        dateToCompare = new Date(this.model[formCode][identifierToTest]);
      } else {
        dateToCompare = new Date();
      }
      dateToCompare.setHours(0, 0, 0, 0);
    }
    if (operator === 'lt') {
      dateToCompare.setTime(
        dateToCompare.getTime() - this.constants.MILLISECONDS_IN_A_DAY
      );
    } else if (operator === 'gt') {
      dateToCompare.setTime(
        dateToCompare.getTime() + this.constants.MILLISECONDS_IN_A_DAY
      );
    }
    return {
      date: dateToCompare,
      operator: operator,
      fieldName: identifierToTest
    };
  }

  private getDateOperator(pattern: string, dateType: string): RegExpExecArray {
    let regEx: RegExp;
    if (dateType === this.MIN_DATE_TYPE) {
      regEx = new RegExp(`\{#(ge|gt);[(]([#]|[$])([a-zA-Z_-]+)[)]\}`);
    } else {
      regEx = new RegExp(`\{#(le|lt);[(]([#]|[$])([a-zA-Z_-]+)[)]\}`);
    }
    return regEx.exec(pattern);
  }

  private returnDefaultDate(dateType: string): Date {
    return dateType === this.MIN_DATE_TYPE ? this.minDate : this.maxDate;
  }

  getDomainValue(domTable: string): Domain[] {

    let ret = [];
    if (this.domainTables[domTable]) {
      ret = this.domainTables[domTable].domains;
    }
    return ret;
  }

  getDomainValueForDomCode(domTable: string, domCode: string): string {
    let ret = '';
    if (
      this.domainTables[domTable] &&
      this.domainTables[domTable].domains[domCode]
    ) {
      ret = this.domainTables[domTable].domains[domCode].translationCod;
    }
    return ret;
  }


  getValueForDomCode(domainCod, value) {
    if (domainCod === 'UBZ_DOM_CATEGORY_TYPE') {
      return (this.expiredDomain && this.expiredDomain[value]) ? this.expiredDomain[value].translationCod : '';
    } else if (domainCod === 'UBZ_DOM_REG_CATEGORY_TYPE' || domainCod === 'UBZ_DOM_REG_CATEGORY_TYPE_V') {
      return (this.expiredRegDomain && this.expiredRegDomain[value]) ? this.expiredRegDomain[value].translationCod : this.getDomainValueForDomCode(domainCod, value);
    } else {
      return this.getDomainValueForDomCode(domainCod, value);
    }

  }

  getValueForDomCodeAndModifyModel(value, formCod, field) {
    if (field.domainCod === 'UBZ_DOM_ENERGY_CLASS') {
      this.changeModelRefBySectionAndField(formCod, field.code, '');
      this.setDependentFieldToEmpty(field.code, formCod, false);
    }
    return this.getValueForDomCode(field.domainCod, value);
  }


  public refreshApplicationForm() {
    this.retrieveApplicationForm();
  }

  getUniqueId(fieldCod: string): string {
    return `${fieldCod}-${this.idCode ? this.idCode : '0'}`;
  }

  submitForm() {
    if (this.appForm.valid) {
      this.formSubmit.emit();
    }
  }

  isFieldError(fieldCod) {
    return (
      this.appForm.controls[fieldCod] &&
      this.appForm.submitted &&
      !this.appForm.controls[fieldCod].valid
    );
  }

  recalculateChilds(parentField: Field, value) {
    let domainValue: string;
    // if (!value) {
    //   this.domainTables[parentField.domainCod] = new DomainTable();
    // }

    if (parentField && parentField.domainCod) {
      for (const apForm of this.applicationForms) {
        for (const row of apForm.sections[0].rows) {
          for (const field of row.fields) {

            if (field.renderIf !== 'true' && field.renderIf !== 'false' && field.renderIf.includes(parentField.code) &&
              !this.evalExpression(apForm.sections[0].formCode, field.renderIf)) {
              this.changeModelRefBySectionAndField(apForm.sections[0].formCode, field.code, null);
            }

            if (parentField.code === field.parentFieldCod) {
              this.changeModelRefBySectionAndField(apForm.sections[0].formCode, field.code, '');
              switch (parentField.domainCod) {
                case 'UBZ_DOM_PROVINCE':
                  this.domainService.getDomainCity(value).takeUntil(this.destroyed$).subscribe(x => {
                    this.domainTables[field.domainCod].domains = x;
                  });
                  break;

                default:
                  const parentFieldDomCode: string = this.model[
                    apForm.sections[0].formCode
                  ][parentField.code];
                  if (
                    value &&
                    value.length > 0 &&
                    !(parentFieldDomCode === '')
                  ) {
                    domainValue = this.calculateDomainValue(
                      field,
                      apForm.sections[0].formCode
                    );
                    this.domainService
                      .newGetDomain(field.domainCod, domainValue, field.domainExpired ? field.domainExpired : false)
                      .takeUntil(this.destroyed$).subscribe(x => {
                        this.domainTables[field.domainCod] = new DomainTable();
                        this.domainTables[field.domainCod].domains = x;
                      });
                  }
              }
              this.recalculateChilds(field, domainValue);
            }
          }
        }
      }
    }
    this._cdRef.detectChanges();
  }

  private calculateDomainValue(
    field: Field,
    formCode: number,
    wholeDomain: string = ''
  ): string {
    const parent = field.parentFieldCod;
    if (!parent) {
      return this.removePipeCharacter(wholeDomain);
    } else {
      for (const apForm of this.applicationForms) {
        for (const row of apForm.sections[0].rows) {
          for (const f of row.fields) {
            if (f.code === parent) {
              return this.removePipeCharacter(
                this.calculateDomainValue(
                  f,
                  formCode,
                  this.model[formCode][f.code] + '|' + wholeDomain
                )
              );
            }
          }
        }
      }
    }
  }

  private removePipeCharacter(str: string): string {
    const stringLength: number = str.length;
    if (str.substring(stringLength - 1, stringLength) === '|') {
      return str.substring(0, stringLength - 1);
    } else {
      return str;
    }
  }

  changeCheckboxValue(formCode: any, fieldCode: any) {
    // cambio il valore e cancello i campi che sono stati resi invisibili da lui
    this.changeModelRefBySectionAndField(formCode, fieldCode, !this.model[formCode][fieldCode]);
    this.setDependentFieldToEmpty(fieldCode, formCode, true);
    this.sharedService.setCheckboxChangeSubj(true);
    this._cdRef.detectChanges();
  }


  private setDependentFieldToEmpty(fieldCode: any, formCode: any, fieldIsCheckbox: boolean) {
    for (const apForm of this.applicationForms) {
      for (const rows of apForm.sections[0].rows) {
        for (const field of rows.fields) {
          if (field.renderIf !== 'true' &&
            field.renderIf !== 'false' &&
            field.renderIf.includes(fieldCode)) {
            if (fieldIsCheckbox) {
              if (!this.evalExpression(formCode, field.renderIf)) {
                this.changeModelRefBySectionAndField(formCode, field.code, null);
              }
            } else {
              this.changeModelRefBySectionAndField(formCode, field.code, null);
            }

          }
        }
      }
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

}
