// FIXME - VERSIONE MODIFICATA PER FUNZIONARE IN WAVE 1, LA VERSIONE CORRETTA DELLA PIPE è QUELLA IMPLEMENTATA IN WAVE2
import { Pipe, PipeTransform } from '@angular/core';
import { LandingService } from '../../../simulation';

@Pipe({
  name: 'removeFromDomain'
})

// Pipe per filtrare i valori all'interno del dominio passato in input
// La logica dei metodi da invocare e le casistiche è gestita internamente alla pipe
export class RemoveFromDomainPipe implements PipeTransform {
  przIndCodeValues: string[];
  tecOperEtlValues: string[];
  przCorCodeValues: string[];
  subrogationCodeValues: string[];
  tgpCodeValues: string[];
  
  aggCodeValues: string[];
  worCodeValues: string[];
  constructor(private landingService: LandingService) {
    this.worCodeValues = ['AGG', 'IPG'];
    this.tgpCodeValues = ['001', '003', '007'];
    this.przIndCodeValues = ['ACQ', 'COS', 'RIS', 'EAG'];
    this.tecOperEtlValues = ['ACQ', 'COS', 'RIS'];
    this.przCorCodeValues = [
      'ACQ',
      'COS',
      'RIS',
      'AST',
      'EAG',
      'MUT',
      'IPG',
      'CTU',
      'IND',
      'PRF',
      'MUL'
    ];
    this.subrogationCodeValues = [
      'ACQ',
      'COS',
      'RIS',
      'AST',
      'EAG',
      'MUT',
      'IPG',
      'CTU',
      'IND',
      'PRF'
    ];
    this.aggCodeValues = ['AGG', 'AGD'];
  }
  transform(value: any, macroProcess: string, isSurroga: boolean, isTecnicaOperETL: boolean): any {
    if (macroProcess === 'WOR') {
      return this.worValuesFilter(value);
    }
    if (macroProcess === 'AGG') {
      return this.aggValuesFilter(value);
    }
    if (isSurroga) {
      return this.subrogationValuesFilter(value);
    }
    if (isTecnicaOperETL) {
      return this.tecOperEtlFilter(value);
    }
    if (this.landingService.originationProcess === 'PER') {
      if (this.landingService.posSegment === 'IND') {
        return this.przIndValuesFilter(value);
      } else {
        // posSegment === 'COR'
        return this.przCorValuesFilter(value);
      }
    }
    if (this.landingService.originationProcess === 'TGP') {
      return this.tgpValuesFilter(value);
    }
    return value;
  }

  // Invocato quando macroProcess === 'WOR', rimuove dal dominio il valore con codice CTU
  // Restituisce l'array di origine filtrato
  removeCtuValue(domain: any[]): any[] {
    let domainToReturn = new Array();
    domain.forEach(element => {
      if (element.domCode !== 'CTU') {
        domainToReturn.push(element);
      }
    });
    return domainToReturn;
  }

  // Invocato quando posSegment === 'IND', filtra i valori dell'array conservando solo quelli
  // relativi alL'INDIVIDUAL (indicati dentro przIndCodeValues)
  // Restituisce l'array di origine filtrato
  przIndValuesFilter(domain: any[]): any[] {
    let domainToReturn = new Array();
    domain.forEach(element => {
      if (this.przIndCodeValues.indexOf(element.domCode) !== -1) {
        domainToReturn.push(element);
      }
    });
    return domainToReturn;
  }

  //Called when posSegment === 'IND', macroProcess === 'ETL', userDetails.profile='UBZOPER'
  //and the page isn't in read-only mode 
  tecOperEtlFilter(domain: any[]): any[] {
    let domainToReturn = new Array();
    domain.forEach(element => {
      if (this.tecOperEtlValues.indexOf(element.domCode) !== -1) {
        domainToReturn.push(element);
      }
    });
    return domainToReturn;
  }

  // Invocato quando posSegment === 'COR', filtra i valori dell'array conservando solo quelli
  // relativi al corporate (indicati dentro przCorCodeValues)
  // Restituisce l'array di origine filtrato
  przCorValuesFilter(domain: any[]): any[] {
    let domainToReturn = new Array();
    domain.forEach(element => {
      if (this.przCorCodeValues.indexOf(element.domCode) !== -1) {
        domainToReturn.push(element);
      }
    });
    return domainToReturn;
  }

  // Invocato quando posSegment === 'IND' e originationProcess === 'TGP'
  // filtra i valori dell'array conservando solo quelli relativi al processo TGP (indicati dentro tgpCodeValues)
  // Restituisce l'array di origine filtrato
  tgpValuesFilter(domain: any[]): any[] {
    let domainToReturn = new Array();
    domain.forEach(element => {
      if (this.tgpCodeValues.indexOf(element.domCode) !== -1) {
        domainToReturn.push(element);
      }
    });
    return domainToReturn;
  }

  // Invocato quando macroprocess è 'AGG'
  // filtra i valori dell'array conservando solo quelli relativi al processo AGG
  // Restituisce l'array di origine filtrato
  aggValuesFilter(domain: any[]): any[] {
    let domainToReturn = new Array();
    domain.forEach(element => {
      if (this.aggCodeValues.indexOf(element.domCode) !== -1) {
        domainToReturn.push(element);
      }
    });
    return domainToReturn;
  }

  // Invocato quando macroprocess è 'WOR'
  // filtra i valori dell'array conservando solo quelli relativi al processo WOR
  // Restituisce l'array di origine filtrato
  worValuesFilter(domain: any[]): any[] {
    let domainToReturn = new Array();
    domain.forEach(element => {
      if (this.worCodeValues.indexOf(element.domCode) !== -1) {
        domainToReturn.push(element);
      }
    });
    return domainToReturn;
  }

  /**
   * @name subrogationValuesFilter
   * @description Invocato quando la richiesta è di surroga
   * filtra i valori dell'array conservando solo quelli relativi al processo di surroga (indicati dentro subrogationCodeValues)
   * Restituisce l'array di origine filtrato
   * @param domain Array contenenti tutti i valori da filtrare
   */
  subrogationValuesFilter(domain: any[]): any[] {
    let domainToReturn = new Array();
    domain.forEach(element => {
      if (this.subrogationCodeValues.indexOf(element.domCode) !== -1) {
        domainToReturn.push(element);
      }
    });
    return domainToReturn;
  }
}
