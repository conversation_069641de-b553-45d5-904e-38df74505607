<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="unicreditmedium_italic" horiz-adv-x="1073" >
<font-face units-per-em="2048" ascent="1536" descent="-512" />
<missing-glyph horiz-adv-x="419" />
<glyph unicode="&#xfb01;" horiz-adv-x="1062" d="M-25 -342q0 10 4.5 21.5t8.5 27.5q35 111 66.5 246t58.5 283l106 606h-125q-23 0 -22 18q0 14 6 43l8 41q4 18 12 24.5t27 10.5l121 18l12 72q31 178 147.5 256t309.5 78q80 0 145.5 -8.5t106.5 -18.5q29 -6 28 -26q0 -10 -4 -23.5t-12 -36.5l-14 -43q-8 -27 -25 -26 q-8 0 -16.5 2t-20.5 6q-35 8 -83 13t-105 5q-49 0 -94.5 -7t-80 -27.5t-59 -56.5t-35.5 -93l-10 -59h541q35 0 35 -25q0 -8 -5.5 -37t-13.5 -63l-164 -842q-6 -37 -45 -37h-110q-37 0 -37 25q0 8 5 36.5t13 63.5l142 717h-389l-109 -617q-35 -201 -67.5 -328.5t-65.5 -218.5 q-10 -23 -22.5 -31.5t-39.5 -8.5h-100q-29 0 -29 20z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1173" d="M-25 -342q0 10 4.5 21.5t8.5 27.5q35 111 66.5 246t58.5 283l106 606h-125q-23 0 -22 18q0 14 6 43l8 41q4 18 12 24.5t27 10.5l123 18l12 72q35 180 135.5 258t290.5 78q74 0 143.5 -10.5t114.5 -22.5l90 10q25 4 36 4h15q25 0 25 -22q0 -10 -5 -35.5t-13 -66.5 l-193 -981q-4 -27 -7 -46.5t-3 -37.5q0 -25 14.5 -34t46.5 -9h72q29 0 29 -23q0 -10 -3.5 -24.5t-7.5 -38.5l-6 -31q-4 -23 -15 -30t-34 -7h-76q-109 0 -161 36t-52 128q0 41 13 106l186 953q-39 10 -96.5 18t-106.5 8q-111 0 -167 -41t-79 -149l-10 -55h211q25 0 25 -17 q0 -6 -2 -20.5t-6 -30.5l-13 -64q-4 -16 -12 -23t-31 -7h-200l-109 -617q-35 -201 -67.5 -328.5t-65.5 -218.5q-10 -23 -22.5 -31.5t-39.5 -8.5h-100q-29 0 -29 20z" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="419" />
<glyph unicode=" "  horiz-adv-x="419" />
<glyph unicode="&#x09;" horiz-adv-x="419" />
<glyph unicode="&#xa0;" horiz-adv-x="419" />
<glyph unicode="!" horiz-adv-x="483" d="M10 59q0 18 2 36t6 32q14 57 47 77.5t87 20.5h10q94 0 94 -78q0 -18 -2 -35.5t-6 -31.5q-14 -57 -47 -77.5t-86 -20.5h-11q-94 0 -94 77zM102 346q0 12 3.5 34.5t13.5 76.5l143 848q4 23 18.5 30.5t36.5 7.5h115q41 0 41 -30q0 -12 -6 -40t-14 -63l-185 -845 q-6 -25 -17 -34t-38 -9h-68q-43 0 -43 24z" />
<glyph unicode="&#x22;" horiz-adv-x="763" d="M150 946l65 451q6 43 43 43h137q39 0 31 -41l-109 -455q-8 -35 -38 -35h-105q-31 0 -24 37zM524 946l66 451q6 43 43 43h137q39 0 31 -41l-109 -455q-8 -35 -39 -35h-104q-31 0 -25 37z" />
<glyph unicode="#" horiz-adv-x="1329" d="M25 401q0 10 16 50l22 53q12 29 25.5 38t44.5 9h182l88 211h-190q-33 0 -33 20q0 10 17 49l22 54q12 29 25.5 38t44.5 9h184l145 350q12 29 25.5 37t42.5 8h98q35 0 35 -22q0 -8 -7 -26.5t-17 -41.5l-127 -305h229l145 350q12 29 25.5 37t42.5 8h98q35 0 35 -22 q0 -8 -7 -26.5t-17 -41.5l-127 -305h180q33 0 33 -21q0 -10 -17 -49l-22 -53q-12 -29 -25.5 -38t-44.5 -9h-176l-88 -211h184q33 0 33 -21q0 -10 -16 -49l-23 -53q-12 -29 -25.5 -38t-44.5 -9h-176l-141 -336q-12 -29 -25.5 -37t-42.5 -8h-98q-35 0 -35 23q0 8 7.5 26.5 t17.5 40.5l123 291h-232l-139 -336q-12 -29 -25.5 -37t-42.5 -8h-98q-35 0 -35 23q0 8 7.5 26.5t17.5 40.5l123 291h-189q-33 0 -32 20zM508 551h231l88 211h-231z" />
<glyph unicode="$" d="M12 72q0 10 4 23.5t11 33.5l12 45q8 31 29 31q10 0 18 -3t18 -7q53 -23 122 -37.5t141 -18.5l88 457l-39 10q-72 18 -121 43t-78 58.5t-41 81t-12 108.5q0 51 11 107.5t30 107.5q39 106 137 166.5t264 64.5l31 152q4 23 12 28t29 5h53q23 0 28 -6t1 -27l-31 -156 q66 -4 124 -16t105 -27q25 -8 25 -24q0 -12 -5 -27.5t-13 -38.5l-17 -45q-10 -25 -28 -24q-10 0 -20.5 2t-20.5 6q-33 10 -80 20.5t-101 14.5l-82 -435l25 -4q78 -20 129 -43.5t82 -56.5t43 -77t12 -103q0 -41 -9 -96.5t-28 -110.5q-47 -141 -145 -202.5t-266 -67.5 l-29 -150q-4 -20 -12 -26.5t-31 -6.5h-51q-23 0 -28 8.5t-1 24.5l29 150q-90 6 -165 21t-126 36q-33 12 -33 31zM354 907q0 -51 32 -81.5t112 -53.5l77 410q-84 -4 -131 -38t-69 -95q-10 -25 -15.5 -63t-5.5 -79zM489 143q78 10 128.5 45t72.5 101q10 31 16.5 66.5t6.5 66.5 q0 61 -32 92t-110 53z" />
<glyph unicode="%" horiz-adv-x="1652" d="M139 823q0 57 12.5 134t38.5 151q43 121 116 178t192 57q115 0 174 -57.5t59 -157.5q0 -57 -12 -133.5t-39 -150.5q-43 -121 -115.5 -178.5t-191.5 -57.5q-115 0 -174.5 57.5t-59.5 157.5zM246 2q0 10 12 29l952 1280q16 20 29.5 26t38.5 6h100q31 0 31 -20 q0 -10 -12 -29l-953 -1280q-16 -20 -29.5 -26t-37.5 -6h-101q-31 0 -30 20zM313 838q0 -49 18.5 -68.5t57.5 -19.5q53 0 90 44t60 173q18 98 18 147t-18.5 68.5t-57.5 19.5q-53 0 -90 -44t-59 -173q-19 -98 -19 -147zM922 197q0 57 12 134t39 150q43 121 115.5 178.5 t191.5 57.5q115 0 174 -57.5t59 -157.5q0 -57 -12 -134t-39 -151q-43 -121 -115.5 -178t-191.5 -57q-115 0 -174 57t-59 158zM1096 211q0 -49 18.5 -68.5t56.5 -19.5q53 0 90 44t60 173q18 98 18 147t-18 68.5t-57 19.5q-53 0 -90 -44t-60 -173q-18 -98 -18 -147z" />
<glyph unicode="&#x26;" horiz-adv-x="1259" d="M23 297q0 82 25.5 148.5t69.5 118.5t103.5 91t128.5 68q-51 70 -77.5 141.5t-26.5 136.5q0 160 108.5 251t288.5 91q166 0 252 -74.5t86 -199.5q0 -135 -78 -242.5t-258 -179.5l219 -264q27 57 43.5 117.5t24.5 107.5q4 23 14 29t31 6h100q33 0 33 -25t-16 -94 q-23 -88 -47.5 -153.5t-65.5 -128.5l123 -146q16 -18 16 -28q0 -12 -22 -29l-60 -49q-18 -16 -25 -21.5t-18 -5.5q-8 0 -14 5t-23 26l-98 117q-84 -70 -183 -99.5t-247 -29.5h-24q-186 0 -284.5 76.5t-98.5 238.5zM231 317q0 -39 10.5 -71.5t36 -56t67.5 -37t106 -13.5h18 q170 0 289 94l-318 381q-92 -41 -150.5 -115.5t-58.5 -181.5zM449 1004q0 -66 20 -119.5t66 -106.5l20 -24q123 51 175 122.5t52 172.5q0 74 -43 108.5t-108 34.5q-86 0 -134 -51t-48 -137z" />
<glyph unicode="'" horiz-adv-x="389" d="M150 946l65 451q6 43 43 43h137q39 0 31 -41l-109 -455q-8 -35 -38 -35h-105q-31 0 -24 37z" />
<glyph unicode="(" horiz-adv-x="622" d="M86 389q0 260 127 514t346 473q29 29 43 29q12 0 29 -10l55 -35q27 -16 27 -29q0 -12 -17 -28q-195 -205 -304 -440.5t-109 -477.5q0 -143 32.5 -283.5t85.5 -248.5q10 -20 11 -29q0 -8 -7.5 -14.5t-31.5 -16.5l-41 -18q-31 -14 -45 -15q-18 0 -31 23q-78 137 -124 292.5 t-46 313.5z" />
<glyph unicode=")" horiz-adv-x="622" d="M-117 -166q0 12 17 29q195 205 304 440.5t109 476.5q0 143 -32.5 283.5t-85.5 249.5q-10 20 -11 28q0 8 7.5 14.5t31.5 16.5l41 19q31 14 45 14q18 0 31 -23q78 -137 124 -292.5t46 -313.5q0 -260 -127 -514t-346 -473q-29 -29 -43 -29q-12 0 -29 11l-55 34 q-27 16 -27 29z" />
<glyph unicode="*" horiz-adv-x="757" d="M164 944q0 14 22 33l201 174l-197 45q-25 6 -24 25q0 6 3 12l9 18l39 72q16 27 31 27q6 0 15 -4.5t26 -14.5l145 -84l17 230q2 33 34 32h105q27 0 26 -18q0 -10 -7 -30.5t-15 -43.5l-57 -172l204 92q8 4 14.5 6.5t12.5 2.5q12 0 16 -19q4 -14 6.5 -31.5t6.5 -35.5 q2 -10 3 -22.5t1 -20.5q0 -10 -6.5 -15.5t-26.5 -9.5l-223 -49l131 -152q20 -20 20 -37q0 -20 -32 -39l-56 -32q-25 -14 -33 -15q-18 0 -28 21l-84 196l-150 -202q-18 -25 -34 -25q-6 0 -14.5 3t-22.5 13l-49 35q-16 12 -22.5 18.5t-6.5 16.5z" />
<glyph unicode="+" d="M51 465q0 8 2 24.5t6 34.5l9 49q4 29 17 38.5t42 9.5h307l62 317q6 29 19 37t44 8h84q41 0 41 -25q0 -6 -1 -15t-7 -40l-55 -282h315q33 0 33 -23q0 -8 -2 -24.5t-6 -34.5l-9 -50q-4 -29 -17 -38t-42 -9h-307l-62 -317q-6 -29 -19 -37t-44 -8h-84q-41 0 -41 24 q0 6 1 15.5t7 40.5l55 282h-315q-33 0 -33 23z" />
<glyph unicode="," horiz-adv-x="446" d="M-76 -217q0 8 13 20q72 76 95 110t23 58q0 12 -4 18.5t-12 12.5l-14 10q-31 25 -31 70q0 66 28.5 108.5t100.5 42.5h18q47 0 73 -23.5t26 -76.5q0 -86 -49.5 -179t-170.5 -214q-14 -14 -22 -14q-10 0 -25 10l-28 20q-20 14 -21 27z" />
<glyph unicode="-" horiz-adv-x="645" d="M33 469q0 8 4 27.5t8 42.5l6 32q4 29 16.5 39.5t47.5 10.5h397q27 0 35 -7.5t8 -19.5q0 -8 -4 -27.5t-8 -42.5l-6 -32q-4 -29 -16.5 -39.5t-47.5 -10.5h-397q-27 0 -35 7.5t-8 19.5z" />
<glyph unicode="." horiz-adv-x="417" d="M-20 57q0 14 2 30.5t8 45.5q25 100 129 100h16q47 0 71.5 -20t24.5 -55q0 -14 -2 -30.5t-8 -45.5q-12 -53 -43 -76.5t-86 -23.5h-16q-47 0 -71.5 20.5t-24.5 54.5z" />
<glyph unicode="/" horiz-adv-x="763" d="M-70 -31q0 10 8.5 25.5t24.5 50.5l676 1321q12 27 24.5 34t34.5 7h105q18 0 24.5 -4t6.5 -12q0 -10 -8.5 -26.5t-24.5 -51.5l-676 -1321q-14 -27 -25.5 -34t-33.5 -7h-105q-31 0 -31 18z" />
<glyph unicode="0" d="M59 346q0 111 15.5 218.5t38.5 211.5q31 145 65.5 237.5t85.5 160.5q68 92 162 130.5t223 38.5q365 0 365 -364q0 -111 -15.5 -218.5t-37.5 -211.5q-31 -145 -66 -237.5t-86 -159.5q-68 -92 -162 -131t-223 -39q-365 0 -365 364zM248 371q0 -123 44 -175.5t148 -52.5 q84 0 137.5 31t94.5 96q31 49 51 118t43 161q25 106 42 212.5t17 192.5q0 123 -44 175.5t-146 52.5q-86 0 -139.5 -31t-94.5 -96q-31 -49 -51 -118t-43 -161q-25 -106 -42 -212.5t-17 -192.5z" />
<glyph unicode="1" d="M92 23q0 10 8 51l9 41q4 25 13 33t34 8h258l186 972h-12l-254 -98q-16 -6 -25 -6q-16 0 -24 23l-25 69q-6 16 -8 23.5t-2 11.5q0 18 22 27l324 125q37 14 59.5 18t51.5 4h67q47 0 47 -27q0 -10 -4 -29.5t-8 -41.5l-205 -1071h242q33 0 33 -21q0 -12 -9 -53l-8 -43 q-4 -23 -13 -31t-34 -8h-686q-37 0 -37 23z" />
<glyph unicode="2" d="M10 33q0 10 2 24.5t6 46.5l11 70q16 115 48 196t83 138t122.5 98t168.5 78l116 45q111 41 165 84t71 102q8 27 12 58t4 51q0 90 -56 124t-149 34q-59 0 -136 -10.5t-144 -32.5q-12 -4 -20.5 -5t-12.5 -1q-16 0 -22 24l-9 37q-6 27 -8 41t-2 22q0 21 25 29 q29 10 70.5 20.5t90 18.5t100.5 13t101 5q78 0 144.5 -15t114.5 -50t75 -93.5t27 -146.5q0 -74 -19 -147q-12 -51 -35.5 -92t-62.5 -78t-98.5 -70t-145.5 -65l-129 -49q-76 -29 -127 -58.5t-86 -67.5t-55.5 -89.5t-32.5 -124.5l-6 -37h565q25 0 32 -8.5t3 -30.5l-16 -88 q-4 -23 -14.5 -28t-35.5 -5h-686q-49 0 -49 33z" />
<glyph unicode="3" d="M12 68q0 8 2 16t6 22l19 66q8 25 27 25q6 0 22 -4q61 -18 142 -34t147 -16q88 0 147.5 18.5t98.5 61.5q74 80 73 215q0 96 -63.5 137t-192.5 41h-82q-33 0 -32 21q0 4 2 17.5t6 29.5l10 57q4 20 14.5 27.5t34.5 7.5h70q125 0 205 45t100 121q8 27 11 53.5t3 51.5 q0 76 -54 106.5t-159 30.5q-63 0 -133.5 -11.5t-132.5 -27.5q-8 -2 -14 -3t-13 -1q-18 0 -24 28l-10 45q-4 14 -6.5 26.5t-2.5 21.5q0 23 23 30q70 23 160 37t180 14q180 0 278.5 -70.5t98.5 -209.5q0 -35 -4 -63.5t-11 -55.5q-23 -98 -86 -156.5t-159 -85.5v-4 q86 -25 133 -85t47 -164q0 -57 -9.5 -107.5t-25.5 -93.5q-49 -135 -163.5 -200.5t-307.5 -65.5q-98 0 -194.5 18t-153.5 41q-16 6 -21.5 11t-5.5 16z" />
<glyph unicode="4" d="M4 356q0 10 8 52q4 23 12.5 40t30.5 48l615 778q23 29 45 40t65 11h148q27 0 36 -6t9 -23q0 -10 -3 -26.5t-7 -40.5l-144 -742h133q33 0 33 -18q0 -12 -10 -63l-10 -48q-4 -20 -12.5 -25t-32.5 -5h-134l-55 -285q-4 -25 -15 -34t-36 -9h-102q-33 0 -33 23q0 8 3 22t7 41 l47 242h-567q-31 0 -31 28zM244 487h389l125 641h-6z" />
<glyph unicode="5" d="M20 68q0 10 4.5 25.5t8.5 31.5l12 43q10 29 27 29q8 0 17 -3.5t20 -7.5q63 -20 129.5 -31.5t138.5 -11.5q104 0 179 30t120 110q23 45 37 103t14 124q0 176 -178 176q-66 0 -131.5 -19.5t-130.5 -48.5q-20 -10 -31 -10q-12 0 -27 8l-43 27q-18 12 -18 27q0 8 3 21.5 t11 41.5l146 551q6 23 16 32t37 9h580q29 0 32.5 -10t-0.5 -35l-16 -78q-4 -23 -12.5 -32t-36.5 -9h-430q-16 0 -22.5 -5t-10.5 -21l-92 -344q53 25 114.5 39t126.5 14q139 0 224.5 -80t85.5 -234q0 -168 -66 -305q-61 -129 -181 -186t-296 -57q-45 0 -92 5t-91 12t-83 17.5 t-66 22.5q-29 12 -29 29z" />
<glyph unicode="6" d="M86 371q0 154 28.5 300t67.5 269q68 217 204 310t343 93q72 0 145.5 -14t126.5 -41q23 -12 23 -24q0 -16 -16 -56l-19 -45q-12 -27 -26 -26q-8 0 -17.5 3t-15.5 5q-41 16 -93.5 27.5t-105.5 11.5q-150 0 -234.5 -65.5t-134.5 -219.5q-10 -29 -17 -60.5t-15 -66.5 q59 35 142 54.5t157 19.5q158 0 243.5 -79t85.5 -241q0 -98 -16 -171.5t-47 -135.5q-59 -123 -164.5 -180t-253.5 -57q-201 0 -296 105.5t-95 283.5zM270 387q0 -117 48.5 -183.5t170.5 -66.5q82 0 140.5 34t95.5 108q23 47 35 107t12 126q0 84 -42 131t-138 47 q-76 0 -156 -22.5t-141 -57.5q-10 -61 -17.5 -117.5t-7.5 -105.5z" />
<glyph unicode="7" d="M78 20q0 10 10 26.5t35 51.5l737 1063h-598q-29 0 -29 21q0 8 2.5 20t6.5 27l14 65q4 18 14.5 24.5t32.5 6.5h776q33 0 33 -27q0 -12 -6 -43l-8 -36q-4 -12 -8.5 -24.5t-13.5 -28t-22.5 -35t-33.5 -50.5l-715 -1036q-18 -27 -36.5 -36t-49.5 -9h-117q-25 0 -24 20z" />
<glyph unicode="8" d="M47 303q0 59 10.5 102.5t26.5 83.5q35 78 100.5 137.5t143.5 94.5q-49 35 -82 98.5t-33 147.5q0 47 11.5 94t31.5 86q49 94 146.5 145t252.5 51q182 0 275.5 -80.5t93.5 -215.5q0 -57 -15.5 -115t-47.5 -105q-33 -47 -78 -87t-105 -68q90 -49 131 -118t41 -161 q0 -66 -17.5 -127t-47.5 -108q-66 -98 -170.5 -137t-251.5 -39q-199 0 -307.5 87t-108.5 234zM236 338q0 -98 64.5 -149.5t174.5 -51.5q86 0 148.5 23.5t101.5 79.5q18 27 27.5 62.5t9.5 72.5t-9.5 63.5t-30 49t-53 42.5t-77.5 43q-37 18 -60.5 29.5t-39 19t-23.5 12.5 t-16 9q-66 -29 -122.5 -83t-76.5 -116q-18 -59 -18 -106zM399 985q0 -72 36 -115t122 -86l82 -41q59 29 97 62t59 67q16 29 28.5 65t12.5 83q0 86 -52.5 127t-144.5 41q-78 0 -128 -22.5t-79 -65.5q-33 -52 -33 -115z" />
<glyph unicode="9" d="M66 59q0 10 5 22.5t11 31.5l16 47q8 27 27 26q6 0 18 -4q41 -16 105.5 -28.5t136.5 -12.5q121 0 202 53.5t124 174.5q14 39 27.5 86t21.5 96q-61 -35 -138 -53.5t-157 -18.5q-164 0 -253 81t-89 235q0 92 14.5 163.5t30.5 112.5q29 76 73 128t101 84t126 46t144 14 q182 0 278.5 -104t96.5 -307q0 -186 -26.5 -347t-86.5 -300q-61 -145 -176.5 -224t-312.5 -79q-86 0 -163 15t-134 40q-23 10 -22 22zM311 811q0 -176 193 -176q78 0 150.5 20.5t129.5 59.5q8 47 11.5 96t3.5 94q0 141 -45 212t-154 71q-100 0 -162.5 -40t-97.5 -138 q-12 -35 -20.5 -84t-8.5 -115z" />
<glyph unicode=":" horiz-adv-x="479" d="M19 57q0 14 2 30.5t8 45.5q25 100 129 100h16q47 0 71.5 -20t24.5 -55q0 -14 -2 -30.5t-8 -45.5q-12 -53 -43 -76.5t-86 -23.5h-16q-47 0 -71.5 20.5t-24.5 54.5zM168 837q0 14 2 30.5t8 45.5q25 100 129 100h16q47 0 71.5 -20t24.5 -55q0 -14 -2 -30.5t-8 -45.5 q-12 -53 -43 -76.5t-86 -23.5h-16q-47 0 -71.5 20.5t-24.5 54.5z" />
<glyph unicode=";" horiz-adv-x="464" d="M-64 -217q0 8 13 20q72 76 95 110t23 58q0 12 -4 18.5t-12 12.5l-14 10q-31 25 -31 70q0 66 28.5 108.5t100.5 42.5h18q47 0 73 -23.5t26 -76.5q0 -86 -49.5 -179t-170.5 -214q-14 -14 -22 -14q-10 0 -25 10l-28 20q-20 14 -21 27zM150 837q0 14 2 30.5t8 45.5 q25 100 129 100h16q47 0 71.5 -20t24.5 -55q0 -14 -2 -30.5t-8 -45.5q-12 -53 -43 -76.5t-86 -23.5h-16q-47 0 -71.5 20.5t-24.5 54.5z" />
<glyph unicode="&#x3c;" d="M113 492q0 10 6 40l4 25q4 18 11 28.5t28 20.5l737 359q16 8 24.5 10t14.5 2q10 0 15 -6t14 -21l24 -51q14 -29 15 -45q0 -16 -10.5 -24.5t-28.5 -18.5l-578 -287l451 -278q27 -16 26 -33q0 -16 -32 -57l-29 -37q-20 -27 -37 -27q-14 0 -35 12l-598 361q-23 12 -22 27z " />
<glyph unicode="=" d="M14 258q0 16 9 57l8 47q8 47 59 48h801q41 0 41 -27q0 -16 -8 -57l-9 -47q-8 -47 -59 -48h-801q-41 0 -41 27zM98 680q0 16 8 57l9 47q8 47 59 47h801q41 0 41 -26q0 -16 -8 -57l-9 -48q-8 -47 -59 -47h-801q-41 0 -41 27z" />
<glyph unicode="&#x3e;" d="M33 215q0 16 10 24.5t29 18.5l577 287l-450 278q-27 16 -27 33q0 16 33 57l28 37q12 16 19.5 21.5t17.5 5.5q12 0 35 -12l598 -361q23 -12 23 -26q0 -10 -6 -41l-5 -25q-4 -18 -11 -28.5t-27 -20.5l-738 -359q-16 -8 -24.5 -10t-14.5 -2q-10 0 -15 6t-13 21l-25 51 q-14 29 -14 45z" />
<glyph unicode="?" horiz-adv-x="911" d="M96 59q0 18 2 36t6 32q14 57 47 77.5t87 20.5h10q94 0 94 -78q0 -18 -2 -35.5t-6 -31.5q-14 -57 -47 -77.5t-86 -20.5h-11q-94 0 -94 77zM170 1255q0 16 23 25q74 27 172 45t188 18q188 0 274 -69.5t86 -202.5q0 -66 -14 -120t-49 -103t-92.5 -93t-141.5 -87l-100 -52 q-39 -20 -67.5 -38.5t-48 -42t-32 -52t-18.5 -69.5q-2 -10 -4 -24.5t-4 -29.5q-6 -37 -49 -36h-86q-31 -1 -31 26q0 29 6 66q10 59 27.5 107t45.5 86t67.5 71t97.5 61l100 52q113 57 152 116.5t39 132.5q0 80 -42 112t-145 32q-61 0 -133 -14.5t-135 -36.5q-20 -8 -37 -9 q-18 0 -24 23l-13 51q-4 20 -8 32.5t-4 22.5z" />
<glyph unicode="@" horiz-adv-x="1681" d="M86 502q0 184 61.5 346t177 281.5t282.5 189.5t380 70q135 0 249 -34t195.5 -101.5t128 -169t46.5 -236.5q0 -119 -30 -222.5t-87 -180t-140 -120.5t-190 -44q-178 0 -209 129q-29 -53 -86 -79t-131 -26q-102 0 -155.5 46t-53.5 134q0 51 11.5 121t31.5 156 q33 133 97.5 195.5t181.5 62.5q76 0 122 -22.5t66 -63.5h4l15 37q8 25 34 24h78q25 0 25 -16t-12 -74l-62 -317q-4 -23 -10 -55.5t-6 -61.5q0 -72 82 -72q63 0 112 37t82 96.5t49.5 136t16.5 156.5q0 111 -34 190t-96.5 130t-149.5 74.5t-191 23.5q-160 0 -289 -53 t-219 -148.5t-138.5 -227.5t-48.5 -292q0 -227 121 -332.5t363 -105.5q43 0 91 4t96 11t92 15.5t79 16.5q12 4 22.5 6t20.5 2q8 0 13.5 -5t9.5 -25l6 -35q4 -20 5 -28.5t1 -14.5q0 -18 -29 -27q-92 -27 -200.5 -44t-229.5 -17q-170 0 -292.5 37.5t-202.5 110.5t-118 176.5 t-38 234.5zM702 504q0 -41 18.5 -57.5t61.5 -16.5q61 0 100.5 35t57.5 133l21 107q6 35 12 64.5t6 51.5q0 74 -94 74q-55 0 -88 -29.5t-49 -111.5q-16 -80 -31 -145.5t-15 -104.5z" />
<glyph unicode="A" horiz-adv-x="1155" d="M-86 31q0 14 10 35.5t27 56.5q66 137 143.5 296t157.5 315.5t154.5 296.5t134.5 241q18 33 38.5 43t57.5 10h106q47 0 64.5 -14.5t26.5 -55.5q23 -131 42 -275t36.5 -287.5t30.5 -275.5t24 -239q4 -47 7 -84t3 -59q0 -35 -43 -35h-107q-33 0 -45 8t-14 35q-4 78 -10 160 t-13 159h-473q-37 -76 -75.5 -160.5t-71.5 -158.5q-12 -29 -27.5 -36t-50.5 -7h-88q-27 0 -36 7t-9 24zM350 528h383q-4 68 -10 144t-14.5 152.5t-16.5 153.5t-16 148h-27q-70 -131 -143.5 -275t-155.5 -323z" />
<glyph unicode="B" horiz-adv-x="1093" d="M27 55q0 16 3 33.5t9 48.5l223 1137q6 29 18.5 39t41.5 14q49 6 117.5 11t158.5 5t166 -11t131 -43t86 -86t31 -140q0 -123 -60.5 -222.5t-183.5 -135.5v-5q98 -16 152.5 -73.5t54.5 -167.5q0 -242 -147.5 -350.5t-444.5 -108.5q-39 0 -82 1t-85 3t-81 5t-67 7 q-25 4 -33 13.5t-8 25.5zM250 195q0 -25 31 -27q29 -4 62.5 -6t74.5 -2q188 0 267 73.5t79 208.5q0 96 -62.5 132t-205.5 36h-166l-70 -352q-4 -23 -7 -36t-3 -27zM360 768h115q158 0 242 67.5t84 202.5q0 78 -46 112t-157 34q-39 0 -74.5 -1t-60.5 -5q-23 -2 -29 -31z" />
<glyph unicode="C" d="M80 332q0 76 18.5 205t53.5 301q33 168 82 260t116 145q63 51 147.5 75.5t211.5 24.5q86 0 172 -20t159 -55q25 -12 25 -27q0 -12 -4 -21.5t-14 -27.5l-35 -70q-10 -18 -23 -18q-8 0 -17 4t-20 8q-51 25 -116.5 41.5t-137.5 16.5q-86 0 -144 -22t-98 -68t-67 -120.5 t-47 -179.5q-33 -162 -46 -268t-13 -174q0 -49 11 -86t38.5 -60.5t75 -36t118.5 -12.5q66 0 145 14.5t130 31.5q31 10 43 10q16 0 24 -29l9 -35q12 -55 12 -65q0 -20 -29 -31q-78 -25 -178 -43t-186 -18q-111 0 -189 17t-129 59t-74.5 108.5t-23.5 165.5z" />
<glyph unicode="D" horiz-adv-x="1216" d="M18 37q0 8 4.5 26.5t10.5 47.5l223 1151q4 29 17.5 41t39.5 16q55 8 141.5 12t163.5 4q276 0 397.5 -97t121.5 -308q0 -37 -4.5 -86t-12.5 -105.5t-19 -116t-26 -116.5q-37 -141 -99.5 -237.5t-153.5 -157t-214 -86t-282 -25.5h-248q-59 0 -60 37zM246 182q0 -20 28 -20 h95q113 0 196.5 20.5t144 64.5t101.5 115.5t70 176.5q29 106 42 203.5t13 162.5q0 78 -17.5 130t-56.5 83t-98.5 43.5t-145.5 12.5h-79.5t-71.5 -5q-31 -2 -37 -34l-172 -885q-6 -29 -9 -43t-3 -25z" />
<glyph unicode="E" horiz-adv-x="999" d="M45 180q0 29 4 79t21 132q35 178 69.5 352.5t73.5 341.5q16 74 39.5 121t60.5 73t89.5 36t125.5 10h451q29 0 29 -20q0 -8 -3.5 -23.5t-7.5 -44.5l-8 -43q-6 -35 -47 -35h-430q-82 0 -102 -78q-18 -72 -36 -156.5t-34 -158.5h485q29 0 29 -21q0 -8 -3 -23t-7 -44l-8 -43 q-6 -35 -48 -35h-481q-12 -63 -23.5 -122.5t-19.5 -110t-13 -86t-5 -52.5q0 -35 17.5 -49t70.5 -14h450q29 0 29 -21q0 -8 -3 -23.5t-7 -43.5l-8 -43q-6 -35 -47 -35h-498q-98 0 -151.5 41t-53.5 139z" />
<glyph unicode="F" horiz-adv-x="956" d="M16 23q0 10 2 19t7 32l202 1030q12 68 33 109.5t59 70.5q53 41 175 41h454q29 0 29 -20q0 -8 -3 -25.5t-7 -42.5l-9 -43q-6 -35 -47 -35h-385q-27 0 -45 -3t-32 -15q-20 -14 -31 -74l-60 -307h467q29 0 29 -21q0 -8 -3 -23.5t-7 -43.5l-8 -43q-6 -35 -48 -35h-462 l-111 -561q-4 -18 -13 -25.5t-34 -7.5h-117q-35 0 -35 23z" />
<glyph unicode="G" horiz-adv-x="1187" d="M80 340q0 47 4 98t13 109.5t22.5 128t32.5 155.5q16 76 33.5 137.5t42 111.5t56 89t74.5 74q131 100 375 100q104 0 196.5 -22.5t170.5 -63.5q18 -10 18 -24t-16 -43l-39 -72q-10 -18 -27 -18q-10 0 -24 8q-61 33 -133 51.5t-146 18.5q-96 0 -161.5 -24t-109.5 -72 t-72 -121.5t-48 -176.5q-33 -162 -47 -260t-14 -149q0 -63 13 -106.5t44 -70t82 -39t129 -12.5q47 0 94 4.5t82 10.5q43 6 49 41l68 358h-158q-31 0 -31 23q0 8 2 22.5t7 30.5l12 61q6 29 37 29h331q27 0 27 -20q0 -10 -5 -34t-11 -55l-105 -538q-4 -23 -13 -32t-34 -17 q-72 -23 -170 -36t-196 -13q-117 0 -203 18t-141.5 60t-83 111t-27.5 169z" />
<glyph unicode="H" horiz-adv-x="1251" d="M18 25q0 12 9 53l237 1204q4 27 16.5 35t36.5 8h115q33 0 33 -25q0 -12 -8 -53l-97 -487h568l102 522q4 27 16.5 35t36.5 8h117q33 0 33 -25q0 -12 -8 -53l-238 -1204q-4 -27 -16.5 -35t-36.5 -8h-115q-33 0 -33 25q0 12 9 53l100 516h-567l-109 -551q-4 -27 -16.5 -35 t-36.5 -8h-115q-33 0 -33 25z" />
<glyph unicode="I" horiz-adv-x="505" d="M29 25q0 12 8 53l235 1204q4 27 16.5 35t37.5 8h114q33 0 33 -25q0 -12 -8 -53l-236 -1204q-4 -27 -16 -35t-37 -8h-115q-33 0 -32 25z" />
<glyph unicode="J" horiz-adv-x="671" d="M-76 18q0 8 3 22.5t7 37.5l13 59q4 18 12 24.5t25 6.5h82q57 0 90.5 14.5t56.5 42.5q20 27 33.5 71t29.5 130l168 856q4 27 16.5 35t37.5 8h116q33 0 33 -25q0 -12 -8 -53l-156 -788q-14 -76 -26.5 -130.5t-25.5 -93t-26.5 -66.5t-31.5 -50q-53 -68 -127 -93.5 t-176 -25.5h-115q-18 0 -24.5 5t-6.5 13z" />
<glyph unicode="K" horiz-adv-x="1083" d="M18 25q0 12 9 53l235 1204q4 27 16.5 35t36.5 8h115q33 0 33 -25q0 -12 -8 -53l-97 -487h86q45 0 72 33l426 501q16 18 29.5 24.5t36.5 6.5h133q27 0 26 -18q0 -10 -12 -26.5t-41 -51.5l-442 -518q-14 -16 -21.5 -26.5t-7.5 -20.5q0 -12 7 -25.5t18 -36.5l262 -514 q18 -37 23 -49t5 -19q0 -20 -28 -20h-131q-25 0 -35 6t-21 27l-256 528q-10 23 -21 28t-38 5h-102l-109 -551q-4 -27 -16.5 -35t-36.5 -8h-113q-33 0 -33 25z" />
<glyph unicode="L" horiz-adv-x="815" d="M45 115q0 29 4 60.5t12 72.5l203 1034q4 27 16.5 35t36.5 8h115q33 0 33 -25q0 -12 -8 -53l-191 -964q-6 -29 -10 -50.5t-4 -35.5q0 -16 8 -23.5t37 -7.5h348q29 0 29 -21q0 -8 -3 -23.5t-7 -43.5l-9 -43q-6 -35 -47 -35h-420q-66 0 -104.5 27.5t-38.5 87.5z" />
<glyph unicode="M" horiz-adv-x="1570" d="M-6 29q0 10 3 22t9 39q72 311 143.5 603t145.5 567q10 37 29.5 51t66.5 14h94q53 0 70.5 -11t24.5 -54q39 -266 74.5 -482.5t68.5 -396.5h10q117 213 240 434t241 451q20 37 44 48t71 11h131q31 0 43.5 -11t12.5 -36q0 -23 -4.5 -58.5t-8.5 -66.5q-35 -268 -77 -547.5 t-87 -564.5q-4 -25 -14 -33t-37 -8h-110q-37 0 -37 29q0 10 2 25.5t8 49.5q43 252 85 499t79 478h-12q-106 -203 -227.5 -424t-237.5 -426q-27 -43 -86 -43h-107q-33 0 -47 13.5t-20 44.5q-16 86 -34 186t-36.5 208t-35.5 219.5t-32 221.5h-14q-57 -236 -117.5 -507 t-115.5 -533q-4 -25 -14.5 -33t-37.5 -8h-110q-37 0 -37 29z" />
<glyph unicode="N" horiz-adv-x="1273" d="M18 25q0 12 5.5 34.5t11.5 55.5l229 1169q4 27 17.5 34t46.5 7h129q31 0 44 -9t21 -34l336 -1014h10l199 1018q4 23 14.5 31t34.5 8h105q35 0 34 -20q0 -12 -4 -30t-8 -44l-233 -1190q-4 -25 -17.5 -33t-40.5 -8h-127q-33 0 -48 8t-21 29l-340 1014h-10l-199 -1012 q-4 -23 -14.5 -31t-34.5 -8h-109q-31 0 -31 25z" />
<glyph unicode="O" horiz-adv-x="1234" d="M80 336q0 86 16.5 199.5t44.5 255.5q33 162 80 268t118 169.5t168 89t232 25.5q197 0 306.5 -84.5t109.5 -273.5q0 -74 -17 -198t-48 -261q-35 -152 -84.5 -256t-119 -168.5t-164.5 -92t-222 -27.5q-205 0 -312.5 86t-107.5 268zM283 365q0 -111 53 -164.5t180 -53.5 q82 0 140.5 19.5t101.5 64.5t74.5 120t56.5 186q14 59 25.5 118.5t20.5 114.5t14 102t5 82q0 123 -55 173.5t-184 50.5q-145 0 -217 -60q-29 -23 -51.5 -53.5t-43 -73.5t-37 -102.5t-32.5 -138.5q-23 -109 -37 -205t-14 -180z" />
<glyph unicode="P" horiz-adv-x="1079" d="M18 25q0 14 9 49l235 1202q8 41 47 47q59 10 140 15t180 5q197 0 311.5 -70.5t114.5 -232.5q0 -49 -10.5 -120.5t-32.5 -137.5q-53 -152 -180 -224.5t-357 -72.5h-85t-83 2l-90 -454q-4 -18 -13 -25.5t-34 -7.5h-111q-41 0 -41 25zM340 655q41 -2 73.5 -2h71.5 q141 0 223.5 44t116.5 149q16 49 22.5 90t6.5 70q0 102 -61.5 137t-171.5 35q-55 0 -90 -2.5t-66 -8.5q-14 -4 -21.5 -11t-11.5 -26z" />
<glyph unicode="Q" horiz-adv-x="1234" d="M80 336q0 86 16.5 199.5t44.5 255.5q33 162 80 268t118 169.5t168 89t232 25.5q197 0 306.5 -84.5t109.5 -273.5q0 -74 -17 -198t-48 -261q-29 -123 -66 -214t-87 -155.5t-114.5 -102.5t-146.5 -56q41 -59 104.5 -126t130.5 -114q16 -10 17 -24q0 -14 -21 -27l-84 -53 q-27 -16 -41 -16q-20 0 -37 14q-74 63 -138 150t-111 180q-203 2 -309.5 87t-106.5 267zM283 365q0 -111 53 -164.5t180 -53.5q82 0 140.5 19.5t101.5 64.5t74.5 120t56.5 186q14 59 25.5 118.5t20.5 114.5t14 102t5 82q0 123 -55 173.5t-184 50.5q-145 0 -217 -60 q-29 -23 -51.5 -53.5t-43 -73.5t-37 -102.5t-32.5 -138.5q-23 -109 -37 -205t-14 -180z" />
<glyph unicode="R" horiz-adv-x="1091" d="M20 39l240 1229q6 31 19.5 43t46.5 16q49 8 124.5 12t161.5 4q223 0 326.5 -68.5t103.5 -223.5q0 -90 -22 -181q-70 -254 -307 -303l176 -428q23 -53 33 -77.5t10 -36.5q0 -25 -35 -25h-123q-29 0 -43 9t-24 36l-177 473q-6 16 -13 20.5t-23 4.5h-89.5t-87.5 2l-98 -506 q-4 -25 -15 -32t-36 -7h-115q-23 0 -30 8t-3 31zM350 711q39 -2 75 -3t75 -1q137 0 209.5 39.5t107.5 148.5q10 29 14.5 65.5t4.5 65.5q0 90 -58.5 121t-175.5 31q-51 0 -80.5 -1.5t-52.5 -5.5q-16 -2 -23.5 -8t-11.5 -28z" />
<glyph unicode="S" horiz-adv-x="1019" d="M-2 72q0 10 3 22t9 39l13 47q8 31 28 31q10 0 18.5 -3t18.5 -7q63 -25 142 -40.5t163 -15.5q113 0 180.5 34t94.5 114q22 60 22 124v5q0 66 -32.5 96.5t-127.5 52.5l-131 33q-72 18 -121 43t-78.5 59.5t-42 82t-12.5 108.5q0 51 11.5 107.5t29.5 107.5q43 121 148.5 176 t288.5 55q94 0 176 -14t143 -33q25 -8 25 -26q0 -10 -3 -22.5t-14 -41.5l-18 -53q-10 -25 -29 -25q-10 0 -20.5 2.5t-20.5 6.5q-43 14 -113.5 26.5t-138.5 12.5q-113 0 -167 -31t-78 -103q-10 -25 -15.5 -59.5t-5.5 -75.5q0 -51 29.5 -79.5t113.5 -51.5l138 -35 q78 -20 129 -43.5t81.5 -56.5t44 -77t13.5 -103q0 -41 -9.5 -96.5t-27.5 -110.5q-49 -150 -158 -211t-297 -61q-115 0 -212 17t-160 42q-31 10 -31 31z" />
<glyph unicode="T" horiz-adv-x="966" d="M125 1180q0 10 3 24.5t7 38.5l8 43q4 23 13.5 31t31.5 8h842q29 0 29 -22q0 -14 -10 -62l-9 -45q-4 -23 -13 -30t-32 -7h-319l-219 -1120q-4 -25 -15.5 -32t-35.5 -7h-115q-35 0 -35 20q0 8 2 19.5t6 28.5l215 1091h-325q-29 0 -29 21z" />
<glyph unicode="U" horiz-adv-x="1226" d="M74 291q0 29 6 86t22 135l148 770q4 27 16.5 35t36.5 8h117q33 0 33 -25q0 -12 -9 -53l-145 -760q-10 -55 -18.5 -98t-8.5 -78q0 -84 58.5 -124t193.5 -40q70 0 123 16.5t93 52.5t67 94.5t43 142.5l162 829q4 27 16 35t37 8h117q33 0 32 -25q0 -12 -8 -53l-157 -809 q-25 -127 -65 -214t-104.5 -140t-156.5 -77.5t-221 -24.5q-211 0 -319.5 76.5t-108.5 232.5z" />
<glyph unicode="V" horiz-adv-x="1085" d="M145 1282q0 23 11.5 33t40.5 10h98q25 0 36 -7t13 -34q12 -262 35.5 -538.5t58.5 -536.5h19q135 256 268 534.5t248 540.5q10 27 24.5 34t38.5 7h109q37 0 37 -22q0 -12 -7.5 -30t-17.5 -40q-61 -135 -131 -280.5t-143.5 -295t-150.5 -299t-150 -290.5q-20 -39 -43 -53.5 t-66 -14.5h-113q-43 0 -60 14.5t-24 53.5q-23 137 -43 285.5t-36.5 304t-29.5 313t-22 311.5z" />
<glyph unicode="W" horiz-adv-x="1634" d="M137 1284q0 23 11.5 32t39.5 9h97q29 0 40 -7t11 -34q-3 -225 -3 -447v-111q1 -277 9 -525h16q51 115 104.5 239.5t107 252.5t104.5 255t98 246q14 35 31.5 48t56.5 13h115q39 0 55.5 -13t16.5 -48q4 -248 11 -506t19 -487h17q51 123 107 260t113.5 278.5t111 279.5 t98.5 265q10 27 24.5 34t38.5 7h115q35 0 35 -20q0 -12 -6.5 -31t-18.5 -49q-53 -133 -113.5 -280.5t-124 -298t-129 -299t-126.5 -284.5q-16 -39 -41 -51t-66 -12h-113q-41 0 -60 12.5t-24 50.5q-16 223 -23 468q-4 156 -4 306q0 86 1 169h-15q-92 -229 -190 -462 t-209 -481q-16 -39 -41.5 -51t-66.5 -12h-121q-41 0 -60.5 12.5t-21.5 50.5q-6 111 -11.5 250.5t-8.5 297t-5 328.5t-2 345z" />
<glyph unicode="X" horiz-adv-x="1105" d="M-82 23q0 10 11.5 27.5t27.5 39.5l344 522q25 37 35 55.5t10 30.5q0 18 -5 35t-15 45l-140 449q-8 27 -13 45t-5 28q0 25 37 25h110q29 0 40.5 -7t17.5 -30l123 -471q10 -35 20 -45t27 -10h18q20 0 36.5 15.5t51.5 68.5l293 448q12 18 21.5 24.5t31.5 6.5h131 q31 0 31 -18q0 -8 -8 -20.5t-37 -57.5l-317 -469q-29 -43 -39.5 -61.5t-10.5 -34.5t5.5 -35t15.5 -51l168 -482q10 -29 15 -45t5 -26q0 -25 -39 -25h-108q-23 0 -34 6t-17 25l-162 514q-10 33 -20.5 42t-30.5 9h-17q-23 0 -36 -11.5t-31 -39.5l-314 -504q-16 -25 -32.5 -33 t-51.5 -8h-112q-31 0 -31 23z" />
<glyph unicode="Y" horiz-adv-x="974" d="M141 1294q0 31 43 31h107q23 0 33 -5t12 -28q16 -182 47 -345t76 -324h14q111 162 211 330.5t186 338.5q10 23 21.5 28t34.5 5h123q27 0 26 -18q0 -10 -6 -22.5t-10 -22.5q-211 -424 -522 -832l-78 -397q-4 -18 -13.5 -25.5t-33.5 -7.5h-117q-35 0 -35 23q0 10 2 19t6 32 l72 364q-74 190 -120 390t-75 407q-2 10 -3 28.5t-1 30.5z" />
<glyph unicode="Z" horiz-adv-x="1026" d="M-47 37l6 47q4 23 13.5 41t43.5 61l783 973h-572q-35 0 -34 25q0 16 10 63l8 39q4 23 14.5 31t34.5 8h772q33 0 33 -27q0 -10 -2 -24t-6 -27q-10 -39 -23.5 -63.5t-46.5 -65.5l-768 -952h584q29 0 28 -23q0 -6 -2 -18t-6 -33l-10 -55q-4 -20 -13 -28.5t-34 -8.5h-774 q-40 0 -40 29q0 4 1 8z" />
<glyph unicode="[" horiz-adv-x="614" d="M-18 -154q0 10 2 24.5t8 47.5l276 1421q8 39 49 39h345q25 0 24 -16q0 -8 -3 -22.5t-7 -36.5l-12 -62q-4 -18 -13.5 -23.5t-32.5 -5.5h-192l-238 -1226h201q27 0 27 -19q0 -12 -10 -59l-13 -60q-4 -16 -13 -22t-32 -6h-334q-33 0 -32 26z" />
<glyph unicode="\" horiz-adv-x="690" d="M174 1376q0 14 9.5 22.5t33.5 8.5h94q27 0 37 -8t14 -33l148 -1315q6 -59 6 -69q0 -14 -9 -22.5t-34 -8.5h-94q-27 0 -37 8t-14 33l-148 1315q-6 57 -6 69z" />
<glyph unicode="]" horiz-adv-x="614" d="M-111 -164q0 8 3.5 22.5t7.5 37.5l12 61q4 18 13 23.5t32 5.5h193l237 1226h-201q-27 0 -26 19q0 12 10 59l12 60q4 16 13.5 22t31.5 6h334q33 0 33 -26q0 -10 -2 -24.5t-8 -47.5l-277 -1421q-8 -39 -49 -39h-344q-25 0 -25 16z" />
<glyph unicode="^" d="M115 705q0 12 18 32l379 451q23 23 34 28t44 5h90q31 0 43 -6.5t22 -26.5l232 -475q8 -16 8 -29q0 -10 -8 -18.5t-31 -20.5l-45 -24q-25 -14 -39 -15q-20 0 -33 27l-206 383h-13l-342 -381q-23 -29 -43 -29q-16 0 -37 19l-47 41q-16 16 -21 23t-5 16z" />
<glyph unicode="_" horiz-adv-x="1067" d="M-94 -143q0 8 1 17t5 26l6 32q4 23 15.5 32t39.5 9h844q39 0 39 -24q0 -10 -6 -43l-6 -33q-4 -23 -15.5 -32t-40.5 -9h-843q-39 0 -39 25z" />
<glyph unicode="`" horiz-adv-x="694" d="M180 1341q0 14 17 54l22 53q10 23 16.5 29t16.5 6t43 -17l369 -178q23 -10 22 -22q0 -8 -4 -17.5t-10 -23.5l-21 -43q-10 -20 -24 -21q-12 0 -27 6l-393 146q-27 8 -27 28z" />
<glyph unicode="a" horiz-adv-x="968" d="M29 195q0 41 9 94t19 96q35 133 132.5 182t267.5 49h73q31 0 69 -2t67 -6l10 51q6 35 10 62t4 53q0 51 -36 70.5t-126 19.5q-53 0 -116.5 -9t-116.5 -24q-18 -4 -28.5 -7t-18.5 -3q-12 0 -16.5 7.5t-8.5 23.5l-8 47q-6 33 -6 43q0 25 31 33q68 20 155.5 33.5t179.5 13.5 q143 0 226.5 -46t83.5 -159q0 -29 -5.5 -67.5t-13.5 -79.5l-125 -641q-4 -16 -11 -22.5t-25 -6.5h-82q-18 0 -24.5 7t-6.5 24v61h-4q-43 -59 -108.5 -84.5t-164.5 -25.5h-30q-129 0 -192.5 57t-63.5 156zM211 221q0 -45 30.5 -65.5t100.5 -20.5h23q100 0 162.5 43t82.5 148 l25 129q-31 4 -62.5 6t-66.5 2h-60q-51 0 -86.5 -6t-61.5 -19.5t-42 -37t-27 -58.5q-10 -31 -14 -63.5t-4 -57.5z" />
<glyph unicode="b" horiz-adv-x="1030" d="M59 229q0 41 6.5 90.5t20.5 116.5l178 914q6 36 43 36h2h109q37 0 37 -24q0 -8 -5.5 -33.5t-11.5 -56.5l-63 -332h6q41 35 106.5 58.5t151.5 23.5q147 0 227 -64.5t80 -201.5q0 -37 -10 -132.5t-45 -244.5q-25 -106 -62.5 -182t-95 -123t-134.5 -69.5t-179 -22.5 q-193 0 -277 60t-84 187zM250 254q0 -59 41 -89t139 -30q117 0 179.5 62.5t90.5 199.5q27 129 40.5 210t13.5 122q0 137 -164 137q-133 0 -194.5 -61.5t-88.5 -202.5l-35 -184q-10 -55 -16 -90t-6 -74z" />
<glyph unicode="c" horiz-adv-x="901" d="M55 233q0 35 6.5 83.5t14.5 101.5t18 104t19 92q43 205 146 306.5t312 101.5q72 0 148 -19.5t135 -52.5q20 -12 20 -26q0 -10 -16 -41l-33 -62q-6 -10 -10 -15t-14 -5q-8 0 -33 12q-47 23 -100.5 36t-96.5 13q-61 0 -107 -15.5t-79 -51t-55.5 -95t-40.5 -147.5 q-6 -37 -14.5 -76t-14.5 -77t-10 -72.5t-4 -59.5q0 -72 46 -99.5t148 -27.5q51 0 109.5 9.5t103.5 23.5q6 2 13.5 4t15.5 2q14 0 20 -24l9 -39q4 -23 7 -36t3 -24q0 -18 -25 -26q-59 -23 -138 -36t-150 -13q-182 0 -267.5 61t-85.5 190z" />
<glyph unicode="d" horiz-adv-x="1021" d="M55 246q0 59 17.5 153.5t36.5 198.5q25 129 61.5 212t87.5 130t118.5 64.5t153.5 17.5t149.5 -25.5t96.5 -64.5h4l82 418q4 23 13.5 29.5t33.5 6.5h109q35 0 35 -22v-4t-2 -10.5t-4.5 -23.5t-8.5 -42l-178 -907q-23 -115 -57.5 -190.5t-90 -122t-133 -64.5t-188.5 -18 q-336 0 -336 264zM246 276q0 -72 36.5 -105.5t137.5 -33.5q57 0 99 12.5t72 41t49 74.5t32 112l43 219q6 31 11 65.5t5 59.5q0 68 -44 107.5t-146 39.5q-53 0 -92 -13t-68 -46t-49.5 -89t-36.5 -142q-23 -111 -36 -182.5t-13 -119.5z" />
<glyph unicode="e" horiz-adv-x="966" d="M55 252q0 59 15.5 148t44.5 233q20 100 54 173t88 121t130 71.5t178 23.5q160 0 240 -72.5t80 -197.5q0 -66 -12.5 -143t-28.5 -154q-6 -27 -19.5 -38t-46.5 -11h-518q-6 -35 -12 -65t-6 -62q0 -82 54 -110t159 -28q57 0 124.5 11.5t120.5 27.5q6 2 13.5 4t15.5 2 q14 0 21 -24l12 -51q8 -31 8 -45q0 -16 -20 -25q-61 -25 -152.5 -42t-173.5 -17q-104 0 -175 18t-114 53t-61.5 85t-18.5 114zM289 555h358q27 0 33 25q10 37 14 77.5t4 73.5q0 74 -37.5 105.5t-123.5 31.5q-96 0 -152.5 -53t-79.5 -176z" />
<glyph unicode="f" horiz-adv-x="645" d="M-25 -342q0 10 4.5 21.5t8.5 27.5q35 111 66.5 246t58.5 283l106 606h-125q-23 0 -22 18q0 12 6 43l8 41q4 18 12 24.5t27 10.5l121 18l12 72q16 94 45 158.5t75 104.5t109.5 56.5t149.5 16.5q53 0 98 -7t80 -18q12 -4 18.5 -10t6.5 -18q0 -10 -3 -22.5t-14 -41.5 l-10 -31q-8 -27 -25 -26q-6 0 -15 2t-17 4q-18 4 -48 8t-65 4q-88 0 -133 -39t-64 -147l-10 -59h211q25 0 25 -17q0 -6 -2 -20.5t-6 -30.5l-13 -64q-4 -16 -12 -23t-31 -7h-200l-109 -617q-35 -201 -67.5 -328.5t-65.5 -218.5q-10 -23 -22.5 -31.5t-39.5 -8.5h-100 q-29 0 -29 20z" />
<glyph unicode="g" horiz-adv-x="1030" d="M55 233q0 72 15.5 174.5t40.5 225.5q20 106 56 181t87 121t121.5 66.5t163.5 20.5q106 0 173.5 -33t96.5 -86h4l25 70q6 18 15 24.5t32 6.5h71q29 0 29 -21q0 -10 -3 -24.5t-7 -40.5l-158 -859q-25 -133 -53.5 -212.5t-79.5 -131.5q-53 -53 -134 -75.5t-183 -22.5 q-78 0 -150 11.5t-121 27.5q-25 6 -24 22q0 10 3 18.5t9 31.5l16 53q8 25 27 24q4 0 20 -4q41 -14 103.5 -22t126.5 -8q111 0 167 50t85 177l16 82h-6q-35 -47 -107.5 -72.5t-169.5 -25.5q-160 0 -233.5 64.5t-73.5 186.5zM246 266q0 -70 36.5 -99.5t120.5 -29.5 q113 0 186 54.5t95 183.5l43 223q8 41 12 69.5t4 55.5q0 70 -48 107.5t-140 37.5q-63 0 -106 -17t-74 -52t-49.5 -87.5t-32.5 -119.5q-18 -88 -32.5 -179t-14.5 -147z" />
<glyph unicode="h" horiz-adv-x="1034" d="M18 25q0 8 5.5 36.5t13.5 63.5l242 1225q6 36 43 36h2h106q37 0 37 -24q0 -8 -6 -39t-12 -61l-62 -316h6q47 35 107.5 55.5t158.5 20.5q137 0 210 -57.5t75 -167.5v-20q0 -41 -6 -85q-8 -56 -23 -129l-102 -526q-6 -37 -45 -37h-109q-37 0 -36 25q0 8 6 39.5t12 60.5 l92 469q10 47 15.5 84t5.5 63q0 68 -43 96.5t-121 28.5q-53 0 -96 -11t-77 -41t-58.5 -83t-41.5 -137l-108 -557q-6 -37 -45 -37h-109q-37 0 -37 25z" />
<glyph unicode="i" horiz-adv-x="454" d="M29 25q0 8 5 36.5t13 63.5l166 842q6 37 45 37h109q37 0 36 -25q0 -8 -5 -37t-13 -63l-164 -842q-6 -37 -45 -37h-110q-37 0 -37 25zM236 1225q0 16 2 32.5t8 34.5q31 96 139 97h10q53 0 78 -24t25 -65q0 -16 -2 -32.5t-9 -34.5q-31 -96 -139 -96h-10q-53 0 -77.5 23.5 t-24.5 64.5z" />
<glyph unicode="j" horiz-adv-x="452" d="M-291 -344q0 8 2 19.5t8 37.5l11 49q8 33 37 33h36q61 0 96 12.5t62 38.5q33 33 48.5 84.5t29.5 120.5l180 916q6 37 45 37h109q37 0 37 -25q0 -8 -5.5 -37t-13.5 -63l-166 -840q-18 -98 -48 -174t-83 -133q-92 -94 -297 -94h-59q-29 0 -29 18zM242 1225q0 16 2 32.5 t8 34.5q31 96 139 97h10q53 0 78 -24t25 -65q0 -16 -2 -32.5t-8 -34.5q-31 -96 -140 -96h-10q-53 0 -77.5 23.5t-24.5 64.5z" />
<glyph unicode="k" horiz-adv-x="913" d="M18 25q0 8 5.5 36.5t13.5 63.5l239 1225q6 36 44 36h2h108q37 0 37 -24q0 -8 -5 -33.5t-13 -66.5l-130 -648h52q39 0 65 33l285 322q20 23 35.5 29t44.5 6h119q27 0 26 -17q0 -8 -8 -18.5t-23 -26.5l-315 -354q-18 -23 -29.5 -35t-11.5 -29q0 -12 9.5 -30.5t19.5 -40.5 l172 -381q10 -23 15 -33t5 -21q0 -18 -30 -18h-101q-31 0 -46 10t-25 35l-168 379q-10 20 -19.5 26.5t-32.5 6.5h-69l-80 -420q-6 -37 -45 -37h-109q-37 0 -37 25z" />
<glyph unicode="l" horiz-adv-x="530" d="M53 164q0 41 13 106l210 1080q6 36 44 36h2h108q37 0 37 -22q0 -10 -5 -35.5t-13 -66.5l-193 -981q-4 -27 -7 -46.5t-3 -37.5q0 -25 14 -34t47 -9h72q29 0 29 -23q0 -10 -3.5 -24.5t-7.5 -38.5l-6 -31q-4 -23 -15 -30t-34 -7h-76q-109 0 -161 36t-52 128z" />
<glyph unicode="m" horiz-adv-x="1617" d="M18 25q0 8 5.5 36.5t13.5 63.5l168 850q6 29 41 29h71q35 0 33 -27l-4 -68h6q55 55 124 84t167 29q109 0 169 -34t89 -91h2q70 59 156 92t178 33q287 0 287 -246q0 -39 -6.5 -81t-18.5 -107l-106 -551q-4 -20 -13.5 -28.5t-31.5 -8.5h-109q-37 0 -37 25q0 8 5 33.5 t14 66.5l90 461q8 41 13 76.5t5 70.5q0 66 -33.5 99.5t-113.5 33.5q-74 0 -136.5 -26.5t-109.5 -73.5q-2 -80 -21 -164l-110 -565q-4 -20 -13.5 -28.5t-31.5 -8.5h-109q-37 0 -37 25q0 8 5.5 34.5t13.5 65.5l98 502q6 33 9 61.5t3 50.5q0 68 -40.5 97.5t-122.5 29.5 q-104 0 -167 -50t-85 -169l-119 -610q-4 -20 -13.5 -28.5t-31.5 -8.5h-109q-37 0 -37 25z" />
<glyph unicode="n" horiz-adv-x="1036" d="M18 25q0 8 5.5 35.5t13.5 64.5l168 850q6 29 41 29h71q18 0 26.5 -4.5t6.5 -20.5l-4 -72h6q51 53 119 84t188 31q137 0 210 -57.5t73 -167.5q0 -43 -8 -104.5t-21 -129.5l-98 -526q-6 -37 -45 -37h-108q-37 0 -37 25q0 8 5 35.5t13 64.5l88 469q8 45 14.5 84t6.5 70 q0 63 -41 90.5t-119 27.5q-53 0 -97 -11t-78 -41t-58.5 -83t-41.5 -137l-110 -557q-4 -20 -13.5 -28.5t-31.5 -8.5h-107q-37 0 -37 25z" />
<glyph unicode="o" horiz-adv-x="1009" d="M55 264q0 53 12.5 140.5t28.5 173.5q25 123 62 207.5t93 137t134 76t184 23.5q186 0 271.5 -68.5t85.5 -214.5q0 -53 -12.5 -140t-28.5 -173q-25 -123 -62 -208t-93 -137t-134 -75.5t-184 -23.5q-186 0 -271.5 68.5t-85.5 213.5zM246 291q0 -86 42 -121t140 -35 q115 0 174.5 65.5t89.5 219.5q23 115 33 185.5t10 107.5q0 86 -42 120.5t-140 34.5q-115 0 -174.5 -65.5t-89.5 -218.5q-23 -115 -33 -185.5t-10 -107.5z" />
<glyph unicode="p" horiz-adv-x="1046" d="M-53 -338q0 8 5 37t13 63l238 1207q4 20 11 27.5t30 7.5h69q37 0 35 -31l-8 -66h4q57 59 132 87t186 28q139 0 220 -63.5t81 -206.5q0 -100 -52 -363q-20 -100 -51 -176t-80 -127t-121.5 -77.5t-175.5 -26.5q-100 0 -162.5 22.5t-103.5 65.5h-2l-78 -396q-6 -36 -43 -36 h-2h-108q-37 0 -37 24zM266 262q0 -53 43 -90t148 -37q61 0 105 15.5t75 49t52.5 87t37.5 131.5q43 199 43 301q0 76 -40 111.5t-136 35.5q-57 0 -101 -12t-78 -42t-57.5 -81t-38.5 -127l-34 -176q-10 -57 -14.5 -89t-4.5 -77z" />
<glyph unicode="q" horiz-adv-x="1026" d="M55 260q0 53 14.5 143.5t39.5 217.5q41 211 136 306t283 95q117 0 183.5 -33t93.5 -86h4l25 70q8 18 18 24.5t27 6.5h75q31 0 31 -25q0 -4 -3 -25.5t-19 -97.5l-232 -1182q-4 -20 -13 -28t-32 -8h-108q-37 0 -37 24q0 8 5 37t13 63l59 304h-6q-76 -84 -258 -84 q-143 0 -221 64.5t-78 213.5zM244 283q0 -82 37.5 -114t121.5 -32q111 0 180.5 56.5t98.5 193.5l41 205q8 41 12 72.5t4 56.5q0 72 -47 109.5t-139 37.5q-55 0 -97 -13t-74 -47t-54.5 -94.5t-40.5 -152.5q-8 -37 -15.5 -77t-13.5 -77.5t-10 -70.5t-4 -53z" />
<glyph unicode="r" horiz-adv-x="669" d="M18 25q0 8 5.5 35.5t13.5 64.5l166 850q4 16 11 22.5t28 6.5h71q20 0 26.5 -6.5t4.5 -22.5l-10 -90h4q45 63 106.5 91t159.5 28h68q29 0 28 -21q0 -8 -3 -25.5t-7 -35.5l-10 -52q-4 -18 -11 -23t-26 -5h-59q-117 0 -180.5 -60.5t-90.5 -206.5l-104 -538 q-4 -20 -13.5 -28.5t-31.5 -8.5h-109q-37 0 -37 25z" />
<glyph unicode="s" horiz-adv-x="854" d="M-8 63q0 14 14 60l14 47q6 23 23 23q8 0 14 -2.5t11 -4.5q57 -23 124.5 -36t126.5 -13q82 0 126 18.5t67 65.5q23 45 23 103q0 37 -24 60.5t-91 41.5l-123 33q-104 29 -145 87t-41 142q0 178 102 256t270 78q72 0 142.5 -11.5t122.5 -29.5q29 -10 28 -29q0 -14 -16 -63 l-12 -39q-6 -25 -25 -25q-10 0 -29 6q-41 12 -100 22.5t-109 10.5q-80 0 -121.5 -17.5t-58.5 -58.5q-8 -18 -11 -39.5t-3 -37.5q0 -41 27.5 -68t93.5 -43l123 -33q102 -29 143 -75t41 -134q0 -16 -2 -38.5t-7 -47t-10.5 -47t-11.5 -41.5q-41 -115 -129 -158.5t-235 -43.5 q-72 0 -157 13t-149 38q-27 10 -26 30z" />
<glyph unicode="t" horiz-adv-x="665" d="M59 856q0 6 7 37l10 51q6 27 24 31l146 22l43 224q6 39 47 39h108q33 0 33 -21q0 -14 -3 -29.5t-9 -50.5l-31 -155h207q27 0 27 -21q0 -12 -9 -51l-12 -60q-4 -18 -11 -24t-26 -6h-207l-96 -488q-8 -35 -13 -63.5t-5 -48.5q0 -53 26.5 -70.5t96.5 -17.5h96q31 0 31 -21 q0 -8 -2 -21.5t-7 -29.5l-10 -45q-4 -23 -14 -30t-33 -7h-115q-137 0 -198.5 43t-61.5 143q0 35 4 72t15 82l98 502h-135q-20 0 -21 14z" />
<glyph unicode="u" horiz-adv-x="1011" d="M63 219q0 29 3.5 68t13.5 90l115 590q4 20 13 28.5t32 8.5h108q37 0 37 -25q0 -10 -5 -38t-13 -62l-99 -508q-6 -33 -10 -61.5t-4 -55.5q0 -55 35 -83t129 -28q61 0 100 10.5t68 34.5q29 25 46 60t34 112l118 607q4 20 13.5 28.5t31.5 8.5h107q37 0 37 -25 q0 -23 -17 -100l-106 -539q-23 -117 -54.5 -183.5t-88.5 -105.5q-53 -37 -126 -53t-178 -16q-166 0 -253 57t-87 180z" />
<glyph unicode="v" horiz-adv-x="905" d="M86 969q0 35 39 35h102q37 0 39 -35q12 -205 29.5 -399.5t46.5 -389.5h14q106 190 208 389t188 400q16 35 51 35h117q25 0 24 -19q0 -12 -7 -30.5t-26 -59.5q-90 -197 -204.5 -416t-241.5 -428q-18 -29 -36.5 -40t-53.5 -11h-107q-35 0 -50 11.5t-21 45.5 q-39 227 -68 453q-43 338 -43 459z" />
<glyph unicode="w" horiz-adv-x="1366" d="M86 963q0 41 45 41h96q41 0 41 -39q-4 -146 -4 -298v-102q2 -205 12 -385h9q76 166 154.5 343t156.5 370q14 35 31.5 48t52.5 13h111q37 0 49 -13t14 -48q6 -180 12 -359.5t15 -353.5h8q37 82 78 178.5t83 198.5t81.5 206.5t74.5 201.5q14 39 56 39h104q35 0 35 -23 q0 -10 -8.5 -32.5t-20.5 -53.5q-86 -213 -179 -426t-185 -401q-18 -39 -41 -53.5t-58 -14.5h-98q-37 0 -56.5 13.5t-21.5 54.5q-10 168 -13 346t-5 338h-10q-66 -160 -143 -334t-156 -350q-18 -41 -42 -54.5t-59 -13.5h-108q-35 0 -52.5 13.5t-21.5 54.5q-8 88 -15.5 194.5 t-11.5 222t-7 236.5t-3 242z" />
<glyph unicode="x" horiz-adv-x="927" d="M-82 18q0 10 11.5 24.5t29.5 39.5l274 373q23 29 32 43t9 26q0 10 -9 33t-32 80l-112 272q-16 37 -21.5 51.5t-5.5 24.5q0 18 25 19h117q25 0 36 -7.5t19 -29.5l98 -269q23 -59 32 -81.5t21 -22.5h9q16 0 35.5 29.5t43.5 62.5l211 287q12 16 23.5 23.5t40.5 7.5h117 q27 0 26 -17q0 -10 -8 -23.5t-41 -54.5l-237 -317q-35 -45 -47.5 -62.5t-12.5 -31.5t9.5 -35t23.5 -49l137 -303q20 -45 28.5 -62.5t8.5 -28.5q0 -20 -31 -20h-116q-35 0 -46 27l-139 335q-16 41 -26.5 52.5t-20.5 11.5h-8q-16 0 -35.5 -29.5t-48.5 -70.5l-205 -295 q-14 -20 -29.5 -25.5t-46.5 -5.5h-114q-25 0 -25 18z" />
<glyph unicode="y" horiz-adv-x="890" d="M78 -299q0 12 11 29.5t40 58.5q31 47 64.5 102.5t64.5 110.5q-41 16 -64.5 49t-33.5 94q-23 145 -40.5 314.5t-31.5 339.5q-4 63 -7 107t-3 67q0 31 37 31h102q39 0 41 -33q10 -219 22.5 -395.5t26.5 -311.5q4 -49 12.5 -73.5t24.5 -34.5q111 217 208 420.5t179 396.5 q12 31 47 31h117q29 0 29 -23q0 -10 -7.5 -26.5t-17.5 -36.5q-45 -104 -101.5 -222t-116.5 -238t-119.5 -233.5t-112.5 -208.5q-49 -88 -107.5 -187t-108.5 -177q-8 -12 -14 -19.5t-16 -7.5t-23.5 5t-42.5 22l-31 16q-28 17 -28 33z" />
<glyph unicode="z" horiz-adv-x="837" d="M-57 25q0 8 2 20t6 31q4 27 16.5 48t46.5 62l547 658h-416q-31 0 -30 18q0 8 2 24.5t6 33.5l8 45q4 25 13.5 32t31.5 7h631q31 0 31 -25q0 -18 -9 -53q-8 -33 -28.5 -57.5t-59.5 -71.5l-530 -637h457q27 0 26 -21q0 -6 -2 -19.5t-8 -41.5l-8 -41q-4 -23 -13.5 -30 t-31.5 -7h-649q-39 0 -39 25z" />
<glyph unicode="{" horiz-adv-x="675" d="M72 541q0 6 2 16t6 39l8 43q4 20 10 26.5t21 10.5q47 10 78.5 23.5t52 33t32 47t19.5 66.5l59 307q14 80 39 129t59 74.5t80 35t107 9.5h90q25 0 25 -19q0 -12 -10 -63l-11 -53q-4 -20 -14 -25.5t-27 -5.5h-69q-41 0 -59.5 -25.5t-34.5 -99.5l-60 -305 q-23 -94 -62.5 -142.5t-121.5 -74.5v-6q47 -18 74.5 -56.5t27.5 -91.5q0 -25 -4 -55.5t-14 -85.5l-43 -217q-4 -25 -7 -43.5t-3 -32.5q0 -25 13 -34t40 -9h78q25 0 24 -18q0 -10 -2 -24.5t-6 -31.5l-12 -59q-4 -18 -12.5 -25.5t-30.5 -7.5h-86q-102 0 -147.5 39t-45.5 117 q0 33 5 69.5t16 87.5l47 240q6 31 6 59q0 82 -109 113q-29 8 -28 25z" />
<glyph unicode="|" horiz-adv-x="471" d="M10 -18q0 12 13 73l253 1317q4 25 15.5 32t38.5 7h88q25 0 34 -5t9 -20q0 -12 -12 -73l-254 -1317q-4 -25 -15.5 -32t-38.5 -7h-88q-25 0 -34 5t-9 20z" />
<glyph unicode="}" horiz-adv-x="675" d="M-113 -190q0 12 11 63l10 53q4 20 14 25.5t27 5.5h69q41 0 59.5 25.5t35.5 99.5l59 305q12 47 26.5 82t36 60.5t51 43t70.5 31.5v6q-47 18 -74.5 56.5t-27.5 91.5q0 25 4 55.5t14 85.5l43 217q4 25 7.5 43.5t3.5 32.5q0 25 -13.5 34t-40.5 9h-77q-25 0 -25 18 q0 10 2 24.5t6 31.5l12 59q4 18 12.5 25.5t30.5 7.5h86q102 0 147.5 -39t45.5 -117q0 -33 -5 -69.5t-15 -88.5l-48 -239q-6 -31 -6 -60q0 -82 109 -112q29 -8 28 -25q0 -6 -2 -16t-6 -39l-8 -43q-4 -20 -10 -26.5t-21 -10.5q-47 -10 -78.5 -23.5t-52 -33t-32 -47 t-19.5 -66.5l-59 -307q-16 -80 -40 -129t-57.5 -74.5t-80 -35t-107.5 -9.5h-90q-25 0 -25 19z" />
<glyph unicode="~" d="M51 580q0 10 12.5 27.5t24.5 31.5q80 92 152.5 131t154.5 39q45 0 80 -13.5t70 -35.5l47 -33q29 -20 55.5 -33.5t63.5 -13.5q41 0 79.5 25.5t92.5 82.5q14 16 21 20.5t14 4.5q8 0 28 -14l39 -29q20 -16 27.5 -23.5t7.5 -17.5t-12.5 -27.5t-24.5 -31.5 q-80 -92 -155.5 -131t-135.5 -39q-53 0 -91 12t-79 39l-45 31q-33 23 -57.5 35t-57.5 12q-74 0 -178 -113q-18 -20 -28 -20t-31 14l-39 29q-20 16 -27.5 23t-7.5 18z" />
<glyph unicode="&#xa1;" horiz-adv-x="442" d="M-82 -310q0 12 6 40t14 63l185 845q6 25 17 34t38 9h68q43 0 43 -24q0 -12 -3.5 -34.5t-13.5 -76.5l-143 -848q-4 -23 -18.5 -30.5t-36.5 -7.5h-115q-41 0 -41 30zM135 856q0 18 2 35.5t6 31.5q14 57 47 77.5t86 20.5h11q94 0 94 -77q0 -18 -2 -36t-6 -32 q-14 -57 -47 -77.5t-87 -20.5h-10q-94 0 -94 78z" />
<glyph unicode="&#xa2;" d="M115 233q0 35 5 83.5t13 101.5t18.5 104t18.5 92q25 115 62.5 193t93 125t126 67.5t160.5 22.5l31 160q4 23 12.5 27.5t30.5 4.5h53q25 0 25 -16q0 -8 -6 -41l-29 -145q47 -8 93 -23.5t87 -38.5q20 -12 21 -26q0 -6 -3 -14.5t-14 -26.5l-32 -62q-6 -10 -10.5 -15 t-14.5 -5q-8 0 -33 12q-31 14 -60.5 24.5t-64.5 16.5l-139 -713q39 4 78 12.5t72 18.5q6 2 13 4t15 2q14 0 21 -24l8 -37q4 -23 7 -36t3 -24q0 -18 -24 -26q-47 -18 -105.5 -30.5t-118.5 -16.5l-28 -150q-4 -23 -12.5 -28t-30.5 -5h-54q-25 0 -24 17q0 6 6 41l25 125 q-154 8 -224.5 68.5t-70.5 180.5zM301 268q0 -61 34 -91t105 -36l140 719q-96 -10 -149.5 -76.5t-86.5 -230.5q-8 -37 -15 -76t-13.5 -77t-10.5 -72.5t-4 -59.5z" />
<glyph unicode="&#xa3;" d="M-23 20q0 6 1.5 15.5t3.5 19.5l8 39q4 16 13 29.5t42 36.5q49 33 81 62.5t53.5 63t34.5 75.5t26 102l26 137h-123q-23 0 -22 12q0 8 6 39l8 43q4 20 12.5 27.5t32.5 11.5l115 19l33 166q25 125 59.5 200.5t87.5 124.5q106 100 305 100q35 0 74 -4t78 -10t72.5 -14 t58.5 -19q23 -8 22 -24q0 -10 -4 -23.5t-14 -42.5l-10 -28q-8 -27 -27 -27q-6 0 -12 1t-14 3q-47 12 -101.5 20.5t-107.5 8.5q-131 0 -193 -68q-29 -31 -47 -80t-35 -127l-31 -153h345q33 0 32 -21q0 -18 -8 -53l-10 -51q-4 -20 -13.5 -25.5t-31.5 -5.5h-344l-17 -84 q-14 -72 -29.5 -126t-35 -95t-46 -74t-63.5 -59v-2h576q31 0 30 -21q0 -14 -8 -55l-8 -45q-4 -23 -12 -31t-35 -8h-805q-29 0 -29 20z" />
<glyph unicode="&#xa4;" horiz-adv-x="1093" d="M35 262q0 12 20 31l131 108q-23 55 -22 121q0 74 24.5 141.5t69.5 124.5l-86 103q-16 20 -16 31q0 8 5 17t27 26l68 53q16 14 29 14q10 0 24 -14l92 -113q96 51 209 51q109 0 183 -51l133 111q16 14 28 14t23 -14l53 -64q16 -18 17 -30q0 -12 -21 -31l-131 -109 q23 -55 23 -120q0 -74 -25 -141.5t-70 -125.5l86 -102q16 -20 17 -31q0 -8 -5.5 -17t-27.5 -26l-68 -53q-16 -14 -28 -14t-25 14l-92 113q-96 -51 -209 -52q-109 0 -182 52l-133 -111q-16 -14 -29 -14q-12 0 -23 14l-53 63q-16 18 -16 31zM338 547q0 -70 45 -112t121 -42 q51 0 94 19.5t76 53.5t51 78t18 95q0 70 -45 112t-120 42q-51 0 -95.5 -19.5t-76 -53.5t-50 -78t-18.5 -95z" />
<glyph unicode="&#xa5;" d="M90 238l14 77q4 20 13.5 25.5t32.5 5.5h241l19 92l-7 15h-231q-25 0 -30 9t-1 32l15 77q4 20 13 25.5t32 5.5h153q-63 195 -104 404q-6 37 -13.5 77.5t-11.5 79.5t-7 73t-3 56q0 33 41 33h100q23 0 33 -5t12 -28q14 -166 45 -342t74 -329h15q115 162 215 331.5t182 339.5 q10 23 21.5 28t33.5 5h121q27 0 27 -18t-21 -56q-35 -70 -80 -150.5t-95 -165.5t-104.5 -170t-109.5 -163h147q25 0 30 -9t1 -32l-14 -78q-4 -20 -13.5 -25t-31.5 -5h-228l-18 -23l-16 -84h239q25 0 30 -9t1 -32l-14 -78q-4 -20 -13.5 -25t-31.5 -5h-242l-31 -164 q-4 -18 -13 -25.5t-34 -7.5h-108q-35 0 -35 23q0 10 2 19t6 32l24 123h-241q-25 0 -30 9t-1 32z" />
<glyph unicode="&#xa6;" horiz-adv-x="471" d="M10 -18q0 12 13 73l81 424q4 25 15.5 32t38.5 7h88q25 0 34 -5t9 -19q0 -12 -13 -74l-81 -424q-4 -25 -15.5 -32t-38.5 -7h-88q-25 0 -34 5t-9 20zM184 874q0 12 13 74l82 424q4 25 15 32t38 7h88q25 0 34 -5t9 -20q0 -12 -12 -73l-82 -424q-4 -25 -15.5 -32t-38.5 -7 h-88q-25 0 -34 5t-9 19z" />
<glyph unicode="&#xa7;" horiz-adv-x="884" d="M2 57q0 10 3 19.5t9 32.5l15 51q8 27 24 26q8 0 21 -4q57 -23 124.5 -35t125.5 -12q78 0 118.5 14.5t57.5 49.5q8 16 13 37.5t5 42.5q0 45 -28.5 63t-116.5 37l-80 16q-113 23 -155 71t-42 134t46 151.5t126 94.5q-41 20 -64.5 64t-23.5 108q0 59 16.5 109.5t43.5 84.5 q47 66 131 90.5t176 24.5q78 0 143.5 -11t114.5 -32q23 -8 22 -22q0 -10 -4 -22.5t-10 -35.5l-16 -51q-6 -20 -21 -20q-8 0 -18 4t-27 8q-35 10 -81 18t-99 8q-74 0 -116 -15t-58 -50q-16 -33 -17 -72q0 -35 25 -53t92 -33l101 -20q104 -23 151 -64t49 -116q0 -96 -45 -168 t-127 -103q96 -47 96 -160q0 -41 -10 -90t-28 -90q-68 -156 -338 -155q-74 0 -159 15t-144 38q-20 8 -21 22zM285 641q0 -39 18.5 -61.5t67.5 -32.5l100 -21q59 23 89 66t30 98q0 35 -19.5 55.5t-72.5 30.5l-105 23q-45 -16 -76.5 -57.5t-31.5 -100.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="727" d="M205 1268q0 10 1 25.5t9 49.5q10 43 41 63.5t76 20.5h16q41 0 64.5 -18t23.5 -55q0 -10 -2 -25.5t-8 -44.5q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 52.5zM539 1268q0 10 1 25.5t9 49.5q10 43 41 63.5t76 20.5h16q41 0 64.5 -18t23.5 -55 q0 -10 -2 -25.5t-8 -44.5q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 52.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1673" d="M76 565q0 195 64.5 358.5t183 281.5t282.5 183.5t365 65.5q152 0 271.5 -42t202.5 -118.5t127 -187.5t44 -246q0 -195 -64.5 -358.5t-183.5 -281.5t-283.5 -183.5t-363.5 -65.5q-152 0 -271.5 42t-202.5 119t-127 187.5t-44 245.5zM270 571q0 -201 121 -315.5t344 -114.5 q166 0 294 54.5t215 149.5t132 226t45 283q0 201 -120.5 315.5t-344.5 114.5q-166 0 -293.5 -54t-215 -149.5t-132.5 -226.5t-45 -283zM524 502q0 61 11.5 137t23.5 147q16 96 45 159t71 99.5t98 52t126 15.5q57 0 118.5 -15.5t100.5 -37.5q20 -10 21 -29q0 -16 -23 -55 l-16 -29q-8 -14 -13.5 -18t-11.5 -4q-10 0 -22 8q-35 18 -74 29.5t-80 11.5q-37 0 -66.5 -8.5t-52 -32t-39 -64.5t-26.5 -104q-10 -55 -19.5 -119.5t-9.5 -105.5q0 -47 27.5 -67.5t99.5 -20.5q35 0 73 5t66 13q10 4 20.5 6t14.5 2q18 0 21 -20l8 -37q6 -35 6 -49t-23 -23 q-47 -16 -107.5 -26.5t-119.5 -10.5q-248 0 -248 191z" />
<glyph unicode="&#xaa;" horiz-adv-x="667" d="M84 725q0 35 6 64.5t12 54.5q18 76 72.5 108.5t171.5 32.5h37q16 0 46 -1t58 -5l5 20q4 16 6 30.5t2 27.5q0 29 -21.5 41t-79.5 12q-37 0 -72.5 -6t-60.5 -12q-6 -2 -16 -4.5t-19 -2.5q-18 0 -20 31l-4 29q-2 12 -3 22.5t-1 16.5q0 12 16 18q41 12 98.5 20.5t110.5 8.5 q111 0 163 -33t52 -111q0 -29 -5 -58.5t-13 -65.5l-66 -342q-2 -20 -24 -21h-76q-20 0 -21 18v35q-23 -39 -64.5 -51t-105.5 -12h-26q-90 0 -124 36t-34 99zM219 750q0 -29 14.5 -38.5t59.5 -9.5h18q66 0 97.5 19.5t44.5 81.5l12 63q-18 4 -44 5t-48 1h-31q-49 0 -75.5 -12 t-37.5 -47q-10 -41 -10 -63z" />
<glyph unicode="&#xab;" horiz-adv-x="1091" d="M-12 446q0 8 2 20.5t6 25.5q4 20 13 31.5t32 27.5l352 252q10 6 17.5 11t15.5 5q16 0 33 -20l39 -47q12 -14 16 -21.5t4 -15.5q0 -10 -10 -21.5t-39 -34.5l-258 -200v-2l186 -209q12 -12 13 -29q0 -16 -19 -31l-47 -41q-16 -12 -33 -12q-18 0 -35 17l-256 239 q-23 20 -27.5 30.5t-4.5 24.5zM498 446q0 8 2 20.5t6 25.5q4 20 13 31.5t32 27.5l352 252q10 6 17.5 11t15.5 5q16 0 33 -20l39 -47q12 -14 16 -21.5t4 -15.5q0 -10 -10 -21.5t-39 -34.5l-258 -200v-2l186 -209q12 -12 13 -29q0 -16 -19 -31l-47 -41q-16 -12 -33 -12 q-18 0 -35 17l-256 239q-23 20 -27.5 30.5t-4.5 24.5z" />
<glyph unicode="&#xac;" d="M47 471q0 8 4 29l15 73q6 29 17 38.5t40 9.5h803q41 0 41 -29q0 -8 -4 -29l-86 -436q-4 -27 -15.5 -36t-40.5 -9h-94q-41 0 -41 29q0 8 4 28l60 303h-662q-41 0 -41 29z" />
<glyph unicode="&#xad;" horiz-adv-x="645" d="M33 469q0 8 4 27.5t8 42.5l6 32q4 29 16.5 39.5t47.5 10.5h397q27 0 35 -7.5t8 -19.5q0 -8 -4 -27.5t-8 -42.5l-6 -32q-4 -29 -16.5 -39.5t-47.5 -10.5h-397q-27 0 -35 7.5t-8 19.5z" />
<glyph unicode="&#xae;" horiz-adv-x="1673" d="M76 565q0 195 64.5 358.5t183 281.5t282.5 183.5t365 65.5q152 0 271.5 -42t202.5 -118.5t127 -187.5t44 -246q0 -195 -64.5 -358.5t-183.5 -281.5t-283.5 -183.5t-363.5 -65.5q-152 0 -271.5 42t-202.5 119t-127 187.5t-44 245.5zM270 571q0 -201 121 -315.5t344 -114.5 q166 0 294 54.5t215 149.5t132 226t45 283q0 201 -120.5 315.5t-344.5 114.5q-166 0 -293.5 -54t-215 -149.5t-132.5 -226.5t-45 -283zM514 342q0 8 4 24.5t10 49.5l127 649q4 20 11.5 27.5t29.5 11.5q45 8 98.5 11t96.5 3q117 0 193.5 -42t76.5 -152q0 -121 -54 -194 t-157 -99l76 -193q18 -47 26.5 -66.5t8.5 -29.5q0 -20 -31 -20h-100q-16 0 -26.5 6t-16.5 24l-90 260h-21h-22.5t-24.5 2l-49 -260q-4 -20 -14.5 -26t-32.5 -6h-92q-27 0 -27 20zM758 758q18 -2 37.5 -2h42.5q76 0 111.5 37.5t35.5 107.5q0 47 -24.5 64.5t-79.5 17.5 q-18 0 -34 -1t-30 -3q-16 -2 -20 -25z" />
<glyph unicode="&#xaf;" horiz-adv-x="724" d="M205 1243q0 12 8 51l10 47q4 18 12.5 24.5t32.5 6.5h457q29 0 29 -20q0 -12 -9 -52l-10 -47q-4 -18 -12 -24t-33 -6h-457q-29 0 -28 20z" />
<glyph unicode="&#xb0;" horiz-adv-x="706" d="M152 1180q0 66 23.5 125t66.5 103t103.5 70.5t133.5 26.5q102 0 168 -62.5t66 -162.5q0 -66 -23.5 -125t-66.5 -103t-103.5 -71t-134.5 -27q-102 0 -167.5 62.5t-65.5 163.5zM322 1208q0 -41 23.5 -69.5t66.5 -28.5q53 0 92 38t39 103q0 41 -23.5 70t-66.5 29 q-53 0 -92 -38t-39 -104z" />
<glyph unicode="&#xb1;" d="M-35 25q0 14 8 61l9 45q4 29 17 38t42 9h805q37 0 37 -24q0 -14 -9 -62l-8 -45q-4 -29 -17 -38t-42 -9h-805q-37 0 -37 25zM96 711q0 10 2 23.5t6 33.5l9 51q4 29 17 38t42 9h307l60 297q6 29 19 37t44 8h84q41 0 41 -26q0 -10 -2 -23.5t-6 -28.5l-53 -264h315 q33 0 33 -22q0 -10 -2 -23.5t-6 -34.5l-9 -51q-4 -29 -17 -38t-42 -9h-307l-60 -297q-6 -29 -19 -37t-44 -8h-84q-41 0 -41 27q0 10 2 23.5t6 27.5l53 264h-315q-33 0 -33 23z" />
<glyph unicode="&#xb2;" horiz-adv-x="751" d="M170 922q0 16 6 47l6 32q10 63 28.5 107.5t48.5 77.5t72 56.5t101 45.5l80 29q53 20 74.5 44.5t21.5 65.5q0 80 -104 80q-39 0 -77 -7t-75 -19q-23 -8 -28 -8q-16 0 -23 24l-10 35q-6 18 -8 29.5t-2 15.5q0 18 30 31q53 20 109.5 29.5t109.5 9.5q106 0 175 -47.5 t69 -149.5q0 -106 -45 -158.5t-147 -89.5l-97 -35q-70 -25 -99.5 -55.5t-37.5 -77.5h303q31 0 31 -18q0 -10 -6 -37l-12 -57q-4 -16 -11.5 -21.5t-27.5 -5.5h-418q-37 0 -37 27z" />
<glyph unicode="&#xb3;" horiz-adv-x="751" d="M186 952q0 6 4 19l27 67q10 23 23 23q6 0 22 -8q31 -12 72 -22.5t82 -10.5q51 0 92 26.5t41 90.5q0 68 -103 67h-65q-29 0 -23 31l15 76q4 16 9 21t24 5h34q164 0 164 107q0 31 -20.5 49t-75.5 18q-27 0 -64.5 -5t-70.5 -13q-16 -4 -27 -4q-12 0 -16 20l-8 39 q-4 16 -6.5 26.5t-2.5 18.5q0 18 25 25q37 12 90 20.5t102 8.5q106 0 171 -40t65 -130q0 -72 -32 -124.5t-97 -78.5v-6q78 -33 78 -127q0 -55 -17.5 -102.5t-52.5 -82t-88 -55t-123 -20.5q-61 0 -123.5 14t-101.5 33q-23 10 -23 24z" />
<glyph unicode="&#xb4;" horiz-adv-x="694" d="M227 1266q0 12 23 22l368 178q33 16 44 17q10 0 16 -6.5t16 -28.5l23 -53q16 -39 16 -54q0 -20 -26 -28l-394 -146q-16 -6 -26 -6q-14 0 -25 21l-20 43q-6 14 -10.5 23t-4.5 18z" />
<glyph unicode="&#xb5;" horiz-adv-x="1140" d="M-43 -338q0 8 6 37t12 63l234 1205q4 20 13 28.5t32 8.5h108q37 0 37 -23q0 -10 -5 -36.5t-13 -65.5l-94 -490q-8 -41 -12.5 -69.5t-4.5 -51.5q0 -55 32 -90t126 -35q123 0 176 57.5t74 159.5l117 607q6 37 45 37h108q37 0 37 -23q0 -10 -5 -36.5t-13 -65.5l-119 -627 q-4 -16 -6 -32.5t-2 -33.5q0 -47 53 -47q29 0 53 6q16 4 25 5q16 0 18 -27l4 -35q2 -14 3 -27.5t1 -19.5q0 -18 -22 -29q-23 -12 -66 -21t-84 -9q-76 0 -113.5 26.5t-52.5 79.5h-4q-94 -106 -243 -106q-72 0 -119 17t-74 46l-72 -371q-6 -37 -45 -36h-108q-37 0 -37 24z" />
<glyph unicode="&#xb6;" horiz-adv-x="1206" d="M145 950q0 100 36 181t104.5 138.5t168 89.5t224.5 32h571q18 0 23.5 -4.5t5.5 -14.5q0 -6 -2 -19.5t-6 -33.5l-13 -64q-6 -27 -38 -26h-115l-277 -1415q-4 -23 -16 -33t-41 -10h-90q-35 0 -35 24q0 12 4 35t13 64l260 1335h-177l-276 -1415q-4 -23 -16.5 -33t-40.5 -10 h-107q-35 0 -35 24q0 12 4.5 35t12.5 64l145 747q-127 16 -207 94t-80 215z" />
<glyph unicode="&#xb7;" horiz-adv-x="415" d="M68 544q0 14 2 30.5t8 45.5q25 100 129 100h16q47 0 71.5 -20t24.5 -55q0 -14 -2 -30.5t-8 -45.5q-12 -53 -43 -76.5t-86 -23.5h-16q-47 0 -71.5 20.5t-24.5 54.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="655" d="M-78 -432q0 6 8 29l15 36q8 23 18 23q6 0 10 -1t11 -3q29 -10 63.5 -15.5t61.5 -5.5q63 0 90.5 22.5t27.5 70.5q0 47 -63 47q-20 0 -37.5 -2t-28.5 -5q-10 -2 -18 -3t-14 -1q-14 0 -23 15l-8 14q-8 16 -8 25q0 6 3 14t13 31l55 123q10 20 33 20h80q16 0 16 -12 q0 -6 -14 -37l-39 -80q16 4 34.5 6t37.5 2q59 0 95 -33.5t36 -95.5q0 -117 -68.5 -175t-185.5 -58q-47 0 -98.5 8t-83.5 20q-18 8 -19 21z" />
<glyph unicode="&#xb9;" horiz-adv-x="751" d="M176 911q0 6 1 16.5t5 30.5l8 46q4 18 11.5 24t29.5 6h129l82 432h-8l-133 -75q-10 -6 -16 -7q-10 0 -23 19l-24 43q-10 18 -13.5 25.5t-3.5 11.5q0 10 21 22l186 107q25 14 40 18t46 4h86q33 0 33 -20q0 -12 -2 -24.5t-6 -30.5l-103 -525h135q27 0 27 -16q0 -8 -6 -47 l-8 -45q-4 -18 -11.5 -24.5t-29.5 -6.5h-426q-27 0 -27 16z" />
<glyph unicode="&#xba;" horiz-adv-x="667" d="M94 791q0 20 5.5 54.5t17.5 104.5q14 80 38.5 134.5t61.5 87t87 46t118 13.5q111 0 170 -45t59 -148q0 -23 -4 -61.5t-16 -104.5q-29 -152 -98.5 -217t-208.5 -65q-111 0 -170.5 50t-59.5 151zM242 799q0 -90 96 -90q63 0 94 32.5t47 110.5q10 47 17.5 93t7.5 83 q0 45 -21.5 64.5t-70.5 19.5q-70 0 -103 -36t-49 -142q-8 -47 -13 -80t-5 -55z" />
<glyph unicode="&#xbb;" horiz-adv-x="1091" d="M-20 240q0 10 10 21t39 34l258 201v2l-187 209q-12 12 -12 28q0 14 18 31l48 41q14 12 32 12t35 -16l256 -240q23 -20 28 -30.5t5 -24.5q0 -8 -2 -20.5t-6 -24.5q-4 -20 -13.5 -31.5t-31.5 -28.5l-353 -251q-10 -6 -17 -11.5t-15 -5.5q-18 0 -33 21l-39 47q-12 14 -16 21 t-4 16zM489 240q0 10 10.5 21t39.5 34l258 201v2l-187 209q-12 12 -12 28q0 14 18 31l48 41q14 12 32 12t35 -16l256 -240q23 -20 28 -30.5t5 -24.5q0 -8 -2 -20.5t-6 -24.5q-4 -20 -13.5 -31.5t-31.5 -28.5l-353 -251q-10 -6 -17 -11.5t-15 -5.5q-10 0 -16.5 4t-16.5 17 l-39 47q-12 14 -16.5 21t-4.5 16z" />
<glyph unicode="&#xbc;" horiz-adv-x="1714" d="M129 600q0 6 1 16.5t5 30.5l8 45q4 18 11.5 24.5t29.5 6.5h129l82 432h-8l-133 -76q-10 -6 -16 -6q-10 0 -23 19l-25 43q-10 18 -13 25t-3 11q0 10 21 23l186 106q25 14 40 18.5t46 4.5h86q33 0 33 -20q0 -12 -2 -24.5t-6 -31.5l-103 -524h135q27 0 27 -16q0 -8 -6 -48 l-8 -45q-4 -18 -11.5 -24t-29.5 -6h-426q-27 0 -27 16zM326 2q0 10 12 29l952 1280q16 20 29.5 26t38.5 6h100q31 0 31 -20q0 -10 -12 -29l-953 -1280q-16 -20 -29.5 -26t-37.5 -6h-101q-31 0 -30 20zM946 186q0 12 4 33l4 21q6 29 13.5 41t25.5 34l312 373q25 29 44 38 t54 9h108q27 0 34 -9t1 -38l-76 -389h74q18 0 19 -12q0 -12 -7 -43l-8 -39q-4 -23 -11 -29t-24 -6h-67l-27 -141q-4 -18 -12 -23.5t-25 -5.5h-104q-23 0 -23 16q0 6 3.5 18.5t7.5 35.5l18 100h-315q-23 0 -23 16zM1130 299h179l55 283z" />
<glyph unicode="&#xbd;" horiz-adv-x="1714" d="M106 602q0 6 1 16.5t5 30.5l8 46q4 18 11.5 24t29.5 6h129l82 432h-8l-133 -75q-10 -6 -16 -7q-10 0 -23 19l-24 43q-10 18 -13.5 25.5t-3.5 11.5q0 10 21 22l186 107q25 14 40 18t46 4h86q33 0 33 -20q0 -12 -2 -24.5t-6 -30.5l-103 -525h135q27 0 27 -16q0 -8 -6 -47 l-8 -45q-4 -18 -11.5 -24.5t-29.5 -6.5h-426q-27 0 -27 16zM301 2q0 10 13 29l952 1280q16 20 29.5 26t38.5 6h100q31 0 31 -20q0 -10 -13 -29l-952 -1280q-16 -20 -29.5 -26t-37.5 -6h-101q-31 0 -31 20zM1008 29q0 16 6 47l6 32q10 63 28.5 107.5t48.5 77.5t72 56.5 t101 45.5l80 29q53 20 74.5 44.5t21.5 65.5q0 80 -104 80q-39 0 -77 -7t-75 -19q-23 -8 -28 -8q-16 0 -23 24l-10 35q-6 18 -8 29.5t-2 15.5q0 18 30 31q53 20 109.5 29.5t109.5 9.5q106 0 175 -47.5t69 -149.5q0 -106 -45 -158.5t-147 -89.5l-97 -35q-70 -25 -99.5 -55.5 t-37.5 -77.5h303q31 0 31 -18q0 -10 -6 -37l-12 -57q-4 -16 -11.5 -21.5t-27.5 -5.5h-418q-37 0 -37 27z" />
<glyph unicode="&#xbe;" horiz-adv-x="1714" d="M117 641q0 6 4 18l26 68q10 23 23 23q6 0 23 -9q31 -12 71.5 -22t81.5 -10q51 0 92 26.5t41 89.5q0 68 -102 68h-66q-29 0 -22 31l14 75q4 16 9 21.5t24 5.5h35q164 0 164 107q0 31 -20.5 49t-76.5 18q-27 0 -64.5 -5t-70.5 -13q-16 -4 -27 -4q-12 0 -16 20l-8 39 q-4 16 -6 26.5t-2 18.5q0 18 24 25q37 12 90.5 20t102.5 8q106 0 170.5 -40t64.5 -130q0 -72 -31.5 -124t-97.5 -78v-7q78 -33 78 -127q0 -55 -17.5 -102t-52 -82t-88 -55.5t-122.5 -20.5q-61 0 -124 14.5t-102 32.5q-23 10 -22 25zM305 2q0 10 12 29l953 1280 q16 20 29.5 26t37.5 6h101q31 0 30 -20q0 -10 -12 -29l-952 -1280q-16 -20 -29.5 -26t-38.5 -6h-100q-31 0 -31 20zM926 186q0 12 4 33l4 21q6 29 13 41t26 34l311 373q25 29 44.5 38t53.5 9h109q27 0 34 -9t1 -38l-76 -389h74q18 0 18 -12t-6 -43l-8 -39q-4 -23 -11.5 -29 t-23.5 -6h-68l-26 -141q-4 -18 -12.5 -23.5t-24.5 -5.5h-105q-23 0 -22 16q0 6 3 18.5t7 35.5l19 100h-316q-23 0 -22 16zM1110 299h178l55 283z" />
<glyph unicode="&#xbf;" horiz-adv-x="911" d="M-28 -68q0 66 14 120t49 103t92.5 93t141.5 87l100 52q39 20 67.5 38.5t48 42t32 52t18.5 69.5q2 10 4 24.5t4 29.5q6 37 49 36h86q31 1 31 -26q0 -29 -6 -66q-10 -59 -27.5 -107t-45.5 -86t-67.5 -71t-97.5 -61l-100 -52q-113 -57 -152 -116.5t-39 -132.5q0 -80 42 -112 t145 -32q61 0 133 14.5t135 36.5q20 8 37 9q18 0 24 -23l13 -51q4 -20 8 -32.5t4 -22.5q0 -16 -23 -25q-74 -27 -172 -45t-188 -18q-188 0 -274 69.5t-86 202.5zM543 856q0 18 2 35.5t6 31.5q14 57 47 77.5t86 20.5h11q94 0 94 -77q0 -18 -2 -36t-6 -32q-14 -57 -47 -77.5 t-87 -20.5h-10q-94 0 -94 78z" />
<glyph unicode="&#xc0;" horiz-adv-x="1155" d="M-86 31q0 14 10 35.5t27 56.5q66 137 143.5 296t157.5 315.5t154.5 296.5t134.5 241q18 33 38.5 43t57.5 10h106q47 0 64.5 -14.5t26.5 -55.5q23 -131 42 -275t36.5 -287.5t30.5 -275.5t24 -239q4 -47 7 -84t3 -59q0 -35 -43 -35h-107q-33 0 -45 8t-14 35q-4 78 -10 160 t-13 159h-473q-37 -76 -75.5 -160.5t-71.5 -158.5q-12 -29 -27.5 -36t-50.5 -7h-88q-27 0 -36 7t-9 24zM350 528h383q-4 68 -10 144t-14.5 152.5t-16.5 153.5t-16 148h-27q-70 -131 -143.5 -275t-155.5 -323zM377 1597q0 18 14 54l21 55q10 23 16 29t16 6q8 0 16.5 -3 t26.5 -12l373 -165q25 -10 25 -23q0 -8 -4.5 -17.5t-10.5 -23.5l-18 -45q-10 -20 -25 -20q-12 0 -26 6l-398 131q-27 8 -26 28z" />
<glyph unicode="&#xc1;" horiz-adv-x="1155" d="M-86 31q0 14 10 35.5t27 56.5q66 137 143.5 296t157.5 315.5t154.5 296.5t134.5 241q18 33 38.5 43t57.5 10h106q47 0 64.5 -14.5t26.5 -55.5q23 -131 42 -275t36.5 -287.5t30.5 -275.5t24 -239q4 -47 7 -84t3 -59q0 -35 -43 -35h-107q-33 0 -45 8t-14 35q-4 78 -10 160 t-13 159h-473q-37 -76 -75.5 -160.5t-71.5 -158.5q-12 -29 -27.5 -36t-50.5 -7h-88q-27 0 -36 7t-9 24zM350 528h383q-4 68 -10 144t-14.5 152.5t-16.5 153.5t-16 148h-27q-70 -131 -143.5 -275t-155.5 -323zM527 1538q0 12 24 23l373 165q18 8 26.5 11.5t16.5 3.5 q10 0 16 -6t17 -29l20 -55q14 -35 14 -54q0 -20 -26 -28l-397 -131q-16 -6 -27 -6q-14 0 -25 20l-18 45q-6 14 -10 23.5t-4 17.5z" />
<glyph unicode="&#xc2;" horiz-adv-x="1155" d="M-86 31q0 14 10 35.5t27 56.5q66 137 143.5 296t157.5 315.5t154.5 296.5t134.5 241q18 33 38.5 43t57.5 10h106q47 0 64.5 -14.5t26.5 -55.5q23 -131 42 -275t36.5 -287.5t30.5 -275.5t24 -239q4 -47 7 -84t3 -59q0 -35 -43 -35h-107q-33 0 -45 8t-14 35q-4 78 -10 160 t-13 159h-473q-37 -76 -75.5 -160.5t-71.5 -158.5q-12 -29 -27.5 -36t-50.5 -7h-88q-27 0 -36 7t-9 24zM350 528h383q-4 68 -10 144t-14.5 152.5t-16.5 153.5t-16 148h-27q-70 -131 -143.5 -275t-155.5 -323zM392 1479q0 10 8 19t20 22l232 221q12 10 21 14t26 4h86 q14 0 22.5 -3t18.5 -15l164 -234q16 -23 16 -34q0 -12 -21 -27l-34 -23q-25 -16 -37 -16q-14 0 -31 18l-147 172h-9l-200 -167q-12 -10 -24.5 -18.5t-22.5 -8.5q-12 0 -20.5 5t-20.5 15l-27 25q-20 18 -20 31z" />
<glyph unicode="&#xc3;" horiz-adv-x="1155" d="M-86 31q0 14 10 35.5t27 56.5q66 137 143.5 296t157.5 315.5t154.5 296.5t134.5 241q18 33 38.5 43t57.5 10h106q47 0 64.5 -14.5t26.5 -55.5q23 -131 42 -275t36.5 -287.5t30.5 -275.5t24 -239q4 -47 7 -84t3 -59q0 -35 -43 -35h-107q-33 0 -45 8t-14 35q-4 78 -10 160 t-13 159h-473q-37 -76 -75.5 -160.5t-71.5 -158.5q-12 -29 -27.5 -36t-50.5 -7h-88q-27 0 -36 7t-9 24zM350 528h383q-4 68 -10 144t-14.5 152.5t-16.5 153.5t-16 148h-27q-70 -131 -143.5 -275t-155.5 -323zM387 1520q0 8 10 24q53 90 114.5 128t127.5 38q63 0 123 -47 l37 -31q18 -16 33.5 -23t37.5 -7q33 0 59.5 24.5t59.5 65.5q8 12 23 12q8 0 24 -12l41 -33q14 -12 15 -23q0 -8 -11 -22q-111 -162 -235 -162q-39 0 -71 12.5t-58 34.5l-37 31q-31 29 -68 29q-57 0 -118 -86q-12 -16 -25 -17q-12 0 -35 17l-31 22q-10 8 -13 13t-3 12z" />
<glyph unicode="&#xc4;" horiz-adv-x="1155" d="M-86 31q0 14 10 35.5t27 56.5q66 137 143.5 296t157.5 315.5t154.5 296.5t134.5 241q18 33 38.5 43t57.5 10h106q47 0 64.5 -14.5t26.5 -55.5q23 -131 42 -275t36.5 -287.5t30.5 -275.5t24 -239q4 -47 7 -84t3 -59q0 -35 -43 -35h-107q-33 0 -45 8t-14 35q-4 78 -10 160 t-13 159h-473q-37 -76 -75.5 -160.5t-71.5 -158.5q-12 -29 -27.5 -36t-50.5 -7h-88q-27 0 -36 7t-9 24zM350 528h383q-4 68 -10 144t-14.5 152.5t-16.5 153.5t-16 148h-27q-70 -131 -143.5 -275t-155.5 -323zM473 1550q0 10 1 25.5t9 50.5q10 43 41 63.5t76 20.5h16 q41 0 64.5 -18.5t23.5 -55.5q0 -10 -2 -25.5t-8 -43.5q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 51.5zM807 1550q0 10 1 25.5t9 50.5q10 43 40.5 63.5t76.5 20.5h16q41 0 64.5 -18.5t23.5 -55.5q0 -10 -2 -25.5t-8 -43.5q-10 -45 -41 -65.5t-78 -20.5h-16 q-41 0 -63.5 17.5t-22.5 51.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1155" d="M-86 31q0 14 10 35.5t27 56.5q66 137 143.5 296t157.5 315.5t154.5 296.5t134.5 241q18 33 38.5 43t57.5 10h106q47 0 64.5 -14.5t26.5 -55.5q23 -131 42 -275t36.5 -287.5t30.5 -275.5t24 -239q4 -47 7 -84t3 -59q0 -35 -43 -35h-107q-33 0 -45 8t-14 35q-4 78 -10 160 t-13 159h-473q-37 -76 -75.5 -160.5t-71.5 -158.5q-12 -29 -27.5 -36t-50.5 -7h-88q-27 0 -36 7t-9 24zM350 528h383q-4 68 -10 144t-14.5 152.5t-16.5 153.5t-16 148h-27q-70 -131 -143.5 -275t-155.5 -323zM553 1579q0 98 64.5 157.5t158.5 59.5q76 0 123 -44t47 -118 q0 -98 -61.5 -160.5t-159.5 -62.5q-78 0 -125 46t-47 122zM670 1591q0 -29 18 -47t51 -18q41 0 65.5 24.5t24.5 65.5q0 29 -18.5 47t-50.5 18q-41 0 -65.5 -24.5t-24.5 -65.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1701" d="M-72 31q4 14 18.5 35.5t37.5 56.5q92 137 199.5 296t216 315.5t209 296.5t177.5 241q25 33 53.5 43t69.5 10h768q29 0 29 -20q0 -8 -3 -23.5t-7 -44.5l-8 -43q-6 -35 -48 -35h-526l-76 -393h486q29 0 28 -21q0 -8 -3 -23t-7 -44l-8 -43q-6 -35 -47 -35h-481 q-12 -63 -23.5 -122.5t-20 -110t-13.5 -86t-5 -52.5q0 -35 17.5 -49t70.5 -14h451q29 0 28 -21q0 -8 -3 -23.5t-7 -43.5l-8 -43q-6 -35 -47 -35h-498q-98 0 -151.5 41t-53.5 139q0 27 4.5 73t14.5 109h-412q-53 -76 -107.5 -160.5t-101.5 -158.5q-18 -29 -34.5 -36t-51.5 -7 h-88q-27 0 -35 7t-4 24zM461 528h334q31 156 64.5 319t66.5 312h-25q-98 -137 -210.5 -294.5t-229.5 -336.5z" />
<glyph unicode="&#xc7;" d="M80 332q0 76 18.5 205t53.5 301q33 168 77.5 260t112.5 145q63 51 149.5 75.5t215.5 24.5q86 0 172 -20t159 -55q25 -12 25 -27q0 -12 -4 -21.5t-15 -27.5l-34 -70q-10 -18 -23 -18q-8 0 -17 4t-20 8q-51 25 -119.5 41.5t-134.5 16.5q-84 0 -142 -22t-98 -68t-67 -120.5 t-47 -179.5q-33 -162 -46 -268t-13 -174q0 -49 11 -86t38.5 -60.5t75 -36t118.5 -12.5q70 0 147 14.5t128 31.5q31 10 43 10q16 0 24 -29l9 -35q12 -55 12 -65q0 -20 -29 -31q-70 -25 -162 -41t-170 -20l-51 -109q16 4 34.5 6t37.5 2q59 0 95 -33.5t36 -95.5 q0 -117 -68.5 -175t-185.5 -58q-47 0 -98.5 8t-83.5 20q-18 8 -19 21q0 6 8 29l15 36q8 23 18 23q6 0 10.5 -1t10.5 -3q29 -10 63.5 -15.5t61.5 -5.5q63 0 90.5 22.5t27.5 70.5q0 47 -63 47q-20 0 -37.5 -2t-28.5 -5q-10 -2 -18 -3t-14 -1q-14 0 -23 15l-8 14q-8 16 -8 25 q0 6 3 14t13 31l57 127q-172 12 -247.5 91t-75.5 255z" />
<glyph unicode="&#xc8;" horiz-adv-x="999" d="M45 180q0 29 4 79t21 132q35 178 69.5 352.5t73.5 341.5q16 74 39.5 121t60.5 73t89.5 36t125.5 10h451q29 0 29 -20q0 -8 -3.5 -23.5t-7.5 -44.5l-8 -43q-6 -35 -47 -35h-430q-82 0 -102 -78q-18 -72 -36 -156.5t-34 -158.5h485q29 0 29 -21q0 -8 -3 -23t-7 -44l-8 -43 q-6 -35 -48 -35h-481q-12 -63 -23.5 -122.5t-19.5 -110t-13 -86t-5 -52.5q0 -35 17.5 -49t70.5 -14h450q29 0 29 -21q0 -8 -3 -23.5t-7 -43.5l-8 -43q-6 -35 -47 -35h-498q-98 0 -151.5 41t-53.5 139zM387 1597q0 18 14 54l21 55q10 23 16 29t16 6q8 0 16.5 -3t26.5 -12 l373 -165q25 -10 25 -23q0 -8 -4.5 -17.5t-10.5 -23.5l-18 -45q-10 -20 -25 -20q-12 0 -26 6l-398 131q-27 8 -26 28z" />
<glyph unicode="&#xc9;" horiz-adv-x="999" d="M45 180q0 29 4 79t21 132q35 178 69.5 352.5t73.5 341.5q16 74 39.5 121t60.5 73t89.5 36t125.5 10h451q29 0 29 -20q0 -8 -3.5 -23.5t-7.5 -44.5l-8 -43q-6 -35 -47 -35h-430q-82 0 -102 -78q-18 -72 -36 -156.5t-34 -158.5h485q29 0 29 -21q0 -8 -3 -23t-7 -44l-8 -43 q-6 -35 -48 -35h-481q-12 -63 -23.5 -122.5t-19.5 -110t-13 -86t-5 -52.5q0 -35 17.5 -49t70.5 -14h450q29 0 29 -21q0 -8 -3 -23.5t-7 -43.5l-8 -43q-6 -35 -47 -35h-498q-98 0 -151.5 41t-53.5 139zM480 1538q0 12 24 23l373 165q18 8 26.5 11.5t16.5 3.5q10 0 16 -6 t17 -29l20 -55q14 -35 14 -54q0 -20 -26 -28l-397 -131q-16 -6 -27 -6q-14 0 -25 20l-18 45q-6 14 -10 23.5t-4 17.5z" />
<glyph unicode="&#xca;" horiz-adv-x="999" d="M45 180q0 29 4 79t21 132q35 178 69.5 352.5t73.5 341.5q16 74 39.5 121t60.5 73t89.5 36t125.5 10h451q29 0 29 -20q0 -8 -3.5 -23.5t-7.5 -44.5l-8 -43q-6 -35 -47 -35h-430q-82 0 -102 -78q-18 -72 -36 -156.5t-34 -158.5h485q29 0 29 -21q0 -8 -3 -23t-7 -44l-8 -43 q-6 -35 -48 -35h-481q-12 -63 -23.5 -122.5t-19.5 -110t-13 -86t-5 -52.5q0 -35 17.5 -49t70.5 -14h450q29 0 29 -21q0 -8 -3 -23.5t-7 -43.5l-8 -43q-6 -35 -47 -35h-498q-98 0 -151.5 41t-53.5 139zM388 1479q0 10 8 19t20 22l232 221q12 10 21 14t26 4h86q14 0 22.5 -3 t18.5 -15l164 -234q16 -23 16 -34q0 -12 -21 -27l-34 -23q-25 -16 -37 -16q-14 0 -31 18l-147 172h-9l-200 -167q-12 -10 -24.5 -18.5t-22.5 -8.5q-12 0 -20.5 5t-20.5 15l-27 25q-20 18 -20 31z" />
<glyph unicode="&#xcb;" horiz-adv-x="999" d="M45 180q0 29 4 79t21 132q35 178 69.5 352.5t73.5 341.5q16 74 39.5 121t60.5 73t89.5 36t125.5 10h451q29 0 29 -20q0 -8 -3.5 -23.5t-7.5 -44.5l-8 -43q-6 -35 -47 -35h-430q-82 0 -102 -78q-18 -72 -36 -156.5t-34 -158.5h485q29 0 29 -21q0 -8 -3 -23t-7 -44l-8 -43 q-6 -35 -48 -35h-481q-12 -63 -23.5 -122.5t-19.5 -110t-13 -86t-5 -52.5q0 -35 17.5 -49t70.5 -14h450q29 0 29 -21q0 -8 -3 -23.5t-7 -43.5l-8 -43q-6 -35 -47 -35h-498q-98 0 -151.5 41t-53.5 139zM424 1550q0 10 1 25.5t9 50.5q10 43 41 63.5t76 20.5h16 q41 0 64.5 -18.5t23.5 -55.5q0 -10 -2 -25.5t-8 -43.5q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 51.5zM758 1550q0 10 1 25.5t9 50.5q10 43 40.5 63.5t76.5 20.5h16q41 0 64.5 -18.5t23.5 -55.5q0 -10 -2 -25.5t-8 -43.5q-10 -45 -41 -65.5t-78 -20.5h-16 q-41 0 -63.5 17.5t-22.5 51.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="505" d="M29 25q0 12 8 53l235 1204q4 27 16.5 35t37.5 8h114q33 0 33 -25q0 -12 -8 -53l-236 -1204q-4 -27 -16 -35t-37 -8h-115q-33 0 -32 25zM47 1597q0 18 14 54l21 55q10 23 16 29t16 6q8 0 16.5 -3t26.5 -12l373 -165q25 -10 25 -23q0 -8 -4.5 -17.5t-10.5 -23.5l-18 -45 q-10 -20 -25 -20q-12 0 -26 6l-398 131q-27 8 -26 28z" />
<glyph unicode="&#xcd;" horiz-adv-x="505" d="M29 25q0 12 8 53l235 1204q4 27 16.5 35t37.5 8h114q33 0 33 -25q0 -12 -8 -53l-236 -1204q-4 -27 -16 -35t-37 -8h-115q-33 0 -32 25zM258 1538q0 12 24 23l373 165q18 8 26.5 11.5t16.5 3.5q10 0 16 -6t17 -29l20 -55q14 -35 14 -54q0 -20 -26 -28l-397 -131 q-16 -6 -27 -6q-14 0 -25 20l-18 45q-6 14 -10 23.5t-4 17.5z" />
<glyph unicode="&#xce;" horiz-adv-x="505" d="M29 25q0 12 8 53l235 1204q4 27 16.5 35t37.5 8h114q33 0 33 -25q0 -12 -8 -53l-236 -1204q-4 -27 -16 -35t-37 -8h-115q-33 0 -32 25zM101 1479q0 10 8 19t20 22l232 221q12 10 21 14t26 4h86q14 0 22.5 -3t18.5 -15l164 -234q16 -23 16 -34q0 -12 -21 -27l-34 -23 q-25 -16 -37 -16q-14 0 -31 18l-147 172h-9l-200 -167q-12 -10 -24.5 -18.5t-22.5 -8.5q-12 0 -20.5 5t-20.5 15l-27 25q-20 18 -20 31z" />
<glyph unicode="&#xcf;" horiz-adv-x="505" d="M29 25q0 12 8 53l235 1204q4 27 16.5 35t37.5 8h114q33 0 33 -25q0 -12 -8 -53l-236 -1204q-4 -27 -16 -35t-37 -8h-115q-33 0 -32 25zM152 1550q0 10 1 25.5t9 50.5q10 43 41 63.5t76 20.5h16q41 0 64.5 -18.5t23.5 -55.5q0 -10 -2 -25.5t-8 -43.5q-10 -45 -41 -65.5 t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 51.5zM486 1550q0 10 1 25.5t9 50.5q10 43 40.5 63.5t76.5 20.5h16q41 0 64.5 -18.5t23.5 -55.5q0 -10 -2 -25.5t-8 -43.5q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 51.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1255" d="M20 621q0 10 9 49l8 41q6 33 16 43t39 10h107l96 498q4 29 17.5 41t39.5 16q55 8 141.5 12t163.5 4q276 0 397.5 -97t121.5 -308q0 -37 -4.5 -86t-12.5 -105.5t-19.5 -116t-25.5 -116.5q-37 -141 -99.5 -237.5t-153.5 -157t-214 -86t-282 -25.5h-248q-59 0 -60 37 q0 8 4.5 26.5t10.5 47.5l94 483h-107q-39 0 -39 27zM285 182q0 -20 28 -20h95q113 0 196.5 20.5t144 64.5t101.5 115.5t70 176.5q29 106 42 203.5t13 162.5q0 78 -17.5 130t-56.5 83t-98.5 43.5t-145.5 12.5h-79.5t-71.5 -5q-31 -2 -37 -34l-74 -371h211q39 0 39 -27 q0 -10 -8 -49l-8 -41q-6 -33 -16.5 -43t-39.5 -10h-211l-65 -344q-6 -29 -9 -43t-3 -25z" />
<glyph unicode="&#xd1;" horiz-adv-x="1273" d="M18 25q0 12 5.5 34.5t11.5 55.5l229 1169q4 27 17.5 34t46.5 7h129q31 0 44 -9t21 -34l336 -1014h10l199 1018q4 23 14.5 31t34.5 8h105q35 0 34 -20q0 -12 -4 -30t-8 -44l-233 -1190q-4 -25 -17.5 -33t-40.5 -8h-127q-33 0 -48 8t-21 29l-340 1014h-10l-199 -1012 q-4 -23 -14.5 -31t-34.5 -8h-109q-31 0 -31 25zM446 1520q0 8 10 24q53 90 114.5 128t127.5 38q63 0 123 -47l37 -31q18 -16 33.5 -23t37.5 -7q33 0 59.5 24.5t59.5 65.5q8 12 23 12q8 0 24 -12l41 -33q14 -12 15 -23q0 -8 -11 -22q-111 -162 -235 -162q-39 0 -71 12.5 t-58 34.5l-37 31q-31 29 -68 29q-57 0 -118 -86q-12 -16 -25 -17q-12 0 -35 17l-31 22q-10 8 -13 13t-3 12z" />
<glyph unicode="&#xd2;" horiz-adv-x="1234" d="M80 336q0 86 16.5 199.5t44.5 255.5q33 162 80 268t118 169.5t168 89t232 25.5q197 0 306.5 -84.5t109.5 -273.5q0 -74 -17 -198t-48 -261q-35 -152 -84.5 -256t-119 -168.5t-164.5 -92t-222 -27.5q-205 0 -312.5 86t-107.5 268zM283 365q0 -111 53 -164.5t180 -53.5 q82 0 140.5 19.5t101.5 64.5t74.5 120t56.5 186q14 59 25.5 118.5t20.5 114.5t14 102t5 82q0 123 -55 173.5t-184 50.5q-145 0 -217 -60q-29 -23 -51.5 -53.5t-43 -73.5t-37 -102.5t-32.5 -138.5q-23 -109 -37 -205t-14 -180zM467 1597q0 18 14 54l21 55q10 23 16 29t16 6 q8 0 16.5 -3t26.5 -12l373 -165q25 -10 25 -23q0 -8 -4.5 -17.5t-10.5 -23.5l-18 -45q-10 -20 -25 -20q-12 0 -26 6l-398 131q-27 8 -26 28z" />
<glyph unicode="&#xd3;" horiz-adv-x="1234" d="M80 336q0 86 16.5 199.5t44.5 255.5q33 162 80 268t118 169.5t168 89t232 25.5q197 0 306.5 -84.5t109.5 -273.5q0 -74 -17 -198t-48 -261q-35 -152 -84.5 -256t-119 -168.5t-164.5 -92t-222 -27.5q-205 0 -312.5 86t-107.5 268zM283 365q0 -111 53 -164.5t180 -53.5 q82 0 140.5 19.5t101.5 64.5t74.5 120t56.5 186q14 59 25.5 118.5t20.5 114.5t14 102t5 82q0 123 -55 173.5t-184 50.5q-145 0 -217 -60q-29 -23 -51.5 -53.5t-43 -73.5t-37 -102.5t-32.5 -138.5q-23 -109 -37 -205t-14 -180zM566 1538q0 12 24 23l373 165q18 8 26.5 11.5 t16.5 3.5q10 0 16 -6t17 -29l20 -55q14 -35 14 -54q0 -20 -26 -28l-397 -131q-16 -6 -27 -6q-14 0 -25 20l-18 45q-6 14 -10 23.5t-4 17.5z" />
<glyph unicode="&#xd4;" horiz-adv-x="1234" d="M80 336q0 86 16.5 199.5t44.5 255.5q33 162 80 268t118 169.5t168 89t232 25.5q197 0 306.5 -84.5t109.5 -273.5q0 -74 -17 -198t-48 -261q-35 -152 -84.5 -256t-119 -168.5t-164.5 -92t-222 -27.5q-205 0 -312.5 86t-107.5 268zM283 365q0 -111 53 -164.5t180 -53.5 q82 0 140.5 19.5t101.5 64.5t74.5 120t56.5 186q14 59 25.5 118.5t20.5 114.5t14 102t5 82q0 123 -55 173.5t-184 50.5q-145 0 -217 -60q-29 -23 -51.5 -53.5t-43 -73.5t-37 -102.5t-32.5 -138.5q-23 -109 -37 -205t-14 -180zM455 1479q0 10 8 19t20 22l232 221q12 10 21 14 t26 4h86q14 0 22.5 -3t18.5 -15l164 -234q16 -23 16 -34q0 -12 -21 -27l-34 -23q-25 -16 -37 -16q-14 0 -31 18l-147 172h-9l-200 -167q-12 -10 -24.5 -18.5t-22.5 -8.5q-12 0 -20.5 5t-20.5 15l-27 25q-20 18 -20 31z" />
<glyph unicode="&#xd5;" horiz-adv-x="1234" d="M80 336q0 86 16.5 199.5t44.5 255.5q33 162 80 268t118 169.5t168 89t232 25.5q197 0 306.5 -84.5t109.5 -273.5q0 -74 -17 -198t-48 -261q-35 -152 -84.5 -256t-119 -168.5t-164.5 -92t-222 -27.5q-205 0 -312.5 86t-107.5 268zM283 365q0 -111 53 -164.5t180 -53.5 q82 0 140.5 19.5t101.5 64.5t74.5 120t56.5 186q14 59 25.5 118.5t20.5 114.5t14 102t5 82q0 123 -55 173.5t-184 50.5q-145 0 -217 -60q-29 -23 -51.5 -53.5t-43 -73.5t-37 -102.5t-32.5 -138.5q-23 -109 -37 -205t-14 -180zM436 1520q0 8 10 24q53 90 114.5 128t127.5 38 q63 0 123 -47l37 -31q18 -16 33.5 -23t37.5 -7q33 0 59.5 24.5t59.5 65.5q8 12 23 12q8 0 24 -12l41 -33q14 -12 15 -23q0 -8 -11 -22q-111 -162 -235 -162q-39 0 -71 12.5t-58 34.5l-37 31q-31 29 -68 29q-57 0 -118 -86q-12 -16 -25 -17q-12 0 -35 17l-31 22q-10 8 -13 13 t-3 12z" />
<glyph unicode="&#xd6;" horiz-adv-x="1234" d="M80 336q0 86 16.5 199.5t44.5 255.5q33 162 80 268t118 169.5t168 89t232 25.5q197 0 306.5 -84.5t109.5 -273.5q0 -74 -17 -198t-48 -261q-35 -152 -84.5 -256t-119 -168.5t-164.5 -92t-222 -27.5q-205 0 -312.5 86t-107.5 268zM283 365q0 -111 53 -164.5t180 -53.5 q82 0 140.5 19.5t101.5 64.5t74.5 120t56.5 186q14 59 25.5 118.5t20.5 114.5t14 102t5 82q0 123 -55 173.5t-184 50.5q-145 0 -217 -60q-29 -23 -51.5 -53.5t-43 -73.5t-37 -102.5t-32.5 -138.5q-23 -109 -37 -205t-14 -180zM508 1550q0 10 1 25.5t9 50.5q10 43 41 63.5 t76 20.5h16q41 0 64.5 -18.5t23.5 -55.5q0 -10 -2 -25.5t-8 -43.5q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 51.5zM842 1550q0 10 1 25.5t9 50.5q10 43 40.5 63.5t76.5 20.5h16q41 0 64.5 -18.5t23.5 -55.5q0 -10 -2 -25.5t-8 -43.5q-10 -45 -41 -65.5 t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 51.5z" />
<glyph unicode="&#xd7;" d="M100 270q0 18 29 41l260 219l-180 226q-12 14 -12 28q0 16 30 41l54 43q16 12 25 19.5t20 7.5q16 0 28 -18l178 -226l271 226q23 20 39 20q12 0 28 -18l43 -54q25 -27 25 -41t-31 -39l-268 -227l178 -217q12 -16 12 -29q0 -16 -30 -41l-54 -43q-16 -12 -25 -19t-20 -7 q-16 0 -28 18l-174 219l-265 -223q-23 -20 -38 -20q-14 0 -29 18l-53 70q-12 12 -13 26z" />
<glyph unicode="&#xd8;" horiz-adv-x="1234" d="M33 -88q0 12 10 25l113 161q-76 84 -76 238q0 86 16.5 199.5t44.5 255.5q33 162 80 268t118 169.5t168 89t232 25.5q150 0 242 -45l115 166q10 16 24 17q8 0 23 -8l41 -25q20 -12 26.5 -17.5t6.5 -11.5q0 -10 -9 -20l-125 -180q72 -84 72 -234q0 -74 -17 -198t-48 -261 q-35 -152 -84.5 -256t-119 -168.5t-164.5 -92t-222 -27.5q-141 0 -240 41l-104 -152q-14 -23 -27 -23q-4 0 -11 3.5t-26 13.5l-41 24q-18 12 -18 23zM283 365q0 -41 6 -72l585 850q-53 35 -159 35q-145 0 -217 -60q-29 -23 -51.5 -53.5t-43 -73.5t-37 -102.5t-32.5 -138.5 q-23 -109 -37 -205t-14 -180zM367 178q55 -31 149 -31q82 0 140.5 19.5t101.5 64.5t74.5 120t56.5 186q14 59 25.5 118.5t20.5 114.5t14 102t5 82q0 37 -6 68z" />
<glyph unicode="&#xd9;" horiz-adv-x="1226" d="M74 291q0 29 6 86t22 135l148 770q4 27 16.5 35t36.5 8h117q33 0 33 -25q0 -12 -9 -53l-145 -760q-10 -55 -18.5 -98t-8.5 -78q0 -84 58.5 -124t193.5 -40q70 0 123 16.5t93 52.5t67 94.5t43 142.5l162 829q4 27 16 35t37 8h117q33 0 32 -25q0 -12 -8 -53l-157 -809 q-25 -127 -65 -214t-104.5 -140t-156.5 -77.5t-221 -24.5q-211 0 -319.5 76.5t-108.5 232.5zM451 1597q0 18 14 54l21 55q10 23 16 29t16 6q8 0 16.5 -3t26.5 -12l373 -165q25 -10 25 -23q0 -8 -4.5 -17.5t-10.5 -23.5l-18 -45q-10 -20 -25 -20q-12 0 -26 6l-398 131 q-27 8 -26 28z" />
<glyph unicode="&#xda;" horiz-adv-x="1226" d="M74 291q0 29 6 86t22 135l148 770q4 27 16.5 35t36.5 8h117q33 0 33 -25q0 -12 -9 -53l-145 -760q-10 -55 -18.5 -98t-8.5 -78q0 -84 58.5 -124t193.5 -40q70 0 123 16.5t93 52.5t67 94.5t43 142.5l162 829q4 27 16 35t37 8h117q33 0 32 -25q0 -12 -8 -53l-157 -809 q-25 -127 -65 -214t-104.5 -140t-156.5 -77.5t-221 -24.5q-211 0 -319.5 76.5t-108.5 232.5zM553 1538q0 12 24 23l373 165q18 8 26.5 11.5t16.5 3.5q10 0 16 -6t17 -29l20 -55q14 -35 14 -54q0 -20 -26 -28l-397 -131q-16 -6 -27 -6q-14 0 -25 20l-18 45q-6 14 -10 23.5 t-4 17.5z" />
<glyph unicode="&#xdb;" horiz-adv-x="1226" d="M74 291q0 29 6 86t22 135l148 770q4 27 16.5 35t36.5 8h117q33 0 33 -25q0 -12 -9 -53l-145 -760q-10 -55 -18.5 -98t-8.5 -78q0 -84 58.5 -124t193.5 -40q70 0 123 16.5t93 52.5t67 94.5t43 142.5l162 829q4 27 16 35t37 8h117q33 0 32 -25q0 -12 -8 -53l-157 -809 q-25 -127 -65 -214t-104.5 -140t-156.5 -77.5t-221 -24.5q-211 0 -319.5 76.5t-108.5 232.5zM451 1479q0 10 8 19t20 22l232 221q12 10 21 14t26 4h86q14 0 22.5 -3t18.5 -15l164 -234q16 -23 16 -34q0 -12 -21 -27l-34 -23q-25 -16 -37 -16q-14 0 -31 18l-147 172h-9 l-200 -167q-12 -10 -24.5 -18.5t-22.5 -8.5q-12 0 -20.5 5t-20.5 15l-27 25q-20 18 -20 31z" />
<glyph unicode="&#xdc;" horiz-adv-x="1226" d="M74 291q0 29 6 86t22 135l148 770q4 27 16.5 35t36.5 8h117q33 0 33 -25q0 -12 -9 -53l-145 -760q-10 -55 -18.5 -98t-8.5 -78q0 -84 58.5 -124t193.5 -40q70 0 123 16.5t93 52.5t67 94.5t43 142.5l162 829q4 27 16 35t37 8h117q33 0 32 -25q0 -12 -8 -53l-157 -809 q-25 -127 -65 -214t-104.5 -140t-156.5 -77.5t-221 -24.5q-211 0 -319.5 76.5t-108.5 232.5zM494 1550q0 10 1 25.5t9 50.5q10 43 41 63.5t76 20.5h16q41 0 64.5 -18.5t23.5 -55.5q0 -10 -2 -25.5t-8 -43.5q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 51.5z M828 1550q0 10 1 25.5t9 50.5q10 43 40.5 63.5t76.5 20.5h16q41 0 64.5 -18.5t23.5 -55.5q0 -10 -2 -25.5t-8 -43.5q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 51.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="974" d="M141 1294q0 31 43 31h107q23 0 33 -5t12 -28q16 -182 47 -345t76 -324h14q111 162 211 330.5t186 338.5q10 23 21.5 28t34.5 5h123q27 0 26 -18q0 -10 -6 -22.5t-10 -22.5q-211 -424 -522 -832l-78 -397q-4 -18 -13.5 -25.5t-33.5 -7.5h-117q-35 0 -35 23q0 10 2 19t6 32 l72 364q-74 190 -120 390t-75 407q-2 10 -3 28.5t-1 30.5zM451 1538q0 12 24 23l373 165q18 8 26.5 11.5t16.5 3.5q10 0 16 -6t17 -29l20 -55q14 -35 14 -54q0 -20 -26 -28l-397 -131q-16 -6 -27 -6q-14 0 -25 20l-18 45q-6 14 -10 23.5t-4 17.5z" />
<glyph unicode="&#xde;" horiz-adv-x="1077" d="M18 25q0 12 9 53l235 1204q4 27 16.5 35t36.5 8h115q33 0 33 -25q0 -12 -8 -53l-33 -164q35 0 72.5 1t83.5 1q205 0 313 -70.5t108 -232.5q0 -49 -10 -120.5t-33 -137.5q-53 -152 -180 -224.5t-356 -72.5h-84t-82 2l-35 -186q-4 -27 -16.5 -35t-36.5 -8h-115 q-33 0 -33 25zM287 397q41 -2 72.5 -2h70.5q141 0 223 44t117 149q16 49 22.5 90t6.5 76q0 98 -62.5 132t-183.5 34h-82t-82 -2z" />
<glyph unicode="&#xdf;" horiz-adv-x="1085" d="M-33 -127q0 12 4 25.5t13 42.5q59 227 112.5 495.5t100.5 534.5q23 127 59.5 211t94 133t139 69.5t194.5 20.5q70 0 132.5 -17.5t109.5 -52.5t73.5 -89t26.5 -128q0 -147 -59.5 -245.5t-182.5 -135.5v-6q188 -43 189 -223q0 -141 -36 -241.5t-98.5 -164t-150.5 -92 t-192 -28.5q-121 0 -193 16q-23 6 -22 22q0 6 2 17.5t10 40.5l12 47q6 25 27 25q10 0 16 -1.5t17 -3.5q29 -4 60.5 -7t72.5 -3q135 0 205.5 88t70.5 262q0 86 -58.5 124t-170.5 38h-62q-25 0 -24 15q0 8 12 67l8 43q6 33 33 33h31q143 0 214.5 74.5t71.5 212.5 q0 80 -39 117.5t-133 37.5q-59 0 -102 -12t-76 -45t-56.5 -88t-39.5 -141l-139 -717q-18 -94 -40 -184.5t-48 -174.5q-8 -23 -18.5 -30t-39.5 -7h-96q-35 0 -35 25z" />
<glyph unicode="&#xe0;" horiz-adv-x="968" d="M29 195q0 41 9 94t19 96q35 133 132.5 182t267.5 49h73q31 0 69 -2t67 -6l10 51q6 35 10 62t4 53q0 51 -36 70.5t-126 19.5q-53 0 -116.5 -9t-116.5 -24q-18 -4 -28.5 -7t-18.5 -3q-12 0 -16.5 7.5t-8.5 23.5l-8 47q-6 33 -6 43q0 25 31 33q68 20 155.5 33.5t179.5 13.5 q143 0 226.5 -46t83.5 -159q0 -29 -5.5 -67.5t-13.5 -79.5l-125 -641q-4 -16 -11 -22.5t-25 -6.5h-82q-18 0 -24.5 7t-6.5 24v61h-4q-43 -59 -108.5 -84.5t-164.5 -25.5h-30q-129 0 -192.5 57t-63.5 156zM211 221q0 -45 30.5 -65.5t100.5 -20.5h23q100 0 162.5 43t82.5 148 l25 129q-31 4 -62.5 6t-66.5 2h-60q-51 0 -86.5 -6t-61.5 -19.5t-42 -37t-27 -58.5q-10 -31 -14 -63.5t-4 -57.5zM315 1341q0 14 17 54l22 53q10 23 16.5 29t16.5 6t43 -17l369 -178q23 -10 22 -22q0 -8 -4 -17.5t-10 -23.5l-21 -43q-10 -20 -24 -21q-12 0 -27 6l-393 146 q-27 8 -27 28z" />
<glyph unicode="&#xe1;" horiz-adv-x="968" d="M29 195q0 41 9 94t19 96q35 133 132.5 182t267.5 49h73q31 0 69 -2t67 -6l10 51q6 35 10 62t4 53q0 51 -36 70.5t-126 19.5q-53 0 -116.5 -9t-116.5 -24q-18 -4 -28.5 -7t-18.5 -3q-12 0 -16.5 7.5t-8.5 23.5l-8 47q-6 33 -6 43q0 25 31 33q68 20 155.5 33.5t179.5 13.5 q143 0 226.5 -46t83.5 -159q0 -29 -5.5 -67.5t-13.5 -79.5l-125 -641q-4 -16 -11 -22.5t-25 -6.5h-82q-18 0 -24.5 7t-6.5 24v61h-4q-43 -59 -108.5 -84.5t-164.5 -25.5h-30q-129 0 -192.5 57t-63.5 156zM211 221q0 -45 30.5 -65.5t100.5 -20.5h23q100 0 162.5 43t82.5 148 l25 129q-31 4 -62.5 6t-66.5 2h-60q-51 0 -86.5 -6t-61.5 -19.5t-42 -37t-27 -58.5q-10 -31 -14 -63.5t-4 -57.5zM413 1266q0 12 23 22l368 178q33 16 44 17q10 0 16 -6.5t16 -28.5l23 -53q16 -39 16 -54q0 -20 -26 -28l-394 -146q-16 -6 -26 -6q-14 0 -25 21l-20 43 q-6 14 -10.5 23t-4.5 18z" />
<glyph unicode="&#xe2;" horiz-adv-x="968" d="M29 195q0 41 9 94t19 96q35 133 132.5 182t267.5 49h73q31 0 69 -2t67 -6l10 51q6 35 10 62t4 53q0 51 -36 70.5t-126 19.5q-53 0 -116.5 -9t-116.5 -24q-18 -4 -28.5 -7t-18.5 -3q-12 0 -16.5 7.5t-8.5 23.5l-8 47q-6 33 -6 43q0 25 31 33q68 20 155.5 33.5t179.5 13.5 q143 0 226.5 -46t83.5 -159q0 -29 -5.5 -67.5t-13.5 -79.5l-125 -641q-4 -16 -11 -22.5t-25 -6.5h-82q-18 0 -24.5 7t-6.5 24v61h-4q-43 -59 -108.5 -84.5t-164.5 -25.5h-30q-129 0 -192.5 57t-63.5 156zM211 221q0 -45 30.5 -65.5t100.5 -20.5h23q100 0 162.5 43t82.5 148 l25 129q-31 4 -62.5 6t-66.5 2h-60q-51 0 -86.5 -6t-61.5 -19.5t-42 -37t-27 -58.5q-10 -31 -14 -63.5t-4 -57.5zM313 1210q0 10 8 19.5t21 21.5l231 222q12 10 21.5 14t25.5 4h86q14 0 22.5 -3t18.5 -15l164 -234q16 -23 16 -35t-20 -26l-35 -23q-25 -16 -37 -16 q-14 0 -30 18l-148 172h-8l-201 -168q-12 -10 -24.5 -18t-22.5 -8q-12 0 -20.5 5t-20.5 15l-26 25q-20 18 -21 30z" />
<glyph unicode="&#xe3;" horiz-adv-x="968" d="M29 195q0 41 9 94t19 96q35 133 132.5 182t267.5 49h73q31 0 69 -2t67 -6l10 51q6 35 10 62t4 53q0 51 -36 70.5t-126 19.5q-53 0 -116.5 -9t-116.5 -24q-18 -4 -28.5 -7t-18.5 -3q-12 0 -16.5 7.5t-8.5 23.5l-8 47q-6 33 -6 43q0 25 31 33q68 20 155.5 33.5t179.5 13.5 q143 0 226.5 -46t83.5 -159q0 -29 -5.5 -67.5t-13.5 -79.5l-125 -641q-4 -16 -11 -22.5t-25 -6.5h-82q-18 0 -24.5 7t-6.5 24v61h-4q-43 -59 -108.5 -84.5t-164.5 -25.5h-30q-129 0 -192.5 57t-63.5 156zM211 221q0 -45 30.5 -65.5t100.5 -20.5h23q100 0 162.5 43t82.5 148 l25 129q-31 4 -62.5 6t-66.5 2h-60q-51 0 -86.5 -6t-61.5 -19.5t-42 -37t-27 -58.5q-10 -31 -14 -63.5t-4 -57.5zM276 1239q0 8 10 25q53 90 114.5 128t127.5 38q63 0 123 -48l37 -30q18 -16 33.5 -23.5t37.5 -7.5q33 0 59.5 24.5t59.5 65.5q8 12 23 12q8 0 24 -12l41 -33 q14 -12 14 -22q0 -8 -10 -23q-111 -162 -235 -162q-39 0 -71 12.5t-58 35.5l-37 30q-31 29 -68 29q-57 0 -119 -86q-12 -16 -24 -16t-35 16l-31 22q-10 8 -13 13.5t-3 11.5z" />
<glyph unicode="&#xe4;" horiz-adv-x="968" d="M29 195q0 41 9 94t19 96q35 133 132.5 182t267.5 49h73q31 0 69 -2t67 -6l10 51q6 35 10 62t4 53q0 51 -36 70.5t-126 19.5q-53 0 -116.5 -9t-116.5 -24q-18 -4 -28.5 -7t-18.5 -3q-12 0 -16.5 7.5t-8.5 23.5l-8 47q-6 33 -6 43q0 25 31 33q68 20 155.5 33.5t179.5 13.5 q143 0 226.5 -46t83.5 -159q0 -29 -5.5 -67.5t-13.5 -79.5l-125 -641q-4 -16 -11 -22.5t-25 -6.5h-82q-18 0 -24.5 7t-6.5 24v61h-4q-43 -59 -108.5 -84.5t-164.5 -25.5h-30q-129 0 -192.5 57t-63.5 156zM211 221q0 -45 30.5 -65.5t100.5 -20.5h23q100 0 162.5 43t82.5 148 l25 129q-31 4 -62.5 6t-66.5 2h-60q-51 0 -86.5 -6t-61.5 -19.5t-42 -37t-27 -58.5q-10 -31 -14 -63.5t-4 -57.5zM344 1268q0 10 1 25.5t9 49.5q10 43 41 63.5t76 20.5h16q41 0 64.5 -18t23.5 -55q0 -10 -2 -25.5t-8 -44.5q-10 -45 -41 -65.5t-78 -20.5h-16 q-41 0 -63.5 17.5t-22.5 52.5zM678 1268q0 10 1 25.5t9 49.5q10 43 41 63.5t76 20.5h16q41 0 64.5 -18t23.5 -55q0 -10 -2 -25.5t-8 -44.5q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 52.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="968" d="M29 195q0 41 9 94t19 96q35 133 132.5 182t267.5 49h73q31 0 69 -2t67 -6l10 51q6 35 10 62t4 53q0 51 -36 70.5t-126 19.5q-53 0 -116.5 -9t-116.5 -24q-18 -4 -28.5 -7t-18.5 -3q-12 0 -16.5 7.5t-8.5 23.5l-8 47q-6 33 -6 43q0 25 31 33q68 20 155.5 33.5t179.5 13.5 q143 0 226.5 -46t83.5 -159q0 -29 -5.5 -67.5t-13.5 -79.5l-125 -641q-4 -16 -11 -22.5t-25 -6.5h-82q-18 0 -24.5 7t-6.5 24v61h-4q-43 -59 -108.5 -84.5t-164.5 -25.5h-30q-129 0 -192.5 57t-63.5 156zM211 221q0 -45 30.5 -65.5t100.5 -20.5h23q100 0 162.5 43t82.5 148 l25 129q-31 4 -62.5 6t-66.5 2h-60q-51 0 -86.5 -6t-61.5 -19.5t-42 -37t-27 -58.5q-10 -31 -14 -63.5t-4 -57.5zM439 1284q0 98 64.5 157.5t158.5 59.5q76 0 123 -44t47 -118q0 -98 -61.5 -160.5t-159.5 -62.5q-78 0 -125 46t-47 122zM555 1296q0 -29 18.5 -47t51.5 -18 q41 0 65.5 24.5t24.5 65.5q0 29 -18.5 47t-51.5 18q-41 0 -65.5 -24.5t-24.5 -65.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1529" d="M29 180q0 47 10 103.5t20 99.5q35 133 132.5 183t267.5 50h80q31 0 65.5 -2t63.5 -6q2 12 4 23.5t6 25.5l6 33q8 41 8 80q0 53 -35.5 73.5t-126.5 20.5q-53 0 -116.5 -9t-116.5 -24q-18 -4 -28.5 -7t-18.5 -3q-23 0 -25 31l-8 47q-2 12 -3 24.5t-1 22.5q0 20 29 29 q68 20 155.5 33.5t180.5 13.5q199 0 253 -94q51 47 122 70.5t171 23.5q164 0 243 -70.5t79 -199.5q0 -70 -13.5 -142.5t-29.5 -154.5q-6 -27 -20.5 -38t-45.5 -11h-518q-6 -35 -10 -65t-4 -62q0 -82 52 -111t157 -29q57 0 124.5 11.5t121.5 27.5q6 2 13 4t15 2q14 0 21 -24 l12 -49q8 -31 8 -45q0 -16 -20 -25q-61 -25 -152.5 -42t-173.5 -17q-141 0 -215 34.5t-102 96.5q-49 -66 -131 -98.5t-218 -32.5h-28q-125 0 -192.5 48t-67.5 150zM213 219q0 -47 30.5 -65.5t100.5 -18.5h23q100 0 161.5 42t83.5 149l27 131q-61 6 -131 6h-59q-51 0 -87 -6 t-62 -20.5t-42 -38t-27 -58.5q-10 -31 -14 -63.5t-4 -57.5zM848 555h354q27 0 33 25q10 37 15 77.5t5 73.5q0 74 -36.5 105.5t-122.5 31.5q-98 0 -151.5 -49t-80.5 -180z" />
<glyph unicode="&#xe7;" horiz-adv-x="901" d="M55 233q0 35 6.5 83.5t14.5 101.5t18 104t19 92q25 117 63.5 195t95 126t131 67.5t168.5 19.5q72 0 148 -19.5t135 -52.5q20 -12 20 -26q0 -6 -3 -14.5t-13 -26.5l-33 -62q-6 -10 -10 -15t-14 -5q-8 0 -33 12q-47 23 -96 36t-101 13q-61 0 -106 -15.5t-78 -51t-56.5 -95 t-41.5 -147.5q-6 -37 -14.5 -76t-14.5 -77t-10 -72.5t-4 -59.5q0 -72 46 -100.5t148 -28.5q51 0 109.5 9.5t103.5 23.5q6 2 13.5 4t15.5 2q14 0 20 -24l9 -37q4 -23 7 -36t3 -24q0 -18 -25 -26q-53 -20 -119.5 -31.5t-132.5 -15.5l-49 -111q16 4 35 6t37 2q59 0 95 -33.5 t36 -95.5q0 -117 -68.5 -175t-185.5 -58q-47 0 -98 8t-84 20q-18 8 -19 21q0 6 9 29l14 36q8 23 18 23q6 0 10.5 -1t10.5 -3q29 -10 63.5 -15.5t61.5 -5.5q63 0 91 22.5t28 70.5q0 47 -64 47q-20 0 -37.5 -2t-28.5 -5q-10 -2 -18 -3t-14 -1q-14 0 -23 15l-8 14q-8 16 -8 25 q0 6 3 14t13 31l58 127q-137 14 -202 73.5t-65 173.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="966" d="M55 252q0 59 15.5 148t44.5 233q20 100 54 173t88 121t130 71.5t178 23.5q160 0 240 -72.5t80 -197.5q0 -66 -12.5 -143t-28.5 -154q-6 -27 -19.5 -38t-46.5 -11h-518q-6 -35 -12 -65t-6 -62q0 -82 54 -110t159 -28q57 0 124.5 11.5t120.5 27.5q6 2 13.5 4t15.5 2 q14 0 21 -24l12 -51q8 -31 8 -45q0 -16 -20 -25q-61 -25 -152.5 -42t-173.5 -17q-104 0 -175 18t-114 53t-61.5 85t-18.5 114zM282 1341q0 14 17 54l22 53q10 23 16.5 29t16.5 6t43 -17l369 -178q23 -10 22 -22q0 -8 -4 -17.5t-10 -23.5l-21 -43q-10 -20 -24 -21 q-12 0 -27 6l-393 146q-27 8 -27 28zM289 555h358q27 0 33 25q10 37 14 77.5t4 73.5q0 74 -37.5 105.5t-123.5 31.5q-96 0 -152.5 -53t-79.5 -176z" />
<glyph unicode="&#xe9;" horiz-adv-x="966" d="M55 252q0 59 15.5 148t44.5 233q20 100 54 173t88 121t130 71.5t178 23.5q160 0 240 -72.5t80 -197.5q0 -66 -12.5 -143t-28.5 -154q-6 -27 -19.5 -38t-46.5 -11h-518q-6 -35 -12 -65t-6 -62q0 -82 54 -110t159 -28q57 0 124.5 11.5t120.5 27.5q6 2 13.5 4t15.5 2 q14 0 21 -24l12 -51q8 -31 8 -45q0 -16 -20 -25q-61 -25 -152.5 -42t-173.5 -17q-104 0 -175 18t-114 53t-61.5 85t-18.5 114zM289 555h358q27 0 33 25q10 37 14 77.5t4 73.5q0 74 -37.5 105.5t-123.5 31.5q-96 0 -152.5 -53t-79.5 -176zM411 1266q0 12 23 22l368 178 q33 16 44 17q10 0 16 -6.5t16 -28.5l23 -53q16 -39 16 -54q0 -20 -26 -28l-394 -146q-16 -6 -26 -6q-14 0 -25 21l-20 43q-6 14 -10.5 23t-4.5 18z" />
<glyph unicode="&#xea;" horiz-adv-x="966" d="M55 252q0 59 15.5 148t44.5 233q20 100 54 173t88 121t130 71.5t178 23.5q160 0 240 -72.5t80 -197.5q0 -66 -12.5 -143t-28.5 -154q-6 -27 -19.5 -38t-46.5 -11h-518q-6 -35 -12 -65t-6 -62q0 -82 54 -110t159 -28q57 0 124.5 11.5t120.5 27.5q6 2 13.5 4t15.5 2 q14 0 21 -24l12 -51q8 -31 8 -45q0 -16 -20 -25q-61 -25 -152.5 -42t-173.5 -17q-104 0 -175 18t-114 53t-61.5 85t-18.5 114zM280 1210q0 10 8 19.5t21 21.5l231 222q12 10 21.5 14t25.5 4h86q14 0 22.5 -3t18.5 -15l164 -234q16 -23 16 -35t-20 -26l-35 -23 q-25 -16 -37 -16q-14 0 -30 18l-148 172h-8l-201 -168q-12 -10 -24.5 -18t-22.5 -8q-12 0 -20.5 5t-20.5 15l-26 25q-20 18 -21 30zM289 555h358q27 0 33 25q10 37 14 77.5t4 73.5q0 74 -37.5 105.5t-123.5 31.5q-96 0 -152.5 -53t-79.5 -176z" />
<glyph unicode="&#xeb;" horiz-adv-x="966" d="M55 252q0 59 15.5 148t44.5 233q20 100 54 173t88 121t130 71.5t178 23.5q160 0 240 -72.5t80 -197.5q0 -66 -12.5 -143t-28.5 -154q-6 -27 -19.5 -38t-46.5 -11h-518q-6 -35 -12 -65t-6 -62q0 -82 54 -110t159 -28q57 0 124.5 11.5t120.5 27.5q6 2 13.5 4t15.5 2 q14 0 21 -24l12 -51q8 -31 8 -45q0 -16 -20 -25q-61 -25 -152.5 -42t-173.5 -17q-104 0 -175 18t-114 53t-61.5 85t-18.5 114zM289 555h358q27 0 33 25q10 37 14 77.5t4 73.5q0 74 -37.5 105.5t-123.5 31.5q-96 0 -152.5 -53t-79.5 -176zM320 1268q0 10 1 25.5t9 49.5 q10 43 41 63.5t76 20.5h16q41 0 64.5 -18t23.5 -55q0 -10 -2 -25.5t-8 -44.5q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 52.5zM654 1268q0 10 1 25.5t9 49.5q10 43 41 63.5t76 20.5h16q41 0 64.5 -18t23.5 -55q0 -10 -2 -25.5t-8 -44.5q-10 -45 -41 -65.5 t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 52.5z" />
<glyph unicode="&#xec;" horiz-adv-x="454" d="M-15 1341q0 14 17 54l22 53q10 23 16.5 29t16.5 6t43 -17l369 -178q23 -10 22 -22q0 -8 -4 -17.5t-10 -23.5l-21 -43q-10 -20 -24 -21q-12 0 -27 6l-393 146q-27 8 -27 28zM29 25q0 8 5 36.5t13 63.5l166 842q6 37 45 37h109q37 0 36 -25q0 -8 -5 -37t-13 -63l-164 -842 q-6 -37 -45 -37h-110q-37 0 -37 25z" />
<glyph unicode="&#xed;" horiz-adv-x="454" d="M29 25q0 8 5 36.5t13 63.5l166 842q6 37 45 37h109q37 0 36 -25q0 -8 -5 -37t-13 -63l-164 -842q-6 -37 -45 -37h-110q-37 0 -37 25zM186 1266q0 12 23 22l368 178q33 16 44 17q10 0 16 -6.5t16 -28.5l23 -53q16 -39 16 -54q0 -20 -26 -28l-394 -146q-16 -6 -26 -6 q-14 0 -25 21l-20 43q-6 14 -10.5 23t-4.5 18z" />
<glyph unicode="&#xee;" horiz-adv-x="454" d="M29 25q0 8 5 36.5t13 63.5l166 842q6 37 45 37h109q37 0 36 -25q0 -8 -5 -37t-13 -63l-164 -842q-6 -37 -45 -37h-110q-37 0 -37 25zM43 1210q0 10 8 19.5t21 21.5l231 222q12 10 21.5 14t25.5 4h86q14 0 22.5 -3t18.5 -15l164 -234q16 -23 16 -35t-20 -26l-35 -23 q-25 -16 -37 -16q-14 0 -30 18l-148 172h-8l-201 -168q-12 -10 -24.5 -18t-22.5 -8q-12 0 -20.5 5t-20.5 15l-26 25q-20 18 -21 30z" />
<glyph unicode="&#xef;" horiz-adv-x="454" d="M29 25q0 8 5 36.5t13 63.5l166 842q6 37 45 37h109q37 0 36 -25q0 -8 -5 -37t-13 -63l-164 -842q-6 -37 -45 -37h-110q-37 0 -37 25zM84 1268q0 10 1 25.5t9 49.5q10 43 41 63.5t76 20.5h16q41 0 64.5 -18t23.5 -55q0 -10 -2 -25.5t-8 -44.5q-10 -45 -41 -65.5t-78 -20.5 h-16q-41 0 -63.5 17.5t-22.5 52.5zM418 1268q0 10 1 25.5t9 49.5q10 43 41 63.5t76 20.5h16q41 0 64.5 -18t23.5 -55q0 -10 -2 -25.5t-8 -44.5q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 52.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1015" d="M51 252q0 33 13.5 122t33.5 199q18 100 49 171t80 116t118 65.5t163 20.5q78 0 135 -19.5t88 -49.5h4q-4 113 -44 190.5t-99 124.5l-123 -100q-16 -12 -25 -13q-10 0 -24 15l-25 28q-18 20 -18 31q0 14 29 37l92 74q-29 16 -55.5 30.5t-49.5 24.5q-16 8 -16 20 q0 10 24 43l37 50q12 14 25 14q8 0 24 -6q39 -16 71 -32.5t65 -39.5l114 96q16 12 25 13q6 0 10 -2t14 -13l27 -26q18 -18 18 -31q0 -14 -28 -37l-86 -74q90 -76 145.5 -196.5t55.5 -304.5q0 -31 -5.5 -122t-33.5 -241q-23 -117 -51.5 -202t-80 -139t-133.5 -80.5 t-213 -26.5q-178 0 -264 64.5t-86 205.5zM238 285q0 -78 42 -113t140 -35q59 0 101 12.5t72 42t49.5 79.5t35.5 126q16 74 25.5 131.5t9.5 108.5q0 82 -47.5 118t-139.5 36q-127 0 -179 -64.5t-73 -200.5q-14 -80 -25 -141t-11 -100z" />
<glyph unicode="&#xf1;" horiz-adv-x="1036" d="M18 25q0 8 5.5 35.5t13.5 64.5l168 850q6 29 41 29h71q18 0 26.5 -4.5t6.5 -20.5l-4 -72h6q51 53 119 84t188 31q137 0 210 -57.5t73 -167.5q0 -43 -8 -104.5t-21 -129.5l-98 -526q-6 -37 -45 -37h-108q-37 0 -37 25q0 8 5 35.5t13 64.5l88 469q8 45 14.5 84t6.5 70 q0 63 -41 90.5t-119 27.5q-53 0 -97 -11t-78 -41t-58.5 -83t-41.5 -137l-110 -557q-4 -20 -13.5 -28.5t-31.5 -8.5h-107q-37 0 -37 25zM320 1239q0 8 10 25q53 90 114.5 128t127.5 38q63 0 123 -48l37 -30q18 -16 33.5 -23.5t37.5 -7.5q33 0 59.5 24.5t59.5 65.5q8 12 23 12 q8 0 24 -12l41 -33q14 -12 14 -22q0 -8 -10 -23q-111 -162 -235 -162q-39 0 -71 12.5t-58 35.5l-37 30q-31 29 -68 29q-57 0 -119 -86q-12 -16 -24 -16t-35 16l-31 22q-10 8 -13 13.5t-3 11.5z" />
<glyph unicode="&#xf2;" horiz-adv-x="1009" d="M55 264q0 53 12.5 140.5t28.5 173.5q25 123 62 207.5t93 137t134 76t184 23.5q186 0 271.5 -68.5t85.5 -214.5q0 -53 -12.5 -140t-28.5 -173q-25 -123 -62 -208t-93 -137t-134 -75.5t-184 -23.5q-186 0 -271.5 68.5t-85.5 213.5zM246 291q0 -86 42 -121t140 -35 q115 0 174.5 65.5t89.5 219.5q23 115 33 185.5t10 107.5q0 86 -42 120.5t-140 34.5q-115 0 -174.5 -65.5t-89.5 -218.5q-23 -115 -33 -185.5t-10 -107.5zM315 1341q0 14 17 54l22 53q10 23 16.5 29t16.5 6t43 -17l369 -178q23 -10 22 -22q0 -8 -4 -17.5t-10 -23.5l-21 -43 q-10 -20 -24 -21q-12 0 -27 6l-393 146q-27 8 -27 28z" />
<glyph unicode="&#xf3;" horiz-adv-x="1009" d="M55 264q0 53 12.5 140.5t28.5 173.5q25 123 62 207.5t93 137t134 76t184 23.5q186 0 271.5 -68.5t85.5 -214.5q0 -53 -12.5 -140t-28.5 -173q-25 -123 -62 -208t-93 -137t-134 -75.5t-184 -23.5q-186 0 -271.5 68.5t-85.5 213.5zM246 291q0 -86 42 -121t140 -35 q115 0 174.5 65.5t89.5 219.5q23 115 33 185.5t10 107.5q0 86 -42 120.5t-140 34.5q-115 0 -174.5 -65.5t-89.5 -218.5q-23 -115 -33 -185.5t-10 -107.5zM434 1266q0 12 23 22l368 178q33 16 44 17q10 0 16 -6.5t16 -28.5l23 -53q16 -39 16 -54q0 -20 -26 -28l-394 -146 q-16 -6 -26 -6q-14 0 -25 21l-20 43q-6 14 -10.5 23t-4.5 18z" />
<glyph unicode="&#xf4;" horiz-adv-x="1009" d="M55 264q0 53 12.5 140.5t28.5 173.5q25 123 62 207.5t93 137t134 76t184 23.5q186 0 271.5 -68.5t85.5 -214.5q0 -53 -12.5 -140t-28.5 -173q-25 -123 -62 -208t-93 -137t-134 -75.5t-184 -23.5q-186 0 -271.5 68.5t-85.5 213.5zM246 291q0 -86 42 -121t140 -35 q115 0 174.5 65.5t89.5 219.5q23 115 33 185.5t10 107.5q0 86 -42 120.5t-140 34.5q-115 0 -174.5 -65.5t-89.5 -218.5q-23 -115 -33 -185.5t-10 -107.5zM324 1210q0 10 8 19.5t21 21.5l231 222q12 10 21.5 14t25.5 4h86q14 0 22.5 -3t18.5 -15l164 -234q16 -23 16 -35 t-20 -26l-35 -23q-25 -16 -37 -16q-14 0 -30 18l-148 172h-8l-201 -168q-12 -10 -24.5 -18t-22.5 -8q-12 0 -20.5 5t-20.5 15l-26 25q-20 18 -21 30z" />
<glyph unicode="&#xf5;" horiz-adv-x="1009" d="M55 264q0 53 12.5 140.5t28.5 173.5q25 123 62 207.5t93 137t134 76t184 23.5q186 0 271.5 -68.5t85.5 -214.5q0 -53 -12.5 -140t-28.5 -173q-25 -123 -62 -208t-93 -137t-134 -75.5t-184 -23.5q-186 0 -271.5 68.5t-85.5 213.5zM246 291q0 -86 42 -121t140 -35 q115 0 174.5 65.5t89.5 219.5q23 115 33 185.5t10 107.5q0 86 -42 120.5t-140 34.5q-115 0 -174.5 -65.5t-89.5 -218.5q-23 -115 -33 -185.5t-10 -107.5zM301 1239q0 8 10 25q53 90 114.5 128t127.5 38q63 0 123 -48l37 -30q18 -16 33.5 -23.5t37.5 -7.5q33 0 59.5 24.5 t59.5 65.5q8 12 23 12q8 0 24 -12l41 -33q14 -12 14 -22q0 -8 -10 -23q-111 -162 -235 -162q-39 0 -71 12.5t-58 35.5l-37 30q-31 29 -68 29q-57 0 -119 -86q-12 -16 -24 -16t-35 16l-31 22q-10 8 -13 13.5t-3 11.5z" />
<glyph unicode="&#xf6;" horiz-adv-x="1009" d="M55 264q0 53 12.5 140.5t28.5 173.5q25 123 62 207.5t93 137t134 76t184 23.5q186 0 271.5 -68.5t85.5 -214.5q0 -53 -12.5 -140t-28.5 -173q-25 -123 -62 -208t-93 -137t-134 -75.5t-184 -23.5q-186 0 -271.5 68.5t-85.5 213.5zM246 291q0 -86 42 -121t140 -35 q115 0 174.5 65.5t89.5 219.5q23 115 33 185.5t10 107.5q0 86 -42 120.5t-140 34.5q-115 0 -174.5 -65.5t-89.5 -218.5q-23 -115 -33 -185.5t-10 -107.5zM361 1268q0 10 1 25.5t9 49.5q10 43 41 63.5t76 20.5h16q41 0 64.5 -18t23.5 -55q0 -10 -2 -25.5t-8 -44.5 q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 52.5zM695 1268q0 10 1 25.5t9 49.5q10 43 41 63.5t76 20.5h16q41 0 64.5 -18t23.5 -55q0 -10 -2 -25.5t-8 -44.5q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 52.5z" />
<glyph unicode="&#xf7;" d="M53 489l15 84q4 27 16 37.5t43 10.5h801q29 0 36 -10.5t3 -37.5l-15 -84q-4 -29 -17 -38t-42 -9h-801q-29 0 -36 9.5t-3 37.5zM305 113q0 10 1 21t3 22l6 32q10 59 44 83t94 24h12q98 0 98 -74q0 -10 -1 -21.5t-3 -21.5l-6 -33q-10 -59 -44 -82.5t-93 -23.5h-13 q-98 0 -98 74zM446 842q0 10 1.5 21t3.5 22l6 33q10 59 44 82.5t93 23.5h12q98 0 99 -74q0 -10 -1.5 -21t-3.5 -22l-6 -33q-10 -59 -44 -82.5t-93 -23.5h-12q-98 0 -99 74z" />
<glyph unicode="&#xf8;" horiz-adv-x="1009" d="M51 -96q0 10 10 24l78 123q-84 66 -84 213q0 53 12.5 140.5t28.5 173.5q25 123 62 207.5t93 137t134 76t184 23.5q100 0 174 -23l84 131q12 18 25 19q12 0 27 -8l36 -23q18 -10 19 -22q0 -10 -10 -25l-78 -123q80 -72 80 -209q0 -53 -12.5 -140t-28.5 -173 q-25 -123 -62 -208t-93 -137t-134 -75.5t-184 -23.5q-100 0 -170 18l-84 -131q-12 -18 -25 -19q-12 0 -27 9l-36 22q-18 10 -19 23zM246 291q0 -37 6 -64l399 627q-37 14 -98 14q-115 0 -174.5 -65.5t-89.5 -218.5q-23 -115 -33 -185.5t-10 -107.5zM334 145q20 -6 42.5 -8 t51.5 -2q115 0 174.5 65.5t89.5 219.5q23 115 33 185.5t10 107.5q0 31 -6 53z" />
<glyph unicode="&#xf9;" horiz-adv-x="1011" d="M63 219q0 29 3.5 68t13.5 90l115 590q4 20 13 28.5t32 8.5h108q37 0 37 -25q0 -10 -5 -38t-13 -62l-99 -508q-6 -33 -10 -61.5t-4 -55.5q0 -55 35 -83t129 -28q61 0 100 10.5t68 34.5q29 25 46 60t34 112l118 607q4 20 13.5 28.5t31.5 8.5h107q37 0 37 -25 q0 -23 -17 -100l-106 -539q-23 -117 -54.5 -183.5t-88.5 -105.5q-53 -37 -126 -53t-178 -16q-166 0 -253 57t-87 180zM301 1341q0 14 17 54l22 53q10 23 16.5 29t16.5 6t43 -17l369 -178q23 -10 22 -22q0 -8 -4 -17.5t-10 -23.5l-21 -43q-10 -20 -24 -21q-12 0 -27 6 l-393 146q-27 8 -27 28z" />
<glyph unicode="&#xfa;" horiz-adv-x="1011" d="M63 219q0 29 3.5 68t13.5 90l115 590q4 20 13 28.5t32 8.5h108q37 0 37 -25q0 -10 -5 -38t-13 -62l-99 -508q-6 -33 -10 -61.5t-4 -55.5q0 -55 35 -83t129 -28q61 0 100 10.5t68 34.5q29 25 46 60t34 112l118 607q4 20 13.5 28.5t31.5 8.5h107q37 0 37 -25 q0 -23 -17 -100l-106 -539q-23 -117 -54.5 -183.5t-88.5 -105.5q-53 -37 -126 -53t-178 -16q-166 0 -253 57t-87 180zM460 1266q0 12 23 22l368 178q33 16 44 17q10 0 16 -6.5t16 -28.5l23 -53q16 -39 16 -54q0 -20 -26 -28l-394 -146q-16 -6 -26 -6q-14 0 -25 21l-20 43 q-6 14 -10.5 23t-4.5 18z" />
<glyph unicode="&#xfb;" horiz-adv-x="1011" d="M63 219q0 29 3.5 68t13.5 90l115 590q4 20 13 28.5t32 8.5h108q37 0 37 -25q0 -10 -5 -38t-13 -62l-99 -508q-6 -33 -10 -61.5t-4 -55.5q0 -55 35 -83t129 -28q61 0 100 10.5t68 34.5q29 25 46 60t34 112l118 607q4 20 13.5 28.5t31.5 8.5h107q37 0 37 -25 q0 -23 -17 -100l-106 -539q-23 -117 -54.5 -183.5t-88.5 -105.5q-53 -37 -126 -53t-178 -16q-166 0 -253 57t-87 180zM315 1210q0 10 8 19.5t21 21.5l231 222q12 10 21.5 14t25.5 4h86q14 0 22.5 -3t18.5 -15l164 -234q16 -23 16 -35t-20 -26l-35 -23q-25 -16 -37 -16 q-14 0 -30 18l-148 172h-8l-201 -168q-12 -10 -24.5 -18t-22.5 -8q-12 0 -20.5 5t-20.5 15l-26 25q-20 18 -21 30z" />
<glyph unicode="&#xfc;" horiz-adv-x="1011" d="M63 219q0 29 3.5 68t13.5 90l115 590q4 20 13 28.5t32 8.5h108q37 0 37 -25q0 -10 -5 -38t-13 -62l-99 -508q-6 -33 -10 -61.5t-4 -55.5q0 -55 35 -83t129 -28q61 0 100 10.5t68 34.5q29 25 46 60t34 112l118 607q4 20 13.5 28.5t31.5 8.5h107q37 0 37 -25 q0 -23 -17 -100l-106 -539q-23 -117 -54.5 -183.5t-88.5 -105.5q-53 -37 -126 -53t-178 -16q-166 0 -253 57t-87 180zM355 1268q0 10 1 25.5t9 49.5q10 43 41 63.5t76 20.5h16q41 0 64.5 -18t23.5 -55q0 -10 -2 -25.5t-8 -44.5q-10 -45 -41 -65.5t-78 -20.5h-16 q-41 0 -63.5 17.5t-22.5 52.5zM689 1268q0 10 1 25.5t9 49.5q10 43 41 63.5t76 20.5h16q41 0 64.5 -18t23.5 -55q0 -10 -2 -25.5t-8 -44.5q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 52.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="890" d="M78 -299q0 12 11 29.5t40 58.5q31 47 64.5 102.5t64.5 110.5q-41 16 -64.5 49t-33.5 94q-23 145 -40.5 314.5t-31.5 339.5q-4 63 -7 107t-3 67q0 31 37 31h102q39 0 41 -33q10 -219 22.5 -395.5t26.5 -311.5q4 -49 12.5 -73.5t24.5 -34.5q111 217 208 420.5t179 396.5 q12 31 47 31h117q29 0 29 -23q0 -10 -7.5 -26.5t-17.5 -36.5q-45 -104 -101.5 -222t-116.5 -238t-119.5 -233.5t-112.5 -208.5q-49 -88 -107.5 -187t-108.5 -177q-8 -12 -14 -19.5t-16 -7.5t-23.5 5t-42.5 22l-31 16q-28 17 -28 33zM331 1266q0 12 23 22l368 178 q33 16 44 17q10 0 16 -6.5t16 -28.5l23 -53q16 -39 16 -54q0 -20 -26 -28l-394 -146q-16 -6 -26 -6q-14 0 -25 21l-20 43q-6 14 -10.5 23t-4.5 18z" />
<glyph unicode="&#xfe;" horiz-adv-x="1044" d="M-53 -338q0 8 5 37t13 63l309 1588q4 20 13.5 28t31.5 8h109q37 0 37 -22q0 -10 -5 -35.5t-14 -66.5q0 -2 -7 -35t-16 -81t-20.5 -101.5t-19.5 -96.5q53 37 119.5 55.5t156.5 18.5q137 0 219.5 -62.5t82.5 -207.5q0 -100 -52 -363q-20 -100 -51 -176t-81 -127t-123 -77.5 t-175 -26.5t-162.5 21.5t-99.5 68.5h-2l-78 -398q-6 -37 -45 -36h-108q-37 0 -37 24zM266 266q0 -59 43.5 -95t147.5 -36q61 0 105 15.5t75 49t51.5 87t36.5 131.5q43 199 43 301q0 76 -40 111.5t-136 35.5q-57 0 -101.5 -11t-78 -40t-57 -80t-38.5 -133l-32 -170 q-10 -47 -14.5 -82t-4.5 -84z" />
<glyph unicode="&#xff;" horiz-adv-x="890" d="M78 -299q0 12 11 29.5t40 58.5q31 47 64.5 102.5t64.5 110.5q-41 16 -64.5 49t-33.5 94q-23 145 -40.5 314.5t-31.5 339.5q-4 63 -7 107t-3 67q0 31 37 31h102q39 0 41 -33q10 -219 22.5 -395.5t26.5 -311.5q4 -49 12.5 -73.5t24.5 -34.5q111 217 208 420.5t179 396.5 q12 31 47 31h117q29 0 29 -23q0 -10 -7.5 -26.5t-17.5 -36.5q-45 -104 -101.5 -222t-116.5 -238t-119.5 -233.5t-112.5 -208.5q-49 -88 -107.5 -187t-108.5 -177q-8 -12 -14 -19.5t-16 -7.5t-23.5 5t-42.5 22l-31 16q-28 17 -28 33zM262 1268q0 10 1 25.5t9 49.5 q10 43 41 63.5t76 20.5h16q41 0 64.5 -18t23.5 -55q0 -10 -2 -25.5t-8 -44.5q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 52.5zM596 1268q0 10 1 25.5t9 49.5q10 43 41 63.5t76 20.5h16q41 0 64.5 -18t23.5 -55q0 -10 -2 -25.5t-8 -44.5q-10 -45 -41 -65.5 t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 52.5z" />
<glyph unicode="&#x152;" horiz-adv-x="1744" d="M80 336q0 86 16.5 199.5t44.5 255.5q33 162 82 268t116 169.5t153 89t190 25.5q113 0 196 -30.5t136 -77.5q37 53 98.5 71.5t161.5 18.5h450q29 0 29 -20q0 -8 -3 -23.5t-7 -44.5l-8 -43q-6 -35 -47 -35h-431q-82 0 -102 -78q-18 -72 -35.5 -156.5t-34.5 -158.5h486 q29 0 28 -21q0 -8 -3 -23t-7 -44l-8 -43q-6 -35 -47 -35h-481q-12 -63 -23.5 -122.5t-20 -110t-13.5 -86t-5 -52.5q0 -35 17.5 -49t70.5 -14h451q29 0 29 -21q0 -8 -3.5 -23.5t-7.5 -43.5l-8 -43q-6 -35 -47 -35h-498q-70 0 -117 19.5t-69 66.5q-55 -51 -134 -77.5 t-188 -26.5q-199 0 -303 86t-104 268zM283 365q0 -111 55 -164.5t182 -53.5q92 0 160 33t115 90q4 25 8 54.5t12 66.5q33 174 68 348t73 340q-47 47 -111.5 73t-137.5 26q-76 0 -125.5 -15.5t-83.5 -44.5q-29 -23 -51.5 -53.5t-43 -73.5t-37 -102.5t-32.5 -138.5 q-23 -109 -37 -205t-14 -180z" />
<glyph unicode="&#x153;" horiz-adv-x="1587" d="M55 264q0 53 12.5 140.5t28.5 173.5q25 123 62 207.5t93 137t134 76t184 23.5q111 0 184.5 -28.5t112.5 -82.5q94 111 320 111q164 0 242.5 -70.5t78.5 -199.5q0 -70 -13 -142.5t-30 -154.5q-6 -27 -20 -38t-45 -11h-518q-6 -35 -10.5 -65t-4.5 -62q0 -82 52.5 -111 t156.5 -29q57 0 125 11.5t121 27.5q6 2 13 4t16 2q14 0 20 -24l12 -49q8 -31 9 -45q0 -16 -21 -25q-61 -25 -152.5 -42t-173.5 -17q-125 0 -201.5 35.5t-107.5 93.5q-47 -68 -127.5 -98.5t-195.5 -30.5q-186 0 -271.5 68.5t-85.5 213.5zM246 291q0 -86 42 -121t140 -35 q57 0 100 13.5t74 46t52.5 87t37.5 138.5l19 100q25 137 24 203q0 82 -42 113.5t-140 31.5q-115 0 -174.5 -65.5t-89.5 -218.5q-23 -115 -33 -185.5t-10 -107.5zM909 555h355q27 0 32 25q10 37 15.5 77.5t5.5 73.5q0 74 -37 105.5t-123 31.5q-98 0 -151.5 -49t-79.5 -180z " />
<glyph unicode="&#x178;" horiz-adv-x="974" d="M141 1294q0 31 43 31h107q23 0 33 -5t12 -28q16 -182 47 -345t76 -324h14q111 162 211 330.5t186 338.5q10 23 21.5 28t34.5 5h123q27 0 26 -18q0 -10 -6 -22.5t-10 -22.5q-211 -424 -522 -832l-78 -397q-4 -18 -13.5 -25.5t-33.5 -7.5h-117q-35 0 -35 23q0 10 2 19t6 32 l72 364q-74 190 -120 390t-75 407q-2 10 -3 28.5t-1 30.5zM371 1550q0 10 1 25.5t9 50.5q10 43 41 63.5t76 20.5h16q41 0 64.5 -18.5t23.5 -55.5q0 -10 -2 -25.5t-8 -43.5q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 51.5zM705 1550q0 10 1 25.5t9 50.5 q10 43 40.5 63.5t76.5 20.5h16q41 0 64.5 -18.5t23.5 -55.5q0 -10 -2 -25.5t-8 -43.5q-10 -45 -41 -65.5t-78 -20.5h-16q-41 0 -63.5 17.5t-22.5 51.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="763" d="M174 1210q0 10 8 19.5t21 21.5l231 222q12 10 21.5 14t25.5 4h86q14 0 22.5 -3t18.5 -15l164 -234q16 -23 16 -35t-20 -26l-35 -23q-25 -16 -37 -16q-14 0 -30 18l-148 172h-8l-201 -168q-12 -10 -24.5 -18t-22.5 -8q-12 0 -20.5 5t-20.5 15l-26 25q-20 18 -21 30z" />
<glyph unicode="&#x2dc;" horiz-adv-x="794" d="M170 1239q0 8 10 25q53 90 114.5 128t127.5 38q63 0 123 -48l37 -30q18 -16 33.5 -23.5t37.5 -7.5q33 0 59.5 24.5t59.5 65.5q8 12 23 12q8 0 24 -12l41 -33q14 -12 14 -22q0 -8 -10 -23q-111 -162 -235 -162q-39 0 -71 12.5t-58 35.5l-37 30q-31 29 -68 29 q-57 0 -119 -86q-12 -16 -24 -16t-35 16l-31 22q-10 8 -13 13.5t-3 11.5z" />
<glyph unicode="&#x2000;" horiz-adv-x="898" />
<glyph unicode="&#x2001;" horiz-adv-x="1796" />
<glyph unicode="&#x2002;" horiz-adv-x="898" />
<glyph unicode="&#x2003;" horiz-adv-x="1796" />
<glyph unicode="&#x2004;" horiz-adv-x="598" />
<glyph unicode="&#x2005;" horiz-adv-x="449" />
<glyph unicode="&#x2006;" horiz-adv-x="299" />
<glyph unicode="&#x2007;" horiz-adv-x="299" />
<glyph unicode="&#x2008;" horiz-adv-x="224" />
<glyph unicode="&#x2009;" horiz-adv-x="359" />
<glyph unicode="&#x200a;" horiz-adv-x="99" />
<glyph unicode="&#x2010;" horiz-adv-x="645" d="M33 469q0 8 4 27.5t8 42.5l6 32q4 29 16.5 39.5t47.5 10.5h397q27 0 35 -7.5t8 -19.5q0 -8 -4 -27.5t-8 -42.5l-6 -32q-4 -29 -16.5 -39.5t-47.5 -10.5h-397q-27 0 -35 7.5t-8 19.5z" />
<glyph unicode="&#x2011;" horiz-adv-x="645" d="M33 469q0 8 4 27.5t8 42.5l6 32q4 29 16.5 39.5t47.5 10.5h397q27 0 35 -7.5t8 -19.5q0 -8 -4 -27.5t-8 -42.5l-6 -32q-4 -29 -16.5 -39.5t-47.5 -10.5h-397q-27 0 -35 7.5t-8 19.5z" />
<glyph unicode="&#x2012;" horiz-adv-x="645" d="M33 469q0 8 4 27.5t8 42.5l6 32q4 29 16.5 39.5t47.5 10.5h397q27 0 35 -7.5t8 -19.5q0 -8 -4 -27.5t-8 -42.5l-6 -32q-4 -29 -16.5 -39.5t-47.5 -10.5h-397q-27 0 -35 7.5t-8 19.5z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M-33 469q0 8 4 27.5t9 42.5l6 32q4 29 16 39.5t47 10.5h920q27 0 35 -7.5t8 -19.5q0 -8 -4 -27.5t-9 -42.5l-6 -32q-4 -29 -16 -39.5t-47 -10.5h-920q-27 0 -35 7.5t-8 19.5z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M-33 469q0 8 4 27.5t9 42.5l6 32q4 29 16 39.5t47 10.5h1944q27 0 35 -7.5t8 -19.5q0 -8 -4 -27.5t-9 -42.5l-6 -32q-4 -29 -16 -39.5t-47 -10.5h-1944q-27 0 -35 7.5t-8 19.5z" />
<glyph unicode="&#x2018;" horiz-adv-x="430" d="M163 1004q0 86 49.5 179t170.5 214q14 14 22 14q10 0 25 -10l28 -20q20 -14 21 -27q0 -8 -13 -20q-72 -76 -95 -110t-23 -58q0 -12 4 -18.5t12 -12.5l14 -10q31 -25 31 -70q0 -66 -28.5 -108.5t-100.5 -42.5h-18q-47 0 -73 23.5t-26 76.5z" />
<glyph unicode="&#x2019;" horiz-adv-x="430" d="M135 961q0 8 13 20q72 76 95 110t23 58q0 12 -4 18.5t-12 12.5l-14 10q-31 25 -31 70q0 66 28.5 108.5t100.5 42.5h18q47 0 73 -23.5t26 -76.5q0 -86 -49.5 -179t-170.5 -214q-14 -14 -22 -14q-10 0 -25 10l-28 20q-20 14 -21 27z" />
<glyph unicode="&#x201a;" horiz-adv-x="393" d="M-70 -217q0 8 13 20q72 76 95 110t23 58q0 12 -4 18.5t-12 12.5l-14 10q-31 25 -31 70q0 66 28.5 108.5t100.5 42.5h18q47 0 73 -23.5t26 -76.5q0 -86 -49.5 -179t-170.5 -214q-14 -14 -22 -14q-10 0 -25 10l-28 20q-20 14 -21 27z" />
<glyph unicode="&#x201c;" horiz-adv-x="815" d="M163 1004q0 86 49.5 179t170.5 214q14 14 22 14q10 0 25 -10l28 -20q20 -14 21 -27q0 -8 -13 -20q-72 -76 -95 -110t-23 -58q0 -12 4 -18.5t12 -12.5l14 -10q31 -25 31 -70q0 -66 -28.5 -108.5t-100.5 -42.5h-18q-47 0 -73 23.5t-26 76.5zM548 1004q0 86 49.5 179 t170.5 214q14 14 22 14q10 0 25 -10l28 -20q20 -14 21 -27q0 -8 -13 -20q-72 -76 -95 -110t-23 -58q0 -12 4 -18.5t12 -12.5l14 -10q31 -25 31 -70q0 -66 -28.5 -108.5t-100.5 -42.5h-18q-47 0 -73 23.5t-26 76.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="815" d="M135 961q0 8 13 20q72 76 95 110t23 58q0 12 -4 18.5t-12 12.5l-14 10q-31 25 -31 70q0 66 28.5 108.5t100.5 42.5h18q47 0 73 -23.5t26 -76.5q0 -86 -49.5 -179t-170.5 -214q-14 -14 -22 -14q-10 0 -25 10l-28 20q-20 14 -21 27zM520 961q0 8 13 20q72 76 95 110t23 58 q0 12 -4 18.5t-12 12.5l-14 10q-31 25 -31 70q0 66 28.5 108.5t100.5 42.5h18q47 0 73 -23.5t26 -76.5q0 -86 -49.5 -179t-170.5 -214q-14 -14 -22 -14q-10 0 -25 10l-28 20q-20 14 -21 27z" />
<glyph unicode="&#x201e;" horiz-adv-x="815" d="M-84 -217q0 8 13 20q72 76 95 110t23 58q0 12 -4 18.5t-12 12.5l-14 10q-31 25 -31 70q0 66 28.5 108.5t100.5 42.5h18q47 0 73 -23.5t26 -76.5q0 -86 -49.5 -179t-170.5 -214q-14 -14 -22 -14q-10 0 -25 10l-28 20q-20 14 -21 27zM295 -217q0 8 13 20q72 76 95 110 t23 58q0 12 -4 18.5t-12 12.5l-14 10q-31 25 -31 70q0 66 28.5 108.5t100.5 42.5h18q47 0 73 -23.5t26 -76.5q0 -86 -49.5 -179t-170.5 -214q-14 -14 -22 -14q-10 0 -25 10l-28 20q-20 14 -21 27z" />
<glyph unicode="&#x2022;" horiz-adv-x="692" d="M70 489q0 139 77.5 215t206.5 76q102 0 158.5 -57t56.5 -143q0 -139 -77.5 -215t-206.5 -76q-102 0 -158.5 57t-56.5 143z" />
<glyph unicode="&#x2026;" horiz-adv-x="1247" d="M-25 57q0 14 2.5 30.5t8.5 45.5q25 100 129 100h16q47 0 71.5 -20t24.5 -55q0 -14 -2 -30.5t-8 -45.5q-12 -53 -43 -76.5t-86 -23.5h-16q-47 0 -72 20.5t-25 54.5zM391 57q0 14 2 30.5t8 45.5q25 100 129 100h17q47 0 71.5 -20t24.5 -55q0 -14 -2 -30.5t-8 -45.5 q-12 -53 -43 -76.5t-86 -23.5h-17q-47 0 -71.5 20.5t-24.5 54.5zM807 57q0 14 2 30.5t8 45.5q25 100 129 100h17q47 0 71.5 -20t24.5 -55q0 -14 -2 -30.5t-8 -45.5q-12 -53 -43 -76.5t-86 -23.5h-17q-47 0 -71.5 20.5t-24.5 54.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="359" />
<glyph unicode="&#x2039;" horiz-adv-x="581" d="M-12 446q0 8 2 20.5t6 25.5q4 20 13 31.5t32 27.5l352 252q10 6 17.5 11t15.5 5q16 0 33 -20l39 -47q12 -14 16 -21.5t4 -15.5q0 -10 -10 -21.5t-39 -34.5l-258 -200v-2l186 -209q12 -12 13 -29q0 -16 -19 -31l-47 -41q-16 -12 -33 -12q-18 0 -35 17l-256 239 q-23 20 -27.5 30.5t-4.5 24.5z" />
<glyph unicode="&#x203a;" horiz-adv-x="581" d="M-20 240q0 10 10 21t39 34l258 201v2l-187 209q-12 12 -12 28q0 14 18 31l48 41q14 12 32 12t35 -16l256 -240q23 -20 28 -30.5t5 -24.5q0 -8 -2 -20.5t-6 -24.5q-4 -20 -13.5 -31.5t-31.5 -28.5l-353 -251q-10 -6 -17 -11.5t-15 -5.5q-18 0 -33 21l-39 47q-12 14 -16 21 t-4 16z" />
<glyph unicode="&#x205f;" horiz-adv-x="449" />
<glyph unicode="&#x20ac;" d="M39 465l14 72q4 20 11.5 26t31.5 6h117q4 33 10 67t15 73h-113q-20 0 -27.5 7t-3.5 32l15 71q4 20 11 26.5t32 6.5h112q27 129 69 222t102.5 153.5t143.5 88t193 27.5q90 0 171 -19t142 -52q23 -12 23 -31q0 -8 -4 -17.5t-14 -31.5l-27 -55q-10 -18 -23 -19q-8 0 -17 4 t-19 8q-53 23 -111.5 35.5t-118.5 12.5q-78 0 -129 -18.5t-86 -57.5t-58.5 -101.5t-41.5 -148.5h399q20 0 27.5 -7t3.5 -32l-15 -72q-4 -18 -11 -25t-32 -7h-403l-22 -140h397q20 0 27.5 -7t3.5 -32l-15 -71q-4 -18 -11 -25.5t-32 -7.5h-387q-4 -25 -4 -45.5v-38.5 q0 -43 10.5 -80t36 -62.5t67.5 -41t105 -15.5q66 0 138.5 12.5t121.5 28.5q31 10 43 11q16 0 25 -29l8 -27q8 -29 10 -42t2 -21q0 -20 -28 -31q-74 -29 -171.5 -46t-179.5 -17q-209 0 -296 96t-87 272v34.5t5 41.5h-125q-20 0 -27.5 7t-3.5 32z" />
<glyph unicode="&#x2122;" horiz-adv-x="1470" d="M164 1225q0 8 2 19t6 28l6 33q4 18 12.5 23t22.5 5h442q29 0 23 -28l-12 -68q-6 -29 -35 -29h-148l-90 -475q-6 -29 -39 -28h-86q-33 0 -26 28l90 475h-144q-25 0 -24 17zM641 723q0 10 4 25.5t10 44.5q33 139 64 256.5t65 236.5q10 29 22.5 38t41.5 9h119q33 0 42 -9 t11 -38q8 -100 15 -193.5t16 -185.5h8q43 90 87 188.5t83 190.5q10 29 24.5 38t46.5 9h134q39 0 39 -28q0 -25 -3.5 -53.5t-7.5 -69.5q-10 -96 -24.5 -211t-32.5 -238q-6 -29 -35 -28h-86q-27 0 -27 20q0 10 4.5 30.5t8.5 51.5q16 100 31.5 193.5t27.5 193.5h-6 q-45 -104 -89 -202.5t-91 -184.5q-10 -23 -23.5 -30t-40.5 -7h-90q-29 0 -37 9t-12 34q-10 92 -19.5 187.5t-15.5 193.5h-6q-14 -57 -27.5 -111.5t-25.5 -109.5t-24.5 -113.5t-26.5 -126.5q-6 -29 -41 -28h-86q-27 0 -27 18z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1003" d="M0 0v1004h1004v-1004h-1004z" />
<hkern u1="&#x26;" u2="T" k="150" />
<hkern u1="&#x26;" u2="V" k="115" />
<hkern u1="&#x28;" u2="&#x178;" k="-82" />
<hkern u1="&#x28;" u2="&#x153;" k="20" />
<hkern u1="&#x28;" u2="&#xfc;" k="20" />
<hkern u1="&#x28;" u2="&#xfb;" k="20" />
<hkern u1="&#x28;" u2="&#xfa;" k="20" />
<hkern u1="&#x28;" u2="&#xf9;" k="20" />
<hkern u1="&#x28;" u2="&#xf8;" k="20" />
<hkern u1="&#x28;" u2="&#xf6;" k="20" />
<hkern u1="&#x28;" u2="&#xf5;" k="20" />
<hkern u1="&#x28;" u2="&#xf4;" k="20" />
<hkern u1="&#x28;" u2="&#xf3;" k="20" />
<hkern u1="&#x28;" u2="&#xf2;" k="20" />
<hkern u1="&#x28;" u2="&#xf0;" k="20" />
<hkern u1="&#x28;" u2="&#xeb;" k="20" />
<hkern u1="&#x28;" u2="&#xea;" k="20" />
<hkern u1="&#x28;" u2="&#xe9;" k="20" />
<hkern u1="&#x28;" u2="&#xe8;" k="20" />
<hkern u1="&#x28;" u2="&#xe7;" k="20" />
<hkern u1="&#x28;" u2="&#xdd;" k="-82" />
<hkern u1="&#x28;" u2="u" k="20" />
<hkern u1="&#x28;" u2="o" k="20" />
<hkern u1="&#x28;" u2="j" k="-186" />
<hkern u1="&#x28;" u2="e" k="20" />
<hkern u1="&#x28;" u2="c" k="20" />
<hkern u1="&#x28;" u2="Y" k="-82" />
<hkern u1="&#x28;" u2="W" k="-113" />
<hkern u1="&#x28;" u2="T" k="-74" />
<hkern u1="&#x28;" u2="q" k="20" />
<hkern u1="&#x28;" u2="X" k="-33" />
<hkern u1="&#x28;" u2="V" k="-57" />
<hkern u1="&#x28;" u2="D" k="-59" />
<hkern u1="&#x2a;" u2="&#x178;" k="-49" />
<hkern u1="&#x2a;" u2="&#x152;" k="41" />
<hkern u1="&#x2a;" u2="&#xdd;" k="-49" />
<hkern u1="&#x2a;" u2="&#xd8;" k="41" />
<hkern u1="&#x2a;" u2="&#xd6;" k="41" />
<hkern u1="&#x2a;" u2="&#xd5;" k="41" />
<hkern u1="&#x2a;" u2="&#xd4;" k="41" />
<hkern u1="&#x2a;" u2="&#xd3;" k="41" />
<hkern u1="&#x2a;" u2="&#xd2;" k="41" />
<hkern u1="&#x2a;" u2="&#xc7;" k="41" />
<hkern u1="&#x2a;" u2="&#xc5;" k="102" />
<hkern u1="&#x2a;" u2="&#xc4;" k="102" />
<hkern u1="&#x2a;" u2="&#xc3;" k="102" />
<hkern u1="&#x2a;" u2="&#xc2;" k="102" />
<hkern u1="&#x2a;" u2="&#xc1;" k="102" />
<hkern u1="&#x2a;" u2="&#xc0;" k="102" />
<hkern u1="&#x2a;" u2="Y" k="-49" />
<hkern u1="&#x2a;" u2="W" k="-49" />
<hkern u1="&#x2a;" u2="T" k="-57" />
<hkern u1="&#x2a;" u2="O" k="41" />
<hkern u1="&#x2a;" u2="G" k="41" />
<hkern u1="&#x2a;" u2="C" k="41" />
<hkern u1="&#x2a;" u2="A" k="102" />
<hkern u1="&#x2a;" u2="V" k="-41" />
<hkern u1="&#x2a;" u2="Q" k="41" />
<hkern u1="&#x2c;" u2="x" k="-20" />
<hkern u1="&#x2c;" u2="v" k="82" />
<hkern u1="&#x2c;" u2="X" k="-61" />
<hkern u1="&#x2c;" u2="V" k="137" />
<hkern u1="&#x2c;" u2="L" k="20" />
<hkern u1="&#x2c;" u2="J" k="-125" />
<hkern u1="&#x2e;" u2="x" k="-20" />
<hkern u1="&#x2e;" u2="v" k="82" />
<hkern u1="&#x2e;" u2="X" k="-61" />
<hkern u1="&#x2e;" u2="V" k="137" />
<hkern u1="&#x2e;" u2="L" k="20" />
<hkern u1="&#x2e;" u2="J" k="-125" />
<hkern u1="&#x2f;" u2="&#x178;" k="-61" />
<hkern u1="&#x2f;" u2="&#x153;" k="70" />
<hkern u1="&#x2f;" u2="&#xfc;" k="96" />
<hkern u1="&#x2f;" u2="&#xfb;" k="96" />
<hkern u1="&#x2f;" u2="&#xfa;" k="96" />
<hkern u1="&#x2f;" u2="&#xf9;" k="96" />
<hkern u1="&#x2f;" u2="&#xf8;" k="70" />
<hkern u1="&#x2f;" u2="&#xf6;" k="70" />
<hkern u1="&#x2f;" u2="&#xf5;" k="70" />
<hkern u1="&#x2f;" u2="&#xf4;" k="70" />
<hkern u1="&#x2f;" u2="&#xf3;" k="70" />
<hkern u1="&#x2f;" u2="&#xf2;" k="70" />
<hkern u1="&#x2f;" u2="&#xf1;" k="61" />
<hkern u1="&#x2f;" u2="&#xf0;" k="70" />
<hkern u1="&#x2f;" u2="&#xeb;" k="88" />
<hkern u1="&#x2f;" u2="&#xea;" k="88" />
<hkern u1="&#x2f;" u2="&#xe9;" k="88" />
<hkern u1="&#x2f;" u2="&#xe8;" k="88" />
<hkern u1="&#x2f;" u2="&#xe7;" k="94" />
<hkern u1="&#x2f;" u2="&#xe6;" k="80" />
<hkern u1="&#x2f;" u2="&#xe5;" k="80" />
<hkern u1="&#x2f;" u2="&#xe4;" k="80" />
<hkern u1="&#x2f;" u2="&#xe3;" k="80" />
<hkern u1="&#x2f;" u2="&#xe2;" k="80" />
<hkern u1="&#x2f;" u2="&#xe1;" k="80" />
<hkern u1="&#x2f;" u2="&#xe0;" k="80" />
<hkern u1="&#x2f;" u2="&#xdd;" k="-61" />
<hkern u1="&#x2f;" u2="&#xc7;" k="20" />
<hkern u1="&#x2f;" u2="&#xc5;" k="94" />
<hkern u1="&#x2f;" u2="&#xc4;" k="94" />
<hkern u1="&#x2f;" u2="&#xc3;" k="94" />
<hkern u1="&#x2f;" u2="&#xc2;" k="94" />
<hkern u1="&#x2f;" u2="&#xc1;" k="94" />
<hkern u1="&#x2f;" u2="&#xc0;" k="94" />
<hkern u1="&#x2f;" u2="w" k="8" />
<hkern u1="&#x2f;" u2="u" k="96" />
<hkern u1="&#x2f;" u2="o" k="70" />
<hkern u1="&#x2f;" u2="n" k="61" />
<hkern u1="&#x2f;" u2="e" k="88" />
<hkern u1="&#x2f;" u2="d" k="80" />
<hkern u1="&#x2f;" u2="c" k="94" />
<hkern u1="&#x2f;" u2="a" k="80" />
<hkern u1="&#x2f;" u2="Y" k="-61" />
<hkern u1="&#x2f;" u2="W" k="-98" />
<hkern u1="&#x2f;" u2="T" k="-61" />
<hkern u1="&#x2f;" u2="C" k="20" />
<hkern u1="&#x2f;" u2="A" k="94" />
<hkern u1="&#x2f;" u2="q" k="80" />
<hkern u1="&#x2f;" u2="p" k="53" />
<hkern u1="&#x2f;" u2="m" k="70" />
<hkern u1="&#x2f;" u2="V" k="-82" />
<hkern u1="&#x2f;" u2="E" k="8" />
<hkern u1="&#x3a;" u2="X" k="-61" />
<hkern u1="&#x3a;" u2="J" k="-41" />
<hkern u1="&#x3b;" u2="X" k="-61" />
<hkern u1="&#x3b;" u2="J" k="-41" />
<hkern u1="&#x40;" u2="&#xff;" k="-61" />
<hkern u1="&#x40;" u2="&#xfd;" k="-61" />
<hkern u1="&#x40;" u2="y" k="-61" />
<hkern u1="&#x40;" u2="x" k="-61" />
<hkern u1="A" u2="&#x2122;" k="156" />
<hkern u1="A" u2="x" k="-59" />
<hkern u1="A" u2="v" k="82" />
<hkern u1="A" u2="q" k="41" />
<hkern u1="A" u2="b" k="41" />
<hkern u1="A" u2="\" k="109" />
<hkern u1="A" u2="X" k="-33" />
<hkern u1="A" u2="V" k="111" />
<hkern u1="A" u2="Q" k="47" />
<hkern u1="A" u2="L" k="20" />
<hkern u1="A" u2="J" k="-92" />
<hkern u1="A" u2="&#x2f;" k="-78" />
<hkern u1="A" u2="&#x2a;" k="123" />
<hkern u1="B" u2="&#x178;" k="66" />
<hkern u1="B" u2="&#xdd;" k="66" />
<hkern u1="B" u2="&#xc6;" k="10" />
<hkern u1="B" u2="&#xc5;" k="-31" />
<hkern u1="B" u2="&#xc4;" k="-31" />
<hkern u1="B" u2="&#xc3;" k="-31" />
<hkern u1="B" u2="&#xc2;" k="-31" />
<hkern u1="B" u2="&#xc1;" k="-31" />
<hkern u1="B" u2="&#xc0;" k="-31" />
<hkern u1="B" u2="Y" k="66" />
<hkern u1="B" u2="W" k="16" />
<hkern u1="B" u2="T" k="63" />
<hkern u1="B" u2="A" k="-31" />
<hkern u1="B" u2="V" k="57" />
<hkern u1="B" u2="&#x2a;" k="53" />
<hkern u1="C" u2="v" k="76" />
<hkern u1="C" u2="q" k="41" />
<hkern u1="C" u2="X" k="-41" />
<hkern u1="C" u2="V" k="-33" />
<hkern u1="C" u2="J" k="-57" />
<hkern u1="C" u2="&#x2f;" k="-86" />
<hkern u1="C" u2="&#x2a;" k="41" />
<hkern u1="D" u2="&#x2122;" k="98" />
<hkern u1="D" u2="v" k="-10" />
<hkern u1="D" u2="\" k="59" />
<hkern u1="D" u2="X" k="61" />
<hkern u1="D" u2="V" k="51" />
<hkern u1="D" u2="&#x2a;" k="41" />
<hkern u1="D" u2="&#x29;" k="47" />
<hkern u1="E" u2="q" k="27" />
<hkern u1="E" u2="\" k="-14" />
<hkern u1="E" u2="Q" k="20" />
<hkern u1="E" u2="J" k="-74" />
<hkern u1="E" u2="&#x2f;" k="-76" />
<hkern u1="F" u2="&#x2026;" k="184" />
<hkern u1="F" u2="&#x201e;" k="184" />
<hkern u1="F" u2="&#x201a;" k="184" />
<hkern u1="F" u2="&#x178;" k="-37" />
<hkern u1="F" u2="&#x153;" k="31" />
<hkern u1="F" u2="&#x152;" k="41" />
<hkern u1="F" u2="&#xfc;" k="31" />
<hkern u1="F" u2="&#xfb;" k="31" />
<hkern u1="F" u2="&#xfa;" k="31" />
<hkern u1="F" u2="&#xf9;" k="31" />
<hkern u1="F" u2="&#xf8;" k="31" />
<hkern u1="F" u2="&#xf6;" k="31" />
<hkern u1="F" u2="&#xf5;" k="31" />
<hkern u1="F" u2="&#xf4;" k="31" />
<hkern u1="F" u2="&#xf3;" k="31" />
<hkern u1="F" u2="&#xf2;" k="31" />
<hkern u1="F" u2="&#xf0;" k="31" />
<hkern u1="F" u2="&#xeb;" k="33" />
<hkern u1="F" u2="&#xea;" k="33" />
<hkern u1="F" u2="&#xe9;" k="33" />
<hkern u1="F" u2="&#xe8;" k="33" />
<hkern u1="F" u2="&#xe7;" k="31" />
<hkern u1="F" u2="&#xe6;" k="61" />
<hkern u1="F" u2="&#xe5;" k="61" />
<hkern u1="F" u2="&#xe4;" k="61" />
<hkern u1="F" u2="&#xe3;" k="61" />
<hkern u1="F" u2="&#xe2;" k="61" />
<hkern u1="F" u2="&#xe1;" k="61" />
<hkern u1="F" u2="&#xe0;" k="61" />
<hkern u1="F" u2="&#xdd;" k="-37" />
<hkern u1="F" u2="&#xd8;" k="41" />
<hkern u1="F" u2="&#xd6;" k="41" />
<hkern u1="F" u2="&#xd5;" k="41" />
<hkern u1="F" u2="&#xd4;" k="41" />
<hkern u1="F" u2="&#xd3;" k="41" />
<hkern u1="F" u2="&#xd2;" k="41" />
<hkern u1="F" u2="&#xc7;" k="41" />
<hkern u1="F" u2="&#xc6;" k="174" />
<hkern u1="F" u2="&#xc5;" k="74" />
<hkern u1="F" u2="&#xc4;" k="74" />
<hkern u1="F" u2="&#xc3;" k="74" />
<hkern u1="F" u2="&#xc2;" k="74" />
<hkern u1="F" u2="&#xc1;" k="74" />
<hkern u1="F" u2="&#xc0;" k="74" />
<hkern u1="F" u2="u" k="31" />
<hkern u1="F" u2="o" k="31" />
<hkern u1="F" u2="g" k="31" />
<hkern u1="F" u2="e" k="33" />
<hkern u1="F" u2="d" k="31" />
<hkern u1="F" u2="c" k="31" />
<hkern u1="F" u2="a" k="61" />
<hkern u1="F" u2="Y" k="-37" />
<hkern u1="F" u2="W" k="-41" />
<hkern u1="F" u2="T" k="-27" />
<hkern u1="F" u2="O" k="41" />
<hkern u1="F" u2="G" k="41" />
<hkern u1="F" u2="C" k="41" />
<hkern u1="F" u2="A" k="74" />
<hkern u1="F" u2="&#x2e;" k="184" />
<hkern u1="F" u2="&#x2c;" k="184" />
<hkern u1="F" u2="&#xef;" k="-12" />
<hkern u1="F" u2="&#xee;" k="-25" />
<hkern u1="F" u2="&#xec;" k="-88" />
<hkern u1="F" u2="q" k="31" />
<hkern u1="F" u2="]" k="-25" />
<hkern u1="F" u2="\" k="-74" />
<hkern u1="F" u2="V" k="-41" />
<hkern u1="F" u2="R" k="10" />
<hkern u1="F" u2="Q" k="41" />
<hkern u1="F" u2="J" k="102" />
<hkern u1="F" u2="H" k="10" />
<hkern u1="G" u2="&#x2122;" k="92" />
<hkern u1="G" u2="J" k="-41" />
<hkern u1="G" u2="&#x2a;" k="61" />
<hkern u1="I" u2="&#xc6;" k="16" />
<hkern u1="J" u2="\" k="20" />
<hkern u1="K" u2="&#x7d;" k="-57" />
<hkern u1="K" u2="x" k="-33" />
<hkern u1="K" u2="q" k="37" />
<hkern u1="K" u2="]" k="-49" />
<hkern u1="K" u2="X" k="-55" />
<hkern u1="K" u2="V" k="-41" />
<hkern u1="K" u2="Q" k="35" />
<hkern u1="K" u2="J" k="-51" />
<hkern u1="K" u2="E" k="25" />
<hkern u1="K" u2="&#x2f;" k="-57" />
<hkern u1="K" u2="&#x29;" k="-74" />
<hkern u1="L" u2="&#x2122;" k="238" />
<hkern u1="L" u2="x" k="-41" />
<hkern u1="L" u2="v" k="102" />
<hkern u1="L" u2="q" k="31" />
<hkern u1="L" u2="\" k="170" />
<hkern u1="L" u2="X" k="-45" />
<hkern u1="L" u2="V" k="154" />
<hkern u1="L" u2="Q" k="57" />
<hkern u1="L" u2="J" k="-82" />
<hkern u1="L" u2="E" k="20" />
<hkern u1="L" u2="&#x2a;" k="242" />
<hkern u1="M" u2="&#x178;" k="10" />
<hkern u1="M" u2="&#x153;" k="27" />
<hkern u1="M" u2="&#xf8;" k="27" />
<hkern u1="M" u2="&#xf6;" k="27" />
<hkern u1="M" u2="&#xf5;" k="27" />
<hkern u1="M" u2="&#xf4;" k="27" />
<hkern u1="M" u2="&#xf3;" k="27" />
<hkern u1="M" u2="&#xf2;" k="27" />
<hkern u1="M" u2="&#xf0;" k="27" />
<hkern u1="M" u2="&#xeb;" k="27" />
<hkern u1="M" u2="&#xea;" k="27" />
<hkern u1="M" u2="&#xe9;" k="27" />
<hkern u1="M" u2="&#xe8;" k="27" />
<hkern u1="M" u2="&#xe7;" k="27" />
<hkern u1="M" u2="&#xe6;" k="33" />
<hkern u1="M" u2="&#xe5;" k="33" />
<hkern u1="M" u2="&#xe4;" k="33" />
<hkern u1="M" u2="&#xe3;" k="33" />
<hkern u1="M" u2="&#xe2;" k="33" />
<hkern u1="M" u2="&#xe1;" k="33" />
<hkern u1="M" u2="&#xe0;" k="33" />
<hkern u1="M" u2="&#xdd;" k="10" />
<hkern u1="M" u2="o" k="27" />
<hkern u1="M" u2="g" k="27" />
<hkern u1="M" u2="e" k="27" />
<hkern u1="M" u2="d" k="27" />
<hkern u1="M" u2="c" k="27" />
<hkern u1="M" u2="a" k="33" />
<hkern u1="M" u2="Y" k="10" />
<hkern u1="M" u2="&#x2122;" k="57" />
<hkern u1="M" u2="q" k="27" />
<hkern u1="O" u2="X" k="72" />
<hkern u1="O" u2="V" k="37" />
<hkern u1="O" u2="&#x2a;" k="41" />
<hkern u1="P" u2="&#x2026;" k="164" />
<hkern u1="P" u2="&#x201e;" k="164" />
<hkern u1="P" u2="&#x201a;" k="164" />
<hkern u1="P" u2="&#xff;" k="-20" />
<hkern u1="P" u2="&#xfd;" k="-20" />
<hkern u1="P" u2="&#xc6;" k="213" />
<hkern u1="P" u2="&#xc5;" k="68" />
<hkern u1="P" u2="&#xc4;" k="68" />
<hkern u1="P" u2="&#xc3;" k="68" />
<hkern u1="P" u2="&#xc2;" k="68" />
<hkern u1="P" u2="&#xc1;" k="68" />
<hkern u1="P" u2="&#xc0;" k="68" />
<hkern u1="P" u2="y" k="-20" />
<hkern u1="P" u2="w" k="-20" />
<hkern u1="P" u2="Z" k="33" />
<hkern u1="P" u2="A" k="68" />
<hkern u1="P" u2="&#x2e;" k="164" />
<hkern u1="P" u2="&#x2c;" k="164" />
<hkern u1="P" u2="v" k="-31" />
<hkern u1="P" u2="X" k="33" />
<hkern u1="P" u2="J" k="92" />
<hkern u1="Q" u2="&#x178;" k="45" />
<hkern u1="Q" u2="&#xdd;" k="45" />
<hkern u1="Q" u2="&#xc6;" k="63" />
<hkern u1="Q" u2="j" k="-53" />
<hkern u1="Q" u2="Z" k="20" />
<hkern u1="Q" u2="Y" k="45" />
<hkern u1="Q" u2="T" k="82" />
<hkern u1="Q" u2="X" k="72" />
<hkern u1="Q" u2="V" k="37" />
<hkern u1="Q" u2="&#x2a;" k="41" />
<hkern u1="R" u2="q" k="45" />
<hkern u1="R" u2="X" k="-31" />
<hkern u1="R" u2="Q" k="8" />
<hkern u1="R" u2="J" k="-31" />
<hkern u1="S" u2="&#x2122;" k="74" />
<hkern u1="S" u2="v" k="41" />
<hkern u1="S" u2="X" k="10" />
<hkern u1="S" u2="V" k="45" />
<hkern u1="S" u2="J" k="-51" />
<hkern u1="T" u2="&#x2122;" k="-37" />
<hkern u1="T" u2="&#xef;" k="-80" />
<hkern u1="T" u2="&#xee;" k="-98" />
<hkern u1="T" u2="&#xec;" k="-156" />
<hkern u1="T" u2="&#x7d;" k="-82" />
<hkern u1="T" u2="q" k="94" />
<hkern u1="T" u2="p" k="102" />
<hkern u1="T" u2="m" k="102" />
<hkern u1="T" u2="]" k="-82" />
<hkern u1="T" u2="\" k="-98" />
<hkern u1="T" u2="X" k="-57" />
<hkern u1="T" u2="V" k="-92" />
<hkern u1="T" u2="Q" k="35" />
<hkern u1="T" u2="J" k="80" />
<hkern u1="T" u2="D" k="-20" />
<hkern u1="T" u2="&#x3f;" k="-57" />
<hkern u1="T" u2="&#x2f;" k="82" />
<hkern u1="T" u2="&#x2a;" k="-55" />
<hkern u1="T" u2="&#x29;" k="-74" />
<hkern u1="T" u2="&#x26;" k="10" />
<hkern u1="U" u2="V" k="14" />
<hkern u1="V" u2="&#x2026;" k="125" />
<hkern u1="V" u2="&#x201e;" k="125" />
<hkern u1="V" u2="&#x201d;" k="-63" />
<hkern u1="V" u2="&#x201a;" k="125" />
<hkern u1="V" u2="&#x2019;" k="-63" />
<hkern u1="V" u2="&#x178;" k="-74" />
<hkern u1="V" u2="&#x153;" k="61" />
<hkern u1="V" u2="&#xff;" k="-20" />
<hkern u1="V" u2="&#xfd;" k="-20" />
<hkern u1="V" u2="&#xfc;" k="25" />
<hkern u1="V" u2="&#xfb;" k="25" />
<hkern u1="V" u2="&#xfa;" k="25" />
<hkern u1="V" u2="&#xf9;" k="25" />
<hkern u1="V" u2="&#xf8;" k="61" />
<hkern u1="V" u2="&#xf6;" k="61" />
<hkern u1="V" u2="&#xf5;" k="61" />
<hkern u1="V" u2="&#xf4;" k="61" />
<hkern u1="V" u2="&#xf3;" k="61" />
<hkern u1="V" u2="&#xf2;" k="61" />
<hkern u1="V" u2="&#xf1;" k="47" />
<hkern u1="V" u2="&#xf0;" k="61" />
<hkern u1="V" u2="&#xeb;" k="61" />
<hkern u1="V" u2="&#xea;" k="61" />
<hkern u1="V" u2="&#xe9;" k="61" />
<hkern u1="V" u2="&#xe8;" k="61" />
<hkern u1="V" u2="&#xe7;" k="61" />
<hkern u1="V" u2="&#xe6;" k="61" />
<hkern u1="V" u2="&#xe5;" k="61" />
<hkern u1="V" u2="&#xe4;" k="61" />
<hkern u1="V" u2="&#xe3;" k="61" />
<hkern u1="V" u2="&#xe2;" k="61" />
<hkern u1="V" u2="&#xe1;" k="61" />
<hkern u1="V" u2="&#xe0;" k="61" />
<hkern u1="V" u2="&#xdd;" k="-74" />
<hkern u1="V" u2="&#xc6;" k="109" />
<hkern u1="V" u2="&#xc5;" k="76" />
<hkern u1="V" u2="&#xc4;" k="76" />
<hkern u1="V" u2="&#xc3;" k="76" />
<hkern u1="V" u2="&#xc2;" k="76" />
<hkern u1="V" u2="&#xc1;" k="76" />
<hkern u1="V" u2="&#xc0;" k="76" />
<hkern u1="V" u2="y" k="-20" />
<hkern u1="V" u2="w" k="-20" />
<hkern u1="V" u2="u" k="25" />
<hkern u1="V" u2="t" k="-10" />
<hkern u1="V" u2="s" k="47" />
<hkern u1="V" u2="r" k="47" />
<hkern u1="V" u2="o" k="61" />
<hkern u1="V" u2="n" k="47" />
<hkern u1="V" u2="g" k="72" />
<hkern u1="V" u2="e" k="61" />
<hkern u1="V" u2="d" k="61" />
<hkern u1="V" u2="c" k="61" />
<hkern u1="V" u2="a" k="61" />
<hkern u1="V" u2="Z" k="-6" />
<hkern u1="V" u2="Y" k="-74" />
<hkern u1="V" u2="W" k="-82" />
<hkern u1="V" u2="T" k="-92" />
<hkern u1="V" u2="S" k="-29" />
<hkern u1="V" u2="A" k="76" />
<hkern u1="V" u2="&#x2e;" k="125" />
<hkern u1="V" u2="&#x2c;" k="125" />
<hkern u1="V" u2="&#x2122;" k="-37" />
<hkern u1="V" u2="&#xef;" k="-82" />
<hkern u1="V" u2="&#xec;" k="-168" />
<hkern u1="V" u2="&#x7d;" k="-82" />
<hkern u1="V" u2="v" k="-20" />
<hkern u1="V" u2="q" k="61" />
<hkern u1="V" u2="p" k="47" />
<hkern u1="V" u2="m" k="47" />
<hkern u1="V" u2="]" k="-106" />
<hkern u1="V" u2="\" k="-115" />
<hkern u1="V" u2="X" k="-37" />
<hkern u1="V" u2="V" k="-61" />
<hkern u1="V" u2="J" k="92" />
<hkern u1="V" u2="&#x3f;" k="-41" />
<hkern u1="V" u2="&#x2f;" k="29" />
<hkern u1="V" u2="&#x2a;" k="-55" />
<hkern u1="V" u2="&#x29;" k="-82" />
<hkern u1="W" u2="&#x2122;" k="-55" />
<hkern u1="W" u2="&#xef;" k="-109" />
<hkern u1="W" u2="&#xec;" k="-172" />
<hkern u1="W" u2="&#x7d;" k="-74" />
<hkern u1="W" u2="v" k="-31" />
<hkern u1="W" u2="q" k="47" />
<hkern u1="W" u2="p" k="27" />
<hkern u1="W" u2="m" k="27" />
<hkern u1="W" u2="]" k="-82" />
<hkern u1="W" u2="\" k="-104" />
<hkern u1="W" u2="X" k="-57" />
<hkern u1="W" u2="V" k="-80" />
<hkern u1="W" u2="J" k="74" />
<hkern u1="W" u2="B" k="-14" />
<hkern u1="W" u2="&#x2a;" k="-39" />
<hkern u1="W" u2="&#x29;" k="-90" />
<hkern u1="X" u2="&#x2026;" k="-47" />
<hkern u1="X" u2="&#x201e;" k="-47" />
<hkern u1="X" u2="&#x201a;" k="-47" />
<hkern u1="X" u2="&#x178;" k="-57" />
<hkern u1="X" u2="&#x153;" k="61" />
<hkern u1="X" u2="&#x152;" k="66" />
<hkern u1="X" u2="&#xff;" k="27" />
<hkern u1="X" u2="&#xfd;" k="27" />
<hkern u1="X" u2="&#xf8;" k="61" />
<hkern u1="X" u2="&#xf6;" k="61" />
<hkern u1="X" u2="&#xf5;" k="61" />
<hkern u1="X" u2="&#xf4;" k="61" />
<hkern u1="X" u2="&#xf3;" k="61" />
<hkern u1="X" u2="&#xf2;" k="61" />
<hkern u1="X" u2="&#xf0;" k="61" />
<hkern u1="X" u2="&#xeb;" k="61" />
<hkern u1="X" u2="&#xea;" k="61" />
<hkern u1="X" u2="&#xe9;" k="61" />
<hkern u1="X" u2="&#xe8;" k="61" />
<hkern u1="X" u2="&#xe7;" k="61" />
<hkern u1="X" u2="&#xdd;" k="-57" />
<hkern u1="X" u2="&#xd8;" k="66" />
<hkern u1="X" u2="&#xd6;" k="66" />
<hkern u1="X" u2="&#xd5;" k="66" />
<hkern u1="X" u2="&#xd4;" k="66" />
<hkern u1="X" u2="&#xd3;" k="66" />
<hkern u1="X" u2="&#xd2;" k="66" />
<hkern u1="X" u2="&#xc7;" k="39" />
<hkern u1="X" u2="&#xc6;" k="-72" />
<hkern u1="X" u2="&#xc5;" k="-80" />
<hkern u1="X" u2="&#xc4;" k="-80" />
<hkern u1="X" u2="&#xc3;" k="-80" />
<hkern u1="X" u2="&#xc2;" k="-80" />
<hkern u1="X" u2="&#xc1;" k="-80" />
<hkern u1="X" u2="&#xc0;" k="-80" />
<hkern u1="X" u2="z" k="-37" />
<hkern u1="X" u2="y" k="27" />
<hkern u1="X" u2="w" k="27" />
<hkern u1="X" u2="o" k="61" />
<hkern u1="X" u2="g" k="61" />
<hkern u1="X" u2="e" k="61" />
<hkern u1="X" u2="d" k="61" />
<hkern u1="X" u2="c" k="61" />
<hkern u1="X" u2="Z" k="-49" />
<hkern u1="X" u2="Y" k="-57" />
<hkern u1="X" u2="W" k="-41" />
<hkern u1="X" u2="T" k="-49" />
<hkern u1="X" u2="O" k="66" />
<hkern u1="X" u2="G" k="29" />
<hkern u1="X" u2="C" k="39" />
<hkern u1="X" u2="A" k="-80" />
<hkern u1="X" u2="&#x3b;" k="-41" />
<hkern u1="X" u2="&#x3a;" k="-41" />
<hkern u1="X" u2="&#x2e;" k="-47" />
<hkern u1="X" u2="&#x2c;" k="-47" />
<hkern u1="X" u2="&#xef;" k="-43" />
<hkern u1="X" u2="&#xec;" k="-117" />
<hkern u1="X" u2="&#x7d;" k="-41" />
<hkern u1="X" u2="x" k="-27" />
<hkern u1="X" u2="v" k="31" />
<hkern u1="X" u2="q" k="61" />
<hkern u1="X" u2="]" k="-68" />
<hkern u1="X" u2="X" k="-74" />
<hkern u1="X" u2="V" k="-49" />
<hkern u1="X" u2="Q" k="66" />
<hkern u1="X" u2="J" k="-74" />
<hkern u1="X" u2="E" k="23" />
<hkern u1="X" u2="&#x29;" k="-41" />
<hkern u1="Y" u2="&#x2122;" k="-43" />
<hkern u1="Y" u2="&#xef;" k="-96" />
<hkern u1="Y" u2="&#xee;" k="-96" />
<hkern u1="Y" u2="&#xec;" k="-164" />
<hkern u1="Y" u2="&#x7d;" k="-49" />
<hkern u1="Y" u2="q" k="88" />
<hkern u1="Y" u2="p" k="61" />
<hkern u1="Y" u2="m" k="61" />
<hkern u1="Y" u2="]" k="-74" />
<hkern u1="Y" u2="\" k="-92" />
<hkern u1="Y" u2="X" k="-63" />
<hkern u1="Y" u2="V" k="-82" />
<hkern u1="Y" u2="Q" k="20" />
<hkern u1="Y" u2="J" k="96" />
<hkern u1="Y" u2="&#x3f;" k="-35" />
<hkern u1="Y" u2="&#x2f;" k="82" />
<hkern u1="Y" u2="&#x2a;" k="-33" />
<hkern u1="Y" u2="&#x29;" k="-57" />
<hkern u1="Z" u2="&#x7d;" k="-33" />
<hkern u1="Z" u2="q" k="68" />
<hkern u1="Z" u2="]" k="-41" />
<hkern u1="Z" u2="X" k="-8" />
<hkern u1="Z" u2="V" k="-20" />
<hkern u1="Z" u2="Q" k="29" />
<hkern u1="Z" u2="J" k="-43" />
<hkern u1="Z" u2="E" k="20" />
<hkern u1="Z" u2="&#x29;" k="-25" />
<hkern u1="[" u2="&#x178;" k="-57" />
<hkern u1="[" u2="&#xdd;" k="-57" />
<hkern u1="[" u2="j" k="-170" />
<hkern u1="[" u2="Y" k="-57" />
<hkern u1="[" u2="W" k="-80" />
<hkern u1="[" u2="T" k="-82" />
<hkern u1="[" u2="X" k="-41" />
<hkern u1="[" u2="V" k="-90" />
<hkern u1="\" g2="uniFB02" k="-45" />
<hkern u1="\" g2="uniFB01" k="-45" />
<hkern u1="\" u2="&#x178;" k="82" />
<hkern u1="\" u2="&#xf1;" k="-43" />
<hkern u1="\" u2="&#xdd;" k="82" />
<hkern u1="\" u2="&#xc5;" k="-96" />
<hkern u1="\" u2="&#xc4;" k="-96" />
<hkern u1="\" u2="&#xc3;" k="-96" />
<hkern u1="\" u2="&#xc2;" k="-96" />
<hkern u1="\" u2="&#xc1;" k="-96" />
<hkern u1="\" u2="&#xc0;" k="-96" />
<hkern u1="\" u2="z" k="-78" />
<hkern u1="\" u2="w" k="35" />
<hkern u1="\" u2="n" k="-43" />
<hkern u1="\" u2="j" k="-152" />
<hkern u1="\" u2="f" k="-45" />
<hkern u1="\" u2="Z" k="-57" />
<hkern u1="\" u2="Y" k="82" />
<hkern u1="\" u2="T" k="49" />
<hkern u1="\" u2="A" k="-96" />
<hkern u1="\" u2="x" k="-109" />
<hkern u1="\" u2="v" k="61" />
<hkern u1="\" u2="p" k="-45" />
<hkern u1="\" u2="m" k="-41" />
<hkern u1="\" u2="X" k="-53" />
<hkern u1="\" u2="V" k="125" />
<hkern u1="\" u2="J" k="-31" />
<hkern u1="\" u2="D" k="-20" />
<hkern u1="a" u2="&#x2122;" k="98" />
<hkern u1="a" u2="\" k="80" />
<hkern u1="a" u2="&#x2f;" k="-78" />
<hkern u1="a" u2="&#x2a;" k="92" />
<hkern u1="b" u2="&#x2122;" k="115" />
<hkern u1="b" u2="x" k="29" />
<hkern u1="b" u2="\" k="94" />
<hkern u1="b" u2="&#x2a;" k="102" />
<hkern u1="c" u2="v" k="-33" />
<hkern u1="c" u2="q" k="20" />
<hkern u1="c" u2="\" k="104" />
<hkern u1="c" u2="&#x2f;" k="-88" />
<hkern u1="c" u2="&#x29;" k="-72" />
<hkern u1="d" u2="&#xe6;" k="14" />
<hkern u1="d" u2="&#xe5;" k="14" />
<hkern u1="d" u2="&#xe4;" k="14" />
<hkern u1="d" u2="&#xe3;" k="14" />
<hkern u1="d" u2="&#xe2;" k="14" />
<hkern u1="d" u2="&#xe1;" k="14" />
<hkern u1="d" u2="&#xe0;" k="14" />
<hkern u1="d" u2="a" k="14" />
<hkern u1="d" u2="&#xee;" k="-76" />
<hkern u1="d" u2="&#xec;" k="-141" />
<hkern u1="e" u2="&#x2f;" k="-61" />
<hkern u1="e" u2="&#x2a;" k="102" />
<hkern u1="e" u2="&#x29;" k="-20" />
<hkern u1="f" g2="uniFB02" k="-41" />
<hkern u1="f" g2="uniFB01" k="-41" />
<hkern u1="f" u2="&#x2026;" k="115" />
<hkern u1="f" u2="&#x201e;" k="115" />
<hkern u1="f" u2="&#x201d;" k="-129" />
<hkern u1="f" u2="&#x201a;" k="115" />
<hkern u1="f" u2="&#x2019;" k="-129" />
<hkern u1="f" u2="&#x153;" k="6" />
<hkern u1="f" u2="&#xff;" k="-72" />
<hkern u1="f" u2="&#xfd;" k="-72" />
<hkern u1="f" u2="&#xfc;" k="10" />
<hkern u1="f" u2="&#xfb;" k="10" />
<hkern u1="f" u2="&#xfa;" k="10" />
<hkern u1="f" u2="&#xf9;" k="10" />
<hkern u1="f" u2="&#xf8;" k="6" />
<hkern u1="f" u2="&#xf6;" k="6" />
<hkern u1="f" u2="&#xf5;" k="6" />
<hkern u1="f" u2="&#xf4;" k="6" />
<hkern u1="f" u2="&#xf3;" k="6" />
<hkern u1="f" u2="&#xf2;" k="6" />
<hkern u1="f" u2="&#xf0;" k="6" />
<hkern u1="f" u2="&#xeb;" k="6" />
<hkern u1="f" u2="&#xea;" k="6" />
<hkern u1="f" u2="&#xe9;" k="6" />
<hkern u1="f" u2="&#xe8;" k="6" />
<hkern u1="f" u2="&#xe7;" k="6" />
<hkern u1="f" u2="&#xe6;" k="18" />
<hkern u1="f" u2="&#xe5;" k="18" />
<hkern u1="f" u2="&#xe4;" k="18" />
<hkern u1="f" u2="&#xe3;" k="18" />
<hkern u1="f" u2="&#xe2;" k="18" />
<hkern u1="f" u2="&#xe1;" k="18" />
<hkern u1="f" u2="&#xe0;" k="18" />
<hkern u1="f" u2="z" k="-43" />
<hkern u1="f" u2="y" k="-72" />
<hkern u1="f" u2="w" k="-47" />
<hkern u1="f" u2="u" k="10" />
<hkern u1="f" u2="t" k="-61" />
<hkern u1="f" u2="o" k="6" />
<hkern u1="f" u2="l" k="-37" />
<hkern u1="f" u2="j" k="-25" />
<hkern u1="f" u2="i" k="-25" />
<hkern u1="f" u2="g" k="6" />
<hkern u1="f" u2="f" k="-41" />
<hkern u1="f" u2="e" k="6" />
<hkern u1="f" u2="d" k="6" />
<hkern u1="f" u2="c" k="6" />
<hkern u1="f" u2="a" k="18" />
<hkern u1="f" u2="&#x2e;" k="115" />
<hkern u1="f" u2="&#x2c;" k="115" />
<hkern u1="f" u2="&#x2122;" k="-129" />
<hkern u1="f" u2="&#xef;" k="-176" />
<hkern u1="f" u2="&#xee;" k="-193" />
<hkern u1="f" u2="&#xed;" k="-84" />
<hkern u1="f" u2="&#xec;" k="-276" />
<hkern u1="f" u2="&#x7d;" k="-158" />
<hkern u1="f" u2="x" k="-37" />
<hkern u1="f" u2="v" k="-45" />
<hkern u1="f" u2="q" k="6" />
<hkern u1="f" u2="k" k="-37" />
<hkern u1="f" u2="h" k="-37" />
<hkern u1="f" u2="b" k="-37" />
<hkern u1="f" u2="]" k="-186" />
<hkern u1="f" u2="\" k="-162" />
<hkern u1="f" u2="&#x3f;" k="-113" />
<hkern u1="f" u2="&#x2a;" k="-121" />
<hkern u1="f" u2="&#x29;" k="-113" />
<hkern u1="f" u2="&#x21;" k="-61" />
<hkern u1="g" u2="&#x7d;" k="-47" />
<hkern u1="g" u2="]" k="-55" />
<hkern u1="g" u2="\" k="61" />
<hkern u1="g" u2="&#x29;" k="-39" />
<hkern u1="h" u2="&#x2122;" k="115" />
<hkern u1="h" u2="v" k="37" />
<hkern u1="i" u2="&#xee;" k="-51" />
<hkern u1="i" u2="&#xec;" k="-135" />
<hkern u1="i" u2="v" k="-20" />
<hkern u1="j" u2="&#xee;" k="-61" />
<hkern u1="j" u2="&#xec;" k="-143" />
<hkern u1="j" u2="]" k="-82" />
<hkern u1="k" u2="x" k="-55" />
<hkern u1="k" u2="v" k="-59" />
<hkern u1="k" u2="q" k="10" />
<hkern u1="l" u2="&#x2122;" k="33" />
<hkern u1="l" u2="&#xec;" k="-59" />
<hkern u1="l" u2="x" k="-41" />
<hkern u1="l" u2="v" k="16" />
<hkern u1="l" u2="&#x2f;" k="-106" />
<hkern u1="m" u2="&#x2122;" k="98" />
<hkern u1="m" u2="\" k="88" />
<hkern u1="m" u2="&#x2f;" k="-53" />
<hkern u1="m" u2="&#x2a;" k="82" />
<hkern u1="n" u2="&#x2122;" k="98" />
<hkern u1="n" u2="\" k="70" />
<hkern u1="n" u2="&#x2f;" k="-27" />
<hkern u1="n" u2="&#x2a;" k="82" />
<hkern u1="o" u2="&#x2122;" k="82" />
<hkern u1="o" u2="x" k="37" />
<hkern u1="o" u2="\" k="78" />
<hkern u1="o" u2="&#x2f;" k="-53" />
<hkern u1="o" u2="&#x2a;" k="106" />
<hkern u1="o" u2="&#x29;" k="-68" />
<hkern u1="p" u2="&#x2122;" k="66" />
<hkern u1="p" u2="x" k="29" />
<hkern u1="p" u2="\" k="104" />
<hkern u1="p" u2="&#x2f;" k="-61" />
<hkern u1="q" u2="&#xff;" k="-49" />
<hkern u1="q" u2="&#xfd;" k="-49" />
<hkern u1="q" u2="y" k="-49" />
<hkern u1="q" u2="t" k="-20" />
<hkern u1="q" u2="j" k="-100" />
<hkern u1="q" u2="&#x2f;" k="-61" />
<hkern u1="q" u2="&#x29;" k="-68" />
<hkern u1="r" u2="x" k="-61" />
<hkern u1="r" u2="v" k="-51" />
<hkern u1="r" u2="q" k="10" />
<hkern u1="s" u2="&#x2a;" k="86" />
<hkern u1="t" u2="x" k="-37" />
<hkern u1="t" u2="v" k="-23" />
<hkern u1="t" u2="&#x2f;" k="-131" />
<hkern u1="u" u2="&#x2f;" k="-43" />
<hkern u1="v" g2="uniFB02" k="-72" />
<hkern u1="v" g2="uniFB01" k="-72" />
<hkern u1="v" u2="&#x2026;" k="82" />
<hkern u1="v" u2="&#x201e;" k="82" />
<hkern u1="v" u2="&#x201d;" k="-82" />
<hkern u1="v" u2="&#x201a;" k="82" />
<hkern u1="v" u2="&#x2019;" k="-82" />
<hkern u1="v" u2="&#xff;" k="-72" />
<hkern u1="v" u2="&#xfd;" k="-72" />
<hkern u1="v" u2="z" k="-10" />
<hkern u1="v" u2="y" k="-72" />
<hkern u1="v" u2="w" k="-61" />
<hkern u1="v" u2="t" k="-66" />
<hkern u1="v" u2="f" k="-72" />
<hkern u1="v" u2="&#x2e;" k="82" />
<hkern u1="v" u2="&#x2c;" k="82" />
<hkern u1="v" u2="x" k="-41" />
<hkern u1="v" u2="v" k="-61" />
<hkern u1="v" u2="\" k="-53" />
<hkern u1="v" u2="&#x2f;" k="27" />
<hkern u1="w" u2="x" k="-41" />
<hkern u1="w" u2="v" k="-61" />
<hkern u1="x" g2="uniFB02" k="-37" />
<hkern u1="x" g2="uniFB01" k="-37" />
<hkern u1="x" u2="&#x2026;" k="-33" />
<hkern u1="x" u2="&#x201e;" k="-33" />
<hkern u1="x" u2="&#x201d;" k="-57" />
<hkern u1="x" u2="&#x201a;" k="-33" />
<hkern u1="x" u2="&#x2019;" k="-57" />
<hkern u1="x" u2="&#x153;" k="29" />
<hkern u1="x" u2="&#xff;" k="-61" />
<hkern u1="x" u2="&#xfd;" k="-61" />
<hkern u1="x" u2="&#xf8;" k="29" />
<hkern u1="x" u2="&#xf6;" k="29" />
<hkern u1="x" u2="&#xf5;" k="29" />
<hkern u1="x" u2="&#xf4;" k="29" />
<hkern u1="x" u2="&#xf3;" k="29" />
<hkern u1="x" u2="&#xf2;" k="29" />
<hkern u1="x" u2="&#xf0;" k="29" />
<hkern u1="x" u2="&#xeb;" k="25" />
<hkern u1="x" u2="&#xea;" k="25" />
<hkern u1="x" u2="&#xe9;" k="25" />
<hkern u1="x" u2="&#xe8;" k="25" />
<hkern u1="x" u2="&#xe7;" k="29" />
<hkern u1="x" u2="z" k="-35" />
<hkern u1="x" u2="y" k="-61" />
<hkern u1="x" u2="w" k="-41" />
<hkern u1="x" u2="t" k="-25" />
<hkern u1="x" u2="o" k="29" />
<hkern u1="x" u2="g" k="25" />
<hkern u1="x" u2="f" k="-37" />
<hkern u1="x" u2="e" k="25" />
<hkern u1="x" u2="d" k="29" />
<hkern u1="x" u2="c" k="29" />
<hkern u1="x" u2="&#x2e;" k="-33" />
<hkern u1="x" u2="&#x2c;" k="-33" />
<hkern u1="x" u2="x" k="-41" />
<hkern u1="x" u2="v" k="-41" />
<hkern u1="x" u2="q" k="29" />
<hkern u1="x" u2="&#x2f;" k="-131" />
<hkern u1="x" u2="&#x29;" k="-43" />
<hkern u1="y" u2="x" k="-51" />
<hkern u1="y" u2="v" k="-61" />
<hkern u1="y" u2="q" k="10" />
<hkern u1="z" u2="x" k="-39" />
<hkern u1="z" u2="v" k="-31" />
<hkern u1="z" u2="]" k="-78" />
<hkern u1="z" u2="&#x2f;" k="-96" />
<hkern u1="&#x7b;" u2="&#x178;" k="-74" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-74" />
<hkern u1="&#x7b;" u2="j" k="-203" />
<hkern u1="&#x7b;" u2="Y" k="-74" />
<hkern u1="&#x7b;" u2="W" k="-66" />
<hkern u1="&#x7b;" u2="T" k="-82" />
<hkern u1="&#x7b;" u2="X" k="-41" />
<hkern u1="&#x7b;" u2="V" k="-90" />
<hkern u1="&#xa1;" u2="j" k="-154" />
<hkern u1="&#xbf;" u2="j" k="-246" />
<hkern u1="&#xc0;" u2="&#x2122;" k="156" />
<hkern u1="&#xc0;" u2="x" k="-59" />
<hkern u1="&#xc0;" u2="v" k="82" />
<hkern u1="&#xc0;" u2="q" k="41" />
<hkern u1="&#xc0;" u2="b" k="41" />
<hkern u1="&#xc0;" u2="\" k="109" />
<hkern u1="&#xc0;" u2="X" k="-33" />
<hkern u1="&#xc0;" u2="V" k="111" />
<hkern u1="&#xc0;" u2="Q" k="47" />
<hkern u1="&#xc0;" u2="L" k="20" />
<hkern u1="&#xc0;" u2="J" k="-92" />
<hkern u1="&#xc0;" u2="&#x2f;" k="-78" />
<hkern u1="&#xc0;" u2="&#x2a;" k="123" />
<hkern u1="&#xc1;" u2="&#x2122;" k="156" />
<hkern u1="&#xc1;" u2="x" k="-59" />
<hkern u1="&#xc1;" u2="v" k="82" />
<hkern u1="&#xc1;" u2="q" k="41" />
<hkern u1="&#xc1;" u2="b" k="41" />
<hkern u1="&#xc1;" u2="\" k="109" />
<hkern u1="&#xc1;" u2="X" k="-33" />
<hkern u1="&#xc1;" u2="V" k="111" />
<hkern u1="&#xc1;" u2="Q" k="47" />
<hkern u1="&#xc1;" u2="L" k="20" />
<hkern u1="&#xc1;" u2="J" k="-92" />
<hkern u1="&#xc1;" u2="&#x2f;" k="-78" />
<hkern u1="&#xc1;" u2="&#x2a;" k="123" />
<hkern u1="&#xc2;" u2="&#x2122;" k="156" />
<hkern u1="&#xc2;" u2="x" k="-59" />
<hkern u1="&#xc2;" u2="v" k="82" />
<hkern u1="&#xc2;" u2="q" k="41" />
<hkern u1="&#xc2;" u2="b" k="41" />
<hkern u1="&#xc2;" u2="\" k="109" />
<hkern u1="&#xc2;" u2="X" k="-33" />
<hkern u1="&#xc2;" u2="V" k="111" />
<hkern u1="&#xc2;" u2="Q" k="47" />
<hkern u1="&#xc2;" u2="L" k="20" />
<hkern u1="&#xc2;" u2="J" k="-92" />
<hkern u1="&#xc2;" u2="&#x2f;" k="-78" />
<hkern u1="&#xc2;" u2="&#x2a;" k="123" />
<hkern u1="&#xc3;" u2="&#x2122;" k="156" />
<hkern u1="&#xc3;" u2="x" k="-59" />
<hkern u1="&#xc3;" u2="v" k="82" />
<hkern u1="&#xc3;" u2="q" k="41" />
<hkern u1="&#xc3;" u2="b" k="41" />
<hkern u1="&#xc3;" u2="\" k="109" />
<hkern u1="&#xc3;" u2="X" k="-33" />
<hkern u1="&#xc3;" u2="V" k="111" />
<hkern u1="&#xc3;" u2="Q" k="47" />
<hkern u1="&#xc3;" u2="L" k="20" />
<hkern u1="&#xc3;" u2="J" k="-92" />
<hkern u1="&#xc3;" u2="&#x2f;" k="-78" />
<hkern u1="&#xc3;" u2="&#x2a;" k="123" />
<hkern u1="&#xc4;" u2="&#x2122;" k="156" />
<hkern u1="&#xc4;" u2="x" k="-59" />
<hkern u1="&#xc4;" u2="v" k="82" />
<hkern u1="&#xc4;" u2="q" k="41" />
<hkern u1="&#xc4;" u2="b" k="41" />
<hkern u1="&#xc4;" u2="\" k="109" />
<hkern u1="&#xc4;" u2="X" k="-33" />
<hkern u1="&#xc4;" u2="V" k="111" />
<hkern u1="&#xc4;" u2="Q" k="47" />
<hkern u1="&#xc4;" u2="L" k="20" />
<hkern u1="&#xc4;" u2="J" k="-92" />
<hkern u1="&#xc4;" u2="&#x2f;" k="-78" />
<hkern u1="&#xc4;" u2="&#x2a;" k="123" />
<hkern u1="&#xc5;" u2="&#x2122;" k="156" />
<hkern u1="&#xc5;" u2="x" k="-59" />
<hkern u1="&#xc5;" u2="v" k="82" />
<hkern u1="&#xc5;" u2="q" k="41" />
<hkern u1="&#xc5;" u2="b" k="41" />
<hkern u1="&#xc5;" u2="\" k="109" />
<hkern u1="&#xc5;" u2="X" k="-33" />
<hkern u1="&#xc5;" u2="V" k="111" />
<hkern u1="&#xc5;" u2="Q" k="47" />
<hkern u1="&#xc5;" u2="L" k="20" />
<hkern u1="&#xc5;" u2="J" k="-92" />
<hkern u1="&#xc5;" u2="&#x2f;" k="-78" />
<hkern u1="&#xc5;" u2="&#x2a;" k="123" />
<hkern u1="&#xc6;" u2="q" k="27" />
<hkern u1="&#xc6;" u2="\" k="-14" />
<hkern u1="&#xc6;" u2="Q" k="20" />
<hkern u1="&#xc6;" u2="J" k="-74" />
<hkern u1="&#xc6;" u2="&#x2f;" k="-76" />
<hkern u1="&#xc7;" u2="v" k="76" />
<hkern u1="&#xc7;" u2="q" k="41" />
<hkern u1="&#xc7;" u2="X" k="-41" />
<hkern u1="&#xc7;" u2="V" k="-33" />
<hkern u1="&#xc7;" u2="J" k="-57" />
<hkern u1="&#xc7;" u2="&#x2f;" k="-86" />
<hkern u1="&#xc7;" u2="&#x2a;" k="41" />
<hkern u1="&#xc8;" u2="q" k="27" />
<hkern u1="&#xc8;" u2="\" k="-14" />
<hkern u1="&#xc8;" u2="Q" k="20" />
<hkern u1="&#xc8;" u2="J" k="-74" />
<hkern u1="&#xc8;" u2="&#x2f;" k="-76" />
<hkern u1="&#xc9;" u2="q" k="27" />
<hkern u1="&#xc9;" u2="\" k="-14" />
<hkern u1="&#xc9;" u2="Q" k="20" />
<hkern u1="&#xc9;" u2="J" k="-74" />
<hkern u1="&#xc9;" u2="&#x2f;" k="-76" />
<hkern u1="&#xca;" u2="q" k="27" />
<hkern u1="&#xca;" u2="\" k="-14" />
<hkern u1="&#xca;" u2="Q" k="20" />
<hkern u1="&#xca;" u2="J" k="-74" />
<hkern u1="&#xca;" u2="&#x2f;" k="-76" />
<hkern u1="&#xcb;" u2="q" k="27" />
<hkern u1="&#xcb;" u2="\" k="-14" />
<hkern u1="&#xcb;" u2="Q" k="20" />
<hkern u1="&#xcb;" u2="J" k="-74" />
<hkern u1="&#xcb;" u2="&#x2f;" k="-76" />
<hkern u1="&#xd0;" u2="&#x2122;" k="98" />
<hkern u1="&#xd0;" u2="v" k="-10" />
<hkern u1="&#xd0;" u2="\" k="59" />
<hkern u1="&#xd0;" u2="X" k="61" />
<hkern u1="&#xd0;" u2="V" k="51" />
<hkern u1="&#xd0;" u2="&#x2a;" k="41" />
<hkern u1="&#xd0;" u2="&#x29;" k="47" />
<hkern u1="&#xd2;" u2="X" k="72" />
<hkern u1="&#xd2;" u2="V" k="37" />
<hkern u1="&#xd2;" u2="&#x2a;" k="41" />
<hkern u1="&#xd3;" u2="X" k="72" />
<hkern u1="&#xd3;" u2="V" k="37" />
<hkern u1="&#xd3;" u2="&#x2a;" k="41" />
<hkern u1="&#xd4;" u2="X" k="72" />
<hkern u1="&#xd4;" u2="V" k="37" />
<hkern u1="&#xd4;" u2="&#x2a;" k="41" />
<hkern u1="&#xd5;" u2="X" k="72" />
<hkern u1="&#xd5;" u2="V" k="37" />
<hkern u1="&#xd5;" u2="&#x2a;" k="41" />
<hkern u1="&#xd6;" u2="X" k="72" />
<hkern u1="&#xd6;" u2="V" k="37" />
<hkern u1="&#xd6;" u2="&#x2a;" k="41" />
<hkern u1="&#xd8;" u2="X" k="72" />
<hkern u1="&#xd8;" u2="V" k="37" />
<hkern u1="&#xd8;" u2="&#x2a;" k="41" />
<hkern u1="&#xd9;" u2="V" k="14" />
<hkern u1="&#xda;" u2="V" k="14" />
<hkern u1="&#xdb;" u2="V" k="14" />
<hkern u1="&#xdc;" u2="V" k="14" />
<hkern u1="&#xdd;" u2="&#x2122;" k="-43" />
<hkern u1="&#xdd;" u2="&#xef;" k="-96" />
<hkern u1="&#xdd;" u2="&#xee;" k="-96" />
<hkern u1="&#xdd;" u2="&#xec;" k="-164" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-49" />
<hkern u1="&#xdd;" u2="q" k="88" />
<hkern u1="&#xdd;" u2="p" k="61" />
<hkern u1="&#xdd;" u2="m" k="61" />
<hkern u1="&#xdd;" u2="]" k="-74" />
<hkern u1="&#xdd;" u2="\" k="-92" />
<hkern u1="&#xdd;" u2="X" k="-63" />
<hkern u1="&#xdd;" u2="V" k="-82" />
<hkern u1="&#xdd;" u2="Q" k="20" />
<hkern u1="&#xdd;" u2="J" k="96" />
<hkern u1="&#xdd;" u2="&#x3f;" k="-35" />
<hkern u1="&#xdd;" u2="&#x2f;" k="82" />
<hkern u1="&#xdd;" u2="&#x2a;" k="-33" />
<hkern u1="&#xdd;" u2="&#x29;" k="-57" />
<hkern u1="&#xde;" u2="T" k="123" />
<hkern u1="&#xe0;" u2="&#x2122;" k="98" />
<hkern u1="&#xe0;" u2="\" k="80" />
<hkern u1="&#xe0;" u2="&#x2f;" k="-78" />
<hkern u1="&#xe0;" u2="&#x2a;" k="92" />
<hkern u1="&#xe1;" u2="&#x2122;" k="98" />
<hkern u1="&#xe1;" u2="\" k="80" />
<hkern u1="&#xe1;" u2="&#x2f;" k="-78" />
<hkern u1="&#xe1;" u2="&#x2a;" k="92" />
<hkern u1="&#xe2;" u2="&#x2122;" k="98" />
<hkern u1="&#xe2;" u2="\" k="80" />
<hkern u1="&#xe2;" u2="&#x2f;" k="-78" />
<hkern u1="&#xe2;" u2="&#x2a;" k="92" />
<hkern u1="&#xe3;" u2="&#x2122;" k="98" />
<hkern u1="&#xe3;" u2="\" k="80" />
<hkern u1="&#xe3;" u2="&#x2f;" k="-78" />
<hkern u1="&#xe3;" u2="&#x2a;" k="92" />
<hkern u1="&#xe4;" u2="&#x2122;" k="98" />
<hkern u1="&#xe4;" u2="\" k="80" />
<hkern u1="&#xe4;" u2="&#x2f;" k="-78" />
<hkern u1="&#xe4;" u2="&#x2a;" k="92" />
<hkern u1="&#xe5;" u2="&#x2122;" k="98" />
<hkern u1="&#xe5;" u2="\" k="80" />
<hkern u1="&#xe5;" u2="&#x2f;" k="-78" />
<hkern u1="&#xe5;" u2="&#x2a;" k="92" />
<hkern u1="&#xe7;" u2="v" k="-33" />
<hkern u1="&#xe7;" u2="q" k="20" />
<hkern u1="&#xe7;" u2="\" k="104" />
<hkern u1="&#xe7;" u2="&#x2f;" k="-88" />
<hkern u1="&#xe7;" u2="&#x29;" k="-72" />
<hkern u1="&#xe8;" u2="&#x2f;" k="-61" />
<hkern u1="&#xe8;" u2="&#x2a;" k="102" />
<hkern u1="&#xe8;" u2="&#x29;" k="-20" />
<hkern u1="&#xe9;" u2="&#x2f;" k="-61" />
<hkern u1="&#xe9;" u2="&#x2a;" k="102" />
<hkern u1="&#xe9;" u2="&#x29;" k="-20" />
<hkern u1="&#xea;" u2="&#x2f;" k="-61" />
<hkern u1="&#xea;" u2="&#x2a;" k="102" />
<hkern u1="&#xea;" u2="&#x29;" k="-20" />
<hkern u1="&#xeb;" u2="&#x2f;" k="-61" />
<hkern u1="&#xeb;" u2="&#x2a;" k="102" />
<hkern u1="&#xeb;" u2="&#x29;" k="-20" />
<hkern u1="&#xec;" u2="&#xee;" k="-51" />
<hkern u1="&#xec;" u2="&#xec;" k="-135" />
<hkern u1="&#xec;" u2="v" k="-20" />
<hkern u1="&#xed;" u2="l" k="-76" />
<hkern u1="&#xed;" u2="&#xee;" k="-51" />
<hkern u1="&#xed;" u2="&#xec;" k="-135" />
<hkern u1="&#xed;" u2="v" k="-20" />
<hkern u1="&#xed;" u2="h" k="-76" />
<hkern u1="&#xed;" u2="b" k="-76" />
<hkern u1="&#xee;" u2="&#xef;" k="-51" />
<hkern u1="&#xee;" u2="&#xed;" k="-51" />
<hkern u1="&#xee;" u2="l" k="-61" />
<hkern u1="&#xee;" u2="j" k="-51" />
<hkern u1="&#xee;" u2="i" k="-51" />
<hkern u1="&#xee;" u2="&#xee;" k="-51" />
<hkern u1="&#xee;" u2="&#xec;" k="-135" />
<hkern u1="&#xee;" u2="v" k="-20" />
<hkern u1="&#xee;" u2="k" k="-61" />
<hkern u1="&#xee;" u2="h" k="-61" />
<hkern u1="&#xee;" u2="b" k="-61" />
<hkern u1="&#xef;" u2="l" k="-61" />
<hkern u1="&#xef;" u2="&#xee;" k="-51" />
<hkern u1="&#xef;" u2="&#xec;" k="-135" />
<hkern u1="&#xef;" u2="v" k="-20" />
<hkern u1="&#xef;" u2="k" k="-61" />
<hkern u1="&#xef;" u2="h" k="-61" />
<hkern u1="&#xef;" u2="b" k="-61" />
<hkern u1="&#xf1;" u2="&#x2122;" k="98" />
<hkern u1="&#xf1;" u2="\" k="70" />
<hkern u1="&#xf1;" u2="&#x2f;" k="-27" />
<hkern u1="&#xf1;" u2="&#x2a;" k="82" />
<hkern u1="&#xf2;" u2="&#x2122;" k="82" />
<hkern u1="&#xf2;" u2="x" k="37" />
<hkern u1="&#xf2;" u2="\" k="78" />
<hkern u1="&#xf2;" u2="&#x2f;" k="-53" />
<hkern u1="&#xf2;" u2="&#x2a;" k="106" />
<hkern u1="&#xf2;" u2="&#x29;" k="-68" />
<hkern u1="&#xf3;" u2="&#x2122;" k="82" />
<hkern u1="&#xf3;" u2="x" k="37" />
<hkern u1="&#xf3;" u2="\" k="78" />
<hkern u1="&#xf3;" u2="&#x2f;" k="-53" />
<hkern u1="&#xf3;" u2="&#x2a;" k="106" />
<hkern u1="&#xf3;" u2="&#x29;" k="-68" />
<hkern u1="&#xf4;" u2="&#x2122;" k="82" />
<hkern u1="&#xf4;" u2="x" k="37" />
<hkern u1="&#xf4;" u2="\" k="78" />
<hkern u1="&#xf4;" u2="&#x2f;" k="-53" />
<hkern u1="&#xf4;" u2="&#x2a;" k="106" />
<hkern u1="&#xf4;" u2="&#x29;" k="-68" />
<hkern u1="&#xf5;" u2="&#x2122;" k="82" />
<hkern u1="&#xf5;" u2="x" k="37" />
<hkern u1="&#xf5;" u2="\" k="78" />
<hkern u1="&#xf5;" u2="&#x2f;" k="-53" />
<hkern u1="&#xf5;" u2="&#x2a;" k="106" />
<hkern u1="&#xf5;" u2="&#x29;" k="-68" />
<hkern u1="&#xf6;" u2="&#x2122;" k="82" />
<hkern u1="&#xf6;" u2="x" k="37" />
<hkern u1="&#xf6;" u2="\" k="78" />
<hkern u1="&#xf6;" u2="&#x2f;" k="-53" />
<hkern u1="&#xf6;" u2="&#x2a;" k="106" />
<hkern u1="&#xf6;" u2="&#x29;" k="-68" />
<hkern u1="&#xf8;" u2="&#x2122;" k="82" />
<hkern u1="&#xf8;" u2="x" k="37" />
<hkern u1="&#xf8;" u2="\" k="78" />
<hkern u1="&#xf8;" u2="&#x2f;" k="-53" />
<hkern u1="&#xf8;" u2="&#x2a;" k="106" />
<hkern u1="&#xf8;" u2="&#x29;" k="-68" />
<hkern u1="&#xf9;" u2="&#x2f;" k="-43" />
<hkern u1="&#xfa;" u2="&#x2f;" k="-43" />
<hkern u1="&#xfb;" u2="&#x2f;" k="-43" />
<hkern u1="&#xfc;" u2="&#x2f;" k="-43" />
<hkern u1="&#xfd;" u2="x" k="-51" />
<hkern u1="&#xfd;" u2="v" k="-61" />
<hkern u1="&#xfd;" u2="q" k="10" />
<hkern u1="&#xff;" u2="x" k="-51" />
<hkern u1="&#xff;" u2="v" k="-61" />
<hkern u1="&#xff;" u2="q" k="10" />
<hkern u1="&#x152;" u2="q" k="27" />
<hkern u1="&#x152;" u2="\" k="-14" />
<hkern u1="&#x152;" u2="Q" k="20" />
<hkern u1="&#x152;" u2="J" k="-74" />
<hkern u1="&#x152;" u2="&#x2f;" k="-76" />
<hkern u1="&#x178;" u2="&#x2122;" k="-43" />
<hkern u1="&#x178;" u2="&#xef;" k="-96" />
<hkern u1="&#x178;" u2="&#xee;" k="-96" />
<hkern u1="&#x178;" u2="&#xec;" k="-164" />
<hkern u1="&#x178;" u2="&#x7d;" k="-49" />
<hkern u1="&#x178;" u2="q" k="88" />
<hkern u1="&#x178;" u2="p" k="61" />
<hkern u1="&#x178;" u2="m" k="61" />
<hkern u1="&#x178;" u2="]" k="-74" />
<hkern u1="&#x178;" u2="\" k="-92" />
<hkern u1="&#x178;" u2="X" k="-63" />
<hkern u1="&#x178;" u2="V" k="-82" />
<hkern u1="&#x178;" u2="Q" k="20" />
<hkern u1="&#x178;" u2="J" k="96" />
<hkern u1="&#x178;" u2="&#x3f;" k="-35" />
<hkern u1="&#x178;" u2="&#x2f;" k="82" />
<hkern u1="&#x178;" u2="&#x2a;" k="-33" />
<hkern u1="&#x178;" u2="&#x29;" k="-57" />
<hkern u1="&#x2018;" u2="x" k="-49" />
<hkern u1="&#x2018;" u2="v" k="-41" />
<hkern u1="&#x2018;" u2="V" k="-55" />
<hkern u1="&#x201a;" u2="x" k="-20" />
<hkern u1="&#x201a;" u2="v" k="82" />
<hkern u1="&#x201a;" u2="X" k="-61" />
<hkern u1="&#x201a;" u2="V" k="137" />
<hkern u1="&#x201a;" u2="L" k="20" />
<hkern u1="&#x201a;" u2="J" k="-125" />
<hkern u1="&#x201c;" u2="x" k="-49" />
<hkern u1="&#x201c;" u2="v" k="-41" />
<hkern u1="&#x201c;" u2="V" k="-55" />
<hkern u1="&#x201e;" u2="x" k="-20" />
<hkern u1="&#x201e;" u2="v" k="82" />
<hkern u1="&#x201e;" u2="X" k="-61" />
<hkern u1="&#x201e;" u2="V" k="137" />
<hkern u1="&#x201e;" u2="L" k="20" />
<hkern u1="&#x201e;" u2="J" k="-125" />
<hkern u1="&#x2026;" u2="x" k="-20" />
<hkern u1="&#x2026;" u2="v" k="82" />
<hkern u1="&#x2026;" u2="X" k="-61" />
<hkern u1="&#x2026;" u2="V" k="137" />
<hkern u1="&#x2026;" u2="L" k="20" />
<hkern u1="&#x2026;" u2="J" k="-125" />
<hkern g1="uniFB01" u2="&#xee;" k="-51" />
<hkern g1="uniFB01" u2="&#xec;" k="-135" />
<hkern g1="uniFB01" u2="v" k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="119" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="70" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="z" 	k="-70" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="S" 	k="-12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="106" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="AE" 	k="-102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Z" 	k="-51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="47" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="G" 	k="51" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,ccedilla" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="d" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="g" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="l" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="72" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="70" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,Ccedilla" 	k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="45" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-53" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="-6" />
<hkern g1="C,Ccedilla" 	g2="W" 	k="-31" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="-10" />
<hkern g1="C,Ccedilla" 	g2="AE" 	k="-41" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="-47" />
<hkern g1="C,Ccedilla" 	g2="c,ccedilla" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="d" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="68" />
<hkern g1="C,Ccedilla" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="w" 	k="53" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="35" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="10" />
<hkern g1="D,Eth" 	g2="T" 	k="88" />
<hkern g1="D,Eth" 	g2="W" 	k="41" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="61" />
<hkern g1="D,Eth" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="49" />
<hkern g1="D,Eth" 	g2="AE" 	k="92" />
<hkern g1="D,Eth" 	g2="Z" 	k="45" />
<hkern g1="D,Eth" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="41" />
<hkern g1="D,Eth" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="D,Eth" 	g2="y,yacute,ydieresis" 	k="-10" />
<hkern g1="D,Eth" 	g2="quoteright,quotedblright" 	k="68" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-51" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="T" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="z" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Y,Yacute,Ydieresis" 	k="-31" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="AE" 	k="-20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Z" 	k="-41" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="c,ccedilla" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="d" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="g" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="27" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="G" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-49" />
<hkern g1="G" 	g2="T" 	k="61" />
<hkern g1="G" 	g2="Y,Yacute,Ydieresis" 	k="51" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-84" />
<hkern g1="K" 	g2="T" 	k="-74" />
<hkern g1="K" 	g2="W" 	k="-49" />
<hkern g1="K" 	g2="S" 	k="-14" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="-70" />
<hkern g1="K" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="K" 	g2="AE" 	k="-82" />
<hkern g1="K" 	g2="Z" 	k="-51" />
<hkern g1="K" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="35" />
<hkern g1="K" 	g2="G" 	k="35" />
<hkern g1="K" 	g2="c,ccedilla" 	k="37" />
<hkern g1="K" 	g2="d" 	k="37" />
<hkern g1="K" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="37" />
<hkern g1="K" 	g2="g" 	k="66" />
<hkern g1="K" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="K" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="45" />
<hkern g1="K" 	g2="C,Ccedilla" 	k="35" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-82" />
<hkern g1="L" 	g2="T" 	k="127" />
<hkern g1="L" 	g2="W" 	k="123" />
<hkern g1="L" 	g2="z" 	k="-68" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="127" />
<hkern g1="L" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="L" 	g2="AE" 	k="-66" />
<hkern g1="L" 	g2="Z" 	k="-25" />
<hkern g1="L" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="51" />
<hkern g1="L" 	g2="c,ccedilla" 	k="31" />
<hkern g1="L" 	g2="d" 	k="31" />
<hkern g1="L" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="31" />
<hkern g1="L" 	g2="g" 	k="31" />
<hkern g1="L" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="31" />
<hkern g1="L" 	g2="t" 	k="45" />
<hkern g1="L" 	g2="w" 	k="61" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="51" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="213" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="82" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="45" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Z" 	k="20" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-27" />
<hkern g1="R" 	g2="T" 	k="39" />
<hkern g1="R" 	g2="AE" 	k="-47" />
<hkern g1="R" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="8" />
<hkern g1="R" 	g2="c,ccedilla" 	k="45" />
<hkern g1="R" 	g2="d" 	k="45" />
<hkern g1="R" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="45" />
<hkern g1="R" 	g2="g" 	k="45" />
<hkern g1="R" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="45" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-49" />
<hkern g1="S" 	g2="T" 	k="41" />
<hkern g1="S" 	g2="W" 	k="39" />
<hkern g1="S" 	g2="Y,Yacute,Ydieresis" 	k="49" />
<hkern g1="S" 	g2="AE" 	k="35" />
<hkern g1="S" 	g2="w" 	k="20" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="27" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="72" />
<hkern g1="T" 	g2="T" 	k="-90" />
<hkern g1="T" 	g2="W" 	k="-86" />
<hkern g1="T" 	g2="s" 	k="94" />
<hkern g1="T" 	g2="S" 	k="-31" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-88" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="174" />
<hkern g1="T" 	g2="AE" 	k="139" />
<hkern g1="T" 	g2="Z" 	k="-20" />
<hkern g1="T" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="35" />
<hkern g1="T" 	g2="G" 	k="35" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="T" 	g2="c,ccedilla" 	k="94" />
<hkern g1="T" 	g2="d" 	k="94" />
<hkern g1="T" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="94" />
<hkern g1="T" 	g2="g" 	k="109" />
<hkern g1="T" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="94" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="82" />
<hkern g1="T" 	g2="C,Ccedilla" 	k="35" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="T" 	g2="n,ntilde" 	k="102" />
<hkern g1="T" 	g2="r" 	k="102" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="57" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="W" 	g2="T" 	k="-86" />
<hkern g1="W" 	g2="W" 	k="-90" />
<hkern g1="W" 	g2="S" 	k="-27" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-82" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="90" />
<hkern g1="W" 	g2="AE" 	k="92" />
<hkern g1="W" 	g2="Z" 	k="-20" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="51" />
<hkern g1="W" 	g2="c,ccedilla" 	k="47" />
<hkern g1="W" 	g2="d" 	k="47" />
<hkern g1="W" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="47" />
<hkern g1="W" 	g2="g" 	k="47" />
<hkern g1="W" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="47" />
<hkern g1="W" 	g2="t" 	k="-41" />
<hkern g1="W" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="25" />
<hkern g1="W" 	g2="w" 	k="-31" />
<hkern g1="W" 	g2="y,yacute,ydieresis" 	k="-27" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-49" />
<hkern g1="W" 	g2="n,ntilde" 	k="27" />
<hkern g1="W" 	g2="r" 	k="27" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-76" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-88" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="57" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="S" 	k="-14" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-90" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="129" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="-16" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="G" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,ccedilla" 	k="88" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d" 	k="88" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="88" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="88" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="88" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,Ccedilla" 	k="8" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="n,ntilde" 	k="61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="r" 	k="61" />
<hkern g1="Z" 	g2="T" 	k="-45" />
<hkern g1="Z" 	g2="W" 	k="-27" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="Z" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="Z" 	g2="G" 	k="33" />
<hkern g1="Z" 	g2="c,ccedilla" 	k="68" />
<hkern g1="Z" 	g2="d" 	k="68" />
<hkern g1="Z" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="68" />
<hkern g1="Z" 	g2="g" 	k="68" />
<hkern g1="Z" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="68" />
<hkern g1="Z" 	g2="C,Ccedilla" 	k="33" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="25" />
<hkern g1="c,ccedilla" 	g2="c,ccedilla" 	k="20" />
<hkern g1="c,ccedilla" 	g2="d" 	k="20" />
<hkern g1="c,ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="c,ccedilla" 	g2="f,uniFB01,uniFB02" 	k="-35" />
<hkern g1="c,ccedilla" 	g2="g" 	k="20" />
<hkern g1="c,ccedilla" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="c,ccedilla" 	g2="t" 	k="-20" />
<hkern g1="c,ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="14" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-41" />
<hkern g1="e,egrave,eacute,ecircumflex,edieresis" 	g2="y,yacute,ydieresis" 	k="-10" />
<hkern g1="g" 	g2="z" 	k="-16" />
<hkern g1="g" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-66" />
<hkern g1="g" 	g2="y,yacute,ydieresis" 	k="-61" />
<hkern g1="g" 	g2="j" 	k="-131" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01" 	g2="t" 	k="-20" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01" 	g2="w" 	k="-41" />
<hkern g1="i,igrave,iacute,icircumflex,idieresis,uniFB01" 	g2="y,yacute,ydieresis" 	k="-33" />
<hkern g1="j" 	g2="j" 	k="-102" />
<hkern g1="k" 	g2="z" 	k="-35" />
<hkern g1="k" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-57" />
<hkern g1="k" 	g2="c,ccedilla" 	k="10" />
<hkern g1="k" 	g2="d" 	k="10" />
<hkern g1="k" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="10" />
<hkern g1="k" 	g2="f,uniFB01,uniFB02" 	k="-33" />
<hkern g1="k" 	g2="g" 	k="10" />
<hkern g1="k" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="k" 	g2="t" 	k="-41" />
<hkern g1="k" 	g2="w" 	k="-45" />
<hkern g1="k" 	g2="y,yacute,ydieresis" 	k="-45" />
<hkern g1="l" 	g2="z" 	k="-41" />
<hkern g1="l" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="l" 	g2="f,uniFB01,uniFB02" 	k="29" />
<hkern g1="l" 	g2="g" 	k="29" />
<hkern g1="l" 	g2="t" 	k="29" />
<hkern g1="l" 	g2="y,yacute,ydieresis" 	k="10" />
<hkern g1="r" 	g2="z" 	k="-27" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="172" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="r" 	g2="c,ccedilla" 	k="16" />
<hkern g1="r" 	g2="d" 	k="10" />
<hkern g1="r" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="16" />
<hkern g1="r" 	g2="f,uniFB01,uniFB02" 	k="-51" />
<hkern g1="r" 	g2="g" 	k="16" />
<hkern g1="r" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="r" 	g2="t" 	k="-57" />
<hkern g1="r" 	g2="w" 	k="-51" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-59" />
<hkern g1="s" 	g2="c,ccedilla" 	k="20" />
<hkern g1="s" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="s" 	g2="t" 	k="25" />
<hkern g1="t" 	g2="z" 	k="-33" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-55" />
<hkern g1="t" 	g2="f,uniFB01,uniFB02" 	k="-20" />
<hkern g1="t" 	g2="g" 	k="20" />
<hkern g1="t" 	g2="t" 	k="-20" />
<hkern g1="t" 	g2="w" 	k="-18" />
<hkern g1="t" 	g2="y,yacute,ydieresis" 	k="-29" />
<hkern g1="t" 	g2="j" 	k="-14" />
<hkern g1="w" 	g2="z" 	k="-10" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="w" 	g2="f,uniFB01,uniFB02" 	k="-55" />
<hkern g1="w" 	g2="t" 	k="-53" />
<hkern g1="w" 	g2="w" 	k="-61" />
<hkern g1="w" 	g2="y,yacute,ydieresis" 	k="-72" />
<hkern g1="w" 	g2="quoteright,quotedblright" 	k="-61" />
<hkern g1="y,yacute,ydieresis" 	g2="z" 	k="-10" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="53" />
<hkern g1="y,yacute,ydieresis" 	g2="c,ccedilla" 	k="25" />
<hkern g1="y,yacute,ydieresis" 	g2="f,uniFB01,uniFB02" 	k="-59" />
<hkern g1="y,yacute,ydieresis" 	g2="g" 	k="10" />
<hkern g1="y,yacute,ydieresis" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="y,yacute,ydieresis" 	g2="t" 	k="-51" />
<hkern g1="y,yacute,ydieresis" 	g2="w" 	k="-61" />
<hkern g1="y,yacute,ydieresis" 	g2="y,yacute,ydieresis" 	k="-72" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteright,quotedblright" 	k="-74" />
<hkern g1="z" 	g2="z" 	k="-82" />
<hkern g1="z" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-41" />
<hkern g1="z" 	g2="f,uniFB01,uniFB02" 	k="-59" />
<hkern g1="z" 	g2="g" 	k="31" />
<hkern g1="z" 	g2="t" 	k="-27" />
<hkern g1="z" 	g2="w" 	k="-31" />
<hkern g1="z" 	g2="y,yacute,ydieresis" 	k="-41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="174" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W" 	k="31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="z" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Z" 	k="-41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="y,yacute,ydieresis" 	k="51" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-98" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-49" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-57" />
<hkern g1="quoteleft,quotedblleft" 	g2="f,uniFB01,uniFB02" 	k="-78" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="-57" />
<hkern g1="quoteleft,quotedblleft" 	g2="y,yacute,ydieresis" 	k="-78" />
<hkern g1="J" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-16" />
<hkern g1="J" 	g2="AE" 	k="72" />
</font>
</defs></svg> 