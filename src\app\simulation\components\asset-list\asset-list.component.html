<fieldset [disabled]="_landingService.isLockedTask[currentTask]">
<div class="row step-navigation">
  <div class="col-sm-12 btn-set">
    <ng-container *appAuthKey="'UBZ_ASSET.LIST_SEARCH'">
      <button type="button" class="btn btn-empty waves-effect waves-secondary pull-right" id="create-esistente" (click)="goToAssetSearch()"><i class="fa fa-search" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.11001' | translate }}</button>
    </ng-container>
    <ng-container *appAuthKey="'UBZ_ASSET.LIST_NEW'">
      <button type="button" class="btn btn-empty waves-effect waves-secondary pull-right" id="create-new-asset" (click)="goToNewAsset()"><i class="icon-add"></i> {{'UBZ.SITE_CONTENT.11010' | translate }}</button>
    </ng-container>
  </div>
</div>
<div class="row" id="asset-accordions">
  <div class="col-sm-12">
    <h3>{{'UBZ.SITE_CONTENT.11011' | translate }}</h3>
    <p *ngIf="assetService.listObjcet.length === 0" class="no-asset">{{'UBZ.SITE_CONTENT.11100' | translate }}.</p>
    <accordion>
      <ng-container *ngFor="let asset of assetService.listObjcet; let i = index;">
        <accordion-group #group>
          <div accordion-heading class="panel-heading" role="tab" (click)="toogleAsset(asset.idObject)">
            <h4 class="panel-title">
              <a role="button">
                <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i> {{asset.descObject}}
              </a>
              <div *ngIf="group?.isOpen" class="btn-set">
                <div class="save-controls" *ngIf="readOnlyAssets[asset.idObject]">
                  <ng-container *appAuthKey="'UBZ_ASSET.LIST_DELETE'">
                    <button name="delete-asset" type="button" class="btn btn-empty delete-asset" (click)="openDeleteModal(asset.idObject)"><i class="fa fa-trash-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.11110' | translate }}</button>
                  </ng-container>
                  <ng-container *appAuthKey="'UBZ_ASSET.LIST_MODIFY'">
                    <button name="edit-asset" type="button" class="btn btn-empty" (click)="setReadOnlyAssetProprieties(asset.idObject , false); $event.stopPropagation()"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.11111' | translate }}</button>
                  </ng-container>
                </div>
                <div class="edit-controls" *ngIf="!readOnlyAssets[asset.idObject]">
                  <ng-container *appAuthKey="'UBZ_ASSET.LIST_UNDO'">
                    <button name="cancel-asset" type="button" class="btn btn-empty" (click)="refreshApplicationFormForAssetId(asset.idObject) ; setReadOnlyAssetProprieties(asset.idObject , true) ; $event.stopPropagation()"><i class="fa fa-times" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.100000' | translate }}</button>
                  </ng-container>
                  <ng-container *appAuthKey="'UBZ_ASSET.LIST_SAVE'">
                    <button name="save-asset" type="button" class="btn btn-empty" (click)="upperSaveButtonClick(i); $event.stopPropagation();" [disabled]="formNotValid(i)"><i class="fa fa-check-circle-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.100001' | translate }}</button>
                  </ng-container>
                </div>
              </div>
            </h4>
          </div>
          <div class="panel-collapse collapse in" role="tabpanel">
            <div class="panel-body">
              <app-application-form 
                *ngIf="selectedAsset[asset.idObject]" 
                [idCode]="asset.idObject" 
                [page]="'INFO_ASSET'" 
                [drivers]="{'DD_AST_TIPO': assetService.familyAssetType}" 
                [setReadOnly]="readOnlyAssets[asset.idObject]"
                [positionId]="simulationId" 
                (formSubmit)="saveAssetChanges(asset.idObject)"
                [formDisabled] = "_landingService.isLockedTask[currentTask]">
                <div accordion-heading class="panel-heading" role="tab">
                  <h4 class="panel-title">
                    <a role="button"></a>
                    <div class="btn-set" style="text-align: right;">
                      <div class="save-controls" *ngIf="readOnlyAssets[asset.idObject]">
                        <ng-container *appAuthKey="'UBZ_ASSET.LIST_DELETE'">
                          <button name="delete-asset" type="button" class="btn btn-empty delete-asset" (click)="openDeleteModal(asset.idObject)"><i class="fa fa-trash-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.11110' | translate }}</button>
                        </ng-container>
                        <ng-container *appAuthKey="'UBZ_ASSET.LIST_MODIFY'">
                          <button name="edit-asset" type="button" class="btn btn-empty" (click)="setReadOnlyAssetProprieties(asset.idObject , false)"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.11111' | translate }}</button>
                        </ng-container>
                      </div>
                      <div class="edit-controls" *ngIf="!readOnlyAssets[asset.idObject]">
                        <ng-container *appAuthKey="'UBZ_ASSET.LIST_UNDO'">
                          <button name="cancel-asset" type="button" class="btn btn-empty" (click)="refreshApplicationFormForAssetId(asset.idObject); setReadOnlyAssetProprieties(asset.idObject , true)"><i class="fa fa-times" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.100000' | translate }}</button>
                        </ng-container>
                        <ng-container *appAuthKey="'UBZ_ASSET.LIST_SAVE'">
                          <button id="saveAsset{{i}}" name="saveAsset" type="submit" #saveAsset class="btn btn-empty" [disabled]="formNotValid(i)"><i class="fa fa-check-circle-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.100001' | translate }}</button>
                        </ng-container>
                      </div>
                    </div>
                  </h4>
                </div>
              </app-application-form>
            </div>
          </div>
        </accordion-group>
      </ng-container>
    </accordion>
  </div>
</div>
</fieldset>
<!-- CUSTOM MODAL gestisce la cancellazione dell'asset -->
<app-custom-modal 
  [modalType] = "'delete'"
  [isOpen] = "deleteModalIsOpen"
  [largeModalFlag] = "false"
  [headerTitle] = "'UBZ.SITE_CONTENT.1000101011'"
  [positionId]="''" 
  [idCode]="''"   
  [apfString] = "''"
  [messagesArray] = "['UBZ.SITE_CONTENT.1011001111']"
  [buttonTitle] = "['UBZ.SITE_CONTENT.11110', 'UBZ.SITE_CONTENT.100000']"
  [disabledFlag] = "false"  
  (modalSubmit) = "submitDeleteModal()"
  (modalClose) = "closeDeleteModal()">
</app-custom-modal>

