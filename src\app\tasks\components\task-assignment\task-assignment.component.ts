import { Component, OnInit, Input } from '@angular/core';
import { DomainService } from '../../../shared/domain/domain.service';
import { Domain } from '../../../shared/domain/domain';

@Component({
  selector: 'app-task-assignment',
  templateUrl: './task-assignment.component.html',
  styleUrls: ['./task-assignment.component.css']
})
export class TaskAssignmentComponent implements OnInit {
  @Input() reviser: string;
  @Input() expert: string;
  @Input() socHeading: string;
  @Input() surveyNecFlag: string;
  @Input()sendUcscFlag:string
 

  surveyTypeDomain: any = {};

  constructor(private domainService: DomainService) {}

  ngOnInit() {
    this.domainService.newGetDomain('UBZ_DOM_SURVEY_TYPE')
    .subscribe(res => this.surveyTypeDomain = res );
  }
}
