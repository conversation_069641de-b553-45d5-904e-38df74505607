import {
  Component,
  OnInit,
  Input,
  ViewChild,
  AfterViewChecked
} from '@angular/core';
import { SendAppraisalComponent } from '../../send-appraisal/send-appraisal.component';
import { NgForm } from '@angular/forms';
import { AccordionAPFService } from '../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';

@Component({
  selector: 'app-asset-with-collateral',
  templateUrl: './asset-with-collateral.component.html',
  styleUrls: ['./asset-with-collateral.component.css']
})
export class AssetWithCollateralComponent implements OnInit, AfterViewChecked {
  @Input()
  positionId: string;
  @Input()
  assetList: any[];
  @Input()
  title: string;
  @Input()
  semaforo: string;


  @ViewChild(SendAppraisalComponent)
  sendAppraisalComponent: SendAppraisalComponent;
  private subscribed: boolean;
  public validForm = false;
  constructor(public _accordionAPFService: AccordionAPFService) {}

  ngOnInit() {

  }

  ngAfterViewChecked() {

  }

}
