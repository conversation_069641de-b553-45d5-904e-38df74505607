<div class="MyTabs nav-wrap nav-controls">
  <ul class="nav nav-tabs pull-left">
    <li *ngFor="let ass of assets; let i = index" [ngClass]="{
        'active': ass === selectedAsset,
        'MyTabs__Tab': true,
        'MyTabs__Tab--hidden': i >= maxVisibleTabs
      }">
      <a role="tab" (click)="triggerSelectAsset(ass)">
        <i *ngIf="ass.limSelectedFlag" class="fa fa-user" aria-hidden="true"></i>
        <i class="fa fa-map-marker" data-placement="bottom" data-toggle="tooltip" [title]="getAssetAddress(ass)"></i>
        <span *ngIf="activeCategories.indexOf(ass.category) > -1">
          {{ass.category}} {{ass.idObject}}
        </span>
        <span *ngIf="activeCategories.indexOf(ass.category) === -1">
          <span class="expiredCategory">{{ass.category}} </span> {{ass.idObject}}
        </span>
        <br>
        <!-- Display Asset Id + Tooltip: { Asset Id, Disabled State and Promiscuo State } -->
        <app-tab-managed-asset-details [resItemId]="ass.resItemId" [assetDetail]="ass.assetDetail"
          [isMultipleAssetPerObject]="ass.isMultipleAssetPerObject" [isAppraisalConvalidated]="isAppraisalConvalidated">
        </app-tab-managed-asset-details>

        <span class="state"
          [ngClass]="{'green' : (ass.conclusionStatusFlag === 'Y'), 'red' : (ass.conclusionStatusFlag === 'N')}"></span>
      </a>
    </li>
  </ul>
  <button *ngIf="canAddAsset" (click)="triggerNewAssetModal()" type="button" class="btn btn-empty btn-plus pull-left"
    [disabled]="readOnly" id="addAssetButton">
      <i class="fa fa-plus"></i>
    </button>
    <span *ngIf="!canAddAsset" id="addAssetButton"></span>

  <div dropdown class="MyTabs__Dropdown" *ngIf="shouldShowDropdown">
    <button class="MyTabs__Dropdown__Toggle btn btn-empty btn-plus" dropdownToggle>
      <i class="fa fa-caret-down"></i>
    </button>

    <div *dropdownMenu class="MyTabs__Dropdown__Menu dropdown-menu dropdown-menu-right" role="menu">
      <ul>
        <li *ngFor="let asset of assets; let i = index" class="dropdown-item" [ngClass]="{
            'MyTabs__Dropdown__Menu__Item--active': selectedAsset === asset,
            'hidden': i < maxVisibleTabs
          }">
          <a (click)="triggerSelectAsset(asset)">
            <i class="fa fa-map-marker" data-placement="bottom" data-toggle="tooltip"
              [title]="getAssetAddress(asset)"></i>
            <span *ngIf="activeCategories.indexOf(asset.category) > -1">
              {{asset.category}} {{asset.idObject}}
            </span>
            <span *ngIf="activeCategories.indexOf(asset.category) === -1">
              <span class="expiredCategory">{{asset.category}}</span> {{asset.idObject}}
            </span>
            <br>

            <!-- Display Asset Id + Tooltip: { Asset Id, Disabled State and Promiscuo State } -->
            <app-tab-managed-asset-details [resItemId]="asset.resItemId" [assetDetail]="asset.assetDetail"
              [isMultipleAssetPerObject]="asset.isMultipleAssetPerObject"
              [isAppraisalConvalidated]="isAppraisalConvalidated">
            </app-tab-managed-asset-details>

            <span class="state"
              [ngClass]="{'green' : (asset.conclusionStatusFlag === 'Y'), 'red' : (asset.conclusionStatusFlag === 'N')}"></span>
          </a>
        </li>
      </ul>
    </div>
  </div>
</div>