import {
  Component,
  OnInit,
  ViewChild,
  Input,
  AfterContentChecked
} from '@angular/core';
import { NgForm } from '@angular/forms';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import {
  GrossProductionItem,
  AgrarianAgencyModel
} from '../../model/agrarian-agency.models';
import { Domain } from '../../../../../shared/domain/domain';
import { DomainService } from '../../../../../shared/domain/domain.service';

@Component({
  selector: 'app-gross-production',
  templateUrl: './gross-production.component.html',
  styleUrls: ['./gross-production.component.css']
})
export class GrossProductionComponent implements OnInit, AfterContentChecked {
  @ViewChild(NgForm) private form: NgForm;
  @Input() model: AgrarianAgencyModel = new AgrarianAgencyModel();
  expenseType: Domain[] = [];

  constructor(
    public _accordionAPFService: AccordionAPFService,
    private _domainService: DomainService
  ) {}

  ngOnInit() {
    this._domainService
      .newGetDomain('UBZ_DOM_EXPENSE_TYPE', 'PLV')
      .subscribe(res => {
        this.expenseType = res;
      });
  }

  ngAfterContentChecked() {
    if (
      this.model.appFarmNoIncomePlv &&
      this.model.appFarmNoIncomePlv.length === 0
    ) {
      const item1 = new GrossProductionItem();
      const item2 = new GrossProductionItem();
      const item3 = new GrossProductionItem();
      item1.measurement = 'q.li';
      item2.measurement = 'q.li';
      item3.measurement = 'hl';
      this.model.appFarmNoIncomePlv.push(item1, item2, item3);
    }
  }

  isValid(): boolean {
    return this.form && this.form.valid;
  }

  getTotal(): number {
    let total = 0;
    if (
      this.model &&
      this.model.appFarmNoIncomePlv &&
      this.model.appFarmIncomePlv
    ) {
      for (const item of this.model.appFarmNoIncomePlv) {
        total += item.totPrice ? Number(item.totPrice) : 0;
      }
      total += this.model.appFarmIncomePlv['ULS'].totPrice
        ? Number(this.model.appFarmIncomePlv['ULS'].totPrice)
        : 0;
      total += this.model.appFarmIncomePlv['RGA'].totPrice
        ? Number(this.model.appFarmIncomePlv['RGA'].totPrice)
        : 0;
      total += this.model.appFarmIncomePlv['PAC'].totPrice
        ? Number(this.model.appFarmIncomePlv['PAC'].totPrice)
        : 0;
    }
    return total;
  }
}
