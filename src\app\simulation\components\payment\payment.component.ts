import { AfterViewInit, Component, OnInit, ViewChild } from "@angular/core";
import { PaymentService } from "../../services/payment/payment.service";
import { ActivatedRoute, Params, Router } from "@angular/router";
import { NgForm } from "@angular/forms";
import { LandingService } from "../../services/landing/landing.service";
import { Observable } from "rxjs";
import { GenericInfoService } from "../../services/generic-info/generic-info.service";
import { Simulation } from "../../model/simulation";
import { of } from "rxjs/observable/of";


@Component({
  selector: "app-payment",
  templateUrl: "./payment.component.html",
  styleUrls: ["./payment.component.css"],
})
export class PaymentComponent implements OnInit, AfterViewInit {
  wizardCode: string;
  positionId: any;
  select: any = "";
  userSelect: any = "";
  allCustomers: any[] = [];
  singleuserdb: any;
  sedbUser: any = {};
  mydata: any = {};
  currentTask: string = "UBZ-REQ-PMT";
  simulation = new Simulation();
  isSaveButtonEnabled = false;
  invoiceUcscFlag: any;
  draftButtonCallback = this.saveData.bind(this);
  @ViewChild("data") data!: NgForm;

  constructor(
    private service: PaymentService,
    private activatedRoute: ActivatedRoute,
    public landingService: LandingService,
    public genericInfoService: GenericInfoService,
    private router: Router
  ) {}

  ngOnInit() {
    this.retrievePaymentData();
  }

  ngAfterViewInit() {
    this.data.form.statusChanges.subscribe(() => {
      setTimeout(() => {
        this.updateSaveButtonEnabledState();
      }, 0);
    });
  }

  retrievePaymentData() {
    this.activatedRoute.parent.params
      .switchMap((params: Params) => {
        this.wizardCode = params["wizardCode"];
        this.positionId = params["positionId"];
        this.currentTask = "UBZ-REQ-PMT";
        return this.genericInfoService.getGenericInfo(
          params["positionId"],
          this.wizardCode
        );
      })
      .switchMap((res) => {
        this.simulation = res;
        this.landingService.originationProcess =
          this.simulation.originationProcess;
        this.landingService.checkIfCompletedTask(
          this.positionId,
          this.currentTask
        );
        return Observable.forkJoin(
          this.service.getCustomeres(this.positionId),
          this.service.saveCustomer(this.positionId),
          this.service.getInvoice(this.positionId)
        );
      })
      .subscribe((res) => {
        this.allCustomers = res[0].customers;
        if (this.allCustomers.length === 1) {
          this.userSelect = this.allCustomers[0].name;
          const user = this.allCustomers[0];
          this.mydata = {
            ndg: user.ndg,
            address: user.address,
            city: user.city,
            province: user.province,
            taxCode: user.taxCode,
            email: user.email,
          };
          this.data.form.patchValue(this.mydata);
        }
        this.singleuserdb = res[1];
        this.getSingleObjectDb();
        this.invoiceUcscFlag = res[2].invoiceUcscFlag;
        if (this.invoiceUcscFlag === "Y") {
          this.select = "option1";
        } else if (this.invoiceUcscFlag === "N") {
          this.select = "option2";
        } else {
          if (this.invoiceUcscFlag === null) {
            this.select = "";
          }
        }
      });
  }

  getSelect() {
    const user = this.allCustomers.find((el) => el.name === this.userSelect);

    if (user) {
      this.data.form.patchValue({
        ndg: user.ndg,
        address: user.address,
        city: user.city,
        province: user.province,
        taxCode: user.taxCode,
        email: user.email,
      });
    } else {
      this.data.form.patchValue({
        ndg: "",
        address: "",
        city: "",
        province: "",
        taxCode: "",
        email: "",
      });
    }
  }
  getUserObjectlengtht() {
    const allValues = Object.keys(this.singleuserdb).every(
      (key) => this.singleuserdb[key] === null
    );
    if (allValues) {
      return false;
    } else {
      return true;
    }
  }

  getSingleObjectDb() {
    if (this.singleuserdb) {
      const allValues = Object.keys(this.singleuserdb).every(
        (key) =>  this.singleuserdb[key] === null
      );
      if (!allValues) {
        this.sedbUser = `${this.singleuserdb.name} ${this.singleuserdb.surname}`;
        this.userSelect = this.singleuserdb.name;
        const user = this.singleuserdb;
        this.mydata = {
          ndg: user.ndg,
          name: user.name,
          surname: user.surname,
          address: user.address,
          city: user.city,
          province: user.province,
          taxCode: user.taxCode,
          email: user.email,
        };

        this.data.form.patchValue(this.mydata);
      }
    }
  }

  confirmPayment() {
    if (
      this.mydata.email &&
      this.mydata.city &&
      this.mydata.address &&
      this.mydata.taxCode
    ) {
      this.landingService.goNextPage(
        this.positionId,
        this.currentTask,
        this.wizardCode,
        this.activatedRoute
      );
    } else {
      let payUser = {};
      if (this.select === "option1") {
        this.allCustomers.forEach((el) => {
          let newPayUser = {
            ndg: el.ndg,
            name: el.name,
            surname: el.surname,
            address: "",
            city: "",
            taxCode: "",
            email: "",
          };
          Object.assign(payUser, newPayUser);
          this.service
            .updateCustomer(this.positionId, payUser)
            .subscribe((res) => console.log(res));
        });
      }
    }

    let payUser = {};
    if (
      this.select === "option1" &&
      this.mydata &&
      this.mydata.email &&
      this.mydata.city &&
      this.mydata.address &&
      this.mydata.taxCode
    ) {
      this.allCustomers.forEach((el) => {
        let newPayUser = {
          ndg: el.ndg,
          name: el.name,
          surname: el.surname,
          address: el.address,
          city: el.city,
          province: el.province,
          taxCode: el.taxCode,
          email: el.email,
        };
        if (this.userSelect === el.name) {
          Object.assign(payUser, newPayUser);
          this.service
            .updateCustomer(this.positionId, payUser)
            .subscribe((res) => console.log(res));
        }
      });
    } else {
      if (this.select === "option2") {
        this.service
          .deleteCustomer(this.positionId)
          .subscribe((res) => {
            console.log(res);
            this.landingService.goNextPage(
              this.positionId,
              this.currentTask,
              this.wizardCode,
              this.activatedRoute
            );
          });
      }
    }
  }
  saveData() {
    if (this.select === "option1") {
      const saveUser = {};

      this.allCustomers.forEach((el) => {
        let savePayUser = {
          ndg: el.ndg,
          name: el.name,
          surname: el.surname,
          address: el.address,
          city: el.city,
          province: el.province,
          taxCode: el.taxCode,
          email: el.email,
        };

        if (this.userSelect === el.name) {
          Object.assign(saveUser, savePayUser);
          this.service
            .updateCustomer(this.positionId, saveUser)
            .subscribe((res) => console.log(res));
        }
      });
      return of(true);
    } else {
      return of(true);
    }
  }

  saveDraft() {
    this.router.navigate(["/"]);
  }
  previous() {
    this.landingService.goToPreviousTask(
      this.positionId,
      this.wizardCode,
      this.activatedRoute
    );
  }

  updateSaveButtonEnabledState() {
    /* I'll keep this old check as a record
    the functional check was given to me(EE52245) by Irene Tassi
    The check should be "if it is 'Società Peritale/Perito Beneviso' the button is always enabled, otherwise if it is 'Unicredit Subito Casa' it needs the Email with a valid value"
    this.isSaveButtonEnabled =
      this.data.valid && this.simulation && this.select && this.userSelect;
      */
    this.isSaveButtonEnabled =
      this.data.valid
        && this.select !== null && this.select !== undefined && this.select !== '' && (this.select === 'option2' || (this.select === 'option1'
        && this.mydata.ndg !== null && this.mydata.ndg !== undefined));
  }

  cancelPosition() {
    this.landingService
      .cancelPosition(this.simulation.positionId, this.wizardCode)
      .subscribe((res) => this.router.navigate(["/"]));
  }
}
