import { Component, OnInit } from "@angular/core";
import { ActivatedRoute, Params } from "@angular/router";
import { AppraisalCtuService } from "../../../../appraisal-ctu/service/appraisal-ctu.service";

@Component({
  selector: "app-appraisal-values",
  templateUrl: "./appraisal-values.component.html",
  styleUrls: ["./appraisal-values.component.css"],
  providers: [AppraisalCtuService],
})
export class AppraisalValuesComponent implements OnInit {
  appraisalId: string;
  dataValue:any = {};
  constructor(
    private appraisalService: AppraisalCtuService,
    private activatedRoute: ActivatedRoute
  ) {}

  ngOnInit() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.appraisalId = params["positionId"];
      this.appraisalService
        .getAppraisalValue(this.appraisalId)
        .subscribe((res: any) => {
          this.dataValue = res;
        });
    });
  }
}
