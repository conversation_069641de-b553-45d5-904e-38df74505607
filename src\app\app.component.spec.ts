import { CommonModule } from '@angular/common';
import { UrlResolver } from '@angular/compiler';
import { CUSTOM_ELEMENTS_SCHEMA, LOCALE_ID, NO_ERRORS_SCHEMA } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ConnectionBackend, Http, HttpModule, RequestOptions } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { MissingTranslationHandler, TranslateLoader, TranslateModule, TranslateParser, TranslateService, USE_STORE } from '@ngx-translate/core';
import { TranslateStore } from '@ngx-translate/core/src/translate.store';
import { ToastModule, ToastOptions, ToastsManager } from 'ng2-toastr';
import { TooltipConfig } from 'ngx-bootstrap';
import { CookieModule } from 'ngx-cookie';
import { CookieOptionsProvider, COOKIE_OPTIONS } from 'ngx-cookie/src/cookie-options-provider';
import { CookieService } from 'ngx-cookie/src/cookie.service';
import { Mock } from 'protractor/built/driverProviders';
import { AppComponent } from './app.component';
import { APP_CONSTANTS, AppConstants } from './app.constants';
import { getAlertConfig } from './app.module';
import { ExpertFirmRegistryComponent } from './registry/component/expert-firm-registry/expert-firm-registry.component';
import { CustomHttpService } from './shared/http/custom-http.service';
import { MenuService } from './shared/menu/services/menu.service';
import { MessageService } from './shared/messages/services/message.service';
import { TranslationsLoader } from './shared/translations/translations-loader.service';
import { UserDataService } from './shared/user-data/user-data.service';

describe('AppComponent', () => {
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RouterTestingModule, ReactiveFormsModule, FormsModule, CommonModule, HttpModule, TranslateModule.forRoot(),CookieModule.forRoot(), ToastModule.forRoot()],
      declarations: [AppComponent],
      providers:
      [
        MessageService, 
        { provide: Http, useClass: CustomHttpService },
        { provide: APP_CONSTANTS, useValue: AppConstants },
        { provide: LOCALE_ID, useValue: 'it-IT' },
        { provide: TooltipConfig, useFactory: getAlertConfig },
        UserDataService, CookieService, CookieOptionsProvider, MenuService
    ],
      schemas:[NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  it('should create the app', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app).toBeTruthy();
  });
});