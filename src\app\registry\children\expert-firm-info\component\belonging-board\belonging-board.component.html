<form #f="ngForm">
  <accordion-group #group class="panel" [ngClass]="{'empty-accordion': !modifyMode && !belongingBoardPresent }" [isOpen]="modifyMode"
    [isDisabled]="!belongingBoardPresent">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          {{'UBZ.SITE_CONTENT.1011100101' | translate }}
          <div class="accordion-button">
            <ng-container *appAuthKey="'UBZ_REGISTRY.BELONGING_BOARD_NEW'">
              <button *ngIf="!modifyMode && !belongingBoardPresent" type="button" class="btn btn-empty" (click)="enableModify($event)">
                <i class="fa icon-add"></i>{{'UBZ.SITE_CONTENT.111100001' | translate }}
              </button>
            </ng-container>
            <ng-container *appAuthKey="'UBZ_REGISTRY.BELONGING_BOARD_MODIFY'">
              <button *ngIf="!modifyMode && belongingBoardPresent" type="button" class="btn btn-empty" (click)="startEdit($event)">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.11111' | translate }}
              </button>
            </ng-container>
            <ng-container *ngIf="modifyMode">
              <button *appAuthKey="'UBZ_REGISTRY.BELONGING_BOARD_ABORT'" type="button" class="btn btn-empty" (click)="abortAdding($event)">
                <i class="fa fa-times"></i>{{'UBZ.SITE_CONTENT.100000' | translate }}
              </button>
              <button *appAuthKey="'UBZ_REGISTRY.BELONGING_BOARD_SAVE'" type="button" class="btn btn-empty" (click)="saveAddedData($event)"
                [disabled]="f.invalid">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.100001' | translate }}
              </button>
            </ng-container>
          </div>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <form #f="ngForm" novalidate>
            <ng-container *ngIf="modifyMode; else only_visualization">
              <div class="row">
                <div class="col-md-4 col-sm-12 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1011100110' | translate }}*</label>
                  <app-calendario [name]="'registrationData'" [(ngModel)]="registerData.registrationData" [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                    [required]="true">
                  </app-calendario>
                </div>
                <div class="col-md-4 col-sm-12 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1011100111' | translate }}*</label>
                  <input type="text" name="numberRegistration" appOnlyNumbers class="form-control" [(ngModel)]="registerData.numberRegistration"
                    required>
                </div>
                <div class="col-md-4 col-sm-12 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1011101000' | translate }}*</label>
                  <input type="text" name="registerType" [(ngModel)]="registerData.registerType" class="form-control" required>
                </div>
              </div>
              <div class="row">
                <div class="col-md-4 col-sm-12 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1011101001' | translate }}*</label>
                  <input type="text" name="registerBoard" class="form-control" [(ngModel)]="registerData.registerBoard" required>
                </div>
                <div class="col-md-4 col-sm-12 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1011101010' | translate }}*</label>
                  <app-calendario [name]="'registerBoardDate'" [(ngModel)]="registerData.registerBoardDate" [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                    [required]="true">
                  </app-calendario>
                </div>
              </div>
            </ng-container>
          </form>
        </div>
      </div>
    </div>
  </accordion-group>
</form>

<ng-template #only_visualization>
  <div class="row">
    <div class="col-md-4 col-sm-12 form-group">
      <label>{{'UBZ.SITE_CONTENT.1011100110' | translate }}</label>
      {{ registerData.registrationData | date:'dd-MM-yyyy' }}
    </div>
    <div class="col-md-4 col-sm-12 form-group">
      <label>{{'UBZ.SITE_CONTENT.1011100111' | translate }}</label>
      {{ registerData.numberRegistration }}
    </div>
    <div class="col-md-4 col-sm-12 form-group">
      <label>{{'UBZ.SITE_CONTENT.1011101000' | translate }}</label>
      {{ registerData.registerType }}
    </div>
  </div>
  <div class="row">
    <div class="col-md-4 col-sm-12 form-group">
      <label>{{'UBZ.SITE_CONTENT.1011101001' | translate }}</label>
      {{ registerData.registerBoard }}
    </div>
    <div class="col-md-4 col-sm-12 form-group">
      <label>{{'UBZ.SITE_CONTENT.1011101010' | translate }}</label>
      {{ registerData.registerBoardDate | date:'dd-MM-yyyy' }}
    </div>
  </div>
</ng-template>
