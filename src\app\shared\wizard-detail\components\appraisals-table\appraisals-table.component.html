<h4 class="section-heading">{{'UBZ.SITE_CONTENT.11110101' | translate }}</h4>
<table class="uc-table">
  <thead>
    <tr>
      <th scope="col">{{'UBZ.SITE_CONTENT.10000010101' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.100100' | translate }}</th>
      <th scope="col" *ngIf="isAppraisalConvalidated === true">{{'UBZ.SITE_CONTENT.11110000101' | translate }}</th>
      <th scope="col" *ngIf="isAppraisalConvalidated === true">{{'UBZ.SITE_CONTENT.11110011001' | translate }}</th>
      <th scope="col" *ngIf="isAppraisalConvalidated === true">{{'UBZ.SITE_CONTENT.11110000110' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.11110110' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.100101' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.11110111' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.101011' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.11101111' | translate }}</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let asset of list; let i = index;">
      <td attr.data-label="{{'UBZ.SITE_CONTENT.10000010101' | translate }}">{{ asset.objectCod }}</td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.100100' | translate }}">
        <ng-container *ngIf="genericTask !== true; else isAppraisalDetailsPage">
          <!-- CASE: On Richiesta Details Page / Dettaglio Richiesta (in wizard-detail component) -->
          {{ asset.resItemId ? asset.resItemId : '' }}
        </ng-container>
        <ng-template #isAppraisalDetailsPage>
          <!-- CASE: On Appraisal Details Page / Dettaglio Perizia (in generic-task component) -->
          <ng-container *ngIf="isAppraisalConvalidated && isAssetStateAvailable(asset); else missingAssetState">
            <!-- Appraisal is in PER-COV and asset state from UAM is available -->
            <ng-container *appAuthKey="'UBZ_ASSET_ID_WITH_LINK'">
              <ng-container *ngIf="!asset.isMultipleAssetPerObject; else multipleAssetPerObject">
                <a *ngIf="asset.assetDetail?.length" href="javascript:;"
                  (click)="goToAssetPage(asset.assetDetail[0].assetId)">
                  {{ asset.assetDetail[0].assetId }}
                </a>
              </ng-container>
            </ng-container>

            <!--Servicing-->
            <ng-container *appAuthKey="'UBZ_ASSET_ID_NO_LINK'">
              <ng-container *ngIf="!asset.isMultipleAssetPerObject; else multipleAssetPerObject">
                {{ asset.assetDetail[0].assetId }}
              </ng-container>
            </ng-container>


            <ng-template #multipleAssetPerObject>
              <!-- Open Asset Details Modal for Multiple Asset Per Object -->
              <button type="button" class="btn btn-clean waves-effect waves-secondary" data-toggle="modal"
                data-target="#assetDetailsModal" (click)="showAssetDetailsModal(i)">
                <i class="icon-plus_open_lightbox" aria-hidden="true"></i>
              </button>
            </ng-template>
          </ng-container>
          <!-- Appraisal is NOT in PER-COV or asset state from UAM is missing -->
          <ng-template *appAuthKey="'UBZ_ASSET_ID_WITH_LINK'" #missingAssetState>
            <a href="javascript:;" (click)="goToAssetPage(asset.resItemId)">
              {{ asset.resItemId ? asset.resItemId : '' }}
            </a>
          </ng-template>

          <ng-template *appAuthKey="'UBZ_ASSET_ID_NO_LINK'" #missingAssetState>
            {{ asset.resItemId ? asset.resItemId : '' }}
          </ng-template>
        </ng-template>
      </td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.11110000101' | translate }}" *ngIf="isAppraisalConvalidated === true">
        <ng-container *ngIf="asset.isMultipleAssetPerObject === false">
          <span *ngIf="asset.assetDetail?.length && asset.assetDetail[0]?.disabledStatus === false"
            class="state green"></span>
          <div class="td-asset-state">
            <span *ngIf="asset.assetDetail?.length && asset.assetDetail[0]?.disabledStatus === true"
              class="state red"></span>
            <span
              *ngIf="asset.assetDetail?.length && asset.assetDetail[0]?.disabledStatus === true && isOnDetailPage === true">
              <button data-target="#disableAssetNotesModal" data-toggle="modal" (click)="openDisableNotesModal(i)"
                type="button" class="btn btn-empty" id="openDisableNotesBtn">
                <i class="icon-note"></i>
              </button>
            </span>
          </div>
        </ng-container>
      </td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.11110011001' | translate }}" *ngIf="isAppraisalConvalidated === true">
        <ng-container *appAuthKey="'UBZ_ASSET_ID_WITH_LINK'">
          <ng-container *ngIf="asset.isMultipleAssetPerObject === false">
            <a *ngIf="asset.assetDetail?.length && asset.assetDetail[0]?.disabledStatus === true" href="javascript:;"
              (click)="goToAssetPage(asset.assetDetail[0].targetAsset)">
              {{ asset.assetDetail[0].targetAsset }}
            </a>
          </ng-container>
        </ng-container>
        <ng-container *appAuthKey="'UBZ_ASSET_ID_NO_LINK'">
          <ng-container *ngIf="asset.isMultipleAssetPerObject === false">
            {{ asset.assetDetail[0].targetAsset }}
          </ng-container>
        </ng-container>
      </td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.11110000110' | translate }}" *ngIf="isAppraisalConvalidated === true">
        <ng-container *ngIf="asset.isMultipleAssetPerObject === false">
          <ng-container
            *ngIf="asset.assetDetail?.length && asset.assetDetail[0]?.disabledStatus === false; else promiscuoStateGrey">
            <span *ngIf="asset.assetDetail[0]?.promiscuo === false" class="state green"></span>
            <span *ngIf="asset.assetDetail[0]?.promiscuo === true" class="state red"></span>
          </ng-container>
          <ng-template #promiscuoStateGrey>
            <span *ngIf="asset.assetDetail?.length && asset.assetDetail[0]?.disabledStatus === true"
              class="state grey"></span>
          </ng-template>
        </ng-container>
      </td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.11110110' | translate }}">
        <div class="status-wrap">
          <ng-container *ngIf="wizardCode === 'WSIM' || wizardCode === 'WRPE'; else dettaglio_perizia">
            <span *ngIf="asset.objectStatus" class="state green"></span>
            <span *ngIf="!asset.objectStatus" class="state red"></span>
          </ng-container>
          <ng-template #dettaglio_perizia>
            <span *ngIf="asset.conclusionStatusFlag === 'Y'" class="state green"></span>
            <span *ngIf="asset.conclusionStatusFlag === 'N'" class="state red"></span>
          </ng-template>
        </div>
      </td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.100101' | translate }}">{{ getFamilyType(i) | translate }}</td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.11110111' | translate }}">
        {{ getCategoryType(asset.resItemCategory) | translate }}
      </td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.101011' | translate }}">{{ asset.address }}</td>
      <td attr.data-label="{{'UBZ.SITE_CONTENT.11101111' | translate }}">{{ asset.city }}</td>
    </tr>
  </tbody>
</table>

<!-- ASSET DETAILS MODAL -->
<div class="modal fade" id="id-asset-details" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal
  #assetDetailsModal="bs-modal">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.10001100101' | translate }}</h2>
        <button type="button" class="close pull-right" data-dismiss="modal" aria-label="Close"
          (click)="hideAssetDetailsModal()">
          <i class="icon-close" aria-hidden="true"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <table class="uc-table">
              <thead>
                <tr>
                  <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.100100' | translate }}</th>
                  <th scope="col" class="col-sm-2 text-center">{{'UBZ.SITE_CONTENT.11110000101' | translate }}</th>
                  <th scope="col" class="col-sm-2 text-center">{{'UBZ.SITE_CONTENT.11110011001' | translate }}</th>
                  <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.11110000110' | translate }}</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let assetDetails of selectedAssetDetails">
                  <ng-container *appAuthKey="'UBZ_ASSET_ID_WITH_LINK'">
                    <td attr.data-label="{{'UBZ.SITE_CONTENT.100100' | translate }}">
                      <a href="javascript:;" (click)="goToAssetPage(assetDetails.assetId)">
                        {{ assetDetails.assetId }}
                      </a>
                    </td>
                  </ng-container>
                  <ng-container *appAuthKey="'UBZ_ASSET_ID_NO_LINK'">
                    <td attr.data-label="{{'UBZ.SITE_CONTENT.100100' | translate }}">
                      {{ assetDetails.assetId }}
                    </td>
                  </ng-container>
                  <td attr.data-label="{{'UBZ.SITE_CONTENT.11110000101' | translate }}" class="text-center">
                    <span *ngIf="assetDetails.disabledStatus === false" class="state green"></span>
                    <span *ngIf="assetDetails.disabledStatus === true" class="state red"></span>
                  </td>
                  <ng-container *appAuthKey="'UBZ_ASSET_ID_WITH_LINK'">
                    <td attr.data-label="{{'UBZ.SITE_CONTENT.11110011001' | translate }}" class="text-center">
                      <a *ngIf="assetDetails.disabledStatus === true" href="javascript:;"
                        (click)="goToAssetPage(assetDetails.targetAsset)">
                        {{ assetDetails.targetAsset }}
                      </a>
                    </td>
                  </ng-container>
                  <ng-container *appAuthKey="'UBZ_ASSET_ID_NO_LINK'">
                    <td attr.data-label="{{'UBZ.SITE_CONTENT.11110011001' | translate }}" class="text-center">
                      {{ assetDetails.targetAsset }}
                    </td>
                  </ng-container>
                  <td attr.data-label="{{'UBZ.SITE_CONTENT.11110000110' | translate }}">
                    <ng-container *ngIf="assetDetails.disabledStatus === false; else modalPromiscuoStateGrey">
                      <span *ngIf="assetDetails.promiscuo === false" class="state green"></span>
                      <span *ngIf="assetDetails.promiscuo === true" class="state red"></span>
                    </ng-container>
                    <ng-template #modalPromiscuoStateGrey>
                      <span *ngIf="assetDetails.disabledStatus === true" class="state grey"></span>
                    </ng-template>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- DISABLE ASSET NOTES MODAL -->
<div class="modal fade" id="disable-asset-notes" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal
  #disableAssetNotesModal="bs-modal">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.11110001000' | translate }}</h2>
        <button type="button" class="close pull-right" aria-label="Close" (click)="closeModalNote()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <ng-container *ngFor="let asset of assetNotes">
          <div class="row">
            <div class="col-sm-6 form-group">
              <label>{{'UBZ.SITE_CONTENT.10110' | translate }}</label> {{asset.disableUser}}
            </div>
            <div class="col-sm-6 form-group">
              <label>{{'UBZ.SITE_CONTENT.11110001010' | translate }} </label>
              {{asset.disableDate | date: 'dd-MM-yyyy' }}
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12 form-group">
              <label>{{'UBZ.SITE_CONTENT.11110001011' | translate }}</label>
              {{ (activeDomain && activeDomain[asset.disableReason]) ? (activeDomain[asset.disableReason].translationCod
              | translate) : ''}}
            </div>
            <div class="col-sm-12 form-group">
              <label>{{'UBZ.SITE_CONTENT.11001011' | translate }}</label> {{asset.disableNote}}
            </div>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
</div>