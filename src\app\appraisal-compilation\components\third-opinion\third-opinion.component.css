:host>>>.tooltip-inner {
  color: #333;
  background-color: #fff;
  box-shadow: 2px 2px 2px #e3e3e3;
}

:host>>>.tooltip-arrow {
  border-right-color: #ccc !important;
}

.table-fixed-wrap tbody tr td.fixed {
  padding-left: 0px;
}

.table-fixed-wrap .fixed {
  position: absolute;
  left: 0;
  width: 250px;
  line-height: 40px;
  background: #fff;
  border-right: 1px solid #c6c6c6;
  padding-left: 0px;
}

.table-fixed-wrap tbody tr td {
  font-family: 'unicreditregular';
}

.table-fixed-wrap tbody tr td.sub-category {
  padding-left: 15px;
}

.table-fixed-wrap .fixed.fixed-two-rows {
  width: 250px !important;
}

.table-fixed-wrap {
  margin-left: 250px;
}

.table-fixed-wrap tbody tr td.fixed {
  padding-left: unset !important;
}

.table-fixed-wrap tbody tr td.fixed.sub-category {
  padding-left: 35px !important;
}

.modal-content h2 {
  text-align: center;
}

.modal-content p {
  text-align: center;
}
