export class ComparableFieldModel {
  span: number;
  code: null;
  description: string;
  disabled: boolean;
  domainCod: string;
  label: string;
  mandatory: string;
  pattern: string;
  readOnlyInSection: boolean;
  readOnly: boolean;
  renderIf: boolean;
  styleClass: string;
  tooltip: string;
  type: string;
  usage: string;
  value: string;
  xpath: string;
  placeholder: string;
  showLabel: boolean;
  fieldValue: string;
  validationRule: string;
  required: boolean;
  parentFieldCod: string;
  parentFieldValue: string;
  precision: number;
  percentage: number;
  hide: boolean
}