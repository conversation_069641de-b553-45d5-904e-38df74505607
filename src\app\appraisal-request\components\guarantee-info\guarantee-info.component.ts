import { Component, OnInit, Inject } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { GuaranteeInfoService } from '../../services/guarantee-info/guarantee-info.service';
import { <PERSON><PERSON><PERSON><PERSON> } from '../../model/guarantee';
import { DomainService } from '../../../shared/domain/domain.service';
import { Domain } from '../../../shared/domain/domain';
import { LandingService } from '../../../simulation/services/landing/landing.service';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';
import { GenericInfoService } from '../../../simulation';
import { GuaranteeFractionationService } from '../../../guarantee-fractionation/services/guarantee-fractionation.service';
import { of } from 'rxjs/observable/of';

@Component({
  selector: 'app-guarantee-info',
  templateUrl: './guarantee-info.component.html',
  styleUrls: ['./guarantee-info.component.css'],
  providers: [GuaranteeInfoService]
})
export class GuaranteeInfoComponent implements OnInit {
  positionId: string;
  wizardCode: string;
  taskCode = 'UBZ-REQ-GAR';
  guarantees: Guarantee[] = new Array();
  filteredGuarantees: Guarantee[] = [];
  selectAll = false;
  isSearchMode = false;
  areFiltersOpened = false;
  filtersLabel = {
    true: 'UBZ.SITE_CONTENT.10110110',
    false: 'UBZ.SITE_CONTENT.10110111'
  };
  displayStyle = { true: 'block', false: 'none' };
  collatTecDomain: Domain[] = [];
  percentageDomain: Domain[] = [];
  familyAssetType: string;
  filteredText = '';
  filteredCollat = '';
  filteredState = '';
  saveIsEnable: boolean;
  showPreviousButton: boolean = true;
  isFrazionamento: boolean = false;
  credlineAmount: number = 0;
  draftButtonCallback = this.saveDraft.bind(this);
  isDisabled = true;

  constructor(
    private guaranteeInfoService: GuaranteeInfoService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private domainService: DomainService,
    public landingService: LandingService,
    public genericInfoService: GenericInfoService,
    private guaranteeFractionationService: GuaranteeFractionationService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) {
    // All'inizio di ogni nuovo processo di frazionamento si resettano le variabili di processo
    this.guaranteeFractionationService.reset();
  }

  ngOnInit() {
    this.activatedRoute.parent.params.subscribe((params: Params) => {
      // Se siamo in richiesta di frazionamento invochiamo il servizio corretto per il recupero delle garanzie
      if (
        this.activatedRoute.parent.url['value'] &&
        this.activatedRoute.parent.url['value'][0] &&
        this.activatedRoute.parent.url['value'][0].path ===
          'guaranteeFractionation'
      ) {
        this.setFractionationVariables(params);
      } else {
        this.positionId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        Observable.forkJoin(
          this.genericInfoService.getGenericInfo(
            this.positionId,
            this.wizardCode
          ),
          this.guaranteeInfoService.getGuaranteeList(this.positionId)
        ).subscribe(result => {
          this.landingService.originationProcess = result[0].originationProcess;
          this.landingService.checkIfCompletedTask(this.positionId, this.taskCode);
          this.credlineAmount = result[0]['credlineAmount'] ? result[0]['credlineAmount'] : 0;
          return this.setGuaranteesInformations(result[1]);
        });
      }
    });
  }

  /**
   * @name setFractionationVariables
   * @param {Params} params Parametri della rotta
   * @description Imposta le variabili per il processo di frazionemento
   */
  setFractionationVariables(params) {
    setTimeout(() => (this.guaranteeFractionationService.wizardStep = 1), 10);
    this.landingService.originationProcess = 'FRG';
    this.isFrazionamento = true;
    this.landingService.positionLocked = true;
    this.showPreviousButton = false;
    this.guaranteeFractionationService.ndg = params['ndg'];
    this.guaranteeFractionationService.familyAsset = params['familyAsset'];
    this.guaranteeFractionationService.setCurrentStep('UBZ-FRG-ING');
    this.guaranteeFractionationService.getFraWizardData().subscribe(() => {
      this.isDisabled = this.guaranteeFractionationService.isCurrentStepLocked();
      this.guaranteeInfoService
        .getGuaranteeListFrg(params['ndg'], params['familyAsset'])
        .subscribe(response => {
          if (response) {
            // In frazionamento si abilitano i pulsanti e blocca la sezione
            return this.setGuaranteesInformations(response);
          }
        });
    });
  }

  // Invocato dopo aver recuperato le garanzie associate al ndg
  // Imposta le variabili, recupera domini e invoca controllo abilitazione
  // pulsanti per richieste diverse da frazionamento
  setGuaranteesInformations(response) {
    this.guarantees = response.listColl;
    this.filteredGuarantees = response.listColl;
    this.setDefaultSelectValue();
    this.familyAssetType = response.familyAssetType;
    // SE non siamo in richiesta di frazionamento si esegue il metodo
    // che controlla l'abilitazione dei pulsanti
    if (this.isFrazionamento) {
      this.changeAllCheckedStatus();
    } else {
      this.calculateSaveIsEnable();
    }
    Observable.forkJoin(
      this.domainService.newGetDomain(
        'UBZ_DOM_COLLAT_TEC_FORM',
        this.familyAssetType
      ),
      this.domainService.newGetDomain('UBZ_DOM_POOL_TYPE')
    ).subscribe(domResponse => {
      this.collatTecDomain = domResponse[0];
      this.percentageDomain = domResponse[1];
    });
  }

  private setDefaultSelectValue() {
    if (!this.isFrazionamento) {
      for (const el of this.filteredGuarantees) {
        el.percType = 'NES';
      }
    }
  }

  public calculateSaveIsEnable() {    
    if (this.isFrazionamento && this.isDisabled) {
      this.saveIsEnable = false;
      return;
    }
    let isBreak = false;
    let index1 = 0;
    this.saveIsEnable = false;
    for (const el of this.guarantees) {
      if (el.alreadyAssigned === true) {
        let index2 = 0;
        for (const row of this.filteredGuarantees) {
          if (index1 === index2) {
            if (row.percType === 'NES' || row.poolPerc) {
              this.saveIsEnable = true;
            } else {
              this.saveIsEnable = false;
              isBreak = true;
              break;
            }
          }
          index2++;
        }
        if (isBreak) {
          break;
        }
        this.saveIsEnable = true;
      }
      index1++;
    }
    //Check: se antergate è selezionato l'importo deve essere valorizzato
    for (const el of this.filteredGuarantees) {
      if (el['antergateFlag'] === true) {
        if (Number(el['antergateValue']) <= 0) {
          this.saveIsEnable = false;
        }
      }
    }
    if (!this.isFrazionamento && this.landingService.originationProcess !== 'TGP') {
      this.saveIsEnable =
        this.saveIsEnable && this.credlineAmount > 0;
    }
  }

  changeCheckedStatus(index: number) {
    this.guarantees[index].alreadyAssigned = !this.guarantees[index]
      .alreadyAssigned;
    this.makeSum();
    this.calculateSaveIsEnable();
  }

  changeAntergateCheckedStatus(index: number) {
    this.calculateSaveIsEnable();
  }

  changeAllCheckedStatus() {
    this.selectAll = !this.selectAll;
    for (const ind in this.guarantees) {
      if (true) {
        this.guarantees[ind].alreadyAssigned = this.selectAll;
      }
    }
    this.makeSum();
    this.calculateSaveIsEnable();
  }

  changeSearchMode() {
    this.isSearchMode = !this.isSearchMode;
    if (!this.isSearchMode) {
      this.filteredGuarantees = this.guarantees;
    }
  }

  setFilteredText(text: string) {
    this.filteredText = text;
  }

  filter() {
    this.filteredGuarantees = [];
    for (const index in this.guarantees) {
      if (
        (this.guarantees[index].progCollateral.includes(this.filteredText) ||
          this.guarantees[index].propAssociate
            .toString()
            .includes(this.filteredText)) &&
        this.guarantees[index].collateralTecForm.includes(this.filteredCollat)
      ) {
        this.filteredGuarantees.push(this.guarantees[index]);
      }
    }
  }

  saveDraft() {
    return this.saveFrazData().switchMap((response: any) => {
      return of(true);
    });
  }

  draftSaved() {
    this.router.navigateByUrl('index');
  }

  saveFrazData() {
    const guaranteesToSave = [];
    this.filteredGuarantees.forEach(element => {
      let newGuarantee = {
        ndg: element.ndgCollateral,
        type: this.guaranteeFractionationService.familyAsset,
        oldCredlineProg: element.oldCredlineProg,
        oldProgCollateral: element.oldProgCollateral,
        newCredlineProg: element.newCredlineProg,
        newProgCollateral: element.progCollateral,
        poolType: element.percType,
        poolPerc: +element.poolPerc
      };
      guaranteesToSave.push(newGuarantee);
    });
    return this.guaranteeFractionationService
      .frazSaveCollateralInfo(guaranteesToSave).switchMap(() => {
        return of(true);
      });
  }

  goToNextPage() {
    // Se siamo in richiesta frazionamento
    if (this.isFrazionamento) {
      this.saveFrazData().subscribe(() => {
        this.guaranteeFractionationService.wizardSetNextStep().subscribe(() => {
          this.guaranteeFractionationService.goToNextStep();
        });  
      });
      return;
    }
    const responseGuarantee: Guarantee[] = [];
    for (const index in this.filteredGuarantees) {
      if (this.filteredGuarantees[index].alreadyAssigned === true) {
        responseGuarantee.push(this.filteredGuarantees[index]);
      }
    }
    this.guaranteeInfoService.importGuarantee(this.positionId, responseGuarantee, this.credlineAmount)
    .subscribe(result => {
      this.landingService.goNextPage(
        this.positionId,
        'UBZ-REQ-GAR',
        this.wizardCode,
        this.activatedRoute
      );
    });
  }
  
  modify() {
    this.guaranteeFractionationService.wizardInvalidStep().subscribe(() => {      
      this.guaranteeFractionationService.getFraWizardData().subscribe(() => {   
        this.isDisabled = this.guaranteeFractionationService.isCurrentStepLocked();
        this.calculateSaveIsEnable();
      });
    });
  }

  previous() {
    this.landingService.goToPreviousTask(
      this.positionId,
      this.wizardCode,
      this.activatedRoute
    );
  }

  cancelPosition() {
    // Se siamo in richiesta frazionamento
    if (this.isFrazionamento) {
      return this.guaranteeFractionationService.cancelFractionation();
    }
    this.landingService
      .cancelPosition(this.positionId, this.wizardCode)
      .subscribe(res => this.router.navigate(['/']));
  }

  makeSum(){
    this.credlineAmount = 0;
    for ( const g of this.guarantees){
      if(g.alreadyAssigned){
        this.credlineAmount += g.collateralAmmountSum? g.collateralAmmountSum : 0;
      }
    }
  }

}
