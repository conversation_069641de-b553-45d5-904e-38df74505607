<div class="modal-dialog modal-lg" role="document">
  <div class="modal-content">
    <div class="modal-header">
      <ng-container>
        <h1><i class="icon-print_checklist"></i>{{ 'UBZ.SITE_CONTENT.11101110101' | translate }}</h1>
        <button type="reset" class="close pull-right" aria-label="Close" (click)="hide()">
          <i class="icon-close"></i>
        </button>
      </ng-container>
    </div>
    <div class="modal-body">
      <div><div class="box">
          <h4>{{ 'UBZ.SITE_CONTENT.11101110111' | translate }}</h4>
          <div class="row">
            <div class="col-sm-6">
              <label>{{ 'UBZ.SITE_CONTENT.11101101100' | translate}}</label>
            </div>
            <div class="col-sm-6">
              {{ ameori.newAppraisalId }}
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6">
              <label>{{ 'UBZ.SITE_CONTENT.***********' | translate }}</label>
            </div>
            <div class="col-sm-6">
              {{ ameori.originProcess }}
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6">
              <label>{{ 'UBZ.SITE_CONTENT.***********' | translate }}</label>
            </div>
            <div class="col-sm-6">
              {{ ameori.accountType }}
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6">
              <label>{{ 'UBZ.SITE_CONTENT.***********' | translate }}</label>
            </div>
            <div class="col-sm-6">
              {{ ameori.accountNumber }}
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6">
              <label>{{ 'UBZ.SITE_CONTENT.***********' | translate }}</label>
            </div>
            <div class="col-sm-6">
              {{ ameori.ndg }}
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6">
              <label>{{ 'UBZ.SITE_CONTENT.***********' | translate}}</label>
            </div>
            <div class="col-sm-6">
              {{ ameori.collatProg }}
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6">
              <label>{{ 'UBZ.SITE_CONTENT.***********' | translate }}</label>
            </div>
            <div class="col-sm-6">
              {{ ameori.appValue ? (ameori.appValue | currency:'EUR':true:'1.2-2') : '' }}
            </div>
          </div>
      </div>
    </div>

      <div class="col-sm-3 col-md-5 form-group">
        <label>{{ 'UBZ.SITE_CONTENT.11101111000' | translate }}</label>
        <app-importo name="appValue" [(ngModel)]="newAmount" (ngModelChange)="evaluateIsEnableToConfirm()"></app-importo>
      </div>
      <label></label>
      <button class="col-md-3" [disabled]="!isEnableToConfirm" (click)="confirm()" type="submit" class="btn btn-primary">{{ 'UBZ.SITE_CONTENT.11101111001' | translate }}</button>

  </div>
</div>