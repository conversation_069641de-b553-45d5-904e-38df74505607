<form #f="ngForm" novalidate>
<div class="row form-group" *ngIf="!(isMobile || isIndustriale)">
<div class="col-sm-12">
  <div class="table-fixed-wrap">
    <fieldset [disabled]="_landingService.isLockedTask[currentTask] || haveDisabledFields || !_landingService.positionLocked">
    <table class="table-fixed">
      <thead>
        <tr>
          <th rowspan="2" class="fixed"></th>
          <th colspan="4"><strong>{{'UBZ.SITE_CONTENT.1010010110' | translate }}</strong></th>
          <th></th>
          <th colspan="4"><strong>{{'UBZ.SITE_CONTENT.1010010111' | translate }}</strong></th>
          <th></th>
          <th colspan="4"><strong>{{'UBZ.SITE_CONTENT.1001110000' | translate }}</strong></th>
          <th></th>
          <th colspan="4"><strong>{{'UBZ.SITE_CONTENT.1010011000' | translate }}</strong></th>
          <th></th>
            <th colspan="4"><strong>{{'UBZ.SITE_CONTENT.1001110010' | translate }}</strong></th>
          <th></th>
          <th colspan="4"><strong>{{'UBZ.SITE_CONTENT.1010011001' | translate }}</strong></th>
          <th></th>
          <th colspan="4"><strong>{{'UBZ.SITE_CONTENT.1001110011' | translate }}</strong></th>
        </tr>
        <tr>
          <td class="fixed fixed-two-rows"><strong>{{'UBZ.SITE_CONTENT.1001101111' | translate }}</strong></td>
          <td>{{ differences?.socPerFirst }} (1°)</td>
          <td>{{ differences?.socPerSecond }} (2°)</td>
          <td colspan="2">{{'UBZ.SITE_CONTENT.1001110110' | translate }}</td>
          <td></td>
          <td>{{ differences?.socPerFirst }} (1°)</td>
          <td>{{ differences?.socPerSecond }} (2°)</td>
          <td colspan="2">{{'UBZ.SITE_CONTENT.1001110110' | translate }}</td>
          <td></td>
          <td>{{ differences?.socPerFirst }} (1°)</td>
          <td>{{ differences?.socPerSecond }} (2°)</td>
          <td colspan="2">{{'UBZ.SITE_CONTENT.1001110110' | translate }}</td>
          <td></td>
          <td>{{ differences?.socPerFirst }} (1°)</td>
          <td>{{ differences?.socPerSecond }} (2°)</td>
          <td colspan="2">{{'UBZ.SITE_CONTENT.1001110110' | translate }}</td>
          <td></td>
          <td>{{ differences?.socPerFirst }} (1°)</td>
          <td>{{ differences?.socPerSecond }} (2°)</td>
          <td colspan="2">{{'UBZ.SITE_CONTENT.1001110110' | translate }}</td>
          <td></td>
          <td>{{ differences?.socPerFirst }} (1°)</td>
          <td>{{ differences?.socPerSecond }} (2°)</td>
          <td colspan="2">{{'UBZ.SITE_CONTENT.1001110110' | translate }}</td>
          <td></td>
          <td>{{ differences?.socPerFirst }} (1°)</td>
          <td>{{ differences?.socPerSecond }} (2°)</td>
          <td colspan="2">{{'UBZ.SITE_CONTENT.1001110110' | translate }}</td>
        </tr>
      </thead>
      <tbody *ngIf="differences && differences.mapListObject">
        <ng-container *ngFor="let item of (differences?.mapListObject | objectMapToObjectArray); let index = index">
          <tr>
            <td class="fixed" style="padding-left: 12px !important">
              <i role="button" class="fa fa-trash-o" aria-hidden="true" style="position: static" (click)="openModal(item.id, 'l\'asset selezionato')"></i>
              {{ item.objectCategory }}
              <i class="fa fa-ellipsis-h" [tooltip]="getObjectInfo(item._name_)" placement="right"></i>
            </td>
            <td>{{ grossSurfaceFirst[item._name_] | number:'1.2-2' }}</td>
            <td>{{ grossSurfaceSecond[item._name_] | number:'1.2-2' }}</td>
            <td colspan="2" style="box-sizing: content-box">{{ grossSurfaceThird[item._name_] | number:'1.2-2' }}</td>
            <td></td>
            <td>{{ commCoeffFirst[item._name_] | number:'1.2-2' }}</td>
            <td>{{ commCoeffSecond[item._name_] | number:'1.2-2' }}</td>
            <td colspan="2" style="box-sizing: content-box">{{ commCoeffThird[item._name_] | number:'1.2-2' }}</td>
            <td></td>
            <td>{{ commSurfFirst[item._name_] | number:'1.2-2' }}</td>
            <td>{{ commSurfSecond[item._name_] | number:'1.2-2' }}</td>
            <td colspan="2" style="box-sizing: content-box">{{ commSurfThird[item._name_] | number:'1.2-2' }}</td>
            <td></td>
            <td>{{ mqValueFirst[item._name_] | number:'1.2-2' }}</td>
            <td>{{ mqValueSecond[item._name_] | number:'1.2-2' }}</td>
            <td colspan="2" style="box-sizing: content-box">{{ mqValueThird[item._name_] | number:'1.2-2' }}</td>
            <td></td>
            <td>{{ commValueFirst[item._name_] | number:'1.2-2' }}</td>
            <td>{{ commValueSecond[item._name_] | number:'1.2-2' }}</td>
            <td colspan="2" style="box-sizing: content-box">{{ commValueThird[item._name_] | number:'1.2-2' }}</td>
            <td></td>
            <td>{{ pledgedMqValueFirst[item._name_] | number:'1.2-2' }}</td>
            <td>{{ pledgedMqValueSecond[item._name_] | number:'1.2-2' }}</td>
            <td colspan="2" style="box-sizing: content-box">{{ pledgedMqValueThird[item._name_] | number:'1.2-2' }}</td>
            <td></td>
            <td>{{ pledgedValueFirst[item._name_] | number:'1.2-2' }}</td>
            <td>{{ pledgedValueSecond[item._name_] | number:'1.2-2' }}</td>
            <td colspan="2" style="box-sizing: content-box">{{ pledgedValueThird[item._name_] | number:'1.2-2' }}</td>
          </tr>
          <tr *ngFor="let row of item.mapDiffOpinion; let index2 = index;">
            <td class="fixed sub-category">
              <i role="button" class="fa fa-trash-o" aria-hidden="true" style="position: static" (click)="openModal(item.id, 'la destinazione d\'uso selezionata', row.structureType, row.ruleId)"></i>
              {{ structureTypeDom[row.structureType]?.translationCod | translate }}
            </td>
            <td><a role="button" (click)="selectProperty(PropertyType.GROSS_SURFACE, OpinionType.FIRST_OPINION, row, item._name_)">{{ row.grossSurfaceFirst }}</a></td>
            <td><a role="button" (click)="selectProperty(PropertyType.GROSS_SURFACE, OpinionType.SECOND_OPINION, row, item._name_)">{{ row.grossSurfaceSecond }}</a></td>
            <td colspan="2">
              <input appOnlyNumbers type="text" class="form-control" [(ngModel)]="row.grossSurfaceThird" name="grossSurfaceThird-{{index2}}-{{index}}" (ngModelChange)="calculateCommSurface(row, item._name_)">
            </td>
            <td></td>
            <td><a role="button" (click)="selectProperty(PropertyType.COMM_COEFFICIENT, OpinionType.FIRST_OPINION, row, item._name_)">{{ row.commCoefficientFirst }}</a></td>
            <td><a role="button" (click)="selectProperty(PropertyType.COMM_COEFFICIENT, OpinionType.SECOND_OPINION, row, item._name_)">{{ row.commCoefficientSecond }}</a></td>
            <td colspan="2">
              <input appOnlyNumbers type="text" class="form-control" [(ngModel)]="row.commCoefficientThird" name="commCoefficientThird-{{index2}}-{{index}}" (ngModelChange)="calculateCommSurface(row, item._name_)">
            </td>
            <td></td>
            <td><a role="button" (click)="selectProperty(PropertyType.COMM_SURFACE, OpinionType.FIRST_OPINION, row, item._name_)">{{ row.commSurfaceFirst }}</a></td>
            <td><a role="button" (click)="selectProperty(PropertyType.COMM_SURFACE, OpinionType.SECOND_OPINION, row, item._name_)">{{ row.commSurfaceSecond }}</a></td>
            <td colspan="2">
              <input appOnlyNumbers type="text" class="form-control" [(ngModel)]="row.commSurfaceThird" name="commSurfaceThird-{{index2}}-{{index}}" (ngModelChange)="calculateBookValue(row, item._name_)">
            </td>
            <td></td>
            <td><a role="button" (click)="selectProperty(PropertyType.MQ_VALUE, OpinionType.FIRST_OPINION, row, item._name_)">{{ row.mqValueFirst }}</a></td>
            <td><a role="button" (click)="selectProperty(PropertyType.MQ_VALUE, OpinionType.SECOND_OPINION, row, item._name_)">{{ row.mqValueSecond }}</a></td>
            <td colspan="2">
              <input appOnlyNumbers type="text" class="form-control" [(ngModel)]="row.mqValueThird" name="mqValueThird-{{index2}}-{{index}}" (ngModelChange)="calculateBookValue(row, item._name_)">
            </td>
            <td></td>
            <td>{{ row.bookValueFirst | number:'1.2-2' }}</td>
            <td>{{ row.bookValueSecond | number:'1.2-2' }}</td>
            <td colspan="2">{{ row.bookValueThird | number:'1.2-2' }}</td>
            <td></td>
            <td><a role="button" (click)="selectProperty(PropertyType.PLEDGED_MQ_VALUE, OpinionType.FIRST_OPINION, row, item._name_)">{{ row.pledgedMqValueFirst }}</a></td>
            <td><a role="button" (click)="selectProperty(PropertyType.PLEDGED_MQ_VALUE, OpinionType.SECOND_OPINION, row, item._name_)">{{ row.pledgedMqValueSecond }}</a></td>
            <td colspan="2">
              <input appOnlyNumbers type="text" class="form-control" [(ngModel)]="row.pledgedMqValueThird" name="pledgedMqValueThird-{{index2}}-{{index}}" (ngModelChange)="calculatePledgedValue(row, item._name_)">
            </td>
            <td></td>
            <td>{{ row.pledgedValueFirst | number:'1.2-2' }}</td>
            <td>{{ row.pledgedValueSecond | number:'1.2-2' }}</td>
            <td colspan="2">{{ row.pledgedValueThird | number:'1.2-2' }}</td>
          </tr>
        </ng-container>
      </tbody>
    </table>
    </fieldset>
  </div>
</div>
</div>
<fieldset [disabled]="_landingService.isLockedTask[currentTask] || haveDisabledFields || !_landingService.positionLocked">
<div class="row form-group">
  <table class="uc-table">
    <thead>
      <tr>
        <th scope="col" class="col-sm-4" rowspan="2"></th>
        <th scope="col" class="col-sm-4 text-center" colspan="3">{{'UBZ.SITE_CONTENT.1001110001' | translate }}</th>
      </tr>
      <tr>
        <th scope="col" class="text-center">{{ differences?.socPerFirst }}</th>
        <th scope="col" class="text-center">{{ differences?.socPerSecond }}</th>
        <th scope="col" class="text-center">{{'UBZ.SITE_CONTENT.1001110110' | translate }}*</th>
      </tr>
    </thead>
    <tbody *ngIf="differences && differences.totVAlAss && differences.totVAlReal">
      <ng-container *ngIf="isIndustriale">
        <tr>
          <td>{{'UBZ.SITE_CONTENT.1001110111' | translate }}</td>
          <td><a role="button" (click)="differences.totPrudentialValue.thirdOpinionValue = differences.totPrudentialValue.firstOpinionValue">{{ differences.totPrudentialValue.firstOpinionValue }}</a></td>
          <td><a role="button" (click)="differences.totPrudentialValue.thirdOpinionValue = differences.totPrudentialValue.secondOpinionValue">{{ differences.totPrudentialValue.secondOpinionValue }}</a></td>
          <td><input required appOnlyNumbers type="text" name="totPrudentialValue" [(ngModel)]="differences.totPrudentialValue.thirdOpinionValue" class="form-control"></td>
        </tr>
        <tr>
          <td>{{'UBZ.SITE_CONTENT.111110010' | translate }}</td>
          <td><a role="button" (click)="differences.totVAlReal.thirdOpinionValue = differences.totVAlReal.firstOpinionValue">{{ differences.totVAlReal.firstOpinionValue }}</a></td>
          <td><a role="button" (click)="differences.totVAlReal.thirdOpinionValue = differences.totVAlReal.secondOpinionValue">{{ differences.totVAlReal.secondOpinionValue }}</a></td>
          <td><input required appOnlyNumbers type="text" name="totVAlReal" [(ngModel)]="differences.totVAlReal.thirdOpinionValue" class="form-control"></td>
        </tr>
        <tr>
          <td>{{'UBZ.SITE_CONTENT.1001010010' | translate }}</td>
          <td><a role="button" (click)="differences.totVAlAss.thirdOpinionValue = differences.totVAlAss.firstOpinionValue">{{ differences.totVAlAss.firstOpinionValue }}</a></td>
          <td><a role="button" (click)="differences.totVAlAss.thirdOpinionValue = differences.totVAlAss.secondOpinionValue">{{ differences.totVAlAss.secondOpinionValue }}</a></td>
          <td><input required appOnlyNumbers type="text" name="totVAlAss" [(ngModel)]="differences.totVAlAss.thirdOpinionValue" class="form-control"></td>
        </tr>
      </ng-container>
      <ng-container *ngIf="!(isIndustriale || isMobile)">
      <tr>
        <td>{{'UBZ.SITE_CONTENT.1001111000' | translate }}</td>
        <td><a role="button" (click)="differences.totVAlAss.thirdOpinionValue = differences.totVAlAss.firstOpinionValue">{{ differences.totVAlAss.firstOpinionValue }}</a></td>
        <td><a role="button" (click)="differences.totVAlAss.thirdOpinionValue = differences.totVAlAss.secondOpinionValue">{{ differences.totVAlAss.secondOpinionValue }}</a></td>
        <td><input required appOnlyNumbers type="text" name="totVAlAss" [(ngModel)]="differences.totVAlAss.thirdOpinionValue" class="form-control"></td>
      </tr>
      <tr>
        <td>{{'UBZ.SITE_CONTENT.111110010' | translate }}</td>
        <td><a role="button" (click)="differences.totVAlReal.thirdOpinionValue = differences.totVAlReal.firstOpinionValue">{{ differences.totVAlReal.firstOpinionValue }}</a></td>
        <td><a role="button" (click)="differences.totVAlReal.thirdOpinionValue = differences.totVAlReal.secondOpinionValue">{{ differences.totVAlReal.secondOpinionValue }}</a></td>
        <td><input required appOnlyNumbers type="text" name="totVAlReal" [(ngModel)]="differences.totVAlReal.thirdOpinionValue" class="form-control"></td>
      </tr>
      </ng-container>
      <tr *ngIf="isAgrario">
        <td>{{'UBZ.SITE_CONTENT.111110011' | translate }}</td>
        <td><a role="button" (click)="differences.totVAlReal.thirdOpinionValue = differences.totVAlReal.firstOpinionValue">{{ differences.totVAlReal.firstOpinionValue }}</a></td>
        <td><a role="button" (click)="differences.totVAlReal.thirdOpinionValue = differences.totVAlReal.secondOpinionValue">{{ differences.totVAlReal.secondOpinionValue }}</a></td>
        <td><input required appOnlyNumbers type="text" name="totVAlReal" [(ngModel)]="differences.totVAlReal.thirdOpinionValue" class="form-control"></td>
      </tr>
      <ng-container *ngIf="isMobile">
        <tr>
          <td>{{'UBZ.SITE_CONTENT.1001111001' | translate }}</td>
          <td><a role="button" (click)="differences.totPledgedValue.thirdOpinionValue = differences.totPledgedValue.firstOpinionValue">{{ differences.totPledgedValue.firstOpinionValue }}</a></td>
          <td><a role="button" (click)="differences.totPledgedValue.thirdOpinionValue = differences.totPledgedValue.secondOpinionValue">{{ differences.totPledgedValue.secondOpinionValue }}</a></td>
          <td><input required appOnlyNumbers type="text" name="totPledgedValue" [(ngModel)]="differences.totPledgedValue.thirdOpinionValue" class="form-control"></td>

        </tr>
        <tr>
          <td>{{'UBZ.SITE_CONTENT.1001111010' | translate }}</td>
          <td><a role="button" (click)="differences.totBookValue.thirdOpinionValue = differences.totBookValue.firstOpinionValue">{{ differences.totBookValue.firstOpinionValue }}</a></td>
          <td><a role="button" (click)="differences.totBookValue.thirdOpinionValue = differences.totBookValue.secondOpinionValue">{{ differences.totBookValue.secondOpinionValue }}</a></td>
          <td><input required appOnlyNumbers type="text" name="totBookValue" [(ngModel)]="differences.totBookValue.thirdOpinionValue" class="form-control"></td>
        </tr>
        <tr>
          <td>{{'UBZ.SITE_CONTENT.1001111011' | translate }}</td>
          <td><a role="button" (click)="differences.totMarketValue.thirdOpinionValue = differences.totMarketValue.firstOpinionValue">{{ differences.totMarketValue.firstOpinionValue }}</a></td>
          <td><a role="button" (click)="differences.totMarketValue.thirdOpinionValue = differences.totMarketValue.secondOpinionValue">{{ differences.totMarketValue.secondOpinionValue }}</a></td>
          <td><input required appOnlyNumbers type="text" name="totMarketValue" [(ngModel)]="differences.totMarketValue.thirdOpinionValue" class="form-control"></td>
        </tr>
        <tr>
          <td>{{'UBZ.SITE_CONTENT.1001111100' | translate }}</td>
          <td><a role="button" (click)="differences.totVAlAss.thirdOpinionValue = differences.totVAlAss.firstOpinionValue">{{ differences.totVAlAss.firstOpinionValue }}</a></td>
          <td><a role="button" (click)="differences.totVAlAss.thirdOpinionValue = differences.totVAlAss.secondOpinionValue">{{ differences.totVAlAss.secondOpinionValue }}</a></td>
          <td><input required appOnlyNumbers type="text" name="totVAlAss" [(ngModel)]="differences.totVAlAss.thirdOpinionValue" class="form-control"></td>
        </tr>
      </ng-container>
    </tbody>
  </table>
</div>
<div class="row form-group" *ngIf="differences">
  <div class="col-sm-6">                                                      
    <label>
      <span><i class="icon-search note-tooltip" [tooltip]="differences.notes" triggers="click"></i></span>
      {{'UBZ.SITE_CONTENT.1001111101' | translate }}*
    </label>
    <textarea required name="comments" class="form-control" [(ngModel)]="differences.notes" [disabled]="_landingService.isLockedTask[currentTask] || haveDisabledFields || !_landingService.positionLocked"></textarea>
  </div>
  <div class="col-sm-6">                                                          
    <label>
      <span><i class="icon-search note-tooltip" [tooltip]="differences.conclusion" triggers="click"></i></span>
      {{'UBZ.SITE_CONTENT.1001111110' | translate }}*
    </label>
    <textarea required name="conclusions" class="form-control" [(ngModel)]="differences.conclusion" [disabled]="_landingService.isLockedTask[currentTask] || haveDisabledFields || !_landingService.positionLocked"></textarea>
  </div>
</div>
</fieldset>
</form>

<fieldset [disabled]="!_landingService.positionLocked">
  <app-navigation-footer showSaveDraft="true" showPrevious="true" confirmButtonString="{{'UBZ.SITE_CONTENT.110001110' | translate }}"
  [showCancelButton]="false" (saveButtonClick)="save()" (previousButtonClick)="goToPreviousTask()" (closeDraftButtonClick)="goToGenericTask()"
  [saveIsEnable]="f && f.valid" [saveDraftCallback]="saveDraftCallback"
  [activeTaskCode]="currentTask">
</app-navigation-footer>
</fieldset>

<div *ngIf="modalIsOpen" class="modal fade" id="nuovo-asset" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #modal="bs-modal" (onHidden)="closeModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.1100010' | translate }}</h2>
        <button type="button" role="button" class="close pull-right" aria-label="Close" (click)="closeModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <p>{{'UBZ.SITE_CONTENT.1111110101' | translate }} {{labelObjToDelete}}.</p>
          <p>{{'UBZ.SITE_CONTENT.1111110110' | translate }}?</p>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" (click)="confirmDelete()">
          {{'UBZ.SITE_CONTENT.110101000' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>
