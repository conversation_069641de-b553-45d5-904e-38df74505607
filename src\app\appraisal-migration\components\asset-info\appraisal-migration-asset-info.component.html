<div class="row row-eq-height" *ngIf="data">

  <ng-container *ngIf="resourceItemType && resourceItemType === 'IMM'">



    <div class="col-sm-12" *ngIf="data.asset">
      <div class="box">
        <h2>{{'UBZ.SITE_CONTENT.10000101111' | translate }}</h2>
        <div class="row multipleRow">
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.110001' | translate }}</label>
            {{data.asset.reRegistrySheet}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.101011' | translate }}</label>
            {{data.asset.address}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.101100' | translate }}</label>
            {{data.asset.streetNum}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.101001' | translate }}</label>
            {{data.asset.city}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.101010' | translate }}</label>
            {{data.asset.zipCode}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.101000' | translate }}</label>
            {{data.asset.province}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000110000' | translate }}</label>
            {{data.asset.reRegistryPart}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.110011' | translate }}</label>
            {{data.asset.reRegistrySub}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000110001' | translate }}</label>
            {{data.asset.tableRegistryPart}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000110010' | translate }}</label>
            {{data.asset.tableRegistryBody}}
          </div>
        </div>
      </div>
    </div>

    <div class="col-sm-12" *ngIf="data.asset && data.asset.objReRegistryList">
      <div class="box">
        <h2>{{'UBZ.SITE_CONTENT.10000110011' | translate }}</h2>
        <div class="row multipleRow" *ngFor="let row of data.asset.objReRegistryList">
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10010011' | translate }}</label>
            {{ registryDom[row.reRegistryType] ? (registryDom[row.reRegistryType].translationCod | translate ) : '' }}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000110100' | translate }}</label>
            {{row.urbanSection}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.101001' | translate }}</label>
            {{row.reRegistryCity}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.100110' | translate }}</label>
            {{ row.reRegistryCateg ? ( categoryDom[row.reRegistryCateg].translationCod | translate ) : '' }}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000110101' | translate }}</label>
            {{row.portion}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000110110' | translate }}</label>
            {{row.registryClass}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000110111' | translate }}</label>
            {{row.surface}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.111001011' | translate }}</label>
            {{ measurementDom[row.measurement] ? (measurementDom[row.measurement].translationCod | translate ) : '' }}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000111000' | translate }}</label>
            {{row.landParcel}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000111001' | translate }}</label>
            {{row.reRegistryIncome}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000111010' | translate }}</label>
            {{row.roomNumSurf}}
          </div>
        </div>
      </div>
    </div>

    <div class="col-sm-12"
      *ngIf="data.asset && data.asset.resourceItemInfo && data.asset.resourceItemInfo.realEstateDesc">
      <div class="box">
        <h2>{{'UBZ.SITE_CONTENT.10010110' | translate }}</h2>
        <div class="row firstRow">
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000111011' | translate }}</label>
            {{ preservDom[data.asset.resourceItemInfo.realEstateDesc.preservationStatus] ?
            (preservDom[data.asset.resourceItemInfo.realEstateDesc.preservationStatus].translationCod | translate) :
            ''}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000111100' | translate }}</label>
            {{ subtypeDom[data.asset.resourceItemInfo.realEstateDesc.statusSubtype] ?
            (subtypeDom[data.asset.resourceItemInfo.realEstateDesc.statusSubtype].translationCod | translate) : '' }}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.100110' | translate }}</label>
            {{ categoryDom[data.asset.resourceItemInfo.realEstateDesc.realReCategory] ?
            (categoryDom[data.asset.resourceItemInfo.realEstateDesc.realReCategory].tranlsationCod | translate ) : '' }}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000111101' | translate }}</label>
            {{ purposeDom[data.asset.resourceItemInfo.realEstateDesc.purpose] ?
            (purposeDom[data.asset.resourceItemInfo.realEstateDesc.purpose].translationCod | translate) : ''}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000111110' | translate }}</label>
            {{ locativeDom[data.asset.resourceItemInfo.realEstateDesc.locativeStatus] ?
            (locativeDom[data.asset.resourceItemInfo.realEstateDesc.locativeStatus].translationCod | translate) : '' }}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000111111' | translate }}</label>
            {{ projectDom[data.asset.resourceItemInfo.realEstateDesc.projectCorr] ?
            (projectDom[data.asset.resourceItemInfo.realEstateDesc.projectCorr].translationCod | translate) : '' }}
          </div>
        </div>
        <div class="row secondRow">
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10001000000' | translate }}</label>
            {{data.asset.resourceItemInfo.realEstateDesc.changesDescNoCorr}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10001000001' | translate }}</label>
            {{ corrDom[data.asset.resourceItemInfo.realEstateDesc.propReregCorr] ?
            (corrDom[data.asset.resourceItemInfo.realEstateDesc.propReregCorr].translationCod | translate) : '' }}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10001000010' | translate }}</label>
            {{ zoneDom[data.asset.resourceItemInfo.realEstateDesc.zoneType] ?
            (zoneDom[data.asset.resourceItemInfo.realEstateDesc.zoneType].translationCod | translate) : '' }}
          </div>
        </div>
      </div>
    </div>


    <div class="col-sm-12"
      *ngIf="data.objIns && ( data.objIns.insuranceType || data.objIns.insuranceCod || data.objIns.insuranceAmount)">
      <div class="box">
        <h2>{{'UBZ.SITE_CONTENT.10001000011' | translate }}</h2>
        <div class="row">
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10001000100' | translate }}</label>
            {{data.objIns.insuranceType}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10001000101' | translate }}</label>
            {{data.objIns.insuranceCod}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10001000110' | translate }}</label>
            {{data.objIns.insuranceAmount | currency:'EUR':true:'1.2-2'}}
          </div>
        </div>
      </div>
    </div>


    <div class="col-sm-12" *ngIf="data.asset && data.asset.objSanctions">
      <div class="box">
        <h2>{{'UBZ.SITE_CONTENT.10001000111' | translate }}</h2>
        <div class="row multipleRow" *ngFor="let row of data.asset.objSanctions">
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10001001000' | translate }}</label>
            {{row.sanctionId}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10001001001' | translate }}</label>
            {{row.sanctionFlag}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10001001010' | translate }}</label>
            {{ sanctionDom[row.sanctionType] ? ( sanctionDom[row.sanctionType].translationCod | translate ) : '' }}
          </div>
        </div>
      </div>
    </div>

    <div class="col-sm-12"
      *ngIf="data.asset && data.asset.objBuildProvision && (data.asset.objBuildProvision.length !== 0)">
      <div class="box">
        <h2>{{'UBZ.SITE_CONTENT.111100110' | translate }}</h2>
        <div class="row multipleRow" *ngFor="let row of data.asset.objBuildProvision">
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10001001011' | translate }}</label>
            {{row.provisionId}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10001001100' | translate }}</label>
            {{ provisionDom[row.provisionTitle] ? (provisionDom[row.provisionTitle].translationCod | translate) : '' }}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.111101000' | translate }}</label>
            {{row.concPermissionNotif}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.111101001' | translate }}</label>
            {{row.buildPermitNotif}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10001001101' | translate }}</label>
            {{row.provisionNum}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10001001110' | translate }}</label>
            {{row.provisionDate}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10001001111' | translate }}</label>
            {{row.provisionProtocol}}
          </div>
        </div>
      </div>
    </div>

    <div class="col-sm-12" *ngIf="data.asset && data.asset.objUnitEval">
      <div class="box">
        <h2>{{'UBZ.SITE_CONTENT.10001010000' | translate }}</h2>
        <div class="row">
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.1001111010' | translate }}</label>
            {{data.asset.objUnitEval.totBookValue | currency:'EUR':true:'1.2-2'}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.1001111001' | translate }}</label>
            {{data.asset.objUnitEval.totPledgedValue | currency:'EUR':true:'1.2-2'}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10001010001' | translate }}</label>
            {{data.asset.objUnitEval.insuranceValue | currency:'EUR':true:'1.2-2'}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000011100' | translate }}</label>
            {{data.asset.objUnitEval.marketValue | currency:'EUR':true:'1.2-2'}}
          </div>
          <div class="col-sm-2">
            <label>Superificie totale</label>
            {{data.asset.objUnitEval.totCommSurface }}
          </div>
          <div class="col-sm-2">
            <label>Valore cauzionale unitario</label>
            {{data.asset.objUnitEval.bookUnitValue }}
          </div>
        </div>
      </div>
    </div>
  </ng-container>
  <ng-container *ngIf="resourceItemType && resourceItemType === 'MOB'">
    <ng-container *ngIf="resourceItemCategory === 'NAU'">
      <div class="col-sm-12" *ngIf="data.navInfo && data.navDesc">
        <div class="box">
          <h2>{{'UBZ.SITE_CONTENT.10001010010' | translate }}</h2>
          <div class="row">
            <div class="col-sm-3">
              <label>{{'UBZ.SITE_CONTENT.10001010011' | translate }}</label>
              {{data.navInfo.imoNum}}
            </div>
            <div class="col-sm-3">
              <label>{{'UBZ.SITE_CONTENT.10001010100' | translate }}</label>
              {{data.navDesc.boatName}}
            </div>
            <div class="col-sm-3">
              <label>{{'UBZ.SITE_CONTENT.10001010101' | translate }}</label>
              {{ objectDom[data.navDesc.boatType] ? (objectDom[data.navDesc.boatType].translationCod | translate) : ''}}
            </div>
            <div class="col-sm-3">
              <label>{{'UBZ.SITE_CONTENT.10001010110' | translate }}</label>
              {{ subsegmentDom[data.navDesc.subsegment] ? (subsegmentDom[data.navDesc.subsegment].translationCod |
              translate) : ''}}
            </div>
          </div>
        </div>
      </div>
      <div class="col-sm-12" *ngIf="data.asset && data.asset.objUnitEval">
        <div class="box">
          <h2>{{'UBZ.SITE_CONTENT.10001010000' | translate }}</h2>
          <div class="row">
            <div class="col-sm-3">
              <label>{{'UBZ.SITE_CONTENT.1001111010' | translate }}</label>
              {{data.asset.objUnitEval.totBookValue | currency:'EUR':true:'1.2-2'}}
            </div>
            <div class="col-sm-3">
              <label>{{'UBZ.SITE_CONTENT.1001111001' | translate }}</label>
              {{data.asset.objUnitEval.totPledgedValue | currency:'EUR':true:'1.2-2'}}
            </div>
            <div class="col-sm-3">
              <label>{{'UBZ.SITE_CONTENT.10001010001' | translate }}</label>
              {{data.asset.objUnitEval.insuranceValue | currency:'EUR':true:'1.2-2'}}
            </div>
            <div class="col-sm-3">
              <label>{{'UBZ.SITE_CONTENT.10000011100' | translate }}</label>
              {{data.asset.objUnitEval.marketValue | currency:'EUR':true:'1.2-2'}}
            </div>
          </div>
        </div>
      </div>

    </ng-container>
    <ng-container *ngIf="resourceItemCategory === 'AER'">
      <div class="col-sm-12" *ngIf="data.airInfo && data.airDesc">
        <div class="box">
          <h2>{{'UBZ.SITE_CONTENT.10001010010' | translate }}</h2>
          <div class="row">
            <div class="col-sm-4">
              <label>{{'UBZ.SITE_CONTENT.10001010111' | translate }}</label>
              {{data.airInfo.msnNum}}
            </div>
            <div class="col-sm-4">
              <label>{{'UBZ.SITE_CONTENT.10001011000' | translate }}</label>
              {{ objectDom[data.airDesc.aircraftType] ? (objectDom[data.airDesc.aircraftType].translationCod |
              translate) : ''}}
            </div>
            <div class="col-sm-4">
              <label>{{'UBZ.SITE_CONTENT.10001011001' | translate }}</label>
              {{data.airDesc.aircraftModel}}
            </div>
          </div>
        </div>
      </div>

      <div class="col-sm-12" *ngIf="data.asset && data.asset.objUnitEval">
        <div class="box">
          <h2>{{'UBZ.SITE_CONTENT.10001010000' | translate }}</h2>
          <div class="row">
            <div class="col-sm-3">
              <label>{{'UBZ.SITE_CONTENT.1001111010' | translate }}</label>
              {{data.asset.objUnitEval.totBookValue | currency:'EUR':true:'1.2-2'}}
            </div>
            <div class="col-sm-3">
              <label>{{'UBZ.SITE_CONTENT.1001111001' | translate }}</label>
              {{data.asset.objUnitEval.totPledgedValue | currency:'EUR':true:'1.2-2'}}
            </div>
            <div class="col-sm-3">
              <label>{{'UBZ.SITE_CONTENT.10001010001' | translate }}</label>
              {{data.asset.objUnitEval.insuranceValue | currency:'EUR':true:'1.2-2'}}
            </div>
            <div class="col-sm-3">
              <label>{{'UBZ.SITE_CONTENT.10000011100' | translate }}</label>
              {{data.asset.objUnitEval.marketValue | currency:'EUR':true:'1.2-2'}}
            </div>
          </div>
        </div>
      </div>
    </ng-container>
  </ng-container>


  <!-- Datti aggiuntivi -->
  <ng-container>
    <div class="col-sm-12" *ngIf="data.asset ">
      <div class="box">
        <h2>{{'UBZ.SITE_CONTENT.100000000001' | translate }}</h2>
        <div class="row ">
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.111101101' | translate }}</label>
            <!-- Piano -->
            {{data.asset.objUnitEval.floor}}
          </div>
          <div class="col-sm-2">
            <label>{{'UMF_DATA_DICTIONARY|FD_DDB_SG' | translate }}</label>
            <!-- Stato manuntentivo generale -->
           {{ maintenceStatus[data.asset.mantenanceStatus] ? (maintenceStatus[data.asset.mantenanceStatus].translationCod | translate) : '' }}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.110000' | translate }}</label>
            <!-- Anno di costruzione -->
            {{data.asset.buildYear}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.10000100100' | translate }}</label>
            <!-- Anno di ristrutturazione -->
            {{data.asset.renovationYear}}
          </div>
         <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.100000000011' | translate }}</label>
            <!-- Tipo di ristrutturazione -->
          {{ renovationType[data.asset.renovationType] ? (renovationType[data.asset.renovationType].translationCod | translate) : '' }}
          </div>
        </div>
      </div>
    </div>
 </ng-container>
<!-- Dati energetici -->
<ng-container>
<div class="col-sm-12" *ngIf="data.asset">
      <div class="box">
        <h2>{{'UBZ.SITE_CONTENT.100000000010' | translate }}</h2>
        <div class="row">
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.11111100110' | translate }}</label>
            <!-- <label>Classe energetici</label> -->
           {{energyClassDomains[data.asset.energyClass] ? (energyClassDomains[data.asset.energyClass].translationCod | translate) : '' }}
          </div>
          <div class="col-sm-2">
            <label>
              {{'UBZ.SITE_CONTENT.11111100111' | translate }}
            </label>
            <!-- <label>Assenza APE ex Lege</label> -->
           {{ apeExDomains[data.asset.apeEx] ? (apeExDomains[data.asset.apeEx].translationCod | translate) : '' }} 
            </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.11111101000' | translate }}</label>
            <!-- <label>Assenza APE altre motivazzioni</label> -->
            {{data.asset.apeNote}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.11111101001' | translate }}</label>
            <!-- <label>Emisione CO@kg/m2 anno</label> -->
            {{data.asset.co2}}
          </div>
          <div class="col-sm-2">
            <label>{{'UBZ.SITE_CONTENT.11111101010' | translate }}</label>
            <!-- <label>Anno emissione APE</label> -->
            {{data.asset.apeYear}}
          </div>
          <div class="col-sm-2">
            <label>{{"UBZ.SITE_CONTENT.11111110111" | translate}}</label>
            <!-- <label>Prestazione energetica rinovabile(kwh/m2)</label> -->
            {{data.asset.renewableEpgl}}
          </div>
        </div>
        <div class="row">
          <div class="col-sm-2">
            <label>{{"UBZ.SITE_CONTENT.11111111000" | translate}}</label>
            <!-- <label>Prestazione energetica non rinovabile(kwh/m2)</label> -->
            {{data.asset.nonRenewableEpgl}}
          </div>
        </div>
      </div>
    </div>
</ng-container>
</div>
<!-- <div class="col-sm-2">
              <label>RentFlag</label>
              {{data.navInfo.rentFlag}}
            </div> -->
<!-- <div class="col-sm-2">
              <label>RenterName</label>
              {{data.navInfo.renterName}}
            </div>
            <div class="col-sm-2">
              <label>RatingAgencyName</label>
              {{data.navInfo.ratingAgencyName}}
            </div>
            <div class="col-sm-2">
              <label>RentRating</label>
              {{data.navInfo.rentRating}}
            </div>
            <div class="col-sm-2">
              <label>RatingDate</label>
              {{data.navInfo.ratingDate}}
            </div> -->

<!--
    <div class="col-sm-12" *ngIf="data.navDesc" >
        <div class="box">
          <div class="row">
           -->
<!-- <div class="col-sm-2">
              <label>GearBoxFlag</label>
              {{data.navDesc.gearboxFlag}}
            </div>
            <div class="col-sm-2">
              <label>AdditionalNotes</label>
              {{data.navDesc.additionalNotes}}
            </div>
            <div class="col-sm-2">
              <label>PayloadMeasurement</label>
              {{ boatDom[data.navDesc.payloadMeasurement] ? (boatDom[data.navDesc.payloadMeasurement].translationCod | translate) : ''}}
            </div>
            <div class="col-sm-2">
              <label>GrossPayload</label>
              {{data.navDesc.grossPayload}}
            </div>
            <div class="col-sm-2">
              <label>BuildStartDate</label>
              {{data.navDesc.buildStartDate | date: 'dd/MM/yyyy' }}
            </div>
            <div class="col-sm-2">
              <label>BuildEndDate</label>
              {{data.navDesc.buildEndDate | date: 'dd/MM/yyyy' }}
            </div>
            <div class="col-sm-2">
              <label>BuildCountry</label>
              {{ countryDom[data.navDesc.buildCountry] ? (countryDom[data.navDesc.buildCountry].translationCod | translate) : ''}}
            </div>
            <div class="col-sm-2">
              <label>DecommissioningFlag</label>
              {{data.navDesc.decommissioningFlag}}
            </div>
            <div class="col-sm-2">
              <label>DecommissioningDate</label>
              {{data.navDesc.decommissioningDate}}
            </div>
            <div class="col-sm-2">
              <label>UseType</label>
              {{ useTypeDom[data.navDesc.useType] ? ( useTypeDom[data.navDesc.useType].translationCod | translate) : ''}}
            </div>
            <div class="col-sm-2">
              <label>RentAmount</label>
              {{data.navDesc.rentAmount}}
            </div>
            <div class="col-sm-2">
              <label>MarketRentAmount</label>
              {{data.navDesc.marketRentAmount}}
            </div>
            <div class="col-sm-2">
              <label>RentGroupName</label>
              {{data.navDesc.rentGroupName}}
            </div>
            <div class="col-sm-2">
              <label>RentStartDate</label>
              {{data.navDesc.rentStartDate | date: 'dd/MM/yyyy'}}
            </div>
            <div class="col-sm-2">
              <label>RentEndDate</label>
              {{data.navDesc.rentEndDate | date: 'dd/MM/yyyy'}}
            </div>
            <div class="col-sm-2">
              <label>CertifAgency</label>
              {{ agencyDom[data.navDesc.certifAgency] ? (agencyDom[data.navDesc.certifAgency].translationCod | translate) : ''}}
            </div>
            <div class="col-sm-2">
              <label>CertifClass</label>
              {{ classDom[data.navDesc.certifClass] ? (classDom[data.navDesc.certifClass].translationCod | translate) : ''}}
            </div>
            <div class="col-sm-2">
              <label>CertifEndValidDate</label>
              {{data.navDesc.certifEndValidDate | date: 'dd/MM/yyyy'}}
            </div> -->
<!-- </div>
          </div>
        </div> -->



<!-- <div class="col-sm-12" *ngIf="data.airDesc" >
        <div class="box">
          <h2></h2>
          <div class="row"> -->
<!-- <div class="col-sm-2">
              <label>AirportPlace</label>
              {{data.airDesc.airportPlace}}
            </div>
            <div class="col-sm-2">
              <label>BuildSite</label>
              {{data.airDesc.buildSite}}
            </div> -->

<!-- <div class="col-sm-2">
              <label> Aircraft owner</label>
              {{data.airDesc.aricraftOwner}}
            </div> -->
<!-- <div class="col-sm-2">
              <label>aircraftFlag</label>
              {{data.airDesc.aircraftFlag}}
            </div>
            <div class="col-sm-2">
              <label>enrollmentCod</label>
              {{data.airDesc.enrollmentCod}}
            </div>
            <div class="col-sm-2">
              <label>builder</label>
              {{data.airDesc.builder}}
            </div>
            <div class="col-sm-2">
              <label>maker</label>
              {{data.airDesc.maker}}
            </div>
            <div class="col-sm-2">
              <label>buildYear</label>
              {{data.airDesc.buildYear}}
            </div>
            <div class="col-sm-2">
              <label>firstFlightDate</label>
              {{data.airDesc.firstFlightDate}}
            </div>
            <div class="col-sm-2">
              <label>activeStatusFlag</label>
              {{data.airDesc.activeStatusFlag}}
            </div>
            <div class="col-sm-2">
              <label>prevalentRoutes</label>
              {{data.airDesc.prevalentRoutes}}
            </div>
            <div class="col-sm-2">
              <label>totFlightHoursNum</label>
              {{data.airDesc.totFlightHoursNum}}
            </div>
            <div class="col-sm-2">
              <label>totLandingsNum</label>
              {{data.airDesc.totLandingsNum}}
            </div>
            <div class="col-sm-2">
              <label>additionalNotes</label>
              {{data.airDesc.additionalNotes}}
            </div>
            <div class="col-sm-2">
              <label>Length</label>
              {{data.airDesc.length}}
            </div>
            <div class="col-sm-2">
              <label>Width</label>
              {{data.airDesc.width}}
            </div>
            <div class="col-sm-2">
              <label>Height</label>
              {{data.airDesc.height}}
            </div>
            <div class="col-sm-2">
              <label>passengersNum</label>
              {{data.airDesc.passengersNum}}
            </div>
            <div class="col-sm-2">
              <label>lastRestrucDate</label>
              {{data.airDesc.lastRestrucDate}}
            </div>
            <div class="col-sm-2">
              <label>Service</label>
              {{data.airDesc.service}}
            </div>
            <div class="col-sm-2">
              <label>OwnerNum</label>
              {{data.airDesc.ownerNum}}
            </div>
            <div class="col-sm-2">
              <label>ownerFirstName</label>
              {{data.airDesc.ownerFirstName}}
            </div>
            <div class="col-sm-2">
              <label>ownerLastName</label>
              {{data.airDesc.ownerLastName}}
            </div>
            <div class="col-sm-2">
              <label>aliveFusillageMaint</label>
              {{data.airDesc.aliveFusillageMaint}}
            </div>
            <div class="col-sm-2">
              <label>deadFusillageMaint</label>
              {{data.airDesc.deadFusillageMaint}}
            </div>
            <div class="col-sm-2">
              <label>systemEngineMaint</label>
              {{data.airDesc.systemEngineMaint}}
            </div>
            <div class="col-sm-2">
              <label>securEquipmMaint</label>
              {{data.airDesc.securEquipmMaint}}
            </div>
            <div class="col-sm-2">
              <label>enginesNum</label>
              {{data.airDesc.enginesNum}}
            </div>
            <div class="col-sm-2">
              <label>enginesType</label>
              {{data.airDesc.enginesType}}
            </div>
            <div class="col-sm-2">
              <label>maxSpeed</label>
              {{data.airDesc.maxSpeed}}
            </div>
            <div class="col-sm-2">
              <label>annualCashFlowsNum</label>
              {{data.airDesc.annualCashFlowsNum}}
            </div>
            <div class="col-sm-2">
              <label>annualOrdMaintExpense</label>
              {{data.airDesc.annualOrdMaintExpense}}
            </div>
            <div class="col-sm-2">
              <label>annualExtrMaintExpense</label>
              {{data.airDesc.annualExtrMaintExpense}}
            </div>
            <div class="col-sm-2">
              <label>futureValue</label>
              {{data.airDesc.futureValue}}
            </div>
            <div class="col-sm-2">
              <label>certificateFlag</label>
              {{data.airDesc.certificateFlag}}
            </div>
            <div class="col-sm-2">
              <label>certifAgency</label>
              {{ agencyDom[data.airDesc.certifAgency] ? (agencyDom[data.airDesc.certifAgency].translationCod | translate) : '' }}
            </div>
            <div class="col-sm-2">
              <label>otherCertif</label>
              {{data.airDesc.otherCertif}}
            </div>
            <div class="col-sm-2">
              <label>certifEndValidDate</label>
              {{data.airDesc.certifEndValidDate}}
            </div>
            <div class="col-sm-2">
              <label>prelimAmount</label>
              {{data.airDesc.prelimAmount}}
            </div>
            <div class="col-sm-2">
              <label>aircrProjectCorr</label>
              {{ projectDom[data.airDesc.aircrProjectCorr] ? (projectDom[data.airDesc.aircrProjectCorr].translationCod | translate) : '' }}
            </div>
            <div class="col-sm-2">
              <label>diversity</label>
              {{data.airDesc.diversity}}
            </div> -->
<!-- </div>
        </div>
      </div> -->