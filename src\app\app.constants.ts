import { OpaqueToken } from '@angular/core';
export let APP_CONSTANTS = new OpaqueToken('app.constants');

export abstract class IAppConstants {
  landingMap: any;
  wizardCodes: any;
  positionTypes: any;
  processCode: string;
  positionHeaderStatus: any;
  appraisalCompilationPage: string;
  tasks: any;
  searchTypes: any;
  appraisalTypes: any;
  ConfigurationType: any;
  CONNECTION_TIMEOUT: number;
  SubjectType: any;
  SAMPLE_CHECKS: string;
  MILLISECONDS_IN_A_DAY: number;
  REGEX_PATTERN: any;
}

export const AppConstants: IAppConstants = {
  landingMap: {
    // Per simulazione
    'UBZ-SIM-NDG': 'customer-info',
    'UBZ-SIM-AST': 'asset-info',
    'UBZ-SIM-GEN': 'generic-info',
    'UBZ-SIM-SUM': 'summary',
    'UBZ-SIM-CON': 'confirm',
    // Per richiesta perizia
    'UBZ-REQ-NDG': 'customer-info',
    'UBZ-REQ-GAR': 'guarantee-info',
    'UBZ-REQ-AST': 'asset-info',
    'UBZ-REQ-GEN': 'generic-info',
    'UBZ-REQ-SUM': 'summary',
    'UBZ-REQ-CON': 'confirm',
    'UBZ-REQ-PMT':'payment',
    // Utilizzato nella richiesta perizia per il redirect del punto 3
    RIC_LIS: 'appraisal-list',
    // Per frazionamento
    'UBZ-REQ-ACH': 'asset-choice',


    // Compilazione perizia
    'UBZ-PER-DGE': 'generic-data', // Dati generali
    'UBZ-PER-IGA': 'guarantees', // Immobili a Garanzia
    'UBZ-PER-VAL': 'evaluations', // Valutazioni
    'UBZ-PER-SAL': 'sal', // Stato Avanzamento Lavori
    'UBZ-PER-DBE': 'good-description', // Descrizione Bene
    'UBZ-PER-CAT': 'register', // Catasto
    'UBZ-PER-GAR': 'guarantees', // Garanzia
    'UBZ-PER-QTE': 'qte', // QTE Piano finanziario
    'UBZ-PER-AAC': 'agrarian-agency', // Azienda Agricola nel Complesso
    'UBZ-PER-TOP': 'third-opinion', // Third Opinion
    'UBZ-PER-RES': 'restrictions', // Restrizioni
    'UBZ-PER-CON': 'fill-confirm'
  },
  wizardCodes: {
    SIM: 'WSIM',
    REQ: 'WRPE',
    PER: 'PER',
    CHK: '',
    'PER-COM': 'PER' // Wizard code per la compilazione perizia
  },
  positionTypes: {
    SIM: 'SIM',
    REQ: 'REQ',
    PER: 'PER',
    CHK: 'CHK'
  },
  processCode: 'UBZ',
  positionHeaderStatus: {
    REV: 'REV',
    PRE: 'PRE'
  },
  appraisalCompilationPage: 'INFO_DATI_GENERALI',
  tasks: {
    uploadMissingDocs: 'UBZ_UPLOADMISSINGDOCS',
    fixAppointment: 'UBZ_FIXAPPOINTMENT',
    manualAssignment: 'UBZ_MANUALASSIGNMENT',
    compileInternal: 'UBZ_COMPILEPRZINTERNAL',
    compileExternal: 'UBZ_COMPILEPRZEXTERNAL',
    manageTechErr: 'UBZ_MANAGETECHER',
    inspectionOutcome: 'UBZ_OUTCOMEINSPECTION',
    przChecks: 'UBZ_PRZCHECKS',
    checks: 'UBZ_CHECKS',
    techLoadCheck: 'UBZ_TECHLOADCHECK',
    dataModification: 'UBZ_DATAMODIFICATION',
    escalationManagement: 'UBZ_ESCALATIONMGMT',
    checkAsset: 'UBZ_CHECKASSET',
    waitInspection: 'UBZ_WAITINSPECTION'
  },
  searchTypes: {
    RI: 'UBZ.SITE_CONTENT.10100011',
    PE: 'UBZ.SITE_CONTENT.101100111',
    ASAS: 'UBZ.SITE_CONTENT.11101',
    AS: 'UBZ.SITE_CONTENT.10001111',
    GA: 'UBZ.SITE_CONTENT.10100100'
  },
  appraisalTypes: {
    BENI_INDUSTRIALI: 'IND',
    MUTUI_AGRARI: 'MUT',
    EDILIZIA_AGEVOLATA: 'EAG',
    SHIPPING: 'SHI',
    AEREOMOBILE: 'AER',
    SAL: 'SAL',
    FINE_LAVORI: 'FLA',
    FRAZIONAMENTO: 'FRA',
    RESTRIZIONE: 'RES'
  },
  ConfigurationType: {
    SAMPLE_CHECKS: 'SAM',
    EXPERT_ASSIGNMENT: 'EXP',
    APPRAISAL: 'APP',
    INDIVIDUAL_ASSIGNMENT: 'IND'
  },
  CONNECTION_TIMEOUT: 120000,
  SubjectType: {
    PBN: 'PBN', // perito beneviso
    PER: 'PER', // Perito
    SOC: 'SOC' // Società peritale
  },
  SAMPLE_CHECKS: 'SAMPLE_CHECKS',
  MILLISECONDS_IN_A_DAY: 86400000,
  REGEX_PATTERN: {
    PERCENTAGE: '^[0-9]{1,2}([.]|([.][0-9]+)?)$|^100$',
    ONLY_TEXT: '^[a-zA-Z\\s]*$',
    ONLY_NUMBER: '^[0-9]*$',
    EMAIL: '[a-zA-Z_\\-\\.0-9]+@[a-zA-Z_\\-\\.0-9]+[.][a-zA-Z]+',
    ONLY_CHARS_AND_SPACE: '^([a-zA-Z\\s]*)$'
  }
};

// Il valore in input dovrebbe sempre essere sempre una stringa
// Restituisce il valore in stringa convertito e formattata a numerico
export function unmaskValue(value) {
  if (value === undefined) {
    return value;
  }
  if (typeof value === 'string') {
    // Se valore è stringa, punto come separatore migliaia e virgola come separatore decimali
    // Rimuove i punti utilizzati come separatori delle migliaia e sostituisce la virgola con il punto
    // come separatore decimale
    if (value.length) {
      return +(value.split('.').join('').replace(',', '.'));
    } else {
      return null;
    }
  } else {
    // Il valore è numerico
    return +value;
  }
}

// Riceve il valore dal BE e lo formatta per restituirlo al campo currency
// con la maschera numerica applicata
export function maskReceivedValue(value) {
  if (value || value === 0) {
    return value.toString().replace('.', ',');
  }
}

// Riceve il valore (timestamp o data formattata) e lo formatta per restituirlo
// al campo calendario con la maschera appropriata applicata
// initialValue può essere timestamp o formato ISO
export function maskReceivedDate(initialValue) {
  if (initialValue) {
    let date: Date;
    // check timestamp
    if (typeof initialValue === 'number' || !isNaN(initialValue.toString())) {
      date = new Date(initialValue);
    } else {
      const stringValue = initialValue.toString();
      // check formatted date
      date = new Date(stringValue.substr(6), (stringValue.substr(3, 2) - 1), stringValue.substr(0,2));
      // Se la data è arrivata in formato ISO non è stata tradotta correttamente, si utilizza quindi initialValue
      if (isNaN(date.getTime())) {
        date = initialValue;
      }
    }

    const day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
    const month = date.getMonth() < 9 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1;
    // Controllo che l'hanno sia di 4 cifre per gestire anche il caso
    // in cui il campo year sia antecedente al 1000
    // per gestire alcune date di sistema
    let year = '' + date.getFullYear();
    year = year.length >= 4 ? year : new Array(4 - year.length + 1).join('0') + year;
    return day + '-' + month + '-' + year;
  } else {
    return null;
  }
}

// Valore del campo calendario formattato secondo la maschera con
// il formato dd-mm-YYYY
// In base al tipo di formato che aveva inizialmente initialValue si esegue la formattazione
// corretta (timestamp o ISO)
export function unmaskDate(maskedDate): Date {
  const dateToString = maskedDate.toString();
  // FIXME - TOGLIERE SE REWRITE FUNZIONA SEMPRE CORRETTAMENTE
  // let date = new Date(dateToString.substr(6), (dateToString.substr(3,2) - 1), dateToString.substr(0,2))
  // return date;
  // Trasformato in utc e ricreando la data si ovvia al problema della timezone
  let date = Date.UTC(dateToString.substr(6), (dateToString.substr(3,2) - 1), dateToString.substr(0,2));
  return new Date(date);

}

/**
 * Ci dice se una data sotto forma di stringa è valida oppure no
 * @param input una data in formato stringa come dd-mm-YYYY
 */
export function dateIsValid(input: string): boolean {
  if (!input) {
    return false;
  }

  // Prima testo semplicemente il formato
  const validformat = /^\d{2}\-\d{2}\-\d{4}$/;

  if (!validformat.test(input)) {
    return false;
  }

  // Poi faccio un controllo più approfondito convertendo l'input in una data
  // e poi ricontrollando che i vari giorno, mese e anno corrispondano
  const dayfield = parseInt(input.split('-')[0], 10);
  const monthfield = parseInt(input.split('-')[1], 10);
  const yearfield = parseInt(input.split('-')[2], 10);
  // Controllo la validazione anche nel caso che il campo year sia antecedente al 1000
  // per gestire alcune date di sistema
  const daystring = dayfield < 10 ? '0' + dayfield : dayfield;
  const monthstring = monthfield < 10 ? '0' + (monthfield) : monthfield;
  const yearstring = yearfield.toString().length >= 4 ? yearfield : new Array(4 - yearfield.toString().length + 1).join('0') + yearfield;
  const datestring = yearstring + '-' + monthstring + '-' + daystring;
  const dayobj = new Date(datestring);

  if (
    dayobj.getMonth() + 1 !== monthfield ||
    dayobj.getDate() !== dayfield ||
    dayobj.getFullYear() !== yearfield
  ) {
    return false;
  }
  return true;
}
