<app-position-header
  [positionId]="positionId"
  [wizardCode]="wizardCode"
  [lockingUser]="genericTaskService.taskLockingUser">
</app-position-header>

<div class="row">
  <div class="col-sm-12 section-headline">
    <h1><i class="icon-print_checklist"></i> {{'UBZ.SITE_CONTENT.101101000' | translate }}</h1>
    <h2>{{'UBZ.SITE_CONTENT.101100111' | translate }} {{positionId}} - {{opinionType | translate}}</h2>
  </div>
</div>

<div class="row">
  <div class="col-sm-12">
    <h3>{{'UBZ.SITE_CONTENT.10011011010' | translate }}</h3>
  </div>
</div>

<div class="row step-navigation">
  <div class="col-sm-12">
    <div class="custom-checkbox">
      <input type="checkbox" id="selectall" class="checkbox" (click)="toggleAllAssetSelected()">
      <label for="selectall">{{'UBZ.SITE_CONTENT.111010' | translate }}</label>
    </div>
  </div>
</div>

<div class="row">
  <accordion class="panel-group" id="accordion">
    <accordion-group *ngFor="let accord of accordions; let i = index" #group class="panel" [isOpen]="accordionsStatusOpen[i]">
      <div accordion-heading class="acc-note-headline" role="tab">
        <h4 class="panel-title">
          <a role="button">
            <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
            <ng-container *ngIf = "entityTypeDom[accord.entityType]">
              {{ entityTypeDom[accord.entityType].translationCod | translate }} {{ accord.resItemDesc }}
            </ng-container>
            <span [class]="getAccordionStatusClass(accord)"></span>
          </a>
        </h4>
      </div>
      <div class="panel-collapse collapse in" role="tabpanel">
        <div class="panel-body">
          <div class="row">
            <div class="col-sm-12">
              <table class="uc-table">
                <thead>
                  <tr>
                    <th scope="col" class="col-sm-1 checkbox-col"></th>
                    <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10010110' | translate }}</th>
                    <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.11011010' | translate }}</th>
                    <th scope="col" class="col-sm-2">Nome del file</th>
                    <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10000001' | translate }}</th>
                    <th scope="col" class="col-sm-2 hidden-xs hidden-sm">
                      {{'UBZ.SITE_CONTENT.1111110' | translate }}
                    </th>
                    <th scope="col" class="col-sm-2 hidden-xs hidden-sm">{{'UBZ.SITE_CONTENT.10110' | translate }}</th>
                  </tr>
                </thead>
                <tbody>
                  <ng-container *ngFor="let row of accord.groups; let i2 = index">
                  <tr *ngIf="checkMandatory(row.mandatoryFor)" class="" data-index="1">
                    <td attr.data-label="">
                      <div class="custom-checkbox">
                        <input id="{{row.prog}}" name="checkbox-row" type="checkbox" class="checkbox" [checked]="checkboxStatus[row.prog]" (change)="setCheckboxStatus(row.prog)">
                        <label for="{{row.prog}}"></label>
                      </div>
                    </td>
                    <td *ngIf="row.categoryDesc && row.groupCod !== '999'" attr.data-label="{{'UBZ.SITE_CONTENT.10010110' | translate }}">{{ row.categoryDesc  }}</td>
                    <td *ngIf="row.categoryDesc && row.groupCod === '999'" attr.data-label="{{'UBZ.SITE_CONTENT.10010110' | translate }}">{{ row.lastUpload.customDocDesc ? row.lastUpload.customDocDesc : row.categoryDesc }}</td>
                    <td *ngIf="!row.categoryDesc" attr.data-label="{{'UBZ.SITE_CONTENT.10010110' | translate }}" style="color:#999">{{'UBZ.SITE_CONTENT.1100110000' | translate }}</td>
                    <td attr.data-label="{{'UBZ.SITE_CONTENT.11011010' | translate }}">{{ getStatusFormatted(row.mandatoryFor) | translate }}</td>
                    <td style="word-wrap:break-word;">{{row.lastUpload.documentName}}</td>
                    <td attr.data-label="{{'UBZ.SITE_CONTENT.10000001' | translate }}">
                      <div class="status-wrap">
                        <span [class]="getRowClass( row.acquired , row.flagMandatory)"></span>
                          <span class="info-message" style="display: none">
                            <i class="icon-info blue-tooltip" aria-hidden="true" data-placement="right" data-toggle="tooltip" title="{{'UBZ.SITE_CONTENT.1100110001' | translate }}"></i>
                          </span>
                          <span class="info-message-del" style="display: none">
                            <i class="icon-info blue-tooltip" aria-hidden="true" data-placement="right" data-toggle="tooltip" title="{{'UBZ.SITE_CONTENT.1100110010' | translate }}. {{'UBZ.SITE_CONTENT.1100110011' | translate }}."></i>
                          </span>
                      </div>
                    </td>
                    <td class="data hidden-xs hidden-sm" attr.data-label="{{'UBZ.SITE_CONTENT.1111110' | translate }}">
                      <span *ngIf="row.lastUpload.uploadDate" >{{ row.lastUpload.uploadDate | date: 'dd-MM-y' }} {{row.lastUpload.uploadDate | customTime}}</span>
                      <span *ngIf="!row.lastUpload.uploadDate" style="color:#999">---</span>
                    </td>
                    <td class="utente hidden-xs hidden-sm" attr.data-label="{{'UBZ.SITE_CONTENT.10110' | translate }}">
                      <span *ngIf="row.lastUpload.userId">{{row.lastUpload.userId}}</span>
                      <span *ngIf="!row.lastUpload.userId" style="color:#999">---</span>
                    </td>
                  </tr>
                  </ng-container>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </accordion-group>
  </accordion>
</div>

<div class="row submit-buttons" style="margin-top: 35px;">
  <button type="button" class="btn btn-primary waves-effect pull-right" (click)="invalidDocuments()" [disabled]="!canInvalidate" >{{'UBZ.SITE_CONTENT.10011011011' | translate }}</button>
  <button type="button" class="btn btn-tertiary waves-effect waves-secondary pull-right" (click)="goBack()"  >{{'UBZ.SITE_CONTENT.100000' | translate }}</button>
</div>