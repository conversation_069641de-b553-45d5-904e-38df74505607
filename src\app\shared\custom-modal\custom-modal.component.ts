import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  ChangeDetectorRef,
} from '@angular/core';
import { NgForm } from '@angular/forms';
import { ApplicationFormComponent } from '../application-form/components/application-form.component';
import { DomainService } from '../domain';

@Component({
  selector: 'app-custom-modal',
  templateUrl: './custom-modal.component.html',
  styleUrls: ['./custom-modal.component.css']
})
export class CustomModalComponent implements OnInit {
  // Indica se si tratta di una modale di aggiunta, modifica o cancellazione
  @Input() modalType: string; 
  @Input() isOpen: boolean;
  // flag che setta la classe modal-lg per la modale
  @Input() largeModalFlag: boolean;  
  @Input() headerTitle: string;
  @Input() positionId: string;
  @Input() idCode: string;  
  @Input() apfString: string; 
  // Array contenente le stringhe da mostrare nel body per le modali di cancellazione/descrittive
  @Input() messagesArray: string[];   
  // ButtonTitle[0] = string del button principale, buttonTitle[1] = stringa del button di chiusura modal se modalType = 'delete'
  @Input() buttonTitle: string[]; 
  // flag che abilita la logica di disabilitazione sul pulsante di submit
  @Input() disabledFlag: boolean; 
  @Output() modalClose = new EventEmitter();
  @Output() modalSubmit = new EventEmitter();  
  @ViewChild('apForm') apForm: ApplicationFormComponent;

  @ViewChild('f') form: NgForm;
  motivationList = [];
  motivation: any = null;
  notes: any = null;
  activeDomain;
  @Input() domainResp: any; // UBZ_DOM_MOTIVATIONS_DISABLE

  constructor(private cdRef: ChangeDetectorRef, private domainService: DomainService)  {}

  ngOnInit() {
    if (this.modalType === 'deleteAsset' && this.domainResp) {
      this.activeDomain = this.domainService.transform(this.domainResp).forEach((cat) => {
        this.motivationList.push({domCode: cat.domCode, translationCod: cat.translationCod});
      });
    
    }
  }

  // Previene l'errore ExpressionChangedAfterItHasBeenChecked dovuto alla disabilitazione del campo di submit
  ngAfterViewChecked() {
  this.cdRef.detectChanges();
}

  
  closeModal() {
    this.motivation = null;
    this.notes = null;
    this.isOpen = false;
    this.modalClose.emit();
  }

  submit(cond) {
    if(cond === 'deleteAsset'){
      this.modalSubmit.emit({'apForm': this.apForm, 'modalType': this.modalType, 'reason': this.motivation, 'notes': this.notes});
    }
    else{
      this.modalSubmit.emit({'apForm': this.apForm, 'modalType': this.modalType});
    }
    this.motivation = null;
    this.notes = null;
  }

}
