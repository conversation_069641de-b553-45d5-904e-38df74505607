<div *ngIf="isOpen" class="modal fade" id="property-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
  bsModal #propertyModal="bs-modal" (onHidden)="closeModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2 *ngIf="modalType==='add'">{{'UBZ.SITE_CONTENT.1111101011' | translate }}</h2>
        <h2 *ngIf="modalType==='modify'">{{'UBZ.SITE_CONTENT.1111110010' | translate }}</h2>
        <button type="button" class="close pull-right" aria-label="Close" (click)="closeModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <!-- CAMPI A DIGITAZIONE LIBERA TRASFORMATI UPPERCASE A VIDEO E VERSO IL BE -->
      <div class="modal-body">
        <form #modalForm="ngForm" (ngSubmit)="submit()">
          <div class="row form-group">
            <div class="col-sm-12">
              <label>{{'UBZ.SITE_CONTENT.1111101100' | translate | uppercase }}</label>
              <div class="custom-select">
                <select class="form-control" [(ngModel)]="propertyCopy.customer.ndgType" name="ownerType"
                  (ngModelChange)="resetDataStructure()">
                  <option value="" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option value="PF">{{'UBZ.SITE_CONTENT.1001100011' | translate }}</option>
                  <option value="PG">{{'UBZ.SITE_CONTENT.1001100100' | translate }}</option>
                </select>
              </div>
            </div>
          </div>
          <ng-container *ngIf="propertyCopy.customer.ndgType === 'PF'">
            <div class="row">
              <div class="col-sm-6 form-group">
                <label>{{ 'UBZ.SITE_CONTENT.101111110' | translate | uppercase }}</label>
                <input type="text" class="form-control" name="name" [ngModel]="propertyCopy.customer.name | uppercase"
                  (ngModelChange)="propertyCopy.customer.name=$event.toUpperCase()" appForcePattern
                  regexPattern="^[a-zA-Z\s]+$" maxlength="50" required />
              </div>
              <div class="col-sm-6 form-group">
                <label>{{'UBZ.SITE_CONTENT.101111111' | translate | uppercase }}</label>
                <input type="text" class="form-control" name="surname"
                  [ngModel]="propertyCopy.customer.surname | uppercase"
                  (ngModelChange)="propertyCopy.customer.surname=$event.toUpperCase()" appForcePattern
                  regexPattern="^[a-zA-Z\s]+$" maxlength="50" required />
              </div>
            </div>
            <div class="row">
              <div class="col-sm-6 form-group">
                <label>{{'UBZ.SITE_CONTENT.1000011101' | translate }}</label>
                <input type="text" class="form-control" name="taxNum"
                  [ngModel]="propertyCopy.customer.taxNum | uppercase"
                  (ngModelChange)="propertyCopy.customer.taxNum=$event.toUpperCase()" required minlength="16"
                  #tax="ngModel" maxlength="16" appForcePattern regexPattern="^[a-zA-Z0-9]{1,16}$" />
                <div *ngIf="tax.errors?.minlength">
                  <span class="tax-invalid"> {{'UBZ.SITE_CONTENT.11110011110' | translate | uppercase }} </span>
                </div>
              </div>
              <div class="col-sm-6 form-group">
                <label>{{'UBZ.SITE_CONTENT.1111101101' | translate | uppercase }}</label>
                <input type="text" class="form-control" name="ownershipPerc" [(ngModel)]="propertyCopy.ownershipPerc"
                  required appForcePattern regexPattern="^[0-9]{1,3}((\.)[0-9]{0,2})?$" />
              </div>
            </div>
          </ng-container>
          <ng-container *ngIf="propertyCopy.customer.ndgType === 'PG'">
            <div class="row">
              <div class="col-sm-6 form-group">
                <label>{{ 'UBZ.SITE_CONTENT.1101101' | translate | uppercase }}</label>
                <input type="text" class="form-control" name="heading"
                  [ngModel]="propertyCopy.customer.heading | uppercase"
                  (ngModelChange)="propertyCopy.customer.heading=$event.toUpperCase()" maxlength="100" required />
              </div>
              <div class="col-sm-6 form-group">
                <label>{{ 'UBZ.SITE_CONTENT.1001100101' | translate | uppercase }}</label>
                <input type="text" class="form-control" name="vatNum"
                  [ngModel]="propertyCopy.customer.vatNum | uppercase"
                  (ngModelChange)="propertyCopy.customer.vatNum=$event.toUpperCase()" minlength="11" #vat="ngModel"
                  maxlength="11" required appForcePattern regexPattern="^[0-9]{1,11}$" />
                <div *ngIf="vat.errors?.minlength">
                  <span class="vat-invalid">{{'UBZ.SITE_CONTENT.11110011111' | translate | uppercase}}</span>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-6 form-group">
                <label>{{'UBZ.SITE_CONTENT.1000011110' | translate }}</label>
                <input type="text" class="form-control" name="ownershipPerc" [(ngModel)]="propertyCopy.ownershipPerc"
                  required appForcePattern regexPattern="^[0-9]{1,3}((\.)[0-9]{0,2})?$" />
              </div>
            </div>
          </ng-container>
          <div class="row form-group">
            <div class="col-sm-12">
              <label>{{'UBZ.SITE_CONTENT.1111101110' | translate }}</label>
              <div class="custom-select">
                <select class="form-control" [(ngModel)]="propertyCopy.rightType" name="rightType" required>
                  <option [ngValue]="null">{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let row of (rightTypeDomain | domainMapToDomainArray)" value="{{row.domCode}}">
                    {{row.translationCod | translate}}</option>
                </select>
              </div>
            </div>
          </div>
          <div *ngIf="propertyCopy.rightType === 'ALT'" class="row form-group">
            <div class="col-sm-12">
              <label>{{'UBZ.SITE_CONTENT.1111101111' | translate }}</label>
              <textarea rows="4" class="form-control" data-placement="bottom" name="rightNote"
                [(ngModel)]="propertyCopy.rightNote" required></textarea>
            </div>
          </div>
          <div class="modal-footer text-center">
            <button *ngIf="modalType==='add'" class="btn btn-primary waves-effect" type="submit"
              [disabled]="modalForm.invalid">{{'UBZ.SITE_CONTENT.111100001' | translate | uppercase}}</button>
            <button *ngIf="modalType==='modify'" class="btn btn-primary waves-effect" type="submit"
              [disabled]="modalForm.invalid">{{'UBZ.SITE_CONTENT.111111010' | translate }}</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>