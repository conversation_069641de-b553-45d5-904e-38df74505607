export class FinancialItem {
  description: string;
  amount: number;
}

export class QteResponse {
  qteDate: any;
  crn: number;
  generalTechnicalExpenses: number;
  geognosticSurvey: number;
  areaAcquisition: number;
  unexpectedExpenses: number;
  otherExpenses: number;
  urbanizzationExpenses: number;
  connectionsExpenses: number;
  ctn: number;
  roundedTotal: number;
  flagUpdate: boolean;
  qteUpdateDate: any;
  firstSalePrice: number;
  guaranteeflag: boolean;
  deltaReason: string;
  totalCostUpdateDate: any;
  coverages: FinancialItem[] = [];
  expenses: FinancialItem[] = [];
}
