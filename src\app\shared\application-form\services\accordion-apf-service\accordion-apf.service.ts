import { Injectable } from '@angular/core';
import { NgForm } from '@angular/forms';

@Injectable()
export class AccordionAPFService {
  private previousGroup: any;
  private previousForm: NgForm;

  constructor() {}

  public handleOpenProperty(
    accordionGroup: any,
    form: NgForm,
    valid: boolean = false
  ): void {
    if (this.previousForm !== form) {
      // if (this.listaForm.toArray()[this.previousIndex] && this.listaForm.toArray()[this.previousIndex].valid) {
      if (this.previousForm && this.previousForm.valid) {
        this.previousGroup.isOpen = false;
      }
    }
    this.previousGroup = accordionGroup;
    this.previousForm = form;
  }
}
