import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ViewChild,
  QueryList,
  Inject
} from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { LandingService } from '../../../simulation/services/landing/landing.service';
import { AppraisalCompilationService } from '../../service/appraisal-compilation.service';
import { AccordionApplicationFormComponent } from '../../../shared/application-form/components/accordion-application-form/accordion-application-form.component';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';
import { PositionService } from '../../../shared/position/position.service';
import { AppraisalTemplateValidationComponent } from '../appraisal-template-validation/appraisal-template-validation.component';
import { MessageService } from '../../../shared/messages/services/message.service';
import { TranslateService } from '@ngx-translate/core';
import { DomainService } from '../../../shared/domain';

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.css'],
  providers: [AppraisalCompilationService]
})
export class RegisterComponent implements OnInit, OnDestroy {
  public positionId: string;
  private wizardCode: string;
  currentTask = 'UBZ-PER-CAT';
  private bpmTaskId: string;
  bpmTaskCod: string;
  private appraisalType: string;
  public requestId: string;
  public appraisalInfo: any;
  public isInterno: boolean;
  public isEsterno: boolean;

  assets: any[] = [];
  selectedAsset: any = {};
  endDate: string;
  inspectionDate: string;
  isEditable = true;
  deleteAssetModalIsOpen = false;
  @ViewChild('property') property;
  @ViewChildren(AccordionApplicationFormComponent)
  appForms: QueryList<AccordionApplicationFormComponent>;
  @ViewChild(AppraisalTemplateValidationComponent)
  templateValidation: AppraisalTemplateValidationComponent;
  newAssetModalIsOpen = false;
  haveDisabledFields = false; // Quando true i campi sono disabilitati (ad esempio per perizie SAL o Fine lavori)
  public minAdmissibleDate: Date = new Date(1900, 1, 1);
  public maxAdmissibleDate: Date = new Date();
  public today: Date = new Date();
  private previousAssetStatus: string;
  public isAValidPage: boolean;

  public staticPageContent;
  private _subscription;

  draftButtonCallback = this.saveDraft.bind(this);
  activeDomain: any = [];
  activeCategories: any = [];

  constructor(
    private _activatedRoute: ActivatedRoute,
    private _domainService: DomainService,
    public _landingService: LandingService,
    private _appraisalCompilationService: AppraisalCompilationService,
    public _positionService: PositionService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants,
    private _messageService: MessageService,
    private _translateService: TranslateService
  ) { }

  ngOnInit() {
    this._subscription = this._activatedRoute.params
      .switchMap((params: Params) => {
        this.positionId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        this.bpmTaskId = params['taskId'];
        this.bpmTaskCod = params['taskCod'];
        this._landingService.checkIfCompletedTask(
          this.positionId,
          this.currentTask
        );
        return Observable.forkJoin(
          this._appraisalCompilationService.getAssociateAssets(this.positionId),
          this._positionService.getAppraisalInfo(this.positionId)
        );
      })
      .switchMap(res => {
        this.assets = res[0];
        this.selectedAsset = this.assets[0];
        this.setAppraisalInfo(res[1]);
        return this._appraisalCompilationService.getRegisterStaticData(
          this.selectedAsset.idObject
        );
      })
      .subscribe(res => {
        if (!res.partialSaveData) {
          this.staticPageContent = res;
        } else {
          this.getDraftFields(res);
        }
        this.dataFormat();
        this.pageIsValid();
      });
    this.getCategory();
  }

  getCategory() {
    this._domainService.newGetDomain('UBZ_DOM_CATEGORY_TYPE').subscribe(res => {
      this.activeDomain = this._domainService.transform(res).forEach(cat => {
        this.activeCategories.push(cat.domCode);
      });;
    });

  }
  
  /**
   * @name setAppraisalInfo
   * @description Metodo invocato sul recupero dell'oggetto perizia, imposta le variabili ad esso connesse
   * @param appraisalInfo Oggetto perizia recuperto dal wizard container
   */
  setAppraisalInfo(appraisalInfo) {
    this.appraisalInfo = appraisalInfo;
    this.checkProvenienza();
    this.requestId = appraisalInfo.requestId;
    this.appraisalType = appraisalInfo.appraisal.appraisalType;

    // Alcune perizie non migrate hanno campi disabilitati
    if (
      appraisalInfo.appraisal.originProcess !== 'MIG' &&
      !appraisalInfo.migParent
    ) {
      // Beni mobili SAL/FLA hanno alcuni campi disabilitati
      this.haveDisabledFields = this.isBeneMobile(appraisalInfo) &&
        (
          this.appraisalType === this.constants.appraisalTypes.FINE_LAVORI ||
          this.appraisalType === this.constants.appraisalTypes.SAL
        );
      // Frazionamento ha alcuni campi disabilitati
      this.haveDisabledFields = this.haveDisabledFields || (this.appraisalType === this.constants.appraisalTypes.FRAZIONAMENTO);
    }
  }

  isBeneMobile(appraisalInfo) {
    return appraisalInfo.appraisal
      && appraisalInfo.appraisal.resItemType
      && appraisalInfo.appraisal.resItemType === 'MOB';
  }

  ngOnDestroy() {
    this._subscription.unsubscribe();
  }

  private checkProvenienza(): void {
    switch (this.appraisalInfo.appraisal.surveyNecFlag) {
      case 'INTERNO':
      case 'INT':
      case 'I':
        this.isInterno = true;
        break;
      case 'ESTERNO':
      case 'EST':
      case 'E':
        this.isEsterno = true;
        break;
      default:
        this.isInterno = false;
        this.isEsterno = false;
    }
  }

  // Salva i dati: funzione comune al salvataggio delle bozze e salvataggio normale
  private saveData(): Observable<any> {
    this.previousAssetStatus = this.selectedAsset.conclusionStatusFlag;
    this.selectedAsset.conclusionStatusFlag = 'Y';
    const apfMap = {};
    for (const appForm of this.appForms.toArray()) {
      const appModel = appForm.getFormsWithModel();
      for (const prop in appModel) {
        if (appModel.hasOwnProperty(prop)) {
          apfMap[prop] = appModel[prop];
        }
      }
    }
    const apfs = { page: 'CATASTO', apfmap: apfMap };
    const statics = JSON.parse(JSON.stringify(this.staticPageContent));

    statics['docConclusionDate'] = new Date(
      Date.parse(statics['docConclusionDate'])
    );
    statics['surveyDate'] = new Date(Date.parse(statics['surveyDate']));
    return this._appraisalCompilationService.saveRegisterData(
      this.selectedAsset.idObject,
      apfs,
      this.staticPageContent
    );
  }

  public save() {
    if (
      this.staticPageContent['docConclusionDate'] &&
      this.staticPageContent['surveyDate'] &&
      this.staticPageContent['docConclusionDate'].getTime() &&
      this.staticPageContent['surveyDate'].getTime() &&
      this.staticPageContent['docConclusionDate'].getTime() >
      this.staticPageContent['surveyDate'].getTime()
    ) {
      // Errore se data sopralluogo > data completamento documentazione
      this._messageService.showError(
        this._translateService.instant('UBZ.SITE_CONTENT.1010111101'),
        this._translateService.instant('UBZ.SITE_CONTENT.1010111100')
      );
      return;
    }
    this.saveData()
      .switchMap(() => {
        if (this.templateValidation) {
          return this._appraisalCompilationService.saveTemplateValidation(
            this.templateValidation.appValidation
          );
        } else {
          return Observable.of(true);
        }
      })
      .subscribe(
        () => {
          this.isEditable = false;
        },
        () => {
          this.selectedAsset.conclusionStatusFlag = this.previousAssetStatus;
        }
      );
  }

  saveDraft(): Observable<any> {
    const apfMap = {};
    for (const appForm of this.appForms.toArray()) {
      apfMap[appForm.drivers['DD_ORD_PAG']] = JSON.stringify(appForm.model);
    }
    return this._landingService
      .saveDraft(
        this.positionId,
        JSON.stringify(this.staticPageContent),
        apfMap,
        'CATASTO',
        this.selectedAsset.idObject
      )
      .switchMap(() => {
        if (this.templateValidation) {
          return this._appraisalCompilationService.saveTemplateValidation(
            this.templateValidation.appValidation
          );
        } else {
          return Observable.of(true);
        }
      });
  }

  goToNextTask() {
    this._landingService.goNextPage(
      this.positionId,
      this.currentTask,
      this.wizardCode,
      this._activatedRoute
    );
  }

  goToGenericTask() {
    this._landingService.goToGenericTask(this.positionId, this.bpmTaskId, this.bpmTaskCod);
  }

  goToPreviousTask() {
    this._landingService.goToPreviousTask(this.positionId, this.wizardCode, this._activatedRoute);
  }

  isActive(asset: number) {
    if (asset === this.selectedAsset) {
      return 'active';
    } else {
      return '';
    }
  }

  getAssetAddress(asset): string {
    return `Indirizzo\n${asset.adress}, ${asset.numAddress}\n${asset.cap} ${
      asset.city
      } ${asset.province}`;
  }

  changeSelectedAsset(asset: any) {
    this.selectedAsset = undefined;
    this._appraisalCompilationService
      .getRegisterStaticData(asset.idObject)
      .subscribe(y => {
        if (!y.partialSaveData) {
          this.staticPageContent = y;
        } else {
          this.getDraftFields(y);
        }
        this.selectedAsset = asset;
        this.dataFormat();
      });
  }

  private getDraftFields(serviceResponse: any): void {
    this.staticPageContent = JSON.parse(serviceResponse.partialSaveData);
    this.staticPageContent['principalOwner'] =
      serviceResponse['principalOwner'];
  }

  private refreshPage() {
    this._appraisalCompilationService
      .getAssociateAssets(this.positionId)
      .subscribe(x => {
        this.assets = x;
        this.changeSelectedAsset(this.assets[this.assets.length - 1]);
        this.endDate = null;
        this.inspectionDate = null;
      });
  }

  edit() {
    this.isEditable = true;
  }

  private dataFormat() {
    this.staticPageContent['docConclusionDate'] = this.staticPageContent[
      'docConclusionDate'
    ]
      ? new Date(this.staticPageContent['docConclusionDate'])
      : null;
    this.staticPageContent['surveyDate'] = this.staticPageContent['surveyDate']
      ? new Date(this.staticPageContent['surveyDate'])
      : null;
  }

  pageIsValid() {
    if (this.areDynamicsAllValid() && this.areStaticsAllValid()) {
      setTimeout(() => {
        this.isAValidPage = true;
      }, 0);
    } else {
      setTimeout(() => {
        this.isAValidPage = false;
      }, 0);
    }
  }
  private areDynamicsAllValid(): boolean {
    if (this.appForms) {
      for (const af of this.appForms.toArray()) {
        if (af.isAllValid() === false) {
          return false;
        }
      }
    }
    return true;
  }
  private areStaticsAllValid(): boolean {
    if (this.isInterno || this.isEsterno) {
      return (
        this.checkWithProvenienza() &&
        this.staticPageContent['surveyDate'] !== null
      );
    } else {
      return this.checkWithProvenienza();
    }
  }

  private checkWithProvenienza(): boolean {
    return (
      (!this.property || (this.property && this.property.pageIsValid)) &&
      (!this.templateValidation || this.templateValidation.form.valid) &&
      this.staticPageContent['docConclusionDate'] !== null
    );
  }

  areAllAssetsValid(): boolean {
    for (const as of this.assets) {
      if (as.conclusionStatusFlag === 'N') {
        return false;
      }
    }
    return true;
  }

  openNewAssetModal() {
    this.newAssetModalIsOpen = true;
  }

  closeNewAssetModal() {
    this.newAssetModalIsOpen = false;
    this.refreshPage();
  }

  delete() {
    this.deleteAssetModalIsOpen = true;
  }

  copy() {
    this._appraisalCompilationService
      .copyAsset(this.selectedAsset.idObject.toString(), this.positionId)
      .subscribe(newAssetId => {
        this._appraisalCompilationService
          .getAssociateAssets(this.positionId)
          .subscribe(assets => {
            for (const a of assets) {
              if (a.idObject === Number(newAssetId)) {
                this.refreshPage();
                this.changeSelectedAsset(a);
              }
            }
          });
      });
  }

  // Gestisce il submit della modale di cancellazione utente
  submitDeleteModal() {
    if (this.selectedAsset && this.selectedAsset.idObject) {
      this._appraisalCompilationService.deleteAsset(this.selectedAsset.idObject).subscribe(x => {
        this.closeDeleteModal();
        this.refreshPage();
      });
    }
  }

  // Chiude e resetta le variabili per la modale di cancellazione
  closeDeleteModal() {
    this.deleteAssetModalIsOpen = false;
  }

  // Restituisce la massima data selezionabile per il calendario
  // Data completamento documentazione
  getConclusionDateLimit(): Date {
    let maxDate = this.today;
    if (this.staticPageContent['surveyDate']) {
      maxDate = new Date(this.staticPageContent['surveyDate']);
      maxDate.setDate(maxDate.getDate() - 1);
      return maxDate;
    } else {
      return maxDate;
    }
  }
}
