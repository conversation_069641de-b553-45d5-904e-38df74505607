import { Directive, Input } from '@angular/core';
import { Validator, NG_VALIDATORS, AbstractControl, ValidatorFn } from '@angular/forms';

@Directive({
  selector: '[appMinValue]',
  providers: [{provide: NG_VALIDATORS, useExisting: MinValidatorDirective, multi: true}]
})
export class MinValidatorDirective implements Validator {
  @Input() appMinValue: number;

  constructor() { }

  validate(control: AbstractControl): {[key: string]: any} {
    return this.appMinValue ? this.minValueValidator(this.appMinValue)(control) : null;
  }

  private minValueValidator(min: number): ValidatorFn {
    return (c: AbstractControl): { [key: string]: boolean } | null => {
      if (c.value && (isNaN(c.value) || Number(c.value) < Number(min))) {
        return {'minValue': true};
      }
      return null;
    };
  }
}
