<form #f="ngForm" novalidate (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
          <span>{{ title}}</span>
          <span class="state" [ngClass]="{green: semaforo === 'G', red: semaforo === 'R',yellow: semaforo === 'Y'}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <table *ngIf="assetList.length>0" class="uc-table">
          <thead>
            <tr>
                <th class="col-sm-1">{{'UBZ.SITE_CONTENT.11101011100' | translate }}</th>
                <th class="col-sm-2">{{'UBZ.SITE_CONTENT.11101011101' | translate }}</th>
                <th class="col-sm-1">{{'UBZ.SITE_CONTENT.11101011110'| translate }}</th>
                <th class="col-sm-1">{{'UBZ.SITE_CONTENT.11101011111' | translate }}</th>
                <th class="col-sm-1">{{'UBZ.SITE_CONTENT.11101100000' | translate }}</th>
                <th class="col-sm-1">{{'UBZ.SITE_CONTENT.11101100001' | translate }}</th>
                <th class="col-sm-1">{{'UBZ.SITE_CONTENT.11101100010' | translate }}</th>

                <th class="col-sm-1">{{'UBZ.SITE_CONTENT.11101100011' | translate }}</th>

                <th class="col-sm-1">{{'UBZ.SITE_CONTENT.11101100100' | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let asset of assetList; let index = index">
              <td>{{ asset.objectCod }}</td>
              <td>{{ asset.region + ',\n' + asset.city + ',\n' + asset.province + asset.streetNum}}</td>
              <td>{{ asset.resItemCategory }}</td>
              <td>{{ asset.reRegistrySheet }}</td>
              <td>{{ asset.reRegistryPart }}</td>
              <td>{{ asset.reRegistrySub}}</td>
              <td>{{ asset.jointCod }}</td>
              <td>{{ asset.collatTecForm }}</td>

              <td>{{ asset.collatAmount ? (asset.collatAmount | currency:'EUR':true:'1.2-2') :"" }}</td>
            </tr>
          </tbody>
        </table>
        <p style=" margin-top:1%;margin-left:1%" *ngIf="assetList.length<1">{{'UBZ.SITE_CONTENT.11101100111' | translate}}</p>
      </div>
    </div>
  </accordion-group>
</form>