import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import * as Rx from 'rxjs';

import { DashboardDetailComponent } from '../dashboard-detail.component';
import { SearchService } from '../../../shared/search/search.service';
import { CounterInput, Counter } from '../../model/counter-input';
import { DomainService } from '../../../shared/domain/domain.service';
import { Navigable } from '../../model/navigable-interface';

@Component({
  selector: 'app-all-dashboard',
  templateUrl: '../dashboard-detail.component.html',
  styleUrls: ['./all-dashboard.component.css']
})
export class AllDashboardComponent extends DashboardDetailComponent
  implements OnInit, Navigable {
  headFields = this.pageHeaders['ALL'];
  fieldsConditions = this.pageFieldConditions['ALL'];
  private activedRetrievedType = 'SIM';
  private redirectMap = {
    SIM: 'WSIM',
    REQ: 'WRPE'
  };

  constructor(
    public searchService: SearchService,
    public domainService: DomainService,
    private router: Router
  ) {
    super(domainService, searchService);
  }

  ngOnInit() {
    super.ngOnInit();
  }

  getPositionsCounts(
    inChargeUser: boolean,
    inChargeBranch: boolean
  ): Observable<any> {
    return Rx.Observable.merge(
      this.searchService.getSimulationsCounts(inChargeUser, inChargeBranch),
      this.searchService.getRequestsCounts(inChargeUser, inChargeBranch)
    );
  }

  getPositionsData(
    excel: boolean,
    page: number,
    pageSize: number,
    orderBy: string,
    asc: boolean,
    filter: any
  ): Observable<any> {
    return Observable.of({});
  }

  retrieveData() {
    if (this.searchTypes === 'USER') {
      this.inChargeUser = true;
      this.inChargeBranch = false;
    } else {
      this.inChargeUser = false;
      this.inChargeBranch = true;
    }
    this.filter = {};
    this.filter.inChargeUser = this.inChargeUser;
    this.filter.inChargeBranch = this.inChargeBranch;

    this.searchService
      .getSimulationsCounts(this.inChargeUser, this.inChargeBranch)
      .subscribe(y => {
        this.searchService
          .getRequestsCounts(this.inChargeUser, this.inChargeBranch)
          .subscribe(z => {
            const x = new Array();
            for (const i1 in y) {
              if (y.hasOwnProperty(i1)) {
                x.push(y[i1]);
              }
            }
            for (const i3 in z) {
              if (z.hasOwnProperty(i3)) {
                x.push(z[i3]);
              }
            }
            this.counterInput = new CounterInput();
            this.counterInput.counter = [];
            let index = 0;
            for (const i2 in x) {
              if (x.hasOwnProperty(i2)) {
                if (index === 0) {
                  this.counterInput.counter[index] = new Counter(
                    x[i2].id,
                    x[i2].translation,
                    x[i2].count,
                    true
                  );
                  this.filter.phase = x[i2].id;
                  this.searchService
                    .getSimulationsData(
                      this.excel,
                      this.page,
                      this.pageSize,
                      this.orderBy,
                      this.asc,
                      this.filter
                    )
                    .subscribe(x2 => {
                      if (x2.length === 0) {
                        this.activedRetrievedType = 'REQ';
                        this.searchService
                          .getRequestsData(
                            this.excel,
                            this.page,
                            this.pageSize,
                            this.orderBy,
                            this.asc,
                            this.filter
                          )
                          .subscribe(x3 => {
                            this.positionListResults.positions = x3;
                            this.positionListResults.count = x3.length;
                          });
                      } else {
                        this.positionListResults.positions = x2;
                        this.positionListResults.count = x2.length;
                      }
                      this.renderCounter = true;
                    });
                } else {
                  this.counterInput.counter[index] = new Counter(
                    x[i2].id,
                    x[i2].translation,
                    x[i2].count,
                    false
                  );
                }
                index++;
              }
            }
            this.selectedCounterElementsNumb = this.counterInput.counter[0].num;
            index === 0
              ? (this.counterInput.numElem = 0)
              : (this.counterInput.numElem = index + 1);
          });
      });
  }

  protected refreshPositionList() {
    this.searchService
      .getSimulationsData(
        this.excel,
        this.page,
        this.pageSize,
        this.orderBy,
        this.asc,
        this.filter
      )
      .subscribe(y => {
        if (y.length === 0) {
          this.activedRetrievedType = 'REQ';
          this.searchService
            .getRequestsData(
              this.excel,
              this.page,
              this.pageSize,
              this.orderBy,
              this.asc,
              this.filter
            )
            .subscribe(x => {
              this.positionListResults.positions = x;
              this.positionListResults.count = x.length;
            });
        } else {
          this.activedRetrievedType = 'SIM';
          this.positionListResults.positions = y;
          this.positionListResults.count = y.length;
        }
      });
  }

  goToTarget(id) {
    this.router.navigateByUrl(
      `landing/${this.redirectMap[this.activedRetrievedType]}/${id}`
    );
  }
}
