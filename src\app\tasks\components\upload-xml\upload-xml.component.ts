import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChild
} from '@angular/core';

import { GenericTaskService } from '../../services/generic-task/generic-task.service';

@Component({
  selector: 'app-upload-xml',
  templateUrl: './upload-xml.component.html',
  styleUrls: ['./upload-xml.component.css']
})
export class UploadXmlComponent implements OnInit {
  @ViewChild('fileToUpload') fileToUpload: any;
  @Input() isOpen: boolean;
  @Input() positionId: string;
  @Output() closeModal = new EventEmitter();

  fileName = '';

  constructor(private genericTaskService: GenericTaskService) {}

  ngOnInit() {}

  clickOnCloseModal() {
    this.isOpen = false;
    this.closeModal.emit();
  }

  setFileName(fileName?: string) {
    if (!fileName && fileName !== '') {
      fileName = this.fileToUpload.nativeElement.files[0].name;
    }
    this.fileName = fileName;
  }

  setFile() {
    this.setFileName();
  }

  clickOnUploadButton() {
    this.genericTaskService
      .uploadAppraisalXml(
        this.positionId,
        this.fileToUpload.nativeElement.files[0]
      )
      .subscribe(x => {
        this.clickOnCloseModal();
      });
  }

  isSaveEnable(): boolean {
    let ret = true;
    if (!this.fileName || !this.fileName.endsWith('.xml')) {
      ret = false;
    }
    return ret;
  }
}
