<form #f="ngForm" novalidate (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>{{'UBZ.SITE_CONTENT.1000011011' | translate }}
          <span class="state" [ngClass]="{'green' : (pageIsValid), 'red' : !(pageIsValid)}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <ng-container *ngIf="pageContent.length > 0">
          <table class="table">
            <thead>
              <th  style="text-align: left">{{'UBZ.SITE_CONTENT.10011100011' | translate }}</th>
              <th class="col-sm-3" style="text-align: center">{{'UBZ.SITE_CONTENT.10010101110' | translate }}</th>
              <th class="col-sm-3" style="text-align: center">{{'UBZ.SITE_CONTENT.10010101111' | translate }}</th>
              <th></th>
            </thead>
            <tbody>
              <tr *ngFor="let el of pageContent">
                <td *ngIf="originTypes && originTypes[el.originType]" style="text-align: left">
                  {{ originTypes[el.originType].translationCod | translate }}
                </td>
                <!-- FIXME - BISOGNEREBBE TROVARE IL MODO DI TRONCARE LA STRINGA LUNGA TERMINANDOLA CON ...
                ED INSERENDO IL DETTAGLIO DELLA NOTA MAGARI DENTRO IL TOOLTIP -->
                <!-- <td>
                  {{el.originNotes}}
                </td> -->
                <td style="text-align: center">
                  {{el.deedDate | date: 'dd-MM-yyyy' }}
                </td>
                <td style="text-align: center">
                  {{el.archiveNum}}
                </td>
                <td>
                  <ng-container *appAuthKey="'UBZ_ORIGIN_DELETE'">
                    <button type="button" class="btn btn-empty waves-effect waves-secondary" (click)="openCustomModal('delete',el)">
            				      <i class="fa fa-trash-o" aria-hidden="true"></i>
            			  </button>
                  </ng-container>
                  <ng-container *appAuthKey="'UBZ_ORIGIN_MODIFY'">
            			  <button type="button" class="btn btn-empty waves-effect waves-secondary" (click)="openCustomModal('modify', el)">
            				      <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
            			  </button>
                  </ng-container>
                </td>
              </tr>
            </tbody>
          </table>
          </ng-container>
          <div class="row">
            <div class="col-sm-12">
              <ng-container *appAuthKey="'UBZ_ORIGIN_ADD'">
                <button type="button" class="btn btn-empty" (click)="openCustomModal('add')">
                  <i class="fa fa-plus"></i> {{'UBZ.SITE_CONTENT.111100001' | translate }}
                </button>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </accordion-group>
</form>

<!-- CUSTOM MODAL centralizza la gestione delle modal in aggiunta, modfifica e cancellazione per il component -->
<app-custom-modal
  [modalType] = "modalType"
  [isOpen] = "customModalIsOpen"
  [largeModalFlag] = "largeModalFlag"
  [headerTitle] = "headerTitle"
  [positionId]="positionId"
  [idCode]="selectedPageElement ? selectedPageElement.originId : ''"
  [apfString] = "apfString"
  [messagesArray] = "messagesArray"
  [buttonTitle] = "buttonTitle"
  [disabledFlag] = "disabledFlag"
  (modalSubmit) = "handleSubmitCustomModal($event)"
  (modalClose)="closeCustomModal()">
</app-custom-modal>
