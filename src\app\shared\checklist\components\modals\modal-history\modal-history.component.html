<div *ngIf="isOpen" class="modal fade" id="clock" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #stateBox="bs-modal" (onHidden)="closeModal()" [config]="{show: 'true'}">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h2>{{'UBZ.SITE_CONTENT.110011011' | translate }}</h2>
                <button type="button" class="close pull-right" data-dismiss="modal" aria-label="Close" (click)="closeModal()">
                    <i class="icon-close"></i>
                </button>
            </div>
            <div class="modal-body" *ngIf="!deleteConfirm">
                <div class="row">
                    <div class="col-sm-12">
                        <h4>{{'UBZ.SITE_CONTENT.11000110' | translate }}</h4>
                    </div>
                    <div *ngIf="section" class="col-sm-3 form-group">
                      <label>
                        {{'UBZ.SITE_CONTENT.11010000' | translate }} {{ (section.entityType) ? (section.entityType.translationCod | translate) : ''}}
                        </label>
                        {{ (section.resItemType) ? (section.resItemType.translationCod | translate) : '' }} 
                        {{ (section.resItemCategory) ? ('-' + (section.resItemCategory.translationCod | translate)) : '' }}
                    </div>
                    <div class="col-sm-3 form-group">
                        <label>{{'UBZ.SITE_CONTENT.11001000' | translate }}</label>
                        {{document.lastUpload.userId}}
                    </div>
                    <div class="col-sm-3 form-group">
                        <label>{{'UBZ.SITE_CONTENT.1111110' | translate }}</label>
                        {{document.lastUpload.uploadDate | date: 'dd-MM-y'}} {{document.lastUpload.uploadDate | customTime}}
                    </div>
                    <div class="col-sm-3 form-group">
                        <label>{{'UBZ.SITE_CONTENT.10000001' | translate }}</label>
                        <span [class]="isValidClass[document.acquired]"></span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <h4>{{'UBZ.SITE_CONTENT.110011100' | translate }}</h4>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <table class="uc-table">
                            <thead>
                            <tr>
                                <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1110100' | translate }}</th>
                                <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1111110' | translate }}</th>
                                <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1110101' | translate }}</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr *ngFor="let el of versions">
                                <td attr.data-label="{{'UBZ.SITE_CONTENT.1110100' | translate }}">{{el.documentDesc}}</td>
                                <td attr.data-label="{{'UBZ.SITE_CONTENT.1111110' | translate }}">
                                    {{el.uploadDate | date: 'dd-MM-y' }} {{el.uploadDate | customTime}}
                                </td>
                                <td attr.data-label="{{'UBZ.SITE_CONTENT.1110101' | translate }}">
                                    <button type="button" class="btn btn-clean" (click)="downloadDocument(el)">
                                        <i class="icon-download" aria-hidden="true"></i>
                                    </button>
                                    <button data-toggle="modal" data-target="#remove-description" type="button" class="btn btn-clean" (click)="deleteConfirm = true;selectedDocument = el;">
                                        <i class="fa fa-trash-o" aria-hidden="true"></i>
                                    </button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <ng-container *ngIf="deleteConfirm">
              <div class="modal-body">
                <div class="row">
                  <div class="col-sm-12">
                    <p>{{'UBZ.SITE_CONTENT.1010011010' | translate }}</p>
                  </div>
                </div>
              </div>
              <div class="modal-footer text-center">
                <button type="button" class="btn btn-primary waves-effect" data-dismiss="modal" (click)="deleteDocument()">{{'UBZ.SITE_CONTENT.11110' | translate | uppercase }}</button>
                <button type="button" class="btn btn-secondary waves-effect" data-dismiss="modal" (click)="deleteConfirm = false">{{'UBZ.SITE_CONTENT.100000' | translate | uppercase }}</button>
              </div>
            </ng-container>
        </div>
    </div>
</div>
