import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot
} from '@angular/router';
import { Observable } from 'rxjs/Observable';

import { AccessRightsService } from '../../shared/access-rights/services/access-rights.service';

@Injectable()
export class AccessRightsGuard implements CanActivate {
  constructor(private accessRightsService: AccessRightsService) {}

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    const page = next.data ? next.data['name'] : null;
    this.accessRightsService.retrieveAccessRightsFunctions(
      this.getPositionId(next),
      page
    );
    return true;
  }

  private getPositionId(next: ActivatedRouteSnapshot): string {
    if (next.params && next.params['positionId']) {
      return next.params['positionId'];
    }

    if (next.parent) {
      return this.getPositionId(next.parent);
    }
  }
}
