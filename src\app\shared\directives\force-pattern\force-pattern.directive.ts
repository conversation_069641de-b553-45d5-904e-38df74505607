import {
  Directive,
  ElementRef,
  HostListener,
  Input,
  Inject
} from '@angular/core';
import { DOCUMENT } from '@angular/platform-browser';

@Directive({
  selector: '[appForcePattern]'
})
export class ForcePatternDirective {
  @Input() regexPattern: string;

  // Backspace, tab, end, home
  private specialKeys: string[] = [
    'Backspace',
    'Tab',
    'End',
    'Home',
    'Up',
    'Down',
    'Left',
    'Right',
    'ArrowUp',
    'ArrowDown',
    'ArrowLeft',
    'ArrowRight',
    'ShiftLeft',
    'ShiftRight',
    'Delete'
  ];
  // Used to check Ctrl-C, Ctrl-V, Ctrl-X, Crtl-A
  private copyCutPasteKeys: number[] = [65, 67, 86, 88];
  private shiftUps = {
    '96': '~',
    '49': '!',
    '50': '@',
    '51': '#',
    '52': '$',
    '53': '%',
    '54': '^',
    '55': '&',
    '56': '*',
    '57': '(',
    '48': ')',
    '45': '_',
    '61': '+',
    '91': '{',
    '93': '}',
    '92': '|',
    '59': ':',
    '39': "'",
    '44': '<',
    '46': '>',
    '47': '?'
  };

  constructor(
    private el: ElementRef,
    @Inject(DOCUMENT) private document: any
  ) {}

  @HostListener('keydown', ['$event'])
  onKeyDown(event: any) {
    const regex: RegExp =
      this.regexPattern !== undefined
        ? new RegExp(this.regexPattern)
        : new RegExp('');
    let eventKey = event.key;
    if (!eventKey) {
      eventKey = event.code;
    }
    if (
      this.specialKeys.indexOf(eventKey) !== -1 ||
      (this.copyCutPasteKeys.indexOf(event.keyCode) !== -1 && event.ctrlKey)
    ) {
      return;
    }
    const current: string = this.el.nativeElement.value;
    const selectionStart: number = event.currentTarget.selectionStart;
    const selectionEnd: number = event.currentTarget.selectionEnd;
    const keyCode =
      96 <= event.keyCode && event.keyCode <= 105
        ? event.keyCode - 48
        : event.keyCode;
    let newChar: string;
    if (event.keyCode === 190 || event.keyCode === 110) {
      newChar = '.';
    } else if (event.keyCode === 188) {
      newChar = ',';
    } else if (event.keyCode === 189 && !event.shiftKey) {
      newChar = '-';
    } else if (event.keyCode === 109 && !event.shiftKey) {
      newChar = '-';
    } else if (event.shiftKey && this.shiftUps.hasOwnProperty(event.keyCode)) {
      newChar = this.shiftUps[event.keyCode];
    } else {
      newChar = String.fromCharCode(keyCode);
    }
    const stringToCheck: string =
      current.substring(0, selectionStart) +
      newChar +
      current.substring(selectionEnd, current.length);
    if (stringToCheck && regex.exec(stringToCheck) == null) {
      event.preventDefault();
    }
  }

  @HostListener('keyup', ['$event'])
  onKeyUp(event: KeyboardEvent) {
    const regex: RegExp =
      this.regexPattern !== undefined
        ? new RegExp(this.regexPattern)
        : new RegExp('');
    if (event.keyCode === 86 && event.ctrlKey) {
      const pastedText = this.el.nativeElement.value;
      if (pastedText && !String(pastedText).match(regex)) {
        this.el.nativeElement.value = '';
      }
    }
  }

  @HostListener('paste', ['$event'])
  onPaste(event: ClipboardEvent) {
    const regex: RegExp =
      this.regexPattern !== undefined
        ? new RegExp(this.regexPattern)
        : new RegExp('');
    let pastedText: string;
    if (
      this.document.parentWindow &&
      this.document.parentWindow.clipboardData &&
      this.document.parentWindow.clipboardData.getData
    ) {
      // IE
      pastedText = this.document.parentWindow.clipboardData.getData('Text');
    } else if (event.clipboardData && event.clipboardData.getData) {
      pastedText = event.clipboardData.getData('text/plain');
    }
    if (pastedText && !String(pastedText).match(regex)) {
      event.preventDefault();
    }
  }
}
