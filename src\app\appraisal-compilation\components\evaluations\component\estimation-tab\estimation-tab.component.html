<!-- Suddivise parti differenti tra ndg corporate ed individuals tramite utilizzo dell'appAuthKey -->
<form #f="ngForm" novalidate (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>{{'UBZ.SITE_CONTENT.1110110100' | translate }}
          <span class="state green"></span>
        </a>
      </h4>
    </div>
    <div id="collapseFour" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingFour">
      <div class="panel-body">
          <table class="uc-table">
            <thead>
              <tr>
                <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.101000110' | translate }}</th>
                <th scope="col" class="col-sm-3" *appAuthKey="'UBZ_CONSISTENCY_CORPORATE_SURFACE'">{{'UBZ.SITE_CONTENT.101001000' | translate }}</th>
                <th scope="col" class="col-sm-3" *appAuthKey="'UBZ_CONSISTENCY_INDIVIDUAL_QUANTITY'">{{'UBZ.SITE_CONTENT.10010001100' | translate }}</th>
                <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.101001111' | translate }}</th>
                <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.101010001' | translate }}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let model of list">
                <td attr.data-label="{{'UBZ.SITE_CONTENT.101000110' | translate }}" style="text-align: left">{{ (consistencyTypes && consistencyTypes[model.destinazioneUso]) ? (consistencyTypes[model.destinazioneUso].translationCod | translate) : '' }}</td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.101001000' | translate }}" style="text-align: right">{{ model.supCommerciale | number:'1.2-2' }}</td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.101001111' | translate }}" style="text-align: right">{{model.valComStatFin | number:'1.2-2'}}</td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.101010001' | translate }}" style="text-align: right">{{model.valCau | number:'1.2-2'}}</td>
              </tr>
            </tbody>
            <tfoot>
              <tr>
                <td style="text-align: right"><strong>{{'UBZ.SITE_CONTENT.101010010' | translate }}:</strong></td>
                <!-- Per individual bisogna  mostrare la quantità scelta (mq / a corpo) -->
                <!-- <td style="text-align: right" *appAuthKey="'UBZ_CONSISTENCY_INDIVIDUAL_QUANTITY'"><strong>{{ model.quantity }}:</strong></td> -->
                <td></td>
                <td style="text-align: right">{{ fields?.totCom | number:'1.2-2' }}</td>
                <td style="text-align: right">{{ fields?.totCau | number:'1.2-2' }}</td>
                <td></td>
              </tr>
            </tfoot>
          </table>

          <div class="panel-box">
            <div class="row">
                <div class="col-sm-4" *appAuthKey="'UBZ_APPRAISAL.NOT_EPC'">
                  <label>{{'UBZ.SITE_CONTENT.101010011' | translate }}</label>
                  <label>{{fields?.valReal | number:'1.2-2'}}</label>
                </div>
                <div class="col-sm-4">
                  <label>{{'UBZ.SITE_CONTENT.101010100' | translate }}</label>
                  <label>{{fields?.valCos | number:'1.2-2'}}</label>
                </div>
                <div class="col-sm-4" *ngIf="!(opType === 'EDI') && !isTemplateUpdate">
                  <label>{{'UBZ.SITE_CONTENT.101010101' | translate }}</label>
                  <label>{{fields?.valActualStat | number:'1.2-2'}}</label>
                </div>
            </div>
          </div>
      </div>
    </div>
  </accordion-group>
</form>
