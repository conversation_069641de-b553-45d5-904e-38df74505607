import { Component, OnInit, ViewChild } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { GuaranteeFractionationService } from '../../services/guarantee-fractionation.service';
import { Location } from '@angular/common';
import { GuaranteeTransferService } from '../../../guarantee-transfer';
import { GuaranteeInfoService } from '../../../appraisal-request/services/guarantee-info/guarantee-info.service';

@Component({
  selector: 'app-guarantee-summary',
  templateUrl: './guarantee-summary.component.html',
  styleUrls: ['./guarantee-summary.component.css'],
  providers: [GuaranteeTransferService, GuaranteeInfoService]
})
export class GuaranteeSummaryComponent implements OnInit {
  @ViewChild('modal') modal;
  modalIsOpen: boolean;
  modalAsset: any;
  guarantees: any[] = [];
  assets: any[] = [];
  summaryObject: any;
  isDisabled = false;
  hasSaved = false;
  alreadyProcessed = false;

  constructor(
    public guaranteeFractionationService: GuaranteeFractionationService,
    private guaranteeTransferService: GuaranteeTransferService,
    private guaranteeInfoService: GuaranteeInfoService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private location: Location
  ) {}

  ngOnInit() {
    // Se non è presente la lista degli asset selezionati nello step precedente si esce dal wizard
    
    setTimeout(() => (this.guaranteeFractionationService.wizardStep = 4), 10);
    this.activatedRoute.parent.params.subscribe((params: Params) => {
      this.guaranteeFractionationService.ndg = params['ndg'];
      this.guaranteeFractionationService.familyAsset = params['familyAsset'];
      this.guaranteeFractionationService.setCurrentStep('UBZ-FRG-SUM');   
      this.guaranteeFractionationService.setActiveStep('UBZ-FRG-SUM');
      this.summaryObject = this.guaranteeFractionationService.summaryObject ? this.guaranteeFractionationService.summaryObject : [];                  
      this.hasSaved = true;
      this.isDisabled = false;
      if (this.summaryObject.length === 0) {
        this.alreadyProcessed = true;
      } else {
        this.guarantees = this.guaranteeFractionationService.guaranteesStored ? this.guaranteeFractionationService.guaranteesStored : [];            
        this.assets = this.guaranteeFractionationService.assetsStored ? this.guaranteeFractionationService.assetsStored : [];               
      }
    });
  }

  /**
   * @function
   * @name canDeactivate
   * @description Metodo invocato dalla guardia deactivate applicata alla rotta del componente
   * Serve a blocare il previous del browser
   */
  canDeactivate(): boolean {
    if (this.location['_platformStrategy'] && this.location['_platformStrategy']['_platformLocation']) {
      if (this.location['_platformStrategy']['_platformLocation'].hash.search('guarantee-on-arrival') !== -1) {
        return false;
      }
    }
    return true;
  }

  public openAssetModal(asset: any) {
    this.modalIsOpen = true;
    this.modalAsset = asset;
  }

  public closeModal() {
    this.modal.hide();
    this.modalIsOpen = false;
    this.modalAsset = null;
  }

  /**
   * @description Termina il processo di frazionamento ed esegue
   * redirect alla home
  **/
  endProcess() {
    // fixme - inserire toastr che segnala termine del processo
    return this.router.navigateByUrl('/');
  }
}
