import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChild
} from '@angular/core';
import { NgForm } from '@angular/forms';
import { GenericTaskService } from '../../../services/generic-task/generic-task.service';
import { ExpertSearchModel } from '../../../model/expert';
import { SearchObj } from '../../../model/send-appraisal.models';

@Component({
  selector: 'app-expert-search-modal',
  templateUrl: './expert-search-modal.component.html',
  styleUrls: ['./expert-search-modal.component.css']
})
export class ExpertSearchModalComponent implements OnInit {
  @Input() positionId: string;
  @Input() idSocPer: number;
  // It can be 'PER' for 'Perito' or 'REV' for 'Revisore'
  @Input() searchType: string;
  @Input() isOpen: boolean;
  @Input() prevExpertChosen: ExpertSearchModel;
  @Input() prevRevisorChosen: ExpertSearchModel;
  @Output() modalClosed: EventEmitter<any> = new EventEmitter();
  @Output() revisorChosen: EventEmitter<ExpertSearchModel> = new EventEmitter();
  @Output() expertChosen: EventEmitter<ExpertSearchModel> = new EventEmitter();
  @ViewChild(NgForm) form: NgForm;
  searchObj: SearchObj = new SearchObj();
  searchResults: ExpertSearchModel[] = [];
  searchDone = false;
  private selectedExpert: ExpertSearchModel;
  title = {
    PER: 'UBZ.SITE_CONTENT.101111100',
    REV: 'UBZ.SITE_CONTENT.101111101'
  };

  constructor(private _expertAssignmentService: GenericTaskService) {}

  ngOnInit() {}

  hide() {
    this.searchResults = [];
    this.searchObj.firstname = '';
    this.searchObj.lastName = '';
    this.searchObj.ndg = '';
    this.isOpen = false;
    this.modalClosed.emit();
    this.searchDone = false;
  }

  search() {
    if (!this.form || this.form.invalid) {
      return;
    }
    this._expertAssignmentService
      .searchExpert(
        this.positionId,
        this.idSocPer,
        this.searchObj.ndg,
        this.searchObj.firstname,
        this.searchObj.lastName
      )
      .subscribe(res => {
        this.searchResults = res;
        // this.expertChosenBool = false;
        this.searchDone = true;
      });
  }

  // selectExpert(expert: ExpertSearchModel) {
  //   this.selectedExpert = expert;
  //   this.expertChosenBool = true;
  // }

  assign() {
    if (this.searchType === 'PER') {
      this.expertChosen.emit(this.selectedExpert);
    } else {
      this.revisorChosen.emit(this.selectedExpert);
    }
    this.selectedExpert = undefined;
    this.hide();
  }

  // checkIfSelected(expert: ExpertSearchModel): boolean {
  //   if (this.searchType === 'PER') {
  //     if (expert.idAnag === this.prevExpertChosen.idAnag) {
  //       this.selectExpert(expert);
  //       return true;
  //     }
  //   } else {
  //     if (expert.idAnag === this.prevRevisorChosen.idAnag) {
  //       this.selectExpert(expert);
  //       return true;
  //     }
  //   }
  //   return false;
  // }
}
