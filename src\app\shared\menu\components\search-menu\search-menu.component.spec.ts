import { CommonModule } from '@angular/common';
import { EventEmitter, Input, LOCALE_ID, NO_ERRORS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { Http, HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { ToastModule } from 'ng2-toastr';
import { TooltipConfig } from 'ngx-bootstrap';
import { getValueFromObject } from 'ngx-bootstrap/typeahead';
import { CookieModule } from 'ngx-cookie';
import { Observable } from 'rxjs/Observable';
import { of } from 'rxjs/observable/of';
import { ReplaySubject } from 'rxjs/ReplaySubject';
import { APP_CONSTANTS, AppConstants } from '../../../../app.constants';
import { getAlertConfig } from '../../../../app.module';
import { RegistryService } from '../../../../registry/service/registry.service';
import { SearchPageService } from '../../../../search-page/service/search-page.service';
import { AuthKeyDirective } from '../../../access-rights/auth-key.directive';
import { AccessRightsService } from '../../../access-rights/services/access-rights.service';
import { DomainService } from '../../../domain';
import { CustomHttpService } from '../../../http/custom-http.service';
import { MessageService } from '../../../messages/services/message.service';
import { DomainMapToDomainArrayPipe } from '../../../pipes/domain-map-to-domain-array/domain-map-to-domain-array.pipe';
import { PositionService } from '../../../position/position.service';
import { SharedService } from '../../../services/shared.service';
import { UserDataService } from '../../../user-data/user-data.service';
import { MenuService } from '../../services/menu.service';

import { SearchMenuComponent } from './search-menu.component';

class SearchPageServiceStub{
  @Input() isOpened: EventEmitter<boolean>;
}

describe('SearchMenuComponent', () => {
  let component: SearchMenuComponent;
  let fixture: ComponentFixture<SearchMenuComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule, ReactiveFormsModule, FormsModule, CommonModule, HttpModule, TranslateModule.forRoot(), CookieModule.forRoot(), ToastModule.forRoot()],
      declarations: [SearchMenuComponent, DomainMapToDomainArrayPipe],
      providers:
        [MessageService, UserDataService, MenuService, RegistryService, DomainService, SharedService, PositionService,
          { provide: Http, useClass: CustomHttpService },
          { provide: APP_CONSTANTS, useValue: AppConstants },
          { provide: LOCALE_ID, useValue: 'it-IT' },
          { provide: TooltipConfig, useFactory: getAlertConfig },
          
          SearchPageService,
          AccessRightsService
        ],
      schemas: [NO_ERRORS_SCHEMA]
    })
      .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SearchMenuComponent);
    component = fixture.componentInstance;
    component.isOpened = new EventEmitter();
    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });
});

