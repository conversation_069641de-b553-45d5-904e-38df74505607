<!-- FIXME - CENTRALIZZARE MODAL CON CUSTOM MODAL DOPO MARGE CON WAVE2 -->
<!-- LISTA GARANZIE -->
<ng-container *ngIf="alreadyProcessed">
  {{ 'La richiesta è già stata processata' | translate }}
</ng-container>
<ng-container *ngIf="!alreadyProcessed">
<div *ngIf="guarantees?.length" class="row">
  <div class="col-sm-12 form-group">
    <h3>{{ 'UBZ.SITE_CONTENT.10010111000' | translate }}</h3>
    <table class="uc-table" id="table-1">
      <thead>
        <tr>
          <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10001110101' | translate }}</th>
          <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10001110110' | translate }}</th>
          <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10010101' | translate }}</th>
          <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.10010110' | translate }}</th>
          <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10011110' | translate }}</th>
          <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1111110111' | translate }}</th>
          <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1111111000' | translate }}</th>
          <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10011111' | translate }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let row of guarantees; let i = index">
          <td   attr.data-label="{{'UBZ.SITE_CONTENT.10001110101' | translate }}" style="text-align:center">{{row.oldProgCollateral}}</td>
          <td attr.data-label="{{'UBZ.SITE_CONTENT.10001110110' | translate }}">{{row.progCollateral}}</td>
          <td attr.data-label="{{'UBZ.SITE_CONTENT.110001010' | translate }}">{{row.collateralTecForm}}</td>
          <td attr.data-label="{{'UBZ.SITE_CONTENT.10010110' | translate }}">{{row.collateralDesc}}</td>
          <td attr.data-label="{{'UBZ.SITE_CONTENT.11110100' | translate }}">{{row.collateralAmmount | currency:'EUR':true:'1.2-2'}}</td>
          <td>{{ row.percType }}</td>
          <td>{{ row.poolPerc }}</td>
          <td attr.data-label="{{'UBZ.SITE_CONTENT.110001011' | translate }}">{{row.propAssociate}} <button type="button" class="btn btn-clean waves-effect waves-secondary hidden" id="expand-table-1" disabled><i class="fa fa-plus-square-o" aria-hidden="true"></i></button></td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<!-- LISTA ASSET -->
<div class="row">
  <div class="col-sm-12 form-group">
    <h3>{{ 'UBZ.SITE_CONTENT.10001100101' | translate }}</h3>
    <table class="uc-table">
      <thead>
        <tr>
          <th>{{'UBZ.SITE_CONTENT.100100' | translate }}</th>
          <th>{{'UBZ.SITE_CONTENT.111100' | translate }}</th>
          <th>{{'UBZ.SITE_CONTENT.110001' | translate}}</th>
          <th>{{'UBZ.SITE_CONTENT.10000110000' | translate}}</th>
          <th>{{'UBZ.SITE_CONTENT.110011' | translate}}</th>
          <th>{{'UBZ.SITE_CONTENT.101100111' | translate }}</th>
          <th>{{'UBZ.SITE_CONTENT.10000000010' | translate }}</th>
          <th></th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let row of assets; let i = index">
          <td>{{ row.assetId }}</td>
          <td>{{ row.assetDescription }}</td>
          <td>{{ row.reRegistrySheet }}</td>
          <td>{{ row.reRegistryPart }}</td>
          <td>{{ row.reRegistrySub }}</td>
          <td>{{ row.appraisalId }}</td>
          <td>{{ row.lastChange | date:'dd/MM/yyyy' }}</td>
          <td style="text-align: center">
            <i class="icon-detail_page" role="button" (click)="openAssetModal(row)">{{'UBZ.SITE_CONTENT.1000000' | translate }}</i>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<!-- LISTA ID -->
<div class="row">
  <div class="col-sm-12 form-group">
    <h3>>{{ 'UBZ.SITE_CONTENT.10010110110' | translate }}</h3>
    <table class="uc-table">
      <thead>
        <tr>
          <th>{{ 'UBZ.SITE_CONTENT.10010001' | translate }}</th>
          <th>{{ 'UBZ.SITE_CONTENT.10010110100' | translate }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let row of summaryObject">
          <td>{{ row.appraisalIdOld }}</td>
          <td>{{ row.appraisalIdNew }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>


<!-- LISTA GATANZIE PADRE -->
<!-- FIX-ME: translate labels -->
<div class="row">
    <div class="col-sm-12 form-group">
      <h3>>{{ 'GARANZIE PADRE' | translate }}</h3>
      <table class="uc-table">
        <thead>
          <tr>
            <th>{{ 'Progressivo garanzia' | translate }}</th>
            <th>{{ 'Forma tecnica' | translate }}</th>
            <th>{{ 'Descrizione' | translate }}</th>
            <th>{{ 'Importo' | translate }}</th>
            <th>{{ 'Pool capofila' | translate }}</th>
            <th>{{ 'Valore percentuale' | translate }}</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let row of summaryObject">
            <td>{{ row.jointCodOld }}</td>
            <td>{{ row.collatTecForm }}</td>
            <td>{{ row.collatDesc }}</td>
            <td>{{ row.collatAmount }}</td>
            <td>{{ row.poolType }}</td>
            <td>{{ row.poolPerc }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</ng-container>

<div *ngIf="modalIsOpen" class="modal fade" id="nuovo-asset" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #modal="bs-modal" (onHidden)="closeModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.10000000011' | translate }}</h2>
        <button type="button" role="button" class="close pull-right" aria-label="Close" (click)="closeModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <app-application-form
          [idCode]="modalAsset.assetId"
          [page]="'INFO_ASSET_UAM_RIC'"
          [drivers]="{'DD_AST_TIPO': guaranteeFractionationService.familyAsset}"
          [setReadOnly]=true
          [positionId]="modalAsset.appraisalId">
        </app-application-form>
      </div>
    </div>
  </div>
</div>

<app-navigation-footer
  [showPrevious]="false"
  [showCancelButton]="false"
  [saveIsEnable]="!isDisabled && hasSaved"
  confirmButtonString="{{ 'UBZ.SITE_CONTENT.10010110111' | translate }}"
  (saveButtonClick)="endProcess()">
</app-navigation-footer>