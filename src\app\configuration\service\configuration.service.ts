import { Injectable, Inject, EventEmitter } from '@angular/core';
import { Http, Response } from '@angular/http';
import { ActivatedRoute, Params } from '@angular/router';
import { Subscription } from 'rxjs';
import { Observable } from 'rxjs/Observable';
import { APP_CONSTANTS, IAppConstants } from '../../app.constants';
import { SocietyConf } from '../configuration.models';

@Injectable()
export class ConfigurationService {
  private readonly urlMap = {};
  private readonly baseUrl = '/UBZ-ESA-RS/service/configuration/v1/';
  public saveIsEnable = true;
  public undoIsEnabled = false;
  public configurationInfoStore;
  invokeSocietiesFormSubmit = new EventEmitter();
  subscription: Subscription;

  constructor(
    private _http: Http,
    private _activatedRoute: ActivatedRoute,
    @Inject(APP_CONSTANTS) private _constants: IAppConstants
  ) {
    this.urlMap[this._constants.ConfigurationType.SAMPLE_CHECKS] =
      'samplechecks';
    this.urlMap[this._constants.ConfigurationType.EXPERT_ASSIGNMENT] =
      'expertassignment';
    this.urlMap[this._constants.ConfigurationType.APPRAISAL] = 'appraisal';
    this.urlMap[this._constants.ConfigurationType.INDIVIDUAL_ASSIGNMENT] =
      'appraisal/assignment/individual';
    this._activatedRoute.params.subscribe((params: Params) => {
      if (params['configurationCode'] === 'IND') {
        this.saveIsEnable = false;
      }
    });
  }

  public getConfigurationRules(configurationType: string): Observable<any> {
    const url = this.baseUrl + this.urlMap[configurationType];
    return this._http.get(url).map((res) => res.json());
  }

  public saveConfigurationRules(
    configurationType: string,
    toSave: any
  ): Observable<any> {
    const url = this.baseUrl + this.urlMap[configurationType];
    return this._http.post(url, toSave).map((res) => res.json());
  }

  public setSaveButtonState(state: boolean): void {
    this.saveIsEnable = state;
  }
  public setUndoButtonState(state: boolean): void {
    this.undoIsEnabled = state;
  }

  public getSamplecheckTable() {
    const url = `/UBZ-ESA-RS/service/sampleCheck/v1/appraisalEvaluations/questionnaire`;
    return this._http.get(url).map((resp: Response) => {
      return resp.json();
    });
  }
  public saveSamplecheckTable(input: any) {
    const url = `/UBZ-ESA-RS/service/sampleCheck/v1/appraisalEvaluations/questionnaire`;
    return this._http.put(url, input).map((resp: Response) => {
      return resp.json();
    });
  }

  public deleteSociety(expertToDelete: SocietyConf[]): Observable<any> {
    const input: any = {
      experts: expertToDelete,
    };
    const url = this.baseUrl + `appraisal/assignment/individual/remove`;
    return this._http.post(url, input).map((resp: Response) => {
      return resp.json();
    });
  }

  public addNewSociety(expertToAdd: SocietyConf[]): Observable<any> {
    const input: any = {
      experts: expertToAdd,
    };
    const url = this.baseUrl + `appraisal/assignment/individual/add`;
    return this._http.post(url, input).map((resp: Response) => {
      return resp.json();
    });
  }

  public undoConfiguration(): Observable<any> {
    const url = this.baseUrl + `appraisal/assignment/individual/restore/saved`;
    return this._http.post(url, {}).map((resp: Response) => {
      return resp.json();
    });
  }

  public restoreConfiguration(): Observable<any> {
    const url =
      this.baseUrl + `appraisal/assignment/individual/restore/processed`;
    return this._http.post(url, {}).map((resp: Response) => {
      return resp.json();
    });
  }

  public onIndividualAssignmentSave() {
    this.invokeSocietiesFormSubmit.emit('');
  }
}
