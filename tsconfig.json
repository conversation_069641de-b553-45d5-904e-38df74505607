// Module: esnext permette di eseguire, all'interno dei componenti, import dinamici dei moduli (lazy loading)
// Esempio
// import('moduloEsempio').then(moduloEsempio => {
//  usa il modulo
// });


// {
//   "compileOnSave": false,
//   "module": "esnext",
//   "compilerOptions": {
//     "outDir": "./dist/out-tsc",
//     "sourceMap": true,
//     "declaration": false,
//     "moduleResolution": "node",
//     "emitDecoratorMetadata": true,
//     "experimentalDecorators": true,
//     "baseUrl": "app",
//     "lib": [
//        "es2016"
//     ]
//   }
// }

// test, update the config for better compatibility with Internet Explorer
{
  "compileOnSave": false,
  "compilerOptions": {
    "outDir": "./dist/out-tsc",
    "sourceMap": true,
    "declaration": false,
    "moduleResolution": "node",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "baseUrl": "app",
    "target": "es5",           
    "module": "es2015",        
    "lib": [
      "es2015",                
      "dom"
    ]
  }
}
// test, update the config for better compatibility with Internet Explorer