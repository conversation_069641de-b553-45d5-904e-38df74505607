import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { GuaranteeFractionationService } from '../../services/guarantee-fractionation.service';
import { GuaranteeTransferService } from '../../../guarantee-transfer';
import { GuaranteeInfoService } from '../../../appraisal-request/services/guarantee-info/guarantee-info.service';
import { of } from 'rxjs/observable/of';

@Component({
  selector: 'app-guarantee-on-arrival',
  templateUrl: './guarantee-on-arrival.component.html',
  styleUrls: ['./guarantee-on-arrival.component.css'],
  providers: [GuaranteeTransferService, GuaranteeInfoService]
})

// Garanzie di arrivo: In questa sezione il gestore visualizzerà la lista degli asset selezionati nello step precedente
// e potrà attraverso una Combo box associarlo ai progressivi garanzia NEW.
export class GuaranteeOnArrivalComponent implements OnInit {
  newJointCodList: string[] = new Array();
  associatedJointList: Object = {};
  previousModalIsOpen: boolean;
  assetsList: any[];
  draftButtonCallback = this.saveDraft.bind(this);
  isDisabled = true;
  guaranteeList = [];

  constructor(
    private activatedRoute: ActivatedRoute,
    public guaranteeFractionationService: GuaranteeFractionationService,
    private guaranteeTransferService: GuaranteeTransferService,
    private guaranteeInfoService: GuaranteeInfoService,
    private router: Router) {}

  ngOnInit() {
    setTimeout(() => (this.guaranteeFractionationService.wizardStep = 3), 10);
    
    this.activatedRoute.parent.params.subscribe((params: Params) => {
      this.guaranteeFractionationService.ndg = params['ndg'];
      this.guaranteeFractionationService.familyAsset = params['familyAsset'];
      this.guaranteeFractionationService.setCurrentStep('UBZ-FRG-GDA');
      this.guaranteeFractionationService.getFraWizardData().subscribe(() => {        
        this.isDisabled = this.guaranteeFractionationService.isCurrentStepLocked();
        this.guaranteeInfoService.getGuaranteeListFrg(params['ndg'], params['familyAsset'])
          .subscribe((guaranteeList) => {
            if (guaranteeList) {
              this.guaranteeList = guaranteeList['listColl'] ? guaranteeList['listColl'] : [];
              this.guaranteeTransferService.getAssociatedGuaranteesFrg(params['ndg'], params['familyAsset'], guaranteeList)
                .subscribe((assets) => {
                  this.assetsList = assets.filter((asset) => asset['isSelected'] === 'Y');
                  this.guaranteeFractionationService.getNewProg(params['ndg'], params['familyAsset'])
                    .subscribe(response => {
                      if (response) {
                        this.newJointCodList = response['newJointCod'];
                        this.setAssociatedJoinList();
                      }
                    });
                });
            }
          });
      });
    });
  }

  /**
   * @name setAssociatedJoinList
   * @description Inizializza la struttura di associatedJoinList
   * Per ogni asset viene definito un oggetto con all'interno un boolean per ogni joinCod
   * che identifica se l'asset è stato associato a quella garanzia
   */
  setAssociatedJoinList() {
    this.assetsList.forEach(asset => {
      this.associatedJointList[asset['assetId']] = {};
      for (const cod of this.newJointCodList) {
        this.associatedJointList[asset['assetId']][cod] = false;
      }
    });
  }


  /**
   * @name saveIsEnable
   * @description Definisce l'abilitazione del pulsante per proseguire
   * Pulsante abilitato se per ogni asset è stata selezionata una garanzia
   */
  saveIsEnable(): boolean {    
    if (this.isDisabled || !this.assetsList) {
      return false;
    }
    for (const asset of this.assetsList) {
      if (!asset['jointCod']) {
        return false;
      }
    }
    return true;
  }

  /**
   * @name selectedJointCod
   * @description Restituisce il boolean che indica se la garanzia di jointCod è associata
   * all'asset di assetId
   * @param assetId Id identificativo dell'asset in questione
   * @param jointCod Codice della garanzia da associare/associata
   */
  selectedJointCod(assetId: number, jointCod: string): boolean {
    return this.associatedJointList[assetId][jointCod];
  }

  /**
   * @name associateJoinCod
   * @description Esegue il toogle dell'associazione garanzia all'asset in questione
   * @param assetId Id identificativo dell'asset in questione
   * @param jointCod Codice della garanzia da associare/associata
   */
  associateJoinCod(assetId: number, jointCod: string): void {
    this.associatedJointList[assetId][jointCod] = !this.associatedJointList[assetId][jointCod];
  }

  /**
   * @name confirmUndo
   * @description Pulsante di conferma per tornare alla pagina precedente
  **/
  confirmUndo() {
    this.previousModalIsOpen = false;
    this.previous();
  }

  /**
   * @name previous
   * @description Esegue redirect verso stato precedente
  **/
  previous() { 
    this.guaranteeFractionationService.wizardInvalidPreviousStep().subscribe(() => {      
      this.guaranteeFractionationService.goToPrevStep();
    });    
  }

  saveDraft() {
    return this.saveFrazData().switchMap((response: any) => {
      return of(true);
    });
  }

  draftSaved() {
    this.router.navigateByUrl('index');
  }
  
  modify() {
    this.guaranteeFractionationService.wizardInvalidStep().subscribe(() => {      
      this.guaranteeFractionationService.getFraWizardData().subscribe(() => {   
        setTimeout(() => this.isDisabled = this.guaranteeFractionationService.isCurrentStepLocked(), 10);
      });
    });
  }

  /**
   * @name save
   * @description Esegue salvataggio dei dati in pagina e redirect
   * verso stato successivo
  **/
  save() {
    this.saveFrazData().subscribe(response => {
      this.guaranteeFractionationService.getFrazSummary(this.guaranteeFractionationService.ndg, this.guaranteeFractionationService.familyAsset)
        .subscribe((summary) => {
          this.guaranteeFractionationService.summaryObject = summary['collateralSummary'] ? summary['collateralSummary'] : [];
          const assetsToSave = [];
          this.assetsList.forEach((asset) => {
            if (asset['isSelected'] === 'Y') {
              assetsToSave.push(asset);
            }
          });          
          this.guaranteeFractionationService.assetsStored = assetsToSave;
          this.guaranteeFractionationService.guaranteesStored = this.guaranteeList;
          this.guaranteeFractionationService.goToNextStep();
          // @todo: rimuovere quanto segue e scommentare istruzione precedente
          // this.guaranteeFractionationService.wizardSetNextStep().subscribe(() => {
          //   this.guaranteeFractionationService.setCurrentStep('UBZ-FRG-SUM');
          //   this.guaranteeFractionationService.wizardSetNextStep().subscribe(() => {
          //     const targetUrl = `guaranteeFractionation/${this.guaranteeFractionationService.familyAsset}/${this.guaranteeFractionationService.ndg}/summary`;
          //     this.router.navigate([targetUrl]);
          //   });
          // });  
        });
    });
  }
  
  saveFrazData() {
    const data = {
      ndg: this.guaranteeFractionationService.ndg,
      type: this.guaranteeFractionationService.familyAsset,
      assets:[]
    }; 
    this.assetsList.forEach((asset) => {
      if (asset['isSelected'] === 'Y') {
      let newAsset = {
          assetId: asset.assetId,
          newProgCollateral: asset.jointCod
        }
        data.assets.push(newAsset);
      }
    });
    return this.guaranteeFractionationService.frazSaveAssetInfo(data).switchMap((response: any) => {
      return of(true);
    });

  }
}
