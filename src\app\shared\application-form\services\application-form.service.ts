import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';

import { Observable } from 'rxjs/Observable';
import { SharedService } from '../../services/shared.service';

import { ApplicationForm } from '../model/application-form';

@Injectable()
export class ApplicationFormService {
  private cachedAppForms: { [name: string]: Observable<any> } = {};

  constructor(private http: Http, private sharedService: SharedService) {}

  retrieveApplicationForm(
    idCode: string,
    ndg: string,
    page: string,
    drivers: Object,
    positionId: string,
    useCache = false // To do
  ): Observable<ApplicationForm> {
    const input = {
      ndg,
      page,
      drivers,
      positionId,
    };
    if (idCode !== null && idCode !== undefined) {
      input['idCode'] = `${idCode}`;
    }

    const stableHashVal: string = this.sharedService.stableHash(input);
    const cachedProp = this.cachedAppForms[stableHashVal];

    if (useCache && cachedProp) {
      return cachedProp;
    }

    const url = '/UBZ-ESA-RS/service/apfService/apf/forms';

    return (this.cachedAppForms[stableHashVal] = this.http
      .post(url, input)
      .map((resp: Response) => resp.json())
      .shareReplay(1));
  }
}
