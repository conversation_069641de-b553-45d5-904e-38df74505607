import { Component, OnInit, Input, Inject } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { GenericTaskService } from '../../services/generic-task/generic-task.service';
import { PositionService } from '../../../shared/position/position.service';
import { AppraisalDetail } from '../../model/appraisal-detail';
import { UserDataService } from '../../../shared/user-data/user-data.service';
import { UserData } from '../../../shared/user-data/user-data';

@Component({
  selector: 'app-site-inspection-date',
  templateUrl: './site-inspection-date.component.html',
  styleUrls: ['./site-inspection-date.component.css']
})

export class SiteInspectionDateComponent implements OnInit {
  @Input() positionId: string;
  appraisalDetail: AppraisalDetail;
  isChangeAppointmentAllowed: boolean;
  isMacroProcessCte: boolean;
  taskId: string;
  taskCod: string;

  constructor(
    private activatedRoute: ActivatedRoute,
    private genericTaskService: GenericTaskService,
    private userDataService: UserDataService,
    private positionService: PositionService
  ) {
    this.appraisalDetail = new AppraisalDetail();
  }

  ngOnInit() {
    this.activatedRoute.params
      .switchMap((params: Params) => {
        this.taskId = params['taskId'];
        this.taskCod = params['taskCod'];
        return Observable.forkJoin(
          this.positionService.getAppraisalAssignment(this.positionId),
          this.positionService.getAppraisalInfo(this.positionId)
        )
      }).subscribe(res => {
        this.appraisalDetail = res[0];
        if (res[1] && res[1].appraisal) {
          this.isMacroProcessCte = res[1].appraisal.macroProcess === 'CTE';
          this.isChangeAppointmentAllowed =
            this.checkIfChangeAppointmentIsAllowed(res[1].appraisal);
        }
      });
  }

  public taskLocked() {
   this.userDataService.getAll().subscribe((res:UserData)=>{
    this.genericTaskService.taskLockingUser=res.username
   })
  }

  public tasklockedByOtherUser(user: string) {
    this.genericTaskService.taskLockingUser = user;
  }

  public taskUnlocked() {
    this.genericTaskService.taskLockingUser = undefined;
  }
  
  private checkIfChangeAppointmentIsAllowed(appraisal): boolean {
    return (this.taskId !== '-' && this.taskCod !== '-') &&
      appraisal && appraisal.originProcess !== 'MIG' &&
      (appraisal.macroProcess !== 'MLP' || appraisal.macroProcess !== 'FIP');
  }
}
