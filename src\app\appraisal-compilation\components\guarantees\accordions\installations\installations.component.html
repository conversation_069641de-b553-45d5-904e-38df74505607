<form #f="ngForm" novalidate (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
          <span>{{'UBZ.SITE_CONTENT.100110000' | translate }}</span>
          <span class="state" [ngClass]="{'green' : (pageIsValid), 'red' : !(pageIsValid)}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <table class="table">
            <tbody>
              <tr *ngFor="let inst of pageContent">
                <td class="col-sm-3">{{ (systemDomain && systemDomain[inst.systemType] ) ? (systemDomain[inst.systemType].translationCod | translate) : '' | translate}} </td>
                <td class="col-sm-3"> {{inst.certificateDate }} </td>
                <td class="col-sm-3"> {{inst.certificateProtocol}} </td>
                <td>
                  <ng-container *appAuthKey="'UBZ_INSTALLATIONS_DELETE'">
            			  <button type="button" class="btn btn-empty waves-effect waves-secondary" (click)="openCustomModal('delete', inst)">
                      <i class="fa fa-trash-o" aria-hidden="true"></i>
            			  </button>
                  </ng-container>
                  <ng-container *appAuthKey="'UBZ_INSTALLATIONS_MODIFY'">
            			  <button type="button" class="btn btn-empty waves-effect waves-secondary" (click)="openCustomModal('modify', inst)">
                      <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
            			  </button>
                  </ng-container>
          			</td>
              </tr>
            </tbody>
          </table>
          <div class="row">
            <div class="col-sm-12">
              <ng-container *appAuthKey="'UBZ_INSTALLATIONS_ADD'">
                <button type="button" class="btn btn-empty" (click)="openCustomModal('add')">
                  <i class="fa fa-plus"></i>
                  <span>{{'UBZ.SITE_CONTENT.1000010010' | translate }}</span>
                </button>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </div>
  </accordion-group>
</form>

<!-- CUSTOM MODAL centralizza la gestione delle modal in aggiunta, modfifica e cancellazione per il component -->
<app-custom-modal 
  [modalType] = "modalType"
  [isOpen] = "customModalIsOpen"
  [largeModalFlag] = "largeModalFlag"
  [headerTitle] = "headerTitle"
  [positionId]="positionId" 
  [idCode]="selectedPageElement ? selectedPageElement.systemId : ''"   
  [apfString] = "apfString"
  [buttonTitle] = "buttonTitle"
  [disabledFlag] = "disabledFlag"  
  (modalSubmit) = "handleSubmitCustomModal($event)"
  (modalClose)="closeCustomModal()">
</app-custom-modal>