/* Silos iframe styles for all browsers */
.silos-iframe-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: white;
  z-index: 9999;
  display: flex;
  flex-direction: column;
}

.silos-iframe-header {
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 60px;
}

.silos-iframe-header h3 {
  margin: 0;
  color: #495057;
  font-size: 18px;
}

.silos-iframe-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.browser-notice {
  background: #d1ecf1;
  color: #0c5460;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  border: 1px solid #bee5eb;
}

.silos-iframe {
  flex: 1;
  width: 100%;
  border: none;
  background: white;
}

.btn {
  padding: 6px 12px;
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.btn-secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .silos-iframe-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    min-height: auto;
  }

  .silos-iframe-controls {
    width: 100%;
    justify-content: space-between;
  }

  .browser-notice {
    font-size: 11px;
    padding: 4px 8px;
  }
}