<form #f="ngForm" (click)="_accordionAPFService.handleOpenProperty(group, f)">
<accordion-group #group class="panel">
  <div accordion-heading class="acc-note-headline" role="tab">
    <h4 class="panel-title">
      <a role="button">
        <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
        {{'UBZ.SITE_CONTENT.100011110' | translate }}
        <span class="state" [ngClass]="{'red': f.invalid, 'green': f.valid}"></span>
      </a>
    </h4>
  </div>
  <div class="panel-collapse collapse in" role="tabpanel">
    <div class="panel-body">
        <table class="uc-table">
          <thead>
            <tr>
              <th scope="col" class="col-sm-4">{{'UBZ.SITE_CONTENT.11010000' | translate }}</th>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.100011111' | translate }}</th>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.100100000' | translate }}</th>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.100100001' | translate }}</th>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.100100010' | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of list; let i = index">
              <td><input class="form-control" type="text" name="tip-{{i}}" [(ngModel)]="item.estimateType" required=""></td>
              <td><input class="form-control" type="number" name="num-{{i}}" [(ngModel)]="item.estimateNum" required=""></td>
              <td><input class="form-control" type="number" name="sup-{{i}}" [(ngModel)]="item.mqSurface" required=""></td>
              <td><input class="form-control" type="number" name="valMq-{{i}}" [(ngModel)]="item.maxEuroMqTransfer" required=""></td>
              <td><input class="form-control" type="number" name="val-{{i}}" [(ngModel)]="item.maxEuroTransfer" required=""></td>
            </tr>
          </tbody>
          <tfoot class="table-foot">
            <tr>
              <td colspan="4" class="text-center"><strong>{{'UBZ.SITE_CONTENT.100100011' | translate }}</strong></td>
              <td class="text-center"><strong>{{ calculateTotal() | currency:'EUR':true:'1.2-2' }}</strong></td>
            </tr>
          </tfoot>
        </table>
    </div>
  </div>
</accordion-group>
</form>

<div>
  <ng-content></ng-content>
</div>
