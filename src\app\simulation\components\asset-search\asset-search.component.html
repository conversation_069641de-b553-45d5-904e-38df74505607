<div class="row step-navigation">
  <div class="col-sm-12 btn-set">
    <ng-container *appAuthKey="'UBZ_ASSET.SEARCH_UNDO'">
      <button type="button" class="btn btn-empty waves-effect waves-secondary pull-right" id="cancel-esistente-creation" (click)="returnToAssetList()"><i class="fa fa-times" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.1111001' | translate }}</button>
    </ng-container>
  </div>
</div>


<!-- SEZIONE DELLA RICERCA-->
<section id="search-for-esistente" class="form-variables" style="display: block">
  <form>
  <div class="row">
    <div class="col-sm-12 form-group">
      <h3>{{'UBZ.SITE_CONTENT.100010' | translate }}</h3>
      <p>{{'UBZ.SITE_CONTENT.100011' | translate }}</p>
    </div>
  </div>

  <div class="row">
    <div class="col-sm-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.100100' | translate }}</label>
      <input [(ngModel)]="assetSearchObject.filter.resItemId" type="text" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.1001000001' | translate }}" name="idAsset" appOnlyNumbers/>
    </div>
    <div class="col-sm-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.100101' | translate }}</label>
      <div class="custom-select">
        <select [(ngModel)]="assetSearchObject.filter.assetType" class="form-control" name="famiglia-asset-esistente" disabled>
          <option [ngValue]="undefined" selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
          <option *ngFor="let row of (getDomain('domainAssetType') | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
        </select>
      </div>
    </div>
    <div class="col-sm-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.100110' | translate }}</label>
      <div class="custom-select">
        <select [(ngModel)]="assetSearchObject.filter.category" class="form-control categoria" name="categoria">
          <option [ngValue]="undefined" selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
          <option *ngFor="let row of (getDomain('domainCategoryAsset' + assetSearchObject.filter.assetType ) | domainMapToDomainArray | sortCategoryType )" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
        </select>
      </div>
    </div>
    <div class="col-sm-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.100111' | translate }}</label>
      <input [(ngModel)]="assetSearchObject.filter.originNdg" type="text" name="originNdg" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.1001000010' | translate }}" appOnlyNumbers maxlength="16"/>
    </div>
  </div>

  <div class="row">
  	<div class="col-sm-3 form-group">
  		<label>{{'UBZ.SITE_CONTENT.101000' | translate }}</label>
  		<div class="custom-select">
  			<select [(ngModel)]="assetSearchObject.filter.province" #provinceBox name="provincia" class="form-control address-primary" [disabled]="isForeignState" (change)="setProvince(provinceBox.value)">
  			  <option [ngValue]="undefined" selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
  				<option *ngFor="let row of (getDomain('domainProvince') | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
  			</select>
  		</div>
  	</div>
  	<div class="col-sm-2 form-group">
  		<label>{{'UBZ.SITE_CONTENT.101001' | translate }}</label>
  		<div class="custom-select">
  			<select [(ngModel)]="homeCity" name="homeCity" class="form-control address-primary" [disabled]="isForeignState">
  			  <option [ngValue]="undefined" selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
  				<option *ngFor="let row of (getDomain('domainCity' + provinceBox.value ) | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod}}</option>
  			</select>
  		</div>
  	</div>
  	<div class="col-sm-2 form-group">
  		<label>{{'UBZ.SITE_CONTENT.101010' | translate }}</label>
      <input [(ngModel)]="assetSearchObject.filter.zipCod" type="text" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.1001000011' | translate }}" name="cap" [disabled]="isForeignState" appOnlyNumbers maxlength="5"/>
  	</div>
  	<div class="col-sm-3 form-group">
  		<label>{{'UBZ.SITE_CONTENT.101011' | translate }}</label>
  		<div class="custom-select">
  			<input [(ngModel)]="homeAddress" name="homeAddress" type="text" class="form-control address-primary" placeholder="{{'UBZ.SITE_CONTENT.1001000100' | translate }}" [disabled]="isForeignState"/>
  		</div>
  	</div>
  	<div class="col-sm-2 form-group">
  		<label>{{'UBZ.SITE_CONTENT.101100' | translate }}</label>
  		<input [(ngModel)]="assetSearchObject.filter.streetNum" name="streetNum" type="text" class="form-control address-primary" placeholder="{{'UBZ.SITE_CONTENT.100011111' | translate }}" [disabled]="isForeignState"/>
  	</div>
  </div>

  <div class="row">
    <div class="col-sm-3 form-group">
      <label></label>
      <div class="custom-checkbox">
        <input #foreignStateCheckBox type="checkbox" id="ub9" name="ubicazione" class="checkbox" [checked]="isForeignState" (change)="setIsForeignState(foreignStateCheckBox.checked)">
        <label for="ub9">{{'UBZ.SITE_CONTENT.101101' | translate }}</label>
      </div>
    </div>
    <ng-container *ngIf="isForeignState">
      <div class="col-sm-3 form-group nazione">
        <label>{{'UBZ.SITE_CONTENT.101110' | translate }}</label>
        <div class="custom-select">
          <select [(ngModel)]="assetSearchObject.filter.country" name="country" class="form-control" required>
            <option [ngValue]="undefined" selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
    				<option *ngFor="let row of (getDomain('domainNation') | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
          </select>
        </div>
      </div>
      <div class="col-sm-3 form-group city">
        <label>{{'UBZ.SITE_CONTENT.101111' | translate }}</label>
        <input [(ngModel)]="assetSearchObject.filter.city" name="foreignCity" type="text" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.1001000101' | translate }}" />
      </div>
      <div class="col-sm-3 form-group address-secondary">
        <label>{{'UBZ.SITE_CONTENT.101011' | translate }}</label>
        <input [(ngModel)]="assetSearchObject.filter.address" name="foreignAddress" type="text" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.1001000110' | translate }}" />
      </div>
    </ng-container>
  </div>


  <div class="row imm-esistente" >
    <div class="col-sm-3 form-group">
     <label>{{'UBZ.SITE_CONTENT.110000' | translate }}</label>
     <input [(ngModel)]="assetSearchObject.filter.buildYear" name="buildYear" type="text" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.1001000111' | translate }}" appOnlyNumbers maxlength="4" />
    </div>
    <div class="col-sm-2 form-group">
      <label>{{'UBZ.SITE_CONTENT.110001' | translate }}</label>
      <input [(ngModel)]="assetSearchObject.filter.reRegistrySheet" name="reRegistrySheet" type="text" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.1001001000' | translate }}" />
    </div>
    <div class="col-sm-2 form-group">
      <label>{{'UBZ.SITE_CONTENT.110010' | translate }}</label>
      <input [(ngModel)]="assetSearchObject.filter.reRegistryPart" name="reRegistryPart" type="text" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.1001001001' | translate }}" />
    </div>
    <div class="col-sm-2 form-group">
      <label>{{'UBZ.SITE_CONTENT.110011' | translate }}</label>
      <input [(ngModel)]="assetSearchObject.filter.reRegistrySub" name="reRegistrySub" type="text" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.1001001010' | translate }}" [disabled]="isSubaltern"/>
    </div>
    <div class="col-sm-3 form-group">
      <label></label>
      <div class="custom-checkbox">
        <input #subalternCheckBox type="checkbox" name="subalterno" id="sub14" class="checkbox" [checked]="isSubaltern" (change)="setIsSubaltern(subalternCheckBox.checked)">
        <label for="sub14">{{'UBZ.SITE_CONTENT.110100' | translate }}</label>
      </div>
    </div>
  </div>

  <div class="row imm-registration-esistente" style="display: none">
    <div class="col-sm-2 form-group">
      <label></label>
      <div class="custom-checkbox">
        <input type="checkbox" id="bene-usato1" name="bene-usato" class="checkbox">
        <label for="bene-usato1">{{'UBZ.SITE_CONTENT.110101' | translate }}</label>
      </div>
    </div>
    <div class="col-sm-3 form-group bene-usato hidden">
      <label>{{'UBZ.SITE_CONTENT.110110' | translate }}</label>
      <input type="text" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.1001000111' | translate }}"/>
    </div>
  </div>

  <!--  PULSANTE CERCA ASSET-->
    <div class="row">
      <div class="col-sm-12 btn-set">
        <ng-container *appAuthKey="'UBZ_ASSET.SEARCH_FIND'">
          <button type="button" id="searchfor" class="btn btn-primary waves-effect pull-right" (click)="findAssets()">{{'UBZ.SITE_CONTENT.110111' | translate }}</button>
        </ng-container>
        <ng-container *appAuthKey="'UBZ_ASSET.SEARCH_RESET'">
          <button *ngIf="isResearchDone" type="button" id="reset" class="btn btn-secondary waves-effect waves-secondary pull-right" (click)="startPage()">{{'UBZ.SITE_CONTENT.111000' | translate }}</button>
        </ng-container>
      </div>
    </div>

<!--  SEZIONE IN CUI CARICARE I RISULTATI DELLA RICERCA-->
   <section *ngIf="isResearchDone" id="search-asset-results">
     <ng-container *ngIf="assetSearchResponseObject.positions.length !== 0">
      <div class="row">
        <div class="col-sm-12">
          <br/>
          <p>{{'UBZ.SITE_CONTENT.111001' | translate }}
            <strong>
              {{this.searchedAssetDetails['assetType'] | translate}}
              <span *ngIf="searchedAssetDetails['assetId']">, {{'UBZ.SITE_CONTENT.100100' | translate }} {{ searchedAssetDetails['assetId'] | translate }}</span>
              <span *ngIf="searchedAssetDetails['assetCategory']">, {{ searchedAssetDetails['assetCategory'] | translate }}</span>
              <span *ngIf="searchedAssetDetails['originNdg']">, {{ searchedAssetDetails['originNdg'] | translate }}</span>
              <span *ngIf="searchedAssetDetails['assetProvince']">, {{ searchedAssetDetails['assetProvince'] | translate }}</span>
              <span *ngIf="searchedAssetDetails['assetCity']">, {{ searchedAssetDetails['assetCity'] | translate }}</span>
              <span *ngIf="searchedAssetDetails['assetForeignCity']">, {{ searchedAssetDetails['assetForeignCity'] }}</span>
              <span *ngIf="searchedAssetDetails['zipCod']">, {{ searchedAssetDetails['zipCod'] | translate }}</span>
              <span *ngIf="searchedAssetDetails['address']">, {{ searchedAssetDetails['address'] | translate }}</span>
              <span *ngIf="searchedAssetDetails['foreignAddress']">, {{ searchedAssetDetails['foreignAddress'] | translate }}</span>
              <span *ngIf="searchedAssetDetails['streetNum']">, {{ searchedAssetDetails['streetNum'] | translate }}</span>
              <span *ngIf="searchedAssetDetails['assetNation']">, {{ searchedAssetDetails['assetNation'] | translate }}</span>
              <span *ngIf="searchedAssetDetails['buildYear']">, {{ searchedAssetDetails['buildYear'] | translate }}</span>
              <span *ngIf="searchedAssetDetails['reRegistrySheet']">, {{ 'UBZ.SITE_CONTENT.110001' | translate }} {{ searchedAssetDetails['reRegistrySheet'] | translate }}</span>
              <span *ngIf="searchedAssetDetails['reRegistryPart']">, {{ 'UBZ.SITE_CONTENT.110010' | translate }} {{ searchedAssetDetails['reRegistryPart'] | translate }}</span>
              <span *ngIf="searchedAssetDetails['reRegistrySub']">, {{ 'UBZ.SITE_CONTENT.110011' | translate }} {{ searchedAssetDetails['reRegistrySub'] | translate }}</span>
            </strong>
          </p>
        </div>
      </div>

      <div class="row step-navigation">
        <div class="col-sm-6">
          <div class="custom-checkbox">
            <input type="checkbox" id="selectall" class="checkbox" [checked]="selectAll" (change)="setImportedStatusForAllAssets()">
            <label for="selectall">{{'UBZ.SITE_CONTENT.111010' | translate }}</label>
          </div>
          <ng-container *appAuthKey="'UBZ_ASSET.SEARCH_IMPORT.ALL'">
            <button *ngIf="existAnAssetToImport()" id="importa" type="button" class="btn btn-empty waves-effect waves-secondary" (click)="importAllSelectedAsset()"><i class="icon-note"></i></button>
          </ng-container>
        </div>
        <div class="col-sm-6 text-right">
          {{'UBZ.SITE_CONTENT.111011' | translate }} <strong>{{assetSearchResponseObject.count}}</strong> {{'UBZ.SITE_CONTENT.1111010' | translate }}
        </div>
      </div>

      <div class="row">
        <div class="col-sm-12">
          <br/>
          <table class="uc-table import-datatable">
            <thead>
              <tr>
                <th scope="col" class="col-sm-1 checkbox-col"></th>
                <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.100100' | translate }}</th>
                <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.111100' | translate }}</th>
                <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.100101' | translate }}</th>
                <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.100110' | translate }}</th>
                <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.111110' | translate }}</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let row of assetSearchResponseObject.positions; let ind = index">
                <td data-label="">
                  <div class="custom-checkbox">
                    <input class="custom-checkbox" type="checkbox" id="{{row.resItemId}}" name="asset-description" [checked]="assetToBeImported[row.resItemId]" (change)="changeCheckedStatus(row.resItemId)" class="checkbox">
                    <label for="{{row.resItemId}}"></label>
                  </div>
                </td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.100100' | translate }}">{{row.resItemId}}</td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.111100' | translate }}">{{row.description}}</td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.100101' | translate }}">{{row.familyAsset}}</td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.100110' | translate }}">{{row.categoryAsset}}</td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.111110' | translate }}">
                  <ng-container *appAuthKey="'UBZ_ASSET.SEARCH_IMPORT'">
                    <button *ngIf="!assetToBeImported[row.resItemId] || (assetToBeImported[row.resItemId] === false)" type="button" class="btn btn-empty pull-right" data-toggle="modal" (click)="importAsset(row.resItemId)"><i class="icon-add"></i>
                      {{'UBZ.SITE_CONTENT.111111' | translate }}
                    </button>
                  </ng-container>
                  <ng-container *appAuthKey="'UBZ_ASSET.SEARCH_DETAIL'">
                    <button type="button" class="btn btn-empty pull-right" data-toggle="modal" data-target="#details" (click)="showDialog(row.resItemId)"><i class="icon-detail_page"></i>
                      {{'UBZ.SITE_CONTENT.1000000' | translate }}
                    </button>
                  </ng-container>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="assetSearchResponseObject.positions.length === 0">
      <br/>
      <div class="row">
        <div class="col-sm-6 text-center">
          <h2>{{'UBZ.SITE_CONTENT.1100110111' | translate }}</h2>
        </div>
      </div>
    </ng-container>
  </section>
</form>

<br/>
<div *ngIf="assetSearchResponseObject.count > 10" class="row">
  <div class="col-sm-6">
    <div class="results">
      <span>{{'UBZ.SITE_CONTENT.1111111' | translate }}</span>
      <div class="custom-select">
        <select class="form-control" [(ngModel)]="assetSearchObject.pageSize" (change)="changePageSize()">
          <option value="10" selected>10 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{assetSearchResponseObject.count}}</option>
          <option *ngIf="assetSearchResponseObject.count > 20" value="20">20 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{assetSearchResponseObject.count}}</option>
          <option *ngIf="assetSearchResponseObject.count > 30" value="30">30 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{assetSearchResponseObject.count}}</option>
          <option *ngIf="assetSearchResponseObject.count > 40" value="40">40 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{assetSearchResponseObject.count}}</option>
          <option *ngIf="assetSearchResponseObject.count > 50" value="50">50 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{assetSearchResponseObject.count}}</option>
          <option *ngIf="assetSearchResponseObject.count <= 50" [ngValue]="assetSearchResponseObject.count"> {{assetSearchResponseObject.count}} {{'UBZ.SITE_CONTENT.10000000' | translate }} {{assetSearchResponseObject.count}}</option>
        </select>
      </div>
    </div>
  </div>
  <div class="col-sm-6" class="pull-right">
    <pagination [boundaryLinks]="true" [directionLinks]="false" [totalItems]="assetSearchResponseObject.count" [ngModel]="assetSearchObject.page" [itemsPerPage]="assetSearchObject.pageSize"
      [maxSize]="10" (pageChanged)="changePage($event)" class="pagination" previousText="&lsaquo;" nextText="&rsaquo;" firstText="&laquo;" lastText="&raquo;"></pagination>
  </div>
</div>

</section>
<div *ngIf="isDialogOpened" [config]="{show: 'true'}" (onHidden)="onHidden()" bsModal #autoShownModal="bs-modal"
     class="modal fade" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.11100101' | translate }}</h2>
        <button type="button" class="close pull-right" aria-label="Close" (click)="hideDialog()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <ng-container *ngIf="wizardCode === 'WRPE'">
          <app-application-form 
            [idCode]="positionId" 
            [page]="'INFO_ASSET_UAM_RIC'" 
            [drivers]="{'DD_AST_TIPO': assetService.familyAssetType}" 
            [setReadOnly]=true 
            [positionId]="simulationId">
          </app-application-form>
        </ng-container>
        <ng-container *ngIf="wizardCode === 'WSIM'">
          <app-application-form 
            [idCode]="positionId" 
            [page]="'INFO_ASSET_UAM'" 
            [drivers]="{'DD_AST_TIPO': assetService.familyAssetType}" 
            [setReadOnly]=true 
            [positionId]="simulationId">
          </app-application-form>
        </ng-container>
      </div>
    </div>
  </div>
</div>
