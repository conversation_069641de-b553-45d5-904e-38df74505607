<div class="row">
  <div class="col-sm-12 section-headline">
    <ng-container *ngIf="subjectType === subjectTypes['SOC']">
      <h1><i class="icon-richiesta_perizia"></i> {{'UBZ.SITE_CONTENT.1100011000' | translate }}</h1>
      <h2>{{ firmDetail.heading }}</h2>
    </ng-container>
    <ng-container *ngIf="subjectType !== subjectTypes['SOC']">
      <h1><i class="icon-richiesta_perizia"></i> {{'UBZ.SITE_CONTENT.1100011001' | translate }}</h1>
      <h2>{{ firmDetail.firstName + ' ' + firmDetail.lastname }}</h2>
    </ng-container>
    <ng-container #breadcrumbsForLoan *appAuthKey="'UBZ_REGISTRY.BREADCRUMBS_LOAN'">
      <section id="breadcrumbs" class="breadcrumbs">
        <div class="row">
          <div class="col-sm-12">
            <ul>
              <li><a role="button" (click)="goToExpertPage()">{{'UBZ.SITE_CONTENT.1100011010' | translate }}</a></li>
              <li *ngIf="subjectType === subjectTypes['SOC'] && !(_registryService.fromExpertFirm)">{{'UBZ.SITE_CONTENT.1100011000' | translate }}</li>
              <li *ngIf="subjectType !== subjectTypes['SOC'] && _registryService.fromExpertFirm">
                <a role="button" (click)="goToFirmDetail(_registryService.lastExpertFirm)">{{'UBZ.SITE_CONTENT.1100011000' | translate }}</a>
              </li>
              <li *ngIf="subjectType !== subjectTypes['SOC']">{{'UBZ.SITE_CONTENT.1100011001' | translate }}</li>
            </ul>
          </div>
        </div>
      </section>
    </ng-container>
    <ng-container #breadcrumbsNotForLoan *appAuthKey="'UBZ_REGISTRY.BREADCRUMBS_NO_LOAN'">
      <section id="breadcrumbs" class="breadcrumbs">
        <div class="row">
          <div class="col-sm-12">
            <ul>
              <li *ngIf="subjectType === subjectTypes['SOC'] && !(_registryService.fromExpertFirm)">{{'UBZ.SITE_CONTENT.1100011000' | translate }}</li>
              <ng-container>
                <li *ngIf="subjectType !== subjectTypes['SOC'] && _registryService.fromExpertFirm">
                  <a role="button" (click)="goToFirmDetail(_registryService.lastExpertFirm)">{{'UBZ.SITE_CONTENT.1100011000' | translate }}</a>
                </li>
              </ng-container>
              <li *ngIf="subjectType !== subjectTypes['SOC']">{{'UBZ.SITE_CONTENT.1100011001' | translate }}</li>
            </ul>
          </div>
        </div>
      </section>
    </ng-container>
  </div>
</div>

<app-expert-actions-handler
  [subjectType]="subjectType"
  [idAnag]="firmId"
  (downloadSupplyContract)="downloadContract()"
  (modalClose)="refreshComponents($event)">
</app-expert-actions-handler>

<accordion>
  <app-expert-firm-generic-info  #expertFirmGenericInfo [idAnag]="firmId" [subjectType]="subjectType" [subSubjectType]="subSubjectType" ></app-expert-firm-generic-info>
  <app-driver-assignment [idAnag]="firmId" [subjectType]="subjectType"></app-driver-assignment>
  <app-expert-certification [anagId]="firmId" *ngIf="subjectType !== subjectTypes['SOC']"></app-expert-certification>
  <app-belonging-board [anagId]="firmId" [subjectType]="subjectType"></app-belonging-board>
  <app-insurance-policy [idAnag]="firmId" [subjectType]="subjectType"></app-insurance-policy>
  <app-expert-id-card [anagId]="firmId" *ngIf="subjectType !== subjectTypes['SOC']"></app-expert-id-card>
  <app-judicial-record [anagId]="firmId" *ngIf="subjectType !== subjectTypes['SOC']"></app-judicial-record>
  <app-associated-expert [anagId]="firmId" *ngIf="subjectType === 'SOC'"></app-associated-expert>
  <app-competence-provinces [anagId]="firmId" *ngIf="subjectType !== 'SOC'"></app-competence-provinces>
</accordion>
<app-expert-appraisals [anagId]="firmId" [appraisalType]="ExpertAppraisalType.COMPLETED" *ngIf="subjectType !== subjectTypes['SOC']"></app-expert-appraisals>
<app-expert-appraisals [anagId]="firmId" [appraisalType]="ExpertAppraisalType.IN_CHARGE" *ngIf="subjectType !== subjectTypes['SOC']"></app-expert-appraisals>
<app-expert-appraisals [anagId]="firmId" [appraisalType]="ExpertAppraisalType.REVOCATED" *ngIf="subjectType !== subjectTypes['SOC']"></app-expert-appraisals>

<div class="row" *appAuthKey="'UBZ_REGISTRY.DETAILS_RATING'">
  <div class="col-sm-12">
    <h3 *ngIf="subjectType === subjectTypes['SOC']">{{'UBZ.SITE_CONTENT.1100011011' | translate }}</h3>
    <h3 *ngIf="subjectType !== subjectTypes['SOC']">{{'UBZ.SITE_CONTENT.1100011100' | translate }}</h3>
    <table class="uc-table" *ngIf="firmDetail?.ratingHistoryList.length > 0; else noRatingHistory">
      <thead>
        <tr>
          <th scope="col">{{'UBZ.SITE_CONTENT.1111110' | translate }}</th>
          <th scope="col">{{'UBZ.SITE_CONTENT.1100011101' | translate }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of firmDetail.ratingHistoryList">
          <td data-label="Data inserimento">{{ item.insertDate | date:'dd/MM/yyyy' }}</td>
          <td data-label="Valore aggiornato">{{ item.ratingExpert }}</td>
        </tr>
      </tbody>
    </table>
    <ng-template #noRatingHistory>
      <div class="Search__NoResults">
        <div class="Search__NoResults__Icon">
          <i class="icon-placeholder_note"></i>
        </div>
        <div class="Search__NoResults__Text">
          <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1100011110' | translate }}</h3>
          <p class="Search__NoResults__Subtitle">
            {{'UBZ.SITE_CONTENT.1100011111' | translate }}
            <span *ngIf="subjectType === subjectTypes['SOC']">{{'UBZ.SITE_CONTENT.1100100000' | translate }}</span>
            <span *ngIf="subjectType !== subjectTypes['SOC']">{{'UBZ.SITE_CONTENT.1100100001' | translate }}</span>
          </p>
        </div>
      </div>
    </ng-template>
  </div>
</div>

<ng-container *appAuthKey="'UBZ_REGISTRY.DETAILS_GRATUIT_PERC'">
  <div class="row" *ngIf="subjectType === 'SOC'">
    <div class="col-sm-12">
      <h3>{{'UBZ.SITE_CONTENT.1100100010' | translate }}</h3>
      <div class="number-ball">
        <div class="content">
          {{ firmDetail.totGratuit }}
        </div>
      </div>
    </div>
  </div>
</ng-container>
