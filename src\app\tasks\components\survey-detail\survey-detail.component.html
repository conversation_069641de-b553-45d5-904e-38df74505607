<div class="panel-collapse collapse in" role="tabpanel">
    <div class="panel-body">
      <div class="row" *ngIf="surveyDetails && surveyDetails['quest'] && surveyDetails['quest'].length > 0">
        <table class="uc-table survey-table" id="table-1">
          <thead>
            <tr>
              <th scope="col" class="col-sm-6 text-left">{{'UBZ.SITE_CONTENT.1100111101' | translate | lowercase}}</th>
              <th scope="col" class="col-sm-6 text-center">{{'UBZ.SITE_CONTENT.1100111110' | translate | lowercase}}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let request of surveyDetails['quest']; let i = index">
              <td attr.data-label="{{'UBZ.SITE_CONTENT.10011101' | translate }}">{{request['question'] | translate | lowercase}}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.10010110' | translate }}" style="text-align: center;">{{request['answare'] | translate | lowercase}}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <ng-container *ngIf="!surveyDetails || !surveyDetails['quest'] || !surveyDetails['quest'].length || surveyDetails['quest'].length === 0">
        <div class="Search__NoResults">
          <div class="Search__NoResults__Icon">
            <i class="icon-placeholder_note"></i>
          </div>
          <div class="Search__NoResults__Text">
            <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.10011011001' | translate }}</h3>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
