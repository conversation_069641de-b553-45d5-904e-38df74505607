import { Injectable } from '@angular/core';
import { Http, Response, ResponseContentType } from '@angular/http';
import { Observable } from 'rxjs/Observable';

import { Simulation } from '../../model/simulation';

@Injectable()
export class GenericInfoService {
  constructor(private http: Http) {}

  getGenericInfo(
    positionId: string,
    wizardCode: string
  ): Observable<Simulation> {
    positionId = encodeURIComponent(positionId);
    const urlMap = {
      WSIM: `/UBZ-ESA-RS/service/simulation/v1/simulation/${positionId}`,
      WRPE: `/UBZ-ESA-RS/service/reqPer/v1/reqAppraisals/${positionId}`
    };
    const url = urlMap[wizardCode];
    return this.http.get(url).map((resp: Response) => resp.json());
  }

  saveGenericInfo(simulation: Simulation, wizardCode: string): Observable<any> {
    const tmpPosId = encodeURIComponent(simulation.positionId);
    const urlMap = {
      WSIM: `/UBZ-ESA-RS/service/simulation/v1/simulation/updateSimulation/${tmpPosId}`,
      WRPE: `/UBZ-ESA-RS/service/reqPer/v1/reqAppraisals/${tmpPosId}`
    };
    const url = urlMap[wizardCode];
    return this.http.put(url, simulation).map((resp: Response) => resp.json());
  }

  retrieveSummaryDocuments(positionId: string): Observable<any[]> {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/document/v1/documents/simulation/${positionId}`;
    return this.http.get(url).map((resp: Response) => resp.json());
  }

  printDocument(positionId: string, docType: string) {
    return this.http
      .get(this.getPrintServiceUrl(positionId, docType), {
        responseType: ResponseContentType.Blob
      })
      .map((resp: Response) => resp.blob());
  }

  getAppraisalTypeFilters(): Observable<any> {
    const url = '/UBZ-ESA-RS/service/conMap/v1/map/appraisalType';
    return this.http.get(url).map((resp: Response) => resp.json());
  }

  getPrintServiceUrl(positionId: string, docType: string) {
    positionId = encodeURIComponent(positionId);
    docType = encodeURIComponent(docType);
    return `/UBZ-ESA-RS/service/print/v1/prints/${positionId}/${docType}/downloadJasPdf`;
  }

  getAppraisalMacroprocessAvailable(externalAppraisalFlag: string, accessPoint: string, posSegment: string): Observable<any> {
    const url = '/UBZ-ESA-RS/service/domain/v1/domains/macroProcess';
    const data = {
      externalAppraisalFlag: externalAppraisalFlag,
      accessPoint: accessPoint,
      posSegment: posSegment
    };
    return this.http.post(url, data).map((resp: Response) => resp.json());
  }
    
}
