<form #f="ngForm" novalidate (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
          <span>{{'UBZ.SITE_CONTENT.1011000111' | translate }}</span>
          <span class="state" [ngClass]="{green: validForm, red: !validForm}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <app-send-appraisal [positionId]="positionId" [socId]="socId" [isForTemplate]="true" (experts)="newEmit($event)"></app-send-appraisal>
        </div>
      </div>
    </div>
  </accordion-group>
</form>
