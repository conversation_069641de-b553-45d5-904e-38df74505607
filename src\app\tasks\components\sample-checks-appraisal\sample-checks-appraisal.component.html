<div class="row controls">
  <div class="col-sm-12 text-right">
      <button  [disabled]="!saveIsEnable" class="btn btn-empty pull-right" type="button" (click)="saveEvaluation()">{{'UBZ.SITE_CONTENT.100001' | translate | uppercase}}</button>
  </div>
  <div class="col-sm-12 form-group">
    <br>
    <table class="uc-table" id="table-1">
      <thead>
        <tr>
          <th scope="col" class="col-sm-2 text-center">{{'UBZ.SITE_CONTENT.1100111101' | translate | uppercase}}</th>
          <th scope="col" class="col-sm-2 text-center" style="width: 100px">{{'UBZ.SITE_CONTENT.1100111110' | translate | uppercase}}(*)</th>
        </tr>
      </thead>
      <tbody>
          <tr *ngFor="let f of page.field">
            <td data-label="Progressivo">{{f.translationCod | translate | lowercase}}</td>
            <td data-label="Descrizione">
              <select class="form-control" [(ngModel)]="f.value" name="select" required (change)="checkSaveEnable()">
                <option *ngFor="let el of (valueDom | domainMapToDomainArray)" value="{{el.domCode}}">{{el.translationCod | translate}}</option>
              </select>
            </td>
          </tr>
      </tbody>
    </table>
  </div>
</div>
