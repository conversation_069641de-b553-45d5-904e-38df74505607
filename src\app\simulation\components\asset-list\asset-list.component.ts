import {
  Component,
  OnInit,
  ViewChildren,
  Query<PERSON>ist,
  ElementRef,
  ChangeDetectorRef
} from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { LandingService } from '../../services/landing/landing.service';
import { AssetService } from '../../services/asset/asset.service';
import { ApplicationFormComponent } from '../../../shared/application-form/components/application-form.component';

import 'rxjs/add/operator/switchMap';

@Component({
  selector: 'app-asset-list',
  templateUrl: './asset-list.component.html',
  styleUrls: ['./asset-list.component.css']
})
export class AssetListComponent implements OnInit {
  @ViewChildren(ApplicationFormComponent)
  applicationFormComponents: QueryList<ApplicationFormComponent>;
  @ViewChildren('saveAsset') saveAsset: QueryList<ElementRef>;

  assets;
  simulationId: string;
  readOnlyAssets: boolean[] = [];
  wizardCode: string;
  selectedAsset: boolean[] = [];
  currentTask = 'UBZ-SIM-AST';
  deleteModalIsOpen = false;
  selectedAssetId: number;

  constructor(
    public assetService: AssetService,
    private activatedRoute: ActivatedRoute,
    public _landingService: LandingService,
    private router: Router,
    private cdRef: ChangeDetectorRef
  ) {}

  ngOnInit() {
    setTimeout(() => {
      this.assetService.saveIsEnable = true;
      this.cdRef.detectChanges();
    }, 0);
    this.activatedRoute.parent.params.subscribe((params: Params) => {
      this.simulationId = params['positionId'];
      this.wizardCode = params['wizardCode'];
      this.assetService
        .getAssetList(this.simulationId, this.wizardCode)
        .subscribe(x => {
          for (const index in this.assetService.listObjcet) {
            if (this.assetService.listObjcet.hasOwnProperty(index)) {
              this.readOnlyAssets[
                this.assetService.listObjcet[index].idObject
              ] = true;
            }
          }
          for (let i = 0; i < this.assetService.listObjcet.length; i++) {
            this.selectedAsset[
              this.assetService.listObjcet[i].idObject
            ] = false;
          }
        });
    });
  }

  setReadOnlyAssetProprieties(assetId: number, value: boolean) {
    this.readOnlyAssets[assetId] = value;
    this.assetService.saveIsEnable = !this.existAssetOpened();
  }

  public refreshApplicationFormForAssetId(assetId: number) {
    this.getApplicationFormForAssetId(assetId).refreshApplicationForm();
  }

  saveAssetChanges(assetId: number) {
    this.assetService
      .saveAssetChanges(
        this.getModelForAssetId(assetId),
        this.simulationId,
        assetId,
        this.wizardCode
      )
      .subscribe(() => {
        this.refreshApplicationFormForAssetId(assetId);
        this.setReadOnlyAssetProprieties(assetId, true);
      });
  }

  private getModelForAssetId(assetId: number): any {
    const apf: ApplicationFormComponent = this.getApplicationFormForAssetId(
      assetId
    );
    return apf ? { page: apf.page, apfmap: apf.model } : null;
  }

  private getApplicationFormForAssetId(
    assetId: number
  ): ApplicationFormComponent {
    for (const index in this.applicationFormComponents.toArray()) {
      if (
        parseInt(this.applicationFormComponents.toArray()[index].idCode, 10) ===
        assetId
      ) {
        return this.applicationFormComponents.toArray()[index];
      }
    }
    return null;
  }

  deleteAsset(assetId: number) {
    this.assetService
      .deleteAsset(assetId, this.wizardCode, this.simulationId)
      .subscribe(x =>
        this.assetService
          .getAssetList(this.simulationId, this.wizardCode)
          .subscribe()
      );
  }

  goToNewAsset() {
    this.router.navigate([`../new-asset`], { relativeTo: this.activatedRoute });
  }

  goToAssetSearch() {
    this.router.navigate([`../asset-search`], {
      relativeTo: this.activatedRoute
    });
  }

  private existAssetOpened(): boolean {
    for (const x in this.readOnlyAssets) {
      if (!this.readOnlyAssets[x]) {
        return true;
      }
    }
    return false;
  }

  upperSaveButtonClick(index: number) {
    for (const i in this.saveAsset.toArray()) {
      if (
        this.saveAsset.toArray().hasOwnProperty(i) &&
        this.saveAsset.toArray()[i].nativeElement.id === `saveAsset${index}`
      ) {
        this.saveAsset.toArray()[i].nativeElement.click();
        break;
      }
    }
  }

  toogleAsset(assetId: number): void {
    if (this.selectedAsset[assetId]) {
      if (!this.readOnlyAssets[assetId]) {
        this.selectedAsset[assetId] = true;
      } else {
        this.selectedAsset[assetId] = false;
      }
    } else {
      this.selectedAsset[assetId] = true;
    }
  }

  formNotValid(index: number) {
    if (this.applicationFormComponents.toArray()[index]) {
      if (this.applicationFormComponents.toArray()[index].appForm.invalid) {
        return true;
      }
    }
  }

  openDeleteModal(assetId: number) {
    this.selectedAssetId = assetId;
    this.deleteModalIsOpen = true;
  }

  // Gestisce il submit della modale di cancellazione utente
  submitDeleteModal() {
    this.assetService
    .deleteAsset(this.selectedAssetId, this.wizardCode, this.simulationId)
    .subscribe(x => {
      this.assetService    
        .getAssetList(this.simulationId, this.wizardCode)
        .subscribe(res => {
          this.closeDeleteModal();
        });
    });

  }

  // Chiude e resetta le variabili per la modale di cancellazione
  closeDeleteModal() {
    this.deleteModalIsOpen = false;
    this.selectedAssetId = null;
  }
}
