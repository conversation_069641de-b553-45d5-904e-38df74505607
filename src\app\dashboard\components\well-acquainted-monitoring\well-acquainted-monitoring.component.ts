import { Component, OnInit } from '@angular/core';
import { Observable } from 'rxjs/Observable';

import { DashboardDetailComponent } from '../dashboard-detail.component';
import { SearchService } from '../../../shared/search/search.service';
import { DomainService } from '../../../shared/domain/domain.service';

@Component({
  selector: 'app-well-acquainted-monitoring',
  templateUrl: './well-acquainted-monitoring.component.html',
  styleUrls: ['./well-acquainted-monitoring.component.css']
})
export class WellAcquaintedMonitoringComponent extends DashboardDetailComponent
  implements OnInit {
  constructor(
    public searchService: SearchService,
    public domainService: DomainService
  ) {
    super(domainService, searchService);
  }

  ngOnInit() {
    super.ngOnInit();
  }

  getPositionsCounts(
    inChargeUser: boolean,
    inChargeBranch: boolean
  ): Observable<any> {
    return this.searchService.getSimulationsCounts(
      inChargeUser,
      inChargeBranch
    );
  }

  getPositionsData(
    excel: boolean,
    page: number,
    pageSize: number,
    orderBy: string,
    asc: boolean,
    filter: any
  ): Observable<any> {
    return this.searchService.getSimulationsData(
      excel,
      page,
      pageSize,
      orderBy,
      asc,
      filter
    );
  }
}
