import {
  Component,
  OnInit,
  Input,
  ViewChild,
  AfterViewInit,
  Output,
  EventEmitter,
  AfterContentInit
} from '@angular/core';
import { NgForm } from '@angular/forms';
import { Observable } from 'rxjs/Observable';


// Services
import { DomainService } from '../../../../../shared/domain/domain.service';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { LandingService } from '../../../../../simulation';
import { PositionService } from '../../../../../shared/position/position.service';

// Componente utiilzzato nel wizard di compilazione perizia (step Unità a Garanzia)
// Effettuati controlli per distinguere fra NDG CORPORATE/INDIVIDUAL e modificare opportunamente il componente
// per seguire il processo e le indicazioni rispettive
@Component({
  selector: 'app-consistency',
  templateUrl: './consistency.component.html',
  styleUrls: ['./consistency.component.css']
})
export class ConsistencyComponent
  implements OnInit, AfterViewInit, AfterContentInit {
  @Input() pageContent: any;
  @Input() positionId: string;
  @Input() agrarianLoan: boolean;
  @Input() differences: any = {};
  @Input() isMarketValueRequired = false;
  @Input() haveDisabledFields: boolean;
  @Input() haveDisabledFieldsSpecial: boolean;
  @Input() haveActualValue: boolean;
  @Input() domainResp: any; // UBZ_DOM_FLOOR_DIRECT_TYPE, UBZ_DOM_FLOOR_TYPE, UBZ_DOM_IND_MEASUREMENT
  @Output() statusChange: EventEmitter<boolean> = new EventEmitter<boolean>();
  @ViewChild(NgForm) private form: NgForm;

  differencesModalIsOpen = false;
  selectedDifference = {};
  selectedFieldName: string;
  selectedDifferenceType: string;
  selectedShownFieldName: string;
  selectedSubProperty: string;
  selectedDomainCode: string;
  selectedIndex: number;
  sectionIsValid: boolean;

  // Domini per campi select
  structureTypeDom: any;
  unitMeasurTypeDom: any;
  floorDirectTypeDom: any;
  floorTypeDom: any;
  macro_process: any;
  templateType: any

  constructor(
    private domainService: DomainService,
    public _accordionAPFService: AccordionAPFService,
    private landingService: LandingService,
    private positionService: PositionService,
  ) { }

  ngOnInit() {

    this.positionService.getAppraisalInfo(this.positionId).subscribe((res: any) => {
      this.macro_process = res.appraisal.macroProcess;
      this.templateType = res.templateType
      this.checkSectionValidation();

    })

    // Stringa settata a IND_ per modificare dinamicamente gli URL delle chiamate ai domini nel caso di NDG INDIVIDUAL
    let individualDomainString = '';
    if (this.landingService.posSegment === 'IND') {
      individualDomainString = 'IND_';
    }

    this.domainService
      .newGetDomain('UBZ_DOM_' + individualDomainString + 'CONSISTENCE_TYPE')
      .subscribe((res) => {
        if (this.domainResp) {
          this.structureTypeDom = res;
          this.floorDirectTypeDom = this.domainResp['UBZ_DOM_FLOOR_DIRECT_TYPE'];
          this.floorTypeDom = this.domainResp['UBZ_DOM_FLOOR_TYPE'];
          if (this.landingService.posSegment === 'IND') {
            this.unitMeasurTypeDom =
              this.domainResp['UBZ_DOM_IND_MEASUREMENT'];
          }
        }
      });
    this.calculateTotCommSurface();
    this.calculateTotBookValue();
    this.calculateTotPledgedValue();

  }

  ngAfterViewInit() {
    this.form.valueChanges.subscribe(() => {
      this.checkSectionValidation();
    });
  }

  ngAfterContentInit() {
    this.checkSectionValidation();

  }

  private checkSectionValidation() {
    const prev = this.sectionIsValid;
    this.sectionIsValid = this.checkIfComplete();
    if (prev !== this.sectionIsValid) {
      this.statusChange.emit(this.sectionIsValid);
    }

  }

  // Controlla se tutti i valori sono stati inseriti per validare l'accordion
  // insuranceValue, actualValue e floor sono variabili inserite come stringhe ma ritornate dopo l'invocazione dei servizi come number,
  // aggiunti controlli mirati
  checkIfComplete(): boolean {

    if (
      this.pageContent.insuranceValue === undefined ||
      this.pageContent.insuranceValue === null ||
      !this.pageContent.insuranceValue.toString() ||
      this.pageContent.insuranceValue === '' ||
      !this.pageContent.structseval ||
      this.pageContent.structseval.length <= 0 ||
      (this.isMarketValueRequired &&
        (this.pageContent.marketValue === undefined ||
          this.pageContent.marketValue === null ||
          Number.parseFloat(this.pageContent.marketValue) <= 0))

    ) {
      return false;
    }
    if (this.haveActualValue) {
      if (
        (this.pageContent.actualValue === undefined ||
          this.pageContent.actualValue === null) ||
        !this.pageContent.actualValue.toString() ||
        this.pageContent.actualValue === ''
      ) {
        return false;
      }
    }
    for (const strutcEval of this.pageContent.structseval) {

      if (
        !strutcEval.structureType ||
        strutcEval.structureType === '' ||
        (this.landingService.posSegment === 'IND' &&
          (!strutcEval.measurType || strutcEval.measurType === '')) ||
        !strutcEval.grossSurface ||
        strutcEval.grossSurface === '' ||
        !strutcEval.commCoefficient ||
        strutcEval.commCoefficient === '' ||
        !strutcEval.floorDirectType ||
        strutcEval.floorDirectType === '' ||
        (strutcEval.floor === undefined || strutcEval.floor === null) ||
        !strutcEval.floor.toString() ||
        strutcEval.floor === '' ||
        (this.macro_process !== 'ITL' && this.templateType !== 'AGL' && (!strutcEval.floorFlag || strutcEval.floorFlag === '' || strutcEval.floorFlag === undefined || strutcEval.floorFlag === null)) ||
        !strutcEval.floorType ||
        strutcEval.floorType === '' ||
        !strutcEval.mqValue ||
        strutcEval.mqValue === '' ||
        !strutcEval.mqPledgedValue ||
        strutcEval.mqPledgedValue === ''
      ) {
        return false;
      }

    }
    return true;
  }

  add() {
    if (!this.pageContent.structseval) {
      this.pageContent.structseval = [];
    }
    this.pageContent.structseval.push({
      evaluationId: null,
      structureId: null,
      structureType: '',
      grossSurface: null,
      commSurface: null,
      commCoefficient: null,
      pledgedValue: null,
      mqPledgedValue: null,
      bookValue: null,
      floor: null,
      mqValue: null
    });
    this.checkSectionValidation();
  }

  delete(index: number) {
    const tempStructseval = [];
    for (const ind in this.pageContent.structseval) {
      if (this.pageContent.structseval.hasOwnProperty(ind)) {
        if (`${index}` !== ind) {
          tempStructseval.push(this.pageContent.structseval[ind]);
        }
      }
    }
    this.pageContent.structseval = tempStructseval;
    this.calculateTotCommSurface();
    this.calculateTotBookValue();
    this.calculateTotPledgedValue();
    this.checkSectionValidation();
  }

  calculateTotCommSurface() {
    this.pageContent.totCommSurface = 0;
    if (this.pageContent.structseval) {
      for (const x of this.pageContent.structseval) {
        if (x && x.commSurface) {
          this.pageContent.totCommSurface += Number(x.commSurface);
        }
      }
      this.pageContent.totCommSurface = Number.parseFloat(
        this.pageContent.totCommSurface.toFixed(2)
      );
    }
  }

  private calculateCommSurf(index: number): number {
    if (this.pageContent.structseval && this.pageContent.structseval[index]) {
      const item = this.pageContent.structseval[index];
      return item.grossSurface * item.commCoefficient;
    } else {
      return null;
    }
  }

  calculateVarComm(index: number): number {
    if (this.pageContent.structseval && this.pageContent.structseval[index]) {
      const item = this.pageContent.structseval[index];
      return this.calculateCommSurf(index) * item.mqValue;
    } else {
      return null;
    }
  }
  calculateValCauzionale(index: number): number {
    if (this.pageContent.structseval && this.pageContent.structseval[index]) {
      const item = this.pageContent.structseval[index];
      return this.calculateCommSurf(index) * item.mqPledgedValue;
    } else {
      return null;
    }
  }

  calculateTotBookValue() {
    this.pageContent.totBookValue = 0;
    if (this.pageContent.structseval) {
      for (const x of this.pageContent.structseval) {
        if (x && x.commSurface) {
          this.pageContent.totBookValue += Number(x.bookValue);
        }
      }
      this.pageContent.totBookValue = Number.parseFloat(
        this.pageContent.totBookValue.toFixed(2)
      );
    }
  }

  calculateTotPledgedValue() {
    this.pageContent.totPledgedValue = 0;
    if (this.pageContent.structseval) {
      for (const x of this.pageContent.structseval) {
        if (x && x.commSurface) {
          this.pageContent.totPledgedValue += Number(x.pledgedValue);
        }
      }
      this.pageContent.totPledgedValue = Number.parseFloat(
        this.pageContent.totPledgedValue.toFixed(2)
      );
    }
    this.checkIfComplete();
  }

  openDifferencesModal(
    fieldName: string,
    fieldType: string,
    shownFieldName: string,
    subPropertyName: string,
    domainCode: string,
    index: number
  ) {
    this.selectedDifference = this.differences[fieldName];
    this.selectedFieldName = fieldName;
    this.selectedDifferenceType = fieldType;
    this.selectedShownFieldName = shownFieldName;
    this.selectedSubProperty = subPropertyName;
    this.selectedIndex = index;
    if (domainCode && domainCode.length > 0) {
      this.selectedDomainCode = domainCode;
    } else {
      this.selectedDomainCode = null;
    }
    this.differencesModalIsOpen = true;
  }

  closeDifferencesModal() {
    this.differencesModalIsOpen = false;
  }

  selectNewValue(newValueObj: any) {
    this.differences[newValueObj['fieldName']].assigned = true;
    if (this.selectedSubProperty) {
      const names = newValueObj['fieldName'].split('-');
      if (this.selectedIndex) {
        this.pageContent[this.selectedSubProperty][this.selectedIndex][
          names[0]
        ] = newValueObj['selectedValue'];
      } else {
        this.pageContent[this.selectedSubProperty][names[0]] =
          newValueObj['selectedValue'];
      }
    } else {
      this.pageContent[newValueObj['fieldName']] = newValueObj['selectedValue'];
    }
    this.differencesModalIsOpen = false;
  }

  isThereADifference(fieldName: string, assetName: string) {
    const name = this.getName(fieldName, assetName);
    if (this.differences[name]) {
      return true;
    } else {
      return false;
    }
  }

  isAssigned(fieldName: string, assetName: string) {
    const name = this.getName(fieldName, assetName);
    return this.differences[name].assigned;
  }

  private getName(fieldName: string, assetName: string) {
    return fieldName + '-' + assetName;
  }

  public multiplyForPledgedValue(item: any) {
    const surface: number = item.commSurface;
    const unitValue: number = item.mqPledgedValue;
    if (!Number.isNaN(surface) && !Number.isNaN(unitValue)) {
      item.pledgedValue = Number.parseFloat((surface * unitValue).toFixed(2));
      this.calculateTotPledgedValue();
    }
  }

  public multiplyForBookValue(item: any) {
    const surface: number = item.commSurface;
    const unitValue: number = item.mqValue;
    if (!Number.isNaN(surface) && !Number.isNaN(unitValue)) {
      item.bookValue = Number.parseFloat((surface * unitValue).toFixed(2));
      this.calculateTotBookValue();
    }
  }

  public multiplyForCommSurface(row: any) {
    const surface: number = row.grossSurface;
    const coefficient: number = row.commCoefficient;
    if (!Number.isNaN(surface) && !Number.isNaN(coefficient)) {
      row.commSurface = (surface * coefficient).toFixed(2);
      this.calculateTotCommSurface();
      this.multiplyForPledgedValue(row);
      this.multiplyForBookValue(row);
    }
  }

  getIncrementedFloor(floor: string): number {
    const floorNum = parseInt(floor, 10);
    if (isNaN(floorNum)) {
      return 0;
    } else {
      return floorNum + 1;
    }
  }
}
