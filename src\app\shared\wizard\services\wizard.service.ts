import { Injectable, Inject } from '@angular/core';
import { Http, Response, Headers } from '@angular/http';
import { WizardElement } from '../model/wizard-element';
import { AppConstants } from '../../../app.constants';
import { TaskList } from '../model/task-list';
import { Observable } from 'rxjs/Observable';
import { ApiService } from '../../api/api.service';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';

@Injectable()
export class WizardService {
  simulationUrl = 'UMF-EBA-WS/services/rest/wizard/V1?positionId=';
  simulationUrlWrapped = '/UBZ-ESA-RS/service/umfService/wizard/V1?positionId=';
  appraisalRequestUrl = '/assets/data/fake-appraisal-request-wizard.json';
  appraisalCompilationUrl = '/assets/data/fake-appraisal-fill-wizard.json';
  currentWizard: WizardElement[] = [];

  constructor(
    private http: Http,
    private apiService: ApiService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) {}

  getWizard(positionId: string): Observable<WizardElement[]> {
    positionId = encodeURIComponent(positionId);
    const headers = new Headers({
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
      'Expires': 'Sat, 01 Jan 2000 00:00:00 GMT',
      'Content-type': 'application/json'
    });
    const url = `${this.simulationUrlWrapped}${this.constants
      .processCode}${positionId}`;
    return this.http
      .get(url, { withCredentials: true, headers: headers })
      .map((resp: Response) => resp.json() as WizardElement[]);
  }

  invalidateTask(positionId: string, wizardCode: string, taskCode: string) {
    positionId = encodeURIComponent(positionId);
    wizardCode = encodeURIComponent(wizardCode);
    taskCode = encodeURIComponent(taskCode);
    const url = `/UBZ-ESA-RS/service/wizard/v1/wizards/${positionId}/invalidTask/${wizardCode}/${taskCode}`;
    return this.http.put(url, {}).map((resp: Response) => resp.json());
  }

  // Invoca il servizio getWizard che restituisce l'elenco degli step del wizard
  // in base al wizardCode si esamina il wizard corretto e viene restituito lo step precedente
  // a quello attuale
  public getPreviousValidTask(
    positionId: string,
    wizardCode: string
  ): string {
    let prevTaskCod: string;
    const tempWizard: WizardElement[] = [];
    // Prendo quello che serve
    for (const task of this.currentWizard) {
      if (task.taskList.outcomeCod === '2') {
        prevTaskCod = task.taskList.taskCod;
      }
      if (task.taskList.outcomeCod === '3') {
        break;
      }
    }
    return prevTaskCod;
    // FIXME - TOGLI SE NON SERVE INVOCARE IL SERVIZIO
    // return this.getWizard(positionId)
    //   .switchMap(res => {
    //     const tempWizard: WizardElement[] = [];
    //     for (const element of res) {
    //       if (element.taskList && element.taskList.taskCod === `UBZ-${wizardCode}`) {
    //         for (const child of element.childs) {
    //           // se la property outcomeCod == 5 lo step non deve essere inserito nel wizard
    //           if (child.taskList && child.taskList.outcomeCod !== '5') {
    //             tempWizard.push(child);
    //           }
    //         }
    //         break;
    //       }
    //     }
    //     // Prendo quello che serve
    //     for (const task of tempWizard) {
    //       if (task.taskList.outcomeCod === '2') {
    //         prevTaskCod = task.taskList.taskCod;
    //       }
    //       if (task.taskList.outcomeCod === '3') {
    //         break;
    //       }
    //     }
    //     return Observable.of(prevTaskCod);

        // FIXME - TOGLIERE SE REWRITE FUNZIONA CORRETTAMENTE
        // let wizard;
        // for (const wizardT of res) {
        //   if (wizardT.config.parentTask === 'UBZ-PER') {
        //     wizard = wizardT;
        //   }
        // }
        // // Estraggo i task list
        // for (const elem of wizard.childs) {
        //   if (
        //     (elem.taskList.outcomeCod !== '1') &&
        //     (elem.taskList.outcomeCod !== '5')
        //   ) {
        //     tasks.push(elem.taskList);
        //   }
        // }
        // // Prendo quello che serve
        // for (const task of tasks) {
        //   if (task.outcomeCod === '2') {
        //     prevTaskCod = task.taskCod;
        //   }
        //   if (task.outcomeCod === '3') {
        //     break;
        //   }
        // }
        // return Observable.of(prevTaskCod);

      // })
      // .map(res => res);
  }
}
