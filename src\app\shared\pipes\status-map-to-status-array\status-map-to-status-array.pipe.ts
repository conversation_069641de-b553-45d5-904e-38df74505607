import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'statusMapToStatusArray'
})
export class StatusMapToStatusArrayPipe implements PipeTransform {
  transform(value: any, args?: any): any {
    const keys = [];
    for (const key in value) {
      if (value.hasOwnProperty(key)) {
        keys.push({
          domCode: value[key].domCode,
          translationCod: value[key].translationStatusDescCod
        });
      }
    }
    return keys;
  }
}
