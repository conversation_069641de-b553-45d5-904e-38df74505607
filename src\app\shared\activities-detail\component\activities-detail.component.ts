import { Component, OnInit } from '@angular/core';
import { ActivitiesDetailService } from '../service/activities-detail.service';
import { Notification } from '../model/notification';

@Component({
  selector: 'app-activities-detail',
  templateUrl: './activities-detail.component.html',
  styleUrls: ['./activities-detail.component.css']
})
export class ActivitiesDetailComponent implements OnInit {
  notificationList: Notification[] = [
    { titolo: 'titolo 1', descrizione: 'Descrizione 1' },
    { titolo: 'titolo 2', descrizione: 'Descrizione 2' },
    { titolo: 'titolo 3', descrizione: 'Descrizione 3' }
  ];

  constructor(private activitiesDetailService: ActivitiesDetailService) {}

  ngOnInit() {
    this.activitiesDetailService.getActivities().subscribe(res => {
      // this.notificationList = res.events;
    });
  }
}
