import { Component } from '@angular/core';
import { getCurrentAppCode, isZA0, isUBZ, navigateToAppCode } from '../../utils/app-code.util';

@Component({
  selector: 'app-code-switcher',
  template: `
    <div class="app-code-switcher">
      <div class="current-app-info">
        <strong>Current App:</strong> {{ currentAppCode }}
        <span class="badge" [ngClass]="{'badge-primary': isCurrentUBZ, 'badge-warning': isCurrentZA0}">
          {{ currentAppCode }}
        </span>
      </div>
      
      <div class="switcher-buttons" *ngIf="showSwitcher">
        <button 
          type="button" 
          class="btn btn-sm btn-outline-primary" 
          [disabled]="isCurrentUBZ"
          (click)="switchToUBZ()">
          Switch to UBZ
        </button>
        <button 
          type="button" 
          class="btn btn-sm btn-outline-warning" 
          [disabled]="isCurrentZA0"
          (click)="switchToZA0()">
          Switch to ZA0
        </button>
      </div>
    </div>
  `,
  styles: [`
    .app-code-switcher {
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      margin: 10px 0;
      background-color: #f8f9fa;
    }
    
    .current-app-info {
      margin-bottom: 10px;
    }
    
    .badge {
      margin-left: 10px;
      padding: 4px 8px;
      border-radius: 4px;
      color: white;
    }
    
    .badge-primary {
      background-color: #007bff;
    }
    
    .badge-warning {
      background-color: #ffc107;
      color: #212529;
    }
    
    .switcher-buttons button {
      margin-right: 10px;
    }
  `]
})
export class AppCodeSwitcherComponent {
  showSwitcher = true; // Set to false in production if you don't want users to switch

  get currentAppCode(): string {
    return getCurrentAppCode();
  }

  get isCurrentUBZ(): boolean {
    return isUBZ();
  }

  get isCurrentZA0(): boolean {
    return isZA0();
  }

  switchToUBZ(): void {
    if (confirm('Switch to UBZ application? This will reload the page.')) {
      navigateToAppCode('UBZ', true);
    }
  }

  switchToZA0(): void {
    if (confirm('Switch to ZA0 application? This will reload the page.')) {
      navigateToAppCode('ZA0', true);
    }
  }
}
