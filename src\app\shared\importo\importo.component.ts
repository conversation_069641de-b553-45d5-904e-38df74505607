import {
  Component,
  Input,
  ViewChild,
  AfterViewInit,
  forwardRef
} from '@angular/core';
import {
  NgForm,
  NG_VALUE_ACCESSOR,
  ControlValueAccessor
} from '@angular/forms';
import createNumberMask from 'text-mask-addons/dist/createNumberMask';
import { unmaskValue, maskReceivedValue } from '../../app.constants';

@Component({
  selector: 'app-importo',
  templateUrl: './importo.component.html',
  styleUrls: ['./importo.component.sass'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ImportoComponent),
      multi: true
    }
  ]
})

export class ImportoComponent implements AfterViewInit, ControlValueAccessor {
  @ViewChild('f')
  form: NgForm;

  @Input()
  name: string;
  @Input()
  ngClassCondition: boolean;
  @Input()
  ngClassAdd: string;
  @Input()
  required: boolean;
  @Input()
  disabled: boolean;
  @Input()
  title: string;
  @Input()
  maxlength: number;
  @Input()
  minValue: number;
  @Input()
  maxValue: number;
  formattedValue: string;

  numericMaskConfig = createNumberMask({
    prefix: '',
    thousandsSeparatorSymbol: '.',
    allowDecimal: true,
    decimalSymbol: ','
  });

  constructor() {}

  /**
   * Questo è il metodo con il quale questo componente riceve il model dal form durante l'inizializzazione.
   * Qui prendiamo il valore esistente e lo formattiamo per mostrarlo all'utente.
   */
  public writeValue(model: string) {
    this.formattedValue = maskReceivedValue(model);
    // Se il title non è settata non bisogna mostrare il tooltip sull'elemento
    if (!this.title) {
      this.title = '';
    }
  }

  // registers 'fn' that will be fired wheb changes are made
  // this is how we emit the changes back to the form
  public registerOnChange(fn: any) {
    this.propagateChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.propagateTouch = fn;
  }

  // The method set in registerOnChange to emit changes back to the form
  private propagateChange = (_: any) => {};

  private propagateTouch = () => {};

  ngAfterViewInit() {
    // Subscribe invocata ad ogni cambiamento del campo
    this.form.form.statusChanges.subscribe(() => {
      const unmaskedValue = unmaskValue(this.formattedValue);
      if (this.minValue) {
        if (unmaskedValue < this.minValue) {
          return this.propagateChange(null);
        }
      }
      if (this.maxValue) {
        if (unmaskedValue > this.maxValue) {
          return this.propagateChange(null);
        }
      }
      return this.propagateChange(unmaskedValue);
    });
  }
}
