<form #f="ngForm" novalidate (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
          {{'UBZ.SITE_CONTENT.1110110101' | translate }}
          <span class="state" [ngClass]="{'green': sectionIsValid, 'red': !sectionIsValid}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <table class="uc-table">
          <thead>
            <tr>
              <th>{{'UBZ.SITE_CONTENT.101011000' | translate }}</th>
              <th>{{'UBZ.SITE_CONTENT.101100000' | translate }}</th>
              <th>{{'UBZ.SITE_CONTENT.101100001' | translate }}</th>
              <th>{{'UBZ.SITE_CONTENT.101100010' | translate }}</th>
              <th>{{'UBZ.SITE_CONTENT.100011000' | translate }}</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let model of objectList; let index = index">
              <td>{{ (statusTypeDomain && statusTypeDomain[model.destination]) ? (statusTypeDomain[model.destination].translationCod | translate) : '' }}</td>
              <td>{{ model.omiMin }} / {{ model.omiMax }}</td>
              <td>{{ model.nomismaMin }} / {{ model.nomismaMax }}</td>
              <td>{{ model.scenariMin }} / {{ model.scenariMax }}</td>
              <td>{{ model.otherMin }} / {{ model.otherMax }}</td>
              <td>
                <ng-container *appAuthKey="'UBZ_PRICE.EVALUATION_MODIFY'">
                  <button type="button" class="btn btn-empty waves-effect waves-secondary" (click)="openModal('modify', index)">
                    <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                  </button>
                </ng-container>
                <ng-container *appAuthKey="'UBZ_PRICE.EVALUATION_DELETE'">
                  <button type="button" class="btn btn-empty waves-effect waves-secondary" (click)="openDeleteModal(index)">
                    <i class="fa fa-trash-o" aria-hidden="true"></i>
                  </button>
                </ng-container>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="row form-group">
          <div class="col-sm-12">
            <ng-container *appAuthKey="'UBZ_PRICE.EVALUATION_ADD'">
              <button type="button" class="btn btn-empty" (click)="openModal('add')">
                <i class="fa fa-plus"></i> {{'UBZ.SITE_CONTENT.101010110' | translate }}
              </button>
            </ng-container>
          </div>
        </div>
        <app-price-evaluation-modal
          *ngIf="selectedItem && modalIsOpen"
          [modalType] = "modalType"   
          [isOpen] = "modalIsOpen"    
          [model]="selectedItem"           
          [domains]="statusTypeDomain"
          (modalClose) = "modalIsOpen = false"
          (modalSubmit)="submitModal($event)">
        </app-price-evaluation-modal>        
        <!-- CUSTOM MODAL gestisce la cancellazione rilevamento prezzi medi -->
        <app-custom-modal 
          [modalType] = "'delete'"
          [isOpen] = "deleteModalIsOpen"
          [largeModalFlag] = "false"
          [headerTitle] = "'UBZ.SITE_CONTENT.101010111'"
          [positionId]="''" 
          [idCode]="''"
          [apfString] = "''"
          [messagesArray] = "['UBZ.SITE_CONTENT.1011000000']"
          [buttonTitle] = "['UBZ.SITE_CONTENT.11110', 'UBZ.SITE_CONTENT.100000']"
          [disabledFlag] = "false"  
          (modalSubmit) = "submitDeleteModal()"
          (modalClose) = "deleteModalIsOpen = false">
        </app-custom-modal>
        <div class="panel-box">
          <div class="row">
            <div class="col-sm-4 form-group">
              <label></label>
              <div class="custom-checkbox">
                <input id="necChangeNotificFlag" type="checkbox" [(ngModel)]="evalObj.appropriateFlag" name="necChangeNotificFlag" class="checkbox">
                <label for="necChangeNotificFlag">{{'UBZ.SITE_CONTENT.1110101110' | translate }}</label>
              </div>
            </div>
            <div class="col-sm-6 form-group">
              <label>
                <span><i class="icon-search note-tooltip" [tooltip]="evalObj.appropriateOperat" triggers="click"></i></span>
                {{'UBZ.SITE_CONTENT.1110101111' | translate }}*
              </label>
              <textarea type="text" [(ngModel)]="evalObj.appropriateOperat" name="appropriateOperat" class="form-control" required></textarea>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-8 form-group">
              <label>{{'UBZ.SITE_CONTENT.1110110111' | translate }}*</label>
              <input type="text" [(ngModel)]="evalObj.benchmarkDeviation" name="benchmarkDeviation" class="form-control" required>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12 form-group">                        
              <label>
                <span><i class="icon-search note-tooltip" [tooltip]="noteRilevazione" triggers="click"></i></span>
                {{'UBZ.SITE_CONTENT.11000101' | translate }}
              </label>
              <textarea class="form-control" type="text" name="nome" [(ngModel)]="noteRilevazione"></textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
  </accordion-group>
</form>