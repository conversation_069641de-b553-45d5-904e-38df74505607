<!doctype html>
<html>

<head>
  <meta charset="utf-8">
  <meta http-equiv="cache-control" content="no-store, no-cache" />
  <meta http-equiv="expires" content="Tue, 01 Jan 1980 1:00:00 GMT" />
  <meta http-equiv="pragma" content="no-cache" />
  <title>UBZ</title>

  <!-- <base href="/UBZ-EFA-PF/UBZ/"> -->

  <script>
    (function () {
      // pt local
      var isDevelopment = window.location.hostname === 'localhost'
      var profile = document.cookie.match(/pr=([^|]*)/)?.[1];
      console.log('Profile:', profile);

      if (isDevelopment) {

        var baseTag = document.createElement('base');
        baseTag.setAttribute('href', '/');

        var titleTag = document.querySelector('title');
        if (titleTag && titleTag.parentNode) {
          if (titleTag.nextSibling) {
            titleTag.parentNode.insertBefore(baseTag, titleTag.nextSibling);
          } else {
            titleTag.parentNode.appendChild(baseTag);
          }
        } else {
          document.head.appendChild(baseTag);
        }
      } else {

        console.log(profile)

        //pt xdt

        var baseHref = profile === "BIOSUG" ? '/UBZ-EFA-PF/ZA0/' : '/UBZ-EFA-PF/UBZ/';
        console.log(baseHref)
        var baseTag = document.createElement('base');
        baseTag.setAttribute('href', baseHref);

        var titleTag = document.querySelector('title');
        if (titleTag && titleTag.parentNode) {
          if (titleTag.nextSibling) {
            titleTag.parentNode.insertBefore(baseTag, titleTag.nextSibling);
          } else {
            titleTag.parentNode.appendChild(baseTag);
          }
        } else {
          document.head.appendChild(baseTag);
        }
      }
    })();
  </script>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">

  
</head>

<body>
  <app-root>Loading...</app-root>
</body>

</html>