import { Component, OnInit, ViewChild, Input } from '@angular/core';
import { NgForm } from '@angular/forms';

// Models
import { Domain } from '../../../../../shared/domain/domain';

// Services
import { AppraisalCompilationService } from '../../../../service/appraisal-compilation.service';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';

// Components
import { ApplicationFormComponent } from '../../../../../shared/application-form/components/application-form.component';

@Component({
  selector: 'app-building-amnesty',
  templateUrl: './building-amnesty.component.html',
  styleUrls: ['./building-amnesty.component.css']
})
export class BuildingAmnestyComponent implements OnInit {
  @Input() pageContent: any[];
  @Input() positionId: string;
  @Input() assetId: string;
  @Input() agrarianLoan: boolean;
  @Input() domainResp: any; // UBZ_DOM_SANCTION_TYPE

  sanctionTypes: Domain[] = [];

  pageIsValid = true;
  originTypes: string[] = [];
  selectedPageElement = null;

  // Variabili di gestione customModal
  modalType: string;
  customModalIsOpen = false;  
  largeModalFlag: boolean;
  headerTitle: string;
  apfString: string;
  messagesArray: string[];
  buttonTitle: string[];
  disabledFlag: boolean;

  constructor(
    private appraisalCompilationService: AppraisalCompilationService,
    public _accordionAPFService: AccordionAPFService
  ) {}

  ngOnInit() {
    if(this.domainResp) {
      this.sanctionTypes = this.domainResp;
    }
      
  }

  // Imposta le variabili da passare alla modal e ne forza l'apertura
  // modalType indica il tipo di modal che si sta aprendo (add, delete, modify)
  // SelectedElement contiene l'elemento selezionato nel caso di modify e delete
  openCustomModal(modalType: string, selectedElement?: any) {
    if (selectedElement) {
      this.selectedPageElement = selectedElement;      
    }
    this.modalType = modalType;
    switch (this.modalType) {
      case 'add':
        this.largeModalFlag = true;
        this.headerTitle = 'UBZ.SITE_CONTENT.111011100';
        this.apfString = 'COND_EDILIZIO';
        this.messagesArray = new Array();
        this.buttonTitle = ['UBZ.SITE_CONTENT.111011100'];
        this.disabledFlag = true;
        break;
      case 'modify':
        this.largeModalFlag = true;
        this.headerTitle = 'UBZ.SITE_CONTENT.111011111';
        this.apfString = 'COND_EDILIZIO';
        this.messagesArray = new Array();        
        this.buttonTitle = ['UBZ.SITE_CONTENT.111011111'];
        this.disabledFlag = true;
        break;
      case 'delete':
        this.largeModalFlag = false;
        this.headerTitle = 'UBZ.SITE_CONTENT.111011101';
        this.apfString = '';
        this.messagesArray = ['UBZ.SITE_CONTENT.111011110'];
        this.buttonTitle = ['UBZ.SITE_CONTENT.11110', 'UBZ.SITE_CONTENT.100000'];
        this.disabledFlag = false;
        break;
    }
    this.customModalIsOpen = true;
  }

  // Intercetta l'evento submit della customModal ed invoca il metodo appropriato a seconda del modalType.
  // L'objectParams in input contiene l'apForm del componente e il modalType (add, modify, delete)
  handleSubmitCustomModal(objectParams: Object) {
    switch (objectParams['modalType']) {
      case 'add':
        this.submitAddModal(objectParams['apForm']);
        break;
      case 'modify':
        this.submitModifyModal(objectParams['apForm']);
        break;
      case 'delete':
        this.submitDeleteModal(objectParams['apForm']);
        break;
    }
  }

  // Esegue metodo di aggiunta per customModal con modalType === 'add'
  submitAddModal(apForm: ApplicationFormComponent) {
    this.appraisalCompilationService
    .saveNewStaticAccordion('buildingSanc', this.assetId, apForm)
    .subscribe(res => {
      this.closeCustomModal();
    });
  }

  // Esegue metodo di aggiunta per customModal con modalType === 'modify'
  submitModifyModal(apForm: ApplicationFormComponent) {
    this.appraisalCompilationService
    .saveExistingStaticAccordion('buildingSanc', this.selectedPageElement.sanctionId, apForm)
    .subscribe(x => {
      this.closeCustomModal();
    });
  }
  
  // Esegue metodo di aggiunta per customModal con modalType === 'delete'
  submitDeleteModal(apForm: ApplicationFormComponent) {
    this.appraisalCompilationService
      .deleteStaticAccordion('buildingSanc', this.selectedPageElement.sanctionId)
      .subscribe(x => {
        this.closeCustomModal();
      });
  }

  closeCustomModal() {
    this.refreshPagecontent();
    this.selectedPageElement = null;    
    this.customModalIsOpen = false;
  }

  checkIfComplete() {
    // E' una sezione facoltativa
    return true;
  }

  refreshPagecontent() {
    this.appraisalCompilationService
      .getSingleStaticComponentOfPropertyGuarantee(this.assetId, 'buildingSanc')
      .subscribe(x => {
        this.pageContent = x['buildingSanc'];
        this.checkIfComplete();
      });
  }
}
