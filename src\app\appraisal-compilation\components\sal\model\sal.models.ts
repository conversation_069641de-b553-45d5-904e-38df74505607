export class EndJobEvaluationModel {
  progressId: number;
  estimateId: number;
  estimateType: string;
  estimateNum: number;
  mqSurface: number;
  maxEuroMqTransfer: number;
  maxEuroTransfer: number;
}

export class CostPrevisionTableModel {
  appraisalId: string;
  costId: number;
  costType: string;
  expectedMqMc: number;
  expectedCostMqMc: number;
  expectedTotCost: number;
  executedMqMc: number;
  executedCostMqMc: number;
  totExecutedCost: number;
}

export class SALComplessivoModel {
  totalActivCost: OverallJobRowModel[] = [];
  physicalSalPerc = null;
  accountSalPerc = null;
  paramExpenseApprFlag = null;
  buildSiteInfo = null;
  appraisalId = null;
  progressId = null;
  aircraftName = null;
  aircraftIdentNum = null;
  inspectionPlace = null;
  bookValue = null;
  pledgedValue = null;
  insuranceValue = null;
  commercialOpinion = null;
  executedActivityDesc = null;
  planningProcess = null;
  planningStartDate: Date;
  planningEndDate: Date;
  surveyDate = null;
  suspensionReason = null;
  otherSuspReasNotes = null;
  buildSiteRecapCosts = null;
  metricCalculationFlag = null;
  execProjectFlag = null;
  objectsProjectCorr = null;
  changesDescNoCorr = null;
  diversIndemnDesc = null;
  objectsReRegCorr = null;
  diversityDesc = null;
  necChangeNotificFlag = null;
  planningExecCompany = null;
  contract = null;
  planningSupervisor = null;
  propConclusions = null;
}

export class OverallJobRowModel {
  activityCategory: string;
  activityCode: string;
  activitySubCategory: string;
  distributionAmount: number;
  distributionNotes: string;
  distributionPerc: number;
  objectCode: number;
  updatePerc: number;
}

export class ShippingCostItem {
  costType: string;
  total = 0;
  alreadyTaken = 0;
  toGive = 0;
}
