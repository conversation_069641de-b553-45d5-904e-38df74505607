<div class="col-sm-12 section-headline">
  <h1>{{'UBZ.SITE_CONTENT.10010000001' | translate}}</h1>
  <section id="breadcrumbs" class="breadcrumbs">
    <div class="row">
      <div class="col-sm-12">
        <ul>
          <li><a [routerLink]="['/massive-upload']">{{'UBZ.SITE_CONTENT.10001111001' | translate}}</a></li>
          <li>{{'UBZ.SITE_CONTENT.10010000001' | translate}}</li>
        </ul>
      </div>
    </div>
  </section>
</div>

<section id="funds">
  <table class="table table-hover">
    <thead>
      <tr>
        <th scope="col">{{'UBZ.SITE_CONTENT.10010001' | translate}}</th>
        <th scope="col">{{'UBZ.SITE_CONTENT.1001110' | translate}}</th>
        <th scope="col">{{'UBZ.SITE_CONTENT.10010000010' | translate}}</th>
        <th scope="col">{{'UBZ.SITE_CONTENT.10010000011' | translate}}</th>
        <th scope="col">{{'UBZ.SITE_CONTENT.10010000100' | translate}}</th>
        <th scope="col">{{'UBZ.SITE_CONTENT.1000011011' | translate}}</th>
        <th scope="col">{{'UBZ.SITE_CONTENT.11101101' | translate}}</th>
        <th scope="col">{{'UBZ.SITE_CONTENT.10010000101' | translate}}</th>
        <th scope="col">{{'UBZ.SITE_CONTENT.10001100011' | translate}}</th>
        <th scope="col">{{'UBZ.SITE_CONTENT.1100111010' | translate}}</th>
        <th scope="colgroup" colspan="2">{{'UBZ.SITE_CONTENT.11111011011' | translate}}</th>
        <th scope="col">{{'UBZ.SITE_CONTENT.10101011011' | translate}}</th>
        <th scope="col">{{'UBZ.SITE_CONTENT.10001100101' | translate}}</th>
        <th scope="col"></th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngFor="let row of appraisalList; let rowIndex = index">
        <tr>
          <td data-label="">{{ row.appraisalId }}</td>
          <td data-label="">{{ row.ndg }}</td>
          <td data-label="">{{ row.heading }}</td>
          <td data-label="">{{ row.externalAppraisalId }}</td>
          <td data-label="">{{ row.appraisalDate | date:'dd/MM/yyyy' }}</td>
          <td data-label="">{{ macroProcOomain[row.originProcess]?.translationCod | translate }}</td>
          <td data-label="">{{ appTypeDomain[row.appraisalType]?.translationCod | translate }}</td>
          <td data-label="">{{ scopeTypeDomain[row.appraisalScope]?.translationCod | translate }}</td>
          <td data-label="">{{ row.surveyDate | date:'dd/MM/yyyy' }}</td>
          <td data-label="">{{ surveyTypeDomain[row.surveyType]?.translationCod | translate }}</td>
          <td data-label=""><span *ngIf="row.nDocuments > 0" class="state"
              [ngClass]="{ 'red': row.uploadDocumentOutcome === 'R', 'green': row.uploadDocumentOutcome === 'G', 'yellow': row.uploadDocumentOutcome === 'Y'}"></span><span
              *ngIf="row.nDocuments === 0" class="state grey"></span></td>
          <td data-label=""><button *ngIf="(row.uploadDocumentOutcome !== 'Y' || row.continueToLoad)"
              class="btn btn-empty" (click)="openDocumentsModal(row)"><i class="icon-note"></i></button></td>
          <td data-label=""><span class="state"
              [ngClass]="{ 'red': row.loadingOutcome === 'R', 
            'green': row.loadingOutcome === 'G', 'yellow': row.loadingOutcome === 'Y', 'grey': row.loadingOutcome === 'D'}">
            </span></td>
          <td data-label="">
            <button class="btn btn-empty" (click)="openAssetModal(row)"><i class="icon icon-info"></i></button>
          </td>
          <td><a *ngIf="row.loadingOutcome !== 'R'"
              [routerLink]="['/appraisal-migration', row.appraisalId, constants.wizardCodes.PER, '-']"><i
                class="icon-angle-double-right"></i></a></td>
        </tr>
      </ng-container>
    </tbody>
  </table>

  <div class="row" *ngIf="listSize > 10">
    <div class="col-sm-6">
      <div class="results">
        <span>{{'UBZ.SITE_CONTENT.1111111' | translate }}</span>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="pageSize" (change)="changePageSize()">
            <option value="10" selected>10 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}</option>
            <option *ngIf="listSize > 20" value="20">20 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}
            </option>
            <option *ngIf="listSize > 30" value="30">30 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}
            </option>
            <option *ngIf="listSize > 40" value="40">40 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}
            </option>
            <option *ngIf="listSize > 50" value="50">50 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}
            </option>
            <option *ngIf="listSize <= 50" [ngValue]="listSize">{{listSize}}
              {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}</option>
          </select>
        </div>
      </div>
    </div>
    <div class="col-sm-6">
      <nav aria-label="Page navigation" class="pull-right">
        <pagination [boundaryLinks]="true" [directionLinks]="false" [totalItems]="listSize" [(ngModel)]="page"
          [itemsPerPage]="pageSize" (pageChanged)="changePage($event)" class="pagination" previousText="&lsaquo;"
          nextText="&rsaquo;" firstText="&laquo;" lastText="&raquo;"></pagination>
      </nav>
    </div>
  </div>

</section>

<div class="modal fade" bsModal tabindex="-1" role="dialog" aria-labelledby="myModalLabel" (onHidden)="onModalHidden()"
  [config]="">
  <ng-container *ngIf="modalSelected === 'asset'">
    <ng-container *ngIf="!showLogError">
      <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h2>{{'UBZ.SITE_CONTENT.11011' | translate}}</h2>
            <button type="button" class="close pull-right" aria-label="Close" (click)="hideAssetModal()">
              <i class="icon-close"></i>
            </button>
          </div>
          <div class="modal-body" style="word-break: break-all">
            <table class="uc-table">
              <thead>
                <tr>
                  <th scope="col" class="col-sm-2" style="text-align : center">{{'UBZ.SITE_CONTENT.100100' | translate}}
                  </th>
                  <th scope="col" class="col-sm-2" style="text-align : center">{{'UBZ.SITE_CONTENT.100110' | translate}}
                  </th>
                  <th scope="col" class="col-sm-2" style="text-align : center">{{'UBZ.SITE_CONTENT.101011' | translate}}
                  </th>
                  <th scope="col" class="col-sm-2" style="text-align : center">{{'UBZ.SITE_CONTENT.101010' | translate}}
                  </th>
                  <th scope="col" class="col-sm-2" style="text-align : center">
                    {{'UBZ.SITE_CONTENT.1110100000' | translate}}</th>
                  <th scope="colgroup" colspan="2" style="text-align : center; width: 20%">
                    {{'UBZ.SITE_CONTENT.11111011010' | translate}}</th>
                  <th scope="col" class="col-sm-2" style="text-align : center">
                    {{'UBZ.SITE_CONTENT.10000011111' | translate}}</th>
                  <th scope="col" class="col-sm-2" style="text-align : center">
                    {{'UBZ.SITE_CONTENT.10010000110' | translate}}</th>
                  <th scope="col" class="col-sm-2" style="text-align : center">
                    {{'UBZ.SITE_CONTENT.10100100' | translate}}
                  </th>
                  <th scope="col" class="col-sm-2" style="text-align : center">
                    {{'UBZ.SITE_CONTENT.10010101' | translate}}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let asset of selectedAppraisal?.assets; let i2 = index">
                  <td attr.data-label="ID Asset" style="text-align : center">{{asset.resItemId}}</td>
                  <td attr.data-label="Categoria" style="text-align : center">
                    {{assetCategoryDomain[asset.resItemCategory]?.translationCod | translate}}</td>
                  <td attr.data-label="Indirizzo" style="text-align : center">{{asset.address}} {{asset.streetNum}}</td>
                  <td attr.data-label="CAP" style="text-align : center">{{asset.zipCod}}</td>
                  <td attr.data-label="Citta" style="text-align : center">{{asset.city}}</td>
                  <td attr.data-label="Esito Normalizzatore" style="text-align : center"><span *ngIf="asset.normOutcome"
                      class="state" [ngClass]="{ 'red': asset.normOutcome === 'R', 'green': asset.normOutcome === 'G', 'yellow': asset.normOutcome === 'Y',
                   'grey': asset.normOutcome === 'D'}"></span>
                  </td>
                  <td attr.data-label="Error log" style="text-align : center">
                    <button *ngIf="asset.normOutcome === 'R' || asset.normOutcome === 'Y'" class="btn btn-empty"
                      (click)="handleLogModal(asset)"><i class="icon-note"></i></button></td>
                  <td attr.data-label="Valore CTU" style="text-align : center">
                    {{asset.ctuValue | currency:'EUR':'symbol':'1.2-2'}}</td>
                  <td attr.data-label="Valore commericale" style="text-align : center">
                    {{asset.bookValue | currency:'EUR':'symbol':'1.2-2'}}</td>
                  <td attr.data-label="Progressivo" style="text-align : center">{{asset.collatProg}}</td>
                  <td attr.data-label="Forma tecnica" style="text-align : center">{{asset.collatTecForm}}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </ng-container>
    <ng-container *ngIf="showLogError">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h2>{{'UBZ.SITE_CONTENT.10000010100' | translate }}</h2>
            <button type="button" class="close pull-right" aria-label="Close" (click)="handleLogModal(null)">
              <i class="icon-close"></i>
            </button>
          </div>
          <div class="modal-body">
            <div class="row" style="padding-right: 20px;padding-left: 20px;">
              <div *ngIf="selectedAsset?.normOutcomeDescription">
                <div *ngIf="selectedAsset?.normOutcome === 'R'" class="col-sm-6 form-group">
                  <label>{{'UBZ.SITE_CONTENT.10011010100' | translate }} </label>
                </div>
                <div *ngIf="selectedAsset?.normOutcome === 'Y'" class="col-sm-6 form-group">
                  <label> {{'UBZ.SITE_CONTENT.11110011000' | translate }} </label>
                </div>
                <div class="col-sm-6 form-group">
                  <label> {{selectedAsset?.normOutcomeDescription}} </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
  </ng-container>


  <ng-container *ngIf="modalSelected === 'doc'">
    <div class="modal-dialog modal-xl" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h2>{{'UBZ.SITE_CONTENT.1101101011' | translate }}</h2>
          <button type="button" class="close pull-right" aria-label="Close" (click)="hideDocumentsModal()">
            <i class="icon-close"></i>
          </button>
        </div>
        <div class="modal-body" style="word-break: break-all">
          <table class="uc-table">
            <thead style="word-break: normal">
              <tr>
                <th scope="col" class="col-sm-1" style="text-align: center">
                  {{ 'UBZ.SITE_CONTENT.10010001' | translate }}</th>
                <th scope="col" class="col-sm-1" style="text-align: center">
                  {{ 'UBZ.SITE_CONTENT.10010000011' | translate }}</th>
                <th scope="col" class="col-sm-1" style="text-align: center">
                  {{ 'UBZ.SITE_CONTENT.11011111' | translate }}</th>
                <th scope="col" class="col-sm-1" style="text-align: center">
                  {{ 'UBZ.SITE_CONTENT.100110' | translate }}
                </th>
                <th scope="col" class="col-sm-1" style="text-align: center">
                  {{ 'UBZ.SITE_CONTENT.10010110' | translate }}</th>
                <th scope="col" class="col-sm-1" style="text-align: center">
                  {{ 'UBZ.SITE_CONTENT.10001111100' | translate }}</th>
                <th scope="col" class="col-sm-1" style="text-align: center">
                  {{ 'UBZ.SITE_CONTENT.1101000010' | translate }}</th>
                <th scope="col" class="col-sm-1" style="text-align: center">
                  {{ 'UBZ.SITE_CONTENT.100011001111' | translate }}</th>
                <th scope="col" class="col-sm-1" style="text-align: center"></th>
              </tr>
            </thead>
            <tbody style="word-break: normal">
              <tr *ngFor="let document of selectedAppraisal?.documents">
                <td attr.data-label="" style="text-align: center">{{ document.appraisalId }}</td>
                <td attr.data-label="" style="text-align: center">{{ document.externalId }}</td>
                <td attr.data-label="" style=" word-break: break-all; text-align: center">{{ document.docName }}</td>
                <td attr.data-label="" style="text-align: center">
                  {{ docCategoryDomain[document.docCategory] ? document.docCategory : '999'}}</td>
                <td attr.data-label="" style="text-align: center">
                  {{ docCategoryDomain[document.docCategory] ? (docCategoryDomain[document.docCategory].translationCod | translate) : (docCategoryDomain['999'].translationCod | translate) }}
                </td>
                <td attr.data-label="">{{ document.updateDate | date:'dd/MM/yyyy'}}</td>
                <td data-label="" style="text-align: center"><span class="state"
                    [ngClass]="{ 'red':  document.uploadStatus === 'R','green': document.uploadStatus === 'G', 'yellow': document.uploadStatus === 'Y'}"></span>
                </td>
                <td attr.data-label="" style="text-align: center">{{document.outcomeNote}} </td>
                <td attr.data-label="" style="text-align: center"><button class="btn btn-empty"
                    *ngIf="document.uploadStatus === 'R'" (click)="reload(document)"><i
                      class="icon-switch"></i></button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </ng-container>
</div>
