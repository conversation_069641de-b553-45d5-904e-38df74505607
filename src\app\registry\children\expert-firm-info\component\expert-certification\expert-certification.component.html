<form #f="ngForm">
  <accordion-group #group class="panel" [ngClass]="{'empty-accordion': !aCertificateExists}" [isOpen]="modify" [isDisabled]="!aCertificateExists">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          {{'UBZ.SITE_CONTENT.1100000000' | translate }}
          <div class="accordion-button">
            <ng-container *appAuthKey="'UBZ_REGISTRY.EXPERT_CERTIFICATIONS_OPEN_NEW'">
              <button *ngIf="!modify && !aCertificateExists" type="button" class="btn btn-empty" (click)="startModify($event)">
                <i class="fa icon-add"></i>{{'UBZ.SITE_CONTENT.111100001' | translate }}
              </button>
            </ng-container>
            <ng-container *appAuthKey="'UBZ_REGISTRY.EXPERT_CERTIFICATIONS_MODIFY'">
              <button *ngIf="!modify && aCertificateExists" type="button" class="btn btn-empty" (click)="modifyPressed($event)">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.11111' | translate }}
              </button>
            </ng-container>
            <ng-container *ngIf="modify">
              <button *appAuthKey="'UBZ_REGISTRY.EXPERT_CERTIFICATIONS_CANCEL'" type="button" class="btn btn-empty" (click)="cancelModify($event)">
                <i class="fa fa-times"></i>{{'UBZ.SITE_CONTENT.100000' | translate }}
              </button>
              <button *appAuthKey="'UBZ_REGISTRY.EXPERT_CERTIFICATIONS_SAVE'" type="button" class="btn btn-empty" (click)="saveData($event)" [disabled]="f.invalid">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.100001' | translate }}
              </button>
            </ng-container>
          </div>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <form #f="ngForm" novalidate>
            <ng-container *ngIf="modify; else only_visualization">
              <div class="row" *ngFor="let cert of certificates; let index = index">
                <div class="col-sm-12 col-md-3 form-group">
                  <label for="nameCertification-{{index}}">{{'UBZ.SITE_CONTENT.1100000001' | translate }}*</label>
                  <div class="custom-select">
                    <select class="form-control" name="nameCertification-{{index}}" [(ngModel)]="cert.nameCertification" required>
                      <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                      <option *ngFor="let item of (certificatesDomain | domainMapToDomainArray)" value="{{item.domCode}}">{{ item.translationCod | translate }}</option>
                    </select>
                  </div>
                </div>
              </div>
              <div class="row">
                <div class="col-sm-12 btn-set">
                  <button *appAuthKey="'UBZ_REGISTRY.EXPERT_CERTIFICATIONS_ADD_NEW'" class="btn btn-primary waves-effect pull-right" [disabled]="f.invalid" type="button" (click)="addNewCertificate()">{{'UBZ.SITE_CONTENT.1100000010' | translate }}</button>
                </div>
              </div>
            </ng-container>
          </form>
        </div>
      </div>
    </div>
  </accordion-group>
</form>

<ng-template #only_visualization>
  <div class="row" *ngFor="let cert of certificates">
    <div class="col-sm-12 col-md-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.1100000001' | translate }}</label>
      {{ certificatesDomain[cert.nameCertification]?.translationCod | translate }}
    </div>
  </div>
</ng-template>
