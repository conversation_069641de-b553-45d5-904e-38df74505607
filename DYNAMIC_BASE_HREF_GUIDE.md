# Dynamic Base Href Implementation Guide

## Overview

This implementation provides dynamic base href functionality for the DeTrim application, allowing it to work with different application codes (UBZ and ZA0) by automatically setting the correct base href at runtime.

## How It Works

### 1. JavaScript-based Detection (index.html)

The base href is determined and set before Angular bootstraps using JavaScript in `index.html`. This ensures the correct base href is available from the very beginning of the application lifecycle.

**Detection Priority:**
1. URL parameter `?appCode=ZA0` or `?appCode=UBZ`
2. Path contains `/ZA0/`
3. Hostname contains `za0` or `detrim-za0`
4. Default to `UBZ`

### 2. Utility Functions (app-code.util.ts)

Utility functions provide easy access to app code information throughout the application:

```typescript
import { getCurrentAppCode, isZA0, isUBZ } from './shared/utils/app-code.util';

// Get current app code
const appCode = getCurrentAppCode(); // Returns 'UBZ' or 'ZA0'

// Check app code
if (isZA0()) {
  // ZA0-specific logic
}

if (isUBZ()) {
  // UBZ-specific logic
}
```

## Usage Examples

### 1. URL-based Switching

You can switch between applications using URL parameters:

```
# Switch to ZA0
https://your-domain.com/UBZ-EFA-PF/UBZ/?appCode=ZA0

# Switch to UBZ
https://your-domain.com/UBZ-EFA-PF/ZA0/?appCode=UBZ
```

### 2. In Components

```typescript
import { Component } from '@angular/core';
import { getCurrentAppCode, isZA0 } from '../shared/utils/app-code.util';

@Component({
  selector: 'my-component',
  template: `
    <div *ngIf="isZA0App">
      ZA0-specific content
    </div>
    <div *ngIf="!isZA0App">
      UBZ-specific content
    </div>
  `
})
export class MyComponent {
  get isZA0App(): boolean {
    return isZA0();
  }
}
```

### 3. In Services

```typescript
import { Injectable } from '@angular/core';
import { getCurrentAppCode } from '../utils/app-code.util';

@Injectable()
export class MyService {
  
  getApiEndpoint(): string {
    const appCode = getCurrentAppCode();
    return `/UBZ-ESA-RS/service/${appCode.toLowerCase()}/endpoint`;
  }
}
```

### 4. Programmatic Navigation

```typescript
import { navigateToAppCode } from '../shared/utils/app-code.util';

// Switch to ZA0 and preserve current path
navigateToAppCode('ZA0', true);

// Switch to UBZ without preserving path
navigateToAppCode('UBZ', false);
```

## Configuration

### Environment-specific Setup

For different environments, you can configure hostname-based detection:

**Development:**
- `localhost` → UBZ (default)
- `localhost?appCode=ZA0` → ZA0

**Testing:**
- `detrim-test.domain.com` → UBZ
- `detrim-za0-test.domain.com` → ZA0

**Production:**
- `detrim.domain.com` → UBZ
- `detrim-za0.domain.com` → ZA0

### Browser-specific Logic

The existing browser detection logic in `app.component.ts` now uses the dynamic app code:

```typescript
// Get current app code dynamically
const appCode = getCurrentAppCode();

// Apply ZA0-specific browser restrictions
if (appCode === "ZA0" && (isIE || isChrome)) {
  this.browserDetectionService.redirectToEdge(true);
}
```

## Testing

### Manual Testing

1. **Default UBZ behavior:**
   - Open: `http://localhost:4200/`
   - Should show UBZ base href: `/UBZ-EFA-PF/UBZ/`

2. **ZA0 via URL parameter:**
   - Open: `http://localhost:4200/?appCode=ZA0`
   - Should show ZA0 base href: `/UBZ-EFA-PF/ZA0/`

3. **ZA0 via hostname:**
   - Configure local hostname with `za0`
   - Should automatically detect ZA0

### Console Verification

Check browser console for logs:
```
Dynamic base href set to: /UBZ-EFA-PF/ZA0/
App code determined as: ZA0
Current app code: ZA0
Is ZA0 app: true
```

## Integration with Existing Features

### Silos Integration

The existing Silos redirection logic now uses dynamic app code detection:

```typescript
// In app.component.ts
const appCode = getCurrentAppCode(); // Instead of hardcoded "ZA0"

if (appCode === "ZA0" && (isIE || isChrome)) {
  this.browserDetectionService.redirectToEdge(true);
}
```

### Menu and Navigation

All existing navigation should work seamlessly as Angular uses the base href automatically.

## Troubleshooting

### Common Issues

1. **Base href not updating:**
   - Check browser console for JavaScript errors
   - Verify the script in index.html is executing

2. **Wrong app code detected:**
   - Check URL parameters
   - Verify hostname configuration
   - Check console logs for detection logic

3. **Navigation issues:**
   - Ensure all internal links are relative
   - Check that base href is properly set before Angular bootstrap

### Debug Information

Add this to any component to debug:

```typescript
ngOnInit() {
  console.log('Current app code:', getCurrentAppCode());
  console.log('Current base href:', getCurrentBaseHref());
  console.log('Is ZA0:', isZA0());
  console.log('Window app code:', (window as any).DETRIM_APP_CODE);
}
```

## Future Enhancements

1. **Additional App Codes:** Easy to extend for more application variants
2. **User Preferences:** Store user's preferred app code in localStorage
3. **Admin Interface:** UI for switching between app codes (see AppCodeSwitcherComponent)
4. **Environment Detection:** Automatic detection based on deployment environment
