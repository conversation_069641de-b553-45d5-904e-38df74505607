export class Field {
  span: number;
  code: string;
  description: string;
  disabled: boolean;
  domainCod: string;
  label: string;
  mandatory: string;
  pattern: string;
  readOnlyInSection: boolean;
  readOnly: string;
  renderIf: string;
  styleClass: string;
  tooltip: string;
  type: string;
  usage: string;
  value: string;
  xpath: string;
  placeholder: string;
  showLabel: boolean;
  fieldValue: any;
  validationRule: string;
  required: string;
  precision: number;
  scale: number;
  minStep: number;
  minValue: number;
  parentFieldCod: string;
  parentFieldValue: string;
  domainExpired?: boolean;
}
