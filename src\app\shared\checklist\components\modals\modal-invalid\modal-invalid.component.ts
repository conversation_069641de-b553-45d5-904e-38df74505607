import {
  Component,
  OnInit,
  EventEmitter,
  Input,
  Output
} from '@angular/core';
import { ChecklistService } from '../../../service/checklist.service';
import { DomainService } from '../../../../domain/domain.service';
import { TranslateService } from '@ngx-translate/core';
import { Note } from '../../../../notes/model/note';
import { NotesService } from '../../../../notes/services/notes.service';

@Component({
  selector: 'app-modal-invalid',
  templateUrl: './modal-invalid.component.html',
  styleUrls: ['./modal-invalid.component.css'],
  providers: [NotesService]
})
export class ModalInvalidComponent implements OnInit {
  @Input() isOpen = false;
  @Input() document: any;
  @Input() section: any;
  @Output() modalClose = new EventEmitter();
  isValidClass = { Y: 'state green', N: 'state red' };
  reasonOfInvalidation = '';
  notes = '';
  reasonsDomain = {};

  constructor(
    private checklistService: ChecklistService,
    private noteService: NotesService,
    private domainService: DomainService,
    private translateService: TranslateService
  ) { }

  ngOnInit() {
    this.domainService.newGetDomain('UBZ_DOM_CHK_INVAL_DOC').subscribe(x => {
      this.reasonsDomain = x;
    });
  }

  closeModal(refreshPage: boolean) {
    this.isOpen = false;
    this.modalClose.emit(refreshPage);
  }

  invalidateDocument() {
    this.checklistService
      .newInvalidateDocument(this.document.prog, this.reasonOfInvalidation).subscribe(() => {
        const nota: Note = new Note(this.document.prog, 'CHK', 'M');
        nota['noteTitle'] = this.translateService.instant(this.reasonsDomain[this.reasonOfInvalidation].translationCod);
        nota['noteDesc'] = this.notes;
        nota['noteType'] = 'CHK';
        this.noteService.saveNote(nota).subscribe();
        this.closeModal(true);
      });
  }
}
