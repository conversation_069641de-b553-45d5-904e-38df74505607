import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';

@Injectable()
export class AppraisalMigrationService {
  constructor(private http: Http) {}

  public getAppraisalDetail(appraisalId: string) {
    const url = `/UBZ-ESA-RS/service/migration/v1/appraisals/${appraisalId}`;
    // '/assets/data/appraisal-migration/fake-appraisal-migration.json';
    return this.http.get(url).map((resp: Response) => resp.json());
  }

  public getAssetDetail(assetId: string) {
    const url = `/UBZ-ESA-RS/service/migration/v1/appraisalObjects/${assetId}`;
    // const url = `/assets/data/appraisal-migration/fake-appraisal-migration-asset.json`;
    return this.http.get(url).map((resp: Response) => resp.json());
  }
}
