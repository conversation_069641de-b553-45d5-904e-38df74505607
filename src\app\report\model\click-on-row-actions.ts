export class ClickOnRowActions {
  UBZ_REPORT_REN: any;
  UBZ_REPORT_REN_PER: any;
  UBZ_REPORT_MON: any;
  UBZ_REPORT_MON_OPI: any;
  UBZ_REPORT_MON_OPI_ING: any;
  UBZ_REPORT_MON_OPI_SOPR: any;
  UBZ_REPORT_MON_OPI_RED: any;
  UBZ_REPORT_MON_TOT: any;
  UBZ_REPORT_MON_RIC: any;
  UBZ_REPORT_SORV: any;
  UBZ_REPORT_SORV_GEO: any;
  UBZ_REPORT_SORV_GAU1: any;
  UBZ_REPORT_SORV_GAU2: any;
  UBZ_REPORT_SORV_GAU1_DETAIL: any;
  UBZ_REPORT_SORV_ALG: any;
  UBZ_REPORT_SORV_ALG_SEC: any;
  UBZ_REPORT_SORV_CAR: any;
  UBZ_REPORT_SORV_CAR_SEC: any;

  public constructor() {
    this.UBZ_REPORT_REN = {};
    this.UBZ_REPORT_REN.FD_LAV_AUT = new OnClickAction('report/AL', 'b');
  }
}

export class OnClickAction {
  navigateTo: string;
  openModalName: string;

  public constructor(navigateTo: string, openModalName: string) {
    this.navigateTo = navigateTo;
    this.openModalName = openModalName;
  }
}
