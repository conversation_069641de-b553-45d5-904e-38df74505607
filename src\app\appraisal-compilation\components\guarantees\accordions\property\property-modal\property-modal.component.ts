import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { MessageService } from '../../../../../../shared/messages/services/message.service';
import { TranslateService } from '@ngx-translate/core';
import { DomainService } from '../../../../../../shared/domain/domain.service';

@Component({
  selector: 'app-property-modal',
  templateUrl: './property-modal.component.html',
  styleUrls: ['./property-modal.component.css'],
})
export class PropertyModalComponent implements OnInit {
  @Input()
  modalType: string; // add / modify
  @Input()
  isOpen: boolean;
  @Input()
  property; // contiene l'oggetto per l'aggiunta o modifica
  @Input()
  propertyIndex;
  @Input()
  propertyArray: any[];
  @Input()
  actualAmount: any[];
  @Input()
  domainResp: any; // UBZ_DOM_RIGHT_TYPE
  @Output()
  modalClose = new EventEmitter();
  @Output()
  modalSubmit = new EventEmitter();

  propertyCopy: any; // Copia dell'oggett originale per permettere la modifica dei valori
  actualAmountCopy: any[] = []; // Copia dell'array originale per permettere la modifica dei valori e i calcoli
  rightTypeDomain: any;

  constructor(
    private messageService: MessageService,
    private translateService: TranslateService
  ) {}

  ngOnInit() {
    this.propertyCopy = JSON.parse(JSON.stringify(this.property));
    this.actualAmountCopy = JSON.parse(JSON.stringify(this.actualAmount));
    this.actualAmountCopy.forEach(element => {
      if (element.rightType === this.property.rightType) {
        element.sum -= Number(this.property.ownershipPerc);
      }
    });
    if(this.domainResp) {
      this.rightTypeDomain = this.domainResp;
    }
    
  }

  closeModal() {
    this.modalClose.emit();
  }

  /**
   * @function
   * @name submit
   * @description Se siamo in inserimento titolare controlla che non ci sia lo stesso soggetto con il medesimo tipo di diritto.
   * Per il tipo diritto indicato calcola l'ammontare già inserito, se la somma risulta > 100 ne blocca il salvataggio.
   */
  submit() {
    const existIndex = this.checkIfExists(this.propertyCopy);
    if (
      existIndex !== -1 &&
      this.propertyCopy.index !== existIndex &&
      this.modalType === 'add'
    ) {
      this.messageService.showError(
        this.translateService.instant('UBZ.SITE_CONTENT.10011100100'),
        this.translateService.instant('UBZ.SITE_CONTENT.10011100101')
      );
      return;
    }
    for (const el of this.actualAmountCopy) {
      if (el.rightType === this.propertyCopy.rightType) {
        if (Number(el.sum) + Number(this.propertyCopy.ownershipPerc) > 100) {
          // se somma di categoria è oltre il 100
          this.messageService.showError(
            this.translateService.instant('UBZ.SITE_CONTENT.1000011111'),
            this.translateService.instant('UBZ.SITE_CONTENT.1000100000')
          );
          return;
        }
      }
    }
    this.modalSubmit.emit({
      property: this.propertyCopy,
      isStatic: false,
      index: this.propertyIndex
    });
  }

  /**
   * @function
   * @name checkIfExists
   * @description Controlla se l'oggetto titolare che si sta inserendo è già presente nell'array dei titolari.
   * In caso affermativo ne restituisce l'indice, altrimenti viene restituito -1 per indicarne l'assenza
   * @param {Object} objProperty Oggetto contenente i dettagli per il titolare in inserimento
   */
  checkIfExists(objProperty): number {
    let index = 0;
    for (const element of this.propertyArray) {
      if (objProperty.customer.ndgType === element.customer.ndgType) {
        if (
          (objProperty.customer.taxNum &&
            objProperty.customer.taxNum === element.customer.taxNum) ||
          (objProperty.customer.vatNum &&
            objProperty.customer.vatNum === element.customer.vatNum)
        ) {
          if (objProperty.rightType === element.rightType) {
            return index;
          }
        }
      }
      index++;
    }
    return -1;
  }

  /**
   * @name resetDataStructure
   * @description Reinizializza i valori delle property dell'oggetto propertyCopy
   * in seguito alla modifica del tipo soggetto
   */
  resetDataStructure() {
    this.propertyCopy.customer.name = null;
    this.propertyCopy.customer.surname = null;
    this.propertyCopy.customer.taxNum = null;
    this.propertyCopy.customer.heading = null;
    this.propertyCopy.customer.vatNum = null;
  }
}
