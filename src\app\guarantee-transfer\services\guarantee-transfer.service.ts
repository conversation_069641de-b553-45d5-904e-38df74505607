import { Injectable } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { Http, Response } from '@angular/http';
import { Router } from '@angular/router';

@Injectable()
export class GuaranteeTransferService {
  wizardStep = 1;
  ndgOrigin: string = '';
  ndgDestination: string = '';
  ndgAppraisals: Object[] = new Array(); // Perizie associate all'ndg origine
  guaranteeFromList: any[] = new Array();
  guaranteeToList: any[] = new Array();
  appraisalList: any[] = new Array();
  
  guaranteeTo: any;
  // guaranteeFrom e assetType sono valorizzato in startGuaranteesComponent per esssere utilizzate in targetGuaranteesComponent
  guaranteeFrom: any;
  collatTecTo: any;
  assetType: any;
  // Oggetto passato a positionHeader per mostrare le selezioni durante il wizard di sostituzioneGaranzia
  staticHeaderArray: Object[] = new Array();

  constructor(private http: Http, private router: Router) { }

  /**
   * @name reset
   * @description Resetta tutte le variabili del service
  */
  reset() {
    this.wizardStep = 1;
    this.ndgDestination = '';
    this.ndgAppraisals = new Array(); // Perizie associate all'ndg origine
    this.guaranteeFromList = new Array();
    this.guaranteeToList = new Array();
    this.appraisalList = new Array();
    this.guaranteeTo = null;
    this.guaranteeFrom = null;
    this.collatTecTo = null;
    this.assetType = null;
  }

  /**
   * @name initializeStaticHeaderArray
   * @description Inizializza staticHeaderArray
  */
  initializeStaticHeaderArray() {
    this.staticHeaderArray = [
      {
        label: 'UBZ.SITE_CONTENT.10010111011',
        value: this.ndgOrigin
      },
      {
        label: 'UBZ.SITE_CONTENT.10010111100',
        value: this.ndgDestination
      },
      {
        label: 'UBZ.SITE_CONTENT.10010111101',
        value: null
      },
      {
        label: 'UBZ.SITE_CONTENT.10010111110',
        value: null
      }
    ];
  }

  /**
   * @name getNdgAppraisals
   * @description Recupera le perizie legate al ndg di partenza per avviare il wizard di "Sostituzione garanzia"
   * @param {string} ndg - Ndg di origine
   */
  getNdgAppraisals(ndg: string) {
    return this.http
      .post('/UAM-ESA-RS/service/guarantee/v2/list/appraisal', { ndg: ndg })
      .map((resp: Response) => resp.json());
  }

  getAssociatedGuarantees(ndg: string, id?: string) {
    const url = '/UAM-ESA-RS/service/guarantee/v1/list';
    return this.http
      .post(url, { ndg: ndg, id: id })
      .map((resp: Response) => resp.json());
  }

  // Recupera gli asset associate alle garanzie del ndg in caso di richiesta di frazionamento
  // ndg - ndg selezionato per il frazionamento
  // type - Codice di familyType (IMM o MOB)
  // guaranteeList - oggetto con le garanzie associate per ndg
  getAssociatedGuaranteesFrg(ndg: string, type: string, guaranteeList: Object) {
    return this.http
      .post(`/UBZ-ESA-RS/service/collateralFrg/v1/getAssetList/${ndg}/${type}`, guaranteeList)
      .map((resp: Response) => {
        return resp.json();
      });
  }

  getGuaranteesTo(ndg: string, appraisalType: string) {
    const url = `/UBZ-ESA-RS/service/collateral/v1/collaterals/${ndg}/${appraisalType}/fromFidieGaranzie`;
    return this.http.get(url).map((resp: Response) => resp.json());
  }

  saveGuaranteeTransfer(
    ndg: string,
    guaranteeFrom: string,
    assetType: string,
    guaranteeTo: string,
    assets: string[]
  ) {
    const url = '/UAM-ESA-RS/service/guarantee/v1/saveChanges';
    return this.http.post(url, {
      ndg: ndg,
      guaranteeFrom: guaranteeFrom,
      type: assetType,
      guaranteeTo: guaranteeTo,
      assets: assets
    });
  }

  saveSelectedAppraisal(appraisalList: any) {
    const url = `/UAM-ESA-RS/service/guarantee/v2/list/collateral`;
    return (
      this.http
        .post(url, appraisalList)
        .map(resp => {
          this.guaranteeFromList = resp.json();
          return Observable.empty();
        })
    );
  }

  checkCollaterals(input: any) {
    const url = '/UAM-ESA-RS/service/guarantee/v2/list/collateral/check';
    return this.http.post(url, input).map(resp => {
      this.guaranteeToList = resp.json();
      return Observable.empty();
    });
  }


  changeGuarantee(
    ndgFrom: string,
    ndgDestination: string,
    collatFrom: string,
    collatTo: string,
    type: string,
    realAppraisals: string[]

  ) {
    const originAppraisals: string[] = [];

    for (const appr of this.appraisalList) {
      originAppraisals.push(appr.appraisalId);
    }
    const input = {
      ndgFrom: ndgFrom,
      ndgTo: ndgDestination,
      collateralFrom: collatFrom,
      collateralTo: collatTo,
      type: type,
      originAppraisals: originAppraisals,
      realAppraisals: realAppraisals
    };
    const url = '/UAM-ESA-RS/service/guarantee/v3/saveChanges';
    
    return this.http.post(url, input);
  }

  /**
   * @name cancelRequest
   * @description Forza l'uscita dal wizard di sostituzione garanzia effettuando navigazione sulla home
   */
  cancelRequest() {
    this.wizardStep = 1;
    this.router.navigate(['/']);
  }

  /**
   * @name getNdgDetails
   * @description Recupera le informazioni associate al ndg
   * @param {string} ndg ndg identificativo del quale si vogliono recuperare le info
   */
  getNdgDetails(ndg: string) {
    const url = `/UBZ-ESA-RS/service/anagrafe/v1/customers/getCustomerFromId/${ndg}`;
    return this.http.get(url).map((resp: Response) => resp.json());
  }

  public checkAnacreditGuarantees(appraisalList: string[]): Observable<any> {
    const url = '/UBZ-ESA-RS/service/anacredit/v1/position/check';
    return this.http.post(url, appraisalList).map(res => res.json());
  }
}