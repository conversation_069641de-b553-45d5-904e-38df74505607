<app-position-header [positionId]="positionId" [wizardCode]="wizardCode" [lockingUser]="_genericTaskService.taskLockingUser"></app-position-header>

<div class="row">
  <div class="col-sm-12 section-headline">
    <ng-container [ngSwitch]="taskCod">
      <h1 *ngSwitchCase="constants.tasks.manualAssignment"><i class="icon-print_checklist"></i> {{'UBZ.SITE_CONTENT.*********'
        | translate }} </h1>
      <h1 *ngSwitchCase="constants.tasks.fixAppointment"><i class="icon-print_checklist"></i> {{'UBZ.SITE_CONTENT.*********'
        | translate }} </h1>
      <h1 *ngSwitchCase="constants.tasks.przChecks"><i class="icon-print_checklist"></i> {{'UBZ.SITE_CONTENT.1100111011'
        | translate }} </h1>
      <h1 *ngSwitchCase="constants.tasks.checkAsset"><i class="icon-print_checklist"></i> {{'ubz.task.checkAsset'
        | translate }} </h1>
      <h1 *ngSwitchCase="constants.tasks.waitInspection"><i class="icon-print_checklist"></i> {{'ubz.task.waitinspection'
        | translate }} </h1>
      <h1 *ngSwitchDefault><i class="icon-print_checklist"></i> {{'UBZ.SITE_CONTENT.*********' | translate }} </h1>
    </ng-container>
    <h2>{{'UBZ.SITE_CONTENT.*********' | translate }} {{positionId}} - {{opinionType | translate}} {{ (bankCod==='00100')? ("- " + ('UBZ.SITE_CONTENT.***********' | translate)) : ''}} <span *ngIf="isCtu">{{'UBZ.SITE_CONTENT.*********' | translate }} CTU</span></h2>
  </div>
</div>

<section id="breadcrumbs" class="breadcrumbs">
  <div class="row">
    <div class="col-sm-12">
      <ul>
        <li>
          <a role="button" (click)="goToDashboard()">{{'UBZ.SITE_CONTENT.11' | translate }}</a>
        </li>
        <ng-container [ngSwitch]="taskCod">
          <li *ngSwitchCase="constants.tasks.manualAssignment">{{'UBZ.SITE_CONTENT.*********' | translate }}</li>
          <li *ngSwitchCase="constants.tasks.fixAppointment">{{'UBZ.SITE_CONTENT.*********' | translate }}</li>
          <li *ngSwitchCase="constants.tasks.checkAsset">{{'ubz.task.checkAsset' | translate }}</li>
          <li *ngSwitchCase="constants.tasks.waitInspection">{{'ubz.task.waitinspection' | translate }}</li>
          <li *ngSwitchDefault>{{'UBZ.SITE_CONTENT.*********' | translate }}</li>
        </ng-container>
      </ul>
    </div>
  </div>
</section>
<ng-container *appAuthKey="'UBZ_APPRAISAL_DETAILS_ABANDON'">
  <app-drop-assignment-button [positionId]="positionId" type="ABB" (saveSuccessful)="goToDashboard()"></app-drop-assignment-button>
</ng-container>
<app-task-list-access-rights *ngIf="showTaskButtons && this.taskCod !== this.constants.SAMPLE_CHECKS" [positionId]="positionId"></app-task-list-access-rights>

<section id="details" class="details">
  <div class="row">

    <div class="uc-datatabs">
      <!-- Nav tabs -->
      <ul class="nav nav-tabs" role="tablist">
        <ng-container>
          <li *ngIf="this.taskCod === this.constants.SAMPLE_CHECKS" role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.SAM}"><a
              (click)="selezionaTab(activeSectionEnum.SAM)" aria-controls="note" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.10111010'
              | translate }}</a></li>
        </ng-container>
        <ng-container *ngIf="!showTaskButtons">
          <ng-container [ngSwitch]="taskCod">
            <li *ngSwitchCase="constants.tasks.manualAssignment" role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.ASS}"><a
                (click)="selezionaTab(activeSectionEnum.ASS)" aria-controls="assegnazione" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.101101011'
                | translate }}</a></li>
            <li *ngSwitchCase="constants.tasks.fixAppointment" role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.HIS}"><a
                (click)="selezionaTab(activeSectionEnum.HIS)" aria-controls="history" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.*********'
                | translate }}</a></li>
            <li *ngSwitchCase="constants.tasks.waitInspection" role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.WTI}"><a
                (click)="selezionaTab(activeSectionEnum.WTI)" aria-controls="waitInspection" role="button" data-toggle="tab">{{'ubz.task.waitinspection' | translate }}</a></li>
            <li *ngSwitchCase="constants.tasks.checkAsset" role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.CKA}"><a
                (click)="selezionaTab(activeSectionEnum.CKA)" aria-controls="assignCollateralsToAssets" role="button" data-toggle="tab">{{'ubz.task.checkAsset' | translate }}</a></li>
            <li *ngSwitchCase="constants.tasks.inspectionOutcome" role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.STO}"><a
                (click)="selezionaTab(activeSectionEnum.STO)" aria-controls="storicoSopraluogo" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.1100111100'
                | translate }}</a></li>
            <li *ngSwitchDefault role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.DET}"><a (click)="selezionaTab(activeSectionEnum.DET)"
                aria-controls="detail" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.*********' | translate }}</a></li>
          </ng-container>
          <ng-container *appAuthKey="'UBZ_GENERIC.TASK_SIN'">
            <li role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.SIN}"><a (click)="selezionaTab(activeSectionEnum.SIN)"
                aria-controls="sintesi" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.101101100' | translate }}</a></li>
          </ng-container>
        </ng-container>
        <ng-container *ngIf="showTaskButtons">
          <ng-container *appAuthKey="'UBZ_GENERIC.TASK_SIN'">
            <li role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.SIN}"><a (click)="selezionaTab(activeSectionEnum.SIN)"
                aria-controls="sintesi" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.101101100' | translate }}</a></li>
          </ng-container>
          <ng-container [ngSwitch]="taskCod">
            <li *ngSwitchCase="constants.tasks.manualAssignment" role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.ASS}"><a
                (click)="selezionaTab(activeSectionEnum.ASS)" aria-controls="assegnazione" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.101101011'
                | translate }}</a></li>
            <li *ngSwitchCase="constants.tasks.fixAppointment" role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.HIS}"><a
                (click)="selezionaTab(activeSectionEnum.HIS)" aria-controls="history" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.*********'
                | translate }}</a></li>
            <li *ngSwitchCase="constants.tasks.waitInspection" role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.WTI}"><a
                (click)="selezionaTab(activeSectionEnum.WTI)" aria-controls="waitInspection" role="button" data-toggle="tab">{{'ubz.task.waitinspection' | translate }}</a></li>
            <li *ngSwitchCase="constants.tasks.checkAsset" role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.CKA}"><a
              (click)="selezionaTab(activeSectionEnum.CKA)" aria-controls="assignCollateralsToAssets" role="button" data-toggle="tab">{{'ubz.task.checkAsset' | translate }}</a></li>
            <li *ngSwitchCase="constants.tasks.inspectionOutcome" role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.STO}"><a
                (click)="selezionaTab(activeSectionEnum.STO)" aria-controls="storicoSopraluogo" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.1100111100'
                | translate }}</a></li>
            <li *ngSwitchDefault role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.DET}"><a (click)="selezionaTab(activeSectionEnum.DET)"
                aria-controls="assegnazione" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.*********' | translate }}</a></li>
          </ng-container>
        </ng-container>
        <ng-container *appAuthKey="'UBZ_GENERIC.TASK_ALS'">
          <li role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.ALS}"><a (click)="selezionaTab(activeSectionEnum.ALS)"
              aria-controls="apiLogStatus" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.10011100111' | translate}}</a></li>
        </ng-container>
        <ng-container *appAuthKey="'UBZ_GENERIC.TASK_CHE'">
          <li role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.CHE}"><a (click)="selezionaTab(activeSectionEnum.CHE)"
              aria-controls="checklist" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.101101101' | translate }}</a></li>
        </ng-container>
        <ng-container *appAuthKey="'UBZ_GENERIC.TASK_CHE_SERVICING'">
          <li role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.CHE_SERV}"><a (click)="selezionaTab(activeSectionEnum.CHE_SERV)"
              aria-controls="checklist-servicing" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.101101101' | translate }}</a></li>
        </ng-container>
        <ng-container *appAuthKey="'UBZ_GENERIC.TASK_NOT'">
          <li role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.NOT}"><a (click)="selezionaTab(activeSectionEnum.NOT)"
              aria-controls="note" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.11000101' | translate }}</a></li>
        </ng-container>
        <ng-container *appAuthKey="'UBZ_GENERIC.TASK_CON'">
          <li role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.CON}"><a (click)="selezionaTab(activeSectionEnum.CON)"
              aria-controls="esitoControlli" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.1100111011' | translate }}</a></li>
        </ng-container>
        <ng-container *appAuthKey="'UBZ_GENERIC.TASK_SUR'">
          <li *ngIf="hasQuestionario === true" role="presentation" [ngClass]="{active: activeSection === activeSectionEnum.SUR}">
            <a (click)="selezionaTab(activeSectionEnum.SUR)" aria-controls="esitoQuestionario" role="button" data-toggle="tab">{{'UBZ.SITE_CONTENT.10011011000'
              | translate }}</a>
          </li>
        </ng-container>
      </ul>

    <div class="tab-content">

        <!-- Tab Storico sopralluogo -->
        <div role="tabpanel" id="storicoSopraluogo" *ngIf="activeSection === activeSectionEnum.STO">
          <app-site-inspection-result [positionId]="positionId"></app-site-inspection-result>
        </div>
        <!-- Fine tab Storico sopralluogo -->

        <!-- Tab Attesa sopralluogo -->
        <div role="tabpanel" id="waitInspection" *ngIf="activeSection === activeSectionEnum.WTI">
          <app-site-inspection-date [positionId]="positionId"></app-site-inspection-date>
        </div>
        <!-- Fine tab Attesa sopralluogo -->

        <!-- Tab Assegnazione -->
        <div role="tabpanel" id="assegnazione" *ngIf="activeSection === activeSectionEnum.ASS">
          <app-expert-table [positionId]="positionId" [isSecondOpinion]="isSecondOpinion"></app-expert-table>
        </div>
        <!-- Fine tab Assegnazione -->

        <!-- Tab Storico Perizia -->
        <div role="tabpanel" id="history" *ngIf="activeSection === activeSectionEnum.HIS">
          <app-send-appraisal [positionId]="positionId"></app-send-appraisal>
        </div>
        <!-- Fine tab Storico Perizia -->

        <!-- Tab Dettaglio Perizia -->
        <div role="tabpanel" id="detail" *ngIf="activeSection === activeSectionEnum.DET">
          <app-appraisal-detail [positionId]="positionId"></app-appraisal-detail>
        </div>
        <!-- Fine tab Dettaglio Perizia -->

        <!-- Tab Esito controlli -->
        <div role="tabpanel" id="controlResult" *ngIf="activeSection === activeSectionEnum.CON">
          <app-control-result [positionId]="positionId"></app-control-result>
        </div>
        <!-- Fine tab Esito controlli -->

        <!-- Tab Esito questionario -->
        <div role="tabpanel" id="esitoQuestionario" *ngIf="activeSection === activeSectionEnum.SUR">
          <app-survey-detail [positionId]="positionId"></app-survey-detail>
        </div>
        <!-- Fine tab Esito questionario -->

        <!-- Tab Sintesi -->
        <div role="tabpanel" id="sintesi" *ngIf="activeSection === activeSectionEnum.SIN">
          <ng-container *appAuthKey="'UBZ_GENERIC.TASK_VIEW_APPRAISAL'">
            <div *ngIf="showTaskButtons && originationProcess !== 'MIG' && !massiveUpload" class="col-sm-12 text-right">
              <button type="button" class="btn btn-empty pull-right" (click)="goToAppraisalCompile()">{{'UBZ.SITE_CONTENT.1010111001'
                | translate }}</button>
            </div>
          </ng-container>
          <ng-container *appAuthKey="'UBZ_GENERIC.TASK_VIEW_APPRAISAL_MIG'">
            <div *ngIf="showTaskButtons && originationProcess === 'MIG' || massiveUpload" class="col-sm-12 text-right">
              <button type="button" class="btn btn-empty pull-right" (click)="goToAppraisalCompile()">{{'UBZ.SITE_CONTENT.1010111001'
                | translate }}</button>
            </div>
          </ng-container>
          <div class="row row-eq-height">
            <div class="col-sm-4">
              <app-task-synthesis [positionId]="positionId" [translatedAppraisalType]="appraisalResp.appraisalType | translate" [translatedLoanScope]="appraisalResp.loanScope | translate"
                [credlineAmount]="appraisalResp.credlineAmount" [flagExternalAppraisal]="flagExternalAppraisal" [originationProcess]="originationProcess"
                [externalPositionId]="externalPositionId" [translatedAppraisalOwner]="appraisalOwner | translate" [assignmentType]="assignmentType"
                [translatedMacroProcess]="appraisalResp.macroProcess | translate" [appraisalParentId]="appraisalParentId" [originMigration]="originMigration"
                [isTemplateUpdate]="isTemplateUpdate" [updateType]="updateType"></app-task-synthesis>
            </div>
            <div class="col-sm-4">
              <app-task-assignment [expert]="expert" [reviser]="reviser" [socHeading]="socHeading" [surveyNecFlag]="appraisalResp.surveyNecFlag" [sendUcscFlag]="sendUcscFlag"></app-task-assignment>
            </div>
            <div class="col-sm-4">
              <app-customer-general-data [ndg]="customerResp.ndg" [heading]="customerResp.heading" [homeAddress]="customerResp.address"
                [homeCity]="customerResp.city" [homeZipCode]="customerResp.zipCod" [phone]="customerResp.phoneNum" [region]="customerResp.region"
                [translatedNdgType]="customerResp.ndgType | translate"></app-customer-general-data>
            </div>
          </div>


          <ng-container *ngIf="phaseCode === '600' && statusCode === 'PER-COV' || phaseCode === '300' && statusCode === 'PRZ-CHU'">
            <app-appraisal-values></app-appraisal-values>
          </ng-container>
          <app-associated-guarantees-table [list]="guaranteeList" [renderIdAsset]="true"></app-associated-guarantees-table>
          <app-appraisals-table [genericTask]="true" [isAppraisalConvalidated]="isAppraisalConvalidated" [list]="appraisalList"></app-appraisals-table>
        </div>
        <!-- Fine Tab Sintesi -->

        <!-- Tab Checklist -->
        <div role="tabpanel" id="checklist" *ngIf="activeSection === activeSectionEnum.CHE">
          <app-checklist [positionId]="positionId" [requestId]="requestId"  [isRequestId]="false"></app-checklist>
        </div>
        <!-- Fine tab Checklist -->

        <!-- Tab Note -->
        <div role="tabpanel" id="note" *ngIf="activeSection === activeSectionEnum.NOT">
          <app-notes [positionId]="positionId" positionType="PER"></app-notes>
        </div>
        <!-- Fine tab Note -->

        <!-- Tab Controlli a campione -->
        <div role="tabpanel" id="controlliACampione" *ngIf="activeSection === activeSectionEnum.SAM">
          <app-sample-checks-appraisal></app-sample-checks-appraisal>
        </div>
        <!-- Fine tab Controlli a campione -->

        <!-- Tab Api log status -->
        <div role="tabpanel" id="apiLogStatus" *ngIf="activeSection === activeSectionEnum.ALS">
          <app-api-log-status [positionId]="positionId"></app-api-log-status>
        </div>
        <!-- Fine tab Api log status -->

        <!-- Tab Assegna garanzie -->
        <div role="tabpanel" id="assignCollateralsToAssets" *ngIf="activeSection === activeSectionEnum.CKA">
          <app-asset-task [taskId]="taskId" [taskCod]="taskCod" [positionId]="positionId"></app-asset-task>
        </div>
        <!-- Fine tab Assegna garanzie -->
         <!--------------------------------------------SERVICING------------------------------------------------------------------------->

        <!-- Tab Checklist -->
        <div role="tabpanel" id="checklist-servicing" *ngIf="activeSection === activeSectionEnum.CHE_SERV">
          <app-checklist-servicing [positionId]="positionId" [requestId]="requestId"  [isRequestId]="false"></app-checklist-servicing>
        </div>
        <!-- Fine tab Checklist -->
        <!------------------------------------------------------------------------------------------------------------------------------->

      </div>
    </div>
  </div>
</section>
