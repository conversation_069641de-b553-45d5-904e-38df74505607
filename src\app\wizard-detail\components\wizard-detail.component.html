<app-position-header [positionId]="positionId" [wizardCode]="wizardCode" [avoidLocking]="true"></app-position-header>

<div class="row">
  <div class="col-sm-12 section-headline">
    <h1><i class="icon-print_checklist"></i>
      <span *ngIf="isDettaglioSimulazione">{{'UBZ.SITE_CONTENT.11111001' | translate }}</span>
      <span *ngIf="!isDettaglioSimulazione">{{'UBZ.SITE_CONTENT.11101001' | translate }}</span>
    </h1>
    <h2 *ngIf="isDettaglioSimulazione">{{'UBZ.SITE_CONTENT.10010' | translate }} {{positionId}}</h2>
    <h2 *ngIf="!isDettaglioSimulazione">{{'UBZ.SITE_CONTENT.10100101' | translate }} {{positionId}}</h2>
  </div>
</div>

<section id="breadcrumbs" class="breadcrumbs">
  <div class="row">
    <div class="col-sm-12">
      <ul>
        <li><a role="button" (click)="goToDashboard()">{{'UBZ.SITE_CONTENT.11' | translate }}</a></li>
        <li *ngIf="!isDettaglioSimulazione">{{'UBZ.SITE_CONTENT.11101001' | translate }}</li>
        <li *ngIf="isDettaglioSimulazione">{{'UBZ.SITE_CONTENT.11111001' | translate }}</li>

      </ul>
    </div>
  </div>
</section>

<section *ngIf="bpmTaskId && bpmTaskCod">
  <div class="row">
    <div class="col-sm-12">
      <app-task-access-rights [positionId]="positionId" [taskId]="bpmTaskId"
        [taskCod]="bpmTaskCod"></app-task-access-rights>
    </div>
  </div>
</section>

<section id="details" class="details">
  <div class="row">
    <div class="uc-datatabs">
      <!-- Nav tabs -->
      <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" [ngClass]="{active: selectedSection === 'sintesi'}"><a (click)="selezionaTab('sintesi')"
            aria-controls="sintesi" role="tab" data-toggle="tab">{{'UBZ.SITE_CONTENT.11101010' | translate }}</a></li>
        <ng-container *appAuthKey="'UBZ_WIZARD.DETAIL_HISTORY'">
          <li *ngIf="!isDettaglioSimulazione" role="presentation" [ngClass]="{active: selectedSection === 'storico'}"><a
              (click)="selezionaTab('storico');" aria-controls="storico" role="tab"
              data-toggle="tab">{{'UBZ.SITE_CONTENT.11101011'
              | translate }}</a></li>
        </ng-container>

        <li *ngIf="!isDettaglioSimulazione" role="presentation" [ngClass]="{active: selectedSection === 'checklist'}"><a
            (click)="selezionaTab('checklist');" aria-controls="checklist" role="tab"
            data-toggle="tab">{{'UBZ.SITE_CONTENT.101101101'
            | translate }}</a></li>


        <!-- <ng-container *appAuthKey="'UBZ_WIZARD.DETAIL_ACTIVITY'">
          <li *ngIf="!isDettaglioSimulazione" role="presentation" [ngClass]="{active: selectedSection === 'attivita'}"><a (click)="selezionaTab('attivita');" aria-controls="attivita" role="tab" data-toggle="tab">{{'UBZ.SITE_CONTENT.110010000' | translate }}</a></li>
        </ng-container> -->
        <ng-container *appAuthKey="'UBZ_WIZARD.DETAIL_NOTE'">
          <li role="presentation" [ngClass]="{active: selectedSection === 'note'}"><a (click)="selezionaTab('note');"
              aria-controls="note" role="tab" data-toggle="tab">{{'UBZ.SITE_CONTENT.11000101' | translate }}</a></li>
        </ng-container>
      </ul>

      <!-- Tab panes -->
      <div class="tab-content">
        <div role="tabpanel" [ngClass]="{'tab-pane active': selectedSection === 'sintesi'}" id="sintesi"
          *ngIf="selectedSection === 'sintesi';">

          <!-- Bottone per creare una nuova perizia -->
          <div class="row" *ngIf="showNewAppraisalButton">
            <div class="col-sm-12">
              <button type="button" class="btn btn-primary waves-effect pull-right"
                (click)="startAppraisalRequest()">{{'UBZ.SITE_CONTENT.1000110'
                | translate }}</button>
            </div>
          </div>

          <div class="row row-eq-height">
            <div class="col-sm-4">
              <div class="box">
                <h4 *ngIf="!isDettaglioSimulazione">{{'UBZ.SITE_CONTENT.11101100' | translate }}</h4>
                <h4 *ngIf="isDettaglioSimulazione">{{'UBZ.SITE_CONTENT.11111010' | translate }}</h4>
                <div class="row">
                  <div class="col-sm-6">
                    <label *ngIf="!isDettaglioSimulazione">{{'UBZ.SITE_CONTENT.10100101' | translate }}</label>
                    <label *ngIf="isDettaglioSimulazione">{{'UBZ.SITE_CONTENT.10010' | translate }}</label>
                  </div>
                  <div class="col-sm-6">
                    {{positionId}}
                  </div>
                </div>
                <div class="row" *ngIf="!isDettaglioSimulazione">
                  <div class="col-sm-6">
                    <label>{{'UBZ.SITE_CONTENT.1010110100' | translate }}</label>
                  </div>
                  <div class="col-sm-6">
                    {{ appraisalResp.creationDate | date:'dd-MM-yyyy' }}
                  </div>
                </div>
                <div class="row">
                  <div class="col-sm-6">
                    <label>{{'UBZ.SITE_CONTENT.1010001' | translate}}</label>
                  </div>
                  <div class="col-sm-6">
                    {{ getMacroprocess() | translate }}
                  </div>
                </div>
                <div class="row">
                  <div class="col-sm-6">
                    <label>{{'UBZ.SITE_CONTENT.11101101' | translate }}</label>
                  </div>
                  <div class="col-sm-6">
                    {{ getAppraisalType() | translate }}
                  </div>
                </div>
                <div class="row">
                  <div class="col-sm-6">
                    <label>{{'UBZ.SITE_CONTENT.1110001' | translate }}</label>
                  </div>
                  <div class="col-sm-6">
                    {{ getAppraisalScope() | translate }}
                  </div>
                </div>
                <div class="row">
                  <div class="col-sm-6">
                    <label>{{'UBZ.SITE_CONTENT.10000001' | translate }}</label>
                  </div>
                  <div class="col-sm-6">
                    {{ appraisalStatusTranslatedCode | translate }}
                  </div>
                </div>
                <div class="row">
                  <div class="col-sm-6">
                    <label>{{'UBZ.SITE_CONTENT.100000001110' | translate }}</label>
                  </div>
                  <div class="col-sm-6">
                    {{ (sendUcscFlag === 'Y' ? 'UBZ.SITE_CONTENT.100011011' : 'UBZ.SITE_CONTENT.100011100') | translate}}
                  </div>
                </div>
                <div
                  *ngIf="appraisalResp && (appraisalResp.originProcess === 'TGP' || (appraisalResp.originMigration && appraisalResp.originMigration === 'TGP'))"
                  class="row">
                  <div class="col-sm-6">
                    <label>{{'UBZ.SITE_CONTENT.10011101000' | translate}}</label>
                  </div>
                  <div class="col-sm-6">
                    {{ appraisalResp?.externalPosition }}
                  </div>
                </div>
                <div
                  *ngIf="appraisalResp && (appraisalResp.originProcess === 'EMP' || (appraisalResp.originMigration && appraisalResp.originMigration === 'EMP'))"
                  class="row">
                  <div class="col-sm-6">
                    <label>{{'UBZ.SITE_CONTENT.10011101001' | translate}}</label>
                  </div>
                  <div class="col-sm-6">
                    {{ appraisalResp?.externalPosition }}
                  </div>
                </div>
                <div
                  *ngIf="appraisalResp && (appraisalResp.originProcess === 'UB6' || (appraisalResp.originMigration && appraisalResp.originMigration === 'UB6'))"
                  class="row">
                  <div class="col-sm-6">
                    <label>{{'UBZ.SITE_CONTENT.10100101110' | translate}}</label>
                  </div>
                  <div class="col-sm-6">
                    {{ appraisalResp?.externalPosition }}
                  </div>
                </div>
                <div *ngIf="assignmentType !== null" class="row">
                  <div class="col-sm-6">
                    <label>{{'UBZ.SITE_CONTENT.10010110101' | translate }}</label>
                  </div>
                  <div class="col-sm-6">
                    {{assignmentType }}
                  </div>
                </div>
              </div>
            </div>
            <div class="col-sm-4">
              <app-customer-general-data [ndg]="customerResp.ndg" [heading]="customerResp.heading"
                [homeAddress]="customerResp.homeAddress" [homeCity]="customerResp.homeCity"
                [homeZipCode]="customerResp.homeZipCode" [phone]="customerResp.phone" [region]="customerResp.region"
                [translatedNdgType]="getNdgType() | translate"></app-customer-general-data>
            </div>
            <div class="col-sm-4">
              <app-customer-payment-data></app-customer-payment-data>
            </div>
          </div>
          <app-associated-guarantees-table *ngIf="!isDettaglioSimulazione" [renderIdAsset]="false"
            [list]="positionService.collateralsArray">
          </app-associated-guarantees-table>

          <app-appraisals-table></app-appraisals-table>
        </div>
        <div role="tabpanel" [ngClass]="{'tab-pane active': selectedSection === 'storico'}" id="storico"
          *ngIf="(selectedSection === 'storico') && (!isDettaglioSimulazione)">
          <h4 class="section-heading">{{'UBZ.SITE_CONTENT.11111000' | translate }}</h4>
          <table class="table table-hover">
            <thead>
              <tr>
                <th scope="col">{{'UBZ.SITE_CONTENT.10010001' | translate }}</th>
                <th scope="col">{{'UBZ.SITE_CONTENT.11101101' | translate }}</th>
                <th scope="col">{{'UBZ.SITE_CONTENT.10000001' | translate }}</th>
                <th scope="col">{{'UBZ.SITE_CONTENT.10010101001' | translate }}</th>
                <th scope="col"></th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let perizia of appraisalHistory">
                <td attr.data-label="{{'UBZ.SITE_CONTENT.10010001' | translate }}">{{ perizia.appraisalId }}</td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.110010001' | translate }}">{{ perizia.appraisalType | translate
                  }}</td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.10000001' | translate }}">{{ perizia.translatedStatus |
                  translate
                  }}</td>
                <td attr.data-label="{{'UBZ.SITE_CONTENT.10010101001' | translate }}">{{ perizia.opinionType | translate
                  }}</td>
                <td data-label=""><a role="button" (click)="goToAppraisalDetail(perizia.appraisalId)"><i
                      class="icon-angle-double-right"></i></a></td>
              </tr>
            </tbody>
          </table>
        </div>
        <!-- Storico attivita -->
        <!-- <div role="tabpanel" [ngClass]="{'tab-pane active': selectedSection === 'attivita'}" id="attivita" *ngIf="selectedSection === 'attivita';">
          <app-activity-history-table [positionId]="positionId"></app-activity-history-table>
        </div> -->
        <!-- Fine storico attivita -->
        <div role="tabpanel" id="checklist" *ngIf="selectedSection === 'checklist' ">
          <app-checklist [positionId]="positionId" [isRequestId]="true"></app-checklist>
        </div>

        <div role="tabpanel" [ngClass]="{'tab-pane active': selectedSection === 'note'}" id="note"
          *ngIf="selectedSection === 'note';">
          <app-notes [positionId]="positionId" [positionType]="positionType"></app-notes>
        </div>
      </div>
    </div>
  </div>
</section>