import { Component, OnInit, Input } from '@angular/core';
import { AssetDetails } from './tab-managed-asset-details.model';

@Component({
  selector: 'app-tab-managed-asset-details',
  templateUrl: './tab-managed-asset-details.component.html',
  styleUrls: ['./tab-managed-asset-details.component.css']
})
export class TabManagedAssetDetailsComponent implements OnInit {
  @Input() resItemId: number; // Default Case // from UBZ, in UBZ_APP_OBJECT_APPRAISALS
  @Input() assetDetail: Array<AssetDetails>   // from UAM, if appraisal status is PER-COV
  @Input() isMultipleAssetPerObject: boolean; // from UAM, if appraisal status is PER-COV
  @Input() isAppraisalConvalidated: boolean;  // from UBZ, in UBZ_APP_APPRAISALS

  constructor() { }

  ngOnInit() {
  }

  public isAssetStateAvailable(): boolean {
    return this.assetDetail && this.assetDetail.length > 0;
  }

  /**
   * Appraisal is not convalidated (not PER-COV) so it doesn't have access to data from UAM 
   * such as stato && promiscuo => 'assetDetail' list is empty => read ID Asset from 'resiItemId'.
   * 
   * This template is also used when the asset does come form an convaldiated appraisal (PER-COV),
   * but the data is simply missing from UAM ('assetDetail' is an empty list).
   */
  public getMissingAssetStateTemplate(): string {
    return `ID Asset: ${ this.resItemId ? this.resItemId : 'N/A' } \n` +
      `Stato asset: N/A \n` + `Asset promiscuo: N/A`;
  }

  /**
   * Flattens out the object details in the list into a single string to be used as a tooltip.
   * Tooltip binded to the "+" icon that is displayed when there are multiple assets per object.
   */
  public getMultipleAssetState(): string {
    return this.assetDetail.reduce((tooltip, assetDetail, index) => {
      return tooltip + this.reduceAssetStateToString(assetDetail) +
        (index !== this.assetDetail.length - 1 ? '\n\n' : ''); // inter-asset spacing
    }, '');
  }

  public getSingleAssetState(): string {
    if (this.assetDetail && this.assetDetail.length) {
      return this.reduceAssetStateToString(this.assetDetail[0]);
    }
  }

  private computeDisabledStatusValue(disabledStatus: boolean): string {
    if (disabledStatus !== null) {
      return disabledStatus === true ? 'Disabilitato' : 'Attivo';
    } else {
      return 'N/A';
    }
  }

  private computePromiscuoValue(promiscuo: boolean, disabledStatus: boolean): string {
    // promiscuo value is 'N/A' if the asset is disabled (disabilitato)
    if (promiscuo !== null && disabledStatus !== true) {
      return promiscuo === true ? 'Si' : 'No';
    } else {
      return 'N/A';
    }
  }

  private reduceAssetStateToString(assetDetail): string {
    const { assetId, disabledStatus, promiscuo } = assetDetail;
    return `ID Asset: ${ assetId ? assetId : 'N/A' } \n` +
      `Stato asset: ${ this.computeDisabledStatusValue(disabledStatus) } \n` +
      `Asset promiscuo: ${ this.computePromiscuoValue(promiscuo, disabledStatus) }`;
  }

}
