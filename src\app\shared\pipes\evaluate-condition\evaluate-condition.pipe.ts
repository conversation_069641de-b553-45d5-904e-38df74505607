import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'evaluateCondition',
  pure: true,
})
export class EvaluateConditionPipe implements PipeTransform {
  transform(cond: any, mapWithValues: any, expression, formCod, model): any {
    if (mapWithValues) {
      return this.evalAllExpr(mapWithValues, expression);
    } else {
      return this.evalExpression(formCod, expression, model);
    }
  }

  evalExpression(formCod: number, expression: string, model): boolean {
    try {
      if (expression === null) {
        return true;
      } else if (expression.indexOf('$') === -1) {
        return eval(expression);
      } else {
        expression = expression.replace('$', 'model[formCod].');
        return this.evalExpression(formCod, expression, model);
      }
    } catch (e) {
      console.error(
        'EXCEPTION ON APFORM COMPONENT AT EVAL EXPRESSION. FORMCOD: ' +
          formCod +
          ' , EXPRESSION: ' +
          expression
      );
    }
  }

  evalAllExpr(mapWithValues, expression: string): boolean {
    try {
      if (expression === null) {
        return true;
      } else if (expression.indexOf('$') === -1) {
        return eval(expression);
      } else {
        if (mapWithValues) {
          expression = expression.replace('$', 'mapWithValues.');
          return this.evalAllExpr(mapWithValues, expression);
        }
      }
    } catch (e) {
      console.error(
        'EXCEPTION ON APFORM COMPONENT AT EVAL EXPRESSION. FORMCOD: ' +
          ' , EXPRESSION: ' +
          expression
      );
    }
  }
}
