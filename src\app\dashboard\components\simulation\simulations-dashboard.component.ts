import { Component, OnInit } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { Navigable } from '../../model/navigable-interface';
import { Router } from '@angular/router';

import { DashboardDetailComponent } from '../dashboard-detail.component';
import { SearchService } from '../../../shared/search/search.service';
import { DomainService } from '../../../shared/domain/domain.service';

@Component({
  selector: 'app-simulations-dashboard',
  templateUrl: '../dashboard-detail.component.html',
  styleUrls: ['./simulations-dashboard.component.css']
})
export class SimulationsDashboardComponent extends DashboardDetailComponent
  implements OnInit, Navigable {
  headFields = this.pageHeaders['SIM'];
  fieldsConditions = this.pageFieldConditions['SIM'];

  constructor(
    public searchService: SearchService,
    public domainService: DomainService,
    public router: Router
  ) {
    super(domainService, searchService);
  }

  ngOnInit() {
    super.ngOnInit();
  }

  getPositionsCounts(
    inChargeUser: boolean,
    inChargeBranch: boolean
  ): Observable<any> {
    return this.searchService.getSimulationsCounts(
      inChargeUser,
      inChargeBranch
    );
  }

  getPositionsData(
    excel: boolean,
    page: number,
    pageSize: number,
    orderBy: string,
    asc: boolean,
    filter: any
  ): Observable<any> {
    return this.searchService.getSimulationsData(
      excel,
      page,
      pageSize,
      orderBy,
      asc,
      filter
    );
  }

  goToTarget(id) {
    this.router.navigateByUrl(`landing/WSIM/${id}`);
  }
}
