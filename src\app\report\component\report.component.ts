import { Component, OnInit, Input } from '@angular/core';
import { ReportService } from '../service/report.service';
import * as FileSaver from 'file-saver';
import { ClickOnRowActions } from '../model/click-on-row-actions';
import { Router } from '@angular/router';

@Component({
  selector: 'app-report',
  templateUrl: './report.component.html',
  providers: [ReportService]
})
export class ReportComponent implements OnInit {
  @Input() reportCode: string;
  @Input()
  set filters(filters: any) {
    if (filters !== this._filters) {
      this.changeFilterValue(filters);
    }
  }
  @Input() title: string;
  @Input() subTitle: string;
  _filters: any = null;
  table: any;
  pageSize = 10;
  page = 1;
  orderBy = '';
  orderAsc = true;
  filtersVisible = false;
  clickOnRowActions = new ClickOnRowActions();

  constructor(private reportService: ReportService, private router: Router) {}

  ngOnInit() {
    // this.calculateTable();
  }

  changeFilterValue(filterValue: any) {
    this._filters = filterValue;
    this.calculateTable();
  }

  changePageSize() {
    this.page = 1;
    this.calculateTable();
  }

  changePage(event: any) {
    this.page = event.page;
    this.calculateTable();
  }

  private calculateTable() {
    this.reportService
      .getReport(
        this.reportCode,
        this.page,
        this.pageSize,
        this.orderBy,
        this.orderAsc,
        false,
        this._filters
      )
      .subscribe(x => {
        this.table = x;
      });
  }

  private exctractReport() {
    this.reportService
      .getReport(
        this.reportCode,
        this.page,
        this.pageSize,
        this.orderBy,
        this.orderAsc,
        true,
        this._filters
      )
      .subscribe(file => {
        FileSaver.saveAs(file, 'document.xls');
      });
  }

  actionOnClick(field: any) {
    if (
      this.clickOnRowActions[this.reportCode][field.fieldCod].navigateTo !== ''
    ) {
      this.router.navigate([
        this.clickOnRowActions[this.reportCode][field.fieldCod].navigateTo
      ]);
    } else {
      // gestire apertura del modale (tramite output probabilmente)
    }
  }
}
