<fieldset>
<fieldset [disabled]="landingService.isLockedTask[currentTask]">
  <form #data="ngForm">
    <div class="container">
      <div class="row ">
        <div class="col-sm-12">
          <h3>{{ 'UBZ.SITE_CONTENT.100000010110' | translate | uppercase }}</h3>
        </div>
      </div>
      <div class="row myrow">
        <div class="col-sm-4">
          <label for="options">{{'UBZ.SITE_CONTENT.100000010111' | translate }}*</label>
          <select id="options" class="form-control" [(ngModel)]="select" name="select">
            <option value="" selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option value="option1">{{'UBZ.SITE_CONTENT.100000011000' | translate }} </option>
            <option value="option2">{{'UBZ.SITE_CONTENT.100000011001' | translate }}</option>
          </select>
        </div>
        <div class="col-sm-4">
          <ng-container *ngIf="select === 'option1'">
            <label for="user">{{'UBZ.SITE_CONTENT.100000011010' | translate }}*</label>
            <select id="user" class="form-control" (change)="getSelect()" [(ngModel)]="userSelect" name="userSelect">
              <option value="" *ngIf="allCustomers.length !== 1">
                Select
              </option>
                <option *ngFor="let u of allCustomers" [value]="u.name">
                  {{ u.name + " " + u.surname }}
                </option>
            </select>
          </ng-container>
        </div>
        <div class="col-sm-4">
          <ng-container *ngIf="select === 'option1'">
            <label for="ndg"><b>{{'UBZ.SITE_CONTENT.100000011011' | translate }}*</b></label>
            <input type="text" name="ndg" id="ndg" class="form-control" [(ngModel)]="mydata.ndg" disabled />
          </ng-container>
        </div>
      </div>
      <div class="row myrow" *ngIf="select === 'option1'">
        <div class="col-sm-4">
          <label for="rezidenza"><b>{{'UBZ.SITE_CONTENT.100000010000' | translate }}*</b></label>
          <input type="text" name="address" id="rezidenza" class="form-control" [(ngModel)]="mydata.address" disabled />
        </div>
        <div class="col-sm-4">
          <label for="city"><b>{{'UBZ.SITE_CONTENT.1110100000' | translate }}*</b></label>
          <input type="text" name="city" id="city" class="form-control" [(ngModel)]="mydata.city" disabled />
        </div>
        <div class="col-sm-4">
          <label for="provincia"><b>{{'UBZ.SITE_CONTENT.101000' | translate }}</b></label>
          <input type="text" name="province" id="provincia" class="form-control" [(ngModel)]="mydata.province"
            disabled />
        </div>
      </div>
      <div class="row myrow" *ngIf="select === 'option1'">
        <div class="col-sm-4">
          <label for="codiceFiscale"><b>{{'UMF_DATA_DICTIONARY|FD_COD_FIS' | translate }}*</b></label>
          <input type="text" name="taxCode" id="codiceFiscale" class="form-control" [(ngModel)]="mydata.taxCode"
            disabled />
        </div>
        <div class="col-sm-4">
          <label for="email"><b>{{'UBZ.SITE_CONTENT.10011101101' | translate }}*</b></label>
          <input type="text" name="email" id="email" class="form-control" [(ngModel)]="mydata.email" disabled />
        </div>
        <div class="col-sm-4"></div>
      </div>
    </div>
   
  </form>
</fieldset>
<app-navigation-footer showPrevious="true" showSaveDraft="true" [saveIsEnable]="isSaveButtonEnabled"
 (previousButtonClick)="previous()" confirmButtonString="{{'UBZ.SITE_CONTENT.*********' | translate }}"
(saveButtonClick)="confirmPayment()" [activeTaskCode]="currentTask" [saveDraftCallback]="draftButtonCallback" (closeDraftButtonClick)="saveDraft()"(cancelButtonClick)="cancelPosition()">
</app-navigation-footer>
</fieldset>
