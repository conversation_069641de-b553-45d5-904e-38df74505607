<form #f="ngForm">
  <accordion-group #group class="panel" [ngClass]="{'empty-accordion': (!addExpert && !anExpertPresent)}" [isOpen]="addExpert" [isDisabled]="!anExpertPresent">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          {{'UBZ.SITE_CONTENT.1011100001' | translate }}
          <div class="accordion-button">
            <ng-container *appAuthKey="'UBZ_REGISTRY.ASSOCIATED_EXPERTS_OPEN_NEW'">
              <button *ngIf="!addExpert && !anExpertPresent" type="button" class="btn btn-empty" (click)="addAnExpert($event, true)">
                <i class="fa icon-add"></i>{{'UBZ.SITE_CONTENT.111100001' | translate }}
              </button>
            </ng-container>
            <ng-container *appAuthKey="'UBZ_REGISTRY.ASSOCIATED_EXPERTS_MODIFY'">
              <button *ngIf="!addExpert && anExpertPresent" type="button" class="btn btn-empty" (click)="addAnExpert($event, false)">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.11111' | translate }}
              </button>
            </ng-container>
            <ng-container *ngIf="addExpert">
              <button *appAuthKey="'UBZ_REGISTRY.ASSOCIATED_EXPERTS_CANCEL'" type="button" class="btn btn-empty" (click)="abortAdding($event)">
                <i class="fa fa-times"></i>{{'UBZ.SITE_CONTENT.100000' | translate }}
              </button>
              <button *appAuthKey="'UBZ_REGISTRY.ASSOCIATED_EXPERTS_SAVE'" type="button" class="btn btn-empty" (click)="saveAddedData($event)" [disabled]="f.invalid">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.100001' | translate }}
              </button>
            </ng-container>
          </div>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <form #f="ngForm" novalidate>
          <table class="uc-table">
            <thead>
              <tr>
                <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.101111110' | translate }}</th>
                <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.101111111' | translate }}</th>
                <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1011100010' | translate }}</th>
                <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1011100011' | translate }}</th>
                <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10000001' | translate }}</th>
                <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.111110' | translate }}</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngIf="!addExpert; else modifyMode">
                <tr *ngFor="let expert of expertList">
                  <td data-label="Nome">{{ expert.firstName }}</td>
                  <td data-label="Cognome">{{ expert.lastname }}</td>
                  <td data-label="Ruolo">{{ anagSubjectTypes[expert.role]?.translationCod | translate }}</td>
                  <td data-label="Matricola">{{ expert.identificationNum }}</td>
                  <td data-label="Stato">{{ anagStatus[expert.status]?.translationCod | translate }}</td>
                  <td data-label="Operazione">
                    <button type="button" class="btn btn-clean" (click)="goToDetail(expert.idAnag)">
                      <i class="fa fa-external-link" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.1000000' | translate }}
                    </button>
                  </td>
                </tr>
              </ng-container>
              <ng-template #modifyMode>
                <tr *ngFor="let expert of expertList; let index = index;">
                  <td data-label="Nome">
                    <input type="text" name="firstName-{{index}}" class="form-control" [(ngModel)]="expert.firstName" required>
                  </td>
                  <td data-label="Cognome">
                    <input type="text" name="lastname-{{index}}" class="form-control" [(ngModel)]="expert.lastname" required>
                  </td>
                  <td data-label="Ruolo">
                    <div class="custom-select">
                      <select class="form-control" name="role-{{index}}" [(ngModel)]="expert.role" required>
                        <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                        <option *ngFor="let item of (anagSubjectTypes | domainMapToDomainArray)" value="{{item.domCode}}">{{ item.translationCod | translate }}</option>
                      </select>
                    </div>
                  </td>
                  <td data-label="Matricola">
                    <input type="text" name="identificationNum-{{index}}" class="form-control" [(ngModel)]="expert.identificationNum" required>
                  </td>
                  <td data-label="Stato">
                    <div class="custom-select">
                      <select class="form-control" name="status-{{index}}" [(ngModel)]="expert.status" required>
                        <option value="" disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                        <option *ngFor="let item of (anagStatus | domainMapToDomainArray)" value="{{item.domCode}}">{{ item.translationCod | translate }}</option>
                      </select>
                    </div>
                  </td>
                  <td data-label="Operazione">
                    <button type="button" class="btn btn-clean" (click)="goToDetail(expert.idAnag)" disabled>
                      <i class="fa fa-external-link" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.1000000' | translate }}
                    </button>
                  </td>
                </tr>
              </ng-template>
            </tbody>
          </table>
          <div class="row separeted-button" *ngIf="addExpert">
            <div class="col-sm-12 btn-set">
              <button *appAuthKey="'UBZ_REGISTRY.ASSOCIATED_EXPERTS_ADD_NEW'" class="btn btn-primary waves-effect pull-right" [disabled]="f.invalid" type="button" (click)="createNewExpert()">{{'UBZ.SITE_CONTENT.1011100100' | translate }}</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </accordion-group>
</form>
