import { Component, OnInit } from '@angular/core';
import { PaymentService } from '../../../../simulation/services/payment/payment.service';
import { ActivatedRoute, Params } from '@angular/router';

@Component({
  selector: 'app-customer-payment-data',
  templateUrl: './customer-payment-data.component.html',
  styleUrls: ['./customer-payment-data.component.css']
})
export class CustomerPaymentDataComponent implements OnInit {
user:any={}
idRequest:any
  constructor(private service:PaymentService,private _activatedRoute: ActivatedRoute) { }

  ngOnInit() {
 this._activatedRoute.params.subscribe((params: Params) => {
    this.idRequest = params['positionId']
  

  });

  this.service.saveCustomer(this.idRequest).subscribe((res:any)=>{
    this.user=res

  })
   }

}
