import {
  Component,
  OnInit,
  ViewChild,
  Input,
  Inject,
  Output,
  EventEmitter
} from '@angular/core';
import { Router } from '@angular/router';
import { NgForm } from '@angular/forms';
import { Observable } from 'rxjs/Observable';
// Services
import { GenericTaskService } from '../../services/generic-task/generic-task.service';
import { AccessRightsService } from '../../../shared/access-rights/services/access-rights.service';
import { PositionService } from '../../../shared/position/position.service';
import { AccordionApplicationFormComponent } from '../../../shared/application-form/components/accordion-application-form/accordion-application-form.component';
import { LockUnlockTask } from '../../../shared/access-rights/model/lock-unlock-task';
import { UserDataService } from '../../../shared/user-data/user-data.service';
import { UserData } from '../../../shared/user-data/user-data';

@Component({
  selector: 'app-asset-task',
  templateUrl: './asset-task.component.html',
  styleUrls: ['./asset-task.component.css']
})
export class AssetTaskComponent implements OnInit {
  @ViewChild(AccordionApplicationFormComponent)
  accordionApf: AccordionApplicationFormComponent;
  @Input()
  positionId: string;
  @Output()
  validEvent = new EventEmitter<any>();
  appraisalInfo: any;
  // Colori semafori
  semaforo: string[] = [];
  // Liste assets
  assetsWithNoCollateral: any[] = [];
  assetsWithInactiveCollateral: any[] = [];
  assetsWithCollateral: any[] = [];
  // Garanzie selezionabili per assetsWithNoCollateral
  collateralList: any[] = [];
  // Assets da salvare
  saveAssetList: any[] = [];
  valideIsEnable: boolean = false;
  @ViewChild(NgForm)
  public form: NgForm;
  requestId: any;
  @Input()
  taskId: string;
  @Input()
  taskCod: string;
  isTaskLocked: any;

  constructor(
    private genericTaskService: GenericTaskService,
    private _positionService: PositionService,
    private _accesRigths: AccessRightsService,
    private router: Router,
    private _userDataService: UserDataService
  ) {}

  ngOnInit() {
    this.getAssetAndCollateral();
  }

  fillSemaforo() {
    this.semaforo = [];
    this.semaforo.push(this.getColourNoCollateral());
    this.semaforo.push(this.getColourInactiveCollateral());
    this.semaforo.push(this.getColourWithCollateral());
  }

  getAssetAndCollateral() {
    Observable.forkJoin(
      this._positionService.getAssetsLists(this.positionId),
      this._positionService.getAppraisalInfo(this.positionId)
    ).subscribe(res => {
      this.assetsWithNoCollateral = res[0].assetsWithNoCollateral;
      this.assetsWithInactiveCollateral = res[0].assetsWithInactiveCollateral;
      this.assetsWithCollateral = res[0].assetsWithCollateral;
      this.appraisalInfo = res[1];
      this.requestId = this.appraisalInfo.requestId;
      this._positionService.getCollateralData(this.requestId).subscribe(res => {
        this.collateralList = res;
        this.valideIsEnable = this.canValidateTask();
        this.fillSemaforo();
      });
    });
  }

  getColourNoCollateral() {
    if (this.assetsWithNoCollateral.length > 0) return 'R';
    else return 'G';
  }

  getColourInactiveCollateral() {
    if (this.assetsWithInactiveCollateral.length > 0) return 'R';
    else return 'G';
  }

  getColourWithCollateral() {
    if (this.assetsWithCollateral.length > 0) return 'G';
    else return 'Y';
  }

  saveChanges(assets: any[]) {
    this._positionService
      .saveAssetsWithNewCollateral(this.positionId, assets)
      .subscribe(res => {
        let response = res;
        this.getAssetAndCollateral();
      });
  }

  canValidateTask(): boolean {
    return !(this.assetsWithNoCollateral.length > 0) && this.isTaskLocked;
  }

  valida() {
    let input = new LockUnlockTask(this.taskId, this.taskCod);
    input.eventCode = 'UBZ_PRZ_OK';
    this._accesRigths.closeTask(input).subscribe(res => {
      this.router.navigateByUrl('index');
    });
  }

  taskLocked() {
    this.isTaskLocked = true;
   this._userDataService.getAll().subscribe((res:UserData)=>{
    this.genericTaskService.taskLockingUser=res.username
   })
  }

  tasklockedByOtherUser(user: string) {
    this.genericTaskService.taskLockingUser = user;
    this.isTaskLocked = false;
  }
  
  taskUnlocked() {
    this.genericTaskService.taskLockingUser = undefined;
    this.isTaskLocked = false;
  }
}
