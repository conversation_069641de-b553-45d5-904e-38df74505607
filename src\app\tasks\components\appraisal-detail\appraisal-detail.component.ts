import { Component, OnInit, AfterViewInit, Input, Inject } from '@angular/core';
import { ActivatedRoute, Router, Params } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import * as FileSaver from 'file-saver';
import { GenericTaskService } from '../../services/generic-task/generic-task.service';
import { WizardService } from '../../../shared/wizard/services/wizard.service';
import { WizardElement } from '../../../shared/wizard/model/wizard-element';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';
import { AppraisalDetail } from '../../model/appraisal-detail';
import { PositionService } from '../../../shared/position/position.service';
import { TranslateService } from '@ngx-translate/core';
import { MessageService } from '../../../shared/messages/services/message.service';
import { UserDataService } from '../../../shared/user-data/user-data.service';
import { UserData } from '../../../shared/user-data/user-data';

@Component({
  selector: 'app-appraisal-detail',
  templateUrl: './appraisal-detail.component.html',
  styleUrls: ['./appraisal-detail.component.css']
})
export class AppraisalDetailComponent implements OnInit, AfterViewInit {
  @Input()
  positionId: string;
  taskId: string;
  taskCod: string;
  isTaskLocked: boolean;
  appraisalCompiled: boolean;
  canSeeButtons = true;
  appraisalDetail: AppraisalDetail = new AppraisalDetail();
  showOpinionButtons = false;
  thirdOpinionVisible = false;
  positionInfo: any = {};
  originationProcess: string;
  compileAppraisalLabel: string;
  isCompileAppraisalVisible: boolean;
  // FIXME - TOGLIERE SE LA CENTRALIZZAZIONE IN POSITION SERVICE FUNZIONA CORRETTAMENTE
  // isInternalSecondOpinion: boolean;
  isModalXmlOpen = false;
  isXmlVisible = false;
  resItemType: string;
  massiveUpload: boolean;
  isMacroProcessCte: boolean;
  showPrintButton = false;
  isIndividualSecondOpinionAllowed = false;
  isCtu = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private wizardService: WizardService,
    private userDataService: UserDataService,
    private genericTaskService: GenericTaskService,
    public positionService: PositionService,
    private messageService: MessageService,
    private translateService: TranslateService,
    @Inject(APP_CONSTANTS) public constants: IAppConstants
  ) {}

  ngOnInit() {
    this.activatedRoute.params
      .switchMap((params: Params) => {
        this.taskId = params['taskId'];
        this.taskCod = params['taskCod'];
        this.canSeeButtons = this.taskId !== '-' && this.taskCod !== '-';
        this.isCompileAppraisalVisible =
          this.taskCod === this.constants.tasks.compileInternal ||
          this.taskCod === this.constants.tasks.compileExternal ||
          this.taskCod === this.constants.tasks.dataModification ||
          this.taskCod === this.constants.tasks.techLoadCheck ||
          this.taskCod === this.constants.tasks.przChecks;
        return Observable.forkJoin(
          this.positionService.getAppraisalAssignment(this.positionId),
          this.positionService.getPositionDetail(this.positionId),
          this.positionService.getAppraisalInfo(this.positionId)
        );
      })
      .subscribe(res => {
        this.appraisalDetail = res[0];
        this.positionInfo = res[1];
        this.resItemType = res[2].appraisal.resItemType;
        this.originationProcess = res[2].appraisal.originProcess;
        this.isMacroProcessCte = res[2].appraisal.macroProcess === 'CTE';
        // console.log('isMacroProcessCte in appraisal-detail : ' + this.isMacroProcessCte);
        // FIXME - TOGLIERE SE LA CENTRALIZZAZIONE IN POSITION SERVICE FUNZIONA CORRETTAMENTE
        // this.isInternalSecondOpinion =
        //   res[2].opinionType === 'SO' && res[2].internalAgent;
        if (res[2].appraisal.loanScope === 'CTU' && res[2].appraisal.macroProcess === "MLC") {
          this.isCtu = true;
          this.isCompileAppraisalVisible = false;
        }
        this.massiveUpload =
          res[2].appraisal.macroProcess === 'MLP' ||
          res[2].appraisal.macroProcess === 'FIP';
        if (
          this.taskCod === this.constants.tasks.przChecks &&
          this.positionInfo.phaseCode === '500' &&
          (this.positionInfo.statusCode === 'TO' ||
            this.positionInfo.statusCode === 'TRD-OPN')
        ) {
          this.thirdOpinionVisible = true;
        }
        this.checkShowPrintButton();
        this.checkIndividualSecondOpinionAllowed();
      });
  }

  ngAfterViewInit() {
    this.setXmlVisibility();
    if (
      this.taskId !== '-' &&
      this.taskCod !== '-' &&
      this.isCompileAppraisalVisible
    ) {
      this.wizardService.getWizard(this.positionId).subscribe(res => {
        const wizard: WizardElement[] = res;
        let wizardComplete = false;
        if (wizard) {
          for (const task of wizard) {
            if (
              task.taskList.taskCod ===
                `${this.constants.processCode}-${
                  this.constants.wizardCodes['PER-COM']
                }` &&
              task.taskList.outcomeCod === '2'
            ) {
              wizardComplete = true;
              break;
            }
          }
          this.appraisalCompiled = wizardComplete;
          const labelMapObj = {
            true: 'UBZ.SITE_CONTENT.1010111010',
            false: 'UBZ.SITE_CONTENT.1000110110'
          };
          this.compileAppraisalLabel = labelMapObj[wizardComplete.toString()];
        }
      });
    }
  }

  checkShowPrintButton() {
    if (
      this.positionInfo.phaseCode === '300' ||
      this.positionInfo.phaseCode === '400'
    ) {
      if (
        this.positionInfo.statusCode === 'ASS-MAN' ||
        this.positionInfo.statusCode === 'FIS-APP' ||
        this.positionInfo.statusCode === 'ATT-SOP'
      ) {
        this.showPrintButton = false;
        return;
      }
    }
    this.showPrintButton = true;
  }

  setXmlVisibility() {
    this.wizardService.getWizard(this.positionId).subscribe(res => {
      const wizard: WizardElement[] = res;
      if (wizard) {
        for (const task of wizard) {
          if (task.taskList.taskCod === 'UBZ-PER') {
            if (
              task.childs[0] &&
              task.childs[0].taskList &&
              task.childs[0].taskList.outcomeCod === '3'
            ) {
              if (this.resItemType === 'IMM') {
                this.isXmlVisible = true;
              }
            }
          }
        }
      }
    });
  }

  goToAppraisalCompile(readOnly: boolean) {
    if (this.originationProcess !== 'MIG' &&
    !this.massiveUpload &&
    !this.isCtu) {
      this.router.navigateByUrl(
        `landing/${this.constants.wizardCodes['PER-COM']}/${this.positionId}/${
          this.taskId
        }/${this.taskCod}/${readOnly}`
      );
    } else if (this.isCtu) {
      this.router.navigateByUrl(
        `appraisal-ctu/${this.positionId}/-/-`
      );
    } else {
      this.router.navigateByUrl(
        `appraisal-migration/${this.positionId}/${this.taskCod}/-`
      );
    }
  }

  checkIndividualSecondOpinionAllowed() {
    this.genericTaskService
      .checkIndividualSecondOpinionAllowed(this.positionId)
      .subscribe(res => {
        // console.log(res['outcome']);
        if (res['outcome'] && res['outcome'] === true) this.isIndividualSecondOpinionAllowed = true;
        else this.isIndividualSecondOpinionAllowed = false;
      });
  }

  startIndividualSecondOpinion() {
    this.genericTaskService
      .startIndividualSecondOpinion(this.positionId)
      .subscribe(res => {
        this.router.navigateByUrl('index');
      });
  }

  startSecondOpinion() {
    this.genericTaskService
      .startSecondOpinion(this.positionId)
      .subscribe(res => {});
  }

  startThirdOpinion() {
    this.positionService.getAppraisalInfo(this.positionId).subscribe(res => {
      this.router.navigateByUrl(
        `landing/${this.constants.wizardCodes['PER-COM']}/${
          res.thirdOpinionId
        }/${this.taskId}/${this.taskCod}`
      );
    });
  }

  goToDashboard(showMessage: boolean) {
    if (showMessage) {
      this.messageService.showSuccess(
        this.translateService.instant('UBZ.SITE_CONTENT.1001100000'),
        this.translateService.instant('UBZ.SITE_CONTENT.1001100001')
      );
    }
    this.router.navigateByUrl('index');
  }

  taskLocked() {
   this.userDataService.getAll().subscribe((res:UserData)=>{
    this.genericTaskService.taskLockingUser =res.username
   })
    this.isTaskLocked = true;
  }

  tasklockedByOtherUser(user: string) {
    this.genericTaskService.taskLockingUser = user;
    this.isTaskLocked = false;
  }

  taskUnlocked() {
    this.genericTaskService.taskLockingUser = undefined;
    this.isTaskLocked = false;
  }

  printAppraisalDetail() {
    this.genericTaskService
      .printAppraisalDetail(this.positionId)
      .subscribe(file => {
        FileSaver.saveAs(file, 'document.pdf');
      });
  }
  // reOpenAppraisal() {
  //   this.genericTaskService.reopenAppraisal(this.positionId).subscribe( x => {
  //     this.router.navigateByUrl('index');
  //   });
  // }
  downloadXml() {
    this.genericTaskService.getAppraisalXml(this.positionId).subscribe(x => {
      FileSaver.saveAs(x, 'document.xml');
    });
  }

  uploadXml() {
    this.isModalXmlOpen = true;
  }
  
  closeXmlModal() {
    this.isModalXmlOpen = false;
  }
}
