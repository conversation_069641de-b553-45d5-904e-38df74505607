<form #f="ngForm">
  <accordion-group #group class="panel" [ngClass]="{'empty-accordion': !documentExists}" [isOpen]="modify" [isDisabled]="!documentExists">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          {{'UBZ.SITE_CONTENT.1100001011' | translate }}
          <div class="accordion-button">
            <ng-container *appAuthKey="'UBZ_REGISTRY.EXPERT_ID_CARD_OPEN_NEW'">
              <button *ngIf="!modify && !documentExists" type="button" class="btn btn-empty" (click)="startModify($event)">
                <i class="fa icon-add"></i>{{'UBZ.SITE_CONTENT.111100001' | translate }}
              </button>
            </ng-container>
            <ng-container *appAuthKey="'UBZ_REGISTRY.EXPERT_ID_CARD_MODIFY'">
              <button *ngIf="!modify && documentExists" type="button" class="btn btn-empty" (click)="startModify($event)">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.11111' | translate }}
              </button>
            </ng-container>
            <ng-container *ngIf="modify">
              <button *appAuthKey="'UBZ_REGISTRY.EXPERT_ID_CARD_CANCEL'" type="button" class="btn btn-empty" (click)="cancelModify($event)">
                <i class="fa fa-times"></i>{{'UBZ.SITE_CONTENT.100000' | translate }}
              </button>
              <button *appAuthKey="'UBZ_REGISTRY.EXPERT_ID_CARD_SAVE'" type="button" class="btn btn-empty" (click)="saveData($event)" [disabled]="f.invalid">
                <i class="fa fa-pencil-square-o"></i>{{'UBZ.SITE_CONTENT.100001' | translate }}
              </button>
            </ng-container>
          </div>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <form #f="ngForm">
            <ng-container *ngIf="modify; else only_visualization">
              <div class="row">
                <div class="col-sm-12 col-md-3 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1100001100' | translate }}*</label>
                  <input type="text" class="form-control" [(ngModel)]="document.documentType" name="documentType" required>
                </div>
                <div class="col-sm-12 col-md-3 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1100001101' | translate }}*</label>
                  <input type="text" class="form-control" [(ngModel)]="document.documentNumber" name="documentNumber" required>
                </div>
                <div class="col-sm-12 col-md-3 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1100001110' | translate }}*</label>
                  <input type="text" class="form-control" [(ngModel)]="document.releaseCity" name="releaseCity" required>
                </div>
              </div>
              <div class="row">
                <div class="col-md-3 col-sm-12 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1100001111' | translate }}*</label>
                  <app-calendario [name]="'releaseDate'" [(ngModel)]="document.releaseDate" [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                    [required]="true">
                  </app-calendario>
                </div>
                <div class="col-md-3 col-sm-12 form-group">
                  <label>{{'UBZ.SITE_CONTENT.1100010000' | translate }}*</label>
                  <app-calendario [name]="'expiryDate'" [(ngModel)]="document.expiryDate" [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                    [required]="true">
                  </app-calendario>
                </div>
              </div>
            </ng-container>
          </form>
        </div>
      </div>
    </div>
  </accordion-group>
</form>

<ng-template #only_visualization>
  <div class="row">
    <div class="col-sm-12 col-md-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.1100001100' | translate }}</label>
      <span>{{ document.documentType }}</span>
    </div>
    <div class="col-sm-12 col-md-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.1100001101' | translate }}</label>
      <span>{{ document.documentNumber }}</span>
    </div>
    <div class="col-sm-12 col-md-3 form-group">
      <label>{{'UBZ.SITE_CONTENT.1100001110' | translate }}</label>
      <span>{{ document.releaseCity }}</span>
    </div>
  </div>
  <div class="row">
    <div class="col-md-3 col-sm-12 form-group">
      <label>{{'UBZ.SITE_CONTENT.1100001111' | translate }}</label>
      <span>{{ document.releaseDate | date:'dd-MM-yyyy' }}</span>
    </div>
    <div class="col-md-3 col-sm-12 form-group">
      <label>{{'UBZ.SITE_CONTENT.1100010000' | translate }}</label>
      <span>{{ document.expiryDate | date:'dd-MM-yyyy' }}</span>
    </div>
  </div>
</ng-template>
