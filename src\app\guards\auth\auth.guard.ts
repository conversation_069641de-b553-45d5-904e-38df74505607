import { Injectable, Inject } from '@angular/core';
import {
  CanActivate,
  CanActivateChild,
  ActivatedRouteSnapshot,
  RouterStateSnapshot
} from '@angular/router';
import { DOCUMENT } from '@angular/platform-browser';
import { Observable } from 'rxjs/Observable';
import 'rxjs/add/observable/forkJoin';

import { PropertiesService } from '../../shared/properties/properties.service';
import { ApiService } from '../../shared/api/api.service';
import { APP_CONSTANTS, IAppConstants } from '../../app.constants';

@Injectable()
export class AuthGuard implements CanActivate, CanActivateChild {
  constructor(
    private propService: PropertiesService,
    private apiService: ApiService,
    @Inject(DOCUMENT) private document: any,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) {}

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    if (!/pgeToken/.test(this.document.cookie)) {
      this.calcPgeUrl().subscribe(data => {
        const redirect = data[0];
        const callback = data[1];

        if (redirect && callback) {
          this.apiService.apiUrl.subscribe(umfUrl => {
            const url = `${umfUrl}${callback.substring(1)}`;

            let localUrl = `${this.document.location.protocol}//${this.document
              .location.hostname}`;
            if (!!this.document.location.port) {
              localUrl = `${localUrl}:${this.document.location.port}`;
            }
            localUrl = `${localUrl}${this.document.location.pathname}`;
            if (!!this.document.location.hash) {
              localUrl = `${localUrl}${this.document.location.hash}`;
            }

            this.document.location.href = `${redirect}${encodeURIComponent(
              url
            )}&goto=${encodeURIComponent(
              `${localUrl}${state.url.substring(1)}`
            )}&appCode=${this.constants.processCode}`;
          });
        } else {
          throw new Error('User not authenticated');
        }
      });
      return false;
    } else {
      return true;
    }
  }

  canActivateChild(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): boolean {
    return this.canActivate(next, state);
  }

  private calcPgeUrl(): Observable<string[]> {
    return Observable.forkJoin(
      this.propService.getProperty('UBZ', 'pge.tokenRequerst.redirect'),
      this.propService.getProperty('UBZ', 'pge.tokenRequerst.callback')
    );
  }
}
