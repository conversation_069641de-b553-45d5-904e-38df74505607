<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="unicredititalic" horiz-adv-x="1073" >
<font-face units-per-em="2048" ascent="1536" descent="-512" />
<missing-glyph horiz-adv-x="481" />
<glyph unicode="&#xfb01;" horiz-adv-x="974" d="M-6 -338q23 61 41 115.5t32.5 108t26.5 110.5t25 127l137 764h-125q-20 0 -16 22l10 49q6 25 27 27l127 19l12 63q18 96 51 159.5t88 104.5q53 39 117.5 56.5t161.5 17.5q57 0 112.5 -8t96.5 -25q27 -12 16 -37l-21 -47q-6 -16 -13 -20t-23 0q-39 12 -85.5 19t-103.5 7 q-61 0 -104 -12t-74 -38.5t-50.5 -70t-33.5 -104.5l-14 -65h477q25 0 20 -23l-186 -965q-4 -16 -21 -16h-98q-16 0 -14 16l168 871h-369l-133 -737q-16 -84 -30.5 -151t-30 -124t-33 -108.5t-37.5 -104.5q-10 -25 -29 -24h-84q-10 0 -16 7t-2 17z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1077" d="M-6 -338q23 61 41 115.5t32.5 108t26.5 110.5t25 127l137 764h-125q-20 0 -16 22l10 49q6 25 27 27l127 19l12 63q35 180 129 259t276 79q49 0 103.5 -9.5t95.5 -25.5l90 16q27 4 21 -24l-213 -1106q-10 -57 -11.5 -85t9.5 -44q10 -12 26.5 -16.5t48.5 -4.5h66 q27 0 20 -26l-10 -55q-6 -25 -33 -25h-78q-59 0 -95 9t-58 32q-51 53 -21 207l197 1014q-76 33 -166 32q-61 0 -104 -12t-74 -38.5t-50.5 -70t-33.5 -104.5l-14 -65h219q18 0 16 -21l-14 -76q-4 -20 -25 -20h-219l-133 -737q-16 -84 -30.5 -151t-30 -124t-33 -108.5 t-37.5 -104.5q-10 -25 -29 -24h-84q-10 0 -16 7t-2 17z" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="481" />
<glyph unicode=" "  horiz-adv-x="481" />
<glyph unicode="&#x09;" horiz-adv-x="481" />
<glyph unicode="&#xa0;" horiz-adv-x="481" />
<glyph unicode="!" horiz-adv-x="446" d="M25 41q0 4 3 22.5t7 42.5q16 80 102 80h21q72 0 71 -59q0 -4 -2 -18.5t-8 -47.5q-8 -41 -34.5 -60t-67.5 -19h-21q-72 0 -71 59zM111 330l168 964q6 35 38 35h109q20 0 22.5 -12t0.5 -23l-209 -964q-6 -35 -39 -35h-68q-29 0 -22 35z" />
<glyph unicode="&#x22;" horiz-adv-x="604" d="M172 963l61 403q6 43 46 43h67q23 0 30 -8t3 -31l-96 -412q-8 -33 -37 -32h-49q-31 0 -25 37zM457 963l61 403q6 43 45 43h68q23 0 30 -8t3 -31l-97 -412q-8 -33 -37 -32h-49q-31 0 -24 37z" />
<glyph unicode="#" horiz-adv-x="1374" d="M6 446l29 66q10 27 47 27h256l102 252h-264q-35 0 -18 36l28 66q10 27 47 27h261l153 374q14 35 47 35h82q35 0 19 -35l-154 -374h279l153 374q14 35 49 35h78q37 0 21 -35l-154 -374h279q37 0 22 -31l-33 -76q-12 -23 -47 -22h-274l-103 -252h283q37 0 23 -31l-33 -76 q-12 -23 -47 -22h-279l-153 -375q-14 -35 -50 -35h-75q-37 0 -23 35l154 375h-279l-153 -375q-14 -35 -50 -35h-75q-37 0 -23 35l154 375h-260q-35 0 -19 36zM485 539h279l102 252h-278z" />
<glyph unicode="$" d="M49.5 79.5q-4.5 8.5 1.5 26.5l19 48q12 37 47 22q59 -25 134 -41t152 -20l99 508l-92 24q-111 31 -157 81t-46 142q0 33 7 78t15 84q31 154 131.5 222.5t278.5 72.5l33 172q6 31 35 31h36q33 0 27 -31l-33 -174q72 -4 131.5 -13t106.5 -26q20 -6 23 -16t-1 -27l-16 -51 q-6 -18 -19.5 -21.5t-31.5 0.5q-49 14 -105.5 23.5t-109.5 13.5l-94 -477l63 -16q127 -33 176 -88.5t49 -149.5q0 -39 -6 -75.5t-16 -88.5q-16 -82 -52 -138t-87.5 -93t-119 -55.5t-149.5 -24.5l-35 -180q-6 -31 -34 -31h-37q-33 0 -27 31l35 178q-86 4 -168 20.5t-143 42.5 q-16 8 -20.5 16.5zM352 891q0 -51 27 -75.5t98 -45.5l51 -14l88 454q-209 0 -247 -198q-8 -39 -12.5 -66.5t-4.5 -54.5zM502 117q100 12 159.5 58t81.5 144q10 43 13.5 74t3.5 47q0 33 -6 56.5t-23.5 42t-49.5 33t-85 28.5z" />
<glyph unicode="%" horiz-adv-x="1605" d="M131 821q0 35 5 77t16 93q23 119 55.5 185.5t79.5 107.5q35 29 80 44t112 15q61 0 101.5 -16t64.5 -45q23 -29 32 -64.5t9 -72.5q0 -41 -6 -87t-14 -89q-20 -111 -50 -178.5t-84 -112.5q-35 -29 -82 -45.5t-114 -16.5q-115 0 -164 62q-23 29 -32 64.5t-9 78.5zM246 12 l979 1307q16 25 49 24h72q16 0 17 -10t-9 -24l-975 -1303q-10 -16 -21.5 -20t-27.5 -4h-72q-35 0 -12 30zM256 834q0 -66 24.5 -89.5t75.5 -23.5q35 0 62.5 11t50.5 40t40.5 77t31.5 122q10 55 14 91t4 71q0 59 -24.5 82.5t-75.5 23.5q-35 0 -62.5 -12.5t-50 -41t-39 -76.5 t-31.5 -120q-10 -47 -15 -88t-5 -67zM918 186q0 35 5 77t15 93q23 119 55.5 185.5t79.5 107.5q35 29 80 44.5t113 15.5q61 0 101 -16.5t65 -45.5q23 -29 32 -64.5t9 -72.5q0 -41 -6.5 -87t-14.5 -89q-20 -111 -50 -178.5t-83 -112.5q-35 -29 -82 -45t-115 -16 q-115 0 -164 61q-23 29 -31.5 64.5t-8.5 78.5zM1042 199q0 -66 25 -89.5t76 -23.5q35 0 62.5 11.5t50 40t40 76.5t31.5 122q10 55 14.5 91t4.5 71q0 59 -25 82.5t-76 23.5q-35 0 -62.5 -12t-50 -41t-39 -77t-30.5 -120q-10 -47 -15.5 -88t-5.5 -67z" />
<glyph unicode="&#x26;" horiz-adv-x="1245" d="M55 266q0 37 6 80q25 166 108 258t212 139l-12 17q-41 57 -61.5 114.5t-20.5 124.5q0 160 100 252t268 92q154 0 230 -65.5t76 -181.5q0 -160 -85 -265.5t-251 -191.5l229 -281q10 25 18.5 51.5t18.5 55.5l51 178q8 20 31 21h86q31 0 22 -27l-51 -180q-37 -125 -82 -213 l146 -174q20 -23 -11 -47l-39 -29q-18 -12 -28 -12t-23 16l-116 139q-76 -88 -184.5 -121.5t-264.5 -33.5h-22q-172 0 -261.5 70.5t-89.5 213.5zM207 285q2 -96 49 -144.5t170 -48.5h23q121 0 202.5 35t139.5 113l-351 424q-104 -53 -157.5 -125t-69.5 -187q-6 -37 -6 -67z M436 1001q0 -59 20.5 -117.5t75.5 -129.5l29 -37q137 76 193.5 157.5t56.5 204.5q0 74 -41 117t-119 43q-104 0 -159.5 -66t-55.5 -172z" />
<glyph unicode="'" horiz-adv-x="319" d="M172 963l61 403q6 43 46 43h67q23 0 30 -8t3 -31l-96 -412q-8 -33 -37 -32h-49q-31 0 -25 37z" />
<glyph unicode="(" horiz-adv-x="591" d="M104 348q0 291 129.5 560.5t348.5 498.5q20 25 47 8l41 -24q29 -16 8 -41q-96 -104 -176 -220t-136.5 -244t-87 -268.5t-30.5 -295.5q0 -57 10 -125t28.5 -135.5t42 -130t50.5 -109.5q10 -16 1 -25.5t-22 -15.5l-34 -17q-20 -10 -29.5 -7t-20.5 20q-39 63 -69.5 135 t-53 145.5t-35 148.5t-12.5 142z" />
<glyph unicode=")" horiz-adv-x="591" d="M-94 -172q96 104 176 220t136 244t87 268t31 296q0 57 -10.5 125t-28.5 135.5t-42 130t-50 109.5q-10 16 -1 25.5t21 15.5l35 16q20 10 29.5 7t19.5 -19q39 -63 70 -135t53.5 -145.5t34.5 -148.5t12 -143q0 -291 -129 -560t-348 -498q-20 -25 -47 -9l-41 25q-29 16 -8 41 z" />
<glyph unicode="*" horiz-adv-x="720" d="M190 1225q0 6 7 18l39 72q12 20 26 20q10 0 23 -6l172 -104l16 231q2 23 9 31t24 8h74q25 0 24 -18q0 -6 -4 -23l-76 -231l215 106q16 8 25 8q18 0 20 -22q6 -41 8.5 -56.5t2.5 -23.5q0 -10 -5.5 -17.5t-23.5 -11.5l-236 -55l140 -166q12 -12 12 -27q0 -20 -25 -34 l-65 -37q-20 -10 -29 -10q-16 0 -26 28l-68 205l-150 -205q-10 -12 -17 -20t-17 -8q-8 0 -25 10l-53 37q-14 10 -14 24q0 16 26 37l209 176l-221 45q-16 4 -17 19z" />
<glyph unicode="+" d="M63 498l15 71q4 14 11 22.5t26 8.5h354l70 354q4 18 11 24.5t23 6.5h72q14 0 22.5 -6t4.5 -25l-70 -354h354q18 0 23.5 -8t1.5 -23l-14 -75q-4 -20 -15.5 -23.5t-21.5 -3.5h-355l-69 -354q-4 -18 -12.5 -24.5t-22.5 -6.5h-76q-20 0 -22 10t0 21l69 354h-354 q-18 0 -22.5 7t-2.5 24z" />
<glyph unicode="," horiz-adv-x="436" d="M8 -240q-18 16 4 39q31 31 56 68q29 41 47 80q14 29 -4 37l-29 16q-33 18 -33 53q0 6 2 23.5t8 46.5q8 37 30 53.5t65 16.5h28q43 0 63.5 -15.5t20.5 -56.5q0 -31 -8 -72q-8 -35 -21.5 -70.5t-35.5 -76.5q-23 -43 -56.5 -86t-66.5 -74q-18 -18 -37 -4z" />
<glyph unicode="-" horiz-adv-x="608" d="M66 496l14 75q4 14 11 22.5t26 8.5h395q18 0 23.5 -8t1.5 -23l-15 -79q-4 -20 -15 -23.5t-22 -3.5h-395q-18 0 -23.5 7t-0.5 24z" />
<glyph unicode="." horiz-adv-x="401" d="M6 35q0 16 2 32.5t8 45.5q8 41 35 60.5t68 19.5h20q76 0 76 -58q0 -4 -2 -23.5t-8 -48.5q-16 -80 -105 -79h-20q-35 0 -54.5 14t-19.5 37z" />
<glyph unicode="/" horiz-adv-x="696" d="M-35 -23l705 1405q10 18 16 23.5t23 5.5h73q33 0 19 -29l-703 -1405q-10 -18 -18 -23t-25 -5h-73q-29 0 -17 28z" />
<glyph unicode="0" d="M90 352q0 66 10.5 148t30.5 190q20 113 44 197t51.5 148.5t60 112.5t73.5 85q63 59 143.5 84.5t172.5 25.5q180 0 264 -90q41 -45 57.5 -106.5t16.5 -165.5q0 -74 -9.5 -156t-31.5 -190q-20 -104 -42 -187t-47.5 -149t-57 -117t-74.5 -92q-63 -59 -144.5 -83.5 t-179.5 -24.5q-180 0 -259 92t-79 278zM238 360q0 -61 9 -109t32.5 -82t63.5 -52.5t99 -18.5q78 0 132.5 24.5t99.5 78.5q37 43 60.5 98t41 115.5t28.5 121t21 113.5q16 86 28.5 165t12.5 151q0 45 -4 86t-20 75q-41 100 -180 101q-135 0 -224 -105q-37 -43 -61.5 -98 t-43 -114.5t-31.5 -120t-23 -113.5q-16 -86 -28.5 -165t-12.5 -151z" />
<glyph unicode="1" d="M76 31l10 59q6 31 39 31h291l209 1069h-11l-258 -119q-31 -14 -41 10l-24 54q-12 25 16 39l295 133q20 10 37.5 14t44.5 4h64q23 0 30.5 -5t3.5 -28l-225 -1171h291q29 0 22 -31l-10 -63q-2 -27 -35 -27h-723q-33 0 -26 31z" />
<glyph unicode="2" d="M45 55l16 113q16 111 47 191.5t79.5 139t111.5 98.5t143 69l136 47q84 31 139 63.5t87 72.5t44 91t12 117q0 92 -51 129t-158 37q-66 0 -142.5 -14.5t-137.5 -37.5q-35 -12 -43 17l-17 61q-4 14 1.5 23.5t25.5 15.5q72 25 161 40t175 15q336 0 336 -262 q0 -104 -22.5 -179t-69 -130t-118 -96t-171.5 -78l-148 -53q-131 -47 -195.5 -132t-84.5 -220l-11 -72h578q29 0 23 -27l-13 -69q-6 -25 -30 -25h-658q-55 0 -45 55z" />
<glyph unicode="3" d="M66 78l16 45q8 23 20.5 26t32.5 -4q61 -20 122 -32.5t124 -12.5q129 0 207 42t112 153q12 41 17.5 74.5t5.5 68.5q0 109 -68 156q-39 27 -95 38t-138 11h-55q-18 0 -22.5 7t-0.5 24l10 57q6 29 39 29h49q72 0 142.5 20.5t126.5 59.5q53 37 75.5 95t22.5 132q0 84 -49 121 t-160 37q-59 0 -131 -12.5t-127 -30.5q-35 -12 -43 18l-14 51q-8 29 26 41q61 23 149.5 37t164.5 14q160 0 246.5 -61t86.5 -190q0 -90 -17 -148.5t-60 -114.5q-35 -43 -85.5 -75.5t-121.5 -51.5v-6q104 -16 151 -82.5t47 -169.5q0 -68 -18 -147q-39 -166 -159 -240.5 t-330 -74.5q-76 0 -151 15t-130 36q-33 10 -18 45z" />
<glyph unicode="4" d="M29 373l6 41q4 20 15 34.5t40 53.5l592 776q20 27 39.5 37t58.5 10h109q35 0 46 -15.5t5 -45.5l-156 -803h134q23 0 27.5 -6t2.5 -23l-12 -67q-2 -12 -10 -18.5t-31 -6.5h-133l-60 -305q-6 -35 -36 -35h-78q-33 0 -27 35l60 305h-562q-37 0 -30 33zM213 461h430l145 739 h-14z" />
<glyph unicode="5" d="M63 90l19 51q6 18 19.5 21.5t25.5 -2.5q55 -27 131 -44.5t145 -17.5q154 0 232 57.5t119 174.5q16 43 22 97t6 101q0 98 -52 141.5t-142 43.5q-78 0 -144.5 -16.5t-124.5 -43.5q-16 -8 -26 -10t-25 4l-37 17q-16 8 -10 34l156 590q8 37 45 37h547q37 0 30 -33l-12 -57 q-4 -16 -13 -23.5t-34 -7.5h-432q-25 0 -31 -26l-106 -398q47 23 108.5 37t126.5 14q330 0 330 -280q0 -27 -3 -60.5t-9 -70.5t-14.5 -73t-18.5 -66q-53 -154 -179 -227.5t-325 -73.5q-80 0 -160.5 19t-148.5 48q-25 10 -15 43z" />
<glyph unicode="6" d="M115 358q0 59 11 134t28 161q35 180 84 320.5t151 236.5q70 66 166 99.5t231 33.5q63 0 120 -9t98 -25q35 -14 22 -39l-25 -53q-16 -37 -47 -23q-76 33 -172 33q-86 0 -154.5 -22.5t-115.5 -61.5q-41 -33 -70.5 -78t-52 -98.5t-39 -112.5t-31.5 -123q57 41 140.5 70.5 t175.5 29.5q164 0 243.5 -68.5t79.5 -213.5q0 -25 -4 -64t-14 -102q-35 -199 -155.5 -301t-321.5 -102q-84 0 -148.5 19t-108.5 64.5t-67.5 118t-23.5 176.5zM262 373q0 -141 47 -209t166 -68q74 0 127 19.5t90 56.5t60.5 89t35.5 116q10 47 13.5 82t3.5 55q0 98 -42 147.5 t-153 49.5q-94 0 -176 -34t-143 -83q-12 -66 -20.5 -121t-8.5 -100z" />
<glyph unicode="7" d="M104.5 11.5q-8.5 11.5 8.5 33.5l788 1159h-622q-25 0 -23 27l14 67q6 27 33 27h737q45 0 39 -41l-6 -33q-4 -25 -19.5 -50t-45.5 -73l-738 -1083q-14 -25 -27.5 -35t-41.5 -10h-70q-18 0 -26.5 11.5z" />
<glyph unicode="8" d="M80 270q0 72 17.5 139.5t53 126t91 103.5t131.5 74q-61 41 -90 99t-29 144q0 31 6 68t21 78q41 115 143 178t266 63q160 0 248 -68.5t88 -195.5q0 -35 -7.5 -80t-21.5 -84q-27 -72 -81 -133t-148 -112q92 -53 128 -118t36 -136q0 -31 -4 -61.5t-15 -71.5 q-41 -152 -156.5 -226.5t-295.5 -74.5q-197 0 -289 78.5t-92 209.5zM233 283q0 -92 56.5 -140.5t185.5 -48.5q106 0 185 52.5t102 148.5q8 29 10 52.5t2 43.5q0 66 -34.5 115t-121.5 88l-151 72q-117 -59 -175.5 -156.5t-58.5 -226.5zM408 969q0 -39 6 -67t22.5 -50.5 t42 -40.5t66.5 -37l119 -55q72 45 112.5 90t65.5 106q16 43 22 80t6 62q0 92 -55 134t-145 42t-149.5 -38t-88.5 -105q-10 -27 -17 -59.5t-7 -61.5z" />
<glyph unicode="9" d="M113 53l24 53q16 35 47 23q80 -33 178 -33q72 0 138.5 24.5t113.5 63.5q39 33 71 78t56.5 98.5t42 110.5t31.5 117q-57 -41 -139 -68.5t-172 -27.5q-168 0 -246 71.5t-78 224.5q0 25 3 64t16 102q18 104 61 178t104.5 121t140.5 68.5t171 21.5q98 0 164.5 -24.5 t107.5 -70.5t58.5 -111.5t17.5 -147.5q0 -68 -10 -141.5t-27 -159.5q-35 -180 -90 -328.5t-158 -242.5q-70 -66 -166 -101.5t-231 -35.5q-61 0 -113.5 9t-93.5 25q-35 14 -22 39zM334 809q0 -98 41.5 -147.5t152.5 -49.5q92 0 174 34t146 83q14 66 22.5 130.5t8.5 113.5 q0 145 -56 201q-29 29 -66.5 41t-92.5 12q-131 0 -210 -74t-104 -207q-10 -47 -13 -82t-3 -55z" />
<glyph unicode=":" horiz-adv-x="473" d="M51 35q0 16 2 32.5t8 45.5q8 41 35 60.5t68 19.5h20q76 0 76 -58q0 -4 -2 -23.5t-8 -48.5q-16 -80 -105 -79h-20q-35 0 -54.5 14t-19.5 37zM201 815q0 16 2 32.5t8 45.5q8 41 35 60.5t68 19.5h20q76 0 76 -58q0 -4 -2 -23.5t-8 -48.5q-16 -80 -105 -79h-20 q-35 0 -54.5 14t-19.5 37z" />
<glyph unicode=";" horiz-adv-x="473" d="M8 -240q-18 16 4 39q31 31 56 68q29 41 47 80q14 29 -4 37l-29 16q-33 18 -33 53q0 6 2 23.5t8 46.5q8 37 30 53.5t65 16.5h28q43 0 63.5 -15.5t20.5 -56.5q0 -31 -8 -72q-8 -35 -21.5 -70.5t-35.5 -76.5q-23 -43 -56.5 -86t-66.5 -74q-18 -18 -37 -4zM203 815 q0 16 2 32.5t8 45.5q8 41 35 60.5t68 19.5h20q76 0 76 -58q0 -4 -2 -23.5t-8 -48.5q-16 -80 -105 -79h-20q-35 0 -54.5 14t-19.5 37z" />
<glyph unicode="&#x3c;" d="M119 520l6 39q2 18 27 29l704 348q33 14 45 -10l27 -58q12 -25 -19 -39l-606 -303l469 -290q16 -10 17.5 -21.5t-9.5 -28.5l-26 -34q-23 -29 -56 -7l-565 344q-16 10 -14 31z" />
<glyph unicode="=" d="M31 303l14 72q4 16 10 23.5t25 7.5h842q33 0 26 -31l-16 -76q-6 -27 -35 -27h-842q-18 0 -23 7.5t-1 23.5zM104 692l15 72q6 31 41 31h837q31 0 25 -31l-14 -76q-6 -27 -37 -26h-838q-35 0 -29 30z" />
<glyph unicode="&#x3e;" d="M121 209q-12 25 18 39l606 303l-473 293q-16 10 -15 20t11 27l27 35q20 27 51 8l569 -346q16 -10 15 -31l-6 -39q-2 -18 -27 -29l-704 -348q-33 -14 -46 11z" />
<glyph unicode="?" horiz-adv-x="913" d="M133 41q0 10 2 21.5t8 43.5q8 39 36 59.5t67 20.5h20q72 0 72 -61q0 -10 -2 -20.5t-8 -43.5q-8 -41 -35 -60t-68 -19h-20q-33 0 -52.5 15t-19.5 44zM213 328q8 63 21.5 118.5t35 101.5t54 84t84.5 68l139 76q113 61 167 126t54 167q0 86 -55.5 121t-165.5 35 q-125 0 -279 -51q-31 -10 -37 16l-14 61q-6 25 21 35q74 27 153.5 42t171.5 15q174 0 263 -65.5t89 -187.5q0 -72 -15 -127.5t-49 -102.5t-89 -88t-133 -86l-127 -72q-41 -23 -66.5 -46t-43 -55t-29 -77t-19.5 -108q-4 -33 -43 -33h-68q-24 0 -20 33z" />
<glyph unicode="@" horiz-adv-x="1671" d="M129 504q0 180 60.5 340t174 279.5t278.5 190t374 70.5q127 0 236.5 -30.5t189.5 -93t126 -160t46 -230.5q0 -106 -25.5 -209.5t-79 -186.5t-136.5 -134t-197 -51q-80 0 -129.5 33.5t-53.5 93.5h-2q-61 -106 -231 -107q-106 0 -150.5 47t-44.5 119q0 33 5 73t13.5 83 t18.5 84t19 78q29 119 97 173t191 54q68 0 111 -25.5t55 -64.5h4l15 43q4 20 30 20h47q29 0 23 -26l-72 -375q-4 -23 -12 -63t-8 -74q0 -76 92 -76q74 0 132 40t98 105.5t61.5 152.5t21.5 183q0 111 -37.5 190.5t-104.5 131t-159 76t-200 24.5q-178 0 -315.5 -61.5 t-230.5 -168t-141.5 -250t-48.5 -304.5q0 -98 27 -181.5t87 -142.5t158.5 -93t243.5 -34q98 0 190.5 15.5t190.5 48.5q18 6 25 -17l10 -47q0 -16 -16 -22q-98 -39 -208 -58.5t-202 -19.5q-326 0 -486.5 136t-160.5 421zM692 494q0 -45 21.5 -68t72.5 -23q78 0 131.5 45.5 t71.5 137.5l33 170q4 16 6 31.5t2 31.5q0 49 -33.5 74t-85.5 25q-68 0 -109.5 -34t-58.5 -110q-10 -41 -18 -84t-16.5 -81t-12.5 -69.5t-4 -45.5z" />
<glyph unicode="A" horiz-adv-x="1144" d="M-14.5 9q-8.5 9 0.5 30q104 236 216.5 467t223.5 448q41 80 84 160t86 154q20 35 40.5 46t53.5 11h74q55 0 70 -65q14 -72 26 -149t23 -157q29 -215 51 -444t39 -471q2 -20 -9.5 -29.5t-27.5 -9.5h-72q-39 0 -41 33q-4 96 -11 194.5t-15 192.5h-490q-23 -49 -44 -95 t-42.5 -92.5t-43 -95.5t-46.5 -104q-14 -33 -49 -33h-72q-16 0 -24.5 9zM362 537h426q-6 94 -15 182t-19 186q-8 68 -17.5 141.5t-21.5 145.5h-25q-41 -74 -79.5 -147.5t-71.5 -139.5q-47 -96 -91.5 -186t-85.5 -182z" />
<glyph unicode="B" horiz-adv-x="1024" d="M53 76l232 1196q8 47 59 55t113.5 12t142.5 4q90 0 162 -12t122 -43t76.5 -82t26.5 -127q0 -63 -14.5 -123.5t-45 -110.5t-80.5 -88t-120 -57v-4q47 -6 87 -22.5t68.5 -44t45 -69.5t16.5 -99q0 -129 -38 -216t-109 -141q-78 -59 -182.5 -81.5t-245.5 -22.5q-55 0 -131 4 t-150 14q-36 5 -36 43q0 7 1 15zM209 152q-6 -27 20 -29q35 -4 75 -6t87 -2q96 0 172 18.5t129 63.5q47 41 73 103t26 155q0 96 -75 137t-251 41h-162zM326 750h127q98 0 160.5 15t111.5 50q55 41 82 98.5t27 145.5q0 96 -54.5 134t-179.5 38q-45 0 -85 -2t-77 -6 q-20 -2 -26 -29z" />
<glyph unicode="C" horiz-adv-x="1036" d="M104 350q0 59 18.5 172t47.5 277q27 156 73 261t112.5 167.5t154.5 89t203 26.5q100 0 183 -24.5t148 -61.5q25 -14 9 -40l-31 -54q-10 -16 -20.5 -16t-26.5 10q-63 33 -127 49.5t-135 16.5q-154 0 -232 -66q-33 -29 -57.5 -63.5t-46 -85t-38.5 -119t-34 -164.5 q-25 -137 -38 -220t-13 -136q0 -45 3 -78t10 -56.5t18.5 -41t27.5 -33.5q35 -31 86.5 -45.5t137.5 -14.5q61 0 134.5 13.5t145.5 42.5q33 14 41 -15l14 -53q8 -31 -18 -41q-98 -35 -177 -50t-157 -15q-104 0 -182 16t-130 58t-78 114t-26 180z" />
<glyph unicode="D" horiz-adv-x="1187" d="M49 51l238 1217q4 23 13 34t34 15q49 8 128 13t150 5q260 0 386 -93t126 -316q0 -53 -10 -152.5t-43 -234.5q-39 -166 -109.5 -270.5t-169 -165t-225.5 -82t-278 -21.5h-195q-47 0 -47 35q0 7 2 16zM209 143q-6 -27 20 -26h109q125 0 221 23.5t169 75.5t122 135t78 200 q27 104 40 190t13 168q0 88 -21.5 148.5t-65.5 95.5t-115 50.5t-169 15.5q-57 0 -97 -1t-71 -6q-25 -2 -30 -30z" />
<glyph unicode="E" horiz-adv-x="974" d="M78 162q0 43 9 104.5t22 130.5q16 96 32.5 184.5t35 174.5t37.5 175t42 185q27 117 94 166q35 25 81 34t110 9h438q18 0 23.5 -7t1.5 -24l-13 -59q-4 -14 -11 -22.5t-26 -8.5h-428q-59 0 -88 -20q-16 -12 -24 -33t-17 -51q-10 -37 -18 -73t-17.5 -78t-20.5 -91t-24 -113 h500q18 0 23.5 -7t1.5 -23l-13 -60q-4 -14 -11 -22t-25 -8h-500q-29 -139 -43 -224.5t-23 -140.5q-6 -49 -6 -80q2 -59 92 -59h465q18 0 22.5 -8.5t0.5 -22.5l-10 -59q-4 -16 -10.5 -23.5t-24.5 -7.5h-475q-98 0 -150.5 33t-52.5 129z" />
<glyph unicode="F" horiz-adv-x="915" d="M45 35l209 1079q25 113 88 162q35 27 81 38t101 11h424q10 0 19.5 -3t5.5 -24l-15 -67q-2 -27 -34 -27h-392q-70 0 -100 -24q-29 -23 -41 -93l-65 -342h495q31 0 25 -26l-12 -68q-4 -16 -11.5 -21t-25.5 -5h-496l-115 -590q-6 -35 -32 -35h-82q-33 0 -27 35z" />
<glyph unicode="G" horiz-adv-x="1157" d="M100 350q0 72 16.5 171.5t45.5 240.5q29 137 66.5 245.5t102 183.5t165 113.5t252.5 38.5q94 0 183 -23.5t156 -64.5q16 -10 16.5 -19t-7.5 -22l-35 -59q-10 -16 -20.5 -16t-26.5 10q-68 39 -136.5 56.5t-136.5 17.5q-113 0 -186.5 -33t-120.5 -95.5t-75.5 -151.5 t-51.5 -202q-25 -119 -40 -213t-15 -159q0 -74 15.5 -125t50 -83t92 -46.5t141.5 -14.5q63 0 126.5 7.5t98.5 15.5q27 6 33 37l80 424h-184q-31 0 -25 30l12 64q6 27 37 27h297q29 0 23 -29l-115 -590q-4 -27 -13.5 -38t-31.5 -17q-66 -18 -160 -33.5t-194 -15.5 q-104 0 -185.5 17t-136.5 60t-84 114t-29 177z" />
<glyph unicode="H" horiz-adv-x="1216" d="M45 35l244 1255q6 35 37 35h80q20 0 24 -10t0 -25l-106 -549h641l106 549q6 35 37 35h80q20 0 24.5 -10t-0.5 -25l-243 -1255q-6 -35 -39 -35h-80q-29 0 -23 35l115 588h-641l-115 -588q-6 -35 -39 -35h-79q-29 0 -23 35z" />
<glyph unicode="I" horiz-adv-x="460" d="M59 35l244 1255q8 35 37 35h80q20 0 24.5 -12.5t-0.5 -22.5l-243 -1255q-6 -35 -37 -35h-80q-31 0 -25 35z" />
<glyph unicode="J" horiz-adv-x="643" d="M-49 35l10 59q6 27 31 27h86q74 0 116 27.5t66 72.5q23 41 33 84t22 109l170 876q6 35 37 35h80q31 0 25 -35l-172 -889q-31 -156 -84 -245q-47 -82 -123 -119t-187 -37h-77q-39 0 -33 35z" />
<glyph unicode="K" horiz-adv-x="1032" d="M49 35l244 1255q8 35 37 35h80q20 0 24 -12.5t0 -22.5l-106 -549h129q29 0 47 7.5t35 27.5l442 518q16 18 28.5 24.5t28.5 6.5h82q20 0 25.5 -10t-6.5 -25l-498 -583q-16 -18 -16 -29q0 -8 2 -12t4 -9l307 -630q12 -27 -25 -27h-96q-27 0 -37 29l-266 559 q-10 23 -23.5 29t-41.5 6h-144l-115 -588q-6 -35 -36 -35h-80q-31 0 -25 35z" />
<glyph unicode="L" horiz-adv-x="737" d="M70 104q0 29 6 73t14 81l199 1032q6 35 37 35h80q31 0 24 -35l-205 -1052q-4 -23 -7 -41.5t-3 -38.5t14.5 -28.5t57.5 -8.5h321q31 0 25 -27l-12 -67q-6 -27 -35 -27h-393q-63 0 -93 22.5t-30 81.5z" />
<glyph unicode="M" horiz-adv-x="1536" d="M25 35q72 307 147.5 617.5t159.5 604.5q10 39 29.5 53.5t60.5 14.5h72q43 0 59 -13.5t22 -56.5q16 -106 35 -224t39.5 -237.5t41 -235.5t40.5 -218h10q59 102 125 219t132.5 236.5t130 237.5t120.5 222q20 39 41 54.5t68 15.5h69q43 0 58.5 -16.5t11.5 -57.5 q-35 -299 -78 -605t-90 -611q-6 -35 -33 -35h-77q-35 0 -29 35q49 305 93 589.5t81 555.5h-14q-59 -109 -123 -226.5t-128.5 -236.5t-127 -230.5t-117.5 -203.5q-29 -45 -53.5 -58.5t-59.5 -13.5h-57q-35 0 -53.5 13.5t-28.5 58.5q-18 92 -38.5 203.5t-40 230.5t-38 236.5 t-34.5 226.5h-15q-74 -274 -139.5 -557t-132.5 -588q-6 -35 -37 -35h-78q-31 0 -24 35z" />
<glyph unicode="N" horiz-adv-x="1236" d="M45 35l240 1235q6 33 20 44t55 11h93q35 0 53 -11t29 -46l329 -1135h15l221 1153q8 35 37 35h69q31 0 25 -35l-238 -1231q-6 -33 -25.5 -44t-54.5 -11h-90q-37 0 -53 10t-27 43l-327 1135h-15l-225 -1153q-6 -35 -39 -35h-69q-29 0 -23 35z" />
<glyph unicode="O" horiz-adv-x="1200" d="M104 350q0 96 16.5 192.5t33.5 174.5q20 98 41.5 177t48 141.5t62.5 112.5t83 91q123 104 344 104q90 0 165 -18t128 -61t82 -114t29 -175q0 -96 -17 -192.5t-33 -174.5q-37 -172 -86 -291.5t-117.5 -194.5t-160.5 -107.5t-215 -32.5q-96 0 -171 17t-126 60t-79 114 t-28 177zM254 373q0 -70 10 -121t40 -84t80 -49.5t130 -16.5q92 0 159.5 32t118 96.5t86 165t66.5 239.5q27 115 35 186.5t8 130.5q0 135 -55 203t-205 68q-86 0 -141.5 -20.5t-102.5 -61.5q-39 -35 -65.5 -79t-48 -99.5t-38 -123t-34.5 -149.5q-23 -115 -33 -186.5 t-10 -130.5z" />
<glyph unicode="P" horiz-adv-x="1032" d="M45 35l240 1237q4 23 12 32t27 13q57 10 132.5 18t157.5 8q88 0 166 -14t135.5 -48t90 -90t32.5 -142q0 -35 -5 -86.5t-19 -104.5q-51 -190 -187.5 -270t-351.5 -80h-196l-93 -473q-6 -35 -41 -35h-79q-27 0 -21 35zM301 631q47 -2 86 -2h84q176 0 269.5 59.5 t129.5 196.5q12 43 16.5 79.5t4.5 61.5q0 59 -19.5 97t-57.5 60.5t-94 31t-130 8.5q-43 0 -79 -3.5t-67 -7.5q-18 -4 -26 -14t-12 -33z" />
<glyph unicode="Q" horiz-adv-x="1200" d="M104 350q0 96 16.5 192.5t33.5 174.5q20 98 41.5 177t48 141.5t62.5 112.5t83 91q123 104 344 104q90 0 165 -18t128 -61t82 -114t29 -175q0 -96 -17 -192.5t-33 -174.5q-31 -139 -68.5 -244.5t-86.5 -179t-112.5 -119t-143.5 -65.5q37 -63 101.5 -134t135.5 -124 q29 -20 0 -39l-59 -37q-27 -14 -41 -14t-31 14q-74 59 -135 138t-106 178h-33q-96 0 -171 17t-126 60t-79 114t-28 177zM254 373q0 -70 10 -121t40 -84t80 -49.5t130 -16.5q92 0 158.5 28t116 90.5t85 165t70.5 249.5q27 115 35 186.5t8 130.5q0 135 -55 203t-205 68 q-86 0 -141.5 -20.5t-102.5 -61.5q-39 -35 -66.5 -79t-48 -99.5t-38 -123t-33.5 -149.5q-25 -115 -34 -186.5t-9 -130.5z" />
<glyph unicode="R" horiz-adv-x="1040" d="M45 29l240 1243q4 27 17 38t36 15q53 10 108.5 14t133.5 4q100 0 182 -13t138 -44t87 -83t31 -130q0 -47 -7 -95t-26 -104q-41 -123 -110.5 -189.5t-184.5 -94.5l225 -559q16 -31 -24 -31h-105q-25 0 -30 18l-203 531q-4 10 -11 13t-18 3h-59h-85t-91 2l-103 -538 q-4 -18 -14 -23.5t-27 -5.5h-79q-16 0 -20.5 5t-0.5 24zM313 690q49 -4 82 -4h80q82 0 142.5 10.5t103.5 35t72.5 64.5t50.5 99q16 47 22 83t6 66q0 106 -76.5 142.5t-230.5 36.5q-35 0 -62.5 -2.5t-56.5 -6.5q-18 -4 -27 -11t-13 -36z" />
<glyph unicode="S" horiz-adv-x="999" d="M25 90l16 53q12 35 47 21q63 -29 140 -46.5t147 -17.5q123 0 207 43t118 156q10 35 18.5 68.5t8.5 60.5q0 72 -38 107.5t-130 60.5l-158 43q-127 35 -175 99.5t-48 162.5q0 37 11.5 86t27.5 100q18 57 50 104.5t81 81.5t121 52t172 18q90 0 164 -12t139 -35 q27 -10 14 -41l-20 -57q-12 -29 -47 -16q-72 23 -134.5 32t-127.5 9q-127 0 -190.5 -44t-88.5 -130q-12 -45 -19 -75t-7 -65q0 -53 27.5 -87t109.5 -56l186 -51q123 -35 175 -92.5t52 -161.5q0 -37 -9 -83t-25 -106q-43 -152 -156 -221t-301 -69q-102 0 -183 16t-157 47 q-31 10 -18 45z" />
<glyph unicode="T" horiz-adv-x="952" d="M156 1235l12 63q4 20 15.5 23.5t21.5 3.5h837q18 0 23.5 -7t1.5 -24l-12 -59q-4 -14 -11.5 -22.5t-25.5 -8.5h-348l-228 -1169q-6 -35 -36 -35h-80q-31 0 -25 35l227 1169h-348q-18 0 -23 8.5t-1 22.5z" />
<glyph unicode="U" horiz-adv-x="1198" d="M109 293q0 31 3 65.5t11 71.5l166 860q10 35 37 35h80q35 0 24 -35l-164 -846q-8 -43 -12 -77.5t-4 -63.5q0 -111 72.5 -156t193.5 -45q86 0 145.5 16.5t106.5 57.5q23 20 41 44t32.5 54.5t27 71.5t22.5 98l164 846q10 35 37 35h79q35 0 25 -35l-166 -860 q-25 -127 -63.5 -212t-102.5 -138q-63 -53 -153 -75.5t-215 -22.5q-78 0 -149 15t-124 51t-83.5 95.5t-30.5 149.5z" />
<glyph unicode="V" horiz-adv-x="1044" d="M168 1286q-2 20 11.5 29.5t29.5 9.5h65q37 0 39 -33q16 -287 42 -569.5t67 -581.5h24q150 274 287 555t273 596q14 33 49 33h78q39 0 22 -39q-133 -311 -281.5 -611t-320.5 -603q-23 -41 -42 -56.5t-58 -15.5h-74q-39 0 -53.5 15.5t-22.5 56.5q-53 297 -84 597t-51 617z " />
<glyph unicode="W" horiz-adv-x="1593" d="M168 1290q0 20 9 27.5t34 7.5h59q25 0 34 -6t9 -29q-3 -193 -2 -386v-192q2 -290 19 -577h22q111 260 220.5 519t224.5 540q16 37 29.5 51.5t52.5 14.5h92q39 0 55 -14.5t16 -51.5q0 -264 8.5 -527.5t26.5 -531.5h23q135 295 259 595t224 562q14 33 49 33h78q39 0 23 -39 q-111 -289 -239 -586t-277 -628q-18 -41 -41 -56.5t-56 -15.5h-98q-33 0 -52.5 15.5t-23.5 56.5q-20 254 -30 504q-7 176 -6 365q0 80 1 163h-16q-102 -250 -205.5 -503t-230.5 -529q-18 -41 -42 -56.5t-59 -15.5h-90q-31 0 -50 15.5t-23 56.5q-13 219 -21 525 q-6 242 -6 534v159z" />
<glyph unicode="X" horiz-adv-x="1034" d="M-65.5 10q-2.5 10 8.5 25l407 624q18 25 12 50l-167 577q-6 23 5 31t27 8h64q18 0 27.5 -3t15.5 -22l139 -514q8 -27 16.5 -36t24.5 -9h33q23 0 35 8.5t32 36.5l334 517q10 16 22.5 19t33.5 3h88q16 0 21 -7t-9 -30l-387 -579q-12 -16 -13.5 -28.5t3.5 -27.5l215 -622 q8 -31 -23 -31h-96q-27 0 -33 18l-182 566q-8 25 -18.5 32t-34.5 7h-19q-29 0 -40 -7.5t-27 -31.5l-349 -557q-16 -27 -43 -27h-102q-18 0 -20.5 10z" />
<glyph unicode="Y" horiz-adv-x="927" d="M168 1300q-2 25 27 25h90q29 0 30 -20q18 -201 50 -372t90 -337h14q121 174 227.5 345t202.5 364q10 20 37 20h90q12 0 19.5 -5t1.5 -20q-53 -113 -111.5 -217t-123 -207.5t-139.5 -209t-163 -220.5l-80 -411q-6 -35 -37 -35h-80q-31 0 -24 35l80 416 q-80 205 -131.5 412.5t-69.5 436.5z" />
<glyph unicode="Z" horiz-adv-x="1005" d="M-14 33l4 37q4 27 18.5 46t40.5 52l830 1036h-648q-27 0 -20 29l12 65q6 27 33 27h766q23 0 33 -9t6 -34l-4 -29q-4 -29 -21.5 -54t-62.5 -83l-795 -995h641q23 0 21 -27l-13 -67q-6 -27 -30 -27h-774q-41 0 -37 33z" />
<glyph unicode="[" horiz-adv-x="555" d="M-4 -158l295 1526q6 29 37 29h303q18 0 23 -7.5t1 -23.5l-10 -63q-6 -27 -39 -27h-207l-258 -1344h207q31 0 25 -30l-13 -64q-6 -27 -36 -26h-308q-27 0 -20 30z" />
<glyph unicode="\" horiz-adv-x="741" d="M242 1378q-4 33 26 33h74q16 0 22.5 -5t10.5 -24l157 -1400q4 -33 -26 -33h-74q-16 0 -22.5 5t-10.5 23z" />
<glyph unicode="]" horiz-adv-x="555" d="M-96 -158l10 64q6 27 39 26h207l258 1344h-207q-31 0 -25 31l13 63q6 27 37 27h307q27 0 20 -31l-295 -1526q-6 -29 -37 -28h-303q-18 0 -23 7t-1 23z" />
<glyph unicode="^" d="M122 714.5q-1 12.5 9 22.5l414 463q16 18 30.5 25.5t38.5 7.5h68q43 0 59 -33l232 -459q10 -20 7 -30.5t-19 -20.5l-56 -24q-35 -16 -51 14l-213 393h-16l-371 -397q-14 -16 -27.5 -20.5t-29.5 6.5l-62 32q-12 8 -13 20.5z" />
<glyph unicode="_" horiz-adv-x="1030" d="M-74 -154l13 56q4 12 10 16t24 4h848q27 0 21 -22l-13 -56q-4 -12 -10 -16t-24 -4h-848q-27 0 -21 22z" />
<glyph unicode="`" horiz-adv-x="630" d="M227.5 1356.5q-4.5 11.5 1.5 29.5l23 64q12 33 51 14l387 -172q27 -12 15 -37l-21 -47q-8 -23 -41 -10l-391 139q-20 8 -24.5 19.5z" />
<glyph unicode="a" horiz-adv-x="956" d="M53 184q0 49 7.5 95.5t29.5 107.5q18 51 47 90t73 65.5t107.5 40t151.5 13.5h88q35 0 75 -2t75 -6l16 78q6 29 11 61.5t5 54.5q0 35 -8 58.5t-29.5 39t-59.5 21.5t-97 6q-68 0 -132.5 -11t-117.5 -28q-39 -12 -45 23l-8 51q-2 27 26 35q66 18 149 31.5t148 13.5 q145 0 226 -47t81 -164q0 -23 -5 -58.5t-11 -64.5l-129 -668q-2 -20 -25 -20h-67q-25 0 -21 25l9 73h-5q-47 -66 -112.5 -91t-151.5 -25h-51q-250 0 -250 202zM186 197q0 -102 140 -103h43q111 0 184.5 55.5t101.5 180.5l31 145q-37 4 -71.5 6t-75.5 2h-99q-51 0 -88 -7 t-64.5 -25.5t-46 -48t-32.5 -74.5q-12 -39 -17.5 -71t-5.5 -60z" />
<glyph unicode="b" horiz-adv-x="999" d="M92 236q0 41 5 80.5t18 103.5l184 944q4 23 25 22h88q25 0 20 -22l-84 -434h4q94 92 262 92q86 0 145.5 -19.5t100.5 -60.5q35 -37 50.5 -84t15.5 -125q0 -41 -8.5 -108.5t-30.5 -173.5q-18 -86 -33.5 -145.5t-34 -103.5t-41 -75t-51.5 -57q-55 -49 -128 -68.5 t-183 -19.5q-166 0 -245 60t-79 194zM225 240q0 -80 48.5 -113t154.5 -33q68 0 116 13.5t87 48.5q20 20 37.5 44.5t31 61.5t26.5 86t28 119q18 88 25 146.5t7 91.5q0 53 -7 86.5t-29 62.5q-23 29 -63 42t-107 13q-61 0 -109.5 -16t-83.5 -45q-20 -16 -34.5 -35.5t-25 -44.5 t-19.5 -56.5t-17 -74.5l-47 -246q-8 -37 -13.5 -71.5t-5.5 -79.5z" />
<glyph unicode="c" horiz-adv-x="856" d="M78 270q0 59 14 146.5t35 185.5q47 231 164 325.5t291 94.5q72 0 144.5 -21.5t123.5 -54.5q20 -12 4 -39l-27 -45q-16 -27 -39 -14q-53 29 -103 45t-110 16q-63 0 -114 -17.5t-91 -57t-69 -107.5t-49 -166q-16 -84 -28.5 -161.5t-12.5 -118.5q0 -53 10 -89t36 -57.5 t68 -31t105 -9.5q53 0 113.5 12.5t113.5 32.5q29 10 35 -18l13 -51q4 -14 -0.5 -21.5t-20.5 -13.5q-47 -18 -126 -35.5t-150 -17.5q-172 0 -251 66.5t-79 221.5z" />
<glyph unicode="d" horiz-adv-x="995" d="M78 262q0 61 11 138t26 157q29 160 69.5 255t100.5 142q49 41 112.5 54.5t143.5 13.5q113 0 174 -33t82 -92h4l90 467q2 23 27 22h86q23 0 18 -22l-188 -973q-25 -125 -60 -205t-90 -127q-53 -45 -122.5 -61t-174.5 -16q-160 0 -234.5 62t-74.5 218zM211 287 q0 -98 40 -145.5t152 -47.5q68 0 112 11.5t79 39.5q35 31 56.5 82.5t37.5 126.5l57 283q10 47 11 92q0 84 -54.5 132t-154.5 48q-57 0 -100.5 -10t-77.5 -41q-41 -37 -70 -110.5t-51 -198.5q-18 -98 -27.5 -156.5t-9.5 -105.5z" />
<glyph unicode="e" horiz-adv-x="921" d="M78 258q0 66 11 146.5t26 158.5q27 145 59.5 233.5t89.5 141.5q51 47 121 65.5t168 18.5q143 0 223 -63.5t80 -206.5q0 -23 -3 -48.5t-7 -54.5l-33 -182q-6 -41 -59 -41h-529q-8 -41 -12 -77t-4 -68q0 -55 11 -91t37 -57.5t70 -30t111 -8.5q59 0 126 12.5t128 34.5 q35 12 41 -16l10 -47q4 -14 -2 -22.5t-30 -18.5q-76 -29 -152 -42t-143 -13q-100 0 -166 19t-103.5 55t-53 87.5t-15.5 114.5zM246 532h416q27 0 32 23l17 86q6 29 9 57.5t3 51.5q0 86 -42 122.5t-146 36.5q-53 0 -95.5 -9t-72.5 -36q-37 -33 -60.5 -88t-44.5 -158z" />
<glyph unicode="f" horiz-adv-x="614" d="M-6 -338q23 61 41 115.5t32.5 108t26.5 110.5t25 127l137 764h-125q-20 0 -16 22l10 49q6 25 27 27l127 19l12 63q16 86 41.5 150.5t66.5 105.5t103.5 61.5t154.5 20.5q90 0 160 -21q27 -8 17 -36l-17 -48q-8 -27 -35 -20q-25 6 -54.5 10t-68.5 4q-53 0 -91 -8 t-64.5 -32.5t-45 -68.5t-32.5 -116l-14 -65h219q18 0 16 -21l-14 -76q-4 -20 -25 -20h-219l-133 -737q-16 -84 -30.5 -151t-30 -124t-33 -108.5t-37.5 -104.5q-10 -25 -29 -24h-84q-10 0 -16 7t-2 17z" />
<glyph unicode="g" horiz-adv-x="985" d="M78 256q0 61 15.5 154.5t31.5 187.5q20 115 49 195.5t75 131t114.5 74t171.5 23.5q117 0 180 -38t84 -97h4l28 88q10 29 41 29h48q33 0 26 -33l-186 -969q-18 -96 -46 -168t-73 -120t-114 -72.5t-167 -24.5q-72 0 -132 9t-126 30q-27 8 -14 39l18 51q6 16 16.5 17 t24.5 -3q53 -16 104.5 -23t123.5 -7q57 0 101 18.5t73 50.5q35 37 52 88.5t30 110.5l20 96h-6q-79 -112 -263 -112h-3q-143 0 -222 62t-79 212zM211 266q0 -96 45 -135q43 -37 135 -37q119 0 199 63.5t104 194.5l51 266q6 35 10.5 62t4.5 49q0 86 -56.5 133t-154.5 47 q-68 0 -116 -14t-82 -52t-56.5 -102.5t-40.5 -162.5q-16 -90 -29.5 -168t-13.5 -144z" />
<glyph unicode="h" horiz-adv-x="1017" d="M51 31l258 1333q4 23 31 22h82q23 0 20 -20l-88 -451h6q20 23 38 36.5t46 29.5q39 20 84 30.5t123 10.5q98 0 155.5 -23.5t88.5 -72.5t34 -120q0 -8 1 -17q0 -67 -21 -168l-112 -592q-2 -10 -8.5 -19.5t-22.5 -9.5h-72q-23 0 -29 6t-1 29l114 598q19 97 19 147 q0 49 -23 80q-20 29 -56 40t-102 11q-70 0 -116.5 -15t-87.5 -50t-70 -88t-45 -142l-113 -585q-2 -14 -9 -22.5t-25 -8.5h-70q-35 0 -29 31z" />
<glyph unicode="i" horiz-adv-x="462" d="M78 16l186 965q4 23 29 23h84q25 0 20 -23l-186 -965q-4 -16 -21 -16h-98q-16 0 -14 16zM291 1282q10 47 37.5 67.5t74.5 20.5h9q35 0 55 -14.5t20 -44.5q0 -8 -2 -20.5t-8 -39.5q-10 -47 -34.5 -64.5t-71.5 -17.5h-9q-79 0 -79 59q0 13 8 54z" />
<glyph unicode="j" horiz-adv-x="440" d="M-258 -334l12 60q4 25 27 24h45q104 0 166 68q35 39 53 92t31 115l182 956q4 23 29 23h84q25 0 20 -23l-184 -967q-16 -82 -43 -153.5t-80 -124.5q-45 -47 -108.5 -72.5t-163.5 -25.5h-48q-29 0 -22 28zM274 1229q0 18 9 53q10 49 37.5 68.5t74.5 19.5h8q35 0 55.5 -14.5 t20.5 -44.5q0 -10 -2 -22.5t-6 -28.5q-18 -90 -109 -91h-8q-80 0 -80 60z" />
<glyph unicode="k" horiz-adv-x="892" d="M49 16l260 1348q4 23 31 22h80q25 0 20 -22l-147 -770h92q45 0 72 33l307 354q16 23 43 23h94q12 0 17.5 -8.5t-9.5 -24.5l-366 -414q-11 -13 -11 -30q0 -14 7 -31l211 -469q4 -10 -0.5 -18.5t-18.5 -8.5h-88q-25 0 -31 20l-184 431q-10 25 -21.5 31.5t-39.5 6.5h-95 l-92 -473q-4 -16 -20 -16h-94q-20 0 -17 16z" />
<glyph unicode="l" horiz-adv-x="522" d="M102 248l215 1114q4 25 27 24h86q25 0 21 -24l-213 -1106q-10 -57 -11 -85t9 -44q10 -12 26.5 -16.5t48.5 -4.5h66q27 0 20 -26l-10 -55q-6 -25 -33 -25h-78q-59 0 -95 9t-58 32q-32 33 -32 107q0 43 11 100z" />
<glyph unicode="m" horiz-adv-x="1603" d="M51 31l182 952q2 20 27 21h62q25 0 22 -21l-10 -86h4q51 61 115 90q76 35 182 35q66 0 111 -11.5t77 -33.5q29 -20 45.5 -43t26.5 -49h4q61 63 153.5 100t180.5 37q184 0 246 -98q33 -49 36 -122v-17q0 -68 -20 -164l-115 -590q-4 -18 -12 -24.5t-25 -6.5h-69 q-16 0 -23.5 6t-3.5 25l117 602q14 74 14 126t-18 87q-34 65 -150 65h-4q-86 0 -160.5 -36.5t-127.5 -98.5q0 -47 -15 -131l-117 -614q-6 -31 -34 -31h-70q-35 0 -29 31l117 602q13 66 13 116q0 65 -23 101q-41 61 -160 61q-61 0 -106 -15t-84 -48q-41 -35 -68 -85t-39 -112 l-119 -620q-4 -14 -10 -22.5t-24 -8.5h-70q-35 0 -29 31z" />
<glyph unicode="n" horiz-adv-x="1017" d="M51 31l182 952q2 20 27 21h62q25 0 22 -21l-10 -88h4q20 25 45 45.5t61 40.5q39 20 84 30.5t123 10.5q98 0 155.5 -23.5t88.5 -72.5t34 -120q0 -8 1 -17q0 -67 -21 -168l-112 -592q-2 -10 -8.5 -19.5t-22.5 -9.5h-72q-23 0 -29 6t-1 29l114 598q19 97 19 147q0 49 -23 80 q-20 29 -56 40t-102 11q-70 0 -116.5 -15t-87.5 -50t-70 -88t-45 -142l-113 -585q-4 -14 -10 -22.5t-24 -8.5h-70q-35 0 -29 31z" />
<glyph unicode="o" horiz-adv-x="980" d="M78 268q0 51 10 127t23 142q27 131 59.5 223t85.5 150.5t132 85t196 26.5q176 0 248.5 -71.5t72.5 -215.5q0 -51 -10 -127t-23 -141q-27 -131 -59.5 -223t-85.5 -150.5t-132 -85t-196 -26.5q-160 0 -240.5 67.5t-80.5 218.5zM213 272q0 -100 50.5 -139t154.5 -39 q86 0 140 29t90 84t57.5 135t39.5 182q10 55 17.5 110.5t7.5 96.5q0 96 -48 137t-157 41q-86 0 -140 -28.5t-90 -84t-57.5 -135t-39.5 -182.5q-10 -55 -17.5 -110.5t-7.5 -96.5z" />
<glyph unicode="p" horiz-adv-x="1026" d="M-23 -340l254 1315q4 16 9.5 22.5t21.5 6.5h60q25 0 22 -29l-10 -80h4q25 35 50.5 55.5t60.5 36.5q75 35 186 35q170 0 250 -80q35 -37 50 -84t15 -125q0 -41 -8 -108.5t-31 -173.5q-18 -86 -33.5 -145.5t-34 -103.5t-41 -75t-50.5 -57q-55 -49 -128 -68.5t-184 -19.5 q-190 0 -241 118h-4l-84 -440q-4 -23 -23 -22h-94q-20 0 -17 22zM241 261q3 -56 31 -97q25 -35 66 -52.5t115 -17.5q59 0 109 13.5t89 48.5q43 41 71 110.5t56 200.5q18 88 25.5 146.5t7.5 91.5q0 47 -7 83.5t-30 65.5t-64.5 42t-111.5 13q-57 0 -112.5 -15t-90.5 -46 q-20 -18 -34.5 -37.5t-25.5 -46.5t-20.5 -60.5t-17.5 -80.5l-47 -240q-10 -53 -10 -99q0 -12 1 -23z" />
<glyph unicode="q" horiz-adv-x="983" d="M78 256q0 61 15.5 153.5t31.5 188.5q23 129 55.5 211t81.5 130t114.5 65.5t155.5 17.5q117 0 181.5 -38t85.5 -97h4l28 88q10 29 41 29h48q33 0 26 -33l-252 -1311q-4 -23 -26 -22h-93q-25 0 -18 26l94 430h-6q-47 -61 -111.5 -86.5t-156.5 -25.5q-143 0 -221 62t-78 212 zM211 266q0 -94 42 -133t136 -39q123 0 201 63.5t104 194.5l51 266q6 33 10.5 61t4.5 50q0 84 -55.5 132t-161.5 48q-78 0 -127 -21.5t-80 -63.5t-49.5 -104.5t-32.5 -141.5q-16 -92 -29.5 -169t-13.5 -143z" />
<glyph unicode="r" horiz-adv-x="641" d="M51 27l185 956q2 20 26 21h62q25 0 22 -21l-14 -109h4q51 66 115 99q61 31 147 31h64q16 0 20 -5.5t2 -19.5l-14 -70q-6 -23 -31 -22h-66q-59 0 -100 -15.5t-76 -46.5q-74 -63 -98 -200l-115 -598q-6 -27 -39 -27h-63q-37 0 -31 27z" />
<glyph unicode="s" horiz-adv-x="827" d="M16 78l19 57q10 29 41 15q131 -53 246 -54q82 0 139 22.5t86 82.5q14 29 21 58.5t7 68.5q0 43 -27.5 67.5t-96.5 46.5l-127 39q-188 57 -189 205q0 35 5 63.5t16 63.5q35 113 115.5 161t232.5 48q125 0 239 -39q33 -12 25 -37l-18 -59q-10 -29 -43 -17 q-49 18 -104.5 27.5t-106.5 9.5q-100 0 -144.5 -28.5t-66.5 -85.5q-10 -29 -14.5 -51.5t-4.5 -41.5q0 -37 21.5 -62.5t81.5 -43.5l135 -41q55 -16 93 -34.5t61.5 -43t35 -58.5t11.5 -81q0 -31 -9.5 -77t-23.5 -81q-47 -111 -137.5 -153.5t-221.5 -42.5q-63 0 -136 14 t-142 43q-29 10 -19 39z" />
<glyph unicode="t" horiz-adv-x="634" d="M104 909l11 49q6 25 26 27l127 19l45 231q4 25 29 25h84q25 0 20 -25l-45 -231h220q18 0 16 -21l-14 -76q-4 -20 -25 -20h-219l-111 -592q-11 -58 -11 -94v-6q1 -37 24 -60q16 -16 43.5 -22.5t83.5 -6.5h86q10 0 17 -3t3 -19l-12 -61q-4 -23 -27 -23h-96q-68 0 -118 11.5 t-81 37.5q-55 45 -55 139q0 23 4 51.5t8 55.5l109 592h-125q-20 0 -17 22z" />
<glyph unicode="u" horiz-adv-x="1001" d="M92 219q0 27 4 56.5t10 64.5l123 635q6 29 35 29h76q29 0 22 -29l-120 -615q-14 -70 -15 -120q0 -72 42 -106t155 -34q70 0 117 14.5t82 49.5q35 33 52 76t34 122l120 615q4 27 29 27h82q27 0 21 -27l-123 -635q-20 -106 -48 -167.5t-77 -104.5q-53 -47 -124 -67.5 t-186 -20.5q-152 0 -231.5 56t-79.5 181z" />
<glyph unicode="v" horiz-adv-x="860" d="M104 975q0 16 9.5 22.5t19.5 6.5h78q14 0 21.5 -4.5t7.5 -20.5q6 -217 27.5 -431t58.5 -444h18q119 201 222.5 414t207.5 461q6 16 14.5 20.5t22.5 4.5h82q10 0 16 -6.5t0 -22.5q-98 -231 -208.5 -456.5t-241.5 -448.5q-25 -43 -50.5 -56.5t-60.5 -13.5h-65 q-33 0 -52.5 15.5t-29.5 70.5q-39 217 -62.5 436t-34.5 453z" />
<glyph unicode="w" horiz-adv-x="1331" d="M104 969q0 35 35 35h68q33 0 33 -35q-6 -136 -6 -270q0 -80 2 -160q6 -213 22 -435h10q88 182 169 376t179 413q29 61 76 61h92q29 0 40.5 -13t13.5 -48q6 -193 14 -381t20 -408h11q92 195 179 403t179 462q10 35 45 35h72q35 0 22 -35q-90 -213 -177 -434.5t-199 -450.5 q-23 -51 -44.5 -67.5t-56.5 -16.5h-69q-37 0 -55.5 17.5t-22.5 66.5q-16 184 -23.5 368.5t-11.5 378.5h-10q-74 -190 -157 -374.5t-169 -372.5q-23 -49 -46.5 -66.5t-59.5 -17.5h-70q-35 0 -51.5 16.5t-20.5 67.5q-8 104 -14 208.5t-10 213t-6.5 222.5t-2.5 241z" />
<glyph unicode="x" horiz-adv-x="884" d="M-53 23l346 466q23 29 10 58l-170 424q-6 16 1 24.5t20 8.5h77q27 0 35 -23l146 -385q6 -16 11 -24.5t15 -8.5h15q14 0 36 29l287 389q16 23 43 23h101q12 0 16 -7.5t-8 -25.5l-340 -436q-18 -27 -8 -56l206 -456q10 -23 -16 -23h-100q-20 0 -23 14l-176 412 q-8 16 -14 24.5t-21 8.5h-12q-20 0 -43 -33l-289 -412q-8 -14 -26 -14h-107q-29 0 -12 23z" />
<glyph unicode="y" horiz-adv-x="845" d="M106 -313.5q-1 12.5 9 26.5q47 80 91 155t85 146q-41 6 -68.5 36t-38.5 93q-27 168 -45 368t-33 468q0 25 27 25h86q25 0 25 -23q10 -262 26.5 -462t36.5 -353q6 -47 41 -49q55 102 106.5 204.5t101.5 208t99.5 219t102.5 238.5q6 16 26 17h93q14 0 17 -8.5t-1 -18.5 q-82 -193 -158 -357.5t-157 -328.5q-78 -156 -161 -307.5t-179 -317.5q-10 -18 -21.5 -24.5t-30.5 2.5l-63 24q-16 6 -17 18.5z" />
<glyph unicode="z" horiz-adv-x="829" d="M-20 39l4 20q4 20 15 39t38 52l604 737h-473q-25 0 -18 28l12 64q6 25 28 25h613q37 0 31 -39l-5 -27q-2 -10 -6 -19.5t-13 -20.5t-22.5 -26.5t-33.5 -40.5l-590 -714h491q27 0 21 -25l-14 -67q-6 -25 -33 -25h-617q-41 0 -32 39z" />
<glyph unicode="{" horiz-adv-x="624" d="M123 575l10 54q6 27 29 33q84 18 130 59t62 129l62 313q27 137 86 197.5t153 60.5h86q16 0 19.5 -6t-0.5 -22l-10 -68q-2 -25 -29 -25h-66q-51 0 -75.5 -35.5t-42.5 -134.5l-58 -292q-23 -115 -70 -169.5t-139 -78.5v-6q59 -27 88 -65t29 -105q0 -20 -3 -44t-7 -53 l-53 -276q-6 -31 -8.5 -49.5t-2.5 -30.5q0 -59 72 -59h66q23 0 16 -25l-12 -67q-4 -16 -8.5 -22.5t-20.5 -6.5h-78q-78 0 -120 43t-42 115q0 23 3.5 47t7.5 53l57 297q6 37 6 63q0 59 -30.5 89t-90.5 53q-23 10 -16 34z" />
<glyph unicode="|" horiz-adv-x="428" d="M41 -10l268 1388q6 35 37 35h68q20 0 23 -12t1 -23l-268 -1388q-6 -35 -35 -35h-67q-33 0 -27 35z" />
<glyph unicode="}" horiz-adv-x="624" d="M-113.5 -213q-3.5 6 0.5 23l11 67q2 25 28 25h66q51 0 75.5 35.5t43.5 134.5l57 293q23 115 70 169t139 78v6q-59 27 -88 65t-29 105q0 20 3 44t7 53l54 276q6 31 8 49.5t2 30.5q0 59 -72 59h-65q-23 0 -17 25l13 68q4 16 8 22t20 6h78q78 0 120 -43t42 -114 q0 -23 -3 -47.5t-7 -53.5l-58 -297q-6 -37 -6 -63q0 -59 31 -89t90 -52q23 -10 16 -35l-10 -54q-6 -27 -29 -32q-84 -18 -130 -59.5t-62 -129.5l-62 -313q-27 -137 -86 -197.5t-153 -60.5h-86q-16 0 -19.5 6z" />
<glyph unicode="~" d="M31 560q-2 11 14 34q88 123 166 180t151 57q53 0 91.5 -17t72.5 -44l109 -82q29 -23 50.5 -34t45.5 -11q45 0 92 41t103 117q16 23 29.5 23.5t27.5 -9.5l43 -31q16 -10 19.5 -21t-11.5 -34q-43 -66 -84 -111t-78.5 -71.5t-75.5 -37.5t-75 -11q-61 0 -104.5 19.5 t-73.5 41.5l-101 78q-31 23 -52 35t-46 12q-39 0 -81 -31.5t-111 -122.5q-16 -23 -31 -25.5t-31 7.5l-41 27q-16 10 -18 21z" />
<glyph unicode="&#xa1;" horiz-adv-x="446" d="M73 -291l209 964q6 35 39 35h68q29 0 22 -35l-168 -964q-6 -35 -38 -35h-109q-20 0 -22.5 12t-0.5 23zM293 876q0 4 2 18.5t8 47.5q8 41 34.5 60t67.5 19h21q72 0 71 -59q0 -4 -3 -22.5t-7 -42.5q-16 -80 -102 -80h-21q-72 0 -71 59z" />
<glyph unicode="&#xa2;" d="M168 254q0 104 37 305q14 78 29.5 136.5t34 103.5t43 77.5t55.5 61.5q51 43 116.5 62.5t149.5 21.5l33 176q4 16 10 21.5t22 5.5h41q31 0 25 -27l-35 -182q66 -10 119 -31.5t84 -44.5q20 -12 4 -39l-33 -49q-16 -25 -43 -6q-29 18 -73 34.5t-80 20.5l-158 -807 q47 4 102.5 16.5t104.5 30.5q35 12 37 -20l4 -51q2 -14 -3.5 -21.5t-25.5 -15.5q-53 -20 -116.5 -32.5t-123.5 -16.5l-30 -162q-6 -31 -35 -31h-37q-33 0 -27 31l31 162q-135 12 -198.5 80.5t-63.5 189.5zM301 270q0 -78 34 -121t118 -53l157 811q-72 0 -116.5 -25.5 t-74.5 -71.5t-48.5 -112.5t-32.5 -150.5q-16 -92 -26.5 -161t-10.5 -116z" />
<glyph unicode="&#xa3;" d="M6 29l8 43q4 20 17.5 30.5t36.5 26.5q98 68 149 143.5t74 188.5l33 164h-109q-16 0 -21.5 6t-3.5 20l7 39q6 29 41 35l110 20l45 238q20 109 53 173.5t91 109.5q55 41 123.5 59t160.5 18q63 0 132 -16t118 -43q25 -12 12 -39l-26 -57q-14 -28 -45 -12q-49 23 -100.5 35 t-102.5 12q-113 0 -181.5 -52.5t-90.5 -179.5l-48 -246h359q39 0 33 -28l-17 -68q-6 -25 -41 -24h-360l-27 -140q-23 -111 -75 -200.5t-144 -159.5v-4h627q23 0 28 -7.5t0 -29.5l-10 -51q-6 -33 -39 -33h-786q-37 0 -31 29z" />
<glyph unicode="&#xa4;" d="M-9 197.5q1 9.5 17 23.5l172 144q-35 66 -35 151q0 82 29 154.5t82 132.5l-109 133q-10 14 -8 23.5t17 21.5l61 49q12 10 22.5 12t20.5 -12l111 -137q53 29 111.5 45t123.5 16q59 0 112.5 -17t94.5 -48l172 141q14 14 23.5 12t19.5 -12l41 -49q8 -12 7 -21.5t-15 -23.5 l-170 -139q39 -72 39 -156q0 -86 -31 -159.5t-84 -133.5l107 -127q10 -14 7 -23.5t-18 -21.5l-59 -49q-14 -10 -23.5 -12t-19.5 12l-111 133q-49 -27 -106 -41t-121 -14q-127 0 -211 65l-176 -143q-27 -25 -43 0l-43 49q-8 12 -7 21.5zM289 524q0 -86 56 -139t144 -53 q61 0 115.5 22.5t94.5 63.5t62.5 95t22.5 120q0 86 -56 140t-144 54q-61 0 -115.5 -23.5t-94.5 -63.5t-62.5 -96t-22.5 -120z" />
<glyph unicode="&#xa5;" d="M137 272l10 54q4 14 11.5 22t25.5 8h250l25 119h-252q-18 0 -23.5 7t-1.5 24l11 53q2 14 9 22.5t25 8.5h195q-31 86 -55.5 168t-45 168t-38 178t-33.5 199q-2 14 6 19t18 5h91q29 0 34 -20q14 -94 32 -178.5t36 -164t39.5 -156.5t48.5 -157h14q57 80 109.5 157 t102.5 156.5t98.5 163.5t101.5 179q10 20 39 20h90q10 0 17.5 -5t-1.5 -19q-61 -115 -114.5 -208.5t-105.5 -176t-108.5 -162.5t-119.5 -166h192q18 0 23.5 -8.5t3.5 -22.5l-10 -57q-4 -20 -14.5 -23.5t-20.5 -3.5h-252l-25 -119h254q29 0 23 -30l-10 -58q-4 -20 -15.5 -23 t-21.5 -3h-252l-39 -207q-6 -35 -41 -35h-80q-27 0 -20 35l39 207h-250q-18 0 -23.5 7t-1.5 23z" />
<glyph unicode="&#xa6;" horiz-adv-x="428" d="M41 -10l90 469q8 35 39 35h68q20 0 23 -9.5t-1 -25.5l-90 -469q-6 -35 -39 -35h-68q-29 0 -22 35zM219 909l90 469q6 35 37 35h64q35 0 28 -35l-90 -469q-6 -35 -35 -35h-67q-33 0 -27 35z" />
<glyph unicode="&#xa7;" horiz-adv-x="868" d="M33 66l18 59q8 27 37 16q55 -23 116.5 -34t117.5 -11q84 0 136 16.5t72 57.5q12 25 17.5 49.5t5.5 46.5q0 53 -27.5 77t-99.5 46l-119 37q-84 27 -129 67.5t-45 122.5q0 104 51.5 169t141.5 92q-109 41 -109 151q0 33 3 58.5t13 58.5q31 104 114 151t235 47q66 0 122 -9 t109 -27q23 -8 14 -35l-18 -60q-8 -27 -37 -16q-88 33 -197 33q-98 0 -149 -23.5t-70 -87.5q-10 -35 -10 -65q0 -41 27.5 -63.5t87.5 -37.5l119 -28q100 -25 142 -70t42 -123q0 -98 -54.5 -168.5t-140.5 -101.5q61 -29 87 -70t26 -98q0 -33 -6 -71t-17 -72 q-29 -92 -116.5 -130t-227.5 -38q-143 0 -268 49q-23 10 -14 35zM262 639q0 -45 23.5 -69.5t89.5 -45.5l88 -26q172 57 172 211q0 47 -26.5 72.5t-76.5 35.5l-108 25q-76 -29 -119 -75t-43 -128z" />
<glyph unicode="&#xa8;" horiz-adv-x="698" d="M225 1286q0 4 1 16.5t10 53.5q8 39 35.5 59.5t66.5 20.5h20q76 0 76 -60v-15t-10 -52q-8 -41 -37 -60.5t-70 -19.5h-20q-33 0 -52.5 14t-19.5 43zM557 1286q0 8 3 24.5t7 45.5q6 39 34 59.5t69 20.5h20q76 0 76 -60q0 -4 -2 -21.5t-8 -45.5q-8 -41 -37 -60.5t-70 -19.5 h-20q-33 0 -52.5 14t-19.5 43z" />
<glyph unicode="&#xa9;" horiz-adv-x="1656" d="M96 559q0 197 67.5 360.5t187.5 282.5t286 185.5t360 66.5q135 0 248 -40t194 -115.5t127 -184.5t46 -246q0 -195 -65.5 -358.5t-185.5 -283.5t-286 -187.5t-366 -67.5q-135 0 -248 42t-194 120t-126 185.5t-45 240.5zM236 580q0 -109 35.5 -197t101 -151.5t156.5 -98.5 t202 -35q156 0 291 55.5t234.5 154t156.5 233.5t57 297q0 242 -133 365.5t-368 123.5q-168 0 -303.5 -59.5t-230.5 -161.5t-147 -238.5t-52 -287.5zM516 547q0 31 5 71.5t22 118.5q23 111 56.5 185.5t82.5 121.5q45 43 104.5 64.5t145.5 21.5q55 0 110.5 -16t98.5 -47 q12 -10 12 -21.5t-8 -21.5l-29 -41q-10 -14 -19.5 -15t-17.5 5q-37 25 -73.5 37t-81.5 12q-109 2 -165.5 -71.5t-86.5 -227.5q-12 -59 -17.5 -94t-5.5 -60q0 -59 23 -100q35 -66 143 -66q78 0 166 33q33 12 35 -10l6 -55q4 -14 -4 -21.5t-31 -17.5q-55 -23 -102 -31t-94 -8 q-86 0 -137.5 18.5t-80.5 53.5q-31 33 -44 77.5t-13 104.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="634" d="M111 715q0 16 3 35.5t7 44.5q10 53 26.5 84.5t45.5 54.5q33 25 73.5 34t106.5 9h69q23 0 42.5 -1t35.5 -3l10 53q2 10 3.5 19.5t1.5 17.5q0 31 -15 45q-25 25 -104 25q-31 0 -70 -5.5t-76 -15.5q-27 -6 -28 14l-9 45q-4 23 25 29q39 10 90 17.5t92 7.5q66 0 107 -8.5 t67 -28.5q20 -16 27.5 -37t7.5 -45q0 -16 -2 -35.5t-6 -36.5l-78 -411q-4 -14 -18 -15h-68q-18 0 -16 15l4 49q-29 -41 -69 -57.5t-93 -16.5h-43q-80 0 -114.5 29.5t-34.5 87.5zM227 741q0 -49 62 -49h47q59 0 100 35t53 94l13 58q-14 2 -28.5 3t-31.5 1h-92q-25 0 -44 -2 t-34 -13q-16 -10 -23 -27.5t-13 -43.5q-4 -18 -6.5 -32.5t-2.5 -23.5z" />
<glyph unicode="&#xab;" horiz-adv-x="999" d="M-41 449q0 12 10 47q4 14 11.5 22t23.5 21l332 235q14 10 23.5 10t19.5 -10l37 -43q23 -23 -4 -41l-291 -229l188 -207q23 -25 2 -41l-43 -35q-18 -16 -49 10l-242 228q-18 18 -18 33zM365 449q0 12 10 47q4 14 11.5 22t23.5 21l332 235q14 10 23.5 10t19.5 -10l37 -43 q23 -23 -4 -41l-291 -229l188 -207q23 -25 2 -41l-43 -35q-18 -16 -49 10l-242 228q-18 18 -18 33z" />
<glyph unicode="&#xac;" d="M66 508l12 72q6 31 39 30h837q33 0 27 -30l-86 -461q-6 -31 -39 -31h-72q-29 0 -22 31l69 358h-739q-33 0 -26 31z" />
<glyph unicode="&#xad;" horiz-adv-x="608" d="M66 496l14 75q4 14 11 22.5t26 8.5h395q18 0 23.5 -8t1.5 -23l-15 -79q-4 -20 -15 -23.5t-22 -3.5h-395q-18 0 -23.5 7t-0.5 24z" />
<glyph unicode="&#xae;" horiz-adv-x="1656" d="M96 559q0 197 67.5 360.5t187.5 282.5t286 185.5t360 66.5q135 0 248 -40t194 -115.5t127 -184.5t46 -246q0 -195 -65.5 -358.5t-185.5 -283.5t-286 -187.5t-366 -67.5q-135 0 -248 42t-194 120t-126 185.5t-45 240.5zM236 580q0 -109 35.5 -197t101 -151.5t156.5 -98.5 t202 -35q156 0 291 55.5t234.5 154t156.5 233.5t57 297q0 242 -133 365.5t-368 123.5q-168 0 -303.5 -59.5t-230.5 -161.5t-147 -238.5t-52 -287.5zM528 322l150 772q4 18 17.5 29.5t29.5 13.5q47 6 87 8t99 2q53 0 101.5 -8.5t85.5 -30t59.5 -56t22.5 -87.5q0 -27 -3 -55.5 t-8 -51.5q-18 -86 -75.5 -139t-139.5 -70l113 -323q2 -10 -3 -17.5t-20 -7.5h-90q-16 0 -24 21l-101 313h-10h-54.5t-51.5 2l-62 -315q-2 -20 -22 -21h-86q-16 0 -15 21zM735 745q43 -6 94 -6q102 0 150.5 36t62.5 108q4 16 4.5 30.5t0.5 28.5q0 51 -33 75.5t-109 24.5 q-23 0 -49.5 -1t-42.5 -3q-23 -2 -29 -28z" />
<glyph unicode="&#xaf;" horiz-adv-x="694" d="M229 1292l15 68q2 14 9 22.5t26 8.5h460q33 0 27 -31l-16 -72q-6 -27 -35 -26h-463q-29 0 -23 30z" />
<glyph unicode="&#xb0;" horiz-adv-x="657" d="M207 1186q0 59 20.5 112.5t58.5 92.5t90 61.5t116 22.5q92 0 150 -53.5t58 -147.5q0 -59 -20.5 -112.5t-58 -92.5t-90 -61.5t-115.5 -22.5q-92 0 -150.5 53.5t-58.5 147.5zM328 1204q0 -55 28.5 -84.5t75.5 -29.5q66 0 107 46.5t41 118.5q0 47 -29 81t-76 34 q-66 0 -106.5 -47t-40.5 -119z" />
<glyph unicode="&#xb1;" d="M-29 31l11 59q6 31 38 31h842q29 0 23 -31l-11 -63q-6 -27 -34 -27h-842q-33 0 -27 31zM106 717l13 71q4 16 11 23.5t26 7.5h354l68 344q6 31 36 31h68q35 0 29 -31l-68 -344h354q31 0 25 -31l-12 -75q-4 -16 -11.5 -21.5t-25.5 -5.5h-355l-67 -344q-4 -18 -11.5 -24.5 t-23.5 -6.5h-76q-29 0 -22 31l67 344h-354q-31 0 -25 31z" />
<glyph unicode="&#xb2;" horiz-adv-x="684" d="M178 944l10 57q10 55 25.5 96.5t38 72t55.5 52t80 40.5l109 41q59 23 88.5 50t29.5 91q0 39 -26 57.5t-92 18.5q-39 0 -72 -5.5t-62 -15.5q-25 -8 -30 17l-10 55q-2 18 20 26q39 12 82 19.5t98 7.5q98 0 159.5 -38t61.5 -124q0 -18 -3 -42.5t-9 -45.5q-16 -59 -55 -94 t-119 -63l-100 -37q-66 -25 -97.5 -57.5t-42.5 -96.5l-2 -12h318q18 0 16 -17l-12 -63q-4 -18 -23 -19h-409q-33 0 -27 29z" />
<glyph unicode="&#xb3;" horiz-adv-x="684" d="M203 965l20 51q8 23 31 14q29 -12 68.5 -20.5t78.5 -8.5q129 0 152 93q4 12 6 24t2 27q0 78 -141 78h-47q-18 0 -15 18l11 59q2 18 22 19h39q72 0 119 20.5t59 65.5q8 27 8 55q0 31 -24.5 47.5t-79.5 16.5q-31 0 -63.5 -5.5t-61.5 -15.5q-27 -6 -29 17l-8 53q-2 23 23 29 q37 10 78 16t96 6q88 0 141 -32.5t55 -106.5q0 -10 -2 -31.5t-6 -44.5q-25 -104 -131 -137v-6q41 -14 61.5 -41t20.5 -76q0 -16 -3 -36.5t-7 -37.5q-27 -104 -96.5 -139t-180.5 -35q-47 0 -100 9.5t-90 25.5q-14 6 -6 29z" />
<glyph unicode="&#xb4;" horiz-adv-x="630" d="M225 1251q-12 31 17 43l383 170q39 18 51 -14l26 -72q6 -16 -1 -25.5t-35 -19.5l-381 -135q-33 -12 -43 14z" />
<glyph unicode="&#xb5;" horiz-adv-x="1136" d="M-31 -334l252 1309q6 29 33 29h76q31 0 24 -29l-123 -635q-29 -141 19 -197q43 -53 151 -53q127 0 209 71.5t109 200.5l123 613q6 29 35 29h73q31 0 25 -29l-152 -774q-10 -59 1.5 -82t43.5 -23q18 0 37 3t31 7q14 4 22.5 2t10.5 -18l4 -45q2 -14 -4 -22.5t-25 -16.5 q-29 -12 -56.5 -18t-66.5 -6q-127 0 -133 118h-4q-100 -119 -299 -118q-78 0 -131 23.5t-76 62.5l-76 -398q-6 -33 -36 -32h-76q-27 0 -21 28z" />
<glyph unicode="&#xb6;" horiz-adv-x="1136" d="M174 967q0 102 36 184t99.5 140.5t149.5 89t184 30.5h582q18 0 22 -7t0 -24l-10 -59q-4 -31 -37 -31h-129l-289 -1497q-4 -23 -15 -28t-32 -5h-49q-23 0 -29 9.5t-2 23.5l289 1497h-213l-289 -1497q-4 -16 -13 -24.5t-36 -8.5h-65q-33 0 -27 33l168 879q-68 0 -122 22.5 t-93 62.5t-59.5 93t-20.5 117z" />
<glyph unicode="&#xb7;" horiz-adv-x="401" d="M4 545q0 16 2 32.5t8 45.5q8 41 35 60.5t68 19.5h20q76 0 76 -58q0 -4 -2 -23.5t-8 -48.5q-16 -80 -105 -79h-20q-35 0 -54.5 14t-19.5 37z" />
<glyph unicode="&#xb8;" horiz-adv-x="552" d="M-117 -418l15 41q8 23 30 17q31 -10 68 -17.5t65 -7.5q61 0 91 16q14 8 25 20.5t18 35.5q4 12 4 20v21q0 57 -70 57q-23 0 -49.5 -3t-50.5 -7q-14 -2 -27 10l-14 18q-12 12 0 33l71 144q10 20 31 20h60q29 0 18 -23l-55 -108q16 4 36.5 5t38.5 1q53 0 88 -29.5t35 -91.5 q0 -35 -6 -65q-10 -43 -26.5 -72t-53.5 -53q-29 -20 -71.5 -28.5t-94.5 -8.5q-33 0 -81 8t-84 23q-16 6 -11 24z" />
<glyph unicode="&#xb9;" horiz-adv-x="684" d="M176 938l10 55q2 20 27 21h160l92 487h-8l-158 -90q-14 -8 -27 8l-30 47q-12 16 8 29l186 107q31 16 70 16h72q20 0 27 -5t3 -22l-110 -577h159q20 0 19 -23l-8 -55q-2 -20 -27 -21h-446q-20 0 -19 23z" />
<glyph unicode="&#xba;" horiz-adv-x="671" d="M137 776q0 25 4.5 57.5t16.5 102.5q16 86 40.5 142.5t63.5 90.5q63 55 197 56q109 0 163 -39t54 -137q0 -25 -4 -59t-17 -103q-31 -168 -104 -234q-63 -55 -197 -55q-111 0 -164 40t-53 138zM254 795q0 -59 24.5 -83t83.5 -24q37 0 65 7t48 28q25 25 39 69t29 111 q10 43 13 73t3 52q0 59 -25.5 83t-82.5 24q-37 0 -65 -7.5t-48 -27.5q-45 -46 -70 -185q-8 -43 -11 -70.5t-3 -49.5z" />
<glyph unicode="&#xbb;" horiz-adv-x="999" d="M112 219q-23 23 4 41l291 229l-188 207q-23 25 -2 41l43 35q18 16 49 -10l242 -228q18 -18 18 -33q0 -12 -10 -47q-4 -14 -11.5 -22t-23.5 -21l-332 -235q-14 -10 -23.5 -10t-19.5 10zM520 219q-23 23 4 41l291 229l-188 207q-23 25 -2 41l43 35q18 16 49 -10l242 -228 q18 -18 18 -33q0 -12 -10 -47q-4 -14 -11.5 -22t-23.5 -21l-332 -235q-14 -10 -23.5 -10t-19.5 10z" />
<glyph unicode="&#xbc;" horiz-adv-x="1605" d="M86 643l10 55q2 20 27 21h160l92 487h-8l-158 -90q-14 -8 -27 8l-30 47q-12 16 8 29l186 107q31 16 70 16h71q20 0 27.5 -5t3.5 -22l-110 -577h159q20 0 19 -23l-8 -55q-2 -20 -27 -20h-447q-20 0 -18 22zM296 -9q-3 9 9 25l969 1303q18 25 47 24h72q16 0 18 -10t-8 -24 l-971 -1303q-18 -25 -45 -24h-72q-16 0 -19 9zM901 195l12 53q2 16 29 45l303 358q23 27 41.5 36t44.5 9h88q41 0 35 -37l-76 -387h76q16 0 14 -18l-12 -59q-4 -20 -22 -21h-74l-29 -156q-4 -18 -20 -18h-88q-20 0 -17 18l31 156h-313q-29 0 -23 21zM1044 272h211l64 322h-4 z" />
<glyph unicode="&#xbd;" horiz-adv-x="1605" d="M88 645l10 55q2 20 27 21h160l92 487h-8l-158 -90q-14 -8 -27 8l-30 47q-12 16 8 29l186 107q31 16 70 16h72q20 0 27 -5t3 -22l-110 -577h159q20 0 19 -23l-8 -55q-2 -20 -27 -21h-446q-20 0 -19 23zM296 -9q-3 9 9 25l969 1303q18 25 47 24h71q16 0 18.5 -10t-7.5 -24 l-971 -1303q-18 -25 -45 -24h-72q-16 0 -19 9zM952 31l10 57q10 55 25.5 96.5t38 72t55.5 52t80 40.5l109 41q59 23 88.5 50t29.5 91q0 39 -26 57.5t-92 18.5q-39 0 -72 -5.5t-62 -15.5q-25 -8 -30 17l-10 55q-2 18 20 26q39 12 82 19.5t98 7.5q98 0 159.5 -38t61.5 -124 q0 -18 -3 -42.5t-9 -45.5q-16 -59 -55 -94t-119 -63l-100 -37q-66 -25 -97.5 -57.5t-42.5 -96.5l-2 -12h318q18 0 16 -17l-12 -63q-4 -18 -23 -19h-409q-33 0 -27 29z" />
<glyph unicode="&#xbe;" horiz-adv-x="1605" d="M96 684l21 51q8 23 30 15q29 -12 69 -20.5t79 -8.5q129 0 151 92q4 12 6.5 24.5t2.5 26.5q0 78 -142 78h-47q-18 0 -14 19l10 59q2 18 23 18h39q72 0 118.5 20.5t59.5 65.5q8 27 8 56q0 31 -24.5 47t-79.5 16q-31 0 -64 -5t-61 -15q-27 -6 -29 16l-8 53q-2 23 22 29 q37 10 78 16t96 6q88 0 141.5 -32.5t55.5 -106.5q0 -10 -2 -31.5t-6 -44.5q-25 -104 -131 -137v-6q41 -14 61.5 -40.5t20.5 -76.5q0 -16 -3.5 -36.5t-7.5 -36.5q-27 -104 -96.5 -139t-179.5 -35q-47 0 -100.5 9t-90.5 25q-14 6 -6 29zM296 -9q-3 9 9 25l969 1303 q18 25 47 24h72q16 0 18 -10t-8 -24l-971 -1303q-18 -25 -45 -24h-72q-16 0 -19 9zM901 195l12 53q2 16 29 45l303 358q23 27 41.5 36t44.5 9h88q41 0 35 -37l-76 -387h76q16 0 14 -18l-12 -59q-4 -20 -22 -21h-74l-29 -156q-4 -18 -20 -18h-88q-20 0 -17 18l31 156h-313 q-29 0 -23 21zM1044 272h211l64 322h-4z" />
<glyph unicode="&#xbf;" horiz-adv-x="913" d="M72 -87q0 72 15 127.5t49 102.5t89 88t133 86l127 72q41 23 66.5 46t43 55t29 77t19.5 108q4 33 43 33h68q24 0 20 -33q-8 -63 -21.5 -118.5t-35 -101.5t-54 -84t-84.5 -68l-139 -76q-113 -61 -167 -126t-54 -167q0 -86 55.5 -121t165.5 -35q125 0 279 51q31 10 37 -16 l14 -61q6 -25 -21 -35q-74 -27 -153.5 -42t-171.5 -15q-174 0 -263 65.5t-89 187.5zM649 878q0 10 2 20.5t8 43.5q8 41 35 60t68 19h20q33 0 52.5 -15t19.5 -44q0 -10 -2 -21.5t-8 -43.5q-8 -39 -36 -59.5t-67 -20.5h-20q-72 0 -72 61z" />
<glyph unicode="&#xc0;" horiz-adv-x="1144" d="M-14.5 9q-8.5 9 0.5 30q104 236 216.5 467t223.5 448q41 80 84 160t86 154q20 35 40.5 46t53.5 11h74q55 0 70 -65q14 -72 26 -149t23 -157q29 -215 51 -444t39 -471q2 -20 -9.5 -29.5t-27.5 -9.5h-72q-39 0 -41 33q-4 96 -11 194.5t-15 192.5h-490q-23 -49 -44 -95 t-42.5 -92.5t-43 -95.5t-46.5 -104q-14 -33 -49 -33h-72q-16 0 -24.5 9zM362 537h426q-6 94 -15 182t-19 186q-8 68 -17.5 141.5t-21.5 145.5h-25q-41 -74 -79.5 -147.5t-71.5 -139.5q-47 -96 -91.5 -186t-85.5 -182zM448.5 1590.5q-4.5 11.5 1.5 29.5l23 63q12 33 51 15 l387 -172q27 -12 15 -37l-21 -47q-8 -23 -41 -10l-391 139q-20 8 -24.5 19.5z" />
<glyph unicode="&#xc1;" horiz-adv-x="1144" d="M-14.5 9q-8.5 9 0.5 30q104 236 216.5 467t223.5 448q41 80 84 160t86 154q20 35 40.5 46t53.5 11h74q55 0 70 -65q14 -72 26 -149t23 -157q29 -215 51 -444t39 -471q2 -20 -9.5 -29.5t-27.5 -9.5h-72q-39 0 -41 33q-4 96 -11 194.5t-15 192.5h-490q-23 -49 -44 -95 t-42.5 -92.5t-43 -95.5t-46.5 -104q-14 -33 -49 -33h-72q-16 0 -24.5 9zM362 537h426q-6 94 -15 182t-19 186q-8 68 -17.5 141.5t-21.5 145.5h-25q-41 -74 -79.5 -147.5t-71.5 -139.5q-47 -96 -91.5 -186t-85.5 -182zM587 1485q-12 31 17 43l383 170q39 18 51 -15l27 -71 q6 -16 -1.5 -25.5t-35.5 -19.5l-381 -135q-33 -12 -43 14z" />
<glyph unicode="&#xc2;" horiz-adv-x="1144" d="M-14.5 9q-8.5 9 0.5 30q104 236 216.5 467t223.5 448q41 80 84 160t86 154q20 35 40.5 46t53.5 11h74q55 0 70 -65q14 -72 26 -149t23 -157q29 -215 51 -444t39 -471q2 -20 -9.5 -29.5t-27.5 -9.5h-72q-39 0 -41 33q-4 96 -11 194.5t-15 192.5h-490q-23 -49 -44 -95 t-42.5 -92.5t-43 -95.5t-46.5 -104q-14 -33 -49 -33h-72q-16 0 -24.5 9zM362 537h426q-6 94 -15 182t-19 186q-8 68 -17.5 141.5t-21.5 145.5h-25q-41 -74 -79.5 -147.5t-71.5 -139.5q-47 -96 -91.5 -186t-85.5 -182zM451 1460q-23 14 6 39l264 225q10 10 20.5 13.5 t20.5 3.5h63q10 0 17.5 -3t15.5 -14l172 -227q16 -23 -8 -41l-41 -31q-29 -16 -45 5l-154 172h-10l-229 -177q-23 -20 -45 -4z" />
<glyph unicode="&#xc3;" horiz-adv-x="1144" d="M-14.5 9q-8.5 9 0.5 30q104 236 216.5 467t223.5 448q41 80 84 160t86 154q20 35 40.5 46t53.5 11h74q55 0 70 -65q14 -72 26 -149t23 -157q29 -215 51 -444t39 -471q2 -20 -9.5 -29.5t-27.5 -9.5h-72q-39 0 -41 33q-4 96 -11 194.5t-15 192.5h-490q-23 -49 -44 -95 t-42.5 -92.5t-43 -95.5t-46.5 -104q-14 -33 -49 -33h-72q-16 0 -24.5 9zM362 537h426q-6 94 -15 182t-19 186q-8 68 -17.5 141.5t-21.5 145.5h-25q-41 -74 -79.5 -147.5t-71.5 -139.5q-47 -96 -91.5 -186t-85.5 -182zM439 1536q57 80 115.5 119t113.5 39q39 0 62.5 -9.5 t52.5 -31.5l51 -41q23 -18 36 -25.5t31 -7.5q25 0 53.5 22.5t71.5 69.5q20 25 41 6l35 -28q20 -14 0 -41q-55 -78 -114.5 -116t-118.5 -38q-35 0 -60.5 9.5t-50.5 29.5l-51 41q-18 16 -30.5 24.5t-28.5 8.5q-31 0 -57.5 -18.5t-72.5 -71.5q-20 -25 -45 -7l-32 25 q-20 14 -2 41z" />
<glyph unicode="&#xc4;" horiz-adv-x="1144" d="M-14.5 9q-8.5 9 0.5 30q104 236 216.5 467t223.5 448q41 80 84 160t86 154q20 35 40.5 46t53.5 11h74q55 0 70 -65q14 -72 26 -149t23 -157q29 -215 51 -444t39 -471q2 -20 -9.5 -29.5t-27.5 -9.5h-72q-39 0 -41 33q-4 96 -11 194.5t-15 192.5h-490q-23 -49 -44 -95 t-42.5 -92.5t-43 -95.5t-46.5 -104q-14 -33 -49 -33h-72q-16 0 -24.5 9zM362 537h426q-6 94 -15 182t-19 186q-8 68 -17.5 141.5t-21.5 145.5h-25q-41 -74 -79.5 -147.5t-71.5 -139.5q-47 -96 -91.5 -186t-85.5 -182zM498 1536q0 4 1 16.5t9 53.5q8 39 35.5 59.5t66.5 20.5 h21q76 0 75 -60v-15.5t-10 -51.5q-8 -41 -36.5 -60.5t-69.5 -19.5h-21q-33 0 -52 14t-19 43zM829 1536q0 8 3 24.5t8 45.5q6 39 33.5 59.5t68.5 20.5h20q76 0 76 -60q0 -4 -2 -21.5t-8 -45.5q-8 -41 -37 -60.5t-69 -19.5h-21q-33 0 -52.5 14t-19.5 43z" />
<glyph unicode="&#xc5;" horiz-adv-x="1144" d="M-14.5 9q-8.5 9 0.5 30q104 236 216.5 467t223.5 448q41 80 84 160t86 154q20 35 40.5 46t53.5 11h74q55 0 70 -65q14 -72 26 -149t23 -157q29 -215 51 -444t39 -471q2 -20 -9.5 -29.5t-27.5 -9.5h-72q-39 0 -41 33q-4 96 -11 194.5t-15 192.5h-490q-23 -49 -44 -95 t-42.5 -92.5t-43 -95.5t-46.5 -104q-14 -33 -49 -33h-72q-16 0 -24.5 9zM362 537h426q-6 94 -15 182t-19 186q-8 68 -17.5 141.5t-21.5 145.5h-25q-41 -74 -79.5 -147.5t-71.5 -139.5q-47 -96 -91.5 -186t-85.5 -182zM575 1548q0 47 17.5 87t48.5 69t71 44t87 15 q72 0 114.5 -43t42.5 -108q0 -100 -64.5 -160.5t-156.5 -60.5q-74 0 -117 42.5t-43 114.5zM674 1556q0 -31 19.5 -50t54.5 -19q47 0 78.5 31.5t31.5 74.5q0 33 -20.5 53.5t-55.5 20.5q-47 0 -77.5 -31.5t-30.5 -79.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1677" d="M-16.5 10q-6.5 10 6.5 29q131 197 242.5 358.5t212 305t194.5 272.5t192 262q35 45 73 66.5t104 21.5h673q18 0 23.5 -7t1.5 -24l-12 -59q-4 -14 -11.5 -22.5t-25.5 -8.5h-559l-88 -459h495q35 0 29 -30l-12 -60q-6 -31 -41 -30h-496l-78 -400q-12 -59 11 -84 q10 -10 27.5 -15t51.5 -5h461q29 0 23 -31l-11 -59q-6 -31 -38 -31h-467q-66 0 -105 10t-65 35q-25 25 -35 63.5t4 110.5l39 195h-414l-252 -381q-18 -33 -49 -33h-80q-18 0 -24.5 10zM475 537h352l129 667h-10q-74 -102 -133 -185t-114.5 -160t-108.5 -154.5t-115 -167.5z " />
<glyph unicode="&#xc7;" horiz-adv-x="1036" d="M104 350q0 59 18.5 172t47.5 277q27 156 73 261t112.5 167.5t154.5 89t203 26.5q100 0 183 -24.5t148 -61.5q25 -14 9 -40l-31 -54q-10 -16 -20.5 -16t-26.5 10q-63 33 -127 49.5t-135 16.5q-154 0 -232 -66q-33 -29 -57.5 -63.5t-46 -85t-38.5 -119t-34 -164.5 q-25 -137 -38 -220t-13 -136q0 -45 3 -78t10 -56.5t18.5 -41t27.5 -33.5q35 -31 86.5 -45.5t137.5 -14.5q61 0 134.5 13.5t145.5 42.5q33 14 41 -15l14 -53q8 -31 -18 -41q-84 -31 -154.5 -45t-136.5 -18q0 -2 -1 -3t-1 -4l-55 -108q16 4 36.5 5t39.5 1q53 0 88 -29.5 t35 -91.5q0 -35 -7 -65q-10 -43 -26.5 -72t-53.5 -53q-29 -20 -71.5 -28.5t-93.5 -8.5q-33 0 -81 8t-85 23q-16 6 -11 24l15 41q8 23 31 17q31 -10 67.5 -17.5t65.5 -7.5q61 0 90 16q14 8 25.5 20.5t17.5 35.5q4 12 4 20v21q0 57 -70 57q-23 0 -49.5 -3t-50.5 -7 q-14 -2 -27 10l-14 18q-12 12 0 33l72 144v4q-88 4 -154 24.5t-109 63.5t-64.5 110.5t-21.5 167.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="974" d="M78 162q0 43 9 104.5t22 130.5q16 96 32.5 184.5t35 174.5t37.5 175t42 185q27 117 94 166q35 25 81 34t110 9h438q18 0 23.5 -7t1.5 -24l-13 -59q-4 -14 -11 -22.5t-26 -8.5h-428q-59 0 -88 -20q-16 -12 -24 -33t-17 -51q-10 -37 -18 -73t-17.5 -78t-20.5 -91t-24 -113 h500q18 0 23.5 -7t1.5 -23l-13 -60q-4 -14 -11 -22t-25 -8h-500q-29 -139 -43 -224.5t-23 -140.5q-6 -49 -6 -80q2 -59 92 -59h465q18 0 22.5 -8.5t0.5 -22.5l-10 -59q-4 -16 -10.5 -23.5t-24.5 -7.5h-475q-98 0 -150.5 33t-52.5 129zM436.5 1590.5q-4.5 11.5 1.5 29.5 l23 63q12 33 51 15l387 -172q27 -12 15 -37l-21 -47q-8 -23 -41 -10l-391 139q-20 8 -24.5 19.5z" />
<glyph unicode="&#xc9;" horiz-adv-x="974" d="M78 162q0 43 9 104.5t22 130.5q16 96 32.5 184.5t35 174.5t37.5 175t42 185q27 117 94 166q35 25 81 34t110 9h438q18 0 23.5 -7t1.5 -24l-13 -59q-4 -14 -11 -22.5t-26 -8.5h-428q-59 0 -88 -20q-16 -12 -24 -33t-17 -51q-10 -37 -18 -73t-17.5 -78t-20.5 -91t-24 -113 h500q18 0 23.5 -7t1.5 -23l-13 -60q-4 -14 -11 -22t-25 -8h-500q-29 -139 -43 -224.5t-23 -140.5q-6 -49 -6 -80q2 -59 92 -59h465q18 0 22.5 -8.5t0.5 -22.5l-10 -59q-4 -16 -10.5 -23.5t-24.5 -7.5h-475q-98 0 -150.5 33t-52.5 129zM553 1485q-12 31 17 43l383 170 q39 18 51 -15l27 -71q6 -16 -1.5 -25.5t-35.5 -19.5l-381 -135q-33 -12 -43 14z" />
<glyph unicode="&#xca;" horiz-adv-x="974" d="M78 162q0 43 9 104.5t22 130.5q16 96 32.5 184.5t35 174.5t37.5 175t42 185q27 117 94 166q35 25 81 34t110 9h438q18 0 23.5 -7t1.5 -24l-13 -59q-4 -14 -11 -22.5t-26 -8.5h-428q-59 0 -88 -20q-16 -12 -24 -33t-17 -51q-10 -37 -18 -73t-17.5 -78t-20.5 -91t-24 -113 h500q18 0 23.5 -7t1.5 -23l-13 -60q-4 -14 -11 -22t-25 -8h-500q-29 -139 -43 -224.5t-23 -140.5q-6 -49 -6 -80q2 -59 92 -59h465q18 0 22.5 -8.5t0.5 -22.5l-10 -59q-4 -16 -10.5 -23.5t-24.5 -7.5h-475q-98 0 -150.5 33t-52.5 129zM445 1460q-23 14 6 39l264 225 q10 10 20.5 13.5t20.5 3.5h63q10 0 17.5 -3t15.5 -14l172 -227q16 -23 -8 -41l-41 -31q-29 -16 -45 5l-154 172h-10l-229 -177q-23 -20 -45 -4z" />
<glyph unicode="&#xcb;" horiz-adv-x="974" d="M78 162q0 43 9 104.5t22 130.5q16 96 32.5 184.5t35 174.5t37.5 175t42 185q27 117 94 166q35 25 81 34t110 9h438q18 0 23.5 -7t1.5 -24l-13 -59q-4 -14 -11 -22.5t-26 -8.5h-428q-59 0 -88 -20q-16 -12 -24 -33t-17 -51q-10 -37 -18 -73t-17.5 -78t-20.5 -91t-24 -113 h500q18 0 23.5 -7t1.5 -23l-13 -60q-4 -14 -11 -22t-25 -8h-500q-29 -139 -43 -224.5t-23 -140.5q-6 -49 -6 -80q2 -59 92 -59h465q18 0 22.5 -8.5t0.5 -22.5l-10 -59q-4 -16 -10.5 -23.5t-24.5 -7.5h-475q-98 0 -150.5 33t-52.5 129zM480 1536q0 4 1 16.5t9 53.5 q8 39 35.5 59.5t66.5 20.5h21q76 0 75 -60v-15.5t-10 -51.5q-8 -41 -36.5 -60.5t-69.5 -19.5h-21q-33 0 -52 14t-19 43zM811 1536q0 8 3 24.5t8 45.5q6 39 33.5 59.5t68.5 20.5h20q76 0 76 -60q0 -4 -2 -21.5t-8 -45.5q-8 -41 -37 -60.5t-69 -19.5h-21q-33 0 -52.5 14 t-19.5 43z" />
<glyph unicode="&#xcc;" horiz-adv-x="460" d="M59 35l244 1255q8 35 37 35h80q20 0 24.5 -12.5t-0.5 -22.5l-243 -1255q-6 -35 -37 -35h-80q-31 0 -25 35zM59.5 1590.5q-4.5 11.5 1.5 29.5l23 63q12 33 51 15l387 -172q27 -12 15 -37l-21 -47q-8 -23 -41 -10l-391 139q-20 8 -24.5 19.5z" />
<glyph unicode="&#xcd;" horiz-adv-x="460" d="M59 35l244 1255q8 35 37 35h80q20 0 24.5 -12.5t-0.5 -22.5l-243 -1255q-6 -35 -37 -35h-80q-31 0 -25 35zM276 1485q-12 31 17 43l383 170q39 18 51 -15l27 -71q6 -16 -1.5 -25.5t-35.5 -19.5l-381 -135q-33 -12 -43 14z" />
<glyph unicode="&#xce;" horiz-adv-x="460" d="M59 35l244 1255q8 35 37 35h80q20 0 24.5 -12.5t-0.5 -22.5l-243 -1255q-6 -35 -37 -35h-80q-31 0 -25 35zM105 1460q-23 14 6 39l264 225q10 10 20.5 13.5t20.5 3.5h63q10 0 17.5 -3t15.5 -14l172 -227q16 -23 -8 -41l-41 -31q-29 -16 -45 5l-154 172h-10l-229 -177 q-23 -20 -45 -4z" />
<glyph unicode="&#xcf;" horiz-adv-x="460" d="M59 35l244 1255q8 35 37 35h80q20 0 24.5 -12.5t-0.5 -22.5l-243 -1255q-6 -35 -37 -35h-80q-31 0 -25 35zM158 1536q0 4 1 16.5t9 53.5q8 39 35.5 59.5t66.5 20.5h21q76 0 75 -60v-15.5t-10 -51.5q-8 -41 -36.5 -60.5t-69.5 -19.5h-21q-33 0 -52 14t-19 43zM489 1536 q0 8 3 24.5t8 45.5q6 39 33.5 59.5t68.5 20.5h20q76 0 76 -60q0 -4 -2 -21.5t-8 -45.5q-8 -41 -37 -60.5t-69 -19.5h-21q-33 0 -52.5 14t-19.5 43z" />
<glyph unicode="&#xd0;" horiz-adv-x="1187" d="M12 653l11 58q4 14 11 22t25 8h125l103 527q4 23 13 34t34 15q49 8 128 13t150 5q260 0 386 -93t126 -316q0 -53 -11 -152.5t-44 -234.5q-39 -166 -108.5 -270.5t-168 -165t-225.5 -82t-278 -21.5h-195q-57 0 -45 51l111 572h-123q-18 0 -23.5 7t-1.5 23zM209 143 q-6 -27 20 -26h109q125 0 221 23.5t168 75.5t121 135t78 200q27 104 41 190t14 168q0 88 -21.5 148.5t-65.5 95.5t-115 50.5t-169 15.5q-57 0 -97 -1t-71 -6q-25 -2 -30 -30l-86 -441h270q18 0 23.5 -8t1.5 -22l-13 -62q-4 -20 -14 -23t-21 -3h-272z" />
<glyph unicode="&#xd1;" horiz-adv-x="1236" d="M45 35l240 1235q6 33 20 44t55 11h93q35 0 53 -11t29 -46l329 -1135h15l221 1153q8 35 37 35h69q31 0 25 -35l-238 -1231q-6 -33 -25.5 -44t-54.5 -11h-90q-37 0 -53 10t-27 43l-327 1135h-15l-225 -1153q-6 -35 -39 -35h-69q-29 0 -23 35zM486 1536q57 80 115.5 119 t113.5 39q39 0 62.5 -9.5t52.5 -31.5l51 -41q23 -18 36 -25.5t31 -7.5q25 0 53.5 22.5t71.5 69.5q20 25 41 6l35 -28q20 -14 0 -41q-55 -78 -114.5 -116t-118.5 -38q-35 0 -60.5 9.5t-50.5 29.5l-51 41q-18 16 -30.5 24.5t-28.5 8.5q-31 0 -57.5 -18.5t-72.5 -71.5 q-20 -25 -45 -7l-32 25q-20 14 -2 41z" />
<glyph unicode="&#xd2;" horiz-adv-x="1200" d="M104 350q0 96 16.5 192.5t33.5 174.5q20 98 41.5 177t48 141.5t62.5 112.5t83 91q123 104 344 104q90 0 165 -18t128 -61t82 -114t29 -175q0 -96 -17 -192.5t-33 -174.5q-37 -172 -86 -291.5t-117.5 -194.5t-160.5 -107.5t-215 -32.5q-96 0 -171 17t-126 60t-79 114 t-28 177zM254 373q0 -70 10 -121t40 -84t80 -49.5t130 -16.5q92 0 159.5 32t118 96.5t86 165t66.5 239.5q27 115 35 186.5t8 130.5q0 135 -55 203t-205 68q-86 0 -141.5 -20.5t-102.5 -61.5q-39 -35 -65.5 -79t-48 -99.5t-38 -123t-34.5 -149.5q-23 -115 -33 -186.5 t-10 -130.5zM516.5 1590.5q-4.5 11.5 1.5 29.5l23 63q12 33 51 15l387 -172q27 -12 15 -37l-21 -47q-8 -23 -41 -10l-391 139q-20 8 -24.5 19.5z" />
<glyph unicode="&#xd3;" horiz-adv-x="1200" d="M104 350q0 96 16.5 192.5t33.5 174.5q20 98 41.5 177t48 141.5t62.5 112.5t83 91q123 104 344 104q90 0 165 -18t128 -61t82 -114t29 -175q0 -96 -17 -192.5t-33 -174.5q-37 -172 -86 -291.5t-117.5 -194.5t-160.5 -107.5t-215 -32.5q-96 0 -171 17t-126 60t-79 114 t-28 177zM254 373q0 -70 10 -121t40 -84t80 -49.5t130 -16.5q92 0 159.5 32t118 96.5t86 165t66.5 239.5q27 115 35 186.5t8 130.5q0 135 -55 203t-205 68q-86 0 -141.5 -20.5t-102.5 -61.5q-39 -35 -65.5 -79t-48 -99.5t-38 -123t-34.5 -149.5q-23 -115 -33 -186.5 t-10 -130.5zM639 1485q-12 31 17 43l383 170q39 18 51 -15l27 -71q6 -16 -1.5 -25.5t-35.5 -19.5l-381 -135q-33 -12 -43 14z" />
<glyph unicode="&#xd4;" horiz-adv-x="1200" d="M104 350q0 96 16.5 192.5t33.5 174.5q20 98 41.5 177t48 141.5t62.5 112.5t83 91q123 104 344 104q90 0 165 -18t128 -61t82 -114t29 -175q0 -96 -17 -192.5t-33 -174.5q-37 -172 -86 -291.5t-117.5 -194.5t-160.5 -107.5t-215 -32.5q-96 0 -171 17t-126 60t-79 114 t-28 177zM254 373q0 -70 10 -121t40 -84t80 -49.5t130 -16.5q92 0 159.5 32t118 96.5t86 165t66.5 239.5q27 115 35 186.5t8 130.5q0 135 -55 203t-205 68q-86 0 -141.5 -20.5t-102.5 -61.5q-39 -35 -65.5 -79t-48 -99.5t-38 -123t-34.5 -149.5q-23 -115 -33 -186.5 t-10 -130.5zM514 1460q-23 14 6 39l264 225q10 10 20.5 13.5t20.5 3.5h63q10 0 17.5 -3t15.5 -14l172 -227q16 -23 -8 -41l-41 -31q-29 -16 -45 5l-154 172h-10l-229 -177q-23 -20 -45 -4z" />
<glyph unicode="&#xd5;" horiz-adv-x="1200" d="M104 350q0 96 16.5 192.5t33.5 174.5q20 98 41.5 177t48 141.5t62.5 112.5t83 91q123 104 344 104q90 0 165 -18t128 -61t82 -114t29 -175q0 -96 -17 -192.5t-33 -174.5q-37 -172 -86 -291.5t-117.5 -194.5t-160.5 -107.5t-215 -32.5q-96 0 -171 17t-126 60t-79 114 t-28 177zM254 373q0 -70 10 -121t40 -84t80 -49.5t130 -16.5q92 0 159.5 32t118 96.5t86 165t66.5 239.5q27 115 35 186.5t8 130.5q0 135 -55 203t-205 68q-86 0 -141.5 -20.5t-102.5 -61.5q-39 -35 -65.5 -79t-48 -99.5t-38 -123t-34.5 -149.5q-23 -115 -33 -186.5 t-10 -130.5zM496 1536q57 80 115.5 119t113.5 39q39 0 62.5 -9.5t52.5 -31.5l51 -41q23 -18 36 -25.5t31 -7.5q25 0 53.5 22.5t71.5 69.5q20 25 41 6l35 -28q20 -14 0 -41q-55 -78 -114.5 -116t-118.5 -38q-35 0 -60.5 9.5t-50.5 29.5l-51 41q-18 16 -30.5 24.5t-28.5 8.5 q-31 0 -57.5 -18.5t-72.5 -71.5q-20 -25 -45 -7l-32 25q-20 14 -2 41z" />
<glyph unicode="&#xd6;" horiz-adv-x="1200" d="M104 350q0 96 16.5 192.5t33.5 174.5q20 98 41.5 177t48 141.5t62.5 112.5t83 91q123 104 344 104q90 0 165 -18t128 -61t82 -114t29 -175q0 -96 -17 -192.5t-33 -174.5q-37 -172 -86 -291.5t-117.5 -194.5t-160.5 -107.5t-215 -32.5q-96 0 -171 17t-126 60t-79 114 t-28 177zM254 373q0 -70 10 -121t40 -84t80 -49.5t130 -16.5q92 0 159.5 32t118 96.5t86 165t66.5 239.5q27 115 35 186.5t8 130.5q0 135 -55 203t-205 68q-86 0 -141.5 -20.5t-102.5 -61.5q-39 -35 -65.5 -79t-48 -99.5t-38 -123t-34.5 -149.5q-23 -115 -33 -186.5 t-10 -130.5zM551 1536q0 4 1 16.5t9 53.5q8 39 35.5 59.5t66.5 20.5h21q76 0 75 -60v-15.5t-10 -51.5q-8 -41 -36.5 -60.5t-69.5 -19.5h-21q-33 0 -52 14t-19 43zM882 1536q0 8 3 24.5t8 45.5q6 39 33.5 59.5t68.5 20.5h20q76 0 76 -60q0 -4 -2 -21.5t-8 -45.5 q-8 -41 -37 -60.5t-69 -19.5h-21q-33 0 -52.5 14t-19.5 43z" />
<glyph unicode="&#xd7;" d="M109 219q-16 23 4 39l325 274l-213 261q-18 23 11 45l59 49q27 20 43 0l211 -262l315 264q25 18 43 -4l41 -47q18 -18 -4 -39l-317 -267l217 -264q10 -12 11 -21t-13 -24l-62 -53q-16 -14 -24 -14t-17 10l-225 272l-321 -270q-27 -23 -46 2z" />
<glyph unicode="&#xd8;" horiz-adv-x="1200" d="M84 -102l111 174q-90 86 -91 278q0 96 16.5 192.5t33.5 174.5q20 98 41.5 177t48 141.5t62.5 112.5t83 91q123 104 344 104q143 0 236 -45l114 179q14 23 37 10l41 -23q20 -12 8 -32l-120 -191q43 -45 65.5 -109.5t22.5 -156.5q0 -96 -17 -192.5t-33 -174.5 q-37 -172 -86 -291.5t-117.5 -194.5t-160.5 -107.5t-215 -32.5q-141 0 -232 38l-106 -167q-10 -14 -17.5 -15.5t-19.5 4.5l-41 23q-20 12 -8 33zM254 373q0 -53 5 -94t20 -74l618 979q-57 39 -170 39q-86 0 -141.5 -20.5t-102.5 -61.5q-39 -35 -66.5 -79t-48 -99.5t-38 -123 t-33.5 -149.5q-25 -115 -34 -186.5t-9 -130.5zM348 133q57 -31 166 -31q92 0 159.5 32t118 96.5t86 165t66.5 239.5q27 115 35 186.5t8 130.5q0 90 -24 152z" />
<glyph unicode="&#xd9;" horiz-adv-x="1198" d="M109 293q0 31 3 65.5t11 71.5l166 860q10 35 37 35h80q35 0 24 -35l-164 -846q-8 -43 -12 -77.5t-4 -63.5q0 -111 72.5 -156t193.5 -45q86 0 145.5 16.5t106.5 57.5q23 20 41 44t32.5 54.5t27 71.5t22.5 98l164 846q10 35 37 35h79q35 0 25 -35l-166 -860 q-25 -127 -63.5 -212t-102.5 -138q-63 -53 -153 -75.5t-215 -22.5q-78 0 -149 15t-124 51t-83.5 95.5t-30.5 149.5zM475.5 1590.5q-4.5 11.5 1.5 29.5l23 63q12 33 51 15l387 -172q27 -12 15 -37l-21 -47q-8 -23 -41 -10l-391 139q-20 8 -24.5 19.5z" />
<glyph unicode="&#xda;" horiz-adv-x="1198" d="M109 293q0 31 3 65.5t11 71.5l166 860q10 35 37 35h80q35 0 24 -35l-164 -846q-8 -43 -12 -77.5t-4 -63.5q0 -111 72.5 -156t193.5 -45q86 0 145.5 16.5t106.5 57.5q23 20 41 44t32.5 54.5t27 71.5t22.5 98l164 846q10 35 37 35h79q35 0 25 -35l-166 -860 q-25 -127 -63.5 -212t-102.5 -138q-63 -53 -153 -75.5t-215 -22.5q-78 0 -149 15t-124 51t-83.5 95.5t-30.5 149.5zM571 1485q-12 31 17 43l383 170q39 18 51 -15l27 -71q6 -16 -1.5 -25.5t-35.5 -19.5l-381 -135q-33 -12 -43 14z" />
<glyph unicode="&#xdb;" horiz-adv-x="1198" d="M109 293q0 31 3 65.5t11 71.5l166 860q10 35 37 35h80q35 0 24 -35l-164 -846q-8 -43 -12 -77.5t-4 -63.5q0 -111 72.5 -156t193.5 -45q86 0 145.5 16.5t106.5 57.5q23 20 41 44t32.5 54.5t27 71.5t22.5 98l164 846q10 35 37 35h79q35 0 25 -35l-166 -860 q-25 -127 -63.5 -212t-102.5 -138q-63 -53 -153 -75.5t-215 -22.5q-78 0 -149 15t-124 51t-83.5 95.5t-30.5 149.5zM482 1460q-23 14 6 39l264 225q10 10 20.5 13.5t20.5 3.5h63q10 0 17.5 -3t15.5 -14l172 -227q16 -23 -8 -41l-41 -31q-29 -16 -45 5l-154 172h-10 l-229 -177q-23 -20 -45 -4z" />
<glyph unicode="&#xdc;" horiz-adv-x="1198" d="M109 293q0 31 3 65.5t11 71.5l166 860q10 35 37 35h80q35 0 24 -35l-164 -846q-8 -43 -12 -77.5t-4 -63.5q0 -111 72.5 -156t193.5 -45q86 0 145.5 16.5t106.5 57.5q23 20 41 44t32.5 54.5t27 71.5t22.5 98l164 846q10 35 37 35h79q35 0 25 -35l-166 -860 q-25 -127 -63.5 -212t-102.5 -138q-63 -53 -153 -75.5t-215 -22.5q-78 0 -149 15t-124 51t-83.5 95.5t-30.5 149.5zM519 1536q0 4 1 16.5t9 53.5q8 39 35.5 59.5t66.5 20.5h21q76 0 75 -60v-15.5t-10 -51.5q-8 -41 -36.5 -60.5t-69.5 -19.5h-21q-33 0 -52 14t-19 43z M850 1536q0 8 3 24.5t8 45.5q6 39 33.5 59.5t68.5 20.5h20q76 0 76 -60q0 -4 -2 -21.5t-8 -45.5q-8 -41 -37 -60.5t-69 -19.5h-21q-33 0 -52.5 14t-19.5 43z" />
<glyph unicode="&#xdd;" horiz-adv-x="927" d="M168 1300q-2 25 27 25h90q29 0 30 -20q18 -201 50 -372t90 -337h14q121 174 227.5 345t202.5 364q10 20 37 20h90q12 0 19.5 -5t1.5 -20q-53 -113 -111.5 -217t-123 -207.5t-139.5 -209t-163 -220.5l-80 -411q-6 -35 -37 -35h-80q-31 0 -24 35l80 416 q-80 205 -131.5 412.5t-69.5 436.5zM440 1485q-12 31 17 43l383 170q39 18 51 -15l27 -71q6 -16 -1.5 -25.5t-35.5 -19.5l-381 -135q-33 -12 -43 14z" />
<glyph unicode="&#xde;" horiz-adv-x="1028" d="M45 35l248 1274q6 35 39 34h80q29 0 22 -34l-39 -187q41 6 89 10.5t89 4.5q88 0 166 -14.5t135.5 -48.5t90 -90t32.5 -142q0 -35 -5 -86t-19 -105q-51 -190 -187.5 -270t-351.5 -80h-196l-52 -266q-6 -35 -36 -35h-80q-31 0 -25 35zM260 424q47 -2 86 -2h84 q176 0 269.5 59.5t129.5 196.5q12 43 16.5 80t4.5 61q0 59 -19.5 97t-57.5 60.5t-94 31t-130 8.5q-43 0 -88 -5t-90 -14z" />
<glyph unicode="&#xdf;" horiz-adv-x="1042" d="M-10 -102q29 70 54.5 159.5t47.5 196.5l156 790q23 119 57.5 183.5t93.5 105.5q57 39 125 55.5t158 16.5q70 0 133.5 -17.5t104.5 -52.5q39 -33 59 -81t20 -119q0 -27 -4 -57.5t-8 -57.5q-18 -104 -77.5 -175t-168.5 -104v-6q98 -16 152.5 -77.5t54.5 -161.5 q0 -59 -10 -120t-25 -106q-47 -154 -164.5 -221t-263.5 -67q-66 0 -107.5 4t-82.5 14q-20 4 -24.5 12t-0.5 25l17 57q6 16 15 19.5t22 -0.5q70 -16 157 -17q96 0 173 42t112 141q16 43 26.5 93t10.5 93q0 51 -9.5 83t-31.5 58q-29 35 -83 50.5t-126 15.5h-64q-33 0 -26 26 l12 62q6 29 35 28h22q72 0 124.5 16.5t93.5 47.5q47 35 67.5 82t32.5 100q4 20 7 48t3 40q0 90 -48 129t-146 39q-61 0 -110.5 -16.5t-78.5 -38.5q-37 -29 -61.5 -79t-42.5 -142l-144 -740q-20 -104 -48.5 -205.5t-57.5 -174.5q-6 -16 -15.5 -20.5t-29.5 -4.5h-68 q-31 0 -20 29z" />
<glyph unicode="&#xe0;" horiz-adv-x="956" d="M53 184q0 49 7.5 95.5t29.5 107.5q18 51 47 90t73 65.5t107.5 40t151.5 13.5h88q35 0 75 -2t75 -6l16 78q6 29 11 61.5t5 54.5q0 35 -8 58.5t-29.5 39t-59.5 21.5t-97 6q-68 0 -132.5 -11t-117.5 -28q-39 -12 -45 23l-8 51q-2 27 26 35q66 18 149 31.5t148 13.5 q145 0 226 -47t81 -164q0 -23 -5 -58.5t-11 -64.5l-129 -668q-2 -20 -25 -20h-67q-25 0 -21 25l9 73h-5q-47 -66 -112.5 -91t-151.5 -25h-51q-250 0 -250 202zM186 197q0 -102 140 -103h43q111 0 184.5 55.5t101.5 180.5l31 145q-37 4 -71.5 6t-75.5 2h-99q-51 0 -88 -7 t-64.5 -25.5t-46 -48t-32.5 -74.5q-12 -39 -17.5 -71t-5.5 -60zM327.5 1356.5q-4.5 11.5 1.5 29.5l23 64q12 33 51 14l387 -172q27 -12 15 -37l-21 -47q-8 -23 -41 -10l-391 139q-20 8 -24.5 19.5z" />
<glyph unicode="&#xe1;" horiz-adv-x="956" d="M53 184q0 49 7.5 95.5t29.5 107.5q18 51 47 90t73 65.5t107.5 40t151.5 13.5h88q35 0 75 -2t75 -6l16 78q6 29 11 61.5t5 54.5q0 35 -8 58.5t-29.5 39t-59.5 21.5t-97 6q-68 0 -132.5 -11t-117.5 -28q-39 -12 -45 23l-8 51q-2 27 26 35q66 18 149 31.5t148 13.5 q145 0 226 -47t81 -164q0 -23 -5 -58.5t-11 -64.5l-129 -668q-2 -20 -25 -20h-67q-25 0 -21 25l9 73h-5q-47 -66 -112.5 -91t-151.5 -25h-51q-250 0 -250 202zM186 197q0 -102 140 -103h43q111 0 184.5 55.5t101.5 180.5l31 145q-37 4 -71.5 6t-75.5 2h-99q-51 0 -88 -7 t-64.5 -25.5t-46 -48t-32.5 -74.5q-12 -39 -17.5 -71t-5.5 -60zM426 1251q-12 31 17 43l383 170q39 18 51 -14l26 -72q6 -16 -1 -25.5t-35 -19.5l-381 -135q-33 -12 -43 14z" />
<glyph unicode="&#xe2;" horiz-adv-x="956" d="M53 184q0 49 7.5 95.5t29.5 107.5q18 51 47 90t73 65.5t107.5 40t151.5 13.5h88q35 0 75 -2t75 -6l16 78q6 29 11 61.5t5 54.5q0 35 -8 58.5t-29.5 39t-59.5 21.5t-97 6q-68 0 -132.5 -11t-117.5 -28q-39 -12 -45 23l-8 51q-2 27 26 35q66 18 149 31.5t148 13.5 q145 0 226 -47t81 -164q0 -23 -5 -58.5t-11 -64.5l-129 -668q-2 -20 -25 -20h-67q-25 0 -21 25l9 73h-5q-47 -66 -112.5 -91t-151.5 -25h-51q-250 0 -250 202zM186 197q0 -102 140 -103h43q111 0 184.5 55.5t101.5 180.5l31 145q-37 4 -71.5 6t-75.5 2h-99q-51 0 -88 -7 t-64.5 -25.5t-46 -48t-32.5 -74.5q-12 -39 -17.5 -71t-5.5 -60zM340 1214q-23 14 7 39l264 242q10 10 20 13t21 3h63q10 0 17.5 -3t15.5 -13l172 -244q16 -23 -8 -41l-41 -30q-29 -16 -45 4l-154 188h-10l-229 -192q-23 -20 -46 -4z" />
<glyph unicode="&#xe3;" horiz-adv-x="956" d="M53 184q0 49 7.5 95.5t29.5 107.5q18 51 47 90t73 65.5t107.5 40t151.5 13.5h88q35 0 75 -2t75 -6l16 78q6 29 11 61.5t5 54.5q0 35 -8 58.5t-29.5 39t-59.5 21.5t-97 6q-68 0 -132.5 -11t-117.5 -28q-39 -12 -45 23l-8 51q-2 27 26 35q66 18 149 31.5t148 13.5 q145 0 226 -47t81 -164q0 -23 -5 -58.5t-11 -64.5l-129 -668q-2 -20 -25 -20h-67q-25 0 -21 25l9 73h-5q-47 -66 -112.5 -91t-151.5 -25h-51q-250 0 -250 202zM186 197q0 -102 140 -103h43q111 0 184.5 55.5t101.5 180.5l31 145q-37 4 -71.5 6t-75.5 2h-99q-51 0 -88 -7 t-64.5 -25.5t-46 -48t-32.5 -74.5q-12 -39 -17.5 -71t-5.5 -60zM319 1288q57 80 116 119t114 39q39 0 62.5 -9.5t52.5 -31.5l51 -41q23 -18 36 -25.5t31 -7.5q25 0 53.5 22.5t71.5 69.5q20 25 41 7l35 -29q20 -14 0 -41q-55 -78 -114.5 -116t-118.5 -38q-66 0 -111 39 l-51 41q-16 16 -29.5 24.5t-30.5 8.5q-31 0 -57.5 -18.5t-71.5 -71.5q-20 -25 -45 -6l-32 24q-20 14 -3 41z" />
<glyph unicode="&#xe4;" horiz-adv-x="956" d="M53 184q0 49 7.5 95.5t29.5 107.5q18 51 47 90t73 65.5t107.5 40t151.5 13.5h88q35 0 75 -2t75 -6l16 78q6 29 11 61.5t5 54.5q0 35 -8 58.5t-29.5 39t-59.5 21.5t-97 6q-68 0 -132.5 -11t-117.5 -28q-39 -12 -45 23l-8 51q-2 27 26 35q66 18 149 31.5t148 13.5 q145 0 226 -47t81 -164q0 -23 -5 -58.5t-11 -64.5l-129 -668q-2 -20 -25 -20h-67q-25 0 -21 25l9 73h-5q-47 -66 -112.5 -91t-151.5 -25h-51q-250 0 -250 202zM186 197q0 -102 140 -103h43q111 0 184.5 55.5t101.5 180.5l31 145q-37 4 -71.5 6t-75.5 2h-99q-51 0 -88 -7 t-64.5 -25.5t-46 -48t-32.5 -74.5q-12 -39 -17.5 -71t-5.5 -60zM387 1286q0 4 1 16.5t10 53.5q8 39 35.5 59.5t66.5 20.5h20q76 0 76 -60v-15t-10 -52q-8 -41 -37 -60.5t-70 -19.5h-20q-33 0 -52.5 14t-19.5 43zM719 1286q0 8 3 24.5t7 45.5q6 39 34 59.5t69 20.5h20 q76 0 76 -60q0 -4 -2 -21.5t-8 -45.5q-8 -41 -37 -60.5t-70 -19.5h-20q-33 0 -52.5 14t-19.5 43z" />
<glyph unicode="&#xe5;" horiz-adv-x="956" d="M53 184q0 49 7.5 95.5t29.5 107.5q18 51 47 90t73 65.5t107.5 40t151.5 13.5h88q35 0 75 -2t75 -6l16 78q6 29 11 61.5t5 54.5q0 35 -8 58.5t-29.5 39t-59.5 21.5t-97 6q-68 0 -132.5 -11t-117.5 -28q-39 -12 -45 23l-8 51q-2 27 26 35q66 18 149 31.5t148 13.5 q145 0 226 -47t81 -164q0 -23 -5 -58.5t-11 -64.5l-129 -668q-2 -20 -25 -20h-67q-25 0 -21 25l9 73h-5q-47 -66 -112.5 -91t-151.5 -25h-51q-250 0 -250 202zM186 197q0 -102 140 -103h43q111 0 184.5 55.5t101.5 180.5l31 145q-37 4 -71.5 6t-75.5 2h-99q-51 0 -88 -7 t-64.5 -25.5t-46 -48t-32.5 -74.5q-12 -39 -17.5 -71t-5.5 -60zM456 1296q0 47 17.5 87t48.5 69t71 44t87 15q72 0 114.5 -43t42.5 -108q0 -100 -64.5 -160.5t-156.5 -60.5q-74 0 -117 42.5t-43 114.5zM555 1305q0 -31 19.5 -50.5t54.5 -19.5q47 0 78.5 31.5t31.5 74.5 q0 33 -20.5 53.5t-55.5 20.5q-47 0 -77.5 -31.5t-30.5 -78.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1509" d="M53 184q0 49 9.5 93.5t25.5 99.5q33 104 118 156.5t261 52.5h88q35 0 75 -2t75 -6l20 94q6 29 10 58.5t4 51.5q0 35 -8 58.5t-29.5 39t-59.5 21.5t-97 6q-68 0 -132.5 -11t-117.5 -28q-39 -12 -45 23l-8 51q-2 27 26 35q66 18 149 31.5t148 13.5q100 0 176 -27.5 t99 -99.5q45 66 115.5 96.5t185.5 30.5q143 0 223 -63.5t80 -206.5q0 -23 -3 -48.5t-7 -54.5l-31 -174q-6 -41 -60 -41l-528 2q-8 -41 -13 -82t-5 -73q0 -55 11 -91t36.5 -57.5t69.5 -30t112 -8.5q59 0 126 12.5t128 34.5q35 12 41 -16l10 -47q4 -14 -2 -22.5t-31 -18.5 q-76 -29 -151.5 -42t-142.5 -13q-147 0 -216 39.5t-96 111.5q-51 -80 -139 -115.5t-199 -35.5h-51q-250 0 -250 202zM186 188q0 -94 140 -94h43q111 0 184.5 50.5t101.5 185.5l29 135q-31 4 -68.5 6t-78.5 2h-99q-102 0 -154.5 -32.5t-76.5 -123.5q-10 -39 -15.5 -66.5 t-5.5 -62.5zM836 543l415 -2q27 0 33 22l16 90q10 57 11 97q0 86 -42.5 122.5t-146.5 36.5q-53 0 -95 -10t-73 -37q-37 -33 -60.5 -87t-43.5 -157z" />
<glyph unicode="&#xe7;" horiz-adv-x="856" d="M78 270q0 59 14 146.5t35 185.5q47 231 164 325.5t291 94.5q72 0 144.5 -21.5t123.5 -54.5q20 -12 4 -39l-27 -45q-16 -27 -39 -14q-53 29 -103 45t-110 16q-63 0 -114 -17.5t-91 -57t-69 -107.5t-49 -166q-16 -84 -28.5 -161.5t-12.5 -118.5q0 -53 10 -89t36 -57.5 t68 -31t105 -9.5q53 0 113.5 12.5t113.5 32.5q29 10 35 -18l13 -51q4 -14 -0.5 -21.5t-20.5 -13.5q-43 -16 -112.5 -32.5t-137.5 -20.5l-2 -5l-55 -108q16 4 36.5 5t39.5 1q53 0 87.5 -29.5t34.5 -91.5q0 -35 -6 -65q-10 -43 -26.5 -72t-53.5 -53q-29 -20 -71.5 -28.5 t-93.5 -8.5q-33 0 -81 8t-85 23q-16 6 -11 24l15 41q8 23 31 17q31 -10 67.5 -17.5t65.5 -7.5q61 0 90 16q14 8 25.5 20.5t17.5 35.5q4 12 4 20v21q0 57 -70 57q-23 0 -49.5 -3t-50.5 -7q-14 -2 -27 10l-14 18q-12 12 0 33l72 144q0 2 1 3t1 3q-129 14 -188.5 81.5 t-59.5 202.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="921" d="M78 258q0 66 11 146.5t26 158.5q27 145 59.5 233.5t89.5 141.5q51 47 121 65.5t168 18.5q143 0 223 -63.5t80 -206.5q0 -23 -3 -48.5t-7 -54.5l-33 -182q-6 -41 -59 -41h-529q-8 -41 -12 -77t-4 -68q0 -55 11 -91t37 -57.5t70 -30t111 -8.5q59 0 126 12.5t128 34.5 q35 12 41 -16l10 -47q4 -14 -2 -22.5t-30 -18.5q-76 -29 -152 -42t-143 -13q-100 0 -166 19t-103.5 55t-53 87.5t-15.5 114.5zM246 532h416q27 0 32 23l17 86q6 29 9 57.5t3 51.5q0 86 -42 122.5t-146 36.5q-53 0 -95.5 -9t-72.5 -36q-37 -33 -60.5 -88t-44.5 -158z M313.5 1356.5q-4.5 11.5 1.5 29.5l23 64q12 33 51 14l387 -172q27 -12 15 -37l-21 -47q-8 -23 -41 -10l-391 139q-20 8 -24.5 19.5z" />
<glyph unicode="&#xe9;" horiz-adv-x="921" d="M78 258q0 66 11 146.5t26 158.5q27 145 59.5 233.5t89.5 141.5q51 47 121 65.5t168 18.5q143 0 223 -63.5t80 -206.5q0 -23 -3 -48.5t-7 -54.5l-33 -182q-6 -41 -59 -41h-529q-8 -41 -12 -77t-4 -68q0 -55 11 -91t37 -57.5t70 -30t111 -8.5q59 0 126 12.5t128 34.5 q35 12 41 -16l10 -47q4 -14 -2 -22.5t-30 -18.5q-76 -29 -152 -42t-143 -13q-100 0 -166 19t-103.5 55t-53 87.5t-15.5 114.5zM246 532h416q27 0 32 23l17 86q6 29 9 57.5t3 51.5q0 86 -42 122.5t-146 36.5q-53 0 -95.5 -9t-72.5 -36q-37 -33 -60.5 -88t-44.5 -158z M424 1251q-12 31 17 43l383 170q39 18 51 -14l26 -72q6 -16 -1 -25.5t-35 -19.5l-381 -135q-33 -12 -43 14z" />
<glyph unicode="&#xea;" horiz-adv-x="921" d="M78 258q0 66 11 146.5t26 158.5q27 145 59.5 233.5t89.5 141.5q51 47 121 65.5t168 18.5q143 0 223 -63.5t80 -206.5q0 -23 -3 -48.5t-7 -54.5l-33 -182q-6 -41 -59 -41h-529q-8 -41 -12 -77t-4 -68q0 -55 11 -91t37 -57.5t70 -30t111 -8.5q59 0 126 12.5t128 34.5 q35 12 41 -16l10 -47q4 -14 -2 -22.5t-30 -18.5q-76 -29 -152 -42t-143 -13q-100 0 -166 19t-103.5 55t-53 87.5t-15.5 114.5zM246 532h416q27 0 32 23l17 86q6 29 9 57.5t3 51.5q0 86 -42 122.5t-146 36.5q-53 0 -95.5 -9t-72.5 -36q-37 -33 -60.5 -88t-44.5 -158z M299 1214q-23 14 7 39l264 242q10 10 20 13t21 3h63q10 0 17.5 -3t15.5 -13l172 -244q16 -23 -8 -41l-41 -30q-29 -16 -45 4l-154 188h-10l-229 -192q-23 -20 -46 -4z" />
<glyph unicode="&#xeb;" horiz-adv-x="921" d="M78 258q0 66 11 146.5t26 158.5q27 145 59.5 233.5t89.5 141.5q51 47 121 65.5t168 18.5q143 0 223 -63.5t80 -206.5q0 -23 -3 -48.5t-7 -54.5l-33 -182q-6 -41 -59 -41h-529q-8 -41 -12 -77t-4 -68q0 -55 11 -91t37 -57.5t70 -30t111 -8.5q59 0 126 12.5t128 34.5 q35 12 41 -16l10 -47q4 -14 -2 -22.5t-30 -18.5q-76 -29 -152 -42t-143 -13q-100 0 -166 19t-103.5 55t-53 87.5t-15.5 114.5zM246 532h416q27 0 32 23l17 86q6 29 9 57.5t3 51.5q0 86 -42 122.5t-146 36.5q-53 0 -95.5 -9t-72.5 -36q-37 -33 -60.5 -88t-44.5 -158z M354 1286q0 4 1 16.5t10 53.5q8 39 35.5 59.5t66.5 20.5h20q76 0 76 -60v-15t-10 -52q-8 -41 -37 -60.5t-70 -19.5h-20q-33 0 -52.5 14t-19.5 43zM686 1286q0 8 3 24.5t7 45.5q6 39 34 59.5t69 20.5h20q76 0 76 -60q0 -4 -2 -21.5t-8 -45.5q-8 -41 -37 -60.5t-70 -19.5h-20 q-33 0 -52.5 14t-19.5 43z" />
<glyph unicode="&#xec;" horiz-adv-x="462" d="M30.5 1356.5q-4.5 11.5 1.5 29.5l23 64q12 33 51 14l387 -172q27 -12 15 -37l-21 -47q-8 -23 -41 -10l-391 139q-20 8 -24.5 19.5zM78 16l186 965q6 23 29 23h84q25 0 20 -23l-186 -965q-4 -16 -21 -16h-98q-16 0 -14 16z" />
<glyph unicode="&#xed;" horiz-adv-x="462" d="M78 16l186 965q6 23 29 23h84q25 0 20 -23l-186 -965q-4 -16 -21 -16h-98q-16 0 -14 16zM250 1251q-12 31 17 43l383 170q39 18 51 -14l26 -72q6 -16 -1 -25.5t-35 -19.5l-381 -135q-33 -12 -43 14z" />
<glyph unicode="&#xee;" horiz-adv-x="462" d="M78 16l186 965q6 23 29 23h84q25 0 20 -23l-186 -965q-4 -16 -21 -16h-98q-16 0 -14 16zM82 1214q-23 14 7 39l264 242q10 10 20 13t21 3h63q10 0 17.5 -3t15.5 -13l172 -244q16 -23 -8 -41l-41 -30q-29 -16 -45 4l-154 188h-10l-229 -192q-23 -20 -46 -4z" />
<glyph unicode="&#xef;" horiz-adv-x="462" d="M78 16l186 965q6 23 29 23h84q25 0 20 -23l-186 -965q-4 -16 -21 -16h-98q-16 0 -14 16zM116 1286q0 4 1 16.5t10 53.5q8 39 35.5 59.5t66.5 20.5h20q76 0 76 -60v-15t-10 -52q-8 -41 -37 -60.5t-70 -19.5h-20q-33 0 -52.5 14t-19.5 43zM448 1286q0 8 3 24.5t7 45.5 q6 39 34 59.5t69 20.5h20q76 0 76 -60q0 -4 -2 -21.5t-8 -45.5q-8 -41 -37 -60.5t-70 -19.5h-20q-33 0 -52.5 14t-19.5 43z" />
<glyph unicode="&#xf0;" horiz-adv-x="985" d="M76 254q0 16 3 51t9 79t13.5 93t13.5 94q16 106 47 178t78 116t111.5 62.5t148.5 18.5q88 0 147.5 -29.5t87.5 -70.5l4 4q-31 240 -166 356l-211 -116q-12 -6 -19 -5.5t-17 17.5l-15 24q-10 16 -6 25.5t17 17.5l178 101q-29 23 -61.5 41t-63.5 32q-10 4 -13.5 16.5 t7.5 26.5l30 41q16 23 41 11q47 -23 89 -46.5t83 -56.5l158 88q27 14 41 -10l16 -27q14 -23 -14 -40l-131 -76q76 -76 119 -168t59 -199q16 -111 10 -229.5t-28 -245.5q-23 -119 -51.5 -204t-78.5 -139t-129 -79.5t-200 -25.5q-170 0 -238.5 70.5t-68.5 203.5zM211 281 q0 -45 8 -80t29.5 -57.5t58.5 -35t94 -12.5q68 0 115 11.5t81 46t57 97.5t46 165q16 78 20.5 120t4.5 68q0 111 -54.5 168t-158.5 57q-76 0 -123 -19t-75.5 -56t-44 -91.5t-25.5 -125.5q-10 -72 -21.5 -143.5t-11.5 -112.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="1017" d="M51 31l182 952q2 20 27 21h62q25 0 22 -21l-10 -88h4q20 25 45 45.5t61 40.5q39 20 84 30.5t123 10.5q98 0 155.5 -23.5t88.5 -72.5t34 -120t-20 -185l-112 -592q-2 -10 -8.5 -19.5t-22.5 -9.5h-72q-23 0 -29 6t-1 29l114 598q18 94 18.5 145t-22.5 82q-20 29 -56 40 t-102 11q-70 0 -116.5 -15t-87.5 -50t-70 -88t-45 -142l-113 -585q-4 -14 -10 -22.5t-24 -8.5h-70q-35 0 -29 31zM362 1288q57 80 116 119t114 39q39 0 62.5 -9.5t52.5 -31.5l51 -41q23 -18 36 -25.5t31 -7.5q25 0 53.5 22.5t71.5 69.5q20 25 41 7l35 -29q20 -14 0 -41 q-55 -78 -114.5 -116t-118.5 -38q-66 0 -111 39l-51 41q-16 16 -29.5 24.5t-30.5 8.5q-31 0 -57.5 -18.5t-71.5 -71.5q-20 -25 -45 -6l-32 24q-20 14 -3 41z" />
<glyph unicode="&#xf2;" horiz-adv-x="980" d="M78 268q0 51 10 127t23 142q27 131 59.5 223t85.5 150.5t132 85t196 26.5q176 0 248.5 -71.5t72.5 -215.5q0 -51 -10 -127t-23 -141q-27 -131 -59.5 -223t-85.5 -150.5t-132 -85t-196 -26.5q-160 0 -240.5 67.5t-80.5 218.5zM213 272q0 -100 50.5 -139t154.5 -39 q86 0 140 29t90 84t57.5 135t39.5 182q10 55 17.5 110.5t7.5 96.5q0 96 -48 137t-157 41q-86 0 -140 -28.5t-90 -84t-57.5 -135t-39.5 -182.5q-10 -55 -17.5 -110.5t-7.5 -96.5zM338.5 1356.5q-4.5 11.5 1.5 29.5l23 64q12 33 51 14l387 -172q27 -12 15 -37l-21 -47 q-8 -23 -41 -10l-391 139q-20 8 -24.5 19.5z" />
<glyph unicode="&#xf3;" horiz-adv-x="980" d="M78 268q0 51 10 127t23 142q27 131 59.5 223t85.5 150.5t132 85t196 26.5q176 0 248.5 -71.5t72.5 -215.5q0 -51 -10 -127t-23 -141q-27 -131 -59.5 -223t-85.5 -150.5t-132 -85t-196 -26.5q-160 0 -240.5 67.5t-80.5 218.5zM213 272q0 -100 50.5 -139t154.5 -39 q86 0 140 29t90 84t57.5 135t39.5 182q10 55 17.5 110.5t7.5 96.5q0 96 -48 137t-157 41q-86 0 -140 -28.5t-90 -84t-57.5 -135t-39.5 -182.5q-10 -55 -17.5 -110.5t-7.5 -96.5zM467 1251q-12 31 17 43l383 170q39 18 51 -14l26 -72q6 -16 -1 -25.5t-35 -19.5l-381 -135 q-33 -12 -43 14z" />
<glyph unicode="&#xf4;" horiz-adv-x="980" d="M78 268q0 51 10 127t23 142q27 131 59.5 223t85.5 150.5t132 85t196 26.5q176 0 248.5 -71.5t72.5 -215.5q0 -51 -10 -127t-23 -141q-27 -131 -59.5 -223t-85.5 -150.5t-132 -85t-196 -26.5q-160 0 -240.5 67.5t-80.5 218.5zM213 272q0 -100 50.5 -139t154.5 -39 q86 0 140 29t90 84t57.5 135t39.5 182q10 55 17.5 110.5t7.5 96.5q0 96 -48 137t-157 41q-86 0 -140 -28.5t-90 -84t-57.5 -135t-39.5 -182.5q-10 -55 -17.5 -110.5t-7.5 -96.5zM344 1214q-23 14 7 39l264 242q10 10 20 13t21 3h63q10 0 17.5 -3t15.5 -13l172 -244 q16 -23 -8 -41l-41 -30q-29 -16 -45 4l-154 188h-10l-229 -192q-23 -20 -46 -4z" />
<glyph unicode="&#xf5;" horiz-adv-x="980" d="M78 268q0 51 10 127t23 142q27 131 59.5 223t85.5 150.5t132 85t196 26.5q176 0 248.5 -71.5t72.5 -215.5q0 -51 -10 -127t-23 -141q-27 -131 -59.5 -223t-85.5 -150.5t-132 -85t-196 -26.5q-160 0 -240.5 67.5t-80.5 218.5zM213 272q0 -100 50.5 -139t154.5 -39 q86 0 140 29t90 84t57.5 135t39.5 182q10 55 17.5 110.5t7.5 96.5q0 96 -48 137t-157 41q-86 0 -140 -28.5t-90 -84t-57.5 -135t-39.5 -182.5q-10 -55 -17.5 -110.5t-7.5 -96.5zM317 1288q57 80 116 119t114 39q39 0 62.5 -9.5t52.5 -31.5l51 -41q23 -18 36 -25.5t31 -7.5 q25 0 53.5 22.5t71.5 69.5q20 25 41 7l35 -29q20 -14 0 -41q-55 -78 -114.5 -116t-118.5 -38q-66 0 -111 39l-51 41q-16 16 -29.5 24.5t-30.5 8.5q-31 0 -57.5 -18.5t-71.5 -71.5q-20 -25 -45 -6l-32 24q-20 14 -3 41z" />
<glyph unicode="&#xf6;" horiz-adv-x="980" d="M78 268q0 51 10 127t23 142q27 131 59.5 223t85.5 150.5t132 85t196 26.5q176 0 248.5 -71.5t72.5 -215.5q0 -51 -10 -127t-23 -141q-27 -131 -59.5 -223t-85.5 -150.5t-132 -85t-196 -26.5q-160 0 -240.5 67.5t-80.5 218.5zM213 272q0 -100 50.5 -139t154.5 -39 q86 0 140 29t90 84t57.5 135t39.5 182q10 55 17.5 110.5t7.5 96.5q0 96 -48 137t-157 41q-86 0 -140 -28.5t-90 -84t-57.5 -135t-39.5 -182.5q-10 -55 -17.5 -110.5t-7.5 -96.5zM370 1286q0 4 1 16.5t10 53.5q8 39 35.5 59.5t66.5 20.5h20q76 0 76 -60v-15t-10 -52 q-8 -41 -37 -60.5t-70 -19.5h-20q-33 0 -52.5 14t-19.5 43zM702 1286q0 8 3 24.5t7 45.5q6 39 34 59.5t69 20.5h20q76 0 76 -60q0 -4 -2 -21.5t-8 -45.5q-8 -41 -37 -60.5t-70 -19.5h-20q-33 0 -52.5 14t-19.5 43z" />
<glyph unicode="&#xf7;" d="M63 504l11 59q6 31 39 31h841q18 0 22.5 -7t0.5 -24l-10 -63q-2 -27 -37 -27h-842q-18 0 -23.5 7t-1.5 24zM334 123q0 4 1 16.5t11 59.5q8 41 38 62.5t73 21.5h20q80 0 80 -66q0 -4 -1 -15.5t-11 -56.5q-18 -84 -111 -84h-20q-80 0 -80 62zM473 846q0 4 1 16t11 60 q8 41 38 62.5t73 21.5h20q80 0 80 -66q0 -4 -1 -15.5t-11 -56.5q-18 -84 -111 -84h-20q-80 0 -80 62z" />
<glyph unicode="&#xf8;" horiz-adv-x="980" d="M78 268q0 51 10 127t23 142q27 131 59.5 223t85.5 150.5t132 85t196 26.5q78 0 133 -14l69 129q14 33 41 24l37 -8q25 -8 10 -39l-75 -139q106 -68 106 -240q0 -51 -10 -127t-23 -141q-27 -131 -59.5 -223t-85.5 -150.5t-132 -85t-196 -26.5q-37 0 -69.5 4t-61.5 10 l-69 -129q-14 -33 -41 -25l-35 8q-27 8 -10 39l73 140q-109 68 -108 239zM213 272q0 -88 37 -127l407 754q-41 10 -92 10q-86 0 -140 -28.5t-90 -84t-57.5 -135t-39.5 -182.5q-10 -55 -17.5 -110.5t-7.5 -96.5zM326 102q35 -8 92 -8q86 0 140 29t90 84t57.5 135t39.5 182 q10 55 17.5 110.5t7.5 96.5q0 80 -37 123z" />
<glyph unicode="&#xf9;" horiz-adv-x="1001" d="M92 219q0 27 4 56.5t10 64.5l123 635q6 29 35 29h76q29 0 22 -29l-120 -615q-14 -70 -15 -120q0 -72 42 -106t155 -34q70 0 117 14.5t82 49.5q35 33 52 76t34 122l120 615q4 27 29 27h82q27 0 21 -27l-123 -635q-20 -106 -48 -167.5t-77 -104.5q-53 -47 -124 -67.5 t-186 -20.5q-152 0 -231.5 56t-79.5 181zM295.5 1356.5q-4.5 11.5 1.5 29.5l23 64q12 33 51 14l387 -172q27 -12 15 -37l-21 -47q-8 -23 -41 -10l-391 139q-20 8 -24.5 19.5z" />
<glyph unicode="&#xfa;" horiz-adv-x="1001" d="M92 219q0 27 4 56.5t10 64.5l123 635q6 29 35 29h76q29 0 22 -29l-120 -615q-14 -70 -15 -120q0 -72 42 -106t155 -34q70 0 117 14.5t82 49.5q35 33 52 76t34 122l120 615q4 27 29 27h82q27 0 21 -27l-123 -635q-20 -106 -48 -167.5t-77 -104.5q-53 -47 -124 -67.5 t-186 -20.5q-152 0 -231.5 56t-79.5 181zM487 1251q-12 31 17 43l383 170q39 18 51 -14l26 -72q6 -16 -1 -25.5t-35 -19.5l-381 -135q-33 -12 -43 14z" />
<glyph unicode="&#xfb;" horiz-adv-x="1001" d="M92 219q0 27 4 56.5t10 64.5l123 635q6 29 35 29h76q29 0 22 -29l-120 -615q-14 -70 -15 -120q0 -72 42 -106t155 -34q70 0 117 14.5t82 49.5q35 33 52 76t34 122l120 615q4 27 29 27h82q27 0 21 -27l-123 -635q-20 -106 -48 -167.5t-77 -104.5q-53 -47 -124 -67.5 t-186 -20.5q-152 0 -231.5 56t-79.5 181zM342 1214q-23 14 7 39l264 242q10 10 20 13t21 3h63q10 0 17.5 -3t15.5 -13l172 -244q16 -23 -8 -41l-41 -30q-29 -16 -45 4l-154 188h-10l-229 -192q-23 -20 -46 -4z" />
<glyph unicode="&#xfc;" horiz-adv-x="1001" d="M92 219q0 27 4 56.5t10 64.5l123 635q6 29 35 29h76q29 0 22 -29l-120 -615q-14 -70 -15 -120q0 -72 42 -106t155 -34q70 0 117 14.5t82 49.5q35 33 52 76t34 122l120 615q4 27 29 27h82q27 0 21 -27l-123 -635q-20 -106 -48 -167.5t-77 -104.5q-53 -47 -124 -67.5 t-186 -20.5q-152 0 -231.5 56t-79.5 181zM387 1286q0 4 1 16.5t10 53.5q8 39 35.5 59.5t66.5 20.5h20q76 0 76 -60v-15t-10 -52q-8 -41 -37 -60.5t-70 -19.5h-20q-33 0 -52.5 14t-19.5 43zM719 1286q0 8 3 24.5t7 45.5q6 39 34 59.5t69 20.5h20q76 0 76 -60q0 -4 -2 -21.5 t-8 -45.5q-8 -41 -37 -60.5t-70 -19.5h-20q-33 0 -52.5 14t-19.5 43z" />
<glyph unicode="&#xfd;" horiz-adv-x="845" d="M106 -313.5q-1 12.5 9 26.5q47 80 91 155t85 146q-41 6 -68.5 36t-38.5 93q-27 168 -45 368t-33 468q0 25 27 25h86q25 0 25 -23q10 -262 26.5 -462t36.5 -353q6 -47 41 -49q55 102 106.5 204.5t101.5 208t99.5 219t102.5 238.5q6 16 26 17h93q14 0 17 -8.5t-1 -18.5 q-82 -193 -158 -357.5t-157 -328.5q-78 -156 -161 -307.5t-179 -317.5q-10 -18 -21.5 -24.5t-30.5 2.5l-63 24q-16 6 -17 18.5zM364 1251q-12 31 17 43l383 170q39 18 51 -14l26 -72q6 -16 -1 -25.5t-35 -19.5l-381 -135q-33 -12 -43 14z" />
<glyph unicode="&#xfe;" horiz-adv-x="1026" d="M-23 -340l330 1698q4 16 9.5 22t21.5 6h80q29 0 22 -28l-88 -447h4q23 25 43.5 43.5t49.5 32.5q76 35 186 35q170 0 250 -80q35 -37 50 -84t15 -125q0 -41 -8 -108.5t-31 -173.5q-18 -86 -33.5 -145.5t-34 -103.5t-41 -75t-50.5 -57q-55 -49 -128 -68.5t-184 -19.5 q-190 0 -241 118h-4l-84 -440q-4 -23 -23 -22h-94q-20 0 -17 22zM241 261q3 -56 31 -97q25 -35 66 -52.5t115 -17.5q59 0 109 13.5t89 48.5q43 41 71 110.5t56 200.5q18 88 25.5 146.5t7.5 91.5q0 47 -7 83.5t-30 65.5t-64.5 42t-111.5 13q-57 0 -112.5 -15t-90.5 -46 q-20 -18 -34.5 -37.5t-25.5 -46.5t-20.5 -60.5t-17.5 -80.5l-47 -240q-12 -66 -9 -122z" />
<glyph unicode="&#xff;" horiz-adv-x="845" d="M106 -313.5q-1 12.5 9 26.5q47 80 91 155t85 146q-41 6 -68.5 36t-38.5 93q-27 168 -45 368t-33 468q0 25 27 25h86q25 0 25 -23q10 -262 26.5 -462t36.5 -353q6 -47 41 -49q55 102 106.5 204.5t101.5 208t99.5 219t102.5 238.5q6 16 26 17h93q14 0 17 -8.5t-1 -18.5 q-82 -193 -158 -357.5t-157 -328.5q-78 -156 -161 -307.5t-179 -317.5q-10 -18 -21.5 -24.5t-30.5 2.5l-63 24q-16 6 -17 18.5zM282 1286q0 4 1 16.5t10 53.5q8 39 35.5 59.5t66.5 20.5h20q76 0 76 -60v-15t-10 -52q-8 -41 -37 -60.5t-70 -19.5h-20q-33 0 -52.5 14t-19.5 43 zM614 1286q0 8 3 24.5t7 45.5q6 39 34 59.5t69 20.5h20q76 0 76 -60q0 -4 -2 -21.5t-8 -45.5q-8 -41 -37 -60.5t-70 -19.5h-20q-33 0 -52.5 14t-19.5 43z" />
<glyph unicode="&#x152;" horiz-adv-x="1720" d="M104 350q0 96 16.5 192.5t33.5 174.5q20 98 41.5 177t48 141.5t62.5 112.5t83 91q123 104 344 104q119 0 193.5 -35.5t115.5 -101.5q23 53 56 78q59 41 180 41h471q18 0 23.5 -7t1.5 -24l-13 -59q-4 -14 -11 -22.5t-26 -8.5h-460q-59 0 -88 -20q-29 -23 -41 -84l-70 -355 h502q18 0 23 -7t1 -23l-12 -60q-4 -14 -11 -22t-26 -8h-500l-77 -400q-12 -59 10 -84q18 -20 80 -20h461q18 0 22 -8.5t0 -22.5l-10 -59q-4 -16 -10.5 -23.5t-24.5 -7.5h-471q-76 0 -125 33t-59 100q-47 -80 -138.5 -115.5t-191.5 -35.5q-96 0 -171 17t-126 60t-79 114 t-28 177zM254 373q0 -72 10 -123t39 -84t79 -48.5t128 -15.5q131 0 222 81t124 243l96 481q16 82 6 141.5t-40.5 98.5t-80 57.5t-110.5 18.5q-86 0 -141.5 -20.5t-102.5 -61.5q-39 -35 -65.5 -79t-48 -99.5t-38 -123t-34.5 -149.5q-23 -115 -33 -186.5t-10 -130.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1558" d="M78 268q0 51 10 127t23 142q27 131 59.5 223t85.5 150.5t132 85t196 26.5q113 0 183.5 -36t94.5 -101q47 72 126 104.5t202 32.5q143 0 223 -63.5t80 -206.5q0 -23 -3 -48.5t-7 -54.5l-35 -184q-6 -41 -59 -41l-525 2q-12 -59 -15 -91t-3 -54q0 -55 11 -91t37 -57.5 t70 -30t111 -8.5q59 0 126 12.5t128 34.5q35 12 41 -16l10 -47q4 -14 -2 -22.5t-30 -18.5q-76 -29 -152 -42t-143 -13q-152 0 -222.5 42t-95.5 115q-41 -80 -128 -118.5t-208 -38.5q-160 0 -240.5 67.5t-80.5 218.5zM213 272q0 -100 50.5 -139t154.5 -39q80 0 133 25.5 t89 76t57.5 123t39.5 164.5q10 59 21.5 121t11.5 127q0 92 -48 135t-157 43q-86 0 -140 -28.5t-90 -84t-57.5 -135t-39.5 -182.5q-10 -55 -17.5 -110.5t-7.5 -96.5zM885 532l411 -2q27 0 33 23l21 100q10 57 10 97q0 86 -42.5 122.5t-146.5 36.5q-53 0 -95 -9t-72 -36 q-18 -16 -35 -38.5t-31 -60.5t-27.5 -94.5t-25.5 -138.5z" />
<glyph unicode="&#x178;" horiz-adv-x="927" d="M168 1300q-2 25 27 25h90q29 0 30 -20q18 -201 50 -372t90 -337h14q121 174 227.5 345t202.5 364q10 20 37 20h90q12 0 19.5 -5t1.5 -20q-53 -113 -111.5 -217t-123 -207.5t-139.5 -209t-163 -220.5l-80 -411q-6 -35 -37 -35h-80q-31 0 -24 35l80 416 q-80 205 -131.5 412.5t-69.5 436.5zM379 1536q0 4 1 16.5t9 53.5q8 39 35.5 59.5t66.5 20.5h21q76 0 75 -60v-15.5t-10 -51.5q-8 -41 -36.5 -60.5t-69.5 -19.5h-21q-33 0 -52 14t-19 43zM710 1536q0 8 3 24.5t8 45.5q6 39 33.5 59.5t68.5 20.5h20q76 0 76 -60q0 -4 -2 -21.5 t-8 -45.5q-8 -41 -37 -60.5t-69 -19.5h-21q-33 0 -52.5 14t-19.5 43z" />
<glyph unicode="&#x2c6;" horiz-adv-x="770" d="M229 1214q-23 14 7 39l264 242q10 10 20 13t21 3h63q10 0 17.5 -3t15.5 -13l172 -244q16 -23 -8 -41l-41 -30q-29 -16 -45 4l-154 188h-10l-229 -192q-23 -20 -46 -4z" />
<glyph unicode="&#x2dc;" horiz-adv-x="833" d="M233 1288q57 80 116 119t114 39q39 0 62.5 -9.5t52.5 -31.5l51 -41q23 -18 36 -25.5t31 -7.5q25 0 53.5 22.5t71.5 69.5q20 25 41 7l35 -29q20 -14 0 -41q-55 -78 -114.5 -116t-118.5 -38q-66 0 -111 39l-51 41q-16 16 -29.5 24.5t-30.5 8.5q-31 0 -57.5 -18.5 t-71.5 -71.5q-20 -25 -45 -6l-32 24q-20 14 -3 41z" />
<glyph unicode="&#x2000;" horiz-adv-x="881" />
<glyph unicode="&#x2001;" horiz-adv-x="1763" />
<glyph unicode="&#x2002;" horiz-adv-x="881" />
<glyph unicode="&#x2003;" horiz-adv-x="1763" />
<glyph unicode="&#x2004;" horiz-adv-x="587" />
<glyph unicode="&#x2005;" horiz-adv-x="440" />
<glyph unicode="&#x2006;" horiz-adv-x="293" />
<glyph unicode="&#x2007;" horiz-adv-x="293" />
<glyph unicode="&#x2008;" horiz-adv-x="220" />
<glyph unicode="&#x2009;" horiz-adv-x="352" />
<glyph unicode="&#x200a;" horiz-adv-x="97" />
<glyph unicode="&#x2010;" horiz-adv-x="608" d="M66 496l14 75q4 14 11 22.5t26 8.5h395q18 0 23.5 -8t1.5 -23l-15 -79q-4 -20 -15 -23.5t-22 -3.5h-395q-18 0 -23.5 7t-0.5 24z" />
<glyph unicode="&#x2011;" horiz-adv-x="608" d="M66 496l14 75q4 14 11 22.5t26 8.5h395q18 0 23.5 -8t1.5 -23l-15 -79q-4 -20 -15 -23.5t-22 -3.5h-395q-18 0 -23.5 7t-0.5 24z" />
<glyph unicode="&#x2012;" horiz-adv-x="608" d="M66 496l14 75q4 14 11 22.5t26 8.5h395q18 0 23.5 -8t1.5 -23l-15 -79q-4 -20 -15 -23.5t-22 -3.5h-395q-18 0 -23.5 7t-0.5 24z" />
<glyph unicode="&#x2013;" horiz-adv-x="1032" d="M-6 496l14 75q4 14 11.5 22.5t25.5 8.5h963q18 0 23 -8t1 -23l-14 -79q-4 -20 -15.5 -23.5t-21.5 -3.5h-963q-18 0 -23 7t-1 24z" />
<glyph unicode="&#x2014;" horiz-adv-x="2056" d="M-6 496l14 75q4 14 11.5 22.5t25.5 8.5h1987q18 0 23 -8t1 -23l-14 -79q-4 -20 -15.5 -23.5t-21.5 -3.5h-1987q-18 0 -23 7t-1 24z" />
<glyph unicode="&#x2018;" horiz-adv-x="350" d="M119 1016q0 31 8 72q8 35 21.5 70.5t35.5 76.5q23 43 56.5 86t66.5 74q18 18 37 4l33 -22q18 -16 -4 -39q-31 -31 -56 -68q-29 -41 -47 -80q-14 -29 4 -37l29 -16q33 -18 33 -53q0 -6 -2 -23.5t-8 -46.5q-8 -37 -30 -53.5t-65 -16.5h-28q-43 0 -63.5 15.5t-20.5 56.5z " />
<glyph unicode="&#x2019;" horiz-adv-x="350" d="M180 977q-18 16 4 39q31 31 56 68q29 41 47 80q14 29 -4 37l-29 16q-33 18 -33 53q0 6 2 23.5t8 46.5q8 37 30 53.5t65 16.5h28q43 0 63.5 -15.5t20.5 -56.5q0 -31 -8 -72q-8 -35 -21.5 -70.5t-35.5 -76.5q-23 -43 -56.5 -86t-66.5 -74q-18 -18 -37 -4z" />
<glyph unicode="&#x201a;" horiz-adv-x="264" d="M6 -240q-18 16 4 39q31 31 56 68q29 41 47 80q14 29 -4 37l-29 16q-33 18 -33 53q0 6 2 23.5t8 46.5q8 37 30 53.5t65 16.5h28q43 0 63.5 -15.5t20.5 -56.5q0 -31 -8 -72q-8 -35 -21.5 -70.5t-35.5 -76.5q-23 -43 -56.5 -86t-66.5 -74q-18 -18 -37 -4z" />
<glyph unicode="&#x201c;" horiz-adv-x="671" d="M119 1016q0 31 8 72q8 35 21.5 70.5t35.5 76.5q23 43 56.5 86t66.5 74q18 18 37 4l33 -22q18 -16 -4 -39q-31 -31 -56 -68q-29 -41 -47 -80q-14 -29 4 -37l29 -16q33 -18 33 -53q0 -6 -2 -23.5t-8 -46.5q-8 -37 -30 -53.5t-65 -16.5h-28q-43 0 -63.5 15.5t-20.5 56.5z M441 1016q0 31 8 72q8 35 21.5 70.5t35.5 76.5q23 43 56.5 86t66.5 74q18 18 37 4l33 -22q18 -16 -4 -39q-31 -31 -56 -68q-29 -41 -47 -80q-14 -29 4 -37l29 -16q33 -18 33 -53q0 -6 -2 -23.5t-8 -46.5q-8 -37 -30 -53.5t-65 -16.5h-28q-43 0 -63.5 15.5t-20.5 56.5z" />
<glyph unicode="&#x201d;" horiz-adv-x="671" d="M180 977q-18 16 4 39q31 31 56 68q29 41 47 80q14 29 -4 37l-29 16q-33 18 -33 53q0 6 2 23.5t8 46.5q8 37 30 53.5t65 16.5h28q43 0 63.5 -15.5t20.5 -56.5q0 -31 -8 -72q-8 -35 -21.5 -70.5t-35.5 -76.5q-23 -43 -56.5 -86t-66.5 -74q-18 -18 -37 -4zM502 977 q-18 16 4 39q31 31 56 68q29 41 47 80q14 29 -4 37l-29 16q-33 18 -33 53q0 6 2 23.5t8 46.5q8 37 30 53.5t65 16.5h28q43 0 63.5 -15.5t20.5 -56.5q0 -31 -8 -72q-8 -35 -21.5 -70.5t-35.5 -76.5q-23 -43 -56.5 -86t-66.5 -74q-18 -18 -37 -4z" />
<glyph unicode="&#x201e;" horiz-adv-x="585" d="M6 -240q-18 16 4 39q31 31 56 68q29 41 47 80q14 29 -4 37l-29 16q-33 18 -33 53q0 6 2 23.5t8 46.5q8 37 30 53.5t65 16.5h28q43 0 63.5 -15.5t20.5 -56.5q0 -31 -8 -72q-8 -35 -21.5 -70.5t-35.5 -76.5q-23 -43 -56.5 -86t-66.5 -74q-18 -18 -37 -4zM325 -240 q-18 16 4 39q31 31 56 68q29 41 47 80q14 29 -4 37l-29 16q-33 18 -33 53q0 6 2 23.5t8 46.5q8 37 30 53.5t65 16.5h28q43 0 63.5 -15.5t20.5 -56.5q0 -31 -8 -72q-8 -35 -21.5 -70.5t-35.5 -76.5q-23 -43 -56.5 -86t-66.5 -74q-18 -18 -37 -4z" />
<glyph unicode="&#x2022;" horiz-adv-x="747" d="M123 481q0 68 22.5 122t62.5 94t94 61.5t116 21.5q92 0 147.5 -57t55.5 -145q0 -68 -24 -121.5t-63.5 -90t-94 -56t-115.5 -19.5q-92 0 -146.5 53t-54.5 137z" />
<glyph unicode="&#x2026;" horiz-adv-x="1187" d="M-96 35q0 16 2 32.5t8 45.5q8 41 35 60.5t68 19.5h20q76 0 76 -58q0 -4 -2 -23.5t-8 -48.5q-16 -80 -105 -79h-20q-35 0 -54.5 14t-19.5 37zM295 35q0 16 2 32.5t8 45.5q8 41 35 60.5t68 19.5h20q76 0 76 -58q0 -4 -2 -23.5t-8 -48.5q-16 -80 -105 -79h-20 q-35 0 -54.5 14t-19.5 37zM688 35q0 16 2 32.5t8 45.5q8 41 35 60.5t68 19.5h20q76 0 76 -58q0 -4 -2 -23.5t-8 -48.5q-16 -80 -105 -79h-20q-35 0 -54.5 14t-19.5 37z" />
<glyph unicode="&#x202f;" horiz-adv-x="352" />
<glyph unicode="&#x2039;" horiz-adv-x="591" d="M82 449q0 12 10 47q4 14 11.5 22t23.5 21l332 235q14 10 23.5 10t19.5 -10l37 -43q23 -23 -4 -41l-291 -229l188 -207q23 -25 2 -41l-43 -35q-18 -16 -49 10l-242 228q-18 18 -18 33z" />
<glyph unicode="&#x203a;" horiz-adv-x="591" d="M18 219q-23 23 5 41l290 229l-188 207q-23 25 -2 41l43 35q18 16 49 -10l242 -227q18 -18 18 -33q0 -12 -10 -47q-4 -14 -11.5 -22.5t-23.5 -20.5l-332 -236q-14 -10 -23 -10t-20 10z" />
<glyph unicode="&#x205f;" horiz-adv-x="440" />
<glyph unicode="&#x20ac;" d="M16 477l11 53q4 14 11 22.5t25 8.5h138q6 29 11 63.5t13 75.5h-135q-18 0 -23.5 7.5t-0.5 23.5l10 53q4 14 11 22.5t26 8.5h135q14 82 32.5 147.5t40 116.5t49 91t62.5 71q61 53 145.5 77.5t194.5 24.5q86 0 168 -20t152 -57q31 -14 14 -41l-37 -60q-14 -23 -45 -6 q-49 29 -122 46.5t-132 17.5q-76 0 -133 -15.5t-98 -54.5q-53 -47 -85 -124t-59 -214h443q18 0 23 -8t1 -23l-10 -57q-4 -20 -15.5 -23.5t-21.5 -3.5h-446l-25 -139h444q18 0 23.5 -8t1.5 -23l-12 -57q-4 -20 -14.5 -23.5t-20.5 -3.5h-436q-4 -23 -4 -42v-35q0 -74 12 -124 t44 -83t84 -47.5t132 -14.5q59 0 127 11.5t135 35.5q23 8 31 2t12 -22l8 -49q6 -29 -26 -41q-31 -12 -69 -22.5t-78 -17.5t-80 -11t-74 -4q-115 0 -192 21.5t-125 66.5t-68.5 114.5t-20.5 165.5q0 41 6 96h-143q-18 0 -23.5 7.5t-1.5 23.5z" />
<glyph unicode="&#x2122;" horiz-adv-x="1388" d="M205 1245l14 70q2 10 8 12t11 2h432q12 0 12 -16l-14 -68q-2 -14 -19 -14h-162l-96 -504q-4 -16 -18 -16h-80q-18 0 -14 16l96 504h-158q-14 0 -12 14zM637 727q18 78 34.5 146.5t33 134t35 134t40.5 146.5q6 25 19.5 33t36.5 8h86q23 0 32 -9t13 -38q14 -117 23 -212 t24 -193h10q53 98 104.5 199.5t100.5 205.5q14 29 28.5 38t36.5 9h86q23 0 31 -10t6 -33q-12 -131 -29.5 -267t-39.5 -292q-4 -16 -17 -16h-78q-18 0 -14 16q25 147 41 262t31 234h-6q-55 -115 -101.5 -210t-105.5 -202q-10 -23 -20.5 -27t-30.5 -4h-78q-23 0 -31 7.5 t-12 23.5q-16 98 -28.5 199.5t-25.5 212.5h-6q-18 -63 -32.5 -118.5t-27.5 -113t-27.5 -121t-33.5 -143.5q-4 -16 -18 -16h-78q-16 0 -12 16z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1003" d="M0 0v1004h1004v-1004h-1004z" />
<hkern u1="&#x26;" u2="&#x178;" k="57" />
<hkern u1="&#x26;" u2="&#xdd;" k="57" />
<hkern u1="&#x26;" u2="Z" k="-35" />
<hkern u1="&#x26;" u2="Y" k="57" />
<hkern u1="&#x26;" u2="T" k="129" />
<hkern u1="&#x26;" u2="X" k="-39" />
<hkern u1="&#x26;" u2="V" k="37" />
<hkern u1="&#x28;" u2="&#x178;" k="-49" />
<hkern u1="&#x28;" u2="&#xdd;" k="-49" />
<hkern u1="&#x28;" u2="Y" k="-49" />
<hkern u1="&#x28;" u2="W" k="-66" />
<hkern u1="&#x28;" u2="T" k="-86" />
<hkern u1="&#x28;" u2="V" k="-86" />
<hkern u1="&#x2a;" u2="&#x178;" k="-39" />
<hkern u1="&#x2a;" u2="&#xdd;" k="-39" />
<hkern u1="&#x2a;" u2="&#xc5;" k="145" />
<hkern u1="&#x2a;" u2="&#xc4;" k="145" />
<hkern u1="&#x2a;" u2="&#xc3;" k="145" />
<hkern u1="&#x2a;" u2="&#xc2;" k="145" />
<hkern u1="&#x2a;" u2="&#xc1;" k="145" />
<hkern u1="&#x2a;" u2="&#xc0;" k="145" />
<hkern u1="&#x2a;" u2="Y" k="-39" />
<hkern u1="&#x2a;" u2="W" k="-39" />
<hkern u1="&#x2a;" u2="T" k="-66" />
<hkern u1="&#x2a;" u2="A" k="145" />
<hkern u1="&#x2a;" u2="X" k="-25" />
<hkern u1="&#x2a;" u2="V" k="-49" />
<hkern u1="&#x2c;" u2="t" k="29" />
<hkern u1="&#x2c;" u2="j" k="-88" />
<hkern u1="&#x2c;" u2="v" k="109" />
<hkern u1="&#x2c;" u2="V" k="78" />
<hkern u1="&#x2c;" u2="J" k="-49" />
<hkern u1="&#x2e;" u2="v" k="109" />
<hkern u1="&#x2e;" u2="V" k="78" />
<hkern u1="&#x2e;" u2="J" k="-49" />
<hkern u1="&#x2f;" g2="uniFB02" k="-35" />
<hkern u1="&#x2f;" g2="uniFB01" k="-35" />
<hkern u1="&#x2f;" u2="&#x178;" k="-76" />
<hkern u1="&#x2f;" u2="&#x153;" k="27" />
<hkern u1="&#x2f;" u2="&#xf8;" k="27" />
<hkern u1="&#x2f;" u2="&#xf6;" k="27" />
<hkern u1="&#x2f;" u2="&#xf5;" k="27" />
<hkern u1="&#x2f;" u2="&#xf4;" k="27" />
<hkern u1="&#x2f;" u2="&#xf3;" k="27" />
<hkern u1="&#x2f;" u2="&#xf2;" k="27" />
<hkern u1="&#x2f;" u2="&#xf0;" k="27" />
<hkern u1="&#x2f;" u2="&#xdd;" k="-76" />
<hkern u1="&#x2f;" u2="o" k="27" />
<hkern u1="&#x2f;" u2="f" k="-35" />
<hkern u1="&#x2f;" u2="Y" k="-76" />
<hkern u1="&#x2f;" u2="W" k="-53" />
<hkern u1="&#x2f;" u2="T" k="-68" />
<hkern u1="&#x2f;" u2="V" k="-78" />
<hkern u1="&#x3b;" u2="j" k="-70" />
<hkern u1="&#x40;" u2="T" k="78" />
<hkern u1="&#x40;" u2="V" k="16" />
<hkern u1="A" u2="&#x2122;" k="221" />
<hkern u1="A" u2="&#x203a;" k="-35" />
<hkern u1="A" u2="&#xbb;" k="-35" />
<hkern u1="A" u2="v" k="68" />
<hkern u1="A" u2="q" k="41" />
<hkern u1="A" u2="p" k="12" />
<hkern u1="A" u2="b" k="47" />
<hkern u1="A" u2="\" k="139" />
<hkern u1="A" u2="X" k="-35" />
<hkern u1="A" u2="V" k="94" />
<hkern u1="A" u2="Q" k="43" />
<hkern u1="A" u2="J" k="-55" />
<hkern u1="A" u2="&#x3f;" k="47" />
<hkern u1="A" u2="&#x2a;" k="139" />
<hkern u1="B" u2="&#x152;" k="-14" />
<hkern u1="B" u2="&#xd8;" k="-14" />
<hkern u1="B" u2="&#xd6;" k="-14" />
<hkern u1="B" u2="&#xd5;" k="-14" />
<hkern u1="B" u2="&#xd4;" k="-14" />
<hkern u1="B" u2="&#xd3;" k="-14" />
<hkern u1="B" u2="&#xd2;" k="-14" />
<hkern u1="B" u2="&#xc5;" k="-29" />
<hkern u1="B" u2="&#xc4;" k="-29" />
<hkern u1="B" u2="&#xc3;" k="-29" />
<hkern u1="B" u2="&#xc2;" k="-29" />
<hkern u1="B" u2="&#xc1;" k="-29" />
<hkern u1="B" u2="&#xc0;" k="-29" />
<hkern u1="B" u2="T" k="57" />
<hkern u1="B" u2="O" k="-14" />
<hkern u1="B" u2="A" k="-29" />
<hkern u1="B" u2="&#x2122;" k="88" />
<hkern u1="B" u2="&#x29;" k="27" />
<hkern u1="C" u2="x" k="-25" />
<hkern u1="C" u2="q" k="37" />
<hkern u1="C" u2="X" k="-37" />
<hkern u1="C" u2="V" k="-35" />
<hkern u1="C" u2="J" k="-68" />
<hkern u1="C" u2="&#x29;" k="-76" />
<hkern u1="D" u2="X" k="41" />
<hkern u1="E" u2="V" k="-14" />
<hkern u1="E" u2="Q" k="16" />
<hkern u1="E" u2="J" k="-37" />
<hkern u1="E" u2="B" k="6" />
<hkern u1="F" u2="&#x2026;" k="139" />
<hkern u1="F" u2="&#x201e;" k="139" />
<hkern u1="F" u2="&#x201d;" k="-39" />
<hkern u1="F" u2="&#x201a;" k="139" />
<hkern u1="F" u2="&#x2019;" k="-39" />
<hkern u1="F" u2="&#x178;" k="-35" />
<hkern u1="F" u2="&#x153;" k="16" />
<hkern u1="F" u2="&#x152;" k="53" />
<hkern u1="F" u2="&#xf8;" k="16" />
<hkern u1="F" u2="&#xf6;" k="16" />
<hkern u1="F" u2="&#xf5;" k="16" />
<hkern u1="F" u2="&#xf4;" k="16" />
<hkern u1="F" u2="&#xf3;" k="16" />
<hkern u1="F" u2="&#xf2;" k="16" />
<hkern u1="F" u2="&#xf0;" k="16" />
<hkern u1="F" u2="&#xdd;" k="-35" />
<hkern u1="F" u2="&#xd8;" k="53" />
<hkern u1="F" u2="&#xd6;" k="53" />
<hkern u1="F" u2="&#xd5;" k="53" />
<hkern u1="F" u2="&#xd4;" k="53" />
<hkern u1="F" u2="&#xd3;" k="53" />
<hkern u1="F" u2="&#xd2;" k="53" />
<hkern u1="F" u2="&#xc7;" k="53" />
<hkern u1="F" u2="&#xc6;" k="197" />
<hkern u1="F" u2="&#xc5;" k="63" />
<hkern u1="F" u2="&#xc4;" k="63" />
<hkern u1="F" u2="&#xc3;" k="63" />
<hkern u1="F" u2="&#xc2;" k="63" />
<hkern u1="F" u2="&#xc1;" k="63" />
<hkern u1="F" u2="&#xc0;" k="63" />
<hkern u1="F" u2="o" k="16" />
<hkern u1="F" u2="Y" k="-35" />
<hkern u1="F" u2="W" k="-35" />
<hkern u1="F" u2="T" k="-25" />
<hkern u1="F" u2="O" k="53" />
<hkern u1="F" u2="G" k="53" />
<hkern u1="F" u2="C" k="53" />
<hkern u1="F" u2="A" k="63" />
<hkern u1="F" u2="&#x2e;" k="139" />
<hkern u1="F" u2="&#x2c;" k="139" />
<hkern u1="F" u2="V" k="-35" />
<hkern u1="F" u2="Q" k="53" />
<hkern u1="F" u2="J" k="94" />
<hkern u1="F" u2="&#x3f;" k="-20" />
<hkern u1="F" u2="&#x2f;" k="106" />
<hkern u1="G" u2="J" k="-25" />
<hkern u1="G" u2="&#x3f;" k="16" />
<hkern u1="J" u2="v" k="-14" />
<hkern u1="K" u2="x" k="-61" />
<hkern u1="K" u2="v" k="27" />
<hkern u1="K" u2="X" k="-80" />
<hkern u1="K" u2="V" k="-66" />
<hkern u1="K" u2="J" k="-66" />
<hkern u1="K" u2="&#x29;" k="-70" />
<hkern u1="L" u2="&#x2122;" k="156" />
<hkern u1="L" u2="x" k="-47" />
<hkern u1="L" u2="v" k="41" />
<hkern u1="L" u2="\" k="109" />
<hkern u1="L" u2="X" k="-109" />
<hkern u1="L" u2="V" k="88" />
<hkern u1="L" u2="P" k="-29" />
<hkern u1="L" u2="M" k="-20" />
<hkern u1="L" u2="J" k="-98" />
<hkern u1="L" u2="F" k="-20" />
<hkern u1="L" u2="&#x3f;" k="47" />
<hkern u1="L" u2="&#x2a;" k="211" />
<hkern u1="L" u2="&#x29;" k="-59" />
<hkern u1="L" u2="&#x26;" k="-14" />
<hkern u1="M" u2="&#x178;" k="16" />
<hkern u1="M" u2="&#xdd;" k="16" />
<hkern u1="M" u2="w" k="-25" />
<hkern u1="M" u2="Y" k="16" />
<hkern u1="M" u2="T" k="29" />
<hkern u1="M" u2="v" k="-25" />
<hkern u1="M" u2="J" k="-20" />
<hkern u1="O" u2="X" k="37" />
<hkern u1="O" u2="V" k="27" />
<hkern u1="P" g2="uniFB02" k="-25" />
<hkern u1="P" g2="uniFB01" k="-25" />
<hkern u1="P" u2="&#x2026;" k="180" />
<hkern u1="P" u2="&#x201e;" k="180" />
<hkern u1="P" u2="&#x201a;" k="180" />
<hkern u1="P" u2="&#x178;" k="-25" />
<hkern u1="P" u2="&#x153;" k="27" />
<hkern u1="P" u2="&#xff;" k="-45" />
<hkern u1="P" u2="&#xfd;" k="-45" />
<hkern u1="P" u2="&#xf8;" k="27" />
<hkern u1="P" u2="&#xf6;" k="27" />
<hkern u1="P" u2="&#xf5;" k="27" />
<hkern u1="P" u2="&#xf4;" k="27" />
<hkern u1="P" u2="&#xf3;" k="27" />
<hkern u1="P" u2="&#xf2;" k="27" />
<hkern u1="P" u2="&#xf0;" k="27" />
<hkern u1="P" u2="&#xe6;" k="41" />
<hkern u1="P" u2="&#xe5;" k="41" />
<hkern u1="P" u2="&#xe4;" k="41" />
<hkern u1="P" u2="&#xe3;" k="41" />
<hkern u1="P" u2="&#xe2;" k="41" />
<hkern u1="P" u2="&#xe1;" k="41" />
<hkern u1="P" u2="&#xe0;" k="41" />
<hkern u1="P" u2="&#xdd;" k="-25" />
<hkern u1="P" u2="&#xc6;" k="195" />
<hkern u1="P" u2="&#xc5;" k="88" />
<hkern u1="P" u2="&#xc4;" k="88" />
<hkern u1="P" u2="&#xc3;" k="88" />
<hkern u1="P" u2="&#xc2;" k="88" />
<hkern u1="P" u2="&#xc1;" k="88" />
<hkern u1="P" u2="&#xc0;" k="88" />
<hkern u1="P" u2="y" k="-45" />
<hkern u1="P" u2="w" k="-45" />
<hkern u1="P" u2="t" k="-25" />
<hkern u1="P" u2="s" k="20" />
<hkern u1="P" u2="o" k="27" />
<hkern u1="P" u2="f" k="-25" />
<hkern u1="P" u2="a" k="41" />
<hkern u1="P" u2="Z" k="37" />
<hkern u1="P" u2="Y" k="-25" />
<hkern u1="P" u2="W" k="-25" />
<hkern u1="P" u2="A" k="88" />
<hkern u1="P" u2="&#x2e;" k="180" />
<hkern u1="P" u2="&#x2c;" k="180" />
<hkern u1="P" u2="x" k="-25" />
<hkern u1="P" u2="v" k="-45" />
<hkern u1="P" u2="X" k="37" />
<hkern u1="P" u2="J" k="88" />
<hkern u1="P" u2="&#x2f;" k="129" />
<hkern u1="P" u2="&#x26;" k="47" />
<hkern u1="Q" u2="&#x178;" k="20" />
<hkern u1="Q" u2="&#xdd;" k="20" />
<hkern u1="Q" u2="Y" k="20" />
<hkern u1="Q" u2="T" k="57" />
<hkern u1="R" u2="&#x2122;" k="78" />
<hkern u1="R" u2="x" k="-45" />
<hkern u1="R" u2="v" k="-25" />
<hkern u1="R" u2="\" k="37" />
<hkern u1="R" u2="X" k="-76" />
<hkern u1="R" u2="J" k="-55" />
<hkern u1="R" u2="&#x29;" k="-25" />
<hkern u1="S" u2="v" k="37" />
<hkern u1="S" u2="V" k="10" />
<hkern u1="S" u2="J" k="-20" />
<hkern u1="T" u2="&#x7d;" k="-57" />
<hkern u1="T" u2="x" k="109" />
<hkern u1="T" u2="v" k="68" />
<hkern u1="T" u2="q" k="131" />
<hkern u1="T" u2="p" k="129" />
<hkern u1="T" u2="m" k="129" />
<hkern u1="T" u2="h" k="37" />
<hkern u1="T" u2="b" k="16" />
<hkern u1="T" u2="]" k="-88" />
<hkern u1="T" u2="V" k="-92" />
<hkern u1="T" u2="Q" k="57" />
<hkern u1="T" u2="M" k="29" />
<hkern u1="T" u2="J" k="78" />
<hkern u1="T" u2="F" k="16" />
<hkern u1="T" u2="E" k="27" />
<hkern u1="T" u2="&#x40;" k="37" />
<hkern u1="T" u2="&#x2a;" k="-66" />
<hkern u1="T" u2="&#x29;" k="-86" />
<hkern u1="U" u2="v" k="-45" />
<hkern u1="V" u2="&#x2026;" k="39" />
<hkern u1="V" u2="&#x201e;" k="39" />
<hkern u1="V" u2="&#x201d;" k="-57" />
<hkern u1="V" u2="&#x201a;" k="39" />
<hkern u1="V" u2="&#x2019;" k="-57" />
<hkern u1="V" u2="&#x178;" k="-66" />
<hkern u1="V" u2="&#x153;" k="61" />
<hkern u1="V" u2="&#x152;" k="4" />
<hkern u1="V" u2="&#xff;" k="-45" />
<hkern u1="V" u2="&#xfd;" k="-45" />
<hkern u1="V" u2="&#xfc;" k="12" />
<hkern u1="V" u2="&#xfb;" k="12" />
<hkern u1="V" u2="&#xfa;" k="12" />
<hkern u1="V" u2="&#xf9;" k="12" />
<hkern u1="V" u2="&#xf8;" k="61" />
<hkern u1="V" u2="&#xf6;" k="61" />
<hkern u1="V" u2="&#xf5;" k="61" />
<hkern u1="V" u2="&#xf4;" k="61" />
<hkern u1="V" u2="&#xf3;" k="61" />
<hkern u1="V" u2="&#xf2;" k="61" />
<hkern u1="V" u2="&#xf1;" k="29" />
<hkern u1="V" u2="&#xf0;" k="61" />
<hkern u1="V" u2="&#xeb;" k="61" />
<hkern u1="V" u2="&#xea;" k="61" />
<hkern u1="V" u2="&#xe9;" k="61" />
<hkern u1="V" u2="&#xe8;" k="61" />
<hkern u1="V" u2="&#xe7;" k="61" />
<hkern u1="V" u2="&#xe6;" k="57" />
<hkern u1="V" u2="&#xe5;" k="57" />
<hkern u1="V" u2="&#xe4;" k="57" />
<hkern u1="V" u2="&#xe3;" k="57" />
<hkern u1="V" u2="&#xe2;" k="57" />
<hkern u1="V" u2="&#xe1;" k="57" />
<hkern u1="V" u2="&#xe0;" k="57" />
<hkern u1="V" u2="&#xdd;" k="-66" />
<hkern u1="V" u2="&#xd8;" k="4" />
<hkern u1="V" u2="&#xd6;" k="4" />
<hkern u1="V" u2="&#xd5;" k="4" />
<hkern u1="V" u2="&#xd4;" k="4" />
<hkern u1="V" u2="&#xd3;" k="4" />
<hkern u1="V" u2="&#xd2;" k="4" />
<hkern u1="V" u2="&#xc7;" k="4" />
<hkern u1="V" u2="&#xc6;" k="143" />
<hkern u1="V" u2="&#xc5;" k="74" />
<hkern u1="V" u2="&#xc4;" k="74" />
<hkern u1="V" u2="&#xc3;" k="74" />
<hkern u1="V" u2="&#xc2;" k="74" />
<hkern u1="V" u2="&#xc1;" k="74" />
<hkern u1="V" u2="&#xc0;" k="74" />
<hkern u1="V" u2="y" k="-45" />
<hkern u1="V" u2="w" k="-37" />
<hkern u1="V" u2="u" k="12" />
<hkern u1="V" u2="t" k="-20" />
<hkern u1="V" u2="s" k="47" />
<hkern u1="V" u2="o" k="61" />
<hkern u1="V" u2="n" k="29" />
<hkern u1="V" u2="g" k="61" />
<hkern u1="V" u2="e" k="61" />
<hkern u1="V" u2="d" k="61" />
<hkern u1="V" u2="c" k="61" />
<hkern u1="V" u2="a" k="57" />
<hkern u1="V" u2="Z" k="-25" />
<hkern u1="V" u2="Y" k="-66" />
<hkern u1="V" u2="W" k="-66" />
<hkern u1="V" u2="T" k="-92" />
<hkern u1="V" u2="S" k="-39" />
<hkern u1="V" u2="O" k="4" />
<hkern u1="V" u2="G" k="4" />
<hkern u1="V" u2="C" k="4" />
<hkern u1="V" u2="A" k="74" />
<hkern u1="V" u2="&#x2e;" k="39" />
<hkern u1="V" u2="&#x2c;" k="39" />
<hkern u1="V" u2="&#x2122;" k="-39" />
<hkern u1="V" u2="&#xbb;" k="16" />
<hkern u1="V" u2="&#x7d;" k="-57" />
<hkern u1="V" u2="v" k="-35" />
<hkern u1="V" u2="q" k="61" />
<hkern u1="V" u2="p" k="37" />
<hkern u1="V" u2="]" k="-57" />
<hkern u1="V" u2="X" k="-55" />
<hkern u1="V" u2="V" k="-66" />
<hkern u1="V" u2="Q" k="4" />
<hkern u1="V" u2="J" k="63" />
<hkern u1="V" u2="F" k="16" />
<hkern u1="V" u2="&#x40;" k="37" />
<hkern u1="V" u2="&#x3f;" k="-29" />
<hkern u1="V" u2="&#x2f;" k="143" />
<hkern u1="V" u2="&#x2a;" k="-39" />
<hkern u1="V" u2="&#x29;" k="-86" />
<hkern u1="V" u2="&#x26;" k="27" />
<hkern u1="W" u2="&#x2122;" k="-49" />
<hkern u1="W" u2="&#x7d;" k="-96" />
<hkern u1="W" u2="x" k="-63" />
<hkern u1="W" u2="v" k="-76" />
<hkern u1="W" u2="p" k="20" />
<hkern u1="W" u2="b" k="4" />
<hkern u1="W" u2="]" k="-78" />
<hkern u1="W" u2="X" k="-66" />
<hkern u1="W" u2="V" k="-66" />
<hkern u1="W" u2="L" k="-45" />
<hkern u1="W" u2="J" k="51" />
<hkern u1="W" u2="H" k="-20" />
<hkern u1="W" u2="F" k="6" />
<hkern u1="W" u2="B" k="-29" />
<hkern u1="W" u2="&#x40;" k="6" />
<hkern u1="W" u2="&#x3f;" k="-39" />
<hkern u1="W" u2="&#x2f;" k="98" />
<hkern u1="W" u2="&#x2a;" k="-59" />
<hkern u1="W" u2="&#x29;" k="-66" />
<hkern u1="X" g2="uniFB02" k="2" />
<hkern u1="X" g2="uniFB01" k="2" />
<hkern u1="X" u2="&#x201d;" k="-39" />
<hkern u1="X" u2="&#x2019;" k="-39" />
<hkern u1="X" u2="&#x178;" k="-25" />
<hkern u1="X" u2="&#x153;" k="27" />
<hkern u1="X" u2="&#x152;" k="37" />
<hkern u1="X" u2="&#xff;" k="16" />
<hkern u1="X" u2="&#xfd;" k="16" />
<hkern u1="X" u2="&#xf8;" k="27" />
<hkern u1="X" u2="&#xf6;" k="27" />
<hkern u1="X" u2="&#xf5;" k="27" />
<hkern u1="X" u2="&#xf4;" k="27" />
<hkern u1="X" u2="&#xf3;" k="27" />
<hkern u1="X" u2="&#xf2;" k="27" />
<hkern u1="X" u2="&#xf1;" k="6" />
<hkern u1="X" u2="&#xf0;" k="27" />
<hkern u1="X" u2="&#xeb;" k="27" />
<hkern u1="X" u2="&#xea;" k="27" />
<hkern u1="X" u2="&#xe9;" k="27" />
<hkern u1="X" u2="&#xe8;" k="27" />
<hkern u1="X" u2="&#xe7;" k="27" />
<hkern u1="X" u2="&#xdd;" k="-25" />
<hkern u1="X" u2="&#xd8;" k="37" />
<hkern u1="X" u2="&#xd6;" k="37" />
<hkern u1="X" u2="&#xd5;" k="37" />
<hkern u1="X" u2="&#xd4;" k="37" />
<hkern u1="X" u2="&#xd3;" k="37" />
<hkern u1="X" u2="&#xd2;" k="37" />
<hkern u1="X" u2="&#xc5;" k="-35" />
<hkern u1="X" u2="&#xc4;" k="-35" />
<hkern u1="X" u2="&#xc3;" k="-35" />
<hkern u1="X" u2="&#xc2;" k="-35" />
<hkern u1="X" u2="&#xc1;" k="-35" />
<hkern u1="X" u2="&#xc0;" k="-35" />
<hkern u1="X" u2="z" k="-45" />
<hkern u1="X" u2="y" k="16" />
<hkern u1="X" u2="w" k="6" />
<hkern u1="X" u2="t" k="12" />
<hkern u1="X" u2="s" k="-25" />
<hkern u1="X" u2="o" k="27" />
<hkern u1="X" u2="n" k="6" />
<hkern u1="X" u2="g" k="27" />
<hkern u1="X" u2="f" k="2" />
<hkern u1="X" u2="e" k="27" />
<hkern u1="X" u2="d" k="27" />
<hkern u1="X" u2="c" k="27" />
<hkern u1="X" u2="Z" k="-35" />
<hkern u1="X" u2="Y" k="-25" />
<hkern u1="X" u2="W" k="-25" />
<hkern u1="X" u2="S" k="-25" />
<hkern u1="X" u2="O" k="37" />
<hkern u1="X" u2="A" k="-35" />
<hkern u1="X" u2="&#xbb;" k="16" />
<hkern u1="X" u2="&#x7d;" k="-49" />
<hkern u1="X" u2="x" k="-20" />
<hkern u1="X" u2="v" k="12" />
<hkern u1="X" u2="q" k="27" />
<hkern u1="X" u2="]" k="-59" />
<hkern u1="X" u2="V" k="-41" />
<hkern u1="X" u2="J" k="-76" />
<hkern u1="Y" u2="&#xbb;" k="16" />
<hkern u1="Y" u2="&#x7d;" k="-70" />
<hkern u1="Y" u2="x" k="16" />
<hkern u1="Y" u2="v" k="-66" />
<hkern u1="Y" u2="q" k="68" />
<hkern u1="Y" u2="p" k="74" />
<hkern u1="Y" u2="h" k="12" />
<hkern u1="Y" u2="b" k="27" />
<hkern u1="Y" u2="]" k="-57" />
<hkern u1="Y" u2="X" k="-41" />
<hkern u1="Y" u2="V" k="-66" />
<hkern u1="Y" u2="Q" k="20" />
<hkern u1="Y" u2="M" k="27" />
<hkern u1="Y" u2="L" k="-66" />
<hkern u1="Y" u2="J" k="88" />
<hkern u1="Y" u2="E" k="16" />
<hkern u1="Y" u2="&#x40;" k="6" />
<hkern u1="Y" u2="&#x3f;" k="-39" />
<hkern u1="Y" u2="&#x2f;" k="156" />
<hkern u1="Y" u2="&#x2a;" k="-39" />
<hkern u1="Y" u2="&#x29;" k="-47" />
<hkern u1="Z" u2="&#x7d;" k="-29" />
<hkern u1="Z" u2="v" k="16" />
<hkern u1="Z" u2="p" k="2" />
<hkern u1="Z" u2="]" k="-35" />
<hkern u1="Z" u2="V" k="-25" />
<hkern u1="Z" u2="J" k="-45" />
<hkern u1="Z" u2="&#x40;" k="16" />
<hkern u1="Z" u2="&#x26;" k="47" />
<hkern u1="[" u2="&#x178;" k="-39" />
<hkern u1="[" u2="&#xdd;" k="-39" />
<hkern u1="[" u2="z" k="-35" />
<hkern u1="[" u2="j" k="-166" />
<hkern u1="[" u2="Z" k="-35" />
<hkern u1="[" u2="Y" k="-39" />
<hkern u1="[" u2="W" k="-59" />
<hkern u1="[" u2="T" k="-80" />
<hkern u1="[" u2="X" k="-49" />
<hkern u1="[" u2="V" k="-59" />
<hkern u1="\" u2="&#xff;" k="47" />
<hkern u1="\" u2="&#xfd;" k="47" />
<hkern u1="\" u2="y" k="47" />
<hkern u1="\" u2="v" k="78" />
<hkern u1="a" u2="&#x2122;" k="119" />
<hkern u1="a" u2="v" k="6" />
<hkern u1="a" u2="\" k="57" />
<hkern u1="a" u2="&#x2a;" k="47" />
<hkern u1="b" u2="&#x201d;" k="68" />
<hkern u1="b" u2="&#x2019;" k="68" />
<hkern u1="b" u2="&#x2122;" k="109" />
<hkern u1="c" u2="&#x2122;" k="16" />
<hkern u1="c" u2="x" k="-33" />
<hkern u1="c" u2="v" k="-41" />
<hkern u1="c" u2="\" k="16" />
<hkern u1="e" u2="&#x2122;" k="57" />
<hkern u1="e" u2="]" k="-25" />
<hkern u1="e" u2="\" k="78" />
<hkern u1="f" g2="uniFB02" k="-25" />
<hkern u1="f" g2="uniFB01" k="-25" />
<hkern u1="f" u2="&#x2026;" k="109" />
<hkern u1="f" u2="&#x201e;" k="109" />
<hkern u1="f" u2="&#x201d;" k="-137" />
<hkern u1="f" u2="&#x201a;" k="109" />
<hkern u1="f" u2="&#x2019;" k="-137" />
<hkern u1="f" u2="&#x153;" k="27" />
<hkern u1="f" u2="&#xff;" k="-45" />
<hkern u1="f" u2="&#xfd;" k="-45" />
<hkern u1="f" u2="&#xf8;" k="27" />
<hkern u1="f" u2="&#xf6;" k="27" />
<hkern u1="f" u2="&#xf5;" k="27" />
<hkern u1="f" u2="&#xf4;" k="27" />
<hkern u1="f" u2="&#xf3;" k="27" />
<hkern u1="f" u2="&#xf2;" k="27" />
<hkern u1="f" u2="&#xf0;" k="27" />
<hkern u1="f" u2="&#xe6;" k="31" />
<hkern u1="f" u2="&#xe5;" k="31" />
<hkern u1="f" u2="&#xe4;" k="31" />
<hkern u1="f" u2="&#xe3;" k="31" />
<hkern u1="f" u2="&#xe2;" k="31" />
<hkern u1="f" u2="&#xe1;" k="31" />
<hkern u1="f" u2="&#xe0;" k="31" />
<hkern u1="f" u2="y" k="-45" />
<hkern u1="f" u2="w" k="-41" />
<hkern u1="f" u2="t" k="-25" />
<hkern u1="f" u2="o" k="27" />
<hkern u1="f" u2="l" k="-35" />
<hkern u1="f" u2="f" k="-25" />
<hkern u1="f" u2="a" k="31" />
<hkern u1="f" u2="&#x2e;" k="109" />
<hkern u1="f" u2="&#x2c;" k="109" />
<hkern u1="f" u2="&#x27;" k="-127" />
<hkern u1="f" u2="&#x22;" k="-127" />
<hkern u1="f" u2="&#x2122;" k="-137" />
<hkern u1="f" u2="&#xef;" k="-176" />
<hkern u1="f" u2="&#xee;" k="-141" />
<hkern u1="f" u2="&#xed;" k="-31" />
<hkern u1="f" u2="&#xec;" k="-264" />
<hkern u1="f" u2="&#x7d;" k="-195" />
<hkern u1="f" u2="v" k="-43" />
<hkern u1="f" u2="k" k="-41" />
<hkern u1="f" u2="h" k="-41" />
<hkern u1="f" u2="b" k="-41" />
<hkern u1="f" u2="]" k="-197" />
<hkern u1="f" u2="\" k="-96" />
<hkern u1="f" u2="&#x3f;" k="-158" />
<hkern u1="f" u2="&#x2a;" k="-147" />
<hkern u1="f" u2="&#x29;" k="-137" />
<hkern u1="g" u2="&#x2122;" k="68" />
<hkern u1="g" u2="]" k="-66" />
<hkern u1="g" u2="\" k="27" />
<hkern u1="g" u2="&#x2a;" k="55" />
<hkern u1="g" u2="&#x29;" k="-86" />
<hkern u1="h" u2="&#x2122;" k="109" />
<hkern u1="i" u2="&#x2f;" k="16" />
<hkern u1="i" u2="&#x2a;" k="-25" />
<hkern u1="j" u2="\" k="20" />
<hkern u1="j" u2="&#x2f;" k="16" />
<hkern u1="k" u2="v" k="-20" />
<hkern u1="k" u2="q" k="33" />
<hkern u1="l" u2="&#x2122;" k="16" />
<hkern u1="l" u2="&#x7d;" k="-88" />
<hkern u1="l" u2="x" k="-35" />
<hkern u1="l" u2="v" k="4" />
<hkern u1="l" u2="&#x2f;" k="-59" />
<hkern u1="l" u2="&#x2a;" k="29" />
<hkern u1="l" u2="&#x29;" k="-57" />
<hkern u1="m" u2="&#x201d;" k="57" />
<hkern u1="m" u2="&#x2019;" k="57" />
<hkern u1="m" u2="&#x2122;" k="109" />
<hkern u1="n" u2="&#x2122;" k="109" />
<hkern u1="n" u2="v" k="6" />
<hkern u1="n" u2="]" k="-25" />
<hkern u1="n" u2="\" k="66" />
<hkern u1="n" u2="&#x2a;" k="125" />
<hkern u1="o" u2="&#x2122;" k="109" />
<hkern u1="o" u2="x" k="2" />
<hkern u1="o" u2="v" k="6" />
<hkern u1="o" u2="\" k="86" />
<hkern u1="o" u2="&#x2a;" k="68" />
<hkern u1="p" u2="&#x201d;" k="88" />
<hkern u1="p" u2="&#x2019;" k="88" />
<hkern u1="p" u2="&#x2122;" k="109" />
<hkern u1="q" u2="j" k="-156" />
<hkern u1="q" u2="&#x2122;" k="57" />
<hkern u1="q" u2="]" k="-55" />
<hkern u1="q" u2="\" k="27" />
<hkern u1="q" u2="&#x2a;" k="66" />
<hkern u1="r" u2="x" k="-25" />
<hkern u1="r" u2="v" k="-45" />
<hkern u1="r" u2="\" k="31" />
<hkern u1="s" u2="&#x2122;" k="57" />
<hkern u1="s" u2="\" k="47" />
<hkern u1="s" u2="&#x2a;" k="55" />
<hkern u1="t" u2="&#x7d;" k="-98" />
<hkern u1="t" u2="x" k="-33" />
<hkern u1="t" u2="v" k="-25" />
<hkern u1="t" u2="]" k="-88" />
<hkern u1="t" u2="\" k="16" />
<hkern u1="t" u2="&#x2f;" k="-57" />
<hkern u1="t" u2="&#x2a;" k="23" />
<hkern u1="t" u2="&#x29;" k="-86" />
<hkern u1="u" u2="&#x2122;" k="33" />
<hkern u1="u" u2="\" k="16" />
<hkern u1="u" u2="&#x2a;" k="47" />
<hkern u1="v" g2="uniFB02" k="-41" />
<hkern u1="v" g2="uniFB01" k="-41" />
<hkern u1="v" u2="&#x2026;" k="109" />
<hkern u1="v" u2="&#x201e;" k="109" />
<hkern u1="v" u2="&#x201d;" k="-39" />
<hkern u1="v" u2="&#x201a;" k="109" />
<hkern u1="v" u2="&#x2019;" k="-39" />
<hkern u1="v" u2="&#x153;" k="2" />
<hkern u1="v" u2="&#xff;" k="-45" />
<hkern u1="v" u2="&#xfd;" k="-45" />
<hkern u1="v" u2="&#xf8;" k="2" />
<hkern u1="v" u2="&#xf6;" k="2" />
<hkern u1="v" u2="&#xf5;" k="2" />
<hkern u1="v" u2="&#xf4;" k="2" />
<hkern u1="v" u2="&#xf3;" k="2" />
<hkern u1="v" u2="&#xf2;" k="2" />
<hkern u1="v" u2="&#xf0;" k="2" />
<hkern u1="v" u2="y" k="-45" />
<hkern u1="v" u2="w" k="-45" />
<hkern u1="v" u2="t" k="-45" />
<hkern u1="v" u2="o" k="2" />
<hkern u1="v" u2="f" k="-41" />
<hkern u1="v" u2="&#x3b;" k="37" />
<hkern u1="v" u2="&#x3a;" k="37" />
<hkern u1="v" u2="&#x2e;" k="109" />
<hkern u1="v" u2="&#x2c;" k="109" />
<hkern u1="v" u2="x" k="-45" />
<hkern u1="v" u2="v" k="-45" />
<hkern u1="w" u2="x" k="-25" />
<hkern u1="w" u2="v" k="-45" />
<hkern u1="x" u2="&#x201d;" k="-57" />
<hkern u1="x" u2="&#x2019;" k="-57" />
<hkern u1="x" u2="&#x153;" k="2" />
<hkern u1="x" u2="&#xff;" k="-45" />
<hkern u1="x" u2="&#xfd;" k="-45" />
<hkern u1="x" u2="&#xf8;" k="2" />
<hkern u1="x" u2="&#xf6;" k="2" />
<hkern u1="x" u2="&#xf5;" k="2" />
<hkern u1="x" u2="&#xf4;" k="2" />
<hkern u1="x" u2="&#xf3;" k="2" />
<hkern u1="x" u2="&#xf2;" k="2" />
<hkern u1="x" u2="&#xf0;" k="2" />
<hkern u1="x" u2="z" k="-25" />
<hkern u1="x" u2="y" k="-45" />
<hkern u1="x" u2="w" k="-45" />
<hkern u1="x" u2="t" k="-25" />
<hkern u1="x" u2="s" k="-20" />
<hkern u1="x" u2="o" k="2" />
<hkern u1="x" u2="&#x2122;" k="37" />
<hkern u1="x" u2="x" k="-25" />
<hkern u1="x" u2="v" k="-45" />
<hkern u1="x" u2="]" k="-100" />
<hkern u1="x" u2="\" k="37" />
<hkern u1="y" u2="&#x2122;" k="27" />
<hkern u1="y" u2="x" k="-41" />
<hkern u1="y" u2="v" k="-45" />
<hkern u1="y" u2="&#x2c;" k="135" />
<hkern u1="z" u2="&#x2122;" k="47" />
<hkern u1="z" u2="v" k="-20" />
<hkern u1="z" u2="]" k="-35" />
<hkern u1="&#x7b;" u2="&#x178;" k="-68" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-68" />
<hkern u1="&#x7b;" u2="j" k="-233" />
<hkern u1="&#x7b;" u2="Z" k="-49" />
<hkern u1="&#x7b;" u2="Y" k="-68" />
<hkern u1="&#x7b;" u2="W" k="-57" />
<hkern u1="&#x7b;" u2="T" k="-78" />
<hkern u1="&#x7b;" u2="X" k="-57" />
<hkern u1="&#x7b;" u2="V" k="-57" />
<hkern u1="&#x7b;" u2="J" k="-68" />
<hkern u1="&#xab;" u2="&#xc5;" k="-35" />
<hkern u1="&#xab;" u2="&#xc4;" k="-35" />
<hkern u1="&#xab;" u2="&#xc3;" k="-35" />
<hkern u1="&#xab;" u2="&#xc2;" k="-35" />
<hkern u1="&#xab;" u2="&#xc1;" k="-35" />
<hkern u1="&#xab;" u2="&#xc0;" k="-35" />
<hkern u1="&#xab;" u2="A" k="-35" />
<hkern u1="&#xab;" u2="X" k="16" />
<hkern u1="&#xab;" u2="V" k="16" />
<hkern u1="&#xbf;" g2="uniFB02" k="-57" />
<hkern u1="&#xbf;" g2="uniFB01" k="-57" />
<hkern u1="&#xbf;" u2="&#xff;" k="-39" />
<hkern u1="&#xbf;" u2="&#xfd;" k="-39" />
<hkern u1="&#xbf;" u2="y" k="-39" />
<hkern u1="&#xbf;" u2="w" k="-47" />
<hkern u1="&#xbf;" u2="t" k="-39" />
<hkern u1="&#xbf;" u2="j" k="-301" />
<hkern u1="&#xbf;" u2="f" k="-57" />
<hkern u1="&#xbf;" u2="v" k="-57" />
<hkern u1="&#xbf;" u2="p" k="-57" />
<hkern u1="&#xc0;" u2="&#x2122;" k="221" />
<hkern u1="&#xc0;" u2="&#x203a;" k="-35" />
<hkern u1="&#xc0;" u2="&#xbb;" k="-35" />
<hkern u1="&#xc0;" u2="v" k="68" />
<hkern u1="&#xc0;" u2="q" k="41" />
<hkern u1="&#xc0;" u2="p" k="12" />
<hkern u1="&#xc0;" u2="b" k="47" />
<hkern u1="&#xc0;" u2="\" k="139" />
<hkern u1="&#xc0;" u2="X" k="-35" />
<hkern u1="&#xc0;" u2="V" k="94" />
<hkern u1="&#xc0;" u2="Q" k="43" />
<hkern u1="&#xc0;" u2="J" k="-55" />
<hkern u1="&#xc0;" u2="&#x3f;" k="47" />
<hkern u1="&#xc0;" u2="&#x2a;" k="139" />
<hkern u1="&#xc1;" u2="&#x2122;" k="221" />
<hkern u1="&#xc1;" u2="&#x203a;" k="-35" />
<hkern u1="&#xc1;" u2="&#xbb;" k="-35" />
<hkern u1="&#xc1;" u2="v" k="68" />
<hkern u1="&#xc1;" u2="q" k="41" />
<hkern u1="&#xc1;" u2="p" k="12" />
<hkern u1="&#xc1;" u2="b" k="47" />
<hkern u1="&#xc1;" u2="\" k="139" />
<hkern u1="&#xc1;" u2="X" k="-35" />
<hkern u1="&#xc1;" u2="V" k="94" />
<hkern u1="&#xc1;" u2="Q" k="43" />
<hkern u1="&#xc1;" u2="J" k="-55" />
<hkern u1="&#xc1;" u2="&#x3f;" k="47" />
<hkern u1="&#xc1;" u2="&#x2a;" k="139" />
<hkern u1="&#xc2;" u2="&#x2122;" k="221" />
<hkern u1="&#xc2;" u2="&#x203a;" k="-35" />
<hkern u1="&#xc2;" u2="&#xbb;" k="-35" />
<hkern u1="&#xc2;" u2="v" k="68" />
<hkern u1="&#xc2;" u2="q" k="41" />
<hkern u1="&#xc2;" u2="p" k="12" />
<hkern u1="&#xc2;" u2="b" k="47" />
<hkern u1="&#xc2;" u2="\" k="139" />
<hkern u1="&#xc2;" u2="X" k="-35" />
<hkern u1="&#xc2;" u2="V" k="94" />
<hkern u1="&#xc2;" u2="Q" k="43" />
<hkern u1="&#xc2;" u2="J" k="-55" />
<hkern u1="&#xc2;" u2="&#x3f;" k="47" />
<hkern u1="&#xc2;" u2="&#x2a;" k="139" />
<hkern u1="&#xc3;" u2="&#x2122;" k="221" />
<hkern u1="&#xc3;" u2="&#x203a;" k="-35" />
<hkern u1="&#xc3;" u2="&#xbb;" k="-35" />
<hkern u1="&#xc3;" u2="v" k="68" />
<hkern u1="&#xc3;" u2="q" k="41" />
<hkern u1="&#xc3;" u2="p" k="12" />
<hkern u1="&#xc3;" u2="b" k="47" />
<hkern u1="&#xc3;" u2="\" k="139" />
<hkern u1="&#xc3;" u2="X" k="-35" />
<hkern u1="&#xc3;" u2="V" k="94" />
<hkern u1="&#xc3;" u2="Q" k="43" />
<hkern u1="&#xc3;" u2="J" k="-55" />
<hkern u1="&#xc3;" u2="&#x3f;" k="47" />
<hkern u1="&#xc3;" u2="&#x2a;" k="139" />
<hkern u1="&#xc4;" u2="&#x2122;" k="221" />
<hkern u1="&#xc4;" u2="&#x203a;" k="-35" />
<hkern u1="&#xc4;" u2="&#xbb;" k="-35" />
<hkern u1="&#xc4;" u2="v" k="68" />
<hkern u1="&#xc4;" u2="q" k="41" />
<hkern u1="&#xc4;" u2="p" k="12" />
<hkern u1="&#xc4;" u2="b" k="47" />
<hkern u1="&#xc4;" u2="\" k="139" />
<hkern u1="&#xc4;" u2="X" k="-35" />
<hkern u1="&#xc4;" u2="V" k="94" />
<hkern u1="&#xc4;" u2="Q" k="43" />
<hkern u1="&#xc4;" u2="J" k="-55" />
<hkern u1="&#xc4;" u2="&#x3f;" k="47" />
<hkern u1="&#xc4;" u2="&#x2a;" k="139" />
<hkern u1="&#xc5;" u2="&#x2122;" k="221" />
<hkern u1="&#xc5;" u2="&#x203a;" k="-35" />
<hkern u1="&#xc5;" u2="&#xbb;" k="-35" />
<hkern u1="&#xc5;" u2="v" k="68" />
<hkern u1="&#xc5;" u2="q" k="41" />
<hkern u1="&#xc5;" u2="p" k="12" />
<hkern u1="&#xc5;" u2="b" k="47" />
<hkern u1="&#xc5;" u2="\" k="139" />
<hkern u1="&#xc5;" u2="X" k="-35" />
<hkern u1="&#xc5;" u2="V" k="94" />
<hkern u1="&#xc5;" u2="Q" k="43" />
<hkern u1="&#xc5;" u2="J" k="-55" />
<hkern u1="&#xc5;" u2="&#x3f;" k="47" />
<hkern u1="&#xc5;" u2="&#x2a;" k="139" />
<hkern u1="&#xc6;" u2="V" k="-14" />
<hkern u1="&#xc6;" u2="Q" k="16" />
<hkern u1="&#xc6;" u2="J" k="-37" />
<hkern u1="&#xc6;" u2="B" k="6" />
<hkern u1="&#xc7;" u2="x" k="-25" />
<hkern u1="&#xc7;" u2="q" k="37" />
<hkern u1="&#xc7;" u2="X" k="-37" />
<hkern u1="&#xc7;" u2="V" k="-35" />
<hkern u1="&#xc7;" u2="J" k="-68" />
<hkern u1="&#xc7;" u2="&#x29;" k="-76" />
<hkern u1="&#xc8;" u2="V" k="-14" />
<hkern u1="&#xc8;" u2="Q" k="16" />
<hkern u1="&#xc8;" u2="J" k="-37" />
<hkern u1="&#xc8;" u2="B" k="6" />
<hkern u1="&#xc9;" u2="V" k="-14" />
<hkern u1="&#xc9;" u2="Q" k="16" />
<hkern u1="&#xc9;" u2="J" k="-37" />
<hkern u1="&#xc9;" u2="B" k="6" />
<hkern u1="&#xca;" u2="V" k="-14" />
<hkern u1="&#xca;" u2="Q" k="16" />
<hkern u1="&#xca;" u2="J" k="-37" />
<hkern u1="&#xca;" u2="B" k="6" />
<hkern u1="&#xcb;" u2="V" k="-14" />
<hkern u1="&#xcb;" u2="Q" k="16" />
<hkern u1="&#xcb;" u2="J" k="-37" />
<hkern u1="&#xcb;" u2="B" k="6" />
<hkern u1="&#xd0;" u2="X" k="41" />
<hkern u1="&#xd2;" u2="X" k="37" />
<hkern u1="&#xd2;" u2="V" k="27" />
<hkern u1="&#xd3;" u2="X" k="37" />
<hkern u1="&#xd3;" u2="V" k="27" />
<hkern u1="&#xd4;" u2="X" k="37" />
<hkern u1="&#xd4;" u2="V" k="27" />
<hkern u1="&#xd5;" u2="X" k="37" />
<hkern u1="&#xd5;" u2="V" k="27" />
<hkern u1="&#xd6;" u2="X" k="37" />
<hkern u1="&#xd6;" u2="V" k="27" />
<hkern u1="&#xd8;" u2="X" k="37" />
<hkern u1="&#xd8;" u2="V" k="27" />
<hkern u1="&#xd9;" u2="v" k="-45" />
<hkern u1="&#xda;" u2="v" k="-45" />
<hkern u1="&#xdb;" u2="v" k="-45" />
<hkern u1="&#xdc;" u2="v" k="-45" />
<hkern u1="&#xdd;" u2="&#xbb;" k="16" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-70" />
<hkern u1="&#xdd;" u2="x" k="16" />
<hkern u1="&#xdd;" u2="v" k="-66" />
<hkern u1="&#xdd;" u2="q" k="68" />
<hkern u1="&#xdd;" u2="p" k="74" />
<hkern u1="&#xdd;" u2="h" k="12" />
<hkern u1="&#xdd;" u2="b" k="27" />
<hkern u1="&#xdd;" u2="]" k="-57" />
<hkern u1="&#xdd;" u2="X" k="-41" />
<hkern u1="&#xdd;" u2="V" k="-66" />
<hkern u1="&#xdd;" u2="Q" k="20" />
<hkern u1="&#xdd;" u2="M" k="27" />
<hkern u1="&#xdd;" u2="L" k="-66" />
<hkern u1="&#xdd;" u2="J" k="88" />
<hkern u1="&#xdd;" u2="E" k="16" />
<hkern u1="&#xdd;" u2="&#x40;" k="6" />
<hkern u1="&#xdd;" u2="&#x3f;" k="-39" />
<hkern u1="&#xdd;" u2="&#x2f;" k="156" />
<hkern u1="&#xdd;" u2="&#x2a;" k="-39" />
<hkern u1="&#xdd;" u2="&#x29;" k="-47" />
<hkern u1="&#xdf;" u2="&#xff;" k="12" />
<hkern u1="&#xdf;" u2="&#xfd;" k="12" />
<hkern u1="&#xdf;" u2="y" k="12" />
<hkern u1="&#xdf;" u2="x" k="16" />
<hkern u1="&#xdf;" u2="v" k="12" />
<hkern u1="&#xe0;" u2="&#x2122;" k="119" />
<hkern u1="&#xe0;" u2="v" k="6" />
<hkern u1="&#xe0;" u2="\" k="57" />
<hkern u1="&#xe0;" u2="&#x2a;" k="47" />
<hkern u1="&#xe1;" u2="&#x2122;" k="119" />
<hkern u1="&#xe1;" u2="v" k="6" />
<hkern u1="&#xe1;" u2="\" k="57" />
<hkern u1="&#xe1;" u2="&#x2a;" k="47" />
<hkern u1="&#xe2;" u2="&#x2122;" k="119" />
<hkern u1="&#xe2;" u2="v" k="6" />
<hkern u1="&#xe2;" u2="\" k="57" />
<hkern u1="&#xe2;" u2="&#x2a;" k="47" />
<hkern u1="&#xe3;" u2="&#x2122;" k="119" />
<hkern u1="&#xe3;" u2="v" k="6" />
<hkern u1="&#xe3;" u2="\" k="57" />
<hkern u1="&#xe3;" u2="&#x2a;" k="47" />
<hkern u1="&#xe4;" u2="&#x2122;" k="119" />
<hkern u1="&#xe4;" u2="v" k="6" />
<hkern u1="&#xe4;" u2="\" k="57" />
<hkern u1="&#xe4;" u2="&#x2a;" k="47" />
<hkern u1="&#xe5;" u2="&#x2122;" k="119" />
<hkern u1="&#xe5;" u2="v" k="6" />
<hkern u1="&#xe5;" u2="\" k="57" />
<hkern u1="&#xe5;" u2="&#x2a;" k="47" />
<hkern u1="&#xe7;" u2="&#x2122;" k="16" />
<hkern u1="&#xe7;" u2="x" k="-33" />
<hkern u1="&#xe7;" u2="v" k="-41" />
<hkern u1="&#xe7;" u2="\" k="16" />
<hkern u1="&#xe8;" u2="&#x2122;" k="57" />
<hkern u1="&#xe8;" u2="]" k="-25" />
<hkern u1="&#xe8;" u2="\" k="78" />
<hkern u1="&#xe9;" u2="&#x2122;" k="57" />
<hkern u1="&#xe9;" u2="]" k="-25" />
<hkern u1="&#xe9;" u2="\" k="78" />
<hkern u1="&#xea;" u2="&#x2122;" k="57" />
<hkern u1="&#xea;" u2="]" k="-25" />
<hkern u1="&#xea;" u2="\" k="78" />
<hkern u1="&#xeb;" u2="&#x2122;" k="57" />
<hkern u1="&#xeb;" u2="]" k="-25" />
<hkern u1="&#xeb;" u2="\" k="78" />
<hkern u1="&#xec;" u2="&#x2f;" k="16" />
<hkern u1="&#xec;" u2="&#x2a;" k="-25" />
<hkern u1="&#xed;" u2="&#x2f;" k="16" />
<hkern u1="&#xed;" u2="&#x2a;" k="-25" />
<hkern u1="&#xee;" u2="&#x2f;" k="16" />
<hkern u1="&#xee;" u2="&#x2a;" k="-25" />
<hkern u1="&#xef;" u2="&#x2f;" k="16" />
<hkern u1="&#xef;" u2="&#x2a;" k="-25" />
<hkern u1="&#xf1;" u2="&#x2122;" k="109" />
<hkern u1="&#xf1;" u2="v" k="6" />
<hkern u1="&#xf1;" u2="]" k="-25" />
<hkern u1="&#xf1;" u2="\" k="66" />
<hkern u1="&#xf1;" u2="&#x2a;" k="125" />
<hkern u1="&#xf2;" u2="&#x2122;" k="109" />
<hkern u1="&#xf2;" u2="x" k="2" />
<hkern u1="&#xf2;" u2="v" k="6" />
<hkern u1="&#xf2;" u2="\" k="86" />
<hkern u1="&#xf2;" u2="&#x2a;" k="68" />
<hkern u1="&#xf3;" u2="&#x2122;" k="109" />
<hkern u1="&#xf3;" u2="x" k="2" />
<hkern u1="&#xf3;" u2="v" k="6" />
<hkern u1="&#xf3;" u2="\" k="86" />
<hkern u1="&#xf3;" u2="&#x2a;" k="68" />
<hkern u1="&#xf4;" u2="&#x2122;" k="109" />
<hkern u1="&#xf4;" u2="x" k="2" />
<hkern u1="&#xf4;" u2="v" k="6" />
<hkern u1="&#xf4;" u2="\" k="86" />
<hkern u1="&#xf4;" u2="&#x2a;" k="68" />
<hkern u1="&#xf5;" u2="&#x2122;" k="109" />
<hkern u1="&#xf5;" u2="x" k="2" />
<hkern u1="&#xf5;" u2="v" k="6" />
<hkern u1="&#xf5;" u2="\" k="86" />
<hkern u1="&#xf5;" u2="&#x2a;" k="68" />
<hkern u1="&#xf6;" u2="&#x2122;" k="109" />
<hkern u1="&#xf6;" u2="x" k="2" />
<hkern u1="&#xf6;" u2="v" k="6" />
<hkern u1="&#xf6;" u2="\" k="86" />
<hkern u1="&#xf6;" u2="&#x2a;" k="68" />
<hkern u1="&#xf8;" u2="&#x2122;" k="109" />
<hkern u1="&#xf8;" u2="x" k="2" />
<hkern u1="&#xf8;" u2="v" k="6" />
<hkern u1="&#xf8;" u2="\" k="86" />
<hkern u1="&#xf8;" u2="&#x2a;" k="68" />
<hkern u1="&#xf9;" u2="&#x2122;" k="33" />
<hkern u1="&#xf9;" u2="\" k="16" />
<hkern u1="&#xf9;" u2="&#x2a;" k="47" />
<hkern u1="&#xfa;" u2="&#x2122;" k="33" />
<hkern u1="&#xfa;" u2="\" k="16" />
<hkern u1="&#xfa;" u2="&#x2a;" k="47" />
<hkern u1="&#xfb;" u2="&#x2122;" k="33" />
<hkern u1="&#xfb;" u2="\" k="16" />
<hkern u1="&#xfb;" u2="&#x2a;" k="47" />
<hkern u1="&#xfc;" u2="&#x2122;" k="33" />
<hkern u1="&#xfc;" u2="\" k="16" />
<hkern u1="&#xfc;" u2="&#x2a;" k="47" />
<hkern u1="&#xfd;" u2="&#x2122;" k="27" />
<hkern u1="&#xfd;" u2="x" k="-41" />
<hkern u1="&#xfd;" u2="v" k="-45" />
<hkern u1="&#xfd;" u2="&#x2c;" k="135" />
<hkern u1="&#xff;" u2="&#x2122;" k="27" />
<hkern u1="&#xff;" u2="x" k="-41" />
<hkern u1="&#xff;" u2="v" k="-45" />
<hkern u1="&#xff;" u2="&#x2c;" k="135" />
<hkern u1="&#x152;" u2="V" k="-14" />
<hkern u1="&#x152;" u2="Q" k="16" />
<hkern u1="&#x152;" u2="J" k="-37" />
<hkern u1="&#x152;" u2="B" k="6" />
<hkern u1="&#x178;" u2="&#xbb;" k="16" />
<hkern u1="&#x178;" u2="&#x7d;" k="-70" />
<hkern u1="&#x178;" u2="x" k="16" />
<hkern u1="&#x178;" u2="v" k="-66" />
<hkern u1="&#x178;" u2="q" k="68" />
<hkern u1="&#x178;" u2="p" k="74" />
<hkern u1="&#x178;" u2="h" k="12" />
<hkern u1="&#x178;" u2="b" k="27" />
<hkern u1="&#x178;" u2="]" k="-57" />
<hkern u1="&#x178;" u2="X" k="-41" />
<hkern u1="&#x178;" u2="V" k="-66" />
<hkern u1="&#x178;" u2="Q" k="20" />
<hkern u1="&#x178;" u2="M" k="27" />
<hkern u1="&#x178;" u2="L" k="-66" />
<hkern u1="&#x178;" u2="J" k="88" />
<hkern u1="&#x178;" u2="E" k="16" />
<hkern u1="&#x178;" u2="&#x40;" k="6" />
<hkern u1="&#x178;" u2="&#x3f;" k="-39" />
<hkern u1="&#x178;" u2="&#x2f;" k="156" />
<hkern u1="&#x178;" u2="&#x2a;" k="-39" />
<hkern u1="&#x178;" u2="&#x29;" k="-47" />
<hkern u1="&#x2018;" u2="v" k="-39" />
<hkern u1="&#x2018;" u2="V" k="-31" />
<hkern u1="&#x2018;" u2="M" k="37" />
<hkern u1="&#x2018;" u2="J" k="135" />
<hkern u1="&#x201a;" u2="v" k="109" />
<hkern u1="&#x201a;" u2="V" k="78" />
<hkern u1="&#x201a;" u2="J" k="-49" />
<hkern u1="&#x201c;" u2="v" k="-39" />
<hkern u1="&#x201c;" u2="V" k="-31" />
<hkern u1="&#x201c;" u2="M" k="37" />
<hkern u1="&#x201c;" u2="J" k="135" />
<hkern u1="&#x201e;" u2="v" k="109" />
<hkern u1="&#x201e;" u2="V" k="78" />
<hkern u1="&#x201e;" u2="J" k="-49" />
<hkern u1="&#x2026;" u2="v" k="109" />
<hkern u1="&#x2026;" u2="V" k="78" />
<hkern u1="&#x2026;" u2="J" k="-49" />
<hkern u1="&#x2039;" u2="&#xc5;" k="-35" />
<hkern u1="&#x2039;" u2="&#xc4;" k="-35" />
<hkern u1="&#x2039;" u2="&#xc3;" k="-35" />
<hkern u1="&#x2039;" u2="&#xc2;" k="-35" />
<hkern u1="&#x2039;" u2="&#xc1;" k="-35" />
<hkern u1="&#x2039;" u2="&#xc0;" k="-35" />
<hkern u1="&#x2039;" u2="A" k="-35" />
<hkern g1="uniFB01" u2="&#x2f;" k="16" />
<hkern g1="uniFB01" u2="&#x2a;" k="-25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="49" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="43" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="156" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="74" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="147" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="AE" 	k="-25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="f,uniFB01,uniFB02" 	k="47" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="l" 	k="33" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,ccedilla" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="d" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="g" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,Ccedilla" 	k="43" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="156" />
<hkern g1="C,Ccedilla" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="6" />
<hkern g1="C,Ccedilla" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="6" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="-45" />
<hkern g1="C,Ccedilla" 	g2="W" 	k="-55" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="-45" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-55" />
<hkern g1="C,Ccedilla" 	g2="c,ccedilla" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="d" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="-35" />
<hkern g1="C,Ccedilla" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-35" />
<hkern g1="D,Eth" 	g2="T" 	k="57" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="D,Eth" 	g2="AE" 	k="72" />
<hkern g1="D,Eth" 	g2="quoteright,quotedblright" 	k="49" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="T" 	k="-25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="W" 	k="-14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Y,Yacute,Ydieresis" 	k="-25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,Ccedilla" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Z" 	k="-25" />
<hkern g1="G" 	g2="T" 	k="55" />
<hkern g1="K" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="K" 	g2="w" 	k="27" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="12" />
<hkern g1="K" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="27" />
<hkern g1="K" 	g2="T" 	k="-66" />
<hkern g1="K" 	g2="W" 	k="-35" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="-55" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-41" />
<hkern g1="K" 	g2="AE" 	k="-39" />
<hkern g1="K" 	g2="quoteright,quotedblright" 	k="-59" />
<hkern g1="K" 	g2="Z" 	k="-66" />
<hkern g1="K" 	g2="s" 	k="-25" />
<hkern g1="K" 	g2="z" 	k="-29" />
<hkern g1="K" 	g2="S" 	k="-37" />
<hkern g1="L" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="L" 	g2="T" 	k="119" />
<hkern g1="L" 	g2="W" 	k="57" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="94" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-55" />
<hkern g1="L" 	g2="AE" 	k="-49" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="184" />
<hkern g1="L" 	g2="Z" 	k="-55" />
<hkern g1="L" 	g2="z" 	k="-41" />
<hkern g1="L" 	g2="S" 	k="-41" />
<hkern g1="L" 	g2="j" 	k="-55" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="57" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="8" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="33" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="57" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="27" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="t" 	k="-14" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="12" />
<hkern g1="R" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="12" />
<hkern g1="R" 	g2="w" 	k="-25" />
<hkern g1="R" 	g2="y,yacute,ydieresis" 	k="-33" />
<hkern g1="R" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="12" />
<hkern g1="R" 	g2="T" 	k="6" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="6" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="R" 	g2="Z" 	k="-51" />
<hkern g1="R" 	g2="z" 	k="-25" />
<hkern g1="R" 	g2="S" 	k="-14" />
<hkern g1="S" 	g2="w" 	k="2" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="4" />
<hkern g1="S" 	g2="W" 	k="10" />
<hkern g1="S" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="S" 	g2="c,ccedilla" 	k="6" />
<hkern g1="T" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="143" />
<hkern g1="T" 	g2="w" 	k="68" />
<hkern g1="T" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="57" />
<hkern g1="T" 	g2="T" 	k="-82" />
<hkern g1="T" 	g2="W" 	k="-82" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-82" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="115" />
<hkern g1="T" 	g2="AE" 	k="197" />
<hkern g1="T" 	g2="f,uniFB01,uniFB02" 	k="37" />
<hkern g1="T" 	g2="l" 	k="33" />
<hkern g1="T" 	g2="t" 	k="37" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="129" />
<hkern g1="T" 	g2="c,ccedilla" 	k="131" />
<hkern g1="T" 	g2="d" 	k="131" />
<hkern g1="T" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="131" />
<hkern g1="T" 	g2="g" 	k="131" />
<hkern g1="T" 	g2="C,Ccedilla" 	k="57" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-88" />
<hkern g1="T" 	g2="Z" 	k="-45" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="55" />
<hkern g1="T" 	g2="s" 	k="123" />
<hkern g1="T" 	g2="z" 	k="98" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="150" />
<hkern g1="T" 	g2="n,ntilde" 	k="129" />
<hkern g1="T" 	g2="colon,semicolon" 	k="27" />
<hkern g1="T" 	g2="r" 	k="129" />
<hkern g1="T" 	g2="G" 	k="57" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="w" 	k="-45" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="y,yacute,ydieresis" 	k="-45" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="W" 	k="-12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="27" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="t" 	k="-14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="31" />
<hkern g1="W" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="W" 	g2="w" 	k="-76" />
<hkern g1="W" 	g2="y,yacute,ydieresis" 	k="-86" />
<hkern g1="W" 	g2="T" 	k="-82" />
<hkern g1="W" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-31" />
<hkern g1="W" 	g2="W" 	k="-66" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-66" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="W" 	g2="AE" 	k="111" />
<hkern g1="W" 	g2="f,uniFB01,uniFB02" 	k="-41" />
<hkern g1="W" 	g2="t" 	k="-55" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-80" />
<hkern g1="W" 	g2="Z" 	k="-57" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="74" />
<hkern g1="W" 	g2="s" 	k="18" />
<hkern g1="W" 	g2="S" 	k="-49" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="37" />
<hkern g1="W" 	g2="n,ntilde" 	k="8" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="-76" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="-70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-86" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-66" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-66" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="121" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="186" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,uniFB01,uniFB02" 	k="6" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="25" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,ccedilla" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="C,Ccedilla" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-57" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="-35" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="170" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="n,ntilde" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="G" 	k="20" />
<hkern g1="Z" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="Z" 	g2="w" 	k="16" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="8" />
<hkern g1="Z" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="Z" 	g2="T" 	k="-37" />
<hkern g1="Z" 	g2="W" 	k="-35" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="-35" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="37" />
<hkern g1="Z" 	g2="quoteright,quotedblright" 	k="-39" />
<hkern g1="Z" 	g2="s" 	k="16" />
<hkern g1="Z" 	g2="z" 	k="-14" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="2" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="4" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="59" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-41" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="-41" />
<hkern g1="c,ccedilla" 	g2="f,uniFB01,uniFB02" 	k="-14" />
<hkern g1="c,ccedilla" 	g2="t" 	k="-29" />
<hkern g1="c,ccedilla" 	g2="quoteright,quotedblright" 	k="-29" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-14" />
<hkern g1="e,egrave,eacute,ecircumflex,edieresis" 	g2="quoteright,quotedblright" 	k="49" />
<hkern g1="g" 	g2="y,yacute,ydieresis" 	k="-35" />
<hkern g1="g" 	g2="quoteright,quotedblright" 	k="29" />
<hkern g1="g" 	g2="j" 	k="-106" />
<hkern g1="h" 	g2="quoteright,quotedblright" 	k="88" />
<hkern g1="j" 	g2="j" 	k="-74" />
<hkern g1="k" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="k" 	g2="w" 	k="-20" />
<hkern g1="k" 	g2="t" 	k="-35" />
<hkern g1="k" 	g2="c,ccedilla" 	k="33" />
<hkern g1="k" 	g2="d" 	k="33" />
<hkern g1="k" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="33" />
<hkern g1="k" 	g2="g" 	k="33" />
<hkern g1="l" 	g2="quoteright,quotedblright" 	k="47" />
<hkern g1="l" 	g2="s" 	k="-14" />
<hkern g1="l" 	g2="z" 	k="-25" />
<hkern g1="n,ntilde" 	g2="w" 	k="6" />
<hkern g1="n,ntilde" 	g2="quoteright,quotedblright" 	k="70" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="f,uniFB01,uniFB02" 	k="27" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="t" 	k="16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="68" />
<hkern g1="r" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="2" />
<hkern g1="r" 	g2="w" 	k="-45" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-45" />
<hkern g1="r" 	g2="f,uniFB01,uniFB02" 	k="-49" />
<hkern g1="r" 	g2="t" 	k="-35" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-47" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="160" />
<hkern g1="r" 	g2="z" 	k="-14" />
<hkern g1="t" 	g2="w" 	k="-25" />
<hkern g1="t" 	g2="y,yacute,ydieresis" 	k="-25" />
<hkern g1="t" 	g2="t" 	k="-14" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-49" />
<hkern g1="w" 	g2="w" 	k="-45" />
<hkern g1="w" 	g2="y,yacute,ydieresis" 	k="-45" />
<hkern g1="w" 	g2="f,uniFB01,uniFB02" 	k="-41" />
<hkern g1="w" 	g2="t" 	k="-45" />
<hkern g1="w" 	g2="quoteright,quotedblright" 	k="-39" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="78" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="4" />
<hkern g1="w" 	g2="colon,semicolon" 	k="12" />
<hkern g1="y,yacute,ydieresis" 	g2="w" 	k="-45" />
<hkern g1="y,yacute,ydieresis" 	g2="y,yacute,ydieresis" 	k="-45" />
<hkern g1="y,yacute,ydieresis" 	g2="f,uniFB01,uniFB02" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="t" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteright,quotedblright" 	k="-39" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="66" />
<hkern g1="z" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="z" 	g2="t" 	k="-29" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w" 	k="78" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="y,yacute,ydieresis" 	k="115" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="37" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="170" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="78" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W" 	k="115" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="170" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="-39" />
<hkern g1="quoteleft,quotedblleft" 	g2="y,yacute,ydieresis" 	k="-39" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="135" />
<hkern g1="quoteleft,quotedblleft" 	g2="t" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,ccedilla" 	k="49" />
<hkern g1="quoteleft,quotedblleft" 	g2="d" 	k="29" />
<hkern g1="quoteleft,quotedblleft" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="39" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="39" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="57" />
<hkern g1="J" 	g2="w" 	k="-14" />
<hkern g1="J" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="J" 	g2="AE" 	k="29" />
<hkern g1="J" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="57" />
</font>
</defs></svg> 