<fieldset [disabled]="_landingService.isLockedTask[taskCode]">
  <div class="row step-navigation">
    <div class="col-sm-6">
      <div class="custom-checkbox" *ngIf="assetService.showRestrictionsCheckboxes">
        <input name="progressivo" type="checkbox" id="selectall" class="checkbox" (change)="changeSelectAll($event)">
        <label for="selectall">{{'UBZ.SITE_CONTENT.111010' | translate }}</label>
      </div>
    </div>
    <div class="col-sm-6 btn-set" *ngIf="canAddAsset">
      <ng-container *appAuthKey="'UBZ_APPRAISAL.INFO_SEARCH'">
        <button type="button" class="btn btn-empty waves-effect waves-secondary pull-right" id="create-esistente" (click)="goToAssetSearch()"><i class="fa fa-search" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.11001' | translate }}</button>
      </ng-container>
      <ng-container *appAuthKey="'UBZ_APPRAISAL.INFO_NEW'">
        <button type="button" class="btn btn-empty waves-effect waves-secondary pull-right" id="create-new-asset" (click)="goToNewAsset()"><i class="icon-add"></i> {{'UBZ.SITE_CONTENT.11010' | translate }}</button>
      </ng-container>
    </div>
  </div>
  <div class="row" id="asset-accordions">
    <div class="col-sm-12">
      <h3>{{'UBZ.SITE_CONTENT.11011' | translate }}</h3>
      <p *ngIf="assetResponse && assetResponse.listObjcet && assetResponse.listObjcet.length > 0 && assetService.showRestrictionsCheckboxes">{{'UBZ.SITE_CONTENT.1110101010' | translate }}.</p>
      <p class="no-asset" *ngIf="assetResponse && assetResponse.listObjcet && assetResponse.listObjcet.length === 0">{{'UBZ.SITE_CONTENT.11100' | translate }}</p>
      <accordion *ngIf="assetResponse && assetResponse.listObjcet && !(assetResponse.listObjcet.length === 0)" class="panel-group">
        <table class="assets-table">
          <tbody>
            <tr *ngFor="let asset of assetResponse.listObjcet; let i = index;">
              <td class="check-col check-fixed-top" *ngIf="assetService.showRestrictionsCheckboxes">
                <div class="custom-checkbox">
                  <input name="selected-asset-check" id="selected-asset-check-{{i}}" type="checkbox" class="checkbox" [(ngModel)]="assetService.assetsSelectedWithCheckbox[asset.idObject]">
                  <label class="check-label" for="selected-asset-check-{{i}}"></label>
                </div>
              </td>
              <td>
                <accordion-group #group class="panel form-variables" [isOpen]="asset.idObject === assetToOpen">
                  <div accordion-heading role="tab">
                    <h4 class="panel-title">
                      <a role="button">
                        <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i> {{asset.descObject}}
                        <span *ngIf="asset['parentAppraisalId'] && asset['parentAppraisalId'] !== null" class="accordion-lable-detail-text"> - {{ 'UBZ.SITE_CONTENT.10011101110' | translate }}: {{asset['parentAppraisalId']}}</span>
                      </a>
                      <div *ngIf="group?.isOpen" class="btn-set">
                        <div class="save-controls" *ngIf="readOnlyAssets[asset.idObject]">
                          <ng-container *appAuthKey="'UBZ_APPRAISAL.INFO_DELETE'">
                            <button *ngIf="canDeleteAsset" name="delete-asset" type="button" class="btn btn-empty delete-asset" (click)="openDeleteModal(asset.idObject)"><i class="fa fa-trash-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.11110' | translate }}</button>
                          </ng-container>
                          <ng-container *appAuthKey="'UBZ_APPRAISAL.INFO_MODIFY'">
                            <button *ngIf="canModifyAsset" name="edit-asset" type="button" class="btn btn-empty" (click)="setReadOnlyAssetProprieties(asset.idObject , false); $event.stopPropagation()"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.11111' | translate }}</button>
                          </ng-container>
                        </div>
                        <div class="edit-controls" *ngIf="!readOnlyAssets[asset.idObject]">
                          <ng-container *appAuthKey="'UBZ_APPRAISAL.INFO_UNDO'">
                            <button name="cancel-asset" type="button" class="btn btn-empty" (click)="undoAsset(i, asset.idObject); $event.stopPropagation()"><i class="fa fa-times" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.100000' | translate }}</button>
                          </ng-container>
                          <ng-container *appAuthKey="'UBZ_APPRAISAL.INFO_SAVE'">
                            <button name="save-asset" type="button" class="btn btn-empty" (click)="formNotValid(i, asset.idObject); upperSaveButtonClick(i); $event.stopPropagation();" [disabled]="formNotValid(i, asset.idObject)"><i class="fa fa-check-circle-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.100001' | translate }}</button>
                          </ng-container>
                        </div>
                      </div>
                    </h4>
                  </div>
                  <!-- Sezione garanzie -->
                  <div id="collapseOne" class="panel-collapse collapse in" role="tabpanel" *ngIf="group?.isOpen" >
                    <div class="panel-body">
                      <div class="panel-box">
                        <app-application-form
                          #apf
                          [idCode]="asset.idObject"
                          [page]="'INFO_ASSET_RIC'"
                          [drivers]="{'DD_AST_TIPO': assetResponse.familyAssetType}"
                          [setReadOnly]="readOnlyAssets[asset.idObject]"
                          [positionId]="requestId"
                          (formSubmit)="saveAssetChanges(asset.idObject)">
                        <!--  lista garanzie associate, da visualizzare solo se si è in una richiesta perizia (da nascondere se richiesta TGP) -->
                        <div class="row" *ngIf = "_landingService.originationProcess !== 'TGP'">
                          <div class="col-sm-12">
                            <h4 class="table-title">{{'UBZ.SITE_CONTENT.10110010' | translate }}</h4>
                            <table class="asset-choice uc-table">
                              <thead><tr>
                                <th scope="col" class="col-sm-1 checkbox-col"></th>
                                <th scope="col" class="col-sm-8">{{'UBZ.SITE_CONTENT.10010110' | translate }}</th>
                                <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.111110' | translate }}</th>
                              </tr></thead>
                              <tbody>
                                <tr *ngFor="let g of guaranteesList; let in = index">
                                  <td data-label="">
                                    <ng-container *appAuthKey="'UBZ_APPRAISAL.INFO_SELECT.GUARANTEE'">
                                      <div class="custom-radio">
                                        <input type="radio" name="asset-description" id="{{i}}-{{in}}" class="radio" [checked]="checkIfSelected(i, in)" (click)="selectGuaranteeForAsset(asset.idObject, g.progCollateral)" [disabled]="apf?.setReadOnly">
                                        <label for="{{i}}-{{in}}"></label>
                                      </div>
                                    </ng-container>
                                  </td>
                                  <td data-label="Descrizione">{{g.progCollateral}} - {{g.collateralDesc}}</td>
                                  <td data-label="Operazione">
                                    <ng-container *appAuthKey="'UBZ_APPRAISAL.INFO_GUARANTEE.DETAILS'">
                                      <button type="button" class="btn btn-clean" data-toggle="modal" data-target="#details" (click)="openGuaranteeDetails(g)" [disabled]="apf?.setReadOnly">{{'UBZ.SITE_CONTENT.1000000' | translate }}</button>
                                    </ng-container>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                        <div accordion-heading class="panel-heading" role="tab" *ngIf = "_landingService.originationProcess !== 'TGP'">
                          <h4 class="panel-title">
                            <a role="button"></a>
                            <div class="btn-set" style="text-align: right;">
                              <div class="save-controls" *ngIf="apf?.setReadOnly">
                                <ng-container *appAuthKey="'UBZ_APPRAISAL.INFO_DELETE'">
                                  <button *ngIf ="canDeleteAsset" name="delete-asset" type="button" class="btn btn-empty delete-asset" (click)="openDeleteModal(asset.idObject)"><i class="fa fa-trash-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.11110' | translate }}</button>
                                </ng-container>
                                <ng-container *appAuthKey="'UBZ_APPRAISAL.INFO_MODIFY'">
                                  <button *ngIf ="canModifyAsset" name="edit-asset" type="button" class="btn btn-empty" (click)="setReadOnlyAssetProprieties(asset.idObject , false)"><i class="fa fa-pencil-square-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.11111' | translate }}</button>
                                </ng-container>
                              </div>
                              <div class="edit-controls" *ngIf="!apf?.setReadOnly">
                                <ng-container *appAuthKey="'UBZ_APPRAISAL.INFO_UNDO'">
                                  <button name="cancel-asset" type="button" class="btn btn-empty" (click)="undoAsset(i, asset.idObject); $event.stopPropagation()"><i class="fa fa-times" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.100000' | translate }}</button>
                                </ng-container>
                                <ng-container *appAuthKey="'UBZ_APPRAISAL.INFO_SAVE'">
                                  <button id="saveAsset{{i}}" name="saveAsset" type="submit" #saveAsset class="btn btn-empty" [disabled]="formNotValid(i, asset.idObject)"><i class="fa fa-check-circle-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.100001' | translate }}</button>
                                </ng-container>
                              </div>
                            </div>
                          </h4>
                        </div>
                      </app-application-form>
                    </div>
                  </div>
                </div>
              </accordion-group>
            </td>
          </tr>
        </tbody>
      </table>
    </accordion>
  </div>
</div>


<!--MODAL BOX-->
<div *ngIf="stateBoxIsOpened" class="modal fade" id="stato-pratica" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #stateBox="bs-modal" (onHidden)="closeGuaranteeDetails()" [config]="{show: 'true'}">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{modalGuarantee.collateralTecForm}} - {{modalGuarantee.collateralDesc}}</h2>
        <button type="button" class="close pull-right" data-dismiss="modal" aria-label="Close" (click)="closeGuaranteeDetails()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-4 form-group">
            <label>{{'UBZ.SITE_CONTENT.10010100' | translate }}</label>
            {{ modalGuarantee.progCollateral }}
          </div>
          <div class="col-sm-4 form-group">
            <label>{{'UBZ.SITE_CONTENT.10110011' | translate }}</label>
            {{ modalGuarantee.garantType }}
          </div>
          <div class="col-sm-4 form-group">
            <label>{{'UBZ.SITE_CONTENT.10010101' | translate }}</label>
            {{ modalGuarantee.collateralTecForm }}
          </div>
        </div>
        <div class="row">
          <div class="col-sm-4 form-group">
            <label>{{'UBZ.SITE_CONTENT.10010110' | translate }}</label>
            {{ modalGuarantee.collateralDesc }}
          </div>
          <div class="col-sm-4 form-group">
            <label>{{'UBZ.SITE_CONTENT.10011110' | translate }}</label>
            {{ modalGuarantee.collateralAmmount | currency:'EUR':true }}
          </div>
          <div class="col-sm-4 form-group">
            <label>{{'UBZ.SITE_CONTENT.1011010' | translate }}</label>
            {{ modalGuarantee.collateralAmmountSum | currency:'EUR':true }}
          </div>
        </div>
        <div class="row">
          <div class="col-sm-4 form-group">
            <label>{{'UBZ.SITE_CONTENT.10110100' | translate }}</label>
            {{ modalGuarantee.propAssociate }}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
</fieldset>
<!-- CUSTOM MODAL gestisce la cancellazione dell'asset -->
<app-custom-modal
  [modalType] = "'delete'"
  [isOpen] = "deleteModalIsOpen"
  [largeModalFlag] = "false"
  [headerTitle] = "'UBZ.SITE_CONTENT.1000101011'"
  [positionId]="''"
  [idCode]="''"
  [apfString] = "''"
  [messagesArray] = "['UBZ.SITE_CONTENT.1011001111']"
  [buttonTitle] = "['UBZ.SITE_CONTENT.11110', 'UBZ.SITE_CONTENT.100000']"
  [disabledFlag] = "false"
  (modalSubmit) = "submitDeleteModal()"
  (modalClose) = "closeDeleteModal()">
</app-custom-modal>
