import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  Inject
} from '@angular/core';
import { ChecklistService } from '../../../service/checklist.service';
import { DomainService } from '../../../../domain/domain.service';
import { Domain } from '../../../../domain/domain';
import { APP_CONSTANTS, IAppConstants } from '../../../../../app.constants';
import { Note } from '../../../../notes/model/note';
import { NotesService } from '../../../../notes/services/notes.service';
import { PropertiesService } from '../../../../properties/properties.service';
import { MessageService } from '../../../../messages/services/message.service';

@Component({
  selector: 'app-modal-upload-single',
  templateUrl: './modal-upload-single.component.html',
  styleUrls: ['./modal-upload-single.component.css'],
  providers: [NotesService]
})
export class ModalUploadSingleComponent implements OnInit {
  @ViewChild('fileToUpload') fileToUpload: any;
  @ViewChild('pageForm') pageForm: any;
  fileName = '';

  @Input() isOpen = false;
  @Input() document: any;
  @Input() section: any;
  @Input() positionId: string;
  @Input() requestId: string;
  @Output() modalClose = new EventEmitter();
  posticipaCaricamentoPressed = false;
  isValidClass = { Y: 'state green', N: 'state red' };
  domains: Domain[] = [];
  selectedPhase = '';
  documentDesc = '';
  note = '';
  sizeLimit: number;
  canUpload = false;

  constructor(
    private checklistService: ChecklistService,
    private noteService: NotesService,
    private domainService: DomainService,
    private propertiesService: PropertiesService,
    private messageService: MessageService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) { }

  ngOnInit() {
    this.domainService.getStatusName().subscribe(x => {
      this.domains = x;
    });
    this.propertiesService.getProperty('UBZ', 'max.file.size').subscribe(x => {
      this.sizeLimit = Number(x);
    });
  }

  closeModal(refreshPage: boolean) {
    this.isOpen = false;
    this.modalClose.emit(refreshPage);
  }

  togglePosticipaCaricamento() {
    this.posticipaCaricamentoPressed = !this.posticipaCaricamentoPressed;
    this.selectedPhase = '';
  }

  setFileName(fileName?: string) {
    if (!fileName && fileName !== '') {
      fileName = this.fileToUpload.nativeElement.files[0].name;
    }
    this.fileName = fileName;
  }

  clickOnUploadButton() {
    if (this.posticipaCaricamentoPressed === true) {
      this.postponeUpload();
    } else {
      this.uploadFile();
    }
  }

  postponeUpload() {
    this.checklistService
      .postponeUpload(this.document.prog, this.selectedPhase)
      .subscribe(x => {
        this.closeModal(true);
      });
  }

  uploadFile() {
    let id;
    if (this.requestId) {
      id = this.requestId;
    } else {
      id = this.positionId;
    }
    const tmpDocumentCod = this.document.documentCod
      ? this.document.documentCod
      : this.document.groupCod;
    const uploadRequest = {
      positionId: `${this.constants.processCode}-` + id,
      documents: [
        {
          prog: this.document.prog,
          positionId: `${this.constants.processCode}-` + id,
          documentName: this.fileToUpload.nativeElement.files[0].name,
          documentCod: tmpDocumentCod,
          customDocDesc: this.documentDesc,
          notes: this.note
        }
      ],
      content: null
    };
    this.checklistService
      .newUploadDocument(
        this.fileToUpload.nativeElement.files[0],
        uploadRequest
      )
      .subscribe(() => {
        if (this.documentDesc && this.note) {
          const nota: Note = new Note(this.document.prog, 'CHK', 'M');
          nota['noteTitle'] = this.documentDesc;
          nota['noteDesc'] = this.note;
          nota['noteType'] = 'CHK';
          this.noteService.saveNote(nota).subscribe(y => {
            this.closeModal(true);
          });
        } else {
          this.closeModal(true);
        }
      });
  }

  buttonIsEnabled(): boolean {
    if (this.fileName !== '' || this.selectedPhase !== '') {
      return true;
    } else {
      return false;
    }
  }

  setFile() {
    if (this.fileToUpload.nativeElement.files[0].size > this.sizeLimit) {
      this.messageService.showError(
        `LA DIMENSIONE DEL FILE NON PUO\' SUPERARE I ${this.sizeLimit /
        1048576} MEGABYTE`,
        'DIMENSIONE FILE NON CONSENTITA'
      );
      this.setFileName('');
    } else {
      this.setFileName();
    }
  }
}
