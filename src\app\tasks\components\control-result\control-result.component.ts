import { Component, OnInit, Input } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { UserData } from '../../../shared/user-data/user-data';
import { UserDataService } from '../../../shared/user-data/user-data.service';
import { GenericTaskService } from '../../services/generic-task/generic-task.service';

@Component({
  selector: 'app-control-result',
  templateUrl: './control-result.component.html',
  styleUrls: ['./control-result.component.css']
})
export class ControlResultComponent implements OnInit {
  @Input() positionId: string;
  rows: any[];
  taskId: string;
  taskCod: string;

  constructor(
    private genericTaskService: GenericTaskService,
    private userDataService: UserDataService,
    private activatedRoute: ActivatedRoute
  ) {}

  ngOnInit() {
    this.activatedRoute.params
      .switchMap((params: Params) => {
        this.taskId = params['taskId'];
        this.taskCod = params['taskCod'];
        return this.genericTaskService.getControlResultPage(this.positionId);
      })
      .subscribe(x => {
        this.rows = x;
      });
  }

  getRowStatus(status: string): string {
    if (status === 'Y') {
      return 'green';
    } else {
      return 'yellow';
    }
  }

  taskLocked() {
    this.userDataService.getAll().subscribe((res:UserData)=>{
      this.genericTaskService.taskLockingUser=res.username
    })
  }

  tasklockedByOtherUser(user: string) {
    this.genericTaskService.taskLockingUser = user;
  }
  
  taskUnlocked() {
    this.genericTaskService.taskLockingUser = undefined;
  }
}
