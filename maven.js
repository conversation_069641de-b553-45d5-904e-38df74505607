const warConfig = require('./maven-war-config.json');
const earConfig = require('./maven-ear-config.json');
const maven = require('maven-deploy');
const copy = require('recursive-copy');

const overwriteOptions = {
    overwrite: true,
    debug: false
};

const war = () => {

    return copy('../webapp/', './dist', overwriteOptions)
        .then(function (results) {
            results.forEach(result => console.log('copied ', result.src, ' to ', result.dest))
            maven.config(warConfig);
            let warArtifact = maven.package();
            console.log('artifact created', warArtifact);
            return warArtifact;
        })
        .catch(function (error) {
            console.error('Copy failed: ' + error);
        });
};

const ear = (warArtifact) => {

    const options = {
        overwrite: true,
        debug: true,
        filter: [
            '*.war'
        ]
    };

    copy('./dist', './ear-package/', options)
        .then(function (copyWarResult) {
            copyWarResult.forEach(path => console.log('copied ', path.src, ' to ', path.dest))
            copy('../../../../UBZ-EFA-EAR/src/main/application/', './ear-package/', overwriteOptions)
                .then(function (results) {
                    results.forEach(result => console.log('copied ', result.src, ' to ', result.dest))
                    maven.config(earConfig);
                    maven.package();
                })
                .catch(function (error) {
                    console.error('Copy failed: ' + error);
                });
        });
};

war().then(ear);