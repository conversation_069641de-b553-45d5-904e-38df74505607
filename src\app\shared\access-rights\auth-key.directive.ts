import {
  Directive,
  Input,
  OnDestroy,
  TemplateRef,
  ViewContainerRef
} from '@angular/core';
import { AccessRightsService } from './services/access-rights.service';
import { Subscription } from 'rxjs/Subscription';

@Directive({
  selector: '[appAuthKey]'
})
export class AuthKeyDirective implements OnDestroy {
  private hasView = false;
  private subscription: Subscription;

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private accessRightsService: AccessRightsService
  ) {}

  @Input()
  set appAuthKey(authKey: string) {
    this.hasView = false;
    this.subscription = this.accessRightsService.authKeysSource.subscribe(
      (resp: string[]) => {
        const isUserQualified = resp.indexOf(authKey) !== -1;
        if (isUserQualified && !this.hasView) {
          this.viewContainer.createEmbeddedView(this.templateRef);
          this.hasView = true;
        } else if (!isUserQualified && this.hasView) {
          this.viewContainer.clear();
          this.hasView = false;
        }
      }
    );
  }

  ngOnDestroy() {
    if (this.subscription && !this.subscription.closed) {
      this.subscription.unsubscribe();
    }
  }
}
