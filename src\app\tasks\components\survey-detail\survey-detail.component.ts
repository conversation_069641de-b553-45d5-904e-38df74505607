import { Component, OnInit, Input } from '@angular/core';
import { GenericTaskService } from '../../services/generic-task/generic-task.service';

@Component({
  selector: 'app-survey-detail',
  templateUrl: './survey-detail.component.html',
  styleUrls: ['./survey-detail.component.css']
})
export class SurveyDetailComponent implements OnInit {
  @Input() positionId: string;
  surveyDetails = {};

  constructor(private genericTaskService: GenericTaskService) {}

  ngOnInit() {
    this.getSurveyDetails();
  }

  /**
   * @function
   * @name getSurveyDetails
   * @description Recupera i dati del questionario da mostrare a video
   */
  getSurveyDetails() {
    this.genericTaskService
      .getSurveyDetails(this.positionId)
      .subscribe(response => {
        this.surveyDetails = response;
      });
  }
}
