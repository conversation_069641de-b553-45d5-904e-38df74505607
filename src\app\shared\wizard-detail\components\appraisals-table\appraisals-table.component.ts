import { Component, OnInit, Input, ViewChild, Inject } from '@angular/core';
import { Params, ActivatedRoute } from '@angular/router';
import { DOCUMENT } from '@angular/platform-browser';
import { AppraisalObjectsInfo } from '../../../../wizard-detail/model/appraisal-objects-info';
import { WizardDetailService } from '../../../../wizard-detail/services/wizard-detail.service';
import { Observable } from 'rxjs/Observable';

import { Domain } from '../../../domain/domain';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { DomainService } from '../../../domain';

@Component({
  selector: 'app-appraisals-table',
  templateUrl: './appraisals-table.component.html',
  styleUrls: ['./appraisals-table.component.css']
})
export class AppraisalsTableComponent implements OnInit {
  @Input() isAppraisalConvalidated: boolean;
  @Input() list: AppraisalObjectsInfo[] = [];
  @Input() genericTask: boolean;
  @ViewChild('assetDetailsModal') assetDetailsModal: ModalDirective;
  @ViewChild('disableAssetNotesModal') disableAssetNotesModal: ModalDirective;
  public positionId: string;
  public selectedAssetDetails: any[];
  public assetNotes: any;
  public wizardCode: string;
  private familyTypes: Domain[] = [];
  private categoryTypes: Domain[] = [];
  modalNoteIsOpen: boolean = false;
  isOnDetailPage: boolean = false;
  activeDomain: any;

  constructor(
    @Inject(DOCUMENT) private document: any,
    private activatedRoute: ActivatedRoute,
    private wizardDetailService: WizardDetailService,
    private domainService: DomainService
  ) { }

  ngOnInit() {
    if (this.genericTask) {
      this.isOnDetailPage = true;
    }
    else {
      this.isOnDetailPage = false;
    }

    this.activatedRoute.params
      .switchMap((params: Params) => {
        this.positionId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        if (!this.genericTask) {
          return Observable.forkJoin(
            this.wizardDetailService.getFamilyTypes(),
            this.wizardDetailService.getCategoryTypes(true),
            // Dati degli oggetti di perizia
            this.wizardDetailService.getAppraisalObjectsInfo(
              this.positionId,
              this.wizardCode
            )
          );
        } else {
          return Observable.forkJoin(
            this.wizardDetailService.getFamilyTypes(),
            this.wizardDetailService.getCategoryTypes(true)
          );
        }
      })
      .subscribe(res => {
        this.familyTypes = res[0];
        this.categoryTypes = res[1];
        if (!this.genericTask) {
          this.list = res[2];
        }
      });

    this.domainService.newGetDomain('UBZ_DOM_UAM_MOTIV_DISABLE').subscribe(data => {
      this.activeDomain = data;

    })
  }

  getFamilyType = function (i: number) {
    if (this.familyTypes && this.familyTypes[this.list[i].resItemType]) {
      return this.familyTypes[this.list[i].resItemType].translationCod;
    } else {
      return '';
    }
  };

  public getCategoryType(toTranslate: string): string {
    if (this.categoryTypes && this.categoryTypes[toTranslate]) {
      return this.categoryTypes[toTranslate].translationCod;
    } else {
      return '';
    }
  }

  public goToAssetPage(assetId: number): void {
    this.document.location.href = `/UAM-EFA-PF/UAM/#/detail/false/${assetId}` +
      `;fromAppraisalId=${this.positionId}`; // send appraisal id as optional param
  }

  public isAssetStateAvailable(asset): boolean {
    return asset.assetDetail && asset.assetDetail.length > 0;
  }

  public hideAssetDetailsModal(): void {
    this.selectedAssetDetails = [];
    this.assetDetailsModal.hide();
  }

  public showAssetDetailsModal(index: number): void {
    this.selectedAssetDetails = this.list[index]['assetDetail'];
    this.assetDetailsModal.show();
  }

  public openDisableNotesModal(index) {
    this.assetNotes = this.list[index]['assetDetail'];   
    this.disableAssetNotesModal.show();
  }

  public closeModalNote() {
    this.disableAssetNotesModal.hide();
  }

}
