<div *ngIf="isOpen" class="modal fade" id="construction-site-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #constructionSiteModal="bs-modal" (onHidden)="closeModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <form (ngSubmit)="submit()" #f="ngForm">
        <div class="modal-header">
          <h2 *ngIf="modalType==='add'">{{'UBZ.SITE_CONTENT.111110100' | translate }}</h2>
          <h2 *ngIf="modalType==='modify'">{{'UBZ.SITE_CONTENT.111111001' | translate }}</h2>
          <button type="button" class="close pull-right" aria-label="Close" (click)="closeModal()">
            <i class="icon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-sm-6 form-group">
              <label>{{'UBZ.SITE_CONTENT.101111110' | translate | uppercase}}*</label>
              <input type="text" class="form-control" [(ngModel)]="propertyCopy.firstName" name="firstName" required appForcePattern regexPattern="{{_constants.REGEX_PATTERN.ONLY_CHARS_AND_SPACE}}"/>
            </div>
            <div class="col-sm-6 form-group">
              <label>{{'UBZ.SITE_CONTENT.101111111' | translate | uppercase}}*</label>
              <input type="text" class="form-control" [(ngModel)]="propertyCopy.lastName" name="lastName" required appForcePattern regexPattern="{{_constants.REGEX_PATTERN.ONLY_CHARS_AND_SPACE}}"/>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6 form-group">
              <label>{{'UBZ.SITE_CONTENT.111110101' | translate }}*</label>
              <input type="text" class="form-control" [(ngModel)]="propertyCopy.email" name="email" required pattern="{{_constants.REGEX_PATTERN.EMAIL}}"/>
            </div>
            <div class="col-sm-6 form-group">
              <label>{{'UBZ.SITE_CONTENT.111110110' | translate }}*</label>
              <input appOnlyNumbers type="text" class="form-control" [(ngModel)]="propertyCopy.phoneNumber" name="phoneNumber" required/>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12">
              <label>{{'UBZ.SITE_CONTENT.11010000' | translate }}*</label>
              <div class="custom-select">
                <select class="form-control" [(ngModel)]="propertyCopy.personRole" name="personRole" required>
                  <option value="" hidden>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let elem of (personRoleDomain | domainMapToDomainArray)" value="{{elem.domCode}}">{{elem.translationCod | translate}}</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer text-center">
          <button *ngIf="modalType==='add'" class="btn btn-primary waves-effect" type="submit" [disabled]="f.invalid">{{'UBZ.SITE_CONTENT.111100001' | translate | uppercase}}</button>
          <button *ngIf="modalType==='modify'" class="btn btn-primary waves-effect" type="submit" [disabled]="f.invalid">{{'UBZ.SITE_CONTENT.111111010' | translate | uppercase}}</button>
        </div>
      </form>
    </div>
  </div>
</div>
