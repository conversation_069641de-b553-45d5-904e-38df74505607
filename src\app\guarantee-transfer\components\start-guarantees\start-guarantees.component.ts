import { Component, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { GuaranteeTransferService } from '../../services/guarantee-transfer.service';

@Component({
  selector: 'app-start-guarantees',
  templateUrl: './start-guarantees.component.html',
  styleUrls: ['start-guarantees.component.css']
})
export class StartGuaranteesComponent implements OnInit {
  @ViewChild('assetModal') assetModal;
  guaranteeId = null;
  arrayIndex: number;
  stepNum = 1;
  accordions: any[];
  asset: any;
  // flag apertura modal dettaglio asset
  modalIsOpen: boolean;
  // flag apertura modal step precedente
  previousModalIsOpen: boolean;

  constructor(
    public guaranteeTransfer: GuaranteeTransferService,
    private router: Router
  ) { }

  ngOnInit() {
    this.guaranteeTransfer.staticHeaderArray.find(obj => obj['label'] === 'UBZ.SITE_CONTENT.***********')['value'] = null;
    // Se l'array non è popolato forza uscita dal wizard
    if (!this.guaranteeTransfer.guaranteeFromList.length) {
      return this.guaranteeTransfer.cancelRequest();
    }
    setTimeout(() => (this.guaranteeTransfer.wizardStep = 2), 10);
    this.accordions = this.guaranteeTransfer.guaranteeFromList;
    this.checkSelectedGuarantee();
  }

  /**
   * @name checkSelectedGuarantee
   * @description Se la garanzia è stata selezionate precedentemente scorre l'array
   * di queste per valorizzarla a video e al termine invoca il servizio che ne setta l'index
  */
  checkSelectedGuarantee() {
    if (this.guaranteeTransfer.guaranteeFrom) {
      this.accordions.forEach((guarantee, index) => {
        if (guarantee.jointCod === this.guaranteeTransfer.guaranteeFrom) {
          this.guaranteeId = this.guaranteeTransfer.guaranteeFrom;
          this.onSelectionChange(index);
        }
      });
    }
  }

  /**
   * @name openAssetDetails
   * @param row Oggetto contenente i dati dell'asset
   * @param modal Riferimento alla modal da aprire per visualizzare i dettagli dell'asset
   * @description Apre la modal contenente i dettagli dell'asset selezionato
   */
  openAssetDetails(row: any, modal: any): void {
    this.modalIsOpen = true;
    this.asset = row;
  }


  /**
   * @name closeAssetDetails
   * @description Chiude la modal contenente i dettagli dell'asset selezionato
   */
  closeAssetModal() {
    this.assetModal.hide();
    this.modalIsOpen = false;
    this.asset = null;
  }

  /**
   * @name onSelectionChange
   * @param {number} arrayIndex  - Indice della garanzia seleziona all'interno dell'array
   * @description Salva nella variabile interna l'indice della garanzia selezionata
   */
  onSelectionChange(arrayIndex: number): void {
    this.arrayIndex = arrayIndex;
  }

  /**
   * @name save
   * @description Invoca il servizio di controllo per la garanzia selezionata e se l'esito è positivo
   * esegue la navigazione verso il prossimo step del wizard
  */
  save(): void {
    const input = {
      ndgFrom: this.guaranteeTransfer.ndgOrigin,
      ndgTo: (this.guaranteeTransfer.ndgDestination && this.guaranteeTransfer.ndgDestination !== '-') ? this.guaranteeTransfer.ndgDestination : this.guaranteeTransfer.ndgOrigin,
      type: this.accordions[this.arrayIndex].assets[0].assetType,
      collateral: this.accordions[this.arrayIndex]
    };
    this.guaranteeTransfer.guaranteeFrom = this.guaranteeId;
    this.guaranteeTransfer.assetType = this.accordions[this.arrayIndex].assets[0].assetType;
    this.guaranteeTransfer.staticHeaderArray.find(obj => obj['label'] === 'UBZ.SITE_CONTENT.***********')['value'] = this.guaranteeId;
    // FIXME - ESEGUI ROUTING RELATIVO FRA LE ROTTE CHILD
    this.guaranteeTransfer
      .checkCollaterals(input)
      .subscribe(() => {
        this.router.navigate([`guaranteeTransfer/${this.guaranteeTransfer.ndgOrigin}/${this.guaranteeTransfer.ndgDestination}/target-guarantees`
        ]);
      });
  }

  // FIXME - ESEGUI ROUTING RELATIVO FRA LE ROTTE CHILD
  /**
   * @name previous
   * @description Effettua la navigazione verso il precedente step del wizard
  */
  previous() {
    this.router.navigate([`guaranteeTransfer/${this.guaranteeTransfer.ndgOrigin}/${this.guaranteeTransfer.ndgDestination}/select-appraisals`])
  }

  /**
   * @name openModal
   * @description Apre la modal precedente
  */
  openModal() {
    this.previousModalIsOpen = true;
  }

  /**
   * @name closeModal
   * @description Chiude la modal precedente
  */
  closeModal() {
    this.previousModalIsOpen = false;
  }

  /**
   * @name confirmUndo
   * @description Sul conferma della modal, chiude e invoca previous()
  */
  confirmUndo() {
    this.previousModalIsOpen = false;
    this.previous();
  }
}
