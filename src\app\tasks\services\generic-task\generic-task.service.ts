import { Injectable } from '@angular/core';
import { Http, Response, ResponseContentType } from '@angular/http';
import { Observable } from 'rxjs/Observable';
import { DomainService } from '../../../shared/domain/domain.service';
import { Appointment } from '../../model/appointment';
import { ExpertSearchModel } from '../../model/expert';

@Injectable()
export class GenericTaskService {
  private _taskLockingUser: string;

  constructor(private _domainsService: DomainService, private _http: Http) {}

  public getExpertTypes(): Observable<any> {
    return this._domainsService.newGetDomain('UBZ_DOM_ANAG_SUBJECT_TYPE', 'Y');
  }

  public getExpertList(
    positionId: string,
    expertType: string,
    pageNumber: number,
    pageSize: number = 10
  ) {
    positionId = encodeURIComponent(positionId);
    expertType = encodeURIComponent(expertType);
    const url =
      '/UBZ-ESA-RS/service/search/v1/experts/' +
      positionId +
      '/' +
      expertType +
      '/' +
      pageNumber +
      '/' +
      pageSize;
    return this._http.get(url).map(res => res.json());
  }

  public saveExpert(
    positionId: string,
    expertType: string,
    expertId: string,
    surveyType?: string
  ): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url =
      '/UBZ-ESA-RS/service/appraisal/v1/appraisals/' +
      positionId +
      '/assigneExpert';
    const obj: any = {};
    if (expertType === 'PER') {
      obj.expertId = expertId; // Se è un perito
    } else {
      obj.expertSocId = expertId; // Se è una società peritale
    }
    obj.surveyNec = surveyType;
    return this._http.put(url, obj).map(res => res.json());
  }

  public searchExpert(
    positionId: string,
    idSocPer: number,
    ndg: string = '',
    firstName: string = '',
    lastName: string = ''
  ): Observable<ExpertSearchModel[]> {
    const url = '/UBZ-ESA-RS/service/search/v1/experts/searchExpBySocPer';
    const searchObj = {};
    searchObj['idSocPe'] = idSocPer;
    searchObj['idAppraisal'] = positionId;
    if (firstName.length > 0) {
      searchObj['name'] = firstName;
    }
    if (lastName.length > 0) {
      searchObj['lastName'] = lastName;
    }
    if (ndg.length > 0) {
      searchObj['ndg'] = ndg;
    }
    return this._http.post(url, searchObj).map(res => res.json());
  }

  public assigneExpert(
    positionId: string,
    idSocPer: string,
    expertId: string,
    revisorId: string
  ): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url =
      '/UBZ-ESA-RS/service/appraisal/v1/appraisals/' +
      positionId +
      '/assigneExpertAndReviser';
    const obj = {};
    obj['expertSocId'] = idSocPer;
    obj['expertId'] = expertId;
    obj['revisionId'] = revisorId;
    return this._http.put(url, obj).map(res => res.json());
  }

  public newAssigneExpert(
    positionId: string,
    expertSocId: string,
    expertId: string,
    revisionId: string,
    spExpertId: string,
    spRevisionId: string,
    spExpertDesc: string,
    spRevisionDesc: string
  ): Observable<any> {
    const url =
      '/UBZ-ESA-RS/service/appraisal/v2/appraisals/' +
      positionId +
      '/assigneExpertAndReviser';
    const obj = {
      expertSocId: expertSocId,
      expertId: expertId,
      revisionId: revisionId,
      spExpertId: spExpertId,
      spRevisionId: spRevisionId,
      spExpertDesc: spExpertDesc,
      spRevisionDesc: spRevisionDesc
    };
    return this._http.put(url, obj).map(res => res.json());
  }

  /**
   * @function
   * @name getBillingData
   * @description Recupera i valori per valorizzare i campi dell'accordion 'Assegnazione Incarico' in 'Dati generali' associati alla sezione trasparenza
   * @param {string} positionId - id perizia
   */
  getBillingData(positionId: string) {
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisals/getTrasparencyData/${positionId}`;
    return this._http.get(url).map(res => res.json());
  }

  /**
   * @function
   * @name saveBillingData
   * @description Salva i campi dell'accordion 'Assegnazione Incarico' in 'Dati generali' associati alla sezione trasparenza
   * @param {string} positionId - id perizia
   * @param {number} billingAmount - Importo fatturazione
   * @param {Date} billingDate - Data fatturazione
   * @param {string} freeAppraisalCheck - Flag perizia gratuita
   */
  saveBillingData(
    positionId: string,
    billingAmount: number,
    billingDate: Date,
    freeAppraisalCheck: string,
    billingAmountSecond:number,
    billingDateSecond:Date,
    secondfreeAppraisalCheck:string

  ) {
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisals/saveTrasparencyData/${positionId}`;
    const obj = {
      billingAmount: billingAmount,
      gratisFlag: freeAppraisalCheck,
      billingAmountDate: billingDate,
      billingAmountSecond:billingAmountSecond,
      billingDateSecond:billingDateSecond,
      flagSecondSurvey: secondfreeAppraisalCheck
    };
    return this._http.post(url, obj).map(res => res.json());
  }

  public setAppointment(
    positionId: string,
    appointments: Appointment[]
  ): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url =
      '/UBZ-ESA-RS/service/appraisal/v1/appraisals/' +
      positionId +
      '/setAppointment';
    const obj = {};
    obj['listAppointment'] = appointments;
    return this._http.put(url, obj).map(res => res.json());
  }

  checklistAcquiredCheck(positionId: string): Observable<number[]> {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/checklist/v1/acquiredCheck/${positionId}`;
    return this._http.get(url).map(res => res.json());
  }

  public get taskLockingUser() {
    return this._taskLockingUser;
  }

  public set taskLockingUser(lockingUser: string) {
    this._taskLockingUser = lockingUser;
  }

  public saveSiteInspectionResult(
    positionId: string,
    postData: any
  ): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisals/${positionId}/surveyOutcome`;
    return this._http.put(url, postData).map(res => res.json());
  }

  public getControlResultPage(positionId: string) {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisals/${positionId}/reviewFinancialEvaluation`;
    return this._http.get(url).map(res => res.json());
  }

  public startSecondOpinion(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisals/${positionId}/createSecondOpinion`;
    return this._http.post(url, {}).map((resp: Response) => resp.text());
  }

  public startThirdOpinion(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/appraisal/v1/appraisals/${positionId}/createThirdOpinion`;
    return this._http.post(url, {}).map((res: Response) => res.text());
  }

  public checkIndividualSecondOpinionAllowed(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/individual/v1/individuals/checkSecondEvalInd/${positionId}`;
    return this._http.get(url).map(res => res.json());
  }

  public startIndividualSecondOpinion(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/individual/v1/individuals/secondEvaluationInd/${positionId}`;
    return this._http.get(url).map(res => res.json());
  }

  public getDifferencesBetweenOpinions(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url =
      '/UBZ-ESA-RS/service/appraisal/v1/appraisals/' +
      positionId +
      '/getDifferenceBetweenOpinion';
    return this._http.get(url).map(res => res.json());
  }

  public getDifferencesBetweenOpinionsV2(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url =
      '/UBZ-ESA-RS/service/appraisal/v2/appraisals/' +
      positionId +
      '/getDifferenceBetweenOpinion';
    return this._http.get(url).map(res => res.json());
  }

  public saveThirdOpinionData(
    positionId: string,
    toSave: any
  ): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url =
      '/UBZ-ESA-RS/service/appraisal/v1/appraisals/' +
      positionId +
      '/updateThirdOpinion';
    return this._http.put(url, toSave).map(res => res.json());
  }

  public printAppraisalDetail(positionId: string) {
    return this._http
      .get(
        `/UBZ-ESA-RS/service/print/v1/prints/${positionId}/DET/downloadJasPdf`,
        { responseType: ResponseContentType.Blob }
      )
      .map((resp: Response) => {
        return resp.blob();
      });
  }

  public getAppraisalEvaluations(appraisalId: string) {
    const url = `/UBZ-ESA-RS/service/sampleCheck/v1/appraisalEvaluations/${appraisalId}`;
    return this._http.get(url).map((resp: Response) => {
      return resp.json();
    });
  }
  public saveAppraisalEvaluations(appraisalId: string, input: any) {
    const url = `/UBZ-ESA-RS/service/sampleCheck/v1/appraisalEvaluations/${appraisalId}`;
    return this._http.put(url, input).map((resp: Response) => {
      return resp.json();
    });
  }

  getAppraisalXml(appraisalId: string): Observable<any> {
    const url = `/UBZ-ESA-RS/service/socPerService/v1/appraisals/${appraisalId}`;
    return this._http
      .get(url, { responseType: ResponseContentType.Blob })
      .map((resp: Response) => resp.blob());
  }

  uploadAppraisalXml(appraisalId: string, file: any): Observable<any> {
    const url = `/UBZ-ESA-RS/service/socPerService/v1/appraisals/${appraisalId}`;
    const formData: FormData = new FormData();
    formData.append('file', file, file.name);
    return this._http.put(url, formData).map((resp: Response) => resp.json());
  }

  /**
   * @name getThirdNetworkProperty
   * @description Restituisce la property(PRELIOS), per le richieste di tipo Reti Terze.
   */
  getThirdNetworkProperty(): Observable<any> {
    return this._http
      .get('/UBZ-ESA-RS/service/individual/v1/thirdNetworks')
      .map((resp: Response) => resp.json());
  }

  /**
   * @function
   * @name getSurveyDetails
   * @description Esegue la chiamata al BE per recuperare il dettaglio del questionario
   * da mostrare nel tab "Esito questionario" del dettaglio perizia
   * @param {string} - appraisalId, id perizia
   */
  getSurveyDetails(appraisalId) {
    const url = `/UBZ-ESA-RS/service/search/v1/positions/questResult/getDetails/${appraisalId}`;
    return this._http.get(url).map(res => res.json());
  }

}
