
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, LOCALE_ID, NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Http, HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { ToastModule } from 'ng2-toastr';
import { TooltipConfig } from 'ngx-bootstrap';
import { CookieModule } from 'ngx-cookie';
import { CookieOptionsProvider} from 'ngx-cookie/src/cookie-options-provider';
import { CookieService } from 'ngx-cookie/src/cookie.service';
import { AppConstants, APP_CONSTANTS } from '../app.constants';
import { getAlertConfig } from '../app.module';
import { DomainService } from '../shared/domain';
import { CustomHttpService } from '../shared/http/custom-http.service';
import { MenuService } from '../shared/menu/services/menu.service';
import { MessageService } from '../shared/messages/services/message.service';
import { CustomTimePipe } from '../shared/pipes/custom-time/custom-time.pipe';
import { DomainMapToDomainArrayPipe } from '../shared/pipes/domain-map-to-domain-array/domain-map-to-domain-array.pipe';
import { PropertiesService } from '../shared/properties/properties.service';
import { SharedService } from '../shared/services/shared.service';
import { UserDataService } from '../shared/user-data/user-data.service';
import { MassiveUploadComponent } from './massive-upload.component';
import { UploadFileService } from './service/upload-file.service';


describe('MassiveUploadComponent ', () => {
  let component: MassiveUploadComponent;
  let fixture: ComponentFixture<MassiveUploadComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RouterTestingModule, ReactiveFormsModule, FormsModule, CommonModule, HttpModule, TranslateModule.forRoot(), CookieModule.forRoot(), ToastModule.forRoot()],
      declarations: [MassiveUploadComponent, CustomTimePipe, DomainMapToDomainArrayPipe],
      providers:
        [
          MessageService,
          { provide: Http, useClass: CustomHttpService },
          { provide: APP_CONSTANTS, useValue: AppConstants },
          { provide: LOCALE_ID, useValue: 'it-IT' },
          { provide: TooltipConfig, useFactory: getAlertConfig },
          UploadFileService,
          UserDataService, CookieService, CookieOptionsProvider, MenuService, PropertiesService, DomainService, SharedService
        ],
      schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(MassiveUploadComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the app', () => {
    expect(component).toBeTruthy();
  });

  it('should test changePage', () => {
    const spy = spyOn(component, 'refreshData')
    component.changePage({ page: 2 })
    expect(spy).toHaveBeenCalled();
    expect(component.page).toBe(2);
  });

  it('should test changePageSize', () => {
    const spy = spyOn(component, 'refreshData')
    component.changePageSize();
    expect(spy).toHaveBeenCalled();
  });

});


