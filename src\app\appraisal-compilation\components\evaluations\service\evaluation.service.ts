import { Injectable } from '@angular/core';
import { Http } from '@angular/http';
import { Observable } from 'rxjs/Observable';
import { IndividualEvalDataModel } from '../model/individual-eval-data.model';

@Injectable()
export class EvaluationService {
  constructor(private _http: Http) {}

  // Results of this service are into the result of getEvalData()
  // Keep this for future changes in the service
  private getConsistencyTableData(positionId: string): Observable<any> {
    const url =
      '/assets/data/fake-appraisal-compilation/fake-consistency-table.json';
    return this._http.get(url).map(res => res.json());
  }

  public getEvalData(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = '/UBZ-ESA-RS/service/immGarService/evalgetdata/' + positionId;
    return this._http.get(url).map(res => res.json());
  }
  
  public checkCollateralRegistry(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = '/UBZ-ESA-RS/service/immGarService/checkCollateralRegistry/' + positionId;
    return this._http.get(url).map(res => res.json());
  }

  getEvalDataIndividual(positionId: string): Observable<IndividualEvalDataModel> {
    positionId = encodeURIComponent(positionId);
    const url = '/UBZ-ESA-RS/service/individual/v1/evalgetdataInd/' + positionId;
    return this._http.get(url).map(res => res.json());
  }

  public getEstimationTableData(positionId: string): Observable<any> {
    const url =
      '/assets/data/fake-appraisal-compilation/fake-estimation-table.json';
    return this._http.get(url).map(res => res.json());
  }

  public saveData(
    positionId: string,
    apfs: any,
    statics: any,
    subjectString: string
  ): Observable<any> {
    positionId = encodeURIComponent(positionId);
    let url: string = '';
    let obj = {};    
    if (subjectString === 'COR') {
      // Creazione oggetto di salvataggio per CORPORATE      
      url = '/UBZ-ESA-RS/service/immGarService/evalsavedata/' + positionId;
      obj['evalStaticData'] = statics;
      obj['apfsave'] = {
        page: 'VALUTAZIONI',
        apfmap: apfs
      };
    } else {
      // Creazione oggetto di salvataggio per INDIVIDUAL
      url = '/UBZ-ESA-RS/service/individual/v1/evalsavedataInd/' + positionId;
      obj = statics;
      obj['staticData']['apfsave'] = {
        page: 'VALUTAZIONI', 
        apfmap: apfs
      };
    }
    return this._http.post(url, obj).map(res => res.json());
  }

  public getPhotovoltaicData(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = '/UBZ-ESA-RS/service/immGarService/fotoEval/' + positionId;
    return this._http.get(url).map(res => res.json());
  }

  public updateEvalFromUnit(appraisalId: string): Observable<any> {
    appraisalId = encodeURIComponent(appraisalId);
    const url =
      '/UBZ-ESA-RS/service/immGarService/updatEvalFromUnit/' + appraisalId;
    return this._http.get(url).map(res => res.json());
  }
}
