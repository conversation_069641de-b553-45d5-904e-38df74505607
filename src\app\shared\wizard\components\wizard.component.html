<section id="wizard-progress">
  <ng-container *ngFor="let child of wizard; let i = index; let l = last">
    <div *ngIf="child.taskList.outcomeCod === '1'" class="page incomplete">
      <a id="step{{i+1}}">
        <span class="progress-badge waves-effect waves-progress">
          <i *ngIf="l" class="icon-check"></i>
          <ng-container *ngIf="!l">{{i+1}}</ng-container>
        </span>
        <p>{{child.translation}}</p>
        <span *ngIf="!l" class="sep"></span>
      </a>
    </div>

    <div *ngIf="child.taskList.outcomeCod === '2'" class="page complete">
      <a id="step{{i+1}}" (click)="goToTask(child.taskList.taskCod)">
        <span class="progress-badge waves-effect waves-progress">
          <i *ngIf="l" class="icon-check"></i>
          <ng-container *ngIf="!l">{{i+1}}</ng-container>
        </span>
        <p>{{child.translation}}</p>
        <span *ngIf="!l" class="sep"></span>
      </a>
    </div>

    <div *ngIf="child.taskList.outcomeCod === '3'" class="page current">
      <a id="step{{i+1}}" (click)="goToTask(child.taskList.taskCod)">
        <span class="progress-badge waves-effect waves-progress">
          <i *ngIf="l" class="icon-check"></i>
          <ng-container *ngIf="!l">{{i+1}}</ng-container>
        </span>
        <p>{{child.translation}}</p>
        <span *ngIf="!l" class="sep"></span>
      </a>
    </div>
  </ng-container>
</section>
