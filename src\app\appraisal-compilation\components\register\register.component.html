<!-- fixme - togliere codice commentato -->
<section id="interactive-tabs" class="interactive-tabs">
  <div class="row">
    <div class="col-sm-12">
      <div class="nav-wrap nav-controls">
        <ul class="nav nav-tabs pull-left" role="tablist">
          <li *ngFor="let ass of assets" role="presentation" [class]="isActive(ass)">
            <a *ngIf="ass === selectedAsset" aria-controls="sub1" role="tab" data-toggle="tab">
              <i class="fa fa-map-marker" aria-hidden="true" aria-hidden="true" data-placement="bottom" data-toggle="tooltip"
                [title]="getAssetAddress(ass)"></i>
              <span *ngIf="activeCategories.indexOf(ass.category) > -1">
                {{ass.category}} {{ass?.idObject}}
              </span>
              <span *ngIf="activeCategories.indexOf(ass.category) === -1">
                <span class="expiredCategory">{{ass.category}}</span> {{ass?.idObject}}
              </span>
              <span class="state"
                [ngClass]="{'green' : (ass.conclusionStatusFlag === 'Y'), 'red' : (ass.conclusionStatusFlag === 'N')}"></span>
            </a>
            <a *ngIf="ass !== selectedAsset" aria-controls="sub1" role="tab" data-toggle="tab" (click)="changeSelectedAsset(ass)">
              <i class="fa fa-map-marker" aria-hidden="true" aria-hidden="true" data-placement="bottom" data-toggle="tooltip"
                [title]="getAssetAddress(ass)"></i>
              <span *ngIf="activeCategories.indexOf(ass.category) > -1">
                {{ass.category}} {{ass?.idObject}}
              </span>
              <span *ngIf="activeCategories.indexOf(ass.category) === -1">
                <span class="expiredCategory">{{ass.category}}</span> {{ass?.idObject}}
              </span>
              <span class="state"
                [ngClass]="{'green' : (ass.conclusionStatusFlag === 'Y'), 'red' : (ass.conclusionStatusFlag === 'N')}"></span>
            </a>
          </li>
        </ul>
        <ng-container *appAuthKey="'UBZ_REGISTER_NEW'">
          <button (click)="openNewAssetModal()" type="button" class="btn btn-empty btn-plus pull-left" data-toggle="modal" data-target="#new-asset"
            [disabled]="!_landingService.positionLocked || _landingService.isLockedTask[currentTask] || haveDisabledFields">
            <i class="fa fa-plus" aria-hidden="true"></i>
          </button>
        </ng-container>
      </div>
      <div class="tab-content">
        <div role="tabpanel" class="tab-pane active" id="sub1">
          <div class="base-wrapper">
            <div class="row">
              <div class="col-sm-12">
                <div class="row">
                  <div class="col-sm-12">
                    <div class="edit-controls">
                      <fieldset [disabled]="!_landingService.positionLocked || _landingService.isLockedTask[currentTask] || haveDisabledFields">
                        <ng-container *appAuthKey="'UBZ_REGISTER_SAVE'">
                          <button *ngIf="isEditable === true" type="button" class="btn btn-empty waves-effect waves-secondary pull-right blue-text"
                            id="save-asset" (click)="save()" [disabled]="!isAValidPage">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.1000101001' | translate }}
                          </button>
                        </ng-container>
                        <ng-container *appAuthKey="'UBZ_REGISTER_COPY'">
                          <button *ngIf="isEditable === false" type="button" class="btn btn-empty waves-effect waves-secondary pull-right blue-text"
                            id="copia-asset" (click)="copy()">
                            <i class="fa fa-files-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.1000101010' | translate }}
                          </button>
                        </ng-container>
                        <ng-container *appAuthKey="'UBZ_REGISTER_DELETE'">
                          <button type="button" class="btn btn-empty waves-effect waves-secondary pull-right blue-text" data-toggle="modal" data-target="#remove-asset"
                            (click)="delete()">
                            <i class="fa fa-trash-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.1000101011' | translate }}
                          </button>
                        </ng-container>
                        <ng-container *appAuthKey="'UBZ_REGISTER_EDIT'">
                          <button *ngIf="isEditable === false" type="button" class="btn btn-empty waves-effect waves-secondary pull-right blue-text"
                            id="modifica-asset" (click)="edit()">
                            <i class="fa fa-pencil-square-o" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.1000101100' | translate }}
                          </button>
                        </ng-container>
                      </fieldset>
                    </div>
                  </div>
                </div>
                <div class="row inner-base-wrapper">
                  <fieldset [disabled]="!_landingService.positionLocked || _landingService.isLockedTask[currentTask] || haveDisabledFields || !isEditable">
                    <div *ngIf="staticPageContent && selectedAsset" class="col-sm-4 form-group">
                      <label>{{'UBZ.SITE_CONTENT.1000101101' | translate }}*</label>
                      <app-calendario
                        [(ngModel)]="staticPageContent['docConclusionDate']"
                        [name]="'docConclusionDate'"
                        [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                        [required]="true"
                        (ngModelChange)="pageIsValid()"
                        [maxDate]="getConclusionDateLimit()">
                      </app-calendario>
                    </div>
                    <ng-container *ngIf="appraisalInfo && (isInterno || isEsterno)">
                      <div *ngIf="staticPageContent && selectedAsset" class="col-sm-4 form-group">
                        <label *ngIf="isInterno">{{'UBZ.SITE_CONTENT.1000101110' | translate }}*</label>
                        <label *ngIf="isEsterno">{{'UBZ.SITE_CONTENT.1010110010' | translate }}*</label>
                        <app-calendario
                          [name] = "'surveyDate'"
                          [(ngModel)]="staticPageContent['surveyDate']"
                          [required] = "true"
                          [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                          [maxDate]="maxAdmissibleDate"
                          (ngModelChange)="pageIsValid()">
                        </app-calendario>
                      </div>
                    </ng-container>
                    <div class="col-sm-12">
                      <div class="row">
                        <div class="col-sm-12">
                          <accordion *ngIf="staticPageContent && selectedAsset" class="panel-group" id="accordion">
                            <app-accordion-application-form [overwriteAPFData]="true" (change)="pageIsValid()" (calendarChange)="pageIsValid()" [idCode]="selectedAsset.idObject"
                              page="CATASTO" [positionId]="positionId" [drivers]="{'DD_ORD_PAG': 1}" [formDisabled]="!_landingService.positionLocked || _landingService.isLockedTask[currentTask] || haveDisabledFields || !isEditable">
                            </app-accordion-application-form>
                            <app-property #property (change)="pageIsValid()" [pageContent]="staticPageContent['ownershipdetails']" [positionId]="positionId"></app-property>
                            <app-accordion-application-form [overwriteAPFData]="true" (change)="pageIsValid()" (calendarChange)="pageIsValid()" [idCode]="selectedAsset.idObject"
                              page="CATASTO" [positionId]="positionId" [drivers]="{'DD_ORD_PAG': 2}" [formDisabled]="!_landingService.positionLocked || _landingService.isLockedTask[currentTask] || haveDisabledFields || !isEditable"></app-accordion-application-form>
                            <app-accordion-application-form [overwriteAPFData]="true" (change)="pageIsValid()" (calendarChange)="pageIsValid()" [idCode]="selectedAsset.idObject"
                              page="CATASTO" [positionId]="positionId" [drivers]="{'DD_ORD_PAG': 4}" [formDisabled]="!_landingService.positionLocked || _landingService.isLockedTask[currentTask] || haveDisabledFields || !isEditable">
                            </app-accordion-application-form>
                            <app-accordion-application-form [overwriteAPFData]="true" (change)="pageIsValid()" (calendarChange)="pageIsValid()" [idCode]="selectedAsset.idObject"
                              page="CATASTO" [positionId]="positionId" [drivers]="{'DD_ORD_PAG': 5}" [formDisabled]="!_landingService.positionLocked || _landingService.isLockedTask[currentTask] || haveDisabledFields || !isEditable">
                            </app-accordion-application-form>
                            <app-appraisal-template-validation *ngIf="_positionService.isInternalSecondOpinion" (statusChange)="pageIsValid()" [positionId]="positionId"
                              [assetId]="selectedAsset.idObject" templateName="CATASTO" [haveDisabledFields]="haveDisabledFields"></app-appraisal-template-validation>
                          </accordion>
                        </div>
                      </div>
                    </div>
                  </fieldset>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<app-new-asset-modal [isOpen]="newAssetModalIsOpen" [positionId]="positionId" (modalClose)="closeNewAssetModal()" [requestId]="requestId"></app-new-asset-modal>
<!-- CUSTOM MODAL gestisce la cancellazione della'asset -->
<app-custom-modal [modalType]="'delete'" [isOpen]="deleteAssetModalIsOpen" [largeModalFlag]="false" [headerTitle]="'UBZ.SITE_CONTENT.1000101011'"
  [positionId]="''" [idCode]="''" [apfString]="''" [messagesArray]="['UBZ.SITE_CONTENT.1011001111']" [buttonTitle]="['UBZ.SITE_CONTENT.11110', 'UBZ.SITE_CONTENT.100000']"
  [disabledFlag]="false" (modalSubmit)="submitDeleteModal()" (modalClose)="closeDeleteModal()">
</app-custom-modal>

<fieldset [disabled]="!_landingService.positionLocked">
  <app-navigation-footer showSaveDraft="true" showPrevious="true" [saveIsEnable]="areAllAssetsValid() && !isEditable" [showCancelButton]="false"
    (saveButtonClick)="goToNextTask()" (previousButtonClick)="goToPreviousTask()" (closeDraftButtonClick)="goToGenericTask()"
    [activeTaskCode]="currentTask" [saveDraftCallback]="draftButtonCallback"></app-navigation-footer>
</fieldset>
