import { Component, OnInit, Inject } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { Observable } from 'rxjs/Observable';

import { LandingService } from '../../services/landing/landing.service';
import { WizardService } from '../../../shared/wizard/services/wizard.service';
import { WizardElement } from '../../../shared/wizard/model/wizard-element';
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';

import { Subscription } from 'rxjs/Subscription';
import 'rxjs/add/operator/switchMap';
import { GenericInfoService } from '../../services/generic-info/generic-info.service';
import { PositionService } from '../../../shared/position/position.service';

@Component({
  selector: 'app-landing',
  templateUrl: './landing.component.html',
  styleUrls: ['./landing.component.css']
})
export class LandingComponent implements OnInit {
  positionId: string;
  wizardCode: string;
  taskId: string;
  taskCod: string;
  readOnly: string;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    public landingService: LandingService,
    private genericInfoService: GenericInfoService,
    private positionService: PositionService,
    private wizardService: WizardService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) {}

  ngOnInit() {
    this.redirectToLandingPage();
  }

  redirectToLandingPage() {
    this.activatedRoute.params
      .switchMap((params: Params) => {
        this.positionId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        this.taskId = params['taskId'];
        this.taskCod = params['taskCod'];
        this.readOnly = params['readOnly'];
        let response: Observable<any>;
        if (this.wizardCode === 'WRPE') {
          response = Observable.forkJoin(
            this.wizardService.getWizard(this.positionId),
            this.genericInfoService.getGenericInfo(this.positionId, this.wizardCode),
            this.positionService.getPositionDetail(this.positionId)
          );
        } else {
          response = this.wizardService.getWizard(this.positionId);
        }
        return response;
        ;
      })
      .subscribe(response => {
        let wiz = response;
        let origin = null;
        let requestOnAir = false;
        let migrAddInfo = '';
        let macroProcess = '';
        let statusCode = '';
        if (this.wizardCode === 'WRPE') {
          wiz = response[0];
          origin = response[1].originationProcess;
          requestOnAir = response[1].requestOnAir;
          migrAddInfo = response[1].migrAddInfo;
          macroProcess = response[1].macroProcess;
          statusCode = response[2].statusCode;
        }
        const route = this.landingService.getLandingPage(
          this.positionId,
          this.wizardCode,
          wiz,
          origin,
          this.taskId,
          this.taskCod,
          this.readOnly,
          requestOnAir,
          migrAddInfo,
          macroProcess,
          statusCode
        );
        if (route) {
          this.router.navigate([route]);
        } else {
          this.router.navigate(['index']);
        }
      });
  }
}
