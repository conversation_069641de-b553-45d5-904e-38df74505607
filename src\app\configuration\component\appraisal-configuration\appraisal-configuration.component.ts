import { Component, OnInit, Input } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { DomainService } from '../../../shared/domain/domain.service';
import { ConfigurationService } from '../../service/configuration.service';
import { AppraisalConfigurationRule } from '../../configuration.models';

@Component({
  selector: 'app-appraisal-configuration',
  templateUrl: './appraisal-configuration.component.html',
  styleUrls: ['./appraisal-configuration.component.css']
})
export class AppraisalConfigurationComponent implements OnInit {
  @Input() configurations: AppraisalConfigurationRule[];
  public selectedRule: AppraisalConfigurationRule;
  private selectedRuleIndex: number;
  macroProcessTypes: any[];
  posSegments: any[];
  appraisalTypes: any[];
  scopeTypes: any[];
  structureTypes: any[];
  goodTypes: any[];
  appraisalOwners: any[];
  editAction = false;
  @Input() length: number;

  public ruleToExpand: AppraisalConfigurationRule = new AppraisalConfigurationRule();

  constructor(
    private _domainService: DomainService,
    private _configurationService: ConfigurationService
  ) {}

  ngOnInit() {
    Observable.forkJoin(
      this._domainService.newGetDomain('UBZ_DOM_MACRO_PROCESS'),
      this._domainService.newGetDomain('UBZ_DOM_POS_SEGMENT'),
      this._domainService.newGetDomain('UBZ_DOM_APPRAISAL_TYPE'),
      this._domainService.newGetDomain('UBZ_DOM_SCOPE_TYPE'),
      this._domainService.newGetDomain('UBZ_DOM_STRUCTURE_TYPE'),
      this._domainService.newGetDomain('UBZ_DOM_GOOD_TYPE'),
      this._domainService.newGetDomain('UBZ_DOM_APPRAISAL_OWNER')
    ).subscribe(res => {
      this.macroProcessTypes = res[0];
      this.posSegments = res[1];
      this.appraisalTypes = res[2];
      this.scopeTypes = res[3];
      this.structureTypes = res[4];
      this.goodTypes = res[5];
      this.appraisalOwners = res[6];
    });
  }

  public goToNewRule(): void {
    this.selectedRule = new AppraisalConfigurationRule();
    this.openEditAction();
  }

  public confirmEditAction(): void {
    const newRule: AppraisalConfigurationRule = JSON.parse(
      JSON.stringify(this.selectedRule)
    );
    if (this.selectedRuleIndex === -1) {
      this.configurations.push(newRule);
    } else {
      this.configurations[this.selectedRuleIndex] = newRule;
    }
    this.closeEditAction();
  }

  public abortEditAction(): void {
    this.closeEditAction();
  }

  private openEditAction(): void {
    this._configurationService.setSaveButtonState(false);
    this.editAction = true;
  }

  private closeEditAction(): void {
    this.selectedRuleIndex = -1;
    this.editAction = false;
    this._configurationService.setSaveButtonState(true);
  }

  public removeRule(item: AppraisalConfigurationRule): void {
    item.activeFlag = false;
  }

  public modifySelectedRule(item: AppraisalConfigurationRule) {
    this.selectedRule = JSON.parse(JSON.stringify(item));
    this.selectedRule.bornDate = new Date(this.selectedRule.bornDate);
    this.selectedRule.deadDate = new Date(this.selectedRule.deadDate);
    this.selectedRuleIndex = this.configurations.indexOf(item);
    this.openEditAction();
  }

  public selectRuleToExpand(item: AppraisalConfigurationRule): void {
    if (this.ruleToExpand.ruleId === item.ruleId) {
      this.ruleToExpand = new AppraisalConfigurationRule();
    } else {
      this.ruleToExpand = item;
    }
  }
}
