/**
 * Utility functions for handling dynamic app codes and base href
 */

/**
 * Get the current app code from various sources
 * @returns string - The current app code (UBZ or ZA0)
 */
export function getCurrentAppCode(): string {
  // Check if app code was set by the index.html script
  if ((window as any).DETRIM_APP_CODE) {
    return (window as any).DETRIM_APP_CODE;
  }

  // Fallback: determine from current base href
  const baseElement = document.querySelector('base');
  if (baseElement) {
    const href = baseElement.getAttribute('href');
    if (href && href.indexOf('/ZA0/') !== -1) {
      return 'ZA0';
    }
  }

  // Fallback: check URL
  const currentPath = window.location.pathname;
  if (currentPath.indexOf('/ZA0/') !== -1) {
    return 'ZA0';
  }

  // Default to UBZ
  return 'UBZ';
}

/**
 * Check if current app code is ZA0
 * @returns boolean - True if current app code is ZA0
 */
export function isZA0(): boolean {
  return getCurrentAppCode() === 'ZA0';
}

/**
 * Check if current app code is UBZ
 * @returns boolean - True if current app code is UBZ
 */
export function isUBZ(): boolean {
  return getCurrentAppCode() === 'UBZ';
}

/**
 * Get the current base href
 * @returns string - Current base href
 */
export function getCurrentBaseHref(): string {
  const baseElement = document.querySelector('base');
  if (baseElement) {
    return baseElement.getAttribute('href') || '/UBZ-EFA-PF/UBZ/';
  }
  return '/UBZ-EFA-PF/UBZ/';
}

/**
 * Generate a URL with the correct base href for the given app code
 * @param appCode - Target app code (UBZ or ZA0)
 * @param relativePath - Optional relative path to append
 * @returns string - Full URL with correct base href
 */
export function generateUrlForAppCode(appCode: string, relativePath: string = ''): string {
  const baseHref = `/UBZ-EFA-PF/${appCode.toUpperCase()}/`;
  const baseUrl = window.location.origin + baseHref;
  return relativePath ? baseUrl + relativePath.replace(/^\//, '') : baseUrl;
}

/**
 * Navigate to a different app code context
 * @param appCode - Target app code (UBZ or ZA0)
 * @param preservePath - Whether to preserve current path (default: true)
 */
export function navigateToAppCode(appCode: string, preservePath: boolean = true): void {
  const targetUrl = generateUrlForAppCode(appCode);
  
  if (preservePath) {
    // Extract current path relative to base href
    const currentPath = window.location.pathname;
    const currentBaseHref = getCurrentBaseHref();
    
    if (currentPath.startsWith(currentBaseHref)) {
      const relativePath = currentPath.substring(currentBaseHref.length);
      const fullTargetUrl = generateUrlForAppCode(appCode, relativePath);
      
      // Preserve query parameters and hash
      let finalUrl = fullTargetUrl;
      if (window.location.search) {
        finalUrl += window.location.search;
      }
      if (window.location.hash) {
        finalUrl += window.location.hash;
      }
      
      window.location.href = finalUrl;
    } else {
      window.location.href = targetUrl;
    }
  } else {
    window.location.href = targetUrl;
  }
}

/**
 * Get URL parameter value
 * @param name - Parameter name
 * @returns string - Parameter value or empty string if not found
 */
export function getUrlParameter(name: string): string {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(name) || '';
}
