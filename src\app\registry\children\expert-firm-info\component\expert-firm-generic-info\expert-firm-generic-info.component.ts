import {
  Component,
  On<PERSON><PERSON>roy,
  Input,
  Inject,
  OnChang<PERSON>,
  SimpleChanges
} from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { Subscription } from 'rxjs/Subscription';
import { APP_CONSTANTS, IAppConstants } from '../../../../../app.constants';
import { ExpertFirm } from '../../../../model/registry.models';
import { DomainService } from '../../../../../shared/domain/domain.service';
import { RegistryService } from '../../../../service/registry.service';

@Component({
  selector: 'app-expert-firm-generic-info',
  templateUrl: './expert-firm-generic-info.component.html',
  styleUrls: ['./expert-firm-generic-info.component.css']
})
export class ExpertFirmGenericInfoComponent implements OnChanges, OnDestroy {
  @Input() idAnag: string;
  @Input() subjectType: string;
  @Input() subSubjectType: string;
  public genericData: any = {};
  public abilitationData: any = {};

  public genericInfoEditable = false;
  public assignmentInfoEditable = false;
  public expertTypes: any = {};
  public provinces: any = {};
  public anagStatus: any[];
  public cities: any = {};
  public selectedCity: string;
  public regions: any[] = [];
  private _subscription: Subscription;
  public isValidIban: boolean = false;

  constructor(
    private _domainService: DomainService,
    private _registryService: RegistryService,
    @Inject(APP_CONSTANTS) private _constants: IAppConstants
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes && this.idAnag && this.subjectType) {
      this._subscription = Observable.forkJoin(
        this._domainService.newGetDomain('UBZ_DOM_EXPERT_TYPE'),
        this._domainService.newGetDomain('UBZ_DOM_PROVINCE'),
        this._domainService.newGetDomain('UBZ_DOM_ANAG_STATUS')
      )
        .switchMap(res => {
          this.expertTypes = res[0];
          this.provinces = res[1];
          this.anagStatus = res[2];
          this.refreshGenericData();
          this.refreshAbilitationData();
          return this._domainService.newGetDomain('UBZ_DOM_REGION');
        })
        .subscribe(res => {
          this.regions = res;
        });
    }
  }

  ngOnDestroy() {
    if (this._subscription && !this._subscription.closed) {
      this._subscription.unsubscribe();
    }
  }

  public editGenericInfo(event: any): void {
    this.genericInfoEditable = true;
    this.stopEventPropagation(event);
  }

  public cancelGenericInfoEdit(event: any): void {
    this.stopEventPropagation(event);
    this.genericInfoEditable = false;
    this.refreshGenericData();
  }

  public editAssignmentInfo(event: any): void {
    this.assignmentInfoEditable = true;
    this.stopEventPropagation(event);
  }

  public cancelAssignmentInfoEdit(event: any): void {
    this.stopEventPropagation(event);
    this.assignmentInfoEditable = false;
    this.refreshAbilitationData();
  }

  private stopEventPropagation(event: any): void {
    event.stopPropagation();
  }

  public calculateCities(event: any) {
    this._domainService.getDomainCity(event.target.value).subscribe(res => {
      this.cities = res;
    });
  }

  public saveGenericInfo(event: any) {
    if (this.subjectType === 'SOC') {
      delete this.genericData['competenceProvince'];
    }
    this.stopEventPropagation(event);
    this._registryService
      .saveGenericData(this.idAnag, this.genericData, this.subjectType)
      .subscribe(res => {
        this.refreshGenericData();
        this.genericInfoEditable = false;
      });
  }

  public saveAbilitationData(event: any) {
    this.stopEventPropagation(event);
    const toSave = JSON.parse(JSON.stringify(this.abilitationData));
    toSave.startAbilitation = Date.parse(toSave.startAbilitation);
    toSave.endAbilitation = Date.parse(toSave.endAbilitation);
    this._registryService
      .saveAbilitationData(this.idAnag, toSave, this.subjectType)
      .subscribe(res => {
        this.assignmentInfoEditable = false;
      });
  }

  public findCityTranslationCode(): string {
    for (const city of this.cities) {
      if (this.genericData.city === city.domCode) {
        return city.translationCod;
      }
    }
  }

  refreshGenericData() {
    this._registryService
      .getGenericData(this.idAnag, this.subjectType)
      .switchMap(res => {
        this.genericData = res;
        if (
          this.subjectType === this._constants.SubjectType['SOC'] &&
          !this.genericData.competenceProvince
        ) {
          this.genericData.competenceProvince = '';
        }
        return this._domainService.getDomainCity(this.genericData.province);
      })
      .subscribe(res => {
        this.cities = res;
        this.selectedCity = this.findCityTranslationCode();
      });
  }

  private refreshAbilitationData() {
    this._registryService
      .getAbilitationData(this.idAnag, this.subjectType)
      .subscribe(res => {
        this.abilitationData = res;
        if (this.abilitationData.startAbilitation !== null) {
          this.abilitationData.startAbilitation = new Date(
            this.abilitationData.startAbilitation
          );
        }
        if (this.abilitationData.endAbilitation !== null) {
          this.abilitationData.endAbilitation = new Date(
            this.abilitationData.endAbilitation
          );
        }
      });
  }

  checkLenghtIban(input: string){
    if (input.length == 27){
      this.isValidIban = false;
    }else{
      this.isValidIban = true;
    }
  }
}
