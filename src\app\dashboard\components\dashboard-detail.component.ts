import {
  Component,
  OnInit,
  OnChanges,
  Input,
  SimpleChanges
} from '@angular/core';
import { Observable } from 'rxjs/Observable';

import { SearchResultsData } from '../../shared/search/search-results-data';
import { CounterInput, Counter } from '../model/counter-input';
import { DomainService } from '../../shared/domain/domain.service';
import { PageHeaders } from '../model/page-headers';
import { PageFieldConditions } from '../model/page-field-conditions';
import { Domain } from '../../shared/domain/domain';
import { Http, Response } from '@angular/http';
import { SearchService } from '../../shared/search/search.service';

@Component({
  selector: 'app-dashboard-detail',
  templateUrl: './dashboard-detail.component.html',
  styleUrls: ['./dashboard-detail.component.css']
})
export class DashboardDetailComponent implements OnInit, OnChanges {
  @Input() searchTypes: string;
  @Input() active: boolean;

  inChargeUser: boolean;
  inChargeBranch: boolean;
  excel = false;
  page = 1;
  pageSize = 10;
  orderBy = 'false';
  asc = false;
  filter: any;
  domainList: any;
  phaseDom: any[] = [];
  pageHeaders = new PageHeaders();
  headFields;
  pageFieldConditions = new PageFieldConditions();
  fieldsConditions;
  positionListResults: SearchResultsData = new SearchResultsData();

  activeTypes: any;
  counterInput: CounterInput;
  selectedCounterElementsNumb = 0;
  selectedCounterId: string;
  selectedSortedField: any;
  renderCounter = false;
  activeSla = false;
  selectedItem = '';
  domains: Domain[] = [];
  resSal: any;
  statusSal = '';
  phaseSal = '';
  salArray = [];
  filteredAppraisal: number;
  countFilter: number;

  constructor(
    protected domainService: DomainService,
    public searchService: SearchService
  ) {}

  ngOnInit() {
    Observable.forkJoin(
      this.domainService.getStatusName(),
      this.domainService.newGetDomain('UBZ_DOM_PHASE')
    ).subscribe(x => {
      this.domainList = x[0];
      this.phaseDom = x[1];
    });

    this.searchService.getSalParam().subscribe(res => {
      this.salArray = res;
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['active'].currentValue) {
      this.retrieveData();
    }
  }

  selectSate() {
    // find the number of appraisal selected
    if (this.selectedItem === '') {
      const tmpFilter: any = {};
      tmpFilter.inChargeUser = this.filter.inChargeUser;
      tmpFilter.inChargeBranch = this.filter.inChargeBranch;
      tmpFilter.phase = this.filter.phase;
      this.filter = tmpFilter;
    } else {
      this.statusSal = this.selectedItem;
      this.filter.statusSal = this.statusSal;
    }
    this.getPositionsCounts(
      this.filter.inChargeUser,
      this.filter.inChargeBranch,
      this.selectedItem,
      this.filter.phase
    ).subscribe(res => {
      this.selectedCounterElementsNumb = 0;
      for (const ind in res) {
        this.selectedCounterElementsNumb = res[ind].count;
      }
    });
    if (this.page === 1) {
      this.getPositionsData(
        this.excel,
        this.page,
        this.pageSize,
        this.orderBy,
        this.asc,
        this.filter
      ).subscribe(y => {
        this.positionListResults.positions = y;
        this.positionListResults.count = y.length;
        this.renderCounter = true;
      });
    } else {
      this.page = 1;
    }
    this.selectedItem = '';
  }

  retrieveData() {
    if (this.searchTypes === 'USER') {
      this.inChargeUser = true;
      this.inChargeBranch = false;
    } else {
      this.inChargeUser = false;
      this.inChargeBranch = true;
    }
    this.filter = {};
    this.filter.inChargeUser = this.inChargeUser;
    this.filter.inChargeBranch = this.inChargeBranch;

    this.getPositionsCounts(this.inChargeUser, this.inChargeBranch).subscribe(
      x => {
        this.counterInput = new CounterInput();
        this.counterInput.counter = [];
        let index = 0;
        for (const i2 in x) {
          if (x.hasOwnProperty(i2)) {
            if (index === 0) {
              this.counterInput.counter[index] = new Counter(
                x[i2].id,
                x[i2].translation,
                x[i2].count,
                true
              );
              this.filter.phase = x[i2].id;
              this.getPositionsData(
                this.excel,
                this.page,
                this.pageSize,
                this.orderBy,
                this.asc,
                this.filter
              ).subscribe(y => {
                this.positionListResults.positions = y;
                this.positionListResults.count = y.length;
                this.renderCounter = true;
              });
            } else {
              this.counterInput.counter[index] = new Counter(
                x[i2].id,
                x[i2].translation,
                x[i2].count,
                false
              );
            }
            index++;
          }
        }
        if (this.counterInput.counter[0]) {
          this.selectedCounterElementsNumb = this.counterInput.counter[0].num;
        }
        index === 0
          ? (this.counterInput.numElem = 0)
          : (this.counterInput.numElem = index + 1);
      }
    );
  }

  protected refreshPositionList() {
    this.getPositionsData(
      this.excel,
      this.page,
      this.pageSize,
      this.orderBy,
      this.asc,
      this.filter
    ).subscribe(y => {
      this.positionListResults.positions = y;
      this.positionListResults.count = y.length;
    });
  }

  changeType() {
    this.page = 1;
    this.refreshPositionList();
  }

  changePageSize() {
    this.page = 1;
    this.refreshPositionList();
  }

  changePage(event: any) {
    this.page = event.page;
    this.refreshPositionList();
  }

  changeCounter(event) {
    this.selectedCounterId = event.counterId;
    this.filter = {};
    this.filter.inChargeUser = this.inChargeUser;
    this.filter.inChargeBranch = this.inChargeBranch;
    this.filter.phase = event.counterId;
    this.counterInput = event.obj;
    this.selectedCounterElementsNumb = this.counterInput.counter[
      event.index
    ].num;
    this.changeType();
    if (event.counterId === 'UBZ_ESCALATIONMGMT') {
      this.activeSla = true;
    } else {
      this.activeSla = false;
    }
  }

  evalExpression(expr: string) {
    return eval(expr);
  }

  getStatusPhase(phase: string, status: string) {
    let ret = '';
    if (phase && status && this.domainList && this.domainList[phase + status]) {
      ret = this.domainList[phase + status].translationStatusCod;
    }
    return ret;
  }

  sortPage(field: any) {
    if (field.orderDesc === null) {
      if (this.selectedSortedField) {
        this.selectedSortedField.orderDesc = null;
      }
      this.selectedSortedField = field;
      field.orderDesc = false;
    } else {
      field.orderDesc = !field.orderDesc;
    }
    this.orderBy = field.orderBy;
    this.asc = field.orderDesc;
    this.page = 1;
    this.refreshPositionList();
  }

  getPositionsCounts(
    inChargeUser: boolean,
    inChargeBranch: boolean,
    statusSal?: string,
    phase?: string
  ): Observable<any> {
    return Observable.empty();
  }

  getPositionsData(
    excel: boolean,
    page: number,
    pageSize: number,
    orderBy: string,
    asc: boolean,
    filter: any
  ): Observable<any> {
    return Observable.empty();
  }
}
