<footer [ngClass]="footerClass">
  <div class="row">
    <div class="col-xs-4">
      <ng-container *appAuthKey="'UBZ_NAVIGATION.FOOTER_CANCEL'">
        <button [disabled]="_landingService.isLockedTask[activeTaskCode]" type="button"
          class="btn btn-secondary waves-effect waves-secondary" (click)="cancelModal.show()" *ngIf="showCancelButton">
          <span *ngIf="cancelButtonString && cancelButtonString !== ''">
            {{ cancelButtonString }}
          </span>
          <span *ngIf="!cancelButtonString || cancelButtonString === ''">
            {{'UBZ.SITE_CONTENT.1010' | translate }}
          </span>
        </button>
      </ng-container>
    </div>
    <div class="col-xs-8 btn-set">
      <div *ngIf="onIndividualAssignment && _valConf" class="col-sm-6">
        <p class="last-modification">
          {{'UBZ.SITE_CONTENT.11111010001' | translate }} {{ _valConf.lastSavedTs | customDateTime }}
          <br>
          {{'UBZ.SITE_CONTENT.11111010010' | translate }} {{ _valConf.user }}
        </p>
      </div>
      <ng-container *appAuthKey="'UBZ_NAVIGATION.FOOTER_NEXT.AND.SAVE'">
        <button type="submit" class="btn btn-primary waves-effect pull-right" id="client-confirmation"
          *ngIf="showSaveButton" (click)="saveAndGoAhead()"
         
          [disabled]="_landingService.isLockedTask[activeTaskCode] || !_saveIsEnable">
          <span *ngIf="confirmButtonString && confirmButtonString !== ''">
            {{ confirmButtonString }}
          </span>
          <span *ngIf="!confirmButtonString || !(confirmButtonString !== '')">
            {{'UBZ.SITE_CONTENT.1011' | translate }}
          </span>
        </button>
        <button type="button" class="btn btn-primary waves-effect pull-right" id="restore"
          [disabled]="_valConf?.processed" *ngIf="onIndividualAssignment" (click)="restoreModification()">
          {{ 'UBZ.SITE_CONTENT.11111000101' | translate }}
        </button>
        <button type="button" class="btn btn-primary waves-effect pull-right" id="undo"
          [disabled]="_valConf?.saved && !_undoIsEnabled" *ngIf="onIndividualAssignment" (click)="undoModification()">
          {{ 'UBZ.SITE_CONTENT.11111000100' | translate }}
        </button>
      </ng-container>
      <ng-container *appAuthKey="'UBZ_NAVIGATION.FOOTER_SAVE'">
        <button [disabled]="_landingService.isLockedTask[activeTaskCode] || saveDraftDisabled" *ngIf="showSaveDraft"
          type="button" class="btn btn-tertiary waves-effect waves-secondary pull-right"
          (click)="callSaveDraftFunction()">
          {{'UBZ.SITE_CONTENT.1100' | translate }}
        </button>
      </ng-container>
      <!-- Se processo EMP non si visualizza footer, non sono possibili azioni poichè la richiesta è in sola lettura -->
      <ng-container *ngIf="_landingService.originationProcess !== 'EMP'">
        <ng-container *appAuthKey="'UBZ_NAVIGATION.FOOTER_PREVIOUS'">
          <button [disabled]="_landingService.isLockedTask[activeTaskCode] " *ngIf="showPrevious" id="return-1"
            type="button" class="btn btn-clean waves-effect waves-secondary return pull-right" (click)="previous()">
            <i class="fa fa-angle-left" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.1101' | translate }}
          </button>
        </ng-container>
        <ng-container *appAuthKey="'UBZ_NAVIGATION.FOOTER_MODIFY'">
          <button *ngIf="_landingService.isLockedTask[activeTaskCode] || (isFrazionamento)"
            [disabled]="!(_landingService.isLockedTask[activeTaskCode] || (isFrazionamento && isModifyActive))"
            id="return-1" type="button" class="btn btn-clean waves-effect waves-secondary return pull-right"
            (click)="openModifyModal()">
            {{'UBZ.SITE_CONTENT.11111' | translate }}
          </button>
        </ng-container>
      </ng-container>
    </div>
  </div>
</footer>

<fieldset [disabled]="_landingService.isLockedTask[activeTaskCode]">
  <div *ngIf="showDraftModal" class="modal fade" bsModal #saveModal="bs-modal" tabindex="-1" role="dialog"
    aria-labelledby="myModalLabel" (onHidden)="hideDraftModal()" [config]="{show: 'true'}">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h2>{{'UBZ.SITE_CONTENT.1100' | translate }}</h2>
          <button type="button" class="close" aria-label="Close" (click)="hideDraftModal()">
            <i class="icon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <p>{{'UBZ.SITE_CONTENT.1110' | translate }}.</p>
          <p>{{'UBZ.SITE_CONTENT.1111' | translate }}.</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary waves-effect" (click)="saveDraft()">
            {{'UBZ.SITE_CONTENT.10000' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="modal fade" bsModal #cancelModal="bs-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h2>{{'UBZ.SITE_CONTENT.1010100' | translate }}</h2>
          <button type="button" class="close pull-right" aria-label="Close" (click)="cancelModal.hide()">
            <i class="icon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <p>{{'UBZ.SITE_CONTENT.1100000' | translate }}?</p>
          <p>{{'UBZ.SITE_CONTENT.1100001' | translate }}.</p>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary waves-effect"
            (click)="cancelPosition()">{{'UBZ.SITE_CONTENT.1100010' | translate }}</button>
        </div>
      </div>
    </div>
  </div>
</fieldset>
<!-- CUSTOM MODAL gestisce la modale di conferma accettazione modifica -->
<app-custom-modal [modalType]="'delete'" [isOpen]="modifyModalIsOpen" [largeModalFlag]="false"
  [headerTitle]="'UBZ.SITE_CONTENT.1001101100'" [positionId]="''" [idCode]="''" [apfString]="''"
  [messagesArray]="messagesArray" [buttonTitle]="['UBZ.SITE_CONTENT.1100010', 'UBZ.SITE_CONTENT.100000']"
  [disabledFlag]="false" (modalSubmit)="submitModifyModal()" (modalClose)="closeModifyModal()">
</app-custom-modal>