<div *ngIf="isOpen" class="modal fade" id="custom-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #customModal="bs-modal" (onHidden)="closeModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" [ngClass] = "{'modal-lg': largeModalFlag}" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{ headerTitle | translate }}</h2>
        <button type="button" class="close pull-right" aria-label="Close" (click)="closeModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <!--Le modal di aggiunta e modifica hanno l'apf, quella di delete ha solamente del testo  -->
        <div *ngIf = "modalType !== 'delete'">
          <app-application-form #apForm [page]="apfString" [positionId]="positionId" [idCode] = "idCode"></app-application-form>
        </div>
        <!-- modalType = delete comprende sia modali di cancellazione che quelle statiche con testo descrittivo -->
        <div class="row" *ngIf = "modalType === 'delete'">
          <div class="col-sm-12" *ngFor = "let message of messagesArray">
            <p>{{ message | translate }}</p>            
          </div>
        </div>
        <form  id="ngForm" #f="ngForm"> 
          <div class="row" *ngIf = "modalType === 'deleteAsset'" 
            [style.padding-top]="'10px'"
            [style.padding-bottom]="'10px'">
            <div class="col-sm-12 ml-auto" *ngFor = "let message of messagesArray">
              <p>{{ message | translate }}</p>            
            </div>
            <div class="col-sm-1 ml-auto">
            </div>
            <div class="col-sm-12 ml-auto" style="margin-top:10px">
              <label>{{'UBZ.SITE_CONTENT.11110001011' | translate }}*</label>
              <select name="motivation" [(ngModel)]="motivation" class="form-control" required>
                <option value="null" disabled selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                <option *ngFor="let reason of motivationList" value="{{ reason.domCode }}">{{ reason.translationCod | translate }}</option>
              </select>
            </div>
            <div class="col-sm-12 ml-auto" style="margin-top:10px">
              <label>{{'UBZ.SITE_CONTENT.11110001100' | translate }}*</label>
              <textarea class="form-control notes" name="notes" type="text" [(ngModel)]="notes" maxlength="3800" placeholder="{{'UBZ.SITE_CONTENT.10001011' | translate }}..." required>
              </textarea>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer text-center">
        <button form="ngForm" *ngIf = "modalType === 'deleteAsset'" class="btn btn-primary waves-effect" id="deleteAssetBtn" type="button" (click)="submit('deleteAsset')" [disabled] = "!f.form.valid || (disabledFlag) && (!apForm || !apForm.appForm || apForm.appForm.invalid)">
          {{ buttonTitle[0] | translate | uppercase}}
        </button>
        <button *ngIf = "modalType !== 'deleteAsset'" class="btn btn-primary waves-effect" type="button" (click)="submit()" [disabled] = "(disabledFlag) && (!apForm || !apForm.appForm || apForm.appForm.invalid)">
          {{ buttonTitle[0] | translate | uppercase}}
        </button>
        <button *ngIf = "modalType === 'delete' || modalType === 'deleteAsset'" class="btn btn-secondary waves-effect" type="button" (click)="closeModal()">
          {{ buttonTitle[1] | translate }}
        </button>     
      </div>
    </div>
  </div>
</div>