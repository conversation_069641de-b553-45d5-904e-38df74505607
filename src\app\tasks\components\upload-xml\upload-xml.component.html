<div *ngIf="isOpen" class="modal fade" id="aggiungi-documento" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #stateBox="bs-modal" (onHidden)="clickOnCloseModal()" [config]="{show: 'true'}">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Upload xml</h2>
        <button type="button" class="close pull-right" aria-label="Close" (click)="clickOnCloseModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="col-sm-12">
            <h4>{{'UBZ.SITE_CONTENT.11000001' | translate }}</h4>
            <div class="input-group">
              <input type="text" class="form-control" readonly [value]="fileName">
              <label class="input-group-btn">
                <span class="btn btn-primary waves-effect">
                  {{'UBZ.SITE_CONTENT.11000011' | translate }}&hellip; <input type="file" #fileToUpload id="sfoglia" style="display: none;" multiple (change)="setFile()">
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button id="upload-filedoc" type="submit" class="btn btn-primary waves-effect" data-dismiss="modal" [disabled]="!isSaveEnable()" (click)="clickOnUploadButton()">
          {{'UBZ.SITE_CONTENT.110101000' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>
