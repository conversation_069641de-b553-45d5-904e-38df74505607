import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs/Observable';

import { DashboardDetailComponent } from '../dashboard-detail.component';
import { SearchService } from '../../../shared/search/search.service';
import { DomainService } from '../../../shared/domain/domain.service';

@Component({
  selector: 'app-loan-administration-all',
  templateUrl: '../dashboard-detail.component.html',
  styleUrls: ['./loan-administration-all.component.css']
})
export class LoanAdministrationAllComponent extends DashboardDetailComponent
  implements OnInit {
  headFields = this.pageHeaders['LAA'];
  fieldsConditions = this.pageFieldConditions['LAA'];

  constructor(
    public searchService: SearchService,
    public domainService: DomainService,
    private router: Router
  ) {
    super(domainService, searchService);
  }

  ngOnInit() {
    super.ngOnInit();
  }

  getPositionsCounts(
    inChargeUser: boolean,
    inChargeBranch: boolean,
    statusSal?: string,
    phase?: string
  ): Observable<any> {
    return this.searchService.getTaskCounts(
      inChargeUser,
      inChargeBranch,
      statusSal,
      phase
    );
  }

  getPositionsData(
    excel: boolean,
    page: number,
    pageSize: number,
    orderBy: string,
    asc: boolean,
    filter: any
  ): Observable<any> {
    return this.searchService.getTaskData(
      excel,
      page,
      pageSize,
      orderBy,
      asc,
      filter
    );
  }

  goToTarget(positionId, appraisalId, taskId, taskCod) {
    if (appraisalId) {
      this.router.navigateByUrl(
        `task-landing/${appraisalId}/${taskId}/${taskCod}`
      );
    } else {
      this.router.navigateByUrl(
        `wizard-detail/WRPE/${positionId}/${taskId}/${taskCod}`
      );
    }
  }
}
