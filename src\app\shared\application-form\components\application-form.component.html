<form #appForm="ngForm" (ngSubmit)="submitForm()" novalidate>
  <ng-container *ngFor="let form of applicationForms">
    <ng-container *ngFor="let section of form.sections">
      <div class="col-sm-12">
        <h3>{{section.sectionDesc}}</h3>
      </div>
      <div *ngFor="let row of section.rows" class="row">
        <ng-container *ngFor="let field of row.fields" >
          <ng-template let-condRenderIf="a" let-condReadOnly="b"  let-condRequired="c"
          [ngTemplateOutletContext]="{ 
            a: (condition | evaluateCondition : mapApf : field.renderIf : section.formCode : this.model), 
            b: (condition | evaluateCondition : mapApf : field.readOnly : section.formCode : this.model),
            c: (condition | evaluateCondition : mapApf : field.required : section.formCode : this.model)
          }" 
            [ngTemplateOutlet]="selfie" #selfie>
          
          <div *ngIf="field.span > 0" class="{{field.styleClass}} form-group"
            [style.visibility]="condRenderIf ? '' : 'hidden'"
            [style.max-height]="field.type === 'Textarea' ? '63.49px' : ''">

            <!-- PER I CAMPI TEXTAREA SI AGGIUNGE IL BUTTON CHE APRE TOOLTIP IN SOLA LETTURA -->
            <label *ngIf="field.showLabel">
              <span *ngIf="field.type === 'Textarea'">
                <i class="icon-search note-tooltip" [tooltip]="model[section.formCode][field.code]"
                  triggers="click"></i>
              </span>
              {{field.type !== 'Checkbox' ? (('UMF_DATA_DICTIONARY|' + field.code) | translate) : ''}}{{condReadOnly && field.type !== 'Checkbox' ? '*' : '' }}
            </label>

            <ng-container *ngIf="condReadOnly || setReadOnly">
              <ng-container *ngIf="field.type !== 'Timestamp' && field.type !== 'Checkbox'">
                <input *ngIf="!field.domainCod" name="{{field.code}}" value="{{model[section.formCode][field.code]}}"
                  class="form-control" type="text" data-toggle="tooltip" title="{{field.tooltip}}" disabled>
                <input *ngIf="field.domainCod" name="{{field.code}}"
                  value="{{getValueForDomCodeAndModifyModel (model[section.formCode][field.code], section.formCode, field) | translate}}"
                  class="form-control" type="text" data-toggle="tooltip" title="{{field.tooltip}}" disabled>
              </ng-container>
              <input *ngIf="field.type === 'Timestamp'" name="{{field.code}}" type="text"
                value="{{model[section.formCode][field.code] | date: 'dd-MM-yyyy'}}" class="form-control"
                data-toggle="tooltip" title="{{field.tooltip}}" disabled>
              <div *ngIf="field.type === 'Checkbox'" class="custom-checkbox" data-toggle="tooltip"
                title="{{field.tooltip}}">
                <input name="{{field.code}}" type="{{field.type}}" class="checkbox"
                  [checked]="model[section.formCode][field.code]" disabled>
                <label for="{{field.code}}">{{('UMF_DATA_DICTIONARY|' + field.code) | translate}}</label>
              </div>
            </ng-container>
            <ng-container *ngIf="!condReadOnly  && !setReadOnly">
              <div *ngIf="field.type === 'Text' && field.domainCod" class="custom-select">
                <select name="{{field.code}}" class="form-control" data-placement="bottom"
                  [(ngModel)]="model[section.formCode][field.code]" data-toggle="tooltip" title="{{field.tooltip}}"
                  [disabled]="field.disabled" [ngClass]="isFieldError(field.code) ? 'error' : 'valid'"
                  [required]="condRequired"
                  (change)="recalculateChilds( field , model[section.formCode][field.code] )">
                  <option value="" selected disabled>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <ng-container *ngIf="field.domainCod === 'UBZ_DOM_CATEGORY_TYPE'">
                    <option
                      *ngFor="let rowOpt of (getDomainValue(field.domainCod) | domainMapToDomainArray | sortRegCategoryType)"
                      value="{{rowOpt.domCode}}">{{rowOpt.translationCod | translate}}</option>
                  </ng-container>
                  <ng-container *ngIf="field.domainCod === 'UBZ_DOM_REG_CATEGORY_TYPE'">
                    <option
                      *ngFor="let rowOpt of (getDomainValue(field.domainCod) | domainMapToDomainArray | sortRegCategoryType)"
                      value="{{rowOpt.domCode}}">{{rowOpt.translationCod | translate}}</option>
                  </ng-container>
                  <ng-container
                    *ngIf="field.domainCod !== 'UBZ_DOM_CATEGORY_TYPE' && field.domainCod !== 'UBZ_DOM_REG_CATEGORY_TYPE'">
                    <option *ngFor="let rowOpt of (getDomainValue(field.domainCod) | domainMapToDomainArray )"
                      value="{{rowOpt.domCode}}">{{rowOpt.translationCod | translate}}</option>
                  </ng-container>
                </select>
              </div>

              <input *ngIf="field.type === 'Text' && !field.domainCod" name="{{field.code}}" class="form-control"
                [ngClass]="isFieldError(field.code) ? 'error' : 'valid'" type="{{field.type}}"
                [(ngModel)]="model[section.formCode][field.code]" data-toggle="tooltip" title="{{field.tooltip}}"
                [disabled]="field.disabled" maxlength="{{(field.precision) ? field.precision : ''}}"
                [required]="condRequired">

              <textarea *ngIf="field.type === 'Textarea'" name="{{field.code}}" #rowNum rows="1" class="form-control"
                [ngClass]="isFieldError(field.code) ? 'error' : 'valid'"
                [(ngModel)]="model[section.formCode][field.code]" data-toggle="tooltip" title="{{field.tooltip}}"
                [disabled]="field.disabled || formDisabled" data-placement="bottom"
                maxlength="{{(field.precision) ? field.precision : ''}}"
                [required]="condRequired" (focus)="rowNum.rows = 4"
                (focusout)="rowNum.rows = 1" style="display: inline-block"></textarea>

              <div *ngIf="field.type === 'Checkbox'" class="custom-checkbox" data-toggle="tooltip"
                title="{{field.tooltip}}">
                <input id="{{getUniqueId(field.code)}}" name="{{field.code}}" type="{{field.type}}" class="checkbox"
                  [ngClass]="isFieldError(field.code) ? 'error' : 'valid'"
                  [checked]="model[section.formCode][field.code]"
                  (change)="changeCheckboxValue(section.formCode , field.code)" [disabled]="field.disabled"
                  [required]="condRequired">
                <label for="{{getUniqueId(field.code)}}">{{('UMF_DATA_DICTIONARY|' + field.code) | translate}}</label>
              </div>

              <input *ngIf="field.type === 'Number'" name="{{field.code}}" type="text" class="form-control"
                [ngClass]="isFieldError(field.code) ? 'error' : 'valid'"
                [(ngModel)]="model[section.formCode][field.code]" data-toggle="tooltip" title="{{field.tooltip}}"
                [disabled]="field.disabled" maxlength="{{(field.precision) ? field.precision : '' }}"
                [required]="condRequired" appForcePattern
                regexPattern="{{ field.pattern }}">

              <app-importo *ngIf="field.type === 'Currency'" [name]="field.code"
                [required]="condRequired" [disabled]="field.disabled"
                [ngClassAdd]="isFieldError(field.code) ? 'error' : 'valid'" [ngClassCondition]="true"
                [(ngModel)]="model[section.formCode][field.code]" [title]="field.tooltip"
                [maxlength]="(field.precision) ? field.precision : ''">
              </app-importo>

              <app-calendario *ngIf="field.type === 'Timestamp'" [name]="field.code"
                [required]="condRequired" [disabled]="field.disabled"
                [ngClassAdd]="isFieldError(field.code) ? 'error' : 'valid'" [ngClassCondition]="true"
                [(ngModel)]="model[section.formCode][field.code]" [title]="field.tooltip"
                [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
                [minDate]="evalTimestampExpression(section.formCode, field.pattern, 'MIN_DATE')"
                [maxDate]="evalTimestampExpression(section.formCode, field.pattern, 'MAX_DATE')">
              </app-calendario>

              <div *ngIf="isFieldError(field.code)" class="tooltip fade bottom in" role="tooltip"
                style="top: 67px; left: 127px;">
                <div class="tooltip-arrow" style="left: 50%;"></div>
                <div class="tooltip-inner">{{'UBZ.SITE_CONTENT.1001' | translate }}.</div>
              </div>
            </ng-container>
            
          </div>
          </ng-template>
        </ng-container>
      </div>
    </ng-container>
  </ng-container>
  <div>
    <ng-content></ng-content>
  </div>
</form>