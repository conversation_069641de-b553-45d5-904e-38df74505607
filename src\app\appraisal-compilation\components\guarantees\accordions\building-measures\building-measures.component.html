<form #f="ngForm" novalidate (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>{{'UBZ.SITE_CONTENT.111100110' | translate }}
          <span class="state" [ngClass]="{'green' : (pageIsValid), 'red' : !(pageIsValid)}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
          <table class="table">
            <thead *ngIf="pageContent[0].length > 0">
              <th style="text-align: center">{{'UBZ.SITE_CONTENT.1011001010' | translate }}</th>
              <th class="col-sm-3" style="text-align: center">{{'UBZ.SITE_CONTENT.10111' | translate }}</th>
              <th class="col-sm-3" style="text-align: center">{{'UBZ.SITE_CONTENT.1000000000' | translate }}</th>
              <th class="col-sm-3" style="text-align: center">{{'UBZ.SITE_CONTENT.10000001' | translate }}</th>
              <th></th>
              <th></th>
            </thead>
            <tbody>
              <tr *ngFor="let el of pageContent[0]">
                <td style="text-align: center">{{ (titleDomain[el.provisionTitle]) ? (titleDomain[el.provisionTitle].translationCod | translate) : '' }}</td>
                <td style="text-align: center">{{el.provisionDate | date: 'dd-MM-yyyy' }}</td>
                <td style="text-align: center">{{el.provisionProtocol}}</td>
                <td style="text-align: center">{{ (statusDomain[el.provisionStatus]) ? (statusDomain[el.provisionStatus].translationCod | translate) : ''
                  }}
                </td>
                <td>
                  <ng-container *ngIf="el.variationFlag === true">Variante
                    <br/>
                  </ng-container>
                  <span></span>
                </td>
                <td>
                  <ng-container *appAuthKey="'UBZ_BUILDING.MEASURES_DELETE'">
                    <button type="button" class="btn btn-empty waves-effect waves-secondary" (click)="openCustomModal('delete', el)">
                      <i class="fa fa-trash-o" aria-hidden="true"></i>
                    </button>
                  </ng-container>
                  <ng-container *appAuthKey="'UBZ_BUILDING.MEASURES_MODIFY'">
                    <button type="button" class="btn btn-empty waves-effect waves-secondary" (click)="openCustomModal('modify', el)">
                      <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                    </button>
                  </ng-container>
                </td>
              </tr>
            </tbody>
          </table>
          <div class="row">
            <div class="col-sm-12">
              <ng-container *appAuthKey="'UBZ_BUILDING.MEASURES_ADD'">
                <button type="button" class="btn btn-empty" (click)="openCustomModal('add')">
                  <i class="fa fa-plus"></i> {{'UBZ.SITE_CONTENT.111100001' | translate }}
                </button>
              </ng-container>
            </div>
          </div>
          <br/>
          <div class="row">
            <div class="col-sm-6 form-group">
              <label>
                {{'UBZ.SITE_CONTENT.1011001011' | translate }}*
              </label>
              <select class="form-control" [(ngModel)]="pageContent[1].projectCorr" name="projectCorr" required>
                <option value="" hidden>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                <option *ngFor="let elem of (projectCorrDom | domainMapToDomainArray)" value="{{elem.domCode}}">{{elem.translationCod | translate}}</option>
              </select>
            </div>
            <div class="col-sm-6 form-group">
              <label>
                <span>
                  <i class="icon-search note-tooltip" [tooltip]="pageContent[1].diversityAmount" triggers="click"></i>
                </span>
                {{'UBZ.SITE_CONTENT.1011001110' | translate }}
              </label>
              <app-importo [name]="'diversityAmount'" [required]="false" [(ngModel)]="pageContent[1].diversityAmount">
              </app-importo>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6 form-group">
              <label>
                <span>
                  <i class="icon-search note-tooltip" [tooltip]="pageContent[1].changesDescNoCorr" triggers="click"></i>
                </span>
                {{'UBZ.SITE_CONTENT.1010000111' | translate }}
                <span *ngIf="pageContent[1].projectCorr && pageContent[1].projectCorr !== 'CON' && pageContent[1].projectCorr !== ''">*</span>
              </label>
              <input type="text" class="form-control" [(ngModel)]="pageContent[1].changesDescNoCorr" name="changesDescNoCorr" [required]="pageContent[1].projectCorr && pageContent[1].projectCorr !== 'CON' && pageContent[1].projectCorr !== ''">
              <!-- FIXME - TOGLIERE SE COME NOTE SI USA CAMPO INPUT -->
              <!-- <textarea
                class="form-control"
                name="changesDescNoCorr"
                [(ngModel)]="pageContent[1].changesDescNoCorr"
                [required]="pageContent[1].projectCorr && pageContent[1].projectCorr !== 'CON' && pageContent[1].projectCorr !== ''">
              </textarea> -->
            </div>
            <div class="col-sm-6 form-group">
              <label>
                <span>
                  <i class="icon-search note-tooltip" [tooltip]="pageContent[1].diversity" triggers="click"></i>
                </span>
                {{'UBZ.SITE_CONTENT.1011001101' | translate }}
              </label>
              <input type="text" class="form-control" [(ngModel)]="pageContent[1].diversity" name="diversity">
              <!-- FIXME - TOGLIERE SE COME NOTE SI USA CAMPO INPUT -->
              <!-- <textarea
                name="diversity"
                class="form-control"
                [(ngModel)]="pageContent[1].diversity">
              </textarea> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </accordion-group>
</form>

<!-- CUSTOM MODAL centralizza la gestione delle modal in aggiunta, modfifica e cancellazione per il component -->
<app-custom-modal [modalType]="modalType" [isOpen]="customModalIsOpen" [largeModalFlag]="largeModalFlag" [headerTitle]="headerTitle"
  [positionId]="positionId" [idCode]="selectedPageElement ? selectedPageElement.provisionId : ''" [apfString]="apfString" [messagesArray]="messagesArray"
  [buttonTitle]="buttonTitle" [disabledFlag]="disabledFlag" (modalSubmit)="handleSubmitCustomModal($event)" (modalClose)="closeCustomModal()">
</app-custom-modal>
