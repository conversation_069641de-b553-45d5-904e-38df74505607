import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router
} from '@angular/router';
import { Observable } from 'rxjs/Observable';

import { AccessRightsService } from '../../shared/access-rights/services/access-rights.service';
// import { MessageService } from '../../shared/messages/services/message.service';

@Injectable()
export class DashboardAccessRightsGuard implements CanActivate {
  constructor(
    private accessRightsService: AccessRightsService,
    // private messageService: MessageService,
    private router: Router
  ) {}

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    const page = next.data ? next.data['name'] : null;
    const authKey = `UBZ_DASHBOARD_${next.params['dashboardType']}`;
    this.accessRightsService.retrieveAccessRightsFunctions(null, page);

    return this.accessRightsService.authKeysSource.map((resp: string[]) => {
      if (resp.indexOf(authKey) !== -1) {
        return true;
      } else {
        this.router.navigateByUrl('index');
        return false;
      }
    });
  }
}
