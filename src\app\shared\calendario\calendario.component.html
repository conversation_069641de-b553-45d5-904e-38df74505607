<form #f="ngForm" novalidate>
  <div class="ngui-datetime-picker-wrapper">
    <input type="text" class="form-control" data-toggle="tooltip" id="datepickerInput" #valueModel="ngModel" (blur)="onBlurAction()"
      [textMask]="{mask: dateMask, keepCharPositions: true}" [placeholder]="placeholder" [name]="name" [title]="title" [ngClass]="{ngClassAdd: ngClassCondition, 'calendar-invalid': calendarInvalid}"
      [(ngModel)]="formattedValue" [required]="required" [disabled]="disabled" />

    <button class="datepicker-button" (click)="toggleDatePicker()" type="button" [disabled]="disabled">
      <i class="fa fa-fw fa-calendar"></i>
    </button>

    <ngui-datetime-picker name="datepickerComponent" [hidden]="!shouldShowDatePicker" [ngClass]="{'calendar-bottom-container': showDatepickerOnBottom, 'calendar-top-container': !showDatepickerOnBottom}"
      [date-only]="true" [min-date]="minDate" [max-date]="maxDate" (selected$)="maskSelectedValue($event)">
    </ngui-datetime-picker>
  </div>
</form>
