import { Component, OnInit, Input } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { DomainService } from '../../../shared/domain/domain.service';
import { AppraisalMigrationService } from '../../service/appraisal-migration.service';

@Component({
  selector: 'app-appraisal-migration-asset-info',
  templateUrl: './appraisal-migration-asset-info.component.html',
  styleUrls: ['./appraisal-migration-asset-info.component.css']
})
export class AppraisalMigrationAssetInfoComponent implements OnInit {
  @Input()
  set assetId(assetId: string) {
    this._assetId = assetId;
    this.retrivePage();
  }
  @Input() resourceItemType: string;
  @Input() resourceItemCategory: string;
  data: any = {};
  _assetId: string;

  locativeDom: any[] = [];
  measurementDom: any[] = [];
  preservDom: any[] = [];
  projectDom: any[] = [];
  provisionDom: any[] = [];
  purposeDom: any[] = [];
  registryDom: any[] = [];
  categoryDom: any[] = [];
  corrDom: any[] = [];
  sanctionDom: any[] = [];
  subtypeDom: any[] = [];
  zoneDom: any[] = [];
  objectDom: any[] = [];
  subsegmentDom: any[] = [];
  boatDom: any[] = [];
  countryDom: any[] = [];
  useTypeDom: any[] = [];
  agencyDom: any[] = [];
  classDom: any[] = [];
  apeExDomains: any = {};
  energyClassDomains: any = {};
  maintenceStatus: any = {}
  renovationType: any = {}

  immDomainNames = [
    'UBZ_DOM_LOCATIVE_STATUS',
    'UBZ_DOM_MEASUREMENT',
    'UBZ_DOM_PRESERV_STATUS',
    'UBZ_DOM_PROJECT_CORR',
    'UBZ_DOM_PROVISION_TITLE',
    'UBZ_DOM_PURPOSE',
    'UBZ_DOM_RE_REGISTRY_TYPE',
    'UBZ_DOM_REGISTRY_CORR',
    'UBZ_DOM_SANCTION_TYPE',
    'UBZ_DOM_SUBTYPE_STATUS',
    'UBZ_DOM_ZONE_TYPE'
  ];

  nauDomainNames = [
    'UBZ_DOM_MOB_OBJECT_TYPE',
    'UBZ_DOM_SUBSEGMENT',
    'UBZ_DOM_BOAT_MEASUREMENT',
    'UBZ_DOM_COUNTRY',
    'UBZ_DOM_USE_TYPE',
    'UBZ_DOM_CERTIF_AGENCY',
    'UBZ_DOM_CERTIF_CLASS'
  ];

  aerDomainNames = [
    'UBZ_DOM_MOB_OBJECT_TYPE',
    'UBZ_DOM_CERTIF_AGENCY',
    'UBZ_DOM_PROJECT_CORR'
  ];




  constructor(
    private appraisalMigrationService: AppraisalMigrationService,
    private domainService: DomainService
  ) { }

  ngOnInit() {
    this.getDomains();
    
    if (this.resourceItemType === 'IMM') {
      this.domainService.newGetDomain('UBZ_DOM_REG_CATEGORY_TYPE', '-', true).subscribe(resp => {
        this.categoryDom = resp;
      });
      this.domainService.getDomainList(this.immDomainNames).subscribe(x => {
        this.locativeDom = x[this.immDomainNames[0]];
        this.measurementDom = x[this.immDomainNames[1]];
        this.preservDom = x[this.immDomainNames[2]];
        this.projectDom = x[this.immDomainNames[3]];
        this.provisionDom = x[this.immDomainNames[4]];
        this.purposeDom = x[this.immDomainNames[5]];
        this.registryDom = x[this.immDomainNames[6]];
        this.corrDom = x[this.immDomainNames[8]];
        this.sanctionDom = x[this.immDomainNames[9]];
        this.subtypeDom = x[this.immDomainNames[10]];
        this.zoneDom = x[this.immDomainNames[11]];
      });
    } else if (this.resourceItemType === 'MOB') {
      if (this.resourceItemCategory === 'NAU') {
        this.domainService.getDomainList(this.nauDomainNames).subscribe(x => {
          this.objectDom = x[this.nauDomainNames[0]];
          this.subsegmentDom = x[this.nauDomainNames[1]];
          this.boatDom = x[this.nauDomainNames[2]];
          this.countryDom = x[this.nauDomainNames[3]];
          this.useTypeDom = x[this.nauDomainNames[4]];
          this.agencyDom = x[this.nauDomainNames[5]];
          this.classDom = x[this.nauDomainNames[6]];
        });
      } else if (this.resourceItemCategory === 'AER') {
        this.domainService.getDomainList(this.aerDomainNames).subscribe(x => {
          this.objectDom = x[this.aerDomainNames[0]];
          this.agencyDom = x[this.aerDomainNames[1]];
          this.projectDom = x[this.aerDomainNames[2]];
        });
      }
    }
  }
  retrivePage() {
    this.appraisalMigrationService
      .getAssetDetail(this._assetId)
      .subscribe(x => {
        this.data = x;
      });
  }

  getDomains() {
    Observable.forkJoin(
      this.domainService.newGetDomain('UBZ_DOM_APE_EX'),
      this.domainService.newGetDomain('UBZ_DOM_ENERGY_CLASS','-', true),
      this.domainService.newGetDomain('UBZ_DOM_MAINTENANCE_ST'),
      this.domainService.newGetDomain('UBZ_DOM_RENOVATION_TYPE'),
    ).subscribe(responseArray => {
      if (responseArray[0]) {
        this.apeExDomains = responseArray[0];
      }
      if (responseArray[1]) {
        this.energyClassDomains = responseArray[1];
      }
      if (responseArray[2]) {
        this.maintenceStatus = responseArray[2];
      }
      if (responseArray[3]) {
        this.renovationType = responseArray[3];
      }
    });
  }

}
