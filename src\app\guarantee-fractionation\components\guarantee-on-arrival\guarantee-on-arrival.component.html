<!-- FIXME - CENTRALIZZARE MODAL CON CUSTOM MODAL DOPO MARGE CON WAVE2 -->
<div class="row">
  <table class="uc-table">
    <thead>
      <tr>
        <th>{{'UBZ.SITE_CONTENT.100100' | translate }}</th>
        <th>{{'UBZ.SITE_CONTENT.111100' | translate }}</th>
        <th>{{'UBZ.SITE_CONTENT.110001' | translate}}</th>
        <th>{{'UBZ.SITE_CONTENT.10000110000' | translate}}</th>
        <th>{{'UBZ.SITE_CONTENT.110011' | translate}}</th>
        <th>{{'UBZ.SITE_CONTENT.101100111' | translate }}</th>
        <th>{{'UBZ.SITE_CONTENT.10000000010' | translate }}</th>
        <th>{{ 'UBZ.SITE_CONTENT.10010100111' | translate }}</th>
        <th>{{ 'UBZ.SITE_CONTENT.10010100110' | translate }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let row of assetsList; let i = index">
        <td>{{ row.assetId }}</td>
        <td>{{ row.assetDescription }}</td>
        <td>{{ row.reRegistrySheet }}</td>
        <td>{{ row.reRegistryPart }}</td>
        <td>{{ row.reRegistrySub }}</td>
        <td>{{ row.appraisalId }}</td>
        <td>{{ row.lastChange | date:'dd/MM/yyyy' }}</td>
        <td>{{ row.oldJointCod }}</td>
        <td>
          <select class="form-control" [(ngModel)]="row.jointCod" name="jointCod{{i}}" [disabled]="isDisabled">
            <option value="" hidden selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let cod of newJointCodList" value="{{cod}}">{{ cod }}</option>
          </select>
        </td>
      </tr>
    </tbody>
  </table>
</div>

<div *ngIf="previousModalIsOpen" class="modal fade" id="nuovo-asset" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #modal="bs-modal" (onHidden)="previousModalIsOpen = false" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.1100010' | translate }}</h2>
        <button type="button" role="button" class="close pull-right" aria-label="Close" (click)="previousModalIsOpen = false">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <p>{{'UBZ.SITE_CONTENT.10000000100' | translate }}.</p>
        <p>{{'UBZ.SITE_CONTENT.1111110110' | translate }}?</p>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" (click)="confirmUndo()">
          {{'UBZ.SITE_CONTENT.110101000' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>

<app-navigation-footer
  [showPrevious]="false"
  [saveIsEnable]="saveIsEnable()"
  confirmButtonString="{{ 'UBZ.SITE_CONTENT.110001110' | translate }}"
  (saveButtonClick)="save()"
  (previousButtonClick)="previousModalIsOpen = true"
  (cancelButtonClick)="guaranteeFractionationService.cancelFractionation()"
  (frazionamentoModify)="modify()" 
  [isFrazionamento]="true" 
  [isModifyActive]="isDisabled"
  [saveDraftCallback]="draftButtonCallback" 
  (closeDraftButtonClick)="draftSaved()" 
  [showSaveDraft]="!isDisabled">
</app-navigation-footer>
