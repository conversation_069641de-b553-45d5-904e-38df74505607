import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-guarantee-transfer-wizard',
  templateUrl: './guarantee-transfer-wizard.component.html',
  styleUrls: ['./guarantee-transfer-wizard.component.css']
})
export class GuaranteeTransferWizardComponent implements OnInit {
  _stepNum: number; // indicatore dello step  attuale, contatore parte da 1
  @Input('stepNum')
  set stepNum(value: number) {
    this._stepNum = value;
  }
  @Input() ndgOrigin: string;
  @Input() ndgDestination: string;
  @Input() guaranteeFrom: string;
  @Input() assetType: string;
  @Input() guaranteeTo: string;

  @ViewChild('modal') modal;
  modalIsOpen: boolean;
  selectedRoute: string;

  labels = [
    'UBZ.SITE_CONTENT.10010111111',
    'UBZ.SITE_CONTENT.10010111101',
    'UBZ.SITE_CONTENT.10010111110',
    'UBZ.SITE_CONTENT.1100010'
  ];

  constructor(private router: Router) {}

  ngOnInit() {}

  /**
   * @name clickOnCounter
   * @param index - Indice del wizard sul quale di vuole navigare
   */
  clickOnCounter(index: number) {
    // FIXME - DA SISTEMARE
    if (this._stepNum > index && this._stepNum <= 3) {
      switch (index) {
        case 1:
          this.openModal(`guaranteeTransfer/${this.ndgOrigin}/${this.ndgDestination}/select-appraisals`);
          break;
        case 2:
          this.openModal(`guaranteeTransfer/${this.ndgOrigin}/${this.ndgDestination}/start-guarantees`);
          break;
      }
    }
  }

  /**
   * @name openModal
   * @description Apre la modal di retrostatazione wizard
   * @param selectedRoute rotta verso la quale effettuare la navigazione
   */
  private openModal(selectedRoute: string) {
    this.modalIsOpen = true;
    this.selectedRoute = selectedRoute;
  }

  /**
   * @name closeModal
   * @description Chiude la modal di retrostatazione wizard
   */
  closeModal() {
    this.modalIsOpen = false;
    this.selectedRoute = null;
  }

  /**
   * @name confirmUndo
   * @description Chiude la modal di retrostatazione wizard ed effettua la navigazione
   * @param selectedRoute rotta verso la quale effettuare la navigazione
   */
  confirmUndo() {
    this.modalIsOpen = false;
    this.router.navigateByUrl(this.selectedRoute);
    this.selectedRoute = null;
  }
}
