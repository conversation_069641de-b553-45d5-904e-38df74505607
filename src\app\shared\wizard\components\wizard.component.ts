import { Component, OnInit, Input } from '@angular/core';
import { WizardService } from '../services/wizard.service';
import { WizardElement } from '../model/wizard-element';
import { Router, ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-wizard',
  templateUrl: './wizard.component.html',
  styleUrls: ['./wizard.component.css']
})
export class WizardComponent implements OnInit {
  @Input() positionId: string;
  @Input() landingMap: Object;
  @Input() wizardCode: string;

  wizard: WizardElement[] = [];

  constructor(
    private wizardService: WizardService,
    private router: Router,
    private activatedRoute: ActivatedRoute
  ) {}

  ngOnInit() {
    this.getWizard();
  }

  getWizard() {
    this.wizardService.getWizard(this.positionId).subscribe((result: WizardElement[]) => {
      const tempWizard: WizardElement[] = [];
      for (const element of result) {
        if (element.taskList && element.taskList.taskCod === `UBZ-${this.wizardCode}`) {
          for (const child of element.childs) {
            // se la property outcomeCod == 5 lo step non deve essere inserito nel wizard
            if (child.taskList && child.taskList.outcomeCod !== '5') {
              tempWizard.push(child);
            }
          }
          break;
        }
      }
      this.wizard = tempWizard;
      this.wizardService.currentWizard = this.wizard;
    });
    // FIXME - TOGLIERE SE IL REWRITE DEL METODO FUNZIONA CORRETTAMETNE
      // this.wizardService
      // .getWizard(this.positionId)
      // .subscribe((data: WizardElement[]) => {
      //   const tempWizard: WizardElement[] = [];
      //   for (const j in data) {
      //     if (
      //       data.hasOwnProperty(j) &&
      //       data[j].taskList.taskCod === `UBZ-${this.wizardCode}`
      //     ) {
      //       for (const i in data[j].childs) {
      //         if (data[j].childs.hasOwnProperty(i)) {
      //           const child: WizardElement = data[j].childs[i];
      //           if (child.taskList.outcomeCod !== '5') {
      //             tempWizard.push(child);
      //           }
      //         }
      //       }
      //       break;
      //     }
      //   }
      //   this.wizard = tempWizard;
      // });
  }

  goToTask(taskId: string) {
    this.router.navigate([`./${this.landingMap[taskId]}`], {
      relativeTo: this.activatedRoute
    });
  }

  invalidateTask(taskId: string) {
    this.wizardService
      .invalidateTask(this.positionId, this.wizardCode, taskId)
      .subscribe(() => {
        this.getWizard();
        this.goToTask(taskId);
      });
  }
}
