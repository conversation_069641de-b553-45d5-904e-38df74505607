<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="unicreditlight_italic" horiz-adv-x="1060" >
<font-face units-per-em="2048" ascent="1536" descent="-512" />
<missing-glyph horiz-adv-x="458" />
<glyph unicode="&#xfb01;" horiz-adv-x="956" d="M-47 -344q25 59 43 115.5t34.5 119t32 134t31.5 161.5l131 736h-123q-16 0 -12 16l6 33q6 16 21 18l127 19l12 57q20 96 54 161.5t83 105.5t112.5 56.5t145.5 16.5q66 0 120 -9.5t89 -19.5q18 -6 14 -22l-12 -43q-4 -14 -24 -11q-39 10 -86 17.5t-101 7.5 q-119 0 -194.5 -52t-106.5 -200l-14 -69h522q14 0 12 -15l-188 -975q-2 -14 -18 -14h-60q-14 0 -12 14l174 908h-449l-129 -701q-20 -109 -36.5 -189.5t-33 -145t-34.5 -120t-43 -112.5q-6 -16 -25 -16h-51q-20 0 -12 18z" />
<glyph unicode="&#xfb02;" horiz-adv-x="999" d="M-47 -344q25 59 43 115.5t34.5 119t32 134t31.5 161.5l131 736h-123q-16 0 -12 16l6 33q6 16 21 18l127 19l12 57q20 96 48 161.5t69 105.5t100 56.5t145 16.5q55 0 115 -9.5t90 -19.5l62 10q14 0 12 -14l-221 -1147q-6 -29 -8 -52t-2 -34q0 -31 15 -46t58 -15h95 q14 0 12 -15l-8 -49q-2 -14 -25 -14h-86q-84 0 -118 27.5t-34 93.5q0 43 13 108l207 1069q-29 10 -82.5 18.5t-94.5 8.5q-59 0 -102 -9t-74.5 -37t-53 -77t-38.5 -129l-14 -69h227q14 0 12 -15l-10 -55q-4 -12 -16 -12h-232l-129 -701q-20 -109 -36.5 -189.5t-33 -145 t-34.5 -120t-43 -112.5q-6 -16 -25 -16h-51q-20 0 -12 18z" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="458" />
<glyph unicode=" "  horiz-adv-x="458" />
<glyph unicode="&#x09;" horiz-adv-x="458" />
<glyph unicode="&#xa0;" horiz-adv-x="458" />
<glyph unicode="!" horiz-adv-x="448" d="M27 27q0 6 2 21t8 50q8 41 27.5 57.5t52.5 16.5h18q55 0 55 -45q0 -18 -12 -74q-10 -41 -27.5 -56t-54.5 -15h-16q-53 0 -53 45zM111 289l188 1011q2 16 18 17h60q20 0 18 -17l-209 -1013q-2 -14 -18 -15h-41q-20 0 -16 17z" />
<glyph unicode="&#x22;" horiz-adv-x="565" d="M141 950l70 430q4 18 11 23.5t24 5.5h47q16 0 23.5 -5t2.5 -24l-98 -430q-6 -25 -28 -24h-35q-23 0 -17 24zM444 950l70 430q4 18 11 23.5t24 5.5h47q16 0 23.5 -5t2.5 -24l-98 -430q-6 -25 -28 -24h-35q-23 0 -17 24z" />
<glyph unicode="#" horiz-adv-x="1398" d="M35 426l16 41q8 18 31 18h272l148 361h-266q-27 0 -19 22l16 41q8 18 31 19h273l153 377q4 12 10.5 15t16.5 3h53q20 0 12 -20l-153 -375h340l153 377q4 12 10.5 15t16.5 3h53q20 0 12 -20l-153 -375h270q27 0 19 -23l-17 -41q-8 -18 -30 -18h-277l-147 -361h270 q27 0 18 -22l-16 -41q-8 -18 -31 -19h-274l-158 -383q-8 -20 -24 -20h-56q-18 0 -12 20l158 383h-340l-158 -383q-8 -20 -25 -20h-55q-18 0 -12 20l158 383h-269q-27 0 -18 23zM446 485h340l148 361h-340z" />
<glyph unicode="$" d="M37 102l14 39q4 8 9.5 13.5t19.5 -0.5q66 -25 143.5 -42.5t155.5 -19.5l108 553l-86 25q-96 29 -146 92t-50 147q0 33 4 73t14 83q33 131 132.5 192.5t262.5 63.5l37 188q4 20 29 21h27q29 0 24 -21l-37 -188q29 -2 63 -6t66.5 -10.5t62.5 -13.5t50 -13q16 -6 19.5 -12 t-1.5 -19l-12 -37q-4 -14 -10 -16t-23 2q-47 14 -109.5 24.5t-121.5 12.5l-100 -516q35 -10 63.5 -17.5t44.5 -13.5q92 -31 131 -91t39 -140q0 -18 -3 -55.5t-13 -77.5q-18 -82 -49 -139.5t-79 -95.5t-116 -57.5t-156 -23.5l-41 -203q-6 -20 -28 -20h-27q-31 0 -24 20 l41 203q-78 4 -167 21.5t-149 46.5q-20 8 -12 28zM301 924q0 -66 44 -117t161 -74l96 502q-111 2 -186.5 -41t-98.5 -141q-16 -70 -16 -129zM459 88q70 6 119 25.5t81.5 48t51 66.5t28.5 79q10 37 15.5 72t5.5 53q0 86 -46 131t-151 66z" />
<glyph unicode="%" horiz-adv-x="1550" d="M115 799q0 41 8 98t18 111q23 121 51.5 182t63.5 94q67 59 180 59q92 0 143.5 -41.5t51.5 -140.5q0 -41 -7.5 -99t-17.5 -110q-27 -125 -55.5 -184t-63.5 -92q-68 -59 -180 -60q-88 0 -140 42t-52 141zM195 809q0 -70 31.5 -96.5t90.5 -26.5q43 0 75 13.5t57.5 45t44 85 t34.5 133.5q10 47 16.5 98t6.5 90q0 66 -29.5 94.5t-97.5 28.5q-41 0 -73 -14.5t-56.5 -46t-43 -85t-32.5 -131.5q-8 -47 -16 -98t-8 -90zM225 -2l989 1331q10 14 31 14h55q20 0 9 -16l-990 -1331q-8 -10 -13 -12t-17 -2h-56q-20 0 -8 16zM903 164q0 41 8.5 98t18.5 111 q23 121 51.5 182t62.5 94q68 60 181 60q92 0 143 -42t51 -141q0 -41 -7 -99t-17 -110q-27 -125 -55.5 -184t-63.5 -92q-68 -59 -180 -59q-88 0 -140.5 42t-52.5 140zM983 174q0 -70 32 -96.5t91 -26.5q43 0 74.5 13.5t57.5 45t44 85t35 133.5q10 47 16 98t6 90 q0 66 -29.5 94.5t-97.5 28.5q-41 0 -72.5 -14.5t-56 -46t-43 -85t-32.5 -131.5q-8 -47 -16.5 -98t-8.5 -90z" />
<glyph unicode="&#x26;" horiz-adv-x="1232" d="M76 279q0 96 26.5 170.5t70.5 132t103.5 98.5t126.5 70q-45 66 -66.5 124t-21.5 123q0 160 95.5 253t257.5 93q129 0 199.5 -65.5t70.5 -187.5q0 -156 -80 -260.5t-252 -190.5l264 -324q31 72 58.5 159t44.5 171q4 12 9 15.5t15 3.5h41q25 -1 19 -21 q-12 -53 -26.5 -108.5t-32 -106.5t-35 -97t-35.5 -83l155 -187q12 -14 -2 -26l-45 -35q-14 -10 -26 4l-136 164q-76 -96 -177 -141t-248 -45h-33q-172 0 -256 71.5t-84 225.5zM164 285q0 -121 63.5 -174.5t192.5 -53.5h35q115 0 210 40t156 136l-377 459 q-133 -59 -206.5 -157.5t-73.5 -249.5zM403 1008q0 -72 35 -137.5t107 -155.5l16 -21q76 39 130 79t89 86t50.5 100.5t15.5 119.5q0 92 -48 139.5t-134 47.5q-121 0 -191 -70t-70 -188z" />
<glyph unicode="'" horiz-adv-x="262" d="M141 950l70 430q4 18 11 23.5t24 5.5h47q16 0 23.5 -5t2.5 -24l-98 -430q-6 -25 -28 -24h-35q-23 0 -17 24z" />
<glyph unicode="(" horiz-adv-x="567" d="M94 344q0 147 34 291.5t96.5 282t150.5 265.5t196 242q10 10 15.5 10.5t13.5 -5.5l27 -23q12 -12 2 -27q-94 -102 -174 -221t-138.5 -250t-91 -272t-32.5 -289q0 -74 6 -141.5t19 -135t35.5 -137t55.5 -145.5q4 -6 3 -13t-13 -14l-27 -12q-20 -8 -28 6 q-70 145 -110 286.5t-40 301.5z" />
<glyph unicode=")" horiz-adv-x="567" d="M-102 -199q94 102 174 221t138 250t91 272.5t33 289.5q0 145 -26.5 275t-90.5 284q-4 6 -3 13t13 13l27 13q20 8 29 -7q70 -145 109.5 -286t39.5 -301q0 -147 -33.5 -292t-96 -282t-151 -265t-196.5 -243q-10 -10 -15.5 -10t-13.5 6l-26 23q-12 12 -2 26z" />
<glyph unicode="*" horiz-adv-x="692" d="M168 1245l37 68q14 23 35 8l178 -107l14 238q0 18 7 24.5t24 6.5h65q16 0 20.5 -6.5t-1.5 -24.5l-76 -240l217 105q31 14 33 -12l8 -60q2 -25 -27 -31l-229 -59l137 -182q18 -20 -6 -37l-63 -37q-27 -14 -37 10l-78 211l-164 -211q-18 -27 -41 -10l-49 35q-20 14 6 37 l213 192l-209 51q-29 6 -14 31z" />
<glyph unicode="+" horiz-adv-x="1048" d="M78 506l12 59q2 14 16 15h357l69 356q2 14 17 14h57q16 0 15 -14l-70 -356h356q14 0 13 -15l-13 -59q-2 -14 -16 -14h-356l-70 -359q-2 -14 -16 -14h-58q-16 0 -14 14l69 359h-356q-14 0 -12 14z" />
<glyph unicode="," horiz-adv-x="389" d="M-51 -219q35 41 60.5 81t41.5 75q12 27 -8 36l-18 9q-31 12 -31 38q0 12 2 30t12 61q14 68 78 67h27q68 0 67 -53q0 -10 -2 -24.5t-10 -49.5q-18 -78 -58 -154.5t-108 -154.5q-12 -14 -25 -6l-28 20q-14 10 0 25z" />
<glyph unicode="-" horiz-adv-x="563" d="M35 512l12 53q2 14 16 15h392q14 0 12 -15l-12 -53q-2 -14 -17 -14h-391q-14 0 -12 14z" />
<glyph unicode="." horiz-adv-x="389" d="M-12 23q0 10 2 24t10 57q12 68 82 68h22q61 0 62 -41q0 -6 -2 -21.5t-12 -60.5q-8 -35 -25.5 -51t-58.5 -16h-21q-59 0 -59 41z" />
<glyph unicode="/" horiz-adv-x="651" d="M-66 -33l705 1428q8 18 25 18h57q23 0 12 -20l-704 -1428q-8 -18 -27 -18h-55q-20 0 -13 20z" />
<glyph unicode="0" d="M86 338q0 74 12.5 173t30.5 204q33 180 82 301t114.5 193.5t147.5 103t180 30.5q152 0 233 -83t81 -271q0 -74 -12.5 -173t-30.5 -204q-33 -180 -82 -301t-114.5 -194.5t-147.5 -104t-181 -30.5q-152 0 -232.5 84t-80.5 272zM184 348q0 -137 48.5 -212t179.5 -75 q78 0 139 27t119 94q33 39 61.5 102.5t50 137.5t38 153.5t27.5 155.5t16 140.5t5 107.5q0 137 -48 211t-179 74q-78 0 -139.5 -26t-118.5 -93q-35 -41 -62.5 -103.5t-49 -136.5t-38 -153.5t-27.5 -155.5t-16.5 -140.5t-5.5 -107.5z" />
<glyph unicode="1" d="M55 20l6 43q2 20 25 21h311l221 1147h-10l-278 -127q-20 -10 -27 6l-16 43q-8 18 8 27l289 131q16 8 29.5 11t31.5 3h62q25 0 18 -22l-233 -1219h311q20 0 18 -21l-6 -43q-2 -20 -24 -20h-717q-20 0 -19 20z" />
<glyph unicode="2" d="M49 37l21 127q18 111 44.5 187.5t68.5 130t100.5 90t140.5 67.5l143 55q74 29 131.5 59.5t97.5 72.5t60.5 100.5t20.5 144.5q0 98 -54.5 143.5t-171.5 45.5q-72 0 -152.5 -14.5t-146.5 -37.5q-23 -8 -28 15l-9 39q-6 18 17 26q162 55 327 55q315 0 316 -260 q0 -98 -20.5 -168.5t-65.5 -125t-118 -96.5t-177 -83l-137 -53q-68 -25 -117 -56.5t-84 -75.5t-56.5 -104.5t-35.5 -146.5l-14 -90h587q20 0 19 -21l-6 -43q-2 -20 -25 -20h-645q-18 0 -26.5 7t-4.5 30z" />
<glyph unicode="3" d="M59 63l15 41q6 18 24 13q66 -23 135.5 -37t137.5 -14q80 0 145.5 19t113.5 63.5t74.5 114t26.5 167.5q0 123 -79 178.5t-244 55.5h-56q-20 0 -16 16l10 53q2 14 29 15h57q190 0 286.5 86t96.5 239q0 100 -54 143.5t-179 43.5q-57 0 -130 -13.5t-126 -29.5 q-18 -6 -23.5 -1t-7.5 15l-6 39q-2 10 0 16t14 10q59 20 140 33.5t153 13.5q166 0 240.5 -64.5t74.5 -188.5q0 -94 -25.5 -162t-66.5 -114t-93 -72.5t-105 -36.5v-7q104 -14 156 -85.5t52 -178.5q0 -229 -121.5 -340.5t-334.5 -111.5q-88 0 -167 17t-132 38q-20 8 -15 26z " />
<glyph unicode="4" d="M43 375l2 24q2 18 11.5 35t37.5 51l631 797q18 23 33.5 33t46.5 10h57q31 0 42 -12.5t5 -46.5l-157 -830h149q20 0 19 -16l-9 -51q-2 -16 -22 -17h-154l-61 -334q-2 -18 -23 -18h-57q-23 0 -19 18l66 334h-573q-16 0 -21.5 4t-3.5 19zM160 436h497l156 807h-12z" />
<glyph unicode="5" d="M37 82l12 33q10 23 35 12q72 -29 139.5 -45t138.5 -16q84 0 159 27.5t131.5 85t89 143.5t32.5 202q0 121 -56 169t-155 48q-80 0 -147.5 -18t-130.5 -47q-16 -8 -26.5 -11t-20.5 1l-27 12q-14 6 -8 27l153 587q6 33 41 33h523q31 0 24 -25l-8 -43q-6 -20 -33 -20h-446 q-25 0 -31 -25l-121 -450q57 29 123 45t150 16q295 0 294 -288q0 -113 -29.5 -213.5t-93 -176t-162 -119.5t-235.5 -44q-72 0 -154.5 19t-150.5 52q-18 10 -10 29z" />
<glyph unicode="6" d="M111 309q0 37 5 92.5t14 118t20.5 128t23.5 124.5q27 125 71 230.5t111.5 181.5t160.5 117.5t216 41.5q70 0 128 -10t97 -26q23 -8 15 -31l-10 -29q-4 -12 -11.5 -15t-23.5 1q-37 10 -87 19.5t-108 9.5q-121 0 -201.5 -46.5t-135 -123t-86 -177t-52.5 -208.5 q66 53 158 84.5t188 31.5q147 0 219 -70.5t72 -205.5q0 -129 -30.5 -233.5t-90 -178.5t-145.5 -113.5t-197 -39.5q-166 0 -243.5 87t-77.5 240zM207 313q0 -123 53 -185t180 -62q96 0 164 38.5t111 103t63.5 147.5t20.5 169q0 111 -48 163t-163 52q-92 0 -187.5 -38.5 t-160.5 -98.5q-12 -63 -22.5 -142t-10.5 -147z" />
<glyph unicode="7" d="M96 27l824 1214h-652q-25 0 -18 21l10 45q2 18 27 18h717q39 0 32 -29l-4 -28q-4 -18 -13 -37t-42 -66l-770 -1142q-8 -12 -17.5 -17.5t-27.5 -5.5h-56q-27 0 -10 27z" />
<glyph unicode="8" d="M53 301q0 137 77 249t222 171q-63 49 -91 106.5t-28 139.5q0 84 30 153.5t84 118.5t129 76.5t167 27.5q82 0 141.5 -21.5t98.5 -58t57 -87t18 -107.5q0 -131 -72.5 -236.5t-199.5 -166.5q102 -47 140 -114t38 -142q0 -199 -116.5 -313.5t-327.5 -114.5 q-174 0 -270.5 82.5t-96.5 236.5zM154 309q0 -125 74.5 -184t203.5 -59q158 0 245 91t87 234q0 39 -8 70t-32 58.5t-63.5 53t-101.5 54.5l-123 55q-135 -57 -208.5 -148.5t-73.5 -224.5zM334 979q0 -78 32.5 -130t129.5 -95l110 -49q111 53 183.5 145t72.5 207 q0 104 -58.5 153.5t-168.5 49.5q-133 0 -217 -75t-84 -206z" />
<glyph unicode="9" d="M98 49l11 29q4 12 11 15t23 -1q37 -10 87.5 -19.5t107.5 -9.5q121 0 202 46.5t135 123t85 177t53 208.5q-66 -53 -158 -84.5t-188 -31.5q-147 0 -219 70.5t-72 205.5q0 129 31 233.5t90 178.5t145 113.5t197 39.5q166 0 244 -87t78 -240q0 -37 -5.5 -92.5t-14.5 -118 t-20.5 -128t-23.5 -124.5q-27 -125 -71 -230.5t-111.5 -181t-160.5 -117.5t-216 -42q-70 0 -128 10t-97 26q-23 8 -15 31zM272 801q0 -111 48 -163t163 -52q92 0 187.5 39t160.5 98q12 63 22.5 142t10.5 147q0 123 -53 185.5t-180 62.5q-96 0 -164 -40t-111 -104.5 t-63.5 -146.5t-20.5 -168z" />
<glyph unicode=":" horiz-adv-x="378" d="M-8 23q0 10 2 24t10 57q12 68 82 68h22q61 0 62 -41q0 -6 -2 -21.5t-12 -60.5q-8 -35 -25.5 -51t-58.5 -16h-21q-59 0 -59 41zM138 803q0 10 2 24t10 57q12 68 82 68h22q61 0 62 -41q0 -6 -2 -21.5t-12 -60.5q-8 -35 -25.5 -51t-58.5 -16h-21q-59 0 -59 41z" />
<glyph unicode=";" horiz-adv-x="378" d="M-71 -219q35 41 60.5 81t41.5 75q12 27 -8 36l-18 9q-31 12 -31 38q0 12 2 30t12 61q14 68 78 67h27q68 0 67 -53q0 -10 -2 -24.5t-10 -49.5q-18 -78 -58 -154.5t-108 -154.5q-12 -14 -25 -6l-28 20q-14 10 0 25zM133 803q0 10 2 24t10 57q12 68 82 68h22q61 0 62 -41 q0 -6 -2 -21.5t-12 -60.5q-8 -35 -25.5 -51t-58.5 -16h-21q-59 0 -59 41z" />
<glyph unicode="&#x3c;" horiz-adv-x="1048" d="M123 514q0 6 8 39q4 10 7 14t14 8l675 340q23 10 31 -6l21 -41q8 -16 -13 -26l-612 -316l475 -299q18 -12 6 -28l-28 -39q-10 -12 -27 -2l-541 336q-16 10 -16 20z" />
<glyph unicode="=" horiz-adv-x="1048" d="M45 324l12 59q2 14 17 14h800q14 0 13 -14l-13 -59q-2 -14 -16 -15h-801q-14 0 -12 15zM115 688l12 60q2 14 16 14h801q14 0 12 -14l-12 -60q-2 -14 -16 -14h-801q-14 0 -12 14z" />
<glyph unicode="&#x3e;" horiz-adv-x="1048" d="M147 205q-8 18 13 26l612 316l-475 299q-18 12 -6 28l28 39q10 12 27 2l541 -335q16 -10 16 -21q0 -6 -8 -39q-4 -10 -7 -14t-14 -8l-675 -340q-23 -10 -31 6z" />
<glyph unicode="?" horiz-adv-x="903" d="M123 27q0 6 2 21t8 50q8 41 27.5 57.5t52.5 16.5h18q55 0 56 -45q0 -18 -13 -74q-10 -41 -27.5 -56t-53.5 -15h-17q-53 0 -53 45zM193 293q14 90 34.5 156.5t51 117.5t75.5 90t107 74l82 45q117 61 171 127t54 172q0 94 -58.5 139.5t-189.5 45.5q-66 0 -140.5 -14.5 t-150.5 -37.5q-20 -6 -26 13l-10 39q-6 20 16 28q76 27 155.5 41t157.5 14q342 0 342 -256q0 -72 -17.5 -127t-52 -99t-85 -81t-117.5 -71l-80 -43q-57 -31 -97 -66t-68 -78t-45 -99t-28 -130q-2 -12 -6 -16.5t-20 -4.5h-35q-23 0 -20 21z" />
<glyph unicode="@" horiz-adv-x="1611" d="M106 567q0 176 56.5 325.5t159 259.5t249 171t324.5 61q135 0 247.5 -37.5t193.5 -110.5t127 -178.5t46 -242.5q0 -78 -19.5 -170t-63.5 -171t-114.5 -132t-172.5 -53q-92 0 -144.5 42t-60.5 97q-31 -49 -89.5 -88t-133.5 -39q-104 0 -154.5 65.5t-50.5 168.5 q0 70 19.5 153.5t47.5 149.5q35 82 97.5 133t160.5 51q66 0 110 -27.5t65 -74.5l16 53q4 14 10 17t21 3h26q14 0 20.5 -4t2.5 -20l-70 -348q-10 -43 -15 -78t-5 -56q0 -55 31.5 -87.5t103.5 -32.5q80 0 132 44t84 111.5t45 146.5t13 150q0 106 -35.5 195.5t-105.5 153 t-169 98t-226 34.5q-152 0 -278 -52t-217 -147t-141 -229.5t-50 -296.5q0 -139 43 -241.5t120.5 -171t189.5 -102t251 -33.5q111 0 214 21.5t181 53.5q12 4 18.5 3t10.5 -13l8 -29q6 -23 -16 -32q-84 -39 -202 -62.5t-235 -23.5q-164 0 -289.5 43t-211.5 124t-130.5 195.5 t-44.5 259.5zM594 532q0 -152 135 -151q84 0 139.5 64.5t73.5 158.5l21 101q12 61 12 102q0 59 -35 98t-109 39q-66 0 -105.5 -32.5t-68.5 -92.5q-33 -70 -48 -146.5t-15 -140.5z" />
<glyph unicode="A" horiz-adv-x="1130" d="M-18 23q164 362 318.5 676.5t307.5 592.5q10 20 23.5 26.5t34.5 6.5h55q16 0 29.5 -7t17.5 -30q20 -115 41.5 -262t40 -313t35 -341t28.5 -347q2 -25 -22 -25h-45q-27 0 -27 25q-6 102 -13 206.5t-18 208.5h-520l-190 -417q-4 -10 -11.5 -16.5t-23.5 -6.5h-47 q-27 0 -14 23zM307 522h473q-20 209 -42.5 395.5t-45.5 325.5h-22q-98 -186 -186.5 -361t-176.5 -360z" />
<glyph unicode="B" horiz-adv-x="999" d="M33 57l237 1229q4 18 12.5 25.5t30.5 11.5q47 10 113.5 15t134.5 5q188 0 277.5 -59t89.5 -188q0 -68 -16.5 -130.5t-50.5 -114.5t-86 -91t-122 -58v-4q104 -10 168 -64t64 -173q0 -47 -7.5 -98.5t-25.5 -100.5t-49 -93t-76 -77q-72 -51 -165 -71.5t-222 -20.5 q-29 0 -64.5 1t-74.5 4t-76 6t-68 9q-18 4 -23 12.5t-1 24.5zM139 125q-4 -16 1 -21.5t20 -9.5q41 -8 90 -10t106 -2q92 0 169 15.5t139 62.5q61 47 90.5 129t29.5 162v6q0 109 -78 149q-80 42 -262 43h-202zM258 733h133q92 0 166 13.5t135 52.5q70 43 104.5 118.5 t34.5 172.5q0 98 -69.5 135t-200.5 37q-49 0 -97 -3.5t-81 -9.5q-12 -4 -19.5 -8t-11.5 -20z" />
<glyph unicode="C" horiz-adv-x="1007" d="M86 315q0 72 19.5 195t50.5 289q31 162 78 267t113.5 166.5t149.5 86t185 24.5q90 0 174 -25.5t143 -62.5q20 -12 11 -32l-19 -31q-8 -14 -16 -14t-19 6q-131 76 -276 76q-86 0 -153.5 -20.5t-121 -78t-92.5 -157t-67 -256.5q-27 -145 -42.5 -252t-15.5 -166 q0 -72 15.5 -122t50.5 -82t93.5 -46t141.5 -14q63 0 144.5 14t152.5 43q12 4 19.5 2t11.5 -19l8 -34q2 -10 0 -15.5t-14 -9.5q-74 -33 -165 -49t-169 -16q-209 0 -300 78.5t-91 254.5z" />
<glyph unicode="D" horiz-adv-x="1146" d="M27 37l243 1235q4 20 12.5 29.5t30.5 13.5q53 10 124 15t134 5q115 0 206 -19.5t153.5 -65.5t96.5 -126t34 -198q0 -53 -12.5 -151.5t-40.5 -213.5q-43 -182 -115 -291.5t-170 -170t-220 -80t-267 -19.5h-170q-25 0 -34 7t-5 30zM133 104q-6 -23 14 -22h123 q145 0 251 24.5t181.5 82t126 152.5t84.5 237q23 98 37.5 190t14.5 152q0 96 -23.5 160.5t-72 102.5t-124.5 54t-180 16q-53 0 -99 -3t-75 -7q-16 -4 -26.5 -11t-14.5 -30z" />
<glyph unicode="E" horiz-adv-x="954" d="M68 174q0 23 4 57.5t12 77.5q37 205 76 399.5t86 395.5q14 59 31.5 101t47 68.5t75.5 39t114 12.5h432q23 0 19 -20l-9 -43q-4 -12 -8 -15.5t-16 -3.5h-430q-74 0 -108 -31.5t-54 -119.5q-16 -74 -34.5 -157t-45.5 -208h520q23 0 19 -20l-11 -45q-4 -10 -8 -14.5 t-16 -4.5h-522q-25 -123 -39.5 -211t-24.5 -141q-12 -78 -12 -119q0 -47 27.5 -67.5t89.5 -20.5h454q20 0 19 -21l-8 -43q-4 -12 -8.5 -16t-16.5 -4h-459q-109 0 -152.5 45t-43.5 129z" />
<glyph unicode="F" horiz-adv-x="876" d="M27 18l213 1094q12 63 31.5 103t56.5 69q33 23 75.5 32t90.5 9h419q25 0 19 -18l-10 -45q-6 -20 -27 -21h-393q-45 0 -70.5 -5t-46.5 -22q-18 -16 -32.5 -44.5t-24.5 -88.5l-68 -350h526q23 0 19 -20l-10 -45q-4 -10 -8.5 -14.5t-16.5 -4.5h-526l-123 -629 q-2 -18 -23 -18h-53q-20 0 -18 18z" />
<glyph unicode="G" horiz-adv-x="1118" d="M86 326q0 76 19.5 195.5t46.5 250.5q29 137 68.5 254t113.5 191q61 61 153.5 93.5t227.5 32.5q98 0 180 -24.5t141 -58.5q20 -14 11 -33l-19 -31q-8 -14 -16 -14t-19 6q-66 35 -131 53.5t-149 18.5q-129 0 -209 -36t-129 -102.5t-78 -160t-53 -209.5 q-29 -135 -43.5 -232.5t-14.5 -175.5q0 -72 14.5 -125t50.5 -88t98.5 -51.5t158.5 -16.5q66 0 130 8.5t118 20.5q20 4 25 10.5t10 22.5l94 477h-238q-25 0 -18 21l10 45q4 10 8 14t17 4h307q23 0 20 -18l-114 -586q-4 -23 -11.5 -34t-29.5 -19q-63 -23 -150.5 -35 t-193.5 -12q-96 0 -171 16t-127.5 56t-80 106.5t-27.5 165.5z" />
<glyph unicode="H" horiz-adv-x="1189" d="M27 18l249 1289q2 18 23 18h53q20 0 19 -18l-113 -580h698l113 580q2 18 23 18h53q20 0 18 -18l-250 -1289q-2 -18 -22 -18h-53q-23 0 -19 18l121 627h-698l-121 -627q-2 -18 -23 -18h-53q-23 0 -18 18z" />
<glyph unicode="I" horiz-adv-x="413" d="M35 18l250 1289q2 18 22 18h53q20 0 19 -18l-250 -1289q-2 -18 -23 -18h-53q-23 0 -18 18z" />
<glyph unicode="J" horiz-adv-x="579" d="M-92 18l8 48q2 16 23 16h84q88 0 136 34t72 83q25 49 36.5 104t21.5 103l174 901q2 18 22 18h54q20 0 18 -18l-176 -914q-12 -61 -27.5 -116.5t-38.5 -108.5q-41 -90 -115.5 -129t-187.5 -39h-88q-18 0 -16 18z" />
<glyph unicode="K" horiz-adv-x="987" d="M27 18l249 1289q2 18 23 18h53q20 0 19 -18l-113 -580h160q37 0 58.5 9t39.5 32l445 537q10 12 17 16t21 4h60q16 0 18 -8t-8 -19l-502 -598q-10 -14 -10 -20t6 -18l311 -639q10 -23 -18 -23h-51q-27 0 -35 18l-276 584q-12 29 -26.5 36t-47.5 7h-178l-121 -627 q-2 -18 -23 -18h-53q-23 0 -18 18z" />
<glyph unicode="L" horiz-adv-x="733" d="M53 123q0 23 4 52.5t13 72.5l204 1059q2 18 23 18h53q20 0 19 -18l-211 -1088q-10 -57 -11 -86q0 -49 66 -49h362q18 0 17 -16l-8 -50q-2 -18 -23 -18h-364q-78 0 -111 33t-33 90z" />
<glyph unicode="M" horiz-adv-x="1501" d="M0 25q39 172 75 327.5t72.5 306t74.5 301t81 308.5q10 33 26.5 45t51.5 12h39q33 0 51 -9t25 -44q18 -100 38.5 -217t42 -242t45 -254t46.5 -252h22q76 129 143.5 250t131 239.5t127 236.5t129.5 243q14 29 32.5 39t49.5 10h53q29 0 46 -11t13 -46q-35 -299 -73 -608 t-85 -635q-2 -25 -26 -25h-51q-23 0 -19 25q25 154 47.5 313.5t44 317t40 307t34.5 278.5h-22q-63 -117 -126 -232.5t-127.5 -235.5t-135 -245t-148.5 -260q-16 -27 -34.5 -36t-43.5 -9h-47q-25 0 -41 10.5t-20 38.5q-23 117 -45.5 243t-46 251t-45 246t-39.5 229h-19 q-43 -158 -81 -307.5t-73.5 -297t-70.5 -299t-72 -312.5q-6 -25 -27 -25h-45q-25 0 -18 25z" />
<glyph unicode="N" horiz-adv-x="1183" d="M27 23l239 1241q6 35 21.5 48t52.5 13h37q35 0 52 -14.5t28 -48.5l360 -1178h14l238 1221q4 20 25 20h49q20 0 14 -22l-239 -1242q-6 -35 -21.5 -48t-52.5 -13h-37q-35 0 -52.5 14.5t-27.5 48.5l-360 1178h-15l-237 -1221q-6 -20 -25 -20h-49q-20 0 -14 23z" />
<glyph unicode="O" horiz-adv-x="1161" d="M82 334q0 98 23.5 229t58.5 291q27 127 69.5 219t107.5 152.5t153 89t206 28.5q176 0 275.5 -79.5t99.5 -276.5q0 -100 -22.5 -231t-61.5 -285q-33 -133 -78 -225t-106.5 -151.5t-144 -86t-191.5 -26.5q-184 0 -286.5 77.5t-102.5 274.5zM180 348q0 -143 64.5 -212.5 t228.5 -69.5q86 0 151.5 20t114.5 69.5t87 131.5t71 205q18 68 32.5 136t25 132.5t16.5 118t6 90.5q0 156 -65.5 223.5t-219.5 67.5q-90 0 -158.5 -23t-121 -74t-90 -133t-64.5 -199q-35 -145 -56.5 -272t-21.5 -211z" />
<glyph unicode="P" horiz-adv-x="993" d="M27 18l243 1258q6 31 35 39q51 14 124 21t151 7q391 0 391 -299q0 -104 -35 -212.5t-109 -182.5q-68 -68 -169 -96.5t-232 -28.5h-207l-98 -506q-2 -18 -23 -18h-53q-23 0 -18 18zM236 610q53 -2 101 -2h95q117 0 197 22.5t131 76.5q57 59 85.5 147t28.5 176 q0 123 -72.5 176.5t-232.5 53.5q-55 0 -101 -4.5t-77 -10.5q-23 -4 -30 -13t-11 -32z" />
<glyph unicode="Q" horiz-adv-x="1161" d="M82 334q0 98 23.5 229t58.5 291q27 127 69.5 219t107.5 152.5t153 89t206 28.5q176 0 275.5 -79.5t99.5 -276.5q0 -100 -22.5 -231t-61.5 -285q-53 -217 -141 -329.5t-234 -145.5q41 -74 106.5 -144.5t131.5 -123.5q18 -16 2 -27l-55 -37q-18 -10 -39 8 q-68 61 -131.5 142.5t-110.5 169.5q-12 -2 -24.5 -2h-24.5q-184 0 -286.5 77.5t-102.5 274.5zM180 348q0 -143 64.5 -212.5t228.5 -69.5q86 0 151.5 20t114.5 69.5t87 131.5t71 205q18 68 32.5 136t25 132.5t16.5 118t6 90.5q0 156 -65.5 223.5t-219.5 67.5 q-90 0 -158.5 -23t-121 -74t-90 -133t-64.5 -199q-35 -145 -56.5 -272t-21.5 -211z" />
<glyph unicode="R" horiz-adv-x="1005" d="M27 18l243 1266q6 33 33 39q41 10 102.5 15t133.5 5q205 0 312 -61t107 -213q0 -84 -20 -161t-62 -140t-105.5 -106.5t-149.5 -59.5l231 -582q8 -20 -12 -20h-62q-20 0 -28 18l-218 549q-4 12 -11 15.5t-17 3.5q-33 -2 -68 -3t-65 -1q-35 0 -73 1t-69 3l-108 -568 q-2 -18 -23 -18h-53q-23 0 -18 18zM246 672q41 -2 89 -3t101 -1q111 0 186.5 18.5t125.5 67.5q55 55 84.5 137t29.5 158q0 63 -20.5 105t-62.5 64.5t-105.5 32t-147.5 9.5q-45 0 -76.5 -3.5t-58.5 -7.5q-23 -4 -30 -12t-11 -31z" />
<glyph unicode="S" horiz-adv-x="980" d="M2 78l16 41q8 23 31 10q66 -31 142.5 -47t146.5 -16q141 0 231 53t125 176q10 39 17.5 77t7.5 66q0 74 -39 115t-145 70l-160 41q-109 27 -156 80t-47 151q0 41 10.5 95t26.5 108q33 113 126 179t277 66q154 0 289 -47q14 -4 16.5 -11t-2.5 -17l-12 -39q-6 -16 -33 -8 q-135 39 -266 39q-143 0 -210.5 -50.5t-96.5 -152.5q-10 -37 -17.5 -76t-7.5 -78q0 -55 28 -94t114 -61l166 -43q137 -35 188 -92.5t51 -157.5q0 -33 -8 -79t-23 -93q-45 -154 -155.5 -227.5t-288.5 -73.5q-92 0 -179 18t-153 49q-18 8 -10 29z" />
<glyph unicode="T" horiz-adv-x="937" d="M143 1262l11 45q2 18 26 18h834q20 0 18 -18l-10 -50q-2 -16 -23 -16h-374l-238 -1223q-2 -18 -22 -18h-56q-23 0 -18 18l237 1223h-366q-25 0 -19 21z" />
<glyph unicode="U" horiz-adv-x="1140" d="M78 281q0 61 12 129.5t25 130.5l147 766q2 18 23 18h55q20 0 18 -18l-170 -869q-10 -49 -13 -88t-3 -65q0 -66 23.5 -108t63.5 -66.5t92 -34.5t110 -10q88 0 154.5 14t114.5 55t81 114t55 189l166 869q2 18 23 18h55q20 0 18 -18l-170 -881q-25 -127 -61.5 -213t-95 -137 t-143.5 -72.5t-203 -21.5q-82 0 -152 15t-120 50t-77.5 92.5t-27.5 141.5z" />
<glyph unicode="V" horiz-adv-x="1011" d="M150 1305q-2 20 24 20h47q27 0 29 -20q23 -311 57.5 -613.5t75.5 -607.5h16q168 303 308.5 600t281.5 621q8 20 35 20h51q29 0 21 -20q-72 -158 -143.5 -311.5t-145.5 -306t-153 -307.5t-167 -314q-20 -37 -37.5 -51.5t-50.5 -14.5h-37q-35 0 -50 15.5t-21 58.5 q-43 295 -79 598t-62 633z" />
<glyph unicode="W" horiz-adv-x="1589" d="M152 1305q0 14 6 17t22 3h41q16 0 22.5 -3t6.5 -17q0 -293 8 -608.5t25 -612.5h16q125 262 241.5 536.5t241.5 577.5q16 37 34 49.5t50 12.5h45q37 0 50.5 -12.5t15.5 -49.5q10 -283 22.5 -557t32.5 -557h17q154 330 284.5 631t241.5 590q8 20 35 20h43q28 0 20 -20 q-55 -143 -113.5 -285.5t-124 -292t-139 -309.5t-159.5 -338q-20 -41 -39 -60.5t-56 -19.5h-34q-29 0 -45.5 16.5t-20.5 61.5q-20 256 -31.5 521t-23.5 570h-19q-57 -135 -113.5 -268t-115.5 -267t-121.5 -274.5t-132.5 -289.5q-16 -39 -36.5 -54.5t-51.5 -15.5h-37 q-33 0 -45 16.5t-16 57.5q-20 266 -31.5 566t-15.5 665z" />
<glyph unicode="X" horiz-adv-x="976" d="M-82 29l412 639q10 14 10 28q0 8 -2 16.5t-6 18.5l-166 572q-6 23 22 22h41q27 0 33 -18l152 -545q6 -20 16 -28.5t35 -8.5h35q23 0 35 8t28 31l365 545q10 16 28 16h68q27 0 8 -29l-405 -583q-10 -14 -11 -25q0 -8 7 -26l219 -639q6 -23 -23 -23h-45q-27 0 -33 18 l-194 596q-6 18 -15.5 23.5t-35.5 5.5h-39q-23 0 -34 -3t-26 -26l-379 -598q-10 -16 -28 -16h-64q-27 0 -8 29z" />
<glyph unicode="Y" horiz-adv-x="890" d="M145 1309q0 16 19 16h55q20 0 23 -16q16 -209 62 -397.5t104 -342.5h16q68 90 128 177.5t116.5 177.5t109.5 185t107 204q6 12 22 12h60q20 0 14 -16q-55 -109 -111.5 -211.5t-120 -204.5t-138 -208.5t-167.5 -225.5l-86 -441q-2 -18 -22 -18h-55q-20 0 -19 18l88 447 q-82 211 -132 413.5t-73 430.5z" />
<glyph unicode="Z" horiz-adv-x="970" d="M-31 23l2 18q4 27 15.5 47.5t46.5 65.5l846 1087h-672q-20 0 -17 19l11 47q2 18 24 18h736q33 0 30 -29l-2 -22q-4 -29 -20.5 -57.5t-61.5 -83.5l-819 -1049h674q20 0 18 -18l-10 -48q-4 -18 -25 -18h-749q-31 0 -27 23z" />
<glyph unicode="[" horiz-adv-x="505" d="M-18 -174l303 1556q2 14 16 15h293q16 0 14 -15l-10 -55q-2 -14 -20 -14h-209l-275 -1417h207q16 0 14 -15l-10 -55q-2 -14 -20 -14h-291q-14 0 -12 14z" />
<glyph unicode="\" horiz-adv-x="620" d="M184 1399q-2 14 13 14h57q18 0 20 -14l162 -1438q2 -14 -12 -14h-57q-18 0 -21 14z" />
<glyph unicode="]" horiz-adv-x="514" d="M-117 -174l11 55q2 14 20 15h209l274 1417h-207q-16 0 -14 14l10 55q2 14 21 15h291q14 0 12 -15l-303 -1556q-2 -14 -17 -14h-292q-16 0 -15 14z" />
<glyph unicode="^" horiz-adv-x="1048" d="M166 756l399 463q12 14 22.5 18t28.5 4h66q18 0 28.5 -6t20.5 -27l213 -465q10 -18 -12 -26l-41 -19q-23 -10 -31 7l-209 430h-18l-391 -435q-10 -10 -16.5 -12t-16.5 6l-41 33q-16 12 -2 29z" />
<glyph unicode="_" horiz-adv-x="989" d="M-96 -170l6 39q2 10 6 11t10 1h848q10 0 8 -12l-6 -39q-2 -12 -16 -12h-848q-8 0 -8 12z" />
<glyph unicode="`" horiz-adv-x="579" d="M185.5 1373q-9.5 11 0.5 30l23 49q10 20 33 12l401 -172q16 -6 10 -22l-20 -51q-6 -12 -23 -7l-397 144q-18 6 -27.5 17z" />
<glyph unicode="a" horiz-adv-x="933" d="M33 174q0 47 11 102.5t28 108.5q14 43 38.5 79t59.5 58q84 57 264 58h98q37 0 82 -3.5t80 -9.5l15 74q22 104 22 157q0 82 -50 113t-165 31q-61 0 -132 -12t-130 -31q-23 -6 -25 10l-6 45q-2 18 15 23q68 20 143.5 32.5t153.5 12.5q55 0 106 -9t91 -32t63.5 -61.5 t25.5 -96.5q0 -33 -9 -91t-19 -118l-113 -598q-2 -16 -18 -16h-48q-20 0 -16 20l12 89h-4q-45 -61 -116.5 -94t-182.5 -33h-22q-139 0 -195.5 49t-56.5 143zM123 186q0 -66 42 -95.5t136 -29.5h18q139 0 223.5 69t106.5 194l31 165q-35 4 -73 7.5t-83 3.5h-114 q-131 0 -191 -45q-23 -16 -38 -41t-25 -56q-12 -39 -22.5 -85t-10.5 -87z" />
<glyph unicode="b" horiz-adv-x="980" d="M72 227q0 43 11 108.5t28 151.5l174 885q2 14 18 14h57q14 0 13 -14l-90 -452h4q45 47 118.5 74.5t180.5 27.5q104 0 164.5 -30.5t91.5 -90.5q28 -59 28 -141v-6q0 -76 -16 -179.5t-43 -220.5q-23 -100 -57.5 -170.5t-87 -115.5t-124 -65.5t-169.5 -20.5q-86 0 -144.5 15 t-93.5 46t-49 77t-14 107zM162 238q0 -94 44 -135.5t171 -41.5q80 0 136 15.5t96 50.5t67 94.5t47 149.5q25 109 40 210t15 154q0 119 -50 163t-161 44q-139 0 -222 -62.5t-112 -215.5l-38 -203q-14 -74 -23.5 -125t-9.5 -98z" />
<glyph unicode="c" horiz-adv-x="835" d="M63 250q0 31 5.5 75t13.5 94t19.5 102t21.5 102q14 63 28.5 112t33 86t40 65.5t49.5 53.5q47 41 113 61.5t158 20.5q72 0 140.5 -17.5t121.5 -48.5q18 -10 10 -28l-18 -37q-8 -16 -31 -6q-51 25 -112.5 41t-120.5 16t-109.5 -15.5t-85.5 -45.5q-43 -37 -72.5 -98.5 t-56.5 -174.5q-10 -45 -20.5 -93t-18.5 -95t-12 -89t-4 -73q0 -104 54 -150.5t183 -46.5q25 0 56.5 4.5t65.5 11.5t66.5 17t59.5 21q8 4 13 3t10 -14l8 -38q4 -14 -1 -19.5t-16 -9.5q-55 -23 -132 -39t-148 -16q-150 0 -231 60t-81 208z" />
<glyph unicode="d" horiz-adv-x="964" d="M63 250q0 72 15.5 168t36.5 203q20 117 55 193.5t84 122.5t112.5 65.5t145.5 19.5q104 0 173 -34t103 -107h3l96 491q2 14 18 14h58q14 0 12 -14l-203 -1036q-23 -109 -56.5 -178.5t-83.5 -108.5t-116.5 -53t-153.5 -14q-164 0 -231.5 66.5t-67.5 201.5zM154 254 q0 -100 52 -146.5t169 -46.5q74 0 126 15.5t89 49.5t59.5 87t36.5 127l53 270q8 39 10.5 70t2.5 53q0 104 -63.5 156.5t-174.5 52.5q-68 0 -118 -16.5t-88 -56.5t-63.5 -105.5t-43.5 -161.5t-32.5 -187t-14.5 -161z" />
<glyph unicode="e" horiz-adv-x="892" d="M63 250q0 37 6.5 92t15.5 114.5t20.5 118t19.5 101.5q37 186 125 266q49 43 116.5 61.5t153.5 18.5q150 0 219.5 -63.5t69.5 -182.5q0 -29 -4 -65.5t-10.5 -76.5t-13.5 -82t-15 -79q-4 -14 -12.5 -23.5t-30.5 -9.5h-553q-18 -104 -18 -180q0 -55 12 -93t40.5 -61.5 t76 -34t116.5 -10.5q61 0 137 16.5t130 35.5q20 8 22 -11l8 -41q2 -16 -14 -24q-70 -27 -147.5 -41t-147.5 -14q-94 0 -155.5 18t-98.5 53t-52.5 84t-15.5 113zM184 518h473q25 0 31 29q14 66 22.5 122t8.5 97q0 98 -54.5 137t-156.5 39q-66 0 -115 -13.5t-86 -48t-61.5 -95 t-40.5 -152.5z" />
<glyph unicode="f" horiz-adv-x="522" d="M-47 -344q25 59 43 115.5t34.5 119t32 134t31.5 161.5l131 736h-123q-16 0 -12 16l6 33q6 16 21 18l127 19l12 57q20 92 44 156.5t61.5 105.5t95 59.5t143.5 18.5q43 0 85 -6t71 -17q16 -8 14 -22l-10 -37q-4 -10 -8 -13t-19 1q-29 6 -61.5 10t-65.5 4q-61 0 -102 -9 t-69.5 -37t-48 -77t-36.5 -129l-14 -69h227q14 0 12 -15l-10 -55q-4 -12 -16 -12h-232l-129 -701q-20 -109 -36.5 -189.5t-33 -145t-34.5 -120t-43 -112.5q-6 -16 -25 -16h-51q-20 0 -12 18z" />
<glyph unicode="g" horiz-adv-x="964" d="M81 415.5q15 102.5 38 221.5q20 113 54 187.5t83 118.5t113.5 61.5t148.5 17.5q96 0 167 -34t99 -105h2l29 100q4 16 9 18.5t18 2.5h35q25 0 20 -23l-195 -985q-20 -102 -46.5 -174t-67.5 -118t-103.5 -66.5t-152.5 -20.5q-66 0 -136.5 12.5t-115.5 30.5q-18 8 -14 23 l14 41q4 10 8 12t16 -2q53 -18 112.5 -28.5t117.5 -10.5q70 0 118 17.5t79.5 54t52 94t37.5 135.5l22 108h-6q-39 -59 -108.5 -90.5t-178.5 -31.5q-284 0 -284 272q0 59 15 161.5zM154 258q0 -104 52 -150.5t152 -46.5q145 0 224.5 72t107.5 217l49 250q6 31 9.5 66.5 t3.5 70.5q0 92 -60.5 148.5t-167.5 56.5q-72 0 -124 -16.5t-90 -53t-62.5 -97t-42.5 -150.5q-23 -119 -37 -213.5t-14 -153.5z" />
<glyph unicode="h" horiz-adv-x="991" d="M35 16l262 1356q2 14 18 14h58q14 0 12 -14l-94 -483h4q47 61 124 97t199 36q137 0 196.5 -57.5t59.5 -161.5q0 -47 -11 -117t-27 -154l-99 -518q-2 -14 -18 -14h-55q-16 0 -15 16l113 586q12 63 18 106.5t6 73.5q0 78 -43 119t-147 41q-66 0 -123 -16.5t-103 -54 t-79 -99t-51 -154.5l-117 -604q-2 -14 -19 -14h-55q-16 0 -14 16z" />
<glyph unicode="i" horiz-adv-x="446" d="M68 14l188 975q2 14 18 15h60q14 0 12 -15l-188 -975q-2 -14 -19 -14h-59q-14 0 -12 14zM278 1214q-5 8 -5 23q0 20 8 53q10 39 28.5 52.5t59.5 13.5h6q47 0 55 -22q3 -8 3 -22q0 -23 -9 -61q-10 -39 -28.5 -48t-53.5 -9h-8q-44 0 -56 20z" />
<glyph unicode="j" horiz-adv-x="415" d="M-295 -342l8 47q2 14 19 14h39q119 0 192 76q37 39 54.5 97.5t27.5 115.5l186 981q2 14 19 15h59q14 0 13 -15l-191 -991q-14 -74 -37.5 -144.5t-77.5 -123.5q-43 -43 -106.5 -65.5t-155.5 -22.5h-33q-18 0 -16 16zM253 1214q-5 8 -5 24q0 20 8 52q10 39 28.5 52.5 t59.5 13.5h6q47 0 56 -22q3 -9 3 -22q0 -23 -10 -61q-10 -39 -28.5 -48t-53.5 -9h-8q-45 0 -56 20z" />
<glyph unicode="k" horiz-adv-x="876" d="M43 16l262 1356q2 14 19 14h57q14 0 12 -14l-151 -797h118q20 0 35 6.5t33 28.5l338 381q10 12 25 13h67q27 0 8 -21l-389 -434q-8 -12 -8 -17q0 -6 4 -14l229 -502q8 -16 -10 -16h-61q-16 0 -25 16l-203 453q-6 14 -14 19.5t-27 5.5h-137l-94 -480q-2 -14 -18 -14h-56 q-16 0 -14 16z" />
<glyph unicode="l" horiz-adv-x="497" d="M74 123q0 41 12 106l221 1143q2 14 19 14h57q14 0 12 -14l-221 -1147q-6 -29 -8 -52t-2 -34q0 -31 15.5 -46t58.5 -15h94q14 0 12 -15l-8 -49q-2 -14 -25 -14h-86q-84 0 -117.5 30.5t-33.5 92.5z" />
<glyph unicode="m" horiz-adv-x="1585" d="M37 16l188 977q2 10 15 11h53q14 0 12 -15l-10 -100h4q47 59 126 96t189 37q100 0 158.5 -39t83.5 -111q53 59 145.5 104.5t204.5 45.5q133 0 196.5 -61.5t63.5 -165.5q0 -39 -9 -99.5t-23 -138.5l-103 -543q-2 -14 -18 -14h-56q-16 0 -14 16l109 566q12 66 19 109.5 t7 82.5q0 86 -46 127t-144 41q-92 0 -173 -41t-145 -117q-4 -47 -12 -103t-20 -124l-103 -543q-2 -14 -18 -14h-55q-16 0 -15 16l109 566q10 55 18 103t8 81q0 92 -49 134t-133 42q-135 0 -230.5 -73.5t-127.5 -250.5l-117 -604q-2 -14 -19 -14h-55q-16 0 -14 16z" />
<glyph unicode="n" horiz-adv-x="993" d="M37 16l188 977q2 10 15 11h53q14 0 12 -15l-10 -100h4q45 61 122 97t200 36q137 0 196.5 -57.5t59.5 -161.5q0 -47 -11.5 -117t-27.5 -154l-99 -518q-2 -14 -18 -14h-55q-16 0 -15 16l113 586q12 63 18 106.5t6 73.5q0 78 -43 119t-147 41q-66 0 -123 -15.5t-103 -53 t-79 -100t-51 -155.5l-117 -604q-2 -14 -19 -14h-55q-16 0 -14 16z" />
<glyph unicode="o" horiz-adv-x="964" d="M63 250q0 70 13.5 154.5t40.5 207.5q25 115 61.5 194t88.5 127t124 68.5t166 20.5q154 0 225.5 -67.5t71.5 -186.5q0 -68 -12.5 -165t-40.5 -212q-27 -111 -61.5 -188.5t-86 -126.5t-122.5 -71.5t-169 -22.5q-160 0 -229.5 67.5t-69.5 200.5zM156 260q0 -92 46 -145.5 t175 -53.5q76 0 130 19.5t93 62.5t66.5 114t50.5 173q23 98 34 181t11 132q0 102 -49 150.5t-172 48.5q-78 0 -132.5 -19.5t-93.5 -62.5t-65.5 -113.5t-48.5 -173.5q-23 -98 -34 -181t-11 -132z" />
<glyph unicode="p" horiz-adv-x="1005" d="M-33 -346l256 1339q2 10 15 11h55q14 0 12 -15l-10 -98h4q47 66 118.5 98.5t186.5 32.5q158 0 225.5 -66.5t67.5 -189.5q0 -61 -11.5 -148.5t-33.5 -197.5q-27 -133 -63.5 -218t-88 -134.5t-118 -67.5t-152.5 -18q-123 0 -186.5 39.5t-91.5 111.5h-7l-90 -481 q-2 -14 -18 -14h-55q-18 0 -15 16zM186 293q0 -49 12.5 -91t42 -73t77.5 -49.5t120 -18.5q70 0 121 17.5t89 60.5t66.5 117t51.5 184q18 86 29.5 171t11.5 145q0 92 -49 139t-168 47q-61 0 -114.5 -12t-98.5 -46t-78 -91.5t-51 -145.5l-51 -254q-4 -27 -7.5 -51.5 t-3.5 -48.5z" />
<glyph unicode="q" horiz-adv-x="964" d="M81 414.5q15 101.5 38 218.5q20 117 56 192.5t86 119.5t113.5 60.5t143.5 16.5q96 0 167 -34t99 -105h2l29 100q4 16 9 18.5t18 2.5h35q25 0 20 -23l-258 -1329q-2 -14 -18 -14h-54q-18 0 -14 16l90 450h-6q-39 -59 -108.5 -90.5t-178.5 -31.5q-284 0 -284 272 q0 59 15 160.5zM154 258q0 -104 52 -150.5t152 -46.5q145 0 224.5 72t107.5 217l49 250q6 31 9.5 66.5t3.5 70.5q0 92 -60.5 148.5t-167.5 56.5q-72 0 -124 -15.5t-89 -52t-61.5 -97t-42.5 -152.5q-23 -119 -38 -213.5t-15 -153.5z" />
<glyph unicode="r" horiz-adv-x="598" d="M37 16l188 977q2 10 15 11h53q14 0 12 -15l-12 -106h4q45 66 106.5 93.5t139.5 27.5h75q16 0 15 -15l-12 -53q-4 -14 -21 -14h-65q-72 0 -122.5 -25t-85 -71t-58 -113.5t-40.5 -155.5l-104 -543q-2 -14 -19 -14h-55q-16 0 -14 16z" />
<glyph unicode="s" horiz-adv-x="802" d="M-8 68l14 36q8 18 29 11q53 -23 117.5 -38.5t130.5 -15.5q100 0 159.5 29t92.5 94q16 31 24 71t8 75q0 47 -35.5 77.5t-119.5 55.5l-113 33q-109 31 -146.5 78t-37.5 112q0 33 5 64.5t17 72.5q33 104 117 151.5t211 47.5q61 0 124.5 -11.5t117.5 -31.5q18 -8 12 -27 l-14 -39q-6 -18 -27 -10q-104 37 -223 37q-104 0 -156.5 -33t-75.5 -96q-20 -59 -20 -115q0 -41 26.5 -71.5t110.5 -55.5l111 -32q61 -18 100 -38t62.5 -43.5t31.5 -53t8 -68.5q0 -45 -11 -91t-28 -83q-41 -102 -131 -140t-202 -38q-152 0 -279 57q-18 8 -10 29z" />
<glyph unicode="t" horiz-adv-x="579" d="M82 938l6 33q2 16 21 18l129 19l47 237q2 14 18 15h57q14 0 13 -15l-47 -241h243q14 0 13 -15l-11 -55q-4 -12 -16 -12h-246l-96 -523q-14 -76 -23.5 -130t-9.5 -91q0 -53 33 -76.5t111 -23.5h112q14 0 13 -15l-11 -49q-2 -14 -18 -14h-113q-104 0 -159.5 38t-55.5 130 q0 55 12.5 120.5t22.5 123.5l94 510h-127q-14 0 -12 16z" />
<glyph unicode="u" horiz-adv-x="974" d="M74 213q0 20 3 52t13 77l123 647q2 14 18 15h58q14 0 12 -15l-123 -637q-16 -78 -16 -133q0 -78 49 -118t168 -40q80 0 129 12.5t86 43.5q41 35 65.5 94t42.5 151l121 627q2 14 19 15h57q14 0 12 -15l-125 -645q-23 -109 -52 -180.5t-83 -112.5q-47 -39 -111.5 -54 t-172.5 -15q-152 0 -222.5 57t-70.5 174z" />
<glyph unicode="v" horiz-adv-x="823" d="M90 985q0 18 19 19h49q10 0 15 -2.5t5 -14.5q12 -225 36 -457.5t58 -449.5h17q66 111 130 227.5t123.5 233t113 229.5t96.5 217q4 12 9 14.5t15 2.5h60q18 0 10 -21q-49 -111 -107.5 -232.5t-121 -243.5t-128.5 -239t-127 -217q-20 -33 -35.5 -42t-37.5 -9h-39 q-47 0 -55 53q-41 223 -66 460t-39 472z" />
<glyph unicode="w" horiz-adv-x="1306" d="M94 981q0 23 27 23h35q16 0 22 -3.5t6 -19.5q0 -233 7 -461.5t28 -439.5h12q106 215 197.5 415.5t181.5 411.5q12 31 25.5 39t42.5 8h47q53 0 55 -61q4 -102 10.5 -200.5t12.5 -198t15 -202t20 -212.5h12q111 219 208 453.5t177 449.5q8 20 31 21h43q28 0 20 -27 q-37 -98 -85 -215t-100 -239t-106.5 -239.5t-105.5 -220.5q-16 -35 -37 -49t-49 -14h-25q-29 0 -46 13.5t-21 54.5q-23 201 -35 398.5t-23 401.5h-10q-88 -205 -180 -404.5t-191 -400.5q-16 -35 -33.5 -49t-54.5 -14h-28q-31 0 -45.5 14.5t-18.5 51.5q-23 209 -32 432 t-9 483z" />
<glyph unicode="x" horiz-adv-x="843" d="M-72 18l342 482q8 10 9 20q0 4 -1.5 8t-3.5 11l-174 446q-6 18 21 19h41q23 0 28 -17l158 -403q14 -37 37 -37h12q16 0 24.5 7t29.5 34l303 401q12 14 30 15h58q29 0 12 -23l-354 -457q-12 -16 -13 -22q0 -2 5 -15l210 -467q10 -20 -12 -20h-53q-23 0 -31 20l-182 420 q-8 16 -15.5 23.5t-23.5 7.5h-12q-16 0 -25.5 -6t-21.5 -23l-301 -428q-12 -14 -35 -14h-51q-25 0 -11 18z" />
<glyph unicode="y" horiz-adv-x="808" d="M94 987q0 16 17 17h59q16 0 16 -17q8 -272 24.5 -472t39.5 -349q4 -35 22.5 -48.5t53.5 -13.5q102 182 209.5 413t205.5 472q4 14 23 15h59q23 0 15 -19q-66 -156 -131.5 -300t-133.5 -282q-82 -166 -176 -344t-217 -397q-6 -10 -11 -12t-15 2l-45 20q-19 11 -9 29 q45 76 91.5 158t91.5 166q-49 4 -77 29.5t-38 86.5q-12 86 -22.5 177.5t-19.5 194.5t-16.5 220t-15.5 254z" />
<glyph unicode="z" horiz-adv-x="823" d="M-31 23l4 22q4 18 9.5 28.5t23.5 32.5l660 820h-514q-12 0 -11 14l9 51q2 12 16 13h606q25 0 23 -21l-2 -18q-2 -16 -9.5 -28.5t-29.5 -41.5l-668 -817h518q12 0 10 -15l-8 -51q-2 -12 -16 -12h-600q-23 0 -21 23z" />
<glyph unicode="{" horiz-adv-x="581" d="M47 580l10 53q2 16 19 18q94 18 141 64.5t68 146.5l67 348q20 102 66.5 144.5t148.5 42.5h74q16 0 14 -15l-12 -55q-2 -14 -18 -14h-66q-57 0 -79.5 -24.5t-39.5 -98.5l-61 -311q-25 -125 -79 -195t-159 -80v-6q96 -25 125 -88.5t4 -192.5l-55 -280q-4 -25 -9 -50.5 t-5 -43.5q0 -25 15 -36t56 -11h76q16 0 14 -15l-12 -55q-2 -14 -18 -14h-72q-152 0 -151 108q0 16 2 38t6 40l63 330q20 106 -8.5 156.5t-110.5 72.5q-16 4 -14 23z" />
<glyph unicode="|" horiz-adv-x="399" d="M20 -29l279 1428q2 18 20 18h52q14 0 12 -18l-279 -1428q-4 -18 -18 -18h-51q-18 0 -15 18z" />
<glyph unicode="}" horiz-adv-x="583" d="M-100 -174l12 55q2 14 18 15h66q57 0 79.5 24.5t39.5 97.5l61 312q25 125 79 194.5t159 79.5v6q-96 25 -125 88.5t-4 192.5l55 280q4 25 9 50.5t5 44.5q0 25 -15 36t-56 11h-76q-16 0 -14 14l12 55q2 14 18 15h72q152 0 151 -109q0 -16 -2 -37.5t-6 -40.5l-63 -329 q-10 -53 -9 -92t16 -66t43 -43t69 -29q16 -4 14 -22l-10 -54q-2 -16 -19 -18q-94 -18 -141 -64.5t-68 -146.5l-67 -348q-20 -102 -66.5 -144t-148.5 -42h-74q-16 0 -14 14z" />
<glyph unicode="~" horiz-adv-x="1048" d="M48 571.5q-3 6.5 3 16.5q74 131 146.5 181t158.5 50q39 0 79 -17.5t81 -47.5l100 -80q37 -29 63 -39t60 -10q51 0 109.5 42t112.5 124q10 16 26 4l37 -25q10 -8 10 -13t-4 -14q-39 -63 -78 -105t-76.5 -67.5t-74.5 -36t-72 -10.5q-47 0 -87 15.5t-83 50.5l-94 74 q-37 29 -65.5 43t-63.5 14q-41 0 -90 -36t-117 -140q-6 -10 -12 -12.5t-19 4.5l-43 24q-4 4 -7 10.5z" />
<glyph unicode="&#xa1;" horiz-adv-x="448" d="M-16 -296l209 1013q2 14 18 15h41q20 0 16 -17l-188 -1011q-2 -16 -18 -17h-60q-20 0 -18 17zM189 877q0 18 12 74q10 41 27.5 56t54.5 15h16q53 0 53 -45q0 -6 -2 -21t-8 -50q-8 -41 -27.5 -57.5t-52.5 -16.5h-18q-55 0 -55 45z" />
<glyph unicode="&#xa2;" d="M158 250q0 31 5 75t13 94t19.5 102t21.5 102q14 63 28.5 112t33 86t40 65.5t50.5 53.5q43 37 103 57.5t144 24.5l37 182q4 20 29 21h29q29 0 22 -23l-37 -184q117 -12 207 -60q8 -4 11.5 -10t-3.5 -18l-18 -37q-8 -16 -31 -6q-84 41 -180 51l-172 -877q51 4 110.5 17.5 t108.5 32.5q23 8 29 -13l8 -32q6 -20 -16 -29q-51 -23 -120 -37t-134 -18l-33 -170q-6 -20 -29 -21h-26q-29 0 -23 25l33 168q-127 10 -193.5 70.5t-66.5 195.5zM250 258q0 -94 42 -139t140 -56l170 877q-109 -6 -168 -59q-43 -37 -72.5 -98.5t-56.5 -174.5 q-10 -45 -20.5 -93t-18.5 -95t-12 -89t-4 -73z" />
<glyph unicode="&#xa3;" d="M-6 25l6 34q4 16 10 25.5t21 19.5q57 43 98 81t71.5 83t51 103.5t37.5 138.5l26 139h-116q-16 0 -15 15l6 34q2 8 6.5 12.5t20.5 8.5l117 20l37 199q23 111 51.5 185.5t75.5 121.5q53 53 122.5 75.5t155.5 22.5q68 0 137.5 -16t120.5 -43q23 -10 10 -31l-16 -32 q-8 -16 -31 -9q-55 23 -114.5 35.5t-106.5 12.5q-68 0 -118 -14.5t-88 -51.5t-63.5 -99.5t-43.5 -158.5l-39 -201h385q16 0 14 -16l-10 -57q-2 -12 -18 -13h-387l-21 -104q-16 -82 -35.5 -149.5t-49.5 -123t-69.5 -101.5t-97.5 -83v-4h664q20 0 18 -18l-10 -50 q-2 -16 -23 -16h-766q-14 0 -21 4t-3 21z" />
<glyph unicode="&#xa4;" horiz-adv-x="1056" d="M10 172q-12 14 6 31l181 147q-16 31 -26.5 73t-10.5 87q0 88 30.5 163t83.5 132l-114 139q-12 14 6 31l43 35q20 16 33 0l114 -140q106 66 240 66q51 0 104.5 -16.5t98.5 -51.5l182 148q20 16 33 0l33 -41q12 -16 -7 -31l-180 -147q16 -31 26.5 -68t10.5 -82 q0 -96 -30.5 -172t-84.5 -133l115 -139q12 -16 -6 -31l-43 -35q-20 -16 -33 0l-115 139q-53 -33 -114.5 -49t-124.5 -16q-55 0 -106.5 16.5t-96.5 51.5l-182 -148q-20 -16 -33 0zM258 522q0 -117 62.5 -171t154.5 -54q76 0 135.5 25.5t101.5 70.5t64.5 106.5t22.5 133.5 q0 57 -17.5 99t-48.5 67.5t-69.5 38t-81.5 12.5q-76 0 -135.5 -25.5t-101.5 -70.5t-64.5 -104.5t-22.5 -127.5z" />
<glyph unicode="&#xa5;" d="M109 260l8 47q2 18 22 19h271l36 180h-272q-12 0 -15 5t-1 15l8 47q2 18 22 19h226q-59 176 -105.5 345t-75.5 372q-4 16 19 16h55q20 0 23 -16q31 -211 76 -390.5t100 -326.5h10q70 94 129.5 179t112.5 171t103 177t104 194q6 12 20 12h70q20 0 14 -16 q-49 -96 -100 -185.5t-106.5 -176.5t-117 -174t-133.5 -181h217q12 0 15.5 -5t1.5 -16l-8 -47q-2 -18 -23 -18h-272l-37 -180h274q12 0 15.5 -5.5t1.5 -15.5l-9 -47q-2 -18 -22 -18h-277l-43 -222q-2 -18 -22 -18h-55q-20 0 -19 18l43 222h-270q-16 0 -14 20z" />
<glyph unicode="&#xa6;" horiz-adv-x="399" d="M20 -29l95 484q2 18 20 18h51q14 0 13 -18l-95 -484q-4 -18 -18 -18h-51q-18 0 -15 18zM205 913l94 486q2 18 20 18h52q14 0 12 -18l-94 -486q-4 -18 -21 -18h-51q-16 0 -12 18z" />
<glyph unicode="&#xa7;" horiz-adv-x="856" d="M39 59l12 31q4 10 10.5 13t22.5 -3q39 -14 101.5 -26.5t123.5 -12.5q70 0 131.5 16.5t89.5 69.5q16 29 24.5 63t8.5 60q0 61 -35.5 89t-113.5 51l-119 36q-86 27 -121 70t-35 102q0 35 7.5 68t17.5 62q18 47 62 86.5t104 56.5q-57 23 -81 63.5t-24 94.5q0 27 4 55.5 t15 58.5q35 102 113.5 141t207.5 39q57 0 120 -9t103 -25q23 -8 17 -27l-10 -35q-4 -10 -8.5 -13t-16.5 1q-37 10 -92 19.5t-107 9.5q-106 0 -162.5 -26t-82.5 -95q-8 -23 -11.5 -46.5t-3.5 -37.5q0 -49 32 -78t126 -51l86 -21q96 -23 137 -65.5t41 -108.5t-22 -121 q-20 -49 -60.5 -94t-105.5 -71q104 -49 104 -162q0 -23 -7 -65t-21 -77q-39 -94 -121 -128.5t-197 -34.5q-66 0 -136.5 14t-117.5 35q-18 8 -10 28zM225 631q0 -45 24.5 -70.5t94.5 -48.5l115 -35q63 18 107 59.5t63 88.5q18 45 18 96q0 47 -27.5 73.5t-109.5 47.5l-90 20 q-66 -14 -111 -49t-65 -86q-8 -23 -13.5 -47.5t-5.5 -48.5z" />
<glyph unicode="&#xa8;" horiz-adv-x="673" d="M209 1300q0 16 10 68q14 74 88 74h31q61 0 61 -51q0 -20 -10 -66q-10 -41 -30.5 -56.5t-61.5 -15.5h-25q-63 0 -63 47zM539 1300q0 16 10 68q14 74 88 74h31q61 0 61 -51q0 -20 -10 -66q-10 -41 -30.5 -56.5t-61.5 -15.5h-25q-63 0 -63 47z" />
<glyph unicode="&#xa9;" horiz-adv-x="1658" d="M82 549q0 203 72.5 369.5t196.5 285.5t287 184.5t343 65.5q135 0 245.5 -40t188.5 -113.5t120 -181t42 -242.5q0 -203 -72.5 -370t-196.5 -286t-287 -184.5t-343 -65.5q-135 0 -245.5 40t-188.5 114t-120 181.5t-42 242.5zM174 551q0 -121 37 -213t105.5 -154.5 t164.5 -94.5t215 -32q162 0 306.5 60.5t251 170t169 259t62.5 327.5q0 121 -37 213.5t-105.5 155t-165 94t-214.5 31.5q-164 0 -307.5 -60.5t-250 -170t-169 -259t-62.5 -327.5zM487 549q0 61 11.5 131t33 137.5t53 129t72.5 108.5q53 61 123 92t164 31q53 0 103.5 -12.5 t91.5 -35.5q16 -10 6 -32l-12 -27q-6 -12 -11.5 -13t-15.5 3q-37 16 -79 27.5t-85 11.5q-131 0 -205 -80q-39 -43 -68.5 -100.5t-49 -122t-29.5 -129t-10 -119.5q0 -104 47 -151.5t139 -47.5q104 0 205 41q18 6 22 -10l11 -33q6 -18 -13 -26q-53 -23 -114.5 -37.5 t-131.5 -14.5q-125 0 -191.5 69t-66.5 210z" />
<glyph unicode="&#xaa;" horiz-adv-x="598" d="M86 729q0 20 4 47t10 55q16 86 71.5 116t170.5 30h68q23 0 44 -1t40 -3l8 39q4 23 7 38t3 31q0 41 -29.5 57.5t-89.5 16.5q-43 0 -89 -7t-83 -20q-16 -4 -18 11l-8 37q-2 12 12 18q45 14 96 22.5t103 8.5q92 0 140 -31t48 -100q0 -18 -3 -38t-9 -55l-72 -366 q-2 -14 -16 -14h-33q-16 0 -17 14l2 49h-4q-27 -35 -68.5 -54.5t-111.5 -19.5h-31q-72 0 -108.5 28t-36.5 91zM170 735q0 -33 20.5 -45t61.5 -12h35q78 0 122 42t58 114l14 71q-12 2 -34.5 3t-45.5 1h-77q-70 0 -100.5 -17.5t-43.5 -76.5q-4 -25 -7 -46t-3 -34z" />
<glyph unicode="&#xab;" horiz-adv-x="919" d="M57 457l4 24q4 14 8.5 22.5t14.5 16.5l319 230q14 12 27 -2l27 -33q10 -14 -4 -25l-281 -227l184 -215q10 -14 -4 -25l-28 -24q-14 -10 -25 0l-227 225q-16 16 -15 33zM419 457l4 24q4 14 8.5 22.5t14.5 16.5l319 230q14 12 27 -2l27 -33q10 -14 -4 -25l-281 -227 l184 -215q10 -14 -4 -25l-28 -24q-14 -10 -25 0l-227 225q-16 16 -15 33z" />
<glyph unicode="&#xac;" horiz-adv-x="1048" d="M63 506l13 59q2 14 16 15h801q16 0 12 -15l-90 -459q-4 -14 -18 -14h-54q-16 0 -14 14l76 386h-729q-14 0 -13 14z" />
<glyph unicode="&#xad;" horiz-adv-x="563" d="M35 512l12 53q2 14 16 15h392q14 0 12 -15l-12 -53q-2 -14 -17 -14h-391q-14 0 -12 14z" />
<glyph unicode="&#xae;" horiz-adv-x="1658" d="M82 549q0 203 72.5 369.5t196.5 285.5t287 184.5t343 65.5q135 0 245.5 -40t188.5 -113.5t120 -181t42 -242.5q0 -203 -72.5 -370t-196.5 -286t-287 -184.5t-343 -65.5q-135 0 -245.5 40t-188.5 114t-120 181.5t-42 242.5zM174 551q0 -121 37 -213t105.5 -154.5 t164.5 -94.5t215 -32q162 0 306.5 60.5t251 170t169 259t62.5 327.5q0 121 -37 213.5t-105.5 155t-165 94t-214.5 31.5q-164 0 -307.5 -60.5t-250 -170t-169 -259t-62.5 -327.5zM492 303l163 807q4 18 12.5 29.5t32.5 15.5q31 6 88.5 11t104.5 5q121 0 186.5 -50t65.5 -160 q0 -68 -21.5 -120.5t-58.5 -90t-84 -61t-98 -32.5l125 -348q8 -25 -19 -24h-35q-16 0 -22 5t-10 17l-123 344h-152l-69 -348q-4 -12 -9.5 -15t-15.5 -3h-45q-18 0 -16 18zM664 727h120q141 0 207 66q29 29 45.5 68.5t16.5 86.5q0 78 -41 111t-127 33q-39 0 -74 -2.5 t-53 -6.5q-16 -4 -20.5 -9t-8.5 -21z" />
<glyph unicode="&#xaf;" horiz-adv-x="669" d="M213 1323l12 55q6 23 25 23h473q23 0 16 -25l-12 -55q-6 -23 -25 -23h-473q-23 0 -16 25z" />
<glyph unicode="&#xb0;" horiz-adv-x="602" d="M156 1184q0 59 21.5 109t58 86t88 55.5t110.5 19.5q90 0 141.5 -49t51.5 -125q0 -127 -77 -203t-202 -76q-88 0 -140 52.5t-52 130.5zM244 1194q0 -53 32.5 -85t85.5 -32q78 0 127.5 50.5t49.5 138.5q0 51 -33 81.5t-86 30.5q-72 0 -124 -50t-52 -134z" />
<glyph unicode="&#xb1;" horiz-adv-x="1048" d="M-20 14l12 60q2 14 16 14h801q14 0 12 -14l-12 -60q-2 -14 -16 -14h-801q-14 0 -12 14zM113 700l12 60q2 14 16 14h357l69 356q2 14 17 15h57q16 0 14 -15l-69 -356h356q14 0 12 -14l-12 -60q-2 -14 -16 -14h-357l-69 -358q-2 -14 -17 -15h-57q-16 0 -14 15l69 358h-356 q-14 0 -12 14z" />
<glyph unicode="&#xb2;" horiz-adv-x="630" d="M152 952l8 43q18 106 63 166t146 96l98 35q66 23 93.5 56.5t27.5 99.5q0 45 -30 65.5t-103 20.5q-35 0 -72 -5t-66 -13q-20 -4 -22 12l-6 37q-2 16 14 20q35 10 78 15.5t94 5.5q92 0 147.5 -33t55.5 -119q0 -53 -14 -94q-31 -86 -160 -131l-98 -35q-43 -16 -71 -32.5 t-45.5 -37t-26.5 -47t-17 -61.5l-4 -19h336q14 0 12 -14l-8 -39q-2 -6 -5.5 -9t-13.5 -3h-393q-20 0 -18 20z" />
<glyph unicode="&#xb3;" horiz-adv-x="630" d="M178 967l12 34q4 16 17 13q33 -10 74 -17.5t79 -7.5q66 0 105 18.5t55 65.5q6 18 10.5 39t4.5 39q0 88 -152 88h-47q-14 0 -12 14l8 41q2 12 16 13h39q203 0 203 157q0 76 -115 76q-37 0 -71.5 -5t-63.5 -15q-14 -4 -16 6l-9 45q0 12 13 14q78 23 164 23q82 0 132 -31 t50 -102q0 -31 -7.5 -64t-24.5 -60.5t-45 -49t-67 -29.5v-4q45 -10 66.5 -40t21.5 -69q0 -37 -9 -76t-25 -71q-27 -51 -83.5 -71.5t-129.5 -20.5q-45 0 -93.5 7t-89.5 21q-16 6 -10 19z" />
<glyph unicode="&#xb4;" horiz-adv-x="579" d="M190 1270q-6 16 11 22l401 172q23 8 33 -12l22 -49q10 -18 1 -29.5t-27 -17.5l-398 -144q-16 -6 -22 7z" />
<glyph unicode="&#xb5;" horiz-adv-x="1146" d="M12 -348l260 1335q2 16 21 17h55q14 0 12 -17l-135 -700q-4 -20 -6 -41t-2 -39q0 -82 54.5 -115t152.5 -33q59 0 114.5 15.5t101.5 51.5t80 92t50 140l123 629q2 16 20 17h56q14 0 12 -17l-160 -825q-6 -29 -6 -51q0 -29 15.5 -40.5t41.5 -11.5q18 0 43 5.5t37 9.5 q16 6 17 -11l2 -32q2 -14 -13 -23q-49 -27 -118 -26q-53 0 -83 25.5t-30 72.5q0 10 1 20.5t3 20.5h-6q-47 -61 -121 -100t-190 -39q-98 0 -152.5 28.5t-77.5 77.5l-84 -436q-2 -16 -18 -17h-55q-16 0 -15 17z" />
<glyph unicode="&#xb6;" horiz-adv-x="1122" d="M170 999q0 94 35 173t98.5 135.5t151.5 88t194 31.5h533q29 0 22 -24l-10 -45q-4 -12 -8 -15.5t-21 -3.5h-157l-306 -1566q-6 -20 -26 -21h-55q-23 0 -17 21l305 1566h-211l-305 -1566q-6 -20 -26 -21h-54q-25 0 -18 21l180 923q-66 4 -121 24.5t-96 59.5t-64.5 93.5 t-23.5 125.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="387" d="M86 545q0 10 2 24.5t10 57.5q12 68 82 67h23q61 0 61 -41q0 -6 -2 -21.5t-12 -60.5q-8 -35 -25.5 -51t-58.5 -16h-21q-59 0 -59 41z" />
<glyph unicode="&#xb8;" horiz-adv-x="471" d="M-145 -418l12 39q6 14 20 8q33 -12 72 -19t70 -7q72 0 109.5 26.5t37.5 106.5q0 59 -70 59q-29 0 -56 -4t-50 -8q-16 -2 -25 8l-12 16q-8 10 0 27l78 152q6 12 22 12h41q27 0 13 -25l-60 -116q20 6 44 9t46 3q53 0 85 -32t32 -91q0 -47 -11 -86t-39 -66.5t-75 -44 t-119 -16.5q-35 0 -76.5 7t-76.5 20q-18 6 -12 22z" />
<glyph unicode="&#xb9;" horiz-adv-x="630" d="M141 944l6 41q2 12 15 12h170l100 533h-8l-176 -103q-14 -8 -21 3l-24 38q-8 12 4 19l186 106q14 8 24.5 10.5t26.5 2.5h66q12 0 17 -3.5t3 -15.5l-114 -590h174q14 0 12 -12l-6 -41q-2 -12 -14 -12h-428q-14 0 -13 12z" />
<glyph unicode="&#xba;" horiz-adv-x="645" d="M117 776q0 29 8 79t18 110q14 76 37 126t56.5 79.5t79.5 42t108 12.5q199 0 199 -166q0 -29 -6.5 -79t-20.5 -110q-16 -74 -38.5 -124t-56.5 -79.5t-79.5 -43t-106.5 -13.5q-199 0 -198 166zM199 782q0 -102 125 -102q88 0 130 46t64 159q10 49 16.5 88t6.5 71 q0 59 -32 85t-93 26q-43 0 -75 -9t-54.5 -32.5t-38 -63.5t-27.5 -100q-10 -47 -16 -91t-6 -77z" />
<glyph unicode="&#xbb;" horiz-adv-x="919" d="M53 233q-10 14 4 25l281 227l-184 215q-10 14 4 25l28 24q14 10 25 0l227 -225q16 -16 15 -33l-4 -24q-4 -14 -8.5 -22.5t-14.5 -16.5l-319 -230q-14 -12 -27 2zM415 233q-10 14 4 25l281 227l-184 215q-10 14 4 25l28 24q14 10 25 0l227 -225q16 -16 15 -33l-4 -24 q-4 -14 -8.5 -22.5t-14.5 -16.5l-319 -230q-14 -12 -27 2z" />
<glyph unicode="&#xbc;" horiz-adv-x="1527" d="M111 662l6 40q2 12 14 13h170l100 532h-8l-176 -102q-14 -8 -20 2l-25 39q-8 12 4 18l186 107q14 8 24.5 10t27.5 2h65q12 0 17.5 -3t3.5 -15l-115 -590h174q14 0 12 -13l-6 -40q-2 -12 -14 -13h-428q-14 0 -12 13zM281 2l991 1331q8 10 22 10h56q23 0 8 -20l-991 -1331 q-8 -10 -23 -10h-55q-23 0 -8 20zM883 195l6 30q4 12 9 19.5t15 19.5l322 377q14 16 23.5 19.5t29.5 3.5h74q27 0 20 -23l-79 -399h77q14 0 13 -11l-11 -45q-2 -10 -14 -10h-78l-33 -164q-2 -12 -16 -12h-55q-14 0 -10 14l32 162h-309q-20 0 -16 19zM981 242h240l69 348h-10 z" />
<glyph unicode="&#xbd;" horiz-adv-x="1519" d="M104 663l6 41q2 12 15 12h170l100 533h-8l-176 -103q-14 -8 -21 3l-24 38q-8 12 4 19l186 106q14 8 24.5 10.5t26.5 2.5h66q12 0 17 -3.5t3 -15.5l-114 -590h174q14 0 12 -12l-6 -41q-2 -12 -14 -12h-428q-14 0 -13 12zM273 2l991 1331q8 10 22 10h56q23 0 8 -20 l-991 -1331q-8 -10 -23 -10h-55q-23 0 -8 20zM887 22l8 43q18 106 63 166t146 96l98 35q66 23 93.5 56.5t27.5 99.5q0 45 -30 65.5t-103 20.5q-35 0 -72 -5t-66 -13q-20 -4 -22 12l-6 37q-2 16 14 20q35 10 78 15.5t94 5.5q92 0 147.5 -33t55.5 -119q0 -53 -14 -94 q-31 -86 -160 -131l-98 -35q-43 -16 -71 -32.5t-45.5 -37t-26.5 -47t-17 -61.5l-4 -19h336q14 0 12 -14l-8 -39q-2 -6 -5.5 -9t-13.5 -3h-393q-20 0 -18 20z" />
<glyph unicode="&#xbe;" horiz-adv-x="1544" d="M158 702l12 35q4 16 16 13q33 -10 74 -17.5t80 -7.5q66 0 104.5 18.5t55.5 65.5q6 18 10 38.5t4 39.5q0 88 -152 88h-47q-14 0 -12 14l8 41q2 12 17 12h39q203 0 202 158q0 76 -114 76q-37 0 -72 -5t-64 -16q-14 -4 -16 7l-8 45q0 12 12 14q78 23 164 22q82 0 132 -30.5 t50 -102.5q0 -31 -7 -63.5t-24.5 -60t-45 -49t-66.5 -29.5v-4q45 -10 66.5 -40t21.5 -69q0 -37 -9 -76t-26 -71q-27 -51 -83 -72t-130 -21q-45 0 -93 7.5t-89 21.5q-16 6 -10 18zM252 2l991 1331q8 10 23 10h55q23 0 8 -20l-991 -1331q-8 -10 -23 -10h-55q-23 0 -8 20z M854 195l6 30q4 12 9 19.5t16 19.5l321 377q14 16 23.5 19.5t30.5 3.5h73q27 0 21 -23l-80 -399h78q14 0 12 -11l-10 -45q-2 -10 -15 -10h-77l-33 -164q-2 -12 -17 -12h-55q-14 0 -10 14l33 162h-310q-20 0 -16 19zM952 242h240l70 348h-11z" />
<glyph unicode="&#xbf;" horiz-adv-x="811" d="M-65 -83q0 72 17.5 127t52 99t85 81t117.5 71l80 43q57 31 97 66t68 78t45 99t28 130q2 12 6 16.5t20 4.5h35q23 0 20 -21q-14 -90 -34.5 -156.5t-51 -117.5t-75.5 -90t-107 -74l-82 -45q-117 -61 -171 -127t-54 -172q0 -94 58.5 -139.5t189.5 -45.5q66 0 140.5 14.5 t150.5 37.5q20 6 26 -13l10 -39q6 -20 -16 -28q-76 -27 -155.5 -41t-157.5 -14q-342 0 -342 256zM512 877q0 18 13 74q10 41 27.5 56t53.5 15h17q53 0 53 -45q0 -6 -2 -21t-8 -50q-8 -41 -27.5 -57.5t-52.5 -16.5h-18q-55 0 -56 45z" />
<glyph unicode="&#xc0;" horiz-adv-x="1130" d="M-18 23q164 362 318.5 676.5t307.5 592.5q10 20 23.5 26.5t34.5 6.5h55q16 0 29.5 -7t17.5 -30q20 -115 41.5 -262t40 -313t35 -341t28.5 -347q2 -25 -22 -25h-45q-27 0 -27 25q-6 102 -13 206.5t-18 208.5h-520l-190 -417q-4 -10 -11.5 -16.5t-23.5 -6.5h-47 q-27 0 -14 23zM307 522h473q-20 209 -42.5 395.5t-45.5 325.5h-22q-98 -186 -186.5 -361t-176.5 -360zM347.5 1586q-9.5 11 0.5 30l23 49q10 20 32 12l402 -172q16 -6 10 -22l-21 -51q-6 -12 -22 -7l-397 144q-18 6 -27.5 17z" />
<glyph unicode="&#xc1;" horiz-adv-x="1130" d="M-18 23q164 362 318.5 676.5t307.5 592.5q10 20 23.5 26.5t34.5 6.5h55q16 0 29.5 -7t17.5 -30q20 -115 41.5 -262t40 -313t35 -341t28.5 -347q2 -25 -22 -25h-45q-27 0 -27 25q-6 102 -13 206.5t-18 208.5h-520l-190 -417q-4 -10 -11.5 -16.5t-23.5 -6.5h-47 q-27 0 -14 23zM307 522h473q-20 209 -42.5 395.5t-45.5 325.5h-22q-98 -186 -186.5 -361t-176.5 -360zM550 1483q-6 16 11 22l401 172q23 8 33 -12l22 -49q10 -18 1 -29.5t-27 -17.5l-398 -144q-16 -6 -22 7z" />
<glyph unicode="&#xc2;" horiz-adv-x="1130" d="M-18 23q164 362 318.5 676.5t307.5 592.5q10 20 23.5 26.5t34.5 6.5h55q16 0 29.5 -7t17.5 -30q20 -115 41.5 -262t40 -313t35 -341t28.5 -347q2 -25 -22 -25h-45q-27 0 -27 25q-6 102 -13 206.5t-18 208.5h-520l-190 -417q-4 -10 -11.5 -16.5t-23.5 -6.5h-47 q-27 0 -14 23zM307 522h473q-20 209 -42.5 395.5t-45.5 325.5h-22q-98 -186 -186.5 -361t-176.5 -360zM426 1475q0 10 11 18l254 227q20 20 43 21h38q14 0 21.5 -3t17.5 -18l191 -239q6 -10 6 -15q0 -10 -12 -18l-35 -27q-10 -6 -17 -6q-10 0 -18 12l-172 207h-12l-238 -207 q-14 -10 -20 -10t-15 8l-34 35q-8 6 -9 15z" />
<glyph unicode="&#xc3;" horiz-adv-x="1130" d="M-18 23q164 362 318.5 676.5t307.5 592.5q10 20 23.5 26.5t34.5 6.5h55q16 0 29.5 -7t17.5 -30q20 -115 41.5 -262t40 -313t35 -341t28.5 -347q2 -25 -22 -25h-45q-27 0 -27 25q-6 102 -13 206.5t-18 208.5h-520l-190 -417q-4 -10 -11.5 -16.5t-23.5 -6.5h-47 q-27 0 -14 23zM307 522h473q-20 209 -42.5 395.5t-45.5 325.5h-22q-98 -186 -186.5 -361t-176.5 -360zM397 1522q51 82 108.5 118.5t110.5 36.5q63 0 121 -49l43 -37q41 -35 76 -35t71.5 31t71.5 78q12 14 29 2l33 -27q16 -12 2 -30q-51 -72 -102.5 -113t-112.5 -41 q-66 0 -123 49l-43 37q-23 18 -39.5 29.5t-38.5 11.5q-35 0 -65.5 -24.5t-69.5 -79.5q-14 -18 -31 -9l-37 27q-14 10 -4 25z" />
<glyph unicode="&#xc4;" horiz-adv-x="1130" d="M-18 23q164 362 318.5 676.5t307.5 592.5q10 20 23.5 26.5t34.5 6.5h55q16 0 29.5 -7t17.5 -30q20 -115 41.5 -262t40 -313t35 -341t28.5 -347q2 -25 -22 -25h-45q-27 0 -27 25q-6 102 -13 206.5t-18 208.5h-520l-190 -417q-4 -10 -11.5 -16.5t-23.5 -6.5h-47 q-27 0 -14 23zM307 522h473q-20 209 -42.5 395.5t-45.5 325.5h-22q-98 -186 -186.5 -361t-176.5 -360zM473 1524q0 16 10 67q14 74 88 74h31q61 0 62 -51q0 -20 -11 -66q-10 -41 -30.5 -56t-61.5 -15h-24q-63 0 -64 47zM803 1524q0 16 10 67q14 74 88 74h31q61 0 61 -51 q0 -20 -10 -66q-10 -41 -30.5 -56t-61.5 -15h-25q-63 0 -63 47z" />
<glyph unicode="&#xc5;" horiz-adv-x="1130" d="M-18 23q164 362 318.5 676.5t307.5 592.5q10 20 23.5 26.5t34.5 6.5h55q16 0 29.5 -7t17.5 -30q20 -115 41.5 -262t40 -313t35 -341t28.5 -347q2 -25 -22 -25h-45q-27 0 -27 25q-6 102 -13 206.5t-18 208.5h-520l-190 -417q-4 -10 -11.5 -16.5t-23.5 -6.5h-47 q-27 0 -14 23zM307 522h473q-20 209 -42.5 395.5t-45.5 325.5h-22q-98 -186 -186.5 -361t-176.5 -360zM547 1532q0 94 58.5 150.5t148.5 56.5q80 0 122 -43t42 -109q0 -53 -17.5 -92t-46.5 -66.5t-64.5 -41t-74.5 -13.5q-78 0 -123 43t-45 115zM627 1544q0 -41 24.5 -65.5 t69.5 -24.5q49 0 83 32t34 85q0 41 -26 65.5t-69 24.5q-49 0 -82.5 -32t-33.5 -85z" />
<glyph unicode="&#xc6;" horiz-adv-x="1601" d="M-39 23q242 373 457 688t403 571q23 29 40.5 36t47.5 7h684q23 0 19 -20l-8 -43q-4 -12 -8.5 -15.5t-16.5 -3.5h-578l-98 -516h524q23 0 19 -20l-10 -45q-4 -10 -8.5 -14.5t-16.5 -4.5h-526l-64 -352q-12 -74 -12 -121q0 -86 121 -86h454q20 0 19 -21l-8 -43 q-4 -12 -8.5 -16t-16.5 -4h-455q-100 0 -150 38t-50 122q0 29 4 65.5t12 83.5l23 131h-420l-271 -417q-8 -10 -15 -16.5t-23 -6.5h-56q-23 0 -8 23zM389 522h379l141 723h-16q-135 -182 -258 -360t-246 -363z" />
<glyph unicode="&#xc7;" horiz-adv-x="1007" d="M86 315q0 72 19.5 194t50.5 290q31 162 78 267t113.5 166.5t149.5 86t185 24.5q90 0 174 -25.5t143 -62.5q20 -12 11 -32l-19 -31q-8 -14 -16 -14t-19 6q-131 76 -276 76q-86 0 -153.5 -20.5t-121 -78t-92 -156.5t-67.5 -257q-27 -145 -42.5 -252t-15.5 -166 q0 -72 15.5 -122t50.5 -82t93.5 -46t141.5 -14q68 0 149 16t153 45q18 8 24 -12l8 -37q4 -10 3 -17.5t-11 -11.5q-70 -29 -152.5 -46t-156.5 -21l-4 -9l-60 -116q20 6 44 9t46 3q53 0 85 -32t32 -91q0 -47 -11 -86t-39 -66.5t-75 -44t-118 -16.5q-35 0 -77 7t-77 20 q-18 6 -12 22l12 39q6 14 20 8q33 -12 72 -19t70 -7q72 0 109.5 26.5t37.5 106.5q0 59 -69 59q-29 0 -56.5 -4t-50.5 -8q-16 -2 -25 8l-12 16q-8 10 0 27l76 150q-180 10 -259 86.5t-79 244.5z" />
<glyph unicode="&#xc8;" horiz-adv-x="954" d="M68 174q0 23 4 57.5t12 77.5q37 205 76 399.5t86 395.5q14 59 31.5 101t47 68.5t75.5 39t114 12.5h432q23 0 19 -20l-9 -43q-4 -12 -8 -15.5t-16 -3.5h-430q-74 0 -108 -31.5t-54 -119.5q-16 -74 -34.5 -157t-45.5 -208h520q23 0 19 -20l-11 -45q-4 -10 -8 -14.5 t-16 -4.5h-522q-25 -123 -39.5 -211t-24.5 -141q-12 -78 -12 -119q0 -47 27.5 -67.5t89.5 -20.5h454q20 0 19 -21l-8 -43q-4 -12 -8.5 -16t-16.5 -4h-459q-109 0 -152.5 45t-43.5 129zM388.5 1586q-9.5 11 0.5 30l23 49q10 20 32 12l402 -172q16 -6 10 -22l-21 -51 q-6 -12 -22 -7l-397 144q-18 6 -27.5 17z" />
<glyph unicode="&#xc9;" horiz-adv-x="954" d="M68 174q0 23 4 57.5t12 77.5q37 205 76 399.5t86 395.5q14 59 31.5 101t47 68.5t75.5 39t114 12.5h432q23 0 19 -20l-9 -43q-4 -12 -8 -15.5t-16 -3.5h-430q-74 0 -108 -31.5t-54 -119.5q-16 -74 -34.5 -157t-45.5 -208h520q23 0 19 -20l-11 -45q-4 -10 -8 -14.5 t-16 -4.5h-522q-25 -123 -39.5 -211t-24.5 -141q-12 -78 -12 -119q0 -47 27.5 -67.5t89.5 -20.5h454q20 0 19 -21l-8 -43q-4 -12 -8.5 -16t-16.5 -4h-459q-109 0 -152.5 45t-43.5 129zM479 1483q-6 16 11 22l401 172q23 8 33 -12l22 -49q10 -18 1 -29.5t-27 -17.5l-398 -144 q-16 -6 -22 7z" />
<glyph unicode="&#xca;" horiz-adv-x="954" d="M68 174q0 23 4 57.5t12 77.5q37 205 76 399.5t86 395.5q14 59 31.5 101t47 68.5t75.5 39t114 12.5h432q23 0 19 -20l-9 -43q-4 -12 -8 -15.5t-16 -3.5h-430q-74 0 -108 -31.5t-54 -119.5q-16 -74 -34.5 -157t-45.5 -208h520q23 0 19 -20l-11 -45q-4 -10 -8 -14.5 t-16 -4.5h-522q-25 -123 -39.5 -211t-24.5 -141q-12 -78 -12 -119q0 -47 27.5 -67.5t89.5 -20.5h454q20 0 19 -21l-8 -43q-4 -12 -8.5 -16t-16.5 -4h-459q-109 0 -152.5 45t-43.5 129zM387 1475q0 10 11 18l254 227q20 20 43 21h38q14 0 21.5 -3t17.5 -18l191 -239 q6 -10 6 -15q0 -10 -12 -18l-35 -27q-10 -6 -17 -6q-10 0 -18 12l-172 207h-12l-238 -207q-14 -10 -20 -10t-15 8l-34 35q-8 6 -9 15z" />
<glyph unicode="&#xcb;" horiz-adv-x="954" d="M68 174q0 23 4 57.5t12 77.5q37 205 76 399.5t86 395.5q14 59 31.5 101t47 68.5t75.5 39t114 12.5h432q23 0 19 -20l-9 -43q-4 -12 -8 -15.5t-16 -3.5h-430q-74 0 -108 -31.5t-54 -119.5q-16 -74 -34.5 -157t-45.5 -208h520q23 0 19 -20l-11 -45q-4 -10 -8 -14.5 t-16 -4.5h-522q-25 -123 -39.5 -211t-24.5 -141q-12 -78 -12 -119q0 -47 27.5 -67.5t89.5 -20.5h454q20 0 19 -21l-8 -43q-4 -12 -8.5 -16t-16.5 -4h-459q-109 0 -152.5 45t-43.5 129zM448 1524q0 16 10 67q14 74 88 74h31q61 0 62 -51q0 -20 -11 -66q-10 -41 -30.5 -56 t-61.5 -15h-24q-63 0 -64 47zM778 1524q0 16 10 67q14 74 88 74h31q61 0 61 -51q0 -20 -10 -66q-10 -41 -30.5 -56t-61.5 -15h-25q-63 0 -63 47z" />
<glyph unicode="&#xcc;" horiz-adv-x="413" d="M34.5 1586q-9.5 11 0.5 30l23 49q10 20 32 12l402 -172q16 -6 10 -22l-21 -51q-6 -12 -22 -7l-397 144q-18 6 -27.5 17zM35 18l250 1289q2 18 22 18h53q20 0 19 -18l-250 -1289q-2 -18 -23 -18h-53q-23 0 -18 18z" />
<glyph unicode="&#xcd;" horiz-adv-x="413" d="M35 18l250 1289q2 18 22 18h53q20 0 19 -18l-250 -1289q-2 -18 -23 -18h-53q-23 0 -18 18zM204 1483q-6 16 11 22l401 172q23 8 33 -12l22 -49q10 -18 1 -29.5t-27 -17.5l-398 -144q-16 -6 -22 7z" />
<glyph unicode="&#xce;" horiz-adv-x="413" d="M35 18l250 1289q2 18 22 18h53q20 0 19 -18l-250 -1289q-2 -18 -23 -18h-53q-23 0 -18 18zM73 1475q0 10 11 18l254 227q20 20 43 21h38q14 0 21.5 -3t17.5 -18l191 -239q6 -10 6 -15q0 -10 -12 -18l-35 -27q-10 -6 -17 -6q-10 0 -18 12l-172 207h-12l-238 -207 q-14 -10 -20 -10t-15 8l-34 35q-8 6 -9 15z" />
<glyph unicode="&#xcf;" horiz-adv-x="413" d="M35 18l250 1289q2 18 22 18h53q20 0 19 -18l-250 -1289q-2 -18 -23 -18h-53q-23 0 -18 18zM121 1524q0 16 10 67q14 74 88 74h31q61 0 62 -51q0 -20 -11 -66q-10 -41 -30.5 -56t-61.5 -15h-24q-63 0 -64 47zM451 1524q0 16 10 67q14 74 88 74h31q61 0 61 -51 q0 -20 -10 -66q-10 -41 -30.5 -56t-61.5 -15h-25q-63 0 -63 47z" />
<glyph unicode="&#xd0;" horiz-adv-x="1173" d="M23 659l8 52q2 12 18 12h139l109 549q4 20 12 29.5t31 13.5q53 10 124 15t134 5q115 0 206 -18.5t153.5 -65.5t96 -125.5t33.5 -199.5q0 -53 -12 -151.5t-41 -213.5q-43 -182 -114.5 -291.5t-170 -170t-220 -80t-267.5 -19.5h-170q-25 0 -34 7t-5 30l119 608h-133 q-18 0 -16 14zM160 104q-6 -23 14 -22h123q145 0 250.5 24.5t181.5 82t126 152.5t85 237q23 98 37 190t14 152q0 96 -23.5 160.5t-71.5 102.5t-124 54t-180 16q-53 0 -99.5 -3t-74.5 -7q-16 -4 -26.5 -11t-14.5 -30l-96 -479h327q18 0 17 -14l-9 -52q-2 -12 -18 -12h-332z " />
<glyph unicode="&#xd1;" horiz-adv-x="1183" d="M27 23l239 1241q6 35 21.5 48t52.5 13h37q35 0 52 -14.5t28 -48.5l360 -1178h14l238 1221q4 20 25 20h49q20 0 14 -22l-239 -1242q-6 -35 -21.5 -48t-52.5 -13h-37q-35 0 -52.5 14.5t-27.5 48.5l-360 1178h-15l-237 -1221q-6 -20 -25 -20h-49q-20 0 -14 23zM430 1522 q51 82 108.5 118.5t110.5 36.5q63 0 121 -49l43 -37q41 -35 76 -35t71.5 31t71.5 78q12 14 29 2l33 -27q16 -12 2 -30q-51 -72 -102.5 -113t-112.5 -41q-66 0 -123 49l-43 37q-23 18 -39.5 29.5t-38.5 11.5q-35 0 -65.5 -24.5t-69.5 -79.5q-14 -18 -31 -9l-37 27 q-14 10 -4 25z" />
<glyph unicode="&#xd2;" horiz-adv-x="1161" d="M82 334q0 98 23.5 229t58.5 291q27 127 69.5 219t107.5 152.5t153 89t206 28.5q176 0 275.5 -79.5t99.5 -276.5q0 -100 -22.5 -231t-61.5 -285q-33 -133 -78 -225t-106.5 -151.5t-144 -86t-191.5 -26.5q-184 0 -286.5 77.5t-102.5 274.5zM180 348q0 -143 64.5 -212.5 t228.5 -69.5q86 0 151.5 20t114.5 69.5t87 131.5t71 205q18 68 32.5 136t25 132.5t16.5 118t6 90.5q0 156 -65.5 223.5t-219.5 67.5q-90 0 -158.5 -23t-121 -74t-90 -133t-64.5 -199q-35 -145 -56.5 -272t-21.5 -211zM460.5 1586q-9.5 11 0.5 30l23 49q10 20 32 12l402 -172 q16 -6 10 -22l-21 -51q-6 -12 -22 -7l-397 144q-18 6 -27.5 17z" />
<glyph unicode="&#xd3;" horiz-adv-x="1161" d="M82 334q0 98 23.5 229t58.5 291q27 127 69.5 219t107.5 152.5t153 89t206 28.5q176 0 275.5 -79.5t99.5 -276.5q0 -100 -22.5 -231t-61.5 -285q-33 -133 -78 -225t-106.5 -151.5t-144 -86t-191.5 -26.5q-184 0 -286.5 77.5t-102.5 274.5zM180 348q0 -143 64.5 -212.5 t228.5 -69.5q86 0 151.5 20t114.5 69.5t87 131.5t71 205q18 68 32.5 136t25 132.5t16.5 118t6 90.5q0 156 -65.5 223.5t-219.5 67.5q-90 0 -158.5 -23t-121 -74t-90 -133t-64.5 -199q-35 -145 -56.5 -272t-21.5 -211zM550 1483q-6 16 11 22l401 172q23 8 33 -12l22 -49 q10 -18 1 -29.5t-27 -17.5l-398 -144q-16 -6 -22 7z" />
<glyph unicode="&#xd4;" horiz-adv-x="1161" d="M82 334q0 98 23.5 229t58.5 291q27 127 69.5 219t107.5 152.5t153 89t206 28.5q176 0 275.5 -79.5t99.5 -276.5q0 -100 -22.5 -231t-61.5 -285q-33 -133 -78 -225t-106.5 -151.5t-144 -86t-191.5 -26.5q-184 0 -286.5 77.5t-102.5 274.5zM180 348q0 -143 64.5 -212.5 t228.5 -69.5q86 0 151.5 20t114.5 69.5t87 131.5t71 205q18 68 32.5 136t25 132.5t16.5 118t6 90.5q0 156 -65.5 223.5t-219.5 67.5q-90 0 -158.5 -23t-121 -74t-90 -133t-64.5 -199q-35 -145 -56.5 -272t-21.5 -211zM428 1475q0 10 11 18l254 227q20 20 43 21h38 q14 0 21.5 -3t17.5 -18l191 -239q6 -10 6 -15q0 -10 -12 -18l-35 -27q-10 -6 -17 -6q-10 0 -18 12l-172 207h-12l-238 -207q-14 -10 -20 -10t-15 8l-34 35q-8 6 -9 15z" />
<glyph unicode="&#xd5;" horiz-adv-x="1161" d="M82 334q0 98 23.5 229t58.5 291q27 127 69.5 219t107.5 152.5t153 89t206 28.5q176 0 275.5 -79.5t99.5 -276.5q0 -100 -22.5 -231t-61.5 -285q-33 -133 -78 -225t-106.5 -151.5t-144 -86t-191.5 -26.5q-184 0 -286.5 77.5t-102.5 274.5zM180 348q0 -143 64.5 -212.5 t228.5 -69.5q86 0 151.5 20t114.5 69.5t87 131.5t71 205q18 68 32.5 136t25 132.5t16.5 118t6 90.5q0 156 -65.5 223.5t-219.5 67.5q-90 0 -158.5 -23t-121 -74t-90 -133t-64.5 -199q-35 -145 -56.5 -272t-21.5 -211zM418 1522q51 82 108.5 118.5t110.5 36.5q63 0 121 -49 l43 -37q41 -35 76 -35t71.5 31t71.5 78q12 14 29 2l33 -27q16 -12 2 -30q-51 -72 -102.5 -113t-112.5 -41q-66 0 -123 49l-43 37q-23 18 -39.5 29.5t-38.5 11.5q-35 0 -65.5 -24.5t-69.5 -79.5q-14 -18 -31 -9l-37 27q-14 10 -4 25z" />
<glyph unicode="&#xd6;" horiz-adv-x="1161" d="M82 334q0 98 23.5 229t58.5 291q27 127 69.5 219t107.5 152.5t153 89t206 28.5q176 0 275.5 -79.5t99.5 -276.5q0 -100 -22.5 -231t-61.5 -285q-33 -133 -78 -225t-106.5 -151.5t-144 -86t-191.5 -26.5q-184 0 -286.5 77.5t-102.5 274.5zM180 348q0 -143 64.5 -212.5 t228.5 -69.5q86 0 151.5 20t114.5 69.5t87 131.5t71 205q18 68 32.5 136t25 132.5t16.5 118t6 90.5q0 156 -65.5 223.5t-219.5 67.5q-90 0 -158.5 -23t-121 -74t-90 -133t-64.5 -199q-35 -145 -56.5 -272t-21.5 -211zM489 1524q0 16 10 67q14 74 88 74h31q61 0 62 -51 q0 -20 -11 -66q-10 -41 -30.5 -56t-61.5 -15h-24q-63 0 -64 47zM819 1524q0 16 10 67q14 74 88 74h31q61 0 61 -51q0 -20 -10 -66q-10 -41 -30.5 -56t-61.5 -15h-25q-63 0 -63 47z" />
<glyph unicode="&#xd7;" horiz-adv-x="1048" d="M123 242q-10 12 4 24l311 260l-205 254q-10 12 3 25l49 41q14 10 22 -2l207 -254l315 264q10 10 25 -2l37 -43q12 -12 -4 -25l-316 -266l205 -256q10 -12 -2 -24l-49 -41q-14 -10 -23 2l-206 258l-314 -262q-12 -12 -24 2z" />
<glyph unicode="&#xd8;" horiz-adv-x="1159" d="M63.5 -140.5q-2.5 5.5 4.5 15.5l110 188q-47 41 -70.5 109t-23.5 166q0 94 21.5 223t56.5 293q27 127 69.5 219t107 152.5t153 89t206.5 28.5q123 0 215 -40l113 190q8 16 27 8l43 -24q8 -4 10 -9.5t-4 -15.5l-119 -199q90 -86 90 -266q0 -100 -22.5 -231t-61.5 -285 q-33 -133 -78 -225t-106.5 -151.5t-144 -86t-191.5 -26.5q-66 0 -120 8t-97 26l-109 -182q-8 -16 -26 -8l-43 24q-8 4 -10.5 9.5zM178 348q0 -66 11.5 -115t39.5 -83l635 1069q-70 41 -174 41q-90 0 -158.5 -23t-121 -74t-90 -133t-64.5 -199q-35 -145 -56.5 -272 t-21.5 -211zM299 96q63 -30 172 -30q86 0 151.5 20t114.5 69.5t87 131.5t71 205q18 68 32.5 136t25 132.5t16.5 118t6 90.5q0 119 -47 190z" />
<glyph unicode="&#xd9;" horiz-adv-x="1140" d="M78 281q0 61 12 129.5t25 130.5l147 766q2 18 23 18h55q20 0 18 -18l-170 -869q-10 -49 -13 -88t-3 -65q0 -66 23.5 -108t63.5 -66.5t92 -34.5t110 -10q88 0 154.5 14t114.5 55t81 114t55 189l166 869q2 18 23 18h55q20 0 18 -18l-170 -881q-25 -127 -61.5 -213t-95 -137 t-143.5 -72.5t-203 -21.5q-82 0 -152 15t-120 50t-77.5 92.5t-27.5 141.5zM431.5 1586q-9.5 11 0.5 30l23 49q10 20 32 12l402 -172q16 -6 10 -22l-21 -51q-6 -12 -22 -7l-397 144q-18 6 -27.5 17z" />
<glyph unicode="&#xda;" horiz-adv-x="1140" d="M78 281q0 61 12 129.5t25 130.5l147 766q2 18 23 18h55q20 0 18 -18l-170 -869q-10 -49 -13 -88t-3 -65q0 -66 23.5 -108t63.5 -66.5t92 -34.5t110 -10q88 0 154.5 14t114.5 55t81 114t55 189l166 869q2 18 23 18h55q20 0 18 -18l-170 -881q-25 -127 -61.5 -213t-95 -137 t-143.5 -72.5t-203 -21.5q-82 0 -152 15t-120 50t-77.5 92.5t-27.5 141.5zM542 1483q-6 16 11 22l401 172q23 8 33 -12l22 -49q10 -18 1 -29.5t-27 -17.5l-398 -144q-16 -6 -22 7z" />
<glyph unicode="&#xdb;" horiz-adv-x="1140" d="M78 281q0 61 12 129.5t25 130.5l147 766q2 18 23 18h55q20 0 18 -18l-170 -869q-10 -49 -13 -88t-3 -65q0 -66 23.5 -108t63.5 -66.5t92 -34.5t110 -10q88 0 154.5 14t114.5 55t81 114t55 189l166 869q2 18 23 18h55q20 0 18 -18l-170 -881q-25 -127 -61.5 -213t-95 -137 t-143.5 -72.5t-203 -21.5q-82 0 -152 15t-120 50t-77.5 92.5t-27.5 141.5zM430 1475q0 10 11 18l254 227q20 20 43 21h38q14 0 21.5 -3t17.5 -18l191 -239q6 -10 6 -15q0 -10 -12 -18l-35 -27q-10 -6 -17 -6q-10 0 -18 12l-172 207h-12l-238 -207q-14 -10 -20 -10t-15 8 l-34 35q-8 6 -9 15z" />
<glyph unicode="&#xdc;" horiz-adv-x="1140" d="M78 281q0 61 12 129.5t25 130.5l147 766q2 18 23 18h55q20 0 18 -18l-170 -869q-10 -49 -13 -88t-3 -65q0 -66 23.5 -108t63.5 -66.5t92 -34.5t110 -10q88 0 154.5 14t114.5 55t81 114t55 189l166 869q2 18 23 18h55q20 0 18 -18l-170 -881q-25 -127 -61.5 -213t-95 -137 t-143.5 -72.5t-203 -21.5q-82 0 -152 15t-120 50t-77.5 92.5t-27.5 141.5zM463 1524q0 16 10 67q14 74 88 74h31q61 0 62 -51q0 -20 -11 -66q-10 -41 -30.5 -56t-61.5 -15h-24q-63 0 -64 47zM793 1524q0 16 10 67q14 74 88 74h31q61 0 61 -51q0 -20 -10 -66 q-10 -41 -30.5 -56t-61.5 -15h-25q-63 0 -63 47z" />
<glyph unicode="&#xdd;" horiz-adv-x="890" d="M145 1309q0 16 19 16h55q20 0 23 -16q16 -209 62 -397.5t104 -342.5h16q68 90 128 177.5t116.5 177.5t109.5 185t107 204q6 12 22 12h60q20 0 14 -16q-55 -109 -111.5 -211.5t-120 -204.5t-138 -208.5t-167.5 -225.5l-86 -441q-2 -18 -22 -18h-55q-20 0 -19 18l88 447 q-82 211 -132 413.5t-73 430.5zM424 1483q-6 16 11 22l401 172q23 8 33 -12l22 -49q10 -18 1 -29.5t-27 -17.5l-398 -144q-16 -6 -22 7z" />
<glyph unicode="&#xde;" horiz-adv-x="989" d="M27 18l256 1315q2 18 22 19h53q20 0 19 -19l-43 -207q80 12 172 13q90 0 163.5 -14.5t125 -48.5t79 -90t27.5 -142q0 -66 -18 -158q-41 -199 -175.5 -283t-351.5 -84h-178l-57 -301q-2 -18 -23 -18h-53q-23 0 -18 18zM195 403h163q197 0 293.5 69t132.5 222 q10 37 13.5 71t3.5 60q0 131 -73 180.5t-232 49.5q-55 0 -101.5 -5.5t-79.5 -13.5z" />
<glyph unicode="&#xdf;" horiz-adv-x="1007" d="M-20 -113q35 98 63.5 200.5t48.5 207.5l139 741q20 104 56.5 175t87.5 114t117.5 61.5t146.5 18.5q135 0 221 -63.5t86 -206.5q0 -147 -68.5 -253t-207.5 -141v-6q94 -12 157.5 -71.5t63.5 -180.5q0 -63 -10.5 -119.5t-26.5 -107.5q-47 -139 -139 -206.5t-254 -67.5 q-53 0 -106.5 6t-94.5 18q-12 4 -14 9t2 16l12 41q6 20 25 14q35 -10 83 -17.5t91 -7.5q129 0 203.5 56.5t109.5 173.5q10 33 18.5 87t8.5 101q0 106 -65.5 160.5t-215.5 54.5h-49q-27 0 -20 21l8 41q2 18 24 18h21q90 0 157.5 26.5t111.5 74t65.5 113t21.5 142.5 q0 113 -62.5 154t-160.5 41q-143 0 -214 -72.5t-97 -214.5l-142 -737q-23 -117 -53.5 -224.5t-65.5 -191.5q-6 -16 -24 -16h-47q-20 0 -12 18z" />
<glyph unicode="&#xe0;" horiz-adv-x="933" d="M33 174q0 47 11 102.5t28 108.5q14 43 38.5 79t59.5 58q84 57 264 58h98q37 0 82 -3.5t80 -9.5l15 74q23 109 22 162q0 78 -50 108.5t-165 30.5q-61 0 -132 -12t-130 -31q-23 -6 -25 10l-6 45q-2 18 15 23q68 20 143.5 32.5t153.5 12.5q55 0 106 -9t91 -32t63.5 -61.5 t25.5 -96.5q0 -33 -9 -91t-19 -118l-113 -598q-2 -16 -18 -16h-48q-20 0 -16 20l12 89h-4q-45 -61 -116.5 -94t-182.5 -33h-22q-139 0 -195.5 49t-56.5 143zM123 186q0 -66 42 -95.5t136 -29.5h18q139 0 223.5 69t106.5 194l31 165q-35 4 -73 7.5t-83 3.5h-114 q-131 0 -191 -45q-23 -16 -38 -41t-25 -56q-12 -39 -22.5 -85t-10.5 -87zM306.5 1373q-9.5 11 0.5 30l23 49q10 20 33 12l401 -172q16 -6 10 -22l-20 -51q-6 -12 -23 -7l-397 144q-18 6 -27.5 17z" />
<glyph unicode="&#xe1;" horiz-adv-x="933" d="M33 174q0 47 11 102.5t28 108.5q14 43 38.5 79t59.5 58q84 57 264 58h98q37 0 82 -3.5t80 -9.5l15 74q23 109 22 162q0 78 -50 108.5t-165 30.5q-61 0 -132 -12t-130 -31q-23 -6 -25 10l-6 45q-2 18 15 23q68 20 143.5 32.5t153.5 12.5q55 0 106 -9t91 -32t63.5 -61.5 t25.5 -96.5q0 -33 -9 -91t-19 -118l-113 -598q-2 -16 -18 -16h-48q-20 0 -16 20l12 89h-4q-45 -61 -116.5 -94t-182.5 -33h-22q-139 0 -195.5 49t-56.5 143zM123 186q0 -66 42 -95.5t136 -29.5h18q139 0 223.5 69t106.5 194l31 165q-35 4 -73 7.5t-83 3.5h-114 q-131 0 -191 -45q-23 -16 -38 -41t-25 -56q-12 -39 -22.5 -85t-10.5 -87zM399 1270q-6 16 11 22l401 172q23 8 33 -12l22 -49q10 -18 1 -29.5t-27 -17.5l-398 -144q-16 -6 -22 7z" />
<glyph unicode="&#xe2;" horiz-adv-x="933" d="M33 174q0 47 11 102.5t28 108.5q14 43 38.5 79t59.5 58q84 57 264 58h98q37 0 82 -3.5t80 -9.5l15 74q23 109 22 162q0 78 -50 108.5t-165 30.5q-61 0 -132 -12t-130 -31q-23 -6 -25 10l-6 45q-2 18 15 23q68 20 143.5 32.5t153.5 12.5q55 0 106 -9t91 -32t63.5 -61.5 t25.5 -96.5q0 -33 -9 -91t-19 -118l-113 -598q-2 -16 -18 -16h-48q-20 0 -16 20l12 89h-4q-45 -61 -116.5 -94t-182.5 -33h-22q-139 0 -195.5 49t-56.5 143zM123 186q0 -66 42 -95.5t136 -29.5h18q139 0 223.5 69t106.5 194l31 165q-35 4 -73 7.5t-83 3.5h-114 q-131 0 -191 -45q-23 -16 -38 -41t-25 -56q-12 -39 -22.5 -85t-10.5 -87zM318 1245q-18 14 2 33l254 227q20 20 43 21h38q14 0 21.5 -3t17.5 -18l191 -239q12 -18 -6 -33l-35 -27q-20 -12 -35 6l-172 207h-12l-238 -207q-12 -10 -20 -9t-15 7z" />
<glyph unicode="&#xe3;" horiz-adv-x="933" d="M33 174q0 47 11 102.5t28 108.5q14 43 38.5 79t59.5 58q84 57 264 58h98q37 0 82 -3.5t80 -9.5l15 74q23 109 22 162q0 78 -50 108.5t-165 30.5q-61 0 -132 -12t-130 -31q-23 -6 -25 10l-6 45q-2 18 15 23q68 20 143.5 32.5t153.5 12.5q55 0 106 -9t91 -32t63.5 -61.5 t25.5 -96.5q0 -33 -9 -91t-19 -118l-113 -598q-2 -16 -18 -16h-48q-20 0 -16 20l12 89h-4q-45 -61 -116.5 -94t-182.5 -33h-22q-139 0 -195.5 49t-56.5 143zM123 186q0 -66 42 -95.5t136 -29.5h18q139 0 223.5 69t106.5 194l31 165q-35 4 -73 7.5t-83 3.5h-114 q-131 0 -191 -45q-23 -16 -38 -41t-25 -56q-12 -39 -22.5 -85t-10.5 -87zM286 1303q51 82 108.5 118.5t111.5 36.5q63 0 120 -49l43 -37q41 -35 76 -35t72 31t71 78q12 14 29 2l33 -27q16 -12 2 -30q-51 -72 -102.5 -113t-112.5 -41q-66 0 -123 49l-43 37q-23 18 -39 29.5 t-39 11.5q-35 0 -65.5 -24.5t-69.5 -79.5q-14 -18 -31 -9l-37 27q-14 10 -4 25z" />
<glyph unicode="&#xe4;" horiz-adv-x="933" d="M33 174q0 47 11 102.5t28 108.5q14 43 38.5 79t59.5 58q84 57 264 58h98q37 0 82 -3.5t80 -9.5l15 74q23 109 22 162q0 78 -50 108.5t-165 30.5q-61 0 -132 -12t-130 -31q-23 -6 -25 10l-6 45q-2 18 15 23q68 20 143.5 32.5t153.5 12.5q55 0 106 -9t91 -32t63.5 -61.5 t25.5 -96.5q0 -33 -9 -91t-19 -118l-113 -598q-2 -16 -18 -16h-48q-20 0 -16 20l12 89h-4q-45 -61 -116.5 -94t-182.5 -33h-22q-139 0 -195.5 49t-56.5 143zM123 186q0 -66 42 -95.5t136 -29.5h18q139 0 223.5 69t106.5 194l31 165q-35 4 -73 7.5t-83 3.5h-114 q-131 0 -191 -45q-23 -16 -38 -41t-25 -56q-12 -39 -22.5 -85t-10.5 -87zM350 1300q0 16 10 68q14 74 88 74h31q61 0 61 -51q0 -20 -10 -66q-10 -41 -30.5 -56.5t-61.5 -15.5h-25q-63 0 -63 47zM680 1300q0 16 10 68q14 74 88 74h31q61 0 61 -51q0 -20 -10 -66 q-10 -41 -30.5 -56.5t-61.5 -15.5h-25q-63 0 -63 47z" />
<glyph unicode="&#xe5;" horiz-adv-x="933" d="M33 174q0 47 11 102.5t28 108.5q14 43 38.5 79t59.5 58q84 57 264 58h98q37 0 82 -3.5t80 -9.5l15 74q23 109 22 162q0 78 -50 108.5t-165 30.5q-61 0 -132 -12t-130 -31q-23 -6 -25 10l-6 45q-2 18 15 23q68 20 143.5 32.5t153.5 12.5q55 0 106 -9t91 -32t63.5 -61.5 t25.5 -96.5q0 -33 -9 -91t-19 -118l-113 -598q-2 -16 -18 -16h-48q-20 0 -16 20l12 89h-4q-45 -61 -116.5 -94t-182.5 -33h-22q-139 0 -195.5 49t-56.5 143zM123 186q0 -66 42 -95.5t136 -29.5h18q139 0 223.5 69t106.5 194l31 165q-35 4 -73 7.5t-83 3.5h-114 q-131 0 -191 -45q-23 -16 -38 -41t-25 -56q-12 -39 -22.5 -85t-10.5 -87zM414 1313q0 94 58.5 150.5t148.5 56.5q80 0 122 -43t42 -109q0 -53 -17.5 -92t-46 -66.5t-64.5 -41t-75 -13.5q-78 0 -123 43t-45 115zM494 1325q0 -41 24.5 -65.5t69.5 -24.5q49 0 83 31.5t34 85.5 q0 41 -25.5 65.5t-68.5 24.5q-49 0 -83 -32t-34 -85z" />
<glyph unicode="&#xe6;" horiz-adv-x="1482" d="M33 170q0 53 11 116.5t30 116.5q16 47 42.5 81t66.5 55.5t95.5 31t131.5 9.5h122q37 0 82 -3.5t80 -9.5l11 56q10 53 18 99t8 81q0 78 -51 108.5t-166 30.5q-66 0 -133.5 -12t-126.5 -31q-23 -6 -25 10l-6 45q-2 18 15 23q68 20 145.5 32.5t155.5 12.5q45 0 86 -5 t76.5 -18.5t62.5 -37t41 -62.5q47 66 123 94.5t182 28.5q152 0 220.5 -61.5t68.5 -184.5q0 -29 -4 -65.5t-10.5 -76.5t-13.5 -82t-15 -79q-4 -14 -12.5 -23.5t-30.5 -9.5h-553q-18 -104 -19 -180q0 -55 12.5 -93t41 -61.5t76 -34t116.5 -10.5q61 0 137 14.5t129 35.5 q20 8 23 -11l8 -39q2 -16 -14 -24q-70 -27 -148 -41t-147 -14q-152 0 -225.5 47t-83.5 129q-23 -55 -59 -90t-82 -53.5t-101 -25.5t-117 -7h-26q-139 0 -193.5 47t-54.5 141zM123 182q0 -66 40 -93.5t134 -27.5h22q78 0 134.5 14.5t95.5 45.5t63.5 81t36.5 122l31 165 q-35 4 -74 7.5t-84 3.5h-131q-104 0 -157.5 -29t-75.5 -94q-16 -45 -25.5 -96.5t-9.5 -98.5zM774 518h473q25 0 31 29q14 66 22.5 122t8.5 97q0 98 -54.5 137t-156.5 39q-66 0 -115 -13.5t-86 -48t-61.5 -95t-40.5 -152.5z" />
<glyph unicode="&#xe7;" horiz-adv-x="835" d="M63 250q0 31 5.5 75t13.5 94t19.5 102t21.5 102q14 63 28.5 112t33 86t40 65.5t49.5 53.5q47 41 113 61.5t158 20.5q72 0 140.5 -17.5t121.5 -48.5q18 -10 10 -28l-18 -37q-8 -16 -31 -6q-51 25 -112.5 41t-120.5 16t-109.5 -15.5t-85.5 -45.5q-43 -37 -72.5 -98.5 t-56.5 -174.5q-10 -45 -20.5 -93t-18.5 -95t-12 -89t-4 -73q0 -104 54 -150.5t183 -46.5q25 0 56.5 4.5t65.5 11.5t66.5 16t59.5 22q16 6 23 -11l8 -38q4 -10 2 -16.5t-19 -12.5q-51 -23 -121.5 -37t-136.5 -18l-63 -125q20 6 43.5 9t46.5 3q53 0 85 -32t32 -91 q0 -47 -11.5 -86t-39 -66.5t-74.5 -44t-119 -16.5q-35 0 -77 7t-77 20q-18 6 -12 22l12 39q6 14 21 8q33 -12 71.5 -19t69.5 -7q72 0 110 26.5t38 106.5q0 59 -70 59q-29 0 -56.5 -4t-50.5 -8q-16 -2 -24 8l-12 16q-8 10 0 27l75 150q-123 10 -187.5 71.5t-64.5 194.5z" />
<glyph unicode="&#xe8;" horiz-adv-x="892" d="M63 250q0 37 6.5 92t15.5 114.5t20.5 118t19.5 101.5q37 186 125 266q49 43 116.5 61.5t153.5 18.5q150 0 219.5 -63.5t69.5 -182.5q0 -29 -4 -65.5t-10.5 -76.5t-13.5 -82t-15 -79q-4 -14 -12.5 -23.5t-30.5 -9.5h-553q-18 -104 -18 -180q0 -55 12 -93t40.5 -61.5 t76 -34t116.5 -10.5q61 0 137 16.5t130 35.5q20 8 22 -11l8 -41q2 -16 -14 -24q-70 -27 -147.5 -41t-147.5 -14q-94 0 -155.5 18t-98.5 53t-52.5 84t-15.5 113zM184 518h473q25 0 31 29q14 66 22.5 122t8.5 97q0 98 -54.5 137t-156.5 39q-66 0 -115 -13.5t-86 -48t-61.5 -95 t-40.5 -152.5zM265.5 1373q-9.5 11 0.5 30l23 49q10 20 33 12l401 -172q16 -6 10 -22l-20 -51q-6 -12 -23 -7l-397 144q-18 6 -27.5 17z" />
<glyph unicode="&#xe9;" horiz-adv-x="892" d="M63 250q0 37 6.5 92t15.5 114.5t20.5 118t19.5 101.5q37 186 125 266q49 43 116.5 61.5t153.5 18.5q150 0 219.5 -63.5t69.5 -182.5q0 -29 -4 -65.5t-10.5 -76.5t-13.5 -82t-15 -79q-4 -14 -12.5 -23.5t-30.5 -9.5h-553q-18 -104 -18 -180q0 -55 12 -93t40.5 -61.5 t76 -34t116.5 -10.5q61 0 137 16.5t130 35.5q20 8 22 -11l8 -41q2 -16 -14 -24q-70 -27 -147.5 -41t-147.5 -14q-94 0 -155.5 18t-98.5 53t-52.5 84t-15.5 113zM184 518h473q25 0 31 29q14 66 22.5 122t8.5 97q0 98 -54.5 137t-156.5 39q-66 0 -115 -13.5t-86 -48t-61.5 -95 t-40.5 -152.5zM385 1270q-6 16 11 22l401 172q23 8 33 -12l22 -49q10 -18 1 -29.5t-27 -17.5l-398 -144q-16 -6 -22 7z" />
<glyph unicode="&#xea;" horiz-adv-x="892" d="M63 250q0 37 6.5 92t15.5 114.5t20.5 118t19.5 101.5q37 186 125 266q49 43 116.5 61.5t153.5 18.5q150 0 219.5 -63.5t69.5 -182.5q0 -29 -4 -65.5t-10.5 -76.5t-13.5 -82t-15 -79q-4 -14 -12.5 -23.5t-30.5 -9.5h-553q-18 -104 -18 -180q0 -55 12 -93t40.5 -61.5 t76 -34t116.5 -10.5q61 0 137 16.5t130 35.5q20 8 22 -11l8 -41q2 -16 -14 -24q-70 -27 -147.5 -41t-147.5 -14q-94 0 -155.5 18t-98.5 53t-52.5 84t-15.5 113zM184 518h473q25 0 31 29q14 66 22.5 122t8.5 97q0 98 -54.5 137t-156.5 39q-66 0 -115 -13.5t-86 -48t-61.5 -95 t-40.5 -152.5zM273 1245q-18 14 2 33l254 227q20 20 43 21h38q14 0 21.5 -3t17.5 -18l191 -239q12 -18 -6 -33l-35 -27q-20 -12 -35 6l-172 207h-12l-238 -207q-12 -10 -20 -9t-15 7z" />
<glyph unicode="&#xeb;" horiz-adv-x="892" d="M63 250q0 37 6.5 92t15.5 114.5t20.5 118t19.5 101.5q37 186 125 266q49 43 116.5 61.5t153.5 18.5q150 0 219.5 -63.5t69.5 -182.5q0 -29 -4 -65.5t-10.5 -76.5t-13.5 -82t-15 -79q-4 -14 -12.5 -23.5t-30.5 -9.5h-553q-18 -104 -18 -180q0 -55 12 -93t40.5 -61.5 t76 -34t116.5 -10.5q61 0 137 16.5t130 35.5q20 8 22 -11l8 -41q2 -16 -14 -24q-70 -27 -147.5 -41t-147.5 -14q-94 0 -155.5 18t-98.5 53t-52.5 84t-15.5 113zM184 518h473q25 0 31 29q14 66 22.5 122t8.5 97q0 98 -54.5 137t-156.5 39q-66 0 -115 -13.5t-86 -48t-61.5 -95 t-40.5 -152.5zM311 1300q0 16 10 68q14 74 88 74h31q61 0 61 -51q0 -20 -10 -66q-10 -41 -30.5 -56.5t-61.5 -15.5h-25q-63 0 -63 47zM641 1300q0 16 10 68q14 74 88 74h31q61 0 61 -51q0 -20 -10 -66q-10 -41 -30.5 -56.5t-61.5 -15.5h-25q-63 0 -63 47z" />
<glyph unicode="&#xec;" horiz-adv-x="446" d="M1.5 1373q-9.5 11 0.5 30l23 49q10 20 33 12l401 -172q16 -6 10 -22l-20 -51q-6 -12 -23 -7l-397 144q-18 6 -27.5 17zM68 14l188 975q2 14 18 15h60q14 0 12 -15l-188 -975q-2 -14 -19 -14h-59q-14 0 -12 14z" />
<glyph unicode="&#xed;" horiz-adv-x="446" d="M68 14l188 975q2 14 18 15h60q14 0 12 -15l-188 -975q-2 -14 -19 -14h-59q-14 0 -12 14zM202 1270q-6 16 11 22l401 172q23 8 33 -12l22 -49q10 -18 1 -29.5t-27 -17.5l-398 -144q-16 -6 -22 7z" />
<glyph unicode="&#xee;" horiz-adv-x="446" d="M68 14l188 975q2 14 18 15h60q14 0 12 -15l-188 -975q-2 -14 -19 -14h-59q-14 0 -12 14zM68 1245q-18 14 2 33l254 227q20 20 43 21h38q14 0 21.5 -3t17.5 -18l191 -239q12 -18 -6 -33l-35 -27q-20 -12 -35 6l-172 207h-12l-238 -207q-12 -10 -20 -9t-15 7z" />
<glyph unicode="&#xef;" horiz-adv-x="446" d="M68 14l188 975q2 14 18 15h60q14 0 12 -15l-188 -975q-2 -14 -19 -14h-59q-14 0 -12 14zM103 1300q0 16 10 68q14 74 88 74h31q61 0 61 -51q0 -20 -10 -66q-10 -41 -30.5 -56.5t-61.5 -15.5h-25q-63 0 -63 47zM433 1300q0 16 10 68q14 74 88 74h31q61 0 61 -51 q0 -20 -10 -66q-10 -41 -30.5 -56.5t-61.5 -15.5h-25q-63 0 -63 47z" />
<glyph unicode="&#xf0;" horiz-adv-x="929" d="M68 238q0 53 13 149t32 207q33 186 115.5 269t238.5 83q86 0 154.5 -31.5t101.5 -85.5h4q-14 129 -58 229.5t-122 166.5l-197 -113q-16 -10 -26 8l-19 33q-6 14 12 27l170 96q-31 23 -62.5 42t-70.5 40q-16 8 -6 22l25 35q10 16 26 8q47 -25 86 -50t76 -54l170 96 q18 10 27 -4l18 -31q8 -18 -6 -26l-152 -88q152 -147 183 -375q6 -47 9 -94t2 -100.5t-8 -114t-20 -136.5q-18 -109 -42.5 -195.5t-69.5 -147t-117 -92t-184 -31.5q-162 0 -232.5 70.5t-70.5 187.5zM158 252q0 -94 46 -143.5t177 -49.5q84 0 138 25t88 74t53.5 120.5 t37.5 165.5q10 51 15.5 95.5t5.5 78.5q0 117 -61.5 182.5t-186.5 65.5q-115 0 -178.5 -60.5t-91.5 -219.5q-16 -96 -29.5 -182.5t-13.5 -151.5z" />
<glyph unicode="&#xf1;" horiz-adv-x="993" d="M37 16l188 977q2 10 15 11h53q14 0 12 -15l-10 -100h4q45 61 122 97t200 36q137 0 196.5 -57.5t59.5 -161.5q0 -47 -11.5 -117t-27.5 -154l-99 -518q-2 -14 -18 -14h-55q-16 0 -15 16l113 586q12 63 18 106.5t6 73.5q0 78 -43 119t-147 41q-66 0 -123 -15.5t-103 -53 t-79 -100t-51 -155.5l-117 -604q-2 -14 -19 -14h-55q-16 0 -14 16zM315 1303q51 82 108.5 118.5t111.5 36.5q63 0 120 -49l43 -37q41 -35 76 -35t72 31t71 78q12 14 29 2l33 -27q16 -12 2 -30q-51 -72 -102.5 -113t-112.5 -41q-66 0 -123 49l-43 37q-23 18 -39 29.5 t-39 11.5q-35 0 -65.5 -24.5t-69.5 -79.5q-14 -18 -31 -9l-37 27q-14 10 -4 25z" />
<glyph unicode="&#xf2;" horiz-adv-x="964" d="M63 250q0 70 13.5 154.5t40.5 207.5q25 115 61.5 194t88.5 127t124 68.5t166 20.5q154 0 225.5 -67.5t71.5 -186.5q0 -68 -12.5 -165t-40.5 -212q-27 -111 -61.5 -188.5t-86 -126.5t-122.5 -71.5t-169 -22.5q-160 0 -229.5 67.5t-69.5 200.5zM156 260q0 -92 46 -145.5 t175 -53.5q76 0 130 19.5t93 62.5t66.5 114t50.5 173q23 98 34 181t11 132q0 102 -49 150.5t-172 48.5q-78 0 -132.5 -19.5t-93.5 -62.5t-65.5 -113.5t-48.5 -173.5q-23 -98 -34 -181t-11 -132zM314.5 1373q-9.5 11 0.5 30l23 49q10 20 33 12l401 -172q16 -6 10 -22l-20 -51 q-6 -12 -23 -7l-397 144q-18 6 -27.5 17z" />
<glyph unicode="&#xf3;" horiz-adv-x="964" d="M63 250q0 70 13.5 154.5t40.5 207.5q25 115 61.5 194t88.5 127t124 68.5t166 20.5q154 0 225.5 -67.5t71.5 -186.5q0 -68 -12.5 -165t-40.5 -212q-27 -111 -61.5 -188.5t-86 -126.5t-122.5 -71.5t-169 -22.5q-160 0 -229.5 67.5t-69.5 200.5zM156 260q0 -92 46 -145.5 t175 -53.5q76 0 130 19.5t93 62.5t66.5 114t50.5 173q23 98 34 181t11 132q0 102 -49 150.5t-172 48.5q-78 0 -132.5 -19.5t-93.5 -62.5t-65.5 -113.5t-48.5 -173.5q-23 -98 -34 -181t-11 -132zM421 1270q-6 16 11 22l401 172q23 8 33 -12l22 -49q10 -18 1 -29.5t-27 -17.5 l-398 -144q-16 -6 -22 7z" />
<glyph unicode="&#xf4;" horiz-adv-x="964" d="M63 250q0 70 13.5 154.5t40.5 207.5q25 115 61.5 194t88.5 127t124 68.5t166 20.5q154 0 225.5 -67.5t71.5 -186.5q0 -68 -12.5 -165t-40.5 -212q-27 -111 -61.5 -188.5t-86 -126.5t-122.5 -71.5t-169 -22.5q-160 0 -229.5 67.5t-69.5 200.5zM156 260q0 -92 46 -145.5 t175 -53.5q76 0 130 19.5t93 62.5t66.5 114t50.5 173q23 98 34 181t11 132q0 102 -49 150.5t-172 48.5q-78 0 -132.5 -19.5t-93.5 -62.5t-65.5 -113.5t-48.5 -173.5q-23 -98 -34 -181t-11 -132zM322 1245q-18 14 2 33l254 227q20 20 43 21h38q14 0 21.5 -3t17.5 -18 l191 -239q12 -18 -6 -33l-35 -27q-20 -12 -35 6l-172 207h-12l-238 -207q-12 -10 -20 -9t-15 7z" />
<glyph unicode="&#xf5;" horiz-adv-x="964" d="M63 250q0 70 13.5 154.5t40.5 207.5q25 115 61.5 194t88.5 127t124 68.5t166 20.5q154 0 225.5 -67.5t71.5 -186.5q0 -68 -12.5 -165t-40.5 -212q-27 -111 -61.5 -188.5t-86 -126.5t-122.5 -71.5t-169 -22.5q-160 0 -229.5 67.5t-69.5 200.5zM156 260q0 -92 46 -145.5 t175 -53.5q76 0 130 19.5t93 62.5t66.5 114t50.5 173q23 98 34 181t11 132q0 102 -49 150.5t-172 48.5q-78 0 -132.5 -19.5t-93.5 -62.5t-65.5 -113.5t-48.5 -173.5q-23 -98 -34 -181t-11 -132zM284 1303q51 82 108.5 118.5t111.5 36.5q63 0 120 -49l43 -37q41 -35 76 -35 t72 31t71 78q12 14 29 2l33 -27q16 -12 2 -30q-51 -72 -102.5 -113t-112.5 -41q-66 0 -123 49l-43 37q-23 18 -39 29.5t-39 11.5q-35 0 -65.5 -24.5t-69.5 -79.5q-14 -18 -31 -9l-37 27q-14 10 -4 25z" />
<glyph unicode="&#xf6;" horiz-adv-x="964" d="M63 250q0 70 13.5 154.5t40.5 207.5q25 115 61.5 194t88.5 127t124 68.5t166 20.5q154 0 225.5 -67.5t71.5 -186.5q0 -68 -12.5 -165t-40.5 -212q-27 -111 -61.5 -188.5t-86 -126.5t-122.5 -71.5t-169 -22.5q-160 0 -229.5 67.5t-69.5 200.5zM156 260q0 -92 46 -145.5 t175 -53.5q76 0 130 19.5t93 62.5t66.5 114t50.5 173q23 98 34 181t11 132q0 102 -49 150.5t-172 48.5q-78 0 -132.5 -19.5t-93.5 -62.5t-65.5 -113.5t-48.5 -173.5q-23 -98 -34 -181t-11 -132zM356 1300q0 16 10 68q14 74 88 74h31q61 0 61 -51q0 -20 -10 -66 q-10 -41 -30.5 -56.5t-61.5 -15.5h-25q-63 0 -63 47zM686 1300q0 16 10 68q14 74 88 74h31q61 0 61 -51q0 -20 -10 -66q-10 -41 -30.5 -56.5t-61.5 -15.5h-25q-63 0 -63 47z" />
<glyph unicode="&#xf7;" horiz-adv-x="1048" d="M86 506l12 59q2 14 17 15h800q14 0 13 -15l-13 -59q-2 -14 -16 -14h-801q-14 0 -12 14zM336 129q0 8 2 23.5t10 56.5q6 35 25.5 51t60.5 16h29q37 0 51 -11t14 -34q0 -8 -2 -23t-10 -56q-6 -35 -22.5 -51.5t-63.5 -16.5h-29q-66 0 -65 45zM473 840q0 8 2 23.5t10 56.5 q6 35 25.5 51t60.5 16h29q37 0 51.5 -11t14.5 -34q0 -8 -2.5 -23.5t-10.5 -56.5q-6 -35 -22.5 -51t-63.5 -16h-28q-66 0 -66 45z" />
<glyph unicode="&#xf8;" horiz-adv-x="964" d="M33 -88l94 143q-63 63 -64 195q0 70 13.5 154.5t40.5 207.5q25 115 61.5 194t88.5 127t124 68.5t166 20.5q55 0 99 -10.5t79 -26.5l101 152q8 14 22 8l37 -21q14 -8 4 -20l-106 -162q61 -66 61 -174q0 -68 -12.5 -165t-40.5 -212q-27 -111 -61.5 -188.5t-86 -126.5 t-122.5 -71.5t-169 -22.5q-106 0 -178 32l-88 -135q-8 -14 -22 -8l-37 20q-14 8 -4 21zM156 260q0 -74 26 -121l506 772q-53 31 -147 31q-78 0 -132.5 -19.5t-93.5 -62.5t-65.5 -113.5t-48.5 -173.5q-23 -98 -34 -181t-11 -132zM233 90q54 -29 144 -29q76 0 130 19.5 t93 62.5t66.5 114t50.5 173q23 98 34 181t11 132q0 72 -25 115z" />
<glyph unicode="&#xf9;" horiz-adv-x="974" d="M74 213q0 20 3 52t13 77l123 647q2 14 18 15h58q14 0 12 -15l-123 -637q-16 -78 -16 -133q0 -78 49 -118t168 -40q80 0 129 12.5t86 43.5q41 35 65.5 94t42.5 151l121 627q2 14 19 15h57q14 0 12 -15l-125 -645q-23 -109 -52 -180.5t-83 -112.5q-47 -39 -111.5 -54 t-172.5 -15q-152 0 -222.5 57t-70.5 174zM296.5 1373q-9.5 11 0.5 30l23 49q10 20 33 12l401 -172q16 -6 10 -22l-20 -51q-6 -12 -23 -7l-397 144q-18 6 -27.5 17z" />
<glyph unicode="&#xfa;" horiz-adv-x="974" d="M74 213q0 20 3 52t13 77l123 647q2 14 18 15h58q14 0 12 -15l-123 -637q-16 -78 -16 -133q0 -78 49 -118t168 -40q80 0 129 12.5t86 43.5q41 35 65.5 94t42.5 151l121 627q2 14 19 15h57q14 0 12 -15l-125 -645q-23 -109 -52 -180.5t-83 -112.5q-47 -39 -111.5 -54 t-172.5 -15q-152 0 -222.5 57t-70.5 174zM432 1270q-6 16 11 22l401 172q23 8 33 -12l22 -49q10 -18 1 -29.5t-27 -17.5l-398 -144q-16 -6 -22 7z" />
<glyph unicode="&#xfb;" horiz-adv-x="974" d="M74 213q0 20 3 52t13 77l123 647q2 14 18 15h58q14 0 12 -15l-123 -637q-16 -78 -16 -133q0 -78 49 -118t168 -40q80 0 129 12.5t86 43.5q41 35 65.5 94t42.5 151l121 627q2 14 19 15h57q14 0 12 -15l-125 -645q-23 -109 -52 -180.5t-83 -112.5q-47 -39 -111.5 -54 t-172.5 -15q-152 0 -222.5 57t-70.5 174zM310 1245q-18 14 2 33l254 227q20 20 43 21h38q14 0 21.5 -3t17.5 -18l191 -239q12 -18 -6 -33l-35 -27q-20 -12 -35 6l-172 207h-12l-238 -207q-12 -10 -20 -9t-15 7z" />
<glyph unicode="&#xfc;" horiz-adv-x="974" d="M74 213q0 20 3 52t13 77l123 647q2 14 18 15h58q14 0 12 -15l-123 -637q-16 -78 -16 -133q0 -78 49 -118t168 -40q80 0 129 12.5t86 43.5q41 35 65.5 94t42.5 151l121 627q2 14 19 15h57q14 0 12 -15l-125 -645q-23 -109 -52 -180.5t-83 -112.5q-47 -39 -111.5 -54 t-172.5 -15q-152 0 -222.5 57t-70.5 174zM346 1300q0 16 10 68q14 74 88 74h31q61 0 61 -51q0 -20 -10 -66q-10 -41 -30.5 -56.5t-61.5 -15.5h-25q-63 0 -63 47zM676 1300q0 16 10 68q14 74 88 74h31q61 0 61 -51q0 -20 -10 -66q-10 -41 -30.5 -56.5t-61.5 -15.5h-25 q-63 0 -63 47z" />
<glyph unicode="&#xfd;" horiz-adv-x="808" d="M94 987q0 16 17 17h59q16 0 16 -17q8 -272 24.5 -472t39.5 -349q4 -35 22.5 -48.5t53.5 -13.5q102 182 209.5 413t205.5 472q4 14 23 15h59q23 0 15 -19q-66 -156 -131.5 -300t-133.5 -282q-82 -166 -176 -344t-217 -397q-6 -10 -11 -12t-15 2l-45 20q-19 11 -9 29 q45 76 91.5 158t91.5 166q-49 4 -77 29.5t-38 86.5q-12 86 -22.5 177.5t-19.5 194.5t-16.5 220t-15.5 254zM342 1270q-6 16 11 22l401 172q23 8 33 -12l22 -49q10 -18 1 -29.5t-27 -17.5l-398 -144q-16 -6 -22 7z" />
<glyph unicode="&#xfe;" horiz-adv-x="1005" d="M-33 -346l328 1712q4 20 18 20h56q16 0 14 -20l-92 -475h4q47 66 120.5 98.5t188.5 32.5q158 0 225.5 -66.5t67.5 -189.5q0 -61 -11.5 -148.5t-33.5 -197.5q-27 -133 -63.5 -218t-88 -134.5t-118 -67.5t-152.5 -18q-123 0 -186.5 39.5t-91.5 111.5h-7l-90 -481 q-2 -14 -18 -14h-55q-18 0 -15 16zM186 293q0 -49 12.5 -91t42 -73t77.5 -49.5t120 -18.5q70 0 121 17.5t89 60.5t66.5 117t51.5 184q18 86 29.5 171t11.5 145q0 92 -49 139t-168 47q-61 0 -114.5 -12t-98.5 -46t-78 -91.5t-51 -145.5l-51 -254q-4 -27 -7.5 -51.5 t-3.5 -48.5z" />
<glyph unicode="&#xff;" horiz-adv-x="808" d="M94 987q0 16 17 17h59q16 0 16 -17q8 -272 24.5 -472t39.5 -349q4 -35 22.5 -48.5t53.5 -13.5q102 182 209.5 413t205.5 472q4 14 23 15h59q23 0 15 -19q-66 -156 -131.5 -300t-133.5 -282q-82 -166 -176 -344t-217 -397q-6 -10 -11 -12t-15 2l-45 20q-19 11 -9 29 q45 76 91.5 158t91.5 166q-49 4 -77 29.5t-38 86.5q-12 86 -22.5 177.5t-19.5 194.5t-16.5 220t-15.5 254zM248 1300q0 16 10 68q14 74 88 74h31q61 0 61 -51q0 -20 -10 -66q-10 -41 -30.5 -56.5t-61.5 -15.5h-25q-63 0 -63 47zM578 1300q0 16 10 68q14 74 88 74h31 q61 0 61 -51q0 -20 -10 -66q-10 -41 -30.5 -56.5t-61.5 -15.5h-25q-63 0 -63 47z" />
<glyph unicode="&#x152;" horiz-adv-x="1681" d="M80 330q0 100 24.5 230t59.5 294q27 127 69.5 219t106 152.5t151 89t203.5 28.5q109 0 185.5 -37.5t117.5 -117.5q29 74 81 105.5t163 31.5h432q23 0 19 -20l-9 -43q-4 -12 -8 -15.5t-16 -3.5h-430q-74 0 -108 -31.5t-54 -119.5q-16 -74 -34.5 -157t-45.5 -208h520 q23 0 19 -20l-10 -45q-4 -10 -8.5 -14.5t-16.5 -4.5h-522q-25 -123 -39.5 -211t-24.5 -141q-12 -78 -12 -119q0 -47 27.5 -67.5t89.5 -20.5h454q20 0 19 -21l-8 -43q-4 -12 -8.5 -16t-16.5 -4h-455q-96 0 -144 38t-44 118q-41 -78 -136.5 -126t-207.5 -48q-90 0 -161 18 t-120 59t-75.5 107.5t-26.5 163.5zM180 348q0 -143 63.5 -212.5t217.5 -69.5q139 0 238.5 85.5t134.5 278.5q23 123 47 245t53 257q12 57 12 102q0 66 -24.5 109t-62.5 69.5t-82 37t-85 10.5q-90 0 -158.5 -23t-121 -74t-90 -133t-64.5 -199q-35 -145 -56.5 -272t-21.5 -211 z" />
<glyph unicode="&#x153;" horiz-adv-x="1533" d="M63 250q0 70 13.5 154.5t40.5 207.5q25 115 61.5 194t88.5 127t124 68.5t166 20.5q115 0 182.5 -39t89.5 -127q41 92 128 129t204 37q152 0 220.5 -61.5t68.5 -184.5q0 -29 -4 -65.5t-10 -76.5t-13.5 -82t-15.5 -79q-4 -14 -12.5 -23.5t-30.5 -9.5h-553l-6 -28 q-12 -86 -12 -152q0 -55 12 -93t40.5 -61.5t76 -34t116.5 -10.5q61 0 137 14.5t130 35.5q20 8 22 -11l8 -39q2 -16 -14 -24q-70 -27 -147.5 -41t-147.5 -14q-150 0 -226.5 50t-80.5 144q-41 -94 -128 -144t-229 -50q-160 0 -229.5 67.5t-69.5 200.5zM156 260 q0 -92 46 -145.5t175 -53.5q76 0 130 19.5t93 62.5t66.5 114t50.5 173q23 98 34 180t11 127q0 106 -49 155.5t-172 49.5q-78 0 -132.5 -19.5t-93.5 -62.5t-65.5 -113.5t-48.5 -173.5q-23 -98 -34 -181t-11 -132zM827 518h471q25 0 31 29q14 66 22.5 122t8.5 97 q0 98 -54.5 137t-156.5 39q-66 0 -115 -13.5t-86 -48t-61.5 -95t-40.5 -152.5l-2 -12q-4 -25 -8.5 -50.5t-8.5 -52.5z" />
<glyph unicode="&#x178;" horiz-adv-x="890" d="M145 1309q0 16 19 16h55q20 0 23 -16q16 -209 62 -397.5t104 -342.5h16q68 90 128 177.5t116.5 177.5t109.5 185t107 204q6 12 22 12h60q20 0 14 -16q-55 -109 -111.5 -211.5t-120 -204.5t-138 -208.5t-167.5 -225.5l-86 -441q-2 -18 -22 -18h-55q-20 0 -19 18l88 447 q-82 211 -132 413.5t-73 430.5zM327 1524q0 16 10 67q14 74 88 74h31q61 0 62 -51q0 -20 -11 -66q-10 -41 -30.5 -56t-61.5 -15h-24q-63 0 -64 47zM657 1524q0 16 10 67q14 74 88 74h31q61 0 61 -51q0 -20 -10 -66q-10 -41 -30.5 -56t-61.5 -15h-25q-63 0 -63 47z" />
<glyph unicode="&#x2c6;" horiz-adv-x="774" d="M238 1245q-18 14 2 33l254 227q20 20 43 21h38q14 0 21.5 -3t17.5 -18l191 -239q12 -18 -6 -33l-35 -27q-20 -12 -35 6l-172 207h-12l-238 -207q-12 -10 -20 -9t-15 7z" />
<glyph unicode="&#x2dc;" horiz-adv-x="864" d="M229 1303q51 82 108.5 118.5t111.5 36.5q63 0 120 -49l43 -37q41 -35 76 -35t72 31t71 78q12 14 29 2l33 -27q16 -12 2 -30q-51 -72 -102.5 -113t-112.5 -41q-66 0 -123 49l-43 37q-23 18 -39 29.5t-39 11.5q-35 0 -65.5 -24.5t-69.5 -79.5q-14 -18 -31 -9l-37 27 q-14 10 -4 25z" />
<glyph unicode="&#x2000;" horiz-adv-x="870" />
<glyph unicode="&#x2001;" horiz-adv-x="1741" />
<glyph unicode="&#x2002;" horiz-adv-x="870" />
<glyph unicode="&#x2003;" horiz-adv-x="1741" />
<glyph unicode="&#x2004;" horiz-adv-x="580" />
<glyph unicode="&#x2005;" horiz-adv-x="435" />
<glyph unicode="&#x2006;" horiz-adv-x="290" />
<glyph unicode="&#x2007;" horiz-adv-x="290" />
<glyph unicode="&#x2008;" horiz-adv-x="217" />
<glyph unicode="&#x2009;" horiz-adv-x="348" />
<glyph unicode="&#x200a;" horiz-adv-x="96" />
<glyph unicode="&#x2010;" horiz-adv-x="563" d="M35 512l12 53q2 14 16 15h392q14 0 12 -15l-12 -53q-2 -14 -17 -14h-391q-14 0 -12 14z" />
<glyph unicode="&#x2011;" horiz-adv-x="563" d="M35 512l12 53q2 14 16 15h392q14 0 12 -15l-12 -53q-2 -14 -17 -14h-391q-14 0 -12 14z" />
<glyph unicode="&#x2012;" horiz-adv-x="563" d="M35 512l12 53q2 14 16 15h392q14 0 12 -15l-12 -53q-2 -14 -17 -14h-391q-14 0 -12 14z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M-27 512l13 53q2 14 16 15h979q14 0 12 -15l-12 -53q-2 -14 -16 -14h-979q-14 0 -13 14z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M-27 512l13 53q2 14 16 15h2003q14 0 12 -15l-12 -53q-2 -14 -16 -14h-2003q-14 0 -13 14z" />
<glyph unicode="&#x2018;" horiz-adv-x="368" d="M160 1012q0 10 2 24.5t10 49.5q18 78 58 154.5t108 154.5q12 14 25 6l28 -20q14 -10 0 -25q-35 -41 -60.5 -81t-41.5 -75q-12 -27 8 -36l18 -9q31 -12 31 -38q0 -12 -2 -30t-12 -61q-14 -68 -78 -67h-27q-68 0 -67 53z" />
<glyph unicode="&#x2019;" horiz-adv-x="368" d="M142 973q-14 10 0 25q35 41 60.5 81t41.5 75q12 27 -8 36l-18 9q-31 12 -31 38q0 12 2 30t12 61q14 68 78 67h27q68 0 67 -53q0 -10 -2 -24.5t-10 -49.5q-18 -78 -58 -154.5t-108 -154.5q-12 -14 -25 -6z" />
<glyph unicode="&#x201a;" horiz-adv-x="368" d="M-67 -219q35 41 60.5 81t41.5 75q12 27 -8 36l-18 9q-31 12 -31 38q0 12 2 30t12 61q14 68 78 67h27q68 0 67 -53q0 -10 -2 -24.5t-10 -49.5q-18 -78 -58 -154.5t-108 -154.5q-12 -14 -25 -6l-28 20q-14 10 0 25z" />
<glyph unicode="&#x201c;" horiz-adv-x="645" d="M160 1012q0 10 2 24.5t10 49.5q18 78 58 154.5t108 154.5q12 14 25 6l28 -20q14 -10 0 -25q-35 -41 -60.5 -81t-41.5 -75q-12 -27 8 -36l18 -9q31 -12 31 -38q0 -12 -2 -30t-12 -61q-14 -68 -78 -67h-27q-68 0 -67 53zM436 1012q0 10 2 24.5t10 49.5q18 78 58 154.5 t108 154.5q12 14 25 6l28 -20q14 -10 0 -25q-35 -41 -60.5 -81t-41.5 -75q-12 -27 8 -36l18 -9q31 -12 31 -38q0 -12 -2 -30t-12 -61q-14 -68 -78 -67h-27q-68 0 -67 53z" />
<glyph unicode="&#x201d;" horiz-adv-x="645" d="M142 968q-14 10 0 25q35 41 60.5 81t41.5 75q12 27 -8 36l-18 9q-31 12 -31 38q0 12 2 30t12 61q14 68 78 67h27q68 0 67 -53q0 -10 -2 -24.5t-10 -49.5q-18 -78 -58 -154.5t-108 -154.5q-12 -14 -25 -6zM418 968q-14 10 0 25q35 41 60.5 81t41.5 75q12 27 -8 36l-18 9 q-31 12 -31 38q0 12 2 30t12 61q14 68 78 67h27q68 0 67 -53q0 -10 -2 -24.5t-10 -49.5q-18 -78 -58 -154.5t-108 -154.5q-12 -14 -25 -6z" />
<glyph unicode="&#x201e;" horiz-adv-x="645" d="M-67 -219q35 41 60.5 81t41.5 75q12 27 -8 36l-18 9q-31 12 -31 38q0 12 2 30t12 61q14 68 78 67h27q68 0 67 -53q0 -10 -2 -24.5t-10 -49.5q-18 -78 -58 -154.5t-108 -154.5q-12 -14 -25 -6l-28 20q-14 10 0 25zM207 -219q35 41 60.5 81t41.5 75q12 27 -8 36l-18 9 q-31 12 -31 38q0 12 2 30t12 61q14 68 78 67h27q68 0 67 -53q0 -10 -2 -24.5t-10 -49.5q-18 -78 -58 -154.5t-108 -154.5q-12 -14 -25 -6l-28 20q-14 10 0 25z" />
<glyph unicode="&#x2022;" horiz-adv-x="698" d="M94 492q0 51 21.5 99t58.5 85t87 59.5t106 22.5q84 0 132 -48t48 -130q0 -51 -21.5 -99.5t-58.5 -85.5t-87.5 -59.5t-105.5 -22.5q-84 0 -132 48.5t-48 130.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1167" d="M-12 23q0 10 2 24t10 57q12 68 82 68h22q61 0 62 -41q0 -6 -2 -21.5t-12 -60.5q-8 -35 -25.5 -51t-58.5 -16h-21q-59 0 -59 41zM377 23q0 10 2 24t10 57q12 68 82 68h23q61 0 61 -41q0 -6 -2 -21.5t-12 -60.5q-8 -35 -25.5 -51t-58.5 -16h-21q-59 0 -59 41zM766 23 q0 10 2 24t10 57q12 68 82 68h23q61 0 61 -41q0 -6 -2 -21.5t-12 -60.5q-8 -35 -25.5 -51t-58.5 -16h-21q-59 0 -59 41z" />
<glyph unicode="&#x202f;" horiz-adv-x="348" />
<glyph unicode="&#x2039;" horiz-adv-x="557" d="M57 457l4 24q4 14 8.5 22.5t14.5 16.5l319 230q14 12 27 -2l27 -33q10 -14 -4 -25l-281 -227l184 -215q10 -14 -4 -25l-28 -24q-14 -10 -25 0l-227 225q-16 16 -15 33z" />
<glyph unicode="&#x203a;" horiz-adv-x="557" d="M53 233q-10 12 4 25l281 227l-184 215q-10 12 4 25l28 25q14 10 25 0l227 -226q16 -16 15 -32l-4 -25q-4 -16 -8.5 -23.5t-14.5 -15.5l-320 -229q-14 -12 -26 2z" />
<glyph unicode="&#x205f;" horiz-adv-x="435" />
<glyph unicode="&#x20ac;" d="M14 487l9 48q2 18 22 18h150q4 37 12 81t14 83h-145q-12 0 -15.5 5t-1.5 15l9 47q2 18 22 19h148q29 164 69.5 269.5t106.5 164.5q63 59 144 82.5t185 23.5q98 0 176 -22.5t138 -56.5q20 -12 10 -33l-18 -29q-8 -14 -16.5 -14t-18.5 6q-63 31 -129 48.5t-140 17.5 q-88 0 -149 -18.5t-109 -61.5q-55 -51 -88.5 -143.5t-60.5 -233.5h465q12 0 15 -5t1 -16l-8 -47q-2 -18 -23 -18h-466q-6 -33 -14.5 -81t-12.5 -83h463q12 0 15 -5t1 -16l-8 -47q-2 -18 -23 -18h-460q-4 -39 -8.5 -73t-4.5 -62q0 -72 12.5 -123t44 -84t88 -47.5t146.5 -14.5 q66 0 141.5 14.5t139.5 37.5q12 4 19 2t11 -19l9 -30q6 -18 -15 -27q-70 -27 -151.5 -43t-163.5 -16q-104 0 -177 17t-118 57t-65.5 103.5t-20.5 155.5q0 33 3 73t7 79h-151q-16 0 -15 20z" />
<glyph unicode="&#x2122;" horiz-adv-x="1316" d="M174 1272l8 41q2 12 15 12h407q10 0 8 -12l-8 -41q-4 -14 -14 -15h-166l-109 -532q-2 -14 -16 -14h-47q-16 0 -14 14l108 532h-164q-12 0 -8 15zM580 727q18 78 35.5 147.5t36 138t38 139.5t41.5 151q4 14 9 18t20 4h80q20 0 22 -22q4 -57 8 -119t9.5 -120t9.5 -109.5 t8 -90.5h8q43 82 98.5 191.5t120.5 247.5q6 12 12.5 17t22.5 5h76q27 0 22 -22q-10 -80 -19 -151t-19.5 -139.5t-20.5 -138t-22 -147.5q-2 -8 -6.5 -12t-16.5 -4h-45q-14 0 -12 14q20 123 40.5 252t37.5 258h-7q-63 -129 -116.5 -233.5t-100.5 -194.5q-10 -16 -16 -21.5 t-21 -5.5h-59q-23 0 -25 27q-10 98 -20 201.5t-21 226.5h-6q-35 -129 -68.5 -258t-62.5 -252q-2 -14 -16 -14h-43q-16 0 -12 16z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1003" d="M0 0v1004h1004v-1004h-1004z" />
<hkern u1="&#x26;" u2="&#x178;" k="57" />
<hkern u1="&#x26;" u2="&#xdd;" k="57" />
<hkern u1="&#x26;" u2="Z" k="-35" />
<hkern u1="&#x26;" u2="Y" k="57" />
<hkern u1="&#x26;" u2="T" k="129" />
<hkern u1="&#x26;" u2="X" k="-39" />
<hkern u1="&#x26;" u2="V" k="37" />
<hkern u1="&#x28;" u2="&#x178;" k="-49" />
<hkern u1="&#x28;" u2="&#xdd;" k="-49" />
<hkern u1="&#x28;" u2="j" k="-121" />
<hkern u1="&#x28;" u2="Y" k="-49" />
<hkern u1="&#x28;" u2="W" k="-66" />
<hkern u1="&#x28;" u2="T" k="-86" />
<hkern u1="&#x28;" u2="V" k="-86" />
<hkern u1="&#x2a;" u2="&#x178;" k="-39" />
<hkern u1="&#x2a;" u2="&#xdd;" k="-39" />
<hkern u1="&#x2a;" u2="&#xc5;" k="145" />
<hkern u1="&#x2a;" u2="&#xc4;" k="145" />
<hkern u1="&#x2a;" u2="&#xc3;" k="145" />
<hkern u1="&#x2a;" u2="&#xc2;" k="145" />
<hkern u1="&#x2a;" u2="&#xc1;" k="145" />
<hkern u1="&#x2a;" u2="&#xc0;" k="145" />
<hkern u1="&#x2a;" u2="Y" k="-39" />
<hkern u1="&#x2a;" u2="W" k="-39" />
<hkern u1="&#x2a;" u2="T" k="-41" />
<hkern u1="&#x2a;" u2="A" k="145" />
<hkern u1="&#x2a;" u2="V" k="-49" />
<hkern u1="&#x2c;" u2="t" k="29" />
<hkern u1="&#x2c;" u2="j" k="-88" />
<hkern u1="&#x2c;" u2="v" k="109" />
<hkern u1="&#x2c;" u2="V" k="78" />
<hkern u1="&#x2c;" u2="J" k="-49" />
<hkern u1="&#x2e;" u2="v" k="109" />
<hkern u1="&#x2e;" u2="V" k="78" />
<hkern u1="&#x2e;" u2="J" k="-49" />
<hkern u1="&#x2f;" g2="uniFB02" k="-35" />
<hkern u1="&#x2f;" g2="uniFB01" k="-35" />
<hkern u1="&#x2f;" u2="&#x178;" k="-76" />
<hkern u1="&#x2f;" u2="&#x153;" k="27" />
<hkern u1="&#x2f;" u2="&#xf8;" k="27" />
<hkern u1="&#x2f;" u2="&#xf6;" k="27" />
<hkern u1="&#x2f;" u2="&#xf5;" k="27" />
<hkern u1="&#x2f;" u2="&#xf4;" k="27" />
<hkern u1="&#x2f;" u2="&#xf3;" k="27" />
<hkern u1="&#x2f;" u2="&#xf2;" k="27" />
<hkern u1="&#x2f;" u2="&#xf0;" k="27" />
<hkern u1="&#x2f;" u2="&#xdd;" k="-76" />
<hkern u1="&#x2f;" u2="o" k="27" />
<hkern u1="&#x2f;" u2="f" k="-35" />
<hkern u1="&#x2f;" u2="Y" k="-76" />
<hkern u1="&#x2f;" u2="W" k="-53" />
<hkern u1="&#x2f;" u2="T" k="-68" />
<hkern u1="&#x2f;" u2="V" k="-78" />
<hkern u1="&#x3b;" u2="j" k="-70" />
<hkern u1="&#x40;" u2="T" k="78" />
<hkern u1="&#x40;" u2="V" k="16" />
<hkern u1="A" u2="&#x2122;" k="221" />
<hkern u1="A" u2="&#x203a;" k="-35" />
<hkern u1="A" u2="&#xbb;" k="-35" />
<hkern u1="A" u2="&#xae;" k="41" />
<hkern u1="A" u2="&#xa9;" k="41" />
<hkern u1="A" u2="v" k="86" />
<hkern u1="A" u2="q" k="61" />
<hkern u1="A" u2="p" k="20" />
<hkern u1="A" u2="m" k="20" />
<hkern u1="A" u2="k" k="20" />
<hkern u1="A" u2="h" k="20" />
<hkern u1="A" u2="b" k="61" />
<hkern u1="A" u2="\" k="139" />
<hkern u1="A" u2="X" k="-35" />
<hkern u1="A" u2="V" k="94" />
<hkern u1="A" u2="Q" k="53" />
<hkern u1="A" u2="J" k="-55" />
<hkern u1="A" u2="E" k="82" />
<hkern u1="A" u2="&#x3f;" k="47" />
<hkern u1="A" u2="&#x2a;" k="145" />
<hkern u1="B" u2="&#x152;" k="-14" />
<hkern u1="B" u2="&#xd8;" k="-14" />
<hkern u1="B" u2="&#xd6;" k="-14" />
<hkern u1="B" u2="&#xd5;" k="-14" />
<hkern u1="B" u2="&#xd4;" k="-14" />
<hkern u1="B" u2="&#xd3;" k="-14" />
<hkern u1="B" u2="&#xd2;" k="-14" />
<hkern u1="B" u2="&#xc5;" k="-29" />
<hkern u1="B" u2="&#xc4;" k="-29" />
<hkern u1="B" u2="&#xc3;" k="-29" />
<hkern u1="B" u2="&#xc2;" k="-29" />
<hkern u1="B" u2="&#xc1;" k="-29" />
<hkern u1="B" u2="&#xc0;" k="-29" />
<hkern u1="B" u2="T" k="57" />
<hkern u1="B" u2="O" k="-14" />
<hkern u1="B" u2="A" k="-29" />
<hkern u1="B" u2="&#x2122;" k="88" />
<hkern u1="B" u2="&#x29;" k="27" />
<hkern u1="C" u2="x" k="-25" />
<hkern u1="C" u2="q" k="37" />
<hkern u1="C" u2="p" k="20" />
<hkern u1="C" u2="m" k="20" />
<hkern u1="C" u2="X" k="-37" />
<hkern u1="C" u2="V" k="-35" />
<hkern u1="C" u2="J" k="-68" />
<hkern u1="C" u2="&#x29;" k="-76" />
<hkern u1="D" u2="X" k="41" />
<hkern u1="E" u2="V" k="-14" />
<hkern u1="E" u2="Q" k="16" />
<hkern u1="E" u2="J" k="-37" />
<hkern u1="E" u2="B" k="6" />
<hkern u1="F" u2="&#x2026;" k="139" />
<hkern u1="F" u2="&#x201e;" k="139" />
<hkern u1="F" u2="&#x201d;" k="-39" />
<hkern u1="F" u2="&#x201a;" k="139" />
<hkern u1="F" u2="&#x2019;" k="-39" />
<hkern u1="F" u2="&#x178;" k="-35" />
<hkern u1="F" u2="&#x153;" k="16" />
<hkern u1="F" u2="&#xf8;" k="16" />
<hkern u1="F" u2="&#xf6;" k="16" />
<hkern u1="F" u2="&#xf5;" k="16" />
<hkern u1="F" u2="&#xf4;" k="16" />
<hkern u1="F" u2="&#xf3;" k="16" />
<hkern u1="F" u2="&#xf2;" k="16" />
<hkern u1="F" u2="&#xf0;" k="16" />
<hkern u1="F" u2="&#xdd;" k="-35" />
<hkern u1="F" u2="&#xc6;" k="197" />
<hkern u1="F" u2="&#xc5;" k="90" />
<hkern u1="F" u2="&#xc4;" k="90" />
<hkern u1="F" u2="&#xc3;" k="90" />
<hkern u1="F" u2="&#xc2;" k="90" />
<hkern u1="F" u2="&#xc1;" k="90" />
<hkern u1="F" u2="&#xc0;" k="90" />
<hkern u1="F" u2="o" k="16" />
<hkern u1="F" u2="Y" k="-35" />
<hkern u1="F" u2="W" k="-35" />
<hkern u1="F" u2="T" k="-25" />
<hkern u1="F" u2="A" k="90" />
<hkern u1="F" u2="&#x2e;" k="139" />
<hkern u1="F" u2="&#x2c;" k="139" />
<hkern u1="F" u2="V" k="-35" />
<hkern u1="F" u2="J" k="94" />
<hkern u1="F" u2="&#x3f;" k="-20" />
<hkern u1="F" u2="&#x2f;" k="106" />
<hkern u1="G" u2="J" k="-25" />
<hkern u1="G" u2="&#x3f;" k="16" />
<hkern u1="J" u2="v" k="-14" />
<hkern u1="K" u2="x" k="-61" />
<hkern u1="K" u2="v" k="27" />
<hkern u1="K" u2="X" k="-80" />
<hkern u1="K" u2="V" k="-66" />
<hkern u1="K" u2="J" k="-66" />
<hkern u1="K" u2="&#x29;" k="-70" />
<hkern u1="L" u2="&#x2122;" k="205" />
<hkern u1="L" u2="&#xae;" k="41" />
<hkern u1="L" u2="&#xa9;" k="41" />
<hkern u1="L" u2="x" k="-47" />
<hkern u1="L" u2="v" k="41" />
<hkern u1="L" u2="\" k="109" />
<hkern u1="L" u2="X" k="-109" />
<hkern u1="L" u2="V" k="88" />
<hkern u1="L" u2="P" k="-29" />
<hkern u1="L" u2="M" k="-20" />
<hkern u1="L" u2="J" k="-98" />
<hkern u1="L" u2="F" k="-20" />
<hkern u1="L" u2="&#x3f;" k="47" />
<hkern u1="L" u2="&#x2a;" k="211" />
<hkern u1="L" u2="&#x29;" k="-59" />
<hkern u1="L" u2="&#x26;" k="-14" />
<hkern u1="M" u2="&#x178;" k="16" />
<hkern u1="M" u2="&#xdd;" k="16" />
<hkern u1="M" u2="w" k="-25" />
<hkern u1="M" u2="Y" k="16" />
<hkern u1="M" u2="T" k="29" />
<hkern u1="M" u2="v" k="-25" />
<hkern u1="M" u2="J" k="-20" />
<hkern u1="O" u2="&#x2122;" k="57" />
<hkern u1="O" u2="X" k="37" />
<hkern u1="O" u2="V" k="27" />
<hkern u1="P" g2="uniFB02" k="-25" />
<hkern u1="P" g2="uniFB01" k="-25" />
<hkern u1="P" u2="&#x2026;" k="180" />
<hkern u1="P" u2="&#x201e;" k="180" />
<hkern u1="P" u2="&#x201a;" k="180" />
<hkern u1="P" u2="&#x178;" k="-25" />
<hkern u1="P" u2="&#x153;" k="27" />
<hkern u1="P" u2="&#xff;" k="-45" />
<hkern u1="P" u2="&#xfd;" k="-45" />
<hkern u1="P" u2="&#xf8;" k="27" />
<hkern u1="P" u2="&#xf6;" k="27" />
<hkern u1="P" u2="&#xf5;" k="27" />
<hkern u1="P" u2="&#xf4;" k="27" />
<hkern u1="P" u2="&#xf3;" k="27" />
<hkern u1="P" u2="&#xf2;" k="27" />
<hkern u1="P" u2="&#xf0;" k="27" />
<hkern u1="P" u2="&#xe6;" k="41" />
<hkern u1="P" u2="&#xe5;" k="41" />
<hkern u1="P" u2="&#xe4;" k="41" />
<hkern u1="P" u2="&#xe3;" k="41" />
<hkern u1="P" u2="&#xe2;" k="41" />
<hkern u1="P" u2="&#xe1;" k="41" />
<hkern u1="P" u2="&#xe0;" k="41" />
<hkern u1="P" u2="&#xdd;" k="-25" />
<hkern u1="P" u2="&#xc6;" k="195" />
<hkern u1="P" u2="&#xc5;" k="131" />
<hkern u1="P" u2="&#xc4;" k="131" />
<hkern u1="P" u2="&#xc3;" k="131" />
<hkern u1="P" u2="&#xc2;" k="131" />
<hkern u1="P" u2="&#xc1;" k="131" />
<hkern u1="P" u2="&#xc0;" k="131" />
<hkern u1="P" u2="y" k="-45" />
<hkern u1="P" u2="w" k="-45" />
<hkern u1="P" u2="t" k="-25" />
<hkern u1="P" u2="s" k="20" />
<hkern u1="P" u2="o" k="27" />
<hkern u1="P" u2="f" k="-25" />
<hkern u1="P" u2="a" k="41" />
<hkern u1="P" u2="Z" k="37" />
<hkern u1="P" u2="Y" k="-25" />
<hkern u1="P" u2="W" k="-25" />
<hkern u1="P" u2="A" k="131" />
<hkern u1="P" u2="&#x2e;" k="180" />
<hkern u1="P" u2="&#x2c;" k="180" />
<hkern u1="P" u2="x" k="-25" />
<hkern u1="P" u2="v" k="-45" />
<hkern u1="P" u2="X" k="37" />
<hkern u1="P" u2="J" k="88" />
<hkern u1="P" u2="&#x2f;" k="129" />
<hkern u1="P" u2="&#x26;" k="47" />
<hkern u1="Q" u2="&#xc5;" k="33" />
<hkern u1="Q" u2="&#xc4;" k="33" />
<hkern u1="Q" u2="&#xc3;" k="33" />
<hkern u1="Q" u2="&#xc2;" k="33" />
<hkern u1="Q" u2="&#xc1;" k="33" />
<hkern u1="Q" u2="&#xc0;" k="33" />
<hkern u1="Q" u2="T" k="61" />
<hkern u1="Q" u2="A" k="33" />
<hkern u1="Q" u2="&#x2122;" k="57" />
<hkern u1="R" u2="&#x2122;" k="78" />
<hkern u1="R" u2="x" k="-45" />
<hkern u1="R" u2="v" k="-25" />
<hkern u1="R" u2="\" k="37" />
<hkern u1="R" u2="X" k="-76" />
<hkern u1="R" u2="J" k="-55" />
<hkern u1="R" u2="&#x29;" k="-25" />
<hkern u1="S" u2="v" k="37" />
<hkern u1="S" u2="V" k="10" />
<hkern u1="S" u2="J" k="-20" />
<hkern u1="T" u2="&#x7d;" k="-57" />
<hkern u1="T" u2="x" k="109" />
<hkern u1="T" u2="v" k="68" />
<hkern u1="T" u2="q" k="131" />
<hkern u1="T" u2="p" k="129" />
<hkern u1="T" u2="m" k="129" />
<hkern u1="T" u2="h" k="37" />
<hkern u1="T" u2="b" k="16" />
<hkern u1="T" u2="]" k="-88" />
<hkern u1="T" u2="V" k="-92" />
<hkern u1="T" u2="Q" k="61" />
<hkern u1="T" u2="M" k="29" />
<hkern u1="T" u2="J" k="78" />
<hkern u1="T" u2="F" k="16" />
<hkern u1="T" u2="E" k="27" />
<hkern u1="T" u2="&#x40;" k="37" />
<hkern u1="T" u2="&#x3f;" k="-37" />
<hkern u1="T" u2="&#x2a;" k="-66" />
<hkern u1="T" u2="&#x29;" k="-86" />
<hkern u1="U" u2="v" k="-45" />
<hkern u1="V" u2="&#x2026;" k="39" />
<hkern u1="V" u2="&#x201e;" k="39" />
<hkern u1="V" u2="&#x201d;" k="-57" />
<hkern u1="V" u2="&#x201a;" k="39" />
<hkern u1="V" u2="&#x2019;" k="-57" />
<hkern u1="V" u2="&#x178;" k="-66" />
<hkern u1="V" u2="&#x153;" k="61" />
<hkern u1="V" u2="&#x152;" k="4" />
<hkern u1="V" u2="&#xff;" k="-45" />
<hkern u1="V" u2="&#xfd;" k="-45" />
<hkern u1="V" u2="&#xfc;" k="12" />
<hkern u1="V" u2="&#xfb;" k="12" />
<hkern u1="V" u2="&#xfa;" k="12" />
<hkern u1="V" u2="&#xf9;" k="12" />
<hkern u1="V" u2="&#xf8;" k="61" />
<hkern u1="V" u2="&#xf6;" k="61" />
<hkern u1="V" u2="&#xf5;" k="61" />
<hkern u1="V" u2="&#xf4;" k="61" />
<hkern u1="V" u2="&#xf3;" k="61" />
<hkern u1="V" u2="&#xf2;" k="61" />
<hkern u1="V" u2="&#xf1;" k="29" />
<hkern u1="V" u2="&#xf0;" k="61" />
<hkern u1="V" u2="&#xeb;" k="61" />
<hkern u1="V" u2="&#xea;" k="61" />
<hkern u1="V" u2="&#xe9;" k="61" />
<hkern u1="V" u2="&#xe8;" k="61" />
<hkern u1="V" u2="&#xe7;" k="61" />
<hkern u1="V" u2="&#xe6;" k="57" />
<hkern u1="V" u2="&#xe5;" k="57" />
<hkern u1="V" u2="&#xe4;" k="57" />
<hkern u1="V" u2="&#xe3;" k="57" />
<hkern u1="V" u2="&#xe2;" k="57" />
<hkern u1="V" u2="&#xe1;" k="57" />
<hkern u1="V" u2="&#xe0;" k="57" />
<hkern u1="V" u2="&#xdd;" k="-66" />
<hkern u1="V" u2="&#xd8;" k="4" />
<hkern u1="V" u2="&#xd6;" k="4" />
<hkern u1="V" u2="&#xd5;" k="4" />
<hkern u1="V" u2="&#xd4;" k="4" />
<hkern u1="V" u2="&#xd3;" k="4" />
<hkern u1="V" u2="&#xd2;" k="4" />
<hkern u1="V" u2="&#xc6;" k="143" />
<hkern u1="V" u2="&#xc5;" k="74" />
<hkern u1="V" u2="&#xc4;" k="74" />
<hkern u1="V" u2="&#xc3;" k="74" />
<hkern u1="V" u2="&#xc2;" k="74" />
<hkern u1="V" u2="&#xc1;" k="74" />
<hkern u1="V" u2="&#xc0;" k="74" />
<hkern u1="V" u2="y" k="-45" />
<hkern u1="V" u2="w" k="-37" />
<hkern u1="V" u2="u" k="12" />
<hkern u1="V" u2="t" k="-20" />
<hkern u1="V" u2="s" k="47" />
<hkern u1="V" u2="o" k="61" />
<hkern u1="V" u2="n" k="29" />
<hkern u1="V" u2="g" k="61" />
<hkern u1="V" u2="e" k="61" />
<hkern u1="V" u2="d" k="61" />
<hkern u1="V" u2="c" k="61" />
<hkern u1="V" u2="a" k="57" />
<hkern u1="V" u2="Z" k="-25" />
<hkern u1="V" u2="Y" k="-66" />
<hkern u1="V" u2="W" k="-66" />
<hkern u1="V" u2="T" k="-92" />
<hkern u1="V" u2="S" k="-39" />
<hkern u1="V" u2="O" k="4" />
<hkern u1="V" u2="A" k="74" />
<hkern u1="V" u2="&#x2e;" k="39" />
<hkern u1="V" u2="&#x2c;" k="39" />
<hkern u1="V" u2="&#x2122;" k="-39" />
<hkern u1="V" u2="&#xbb;" k="16" />
<hkern u1="V" u2="&#x7d;" k="-57" />
<hkern u1="V" u2="v" k="-35" />
<hkern u1="V" u2="q" k="61" />
<hkern u1="V" u2="p" k="37" />
<hkern u1="V" u2="]" k="-57" />
<hkern u1="V" u2="X" k="-55" />
<hkern u1="V" u2="V" k="-66" />
<hkern u1="V" u2="J" k="63" />
<hkern u1="V" u2="F" k="16" />
<hkern u1="V" u2="&#x40;" k="37" />
<hkern u1="V" u2="&#x3f;" k="-29" />
<hkern u1="V" u2="&#x2f;" k="143" />
<hkern u1="V" u2="&#x2a;" k="-39" />
<hkern u1="V" u2="&#x29;" k="-86" />
<hkern u1="V" u2="&#x26;" k="27" />
<hkern u1="W" u2="&#x2122;" k="-49" />
<hkern u1="W" u2="&#x7d;" k="-96" />
<hkern u1="W" u2="x" k="-63" />
<hkern u1="W" u2="v" k="-76" />
<hkern u1="W" u2="p" k="20" />
<hkern u1="W" u2="b" k="4" />
<hkern u1="W" u2="]" k="-78" />
<hkern u1="W" u2="X" k="-66" />
<hkern u1="W" u2="V" k="-66" />
<hkern u1="W" u2="L" k="-45" />
<hkern u1="W" u2="J" k="51" />
<hkern u1="W" u2="H" k="-20" />
<hkern u1="W" u2="F" k="6" />
<hkern u1="W" u2="B" k="-29" />
<hkern u1="W" u2="&#x40;" k="6" />
<hkern u1="W" u2="&#x3f;" k="-39" />
<hkern u1="W" u2="&#x2f;" k="98" />
<hkern u1="W" u2="&#x2a;" k="-59" />
<hkern u1="W" u2="&#x29;" k="-66" />
<hkern u1="X" g2="uniFB02" k="2" />
<hkern u1="X" g2="uniFB01" k="2" />
<hkern u1="X" u2="&#x201d;" k="-39" />
<hkern u1="X" u2="&#x2019;" k="-39" />
<hkern u1="X" u2="&#x178;" k="-25" />
<hkern u1="X" u2="&#x153;" k="27" />
<hkern u1="X" u2="&#x152;" k="37" />
<hkern u1="X" u2="&#xff;" k="16" />
<hkern u1="X" u2="&#xfd;" k="16" />
<hkern u1="X" u2="&#xf8;" k="27" />
<hkern u1="X" u2="&#xf6;" k="27" />
<hkern u1="X" u2="&#xf5;" k="27" />
<hkern u1="X" u2="&#xf4;" k="27" />
<hkern u1="X" u2="&#xf3;" k="27" />
<hkern u1="X" u2="&#xf2;" k="27" />
<hkern u1="X" u2="&#xf1;" k="6" />
<hkern u1="X" u2="&#xf0;" k="27" />
<hkern u1="X" u2="&#xeb;" k="27" />
<hkern u1="X" u2="&#xea;" k="27" />
<hkern u1="X" u2="&#xe9;" k="27" />
<hkern u1="X" u2="&#xe8;" k="27" />
<hkern u1="X" u2="&#xe7;" k="27" />
<hkern u1="X" u2="&#xdd;" k="-25" />
<hkern u1="X" u2="&#xd8;" k="37" />
<hkern u1="X" u2="&#xd6;" k="37" />
<hkern u1="X" u2="&#xd5;" k="37" />
<hkern u1="X" u2="&#xd4;" k="37" />
<hkern u1="X" u2="&#xd3;" k="37" />
<hkern u1="X" u2="&#xd2;" k="37" />
<hkern u1="X" u2="&#xc5;" k="-35" />
<hkern u1="X" u2="&#xc4;" k="-35" />
<hkern u1="X" u2="&#xc3;" k="-35" />
<hkern u1="X" u2="&#xc2;" k="-35" />
<hkern u1="X" u2="&#xc1;" k="-35" />
<hkern u1="X" u2="&#xc0;" k="-35" />
<hkern u1="X" u2="z" k="-45" />
<hkern u1="X" u2="y" k="16" />
<hkern u1="X" u2="w" k="6" />
<hkern u1="X" u2="t" k="12" />
<hkern u1="X" u2="s" k="-25" />
<hkern u1="X" u2="o" k="27" />
<hkern u1="X" u2="n" k="6" />
<hkern u1="X" u2="g" k="27" />
<hkern u1="X" u2="f" k="2" />
<hkern u1="X" u2="e" k="27" />
<hkern u1="X" u2="d" k="27" />
<hkern u1="X" u2="c" k="27" />
<hkern u1="X" u2="Z" k="-35" />
<hkern u1="X" u2="Y" k="-25" />
<hkern u1="X" u2="W" k="-25" />
<hkern u1="X" u2="S" k="-25" />
<hkern u1="X" u2="O" k="37" />
<hkern u1="X" u2="A" k="-35" />
<hkern u1="X" u2="&#xbb;" k="16" />
<hkern u1="X" u2="&#x7d;" k="-49" />
<hkern u1="X" u2="x" k="-20" />
<hkern u1="X" u2="v" k="12" />
<hkern u1="X" u2="q" k="27" />
<hkern u1="X" u2="]" k="-59" />
<hkern u1="X" u2="V" k="-41" />
<hkern u1="X" u2="J" k="-76" />
<hkern u1="Y" u2="&#x2122;" k="-41" />
<hkern u1="Y" u2="&#xbb;" k="16" />
<hkern u1="Y" u2="&#x7d;" k="-70" />
<hkern u1="Y" u2="x" k="16" />
<hkern u1="Y" u2="v" k="-66" />
<hkern u1="Y" u2="q" k="68" />
<hkern u1="Y" u2="p" k="74" />
<hkern u1="Y" u2="h" k="12" />
<hkern u1="Y" u2="b" k="27" />
<hkern u1="Y" u2="]" k="-57" />
<hkern u1="Y" u2="X" k="-41" />
<hkern u1="Y" u2="V" k="-66" />
<hkern u1="Y" u2="M" k="27" />
<hkern u1="Y" u2="L" k="-66" />
<hkern u1="Y" u2="J" k="88" />
<hkern u1="Y" u2="E" k="16" />
<hkern u1="Y" u2="&#x40;" k="6" />
<hkern u1="Y" u2="&#x3f;" k="-39" />
<hkern u1="Y" u2="&#x2f;" k="156" />
<hkern u1="Y" u2="&#x2a;" k="-39" />
<hkern u1="Y" u2="&#x29;" k="-47" />
<hkern u1="Z" u2="&#x7d;" k="-29" />
<hkern u1="Z" u2="v" k="16" />
<hkern u1="Z" u2="p" k="2" />
<hkern u1="Z" u2="]" k="-35" />
<hkern u1="Z" u2="V" k="-25" />
<hkern u1="Z" u2="J" k="-45" />
<hkern u1="Z" u2="&#x40;" k="16" />
<hkern u1="Z" u2="&#x26;" k="47" />
<hkern u1="[" u2="&#x178;" k="-39" />
<hkern u1="[" u2="&#xdd;" k="-39" />
<hkern u1="[" u2="z" k="-35" />
<hkern u1="[" u2="j" k="-166" />
<hkern u1="[" u2="Z" k="-35" />
<hkern u1="[" u2="Y" k="-39" />
<hkern u1="[" u2="W" k="-59" />
<hkern u1="[" u2="T" k="-80" />
<hkern u1="[" u2="X" k="-49" />
<hkern u1="[" u2="V" k="-59" />
<hkern u1="\" u2="&#xff;" k="47" />
<hkern u1="\" u2="&#xfd;" k="47" />
<hkern u1="\" u2="y" k="47" />
<hkern u1="\" u2="v" k="78" />
<hkern u1="a" u2="&#x2122;" k="119" />
<hkern u1="a" u2="v" k="6" />
<hkern u1="a" u2="\" k="57" />
<hkern u1="a" u2="&#x2a;" k="47" />
<hkern u1="b" u2="&#x201d;" k="68" />
<hkern u1="b" u2="&#x2019;" k="68" />
<hkern u1="b" u2="&#x2122;" k="109" />
<hkern u1="c" u2="&#x2122;" k="16" />
<hkern u1="c" u2="x" k="-33" />
<hkern u1="c" u2="v" k="-41" />
<hkern u1="c" u2="\" k="16" />
<hkern u1="e" u2="&#x2122;" k="57" />
<hkern u1="e" u2="]" k="-25" />
<hkern u1="e" u2="\" k="78" />
<hkern u1="f" g2="uniFB02" k="-37" />
<hkern u1="f" g2="uniFB01" k="-37" />
<hkern u1="f" u2="&#x2026;" k="109" />
<hkern u1="f" u2="&#x201e;" k="109" />
<hkern u1="f" u2="&#x201d;" k="-154" />
<hkern u1="f" u2="&#x201a;" k="109" />
<hkern u1="f" u2="&#x2019;" k="-154" />
<hkern u1="f" u2="&#x153;" k="27" />
<hkern u1="f" u2="&#xff;" k="-45" />
<hkern u1="f" u2="&#xfd;" k="-45" />
<hkern u1="f" u2="&#xf8;" k="27" />
<hkern u1="f" u2="&#xf6;" k="27" />
<hkern u1="f" u2="&#xf5;" k="27" />
<hkern u1="f" u2="&#xf4;" k="27" />
<hkern u1="f" u2="&#xf3;" k="27" />
<hkern u1="f" u2="&#xf2;" k="27" />
<hkern u1="f" u2="&#xf0;" k="27" />
<hkern u1="f" u2="&#xef;" k="-20" />
<hkern u1="f" u2="&#xee;" k="-20" />
<hkern u1="f" u2="&#xed;" k="-20" />
<hkern u1="f" u2="&#xec;" k="-20" />
<hkern u1="f" u2="&#xe6;" k="31" />
<hkern u1="f" u2="&#xe5;" k="31" />
<hkern u1="f" u2="&#xe4;" k="31" />
<hkern u1="f" u2="&#xe3;" k="31" />
<hkern u1="f" u2="&#xe2;" k="31" />
<hkern u1="f" u2="&#xe1;" k="31" />
<hkern u1="f" u2="&#xe0;" k="31" />
<hkern u1="f" u2="y" k="-45" />
<hkern u1="f" u2="w" k="-41" />
<hkern u1="f" u2="t" k="-41" />
<hkern u1="f" u2="o" k="27" />
<hkern u1="f" u2="l" k="-35" />
<hkern u1="f" u2="j" k="-41" />
<hkern u1="f" u2="i" k="-20" />
<hkern u1="f" u2="f" k="-37" />
<hkern u1="f" u2="a" k="31" />
<hkern u1="f" u2="&#x3b;" k="-51" />
<hkern u1="f" u2="&#x3a;" k="-51" />
<hkern u1="f" u2="&#x2e;" k="109" />
<hkern u1="f" u2="&#x2c;" k="109" />
<hkern u1="f" u2="&#x27;" k="-127" />
<hkern u1="f" u2="&#x22;" k="-127" />
<hkern u1="f" u2="&#x2122;" k="-176" />
<hkern u1="f" u2="&#xfe;" k="-41" />
<hkern u1="f" u2="&#x7d;" k="-195" />
<hkern u1="f" u2="v" k="-43" />
<hkern u1="f" u2="k" k="-41" />
<hkern u1="f" u2="h" k="-41" />
<hkern u1="f" u2="b" k="-41" />
<hkern u1="f" u2="]" k="-197" />
<hkern u1="f" u2="\" k="-158" />
<hkern u1="f" u2="&#x3f;" k="-158" />
<hkern u1="f" u2="&#x2a;" k="-168" />
<hkern u1="f" u2="&#x29;" k="-137" />
<hkern u1="f" u2="&#x21;" k="-41" />
<hkern u1="g" u2="&#x2122;" k="68" />
<hkern u1="g" u2="]" k="-66" />
<hkern u1="g" u2="\" k="27" />
<hkern u1="g" u2="&#x2a;" k="55" />
<hkern u1="g" u2="&#x29;" k="-86" />
<hkern u1="h" u2="&#x2122;" k="109" />
<hkern u1="i" u2="&#x2f;" k="16" />
<hkern u1="i" u2="&#x2a;" k="-25" />
<hkern u1="j" u2="\" k="20" />
<hkern u1="j" u2="&#x2f;" k="16" />
<hkern u1="k" u2="v" k="-20" />
<hkern u1="k" u2="q" k="33" />
<hkern u1="l" u2="&#x2122;" k="16" />
<hkern u1="l" u2="&#x7d;" k="-88" />
<hkern u1="l" u2="x" k="-35" />
<hkern u1="l" u2="v" k="4" />
<hkern u1="l" u2="&#x2f;" k="-59" />
<hkern u1="l" u2="&#x2a;" k="29" />
<hkern u1="l" u2="&#x29;" k="-57" />
<hkern u1="m" u2="&#x201d;" k="57" />
<hkern u1="m" u2="&#x2019;" k="57" />
<hkern u1="m" u2="&#x2122;" k="109" />
<hkern u1="n" u2="&#x2122;" k="109" />
<hkern u1="n" u2="v" k="6" />
<hkern u1="n" u2="]" k="-25" />
<hkern u1="n" u2="\" k="66" />
<hkern u1="n" u2="&#x2a;" k="125" />
<hkern u1="o" u2="&#x2122;" k="109" />
<hkern u1="o" u2="x" k="2" />
<hkern u1="o" u2="v" k="6" />
<hkern u1="o" u2="\" k="86" />
<hkern u1="o" u2="&#x2a;" k="68" />
<hkern u1="p" u2="&#x201d;" k="88" />
<hkern u1="p" u2="&#x2019;" k="88" />
<hkern u1="p" u2="&#x2122;" k="109" />
<hkern u1="q" u2="j" k="-156" />
<hkern u1="q" u2="&#x2122;" k="57" />
<hkern u1="q" u2="]" k="-55" />
<hkern u1="q" u2="\" k="27" />
<hkern u1="q" u2="&#x2a;" k="66" />
<hkern u1="r" u2="x" k="-25" />
<hkern u1="r" u2="v" k="-45" />
<hkern u1="r" u2="q" k="16" />
<hkern u1="r" u2="\" k="31" />
<hkern u1="s" u2="&#x2122;" k="57" />
<hkern u1="s" u2="\" k="47" />
<hkern u1="s" u2="&#x2a;" k="55" />
<hkern u1="t" u2="&#x7d;" k="-98" />
<hkern u1="t" u2="x" k="-33" />
<hkern u1="t" u2="v" k="-25" />
<hkern u1="t" u2="]" k="-88" />
<hkern u1="t" u2="\" k="16" />
<hkern u1="t" u2="&#x2f;" k="-57" />
<hkern u1="t" u2="&#x2a;" k="23" />
<hkern u1="t" u2="&#x29;" k="-86" />
<hkern u1="u" u2="&#x2122;" k="33" />
<hkern u1="u" u2="\" k="16" />
<hkern u1="u" u2="&#x2a;" k="47" />
<hkern u1="v" g2="uniFB02" k="-41" />
<hkern u1="v" g2="uniFB01" k="-41" />
<hkern u1="v" u2="&#x2026;" k="109" />
<hkern u1="v" u2="&#x201e;" k="109" />
<hkern u1="v" u2="&#x201d;" k="-39" />
<hkern u1="v" u2="&#x201a;" k="109" />
<hkern u1="v" u2="&#x2019;" k="-39" />
<hkern u1="v" u2="&#x153;" k="2" />
<hkern u1="v" u2="&#xff;" k="-45" />
<hkern u1="v" u2="&#xfd;" k="-45" />
<hkern u1="v" u2="&#xf8;" k="2" />
<hkern u1="v" u2="&#xf6;" k="2" />
<hkern u1="v" u2="&#xf5;" k="2" />
<hkern u1="v" u2="&#xf4;" k="2" />
<hkern u1="v" u2="&#xf3;" k="2" />
<hkern u1="v" u2="&#xf2;" k="2" />
<hkern u1="v" u2="&#xf0;" k="2" />
<hkern u1="v" u2="y" k="-45" />
<hkern u1="v" u2="w" k="-45" />
<hkern u1="v" u2="t" k="-45" />
<hkern u1="v" u2="o" k="2" />
<hkern u1="v" u2="f" k="-41" />
<hkern u1="v" u2="&#x3b;" k="37" />
<hkern u1="v" u2="&#x3a;" k="37" />
<hkern u1="v" u2="&#x2e;" k="109" />
<hkern u1="v" u2="&#x2c;" k="109" />
<hkern u1="v" u2="x" k="-45" />
<hkern u1="v" u2="v" k="-45" />
<hkern u1="w" u2="x" k="-25" />
<hkern u1="w" u2="v" k="-45" />
<hkern u1="x" u2="&#x201d;" k="-57" />
<hkern u1="x" u2="&#x2019;" k="-57" />
<hkern u1="x" u2="&#x153;" k="2" />
<hkern u1="x" u2="&#xff;" k="-45" />
<hkern u1="x" u2="&#xfd;" k="-45" />
<hkern u1="x" u2="&#xf8;" k="2" />
<hkern u1="x" u2="&#xf6;" k="2" />
<hkern u1="x" u2="&#xf5;" k="2" />
<hkern u1="x" u2="&#xf4;" k="2" />
<hkern u1="x" u2="&#xf3;" k="2" />
<hkern u1="x" u2="&#xf2;" k="2" />
<hkern u1="x" u2="&#xf0;" k="2" />
<hkern u1="x" u2="z" k="-25" />
<hkern u1="x" u2="y" k="-45" />
<hkern u1="x" u2="w" k="-45" />
<hkern u1="x" u2="t" k="-25" />
<hkern u1="x" u2="s" k="-20" />
<hkern u1="x" u2="o" k="2" />
<hkern u1="x" u2="&#x2122;" k="37" />
<hkern u1="x" u2="x" k="-25" />
<hkern u1="x" u2="v" k="-45" />
<hkern u1="x" u2="]" k="-100" />
<hkern u1="x" u2="\" k="37" />
<hkern u1="y" u2="&#x2122;" k="27" />
<hkern u1="y" u2="x" k="-41" />
<hkern u1="y" u2="v" k="-45" />
<hkern u1="y" u2="&#x2c;" k="135" />
<hkern u1="z" u2="&#x2122;" k="47" />
<hkern u1="z" u2="v" k="-20" />
<hkern u1="z" u2="]" k="-35" />
<hkern u1="&#x7b;" u2="&#x178;" k="-68" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-68" />
<hkern u1="&#x7b;" u2="j" k="-233" />
<hkern u1="&#x7b;" u2="Z" k="-49" />
<hkern u1="&#x7b;" u2="Y" k="-68" />
<hkern u1="&#x7b;" u2="W" k="-57" />
<hkern u1="&#x7b;" u2="T" k="-78" />
<hkern u1="&#x7b;" u2="X" k="-57" />
<hkern u1="&#x7b;" u2="V" k="-57" />
<hkern u1="&#x7b;" u2="J" k="-68" />
<hkern u1="&#xab;" u2="&#xc5;" k="-35" />
<hkern u1="&#xab;" u2="&#xc4;" k="-35" />
<hkern u1="&#xab;" u2="&#xc3;" k="-35" />
<hkern u1="&#xab;" u2="&#xc2;" k="-35" />
<hkern u1="&#xab;" u2="&#xc1;" k="-35" />
<hkern u1="&#xab;" u2="&#xc0;" k="-35" />
<hkern u1="&#xab;" u2="A" k="-35" />
<hkern u1="&#xab;" u2="X" k="16" />
<hkern u1="&#xab;" u2="V" k="16" />
<hkern u1="&#xbf;" g2="uniFB02" k="-57" />
<hkern u1="&#xbf;" g2="uniFB01" k="-57" />
<hkern u1="&#xbf;" u2="&#xff;" k="-39" />
<hkern u1="&#xbf;" u2="&#xfd;" k="-39" />
<hkern u1="&#xbf;" u2="y" k="-39" />
<hkern u1="&#xbf;" u2="w" k="-47" />
<hkern u1="&#xbf;" u2="t" k="-39" />
<hkern u1="&#xbf;" u2="j" k="-301" />
<hkern u1="&#xbf;" u2="f" k="-57" />
<hkern u1="&#xbf;" u2="T" k="121" />
<hkern u1="&#xbf;" u2="v" k="-57" />
<hkern u1="&#xbf;" u2="p" k="-57" />
<hkern u1="&#xc0;" u2="&#x2122;" k="221" />
<hkern u1="&#xc0;" u2="&#x203a;" k="-35" />
<hkern u1="&#xc0;" u2="&#xbb;" k="-35" />
<hkern u1="&#xc0;" u2="&#xae;" k="41" />
<hkern u1="&#xc0;" u2="&#xa9;" k="41" />
<hkern u1="&#xc0;" u2="v" k="86" />
<hkern u1="&#xc0;" u2="q" k="61" />
<hkern u1="&#xc0;" u2="p" k="20" />
<hkern u1="&#xc0;" u2="m" k="20" />
<hkern u1="&#xc0;" u2="k" k="20" />
<hkern u1="&#xc0;" u2="h" k="20" />
<hkern u1="&#xc0;" u2="b" k="61" />
<hkern u1="&#xc0;" u2="\" k="139" />
<hkern u1="&#xc0;" u2="X" k="-35" />
<hkern u1="&#xc0;" u2="V" k="94" />
<hkern u1="&#xc0;" u2="Q" k="53" />
<hkern u1="&#xc0;" u2="J" k="-55" />
<hkern u1="&#xc0;" u2="E" k="82" />
<hkern u1="&#xc0;" u2="&#x3f;" k="47" />
<hkern u1="&#xc0;" u2="&#x2a;" k="145" />
<hkern u1="&#xc1;" u2="&#x2122;" k="221" />
<hkern u1="&#xc1;" u2="&#x203a;" k="-35" />
<hkern u1="&#xc1;" u2="&#xbb;" k="-35" />
<hkern u1="&#xc1;" u2="&#xae;" k="41" />
<hkern u1="&#xc1;" u2="&#xa9;" k="41" />
<hkern u1="&#xc1;" u2="v" k="86" />
<hkern u1="&#xc1;" u2="q" k="61" />
<hkern u1="&#xc1;" u2="p" k="20" />
<hkern u1="&#xc1;" u2="m" k="20" />
<hkern u1="&#xc1;" u2="k" k="20" />
<hkern u1="&#xc1;" u2="h" k="20" />
<hkern u1="&#xc1;" u2="b" k="61" />
<hkern u1="&#xc1;" u2="\" k="139" />
<hkern u1="&#xc1;" u2="X" k="-35" />
<hkern u1="&#xc1;" u2="V" k="94" />
<hkern u1="&#xc1;" u2="Q" k="53" />
<hkern u1="&#xc1;" u2="J" k="-55" />
<hkern u1="&#xc1;" u2="E" k="82" />
<hkern u1="&#xc1;" u2="&#x3f;" k="47" />
<hkern u1="&#xc1;" u2="&#x2a;" k="145" />
<hkern u1="&#xc2;" u2="&#x2122;" k="221" />
<hkern u1="&#xc2;" u2="&#x203a;" k="-35" />
<hkern u1="&#xc2;" u2="&#xbb;" k="-35" />
<hkern u1="&#xc2;" u2="&#xae;" k="41" />
<hkern u1="&#xc2;" u2="&#xa9;" k="41" />
<hkern u1="&#xc2;" u2="v" k="86" />
<hkern u1="&#xc2;" u2="q" k="61" />
<hkern u1="&#xc2;" u2="p" k="20" />
<hkern u1="&#xc2;" u2="m" k="20" />
<hkern u1="&#xc2;" u2="k" k="20" />
<hkern u1="&#xc2;" u2="h" k="20" />
<hkern u1="&#xc2;" u2="b" k="61" />
<hkern u1="&#xc2;" u2="\" k="139" />
<hkern u1="&#xc2;" u2="X" k="-35" />
<hkern u1="&#xc2;" u2="V" k="94" />
<hkern u1="&#xc2;" u2="Q" k="53" />
<hkern u1="&#xc2;" u2="J" k="-55" />
<hkern u1="&#xc2;" u2="E" k="82" />
<hkern u1="&#xc2;" u2="&#x3f;" k="47" />
<hkern u1="&#xc2;" u2="&#x2a;" k="145" />
<hkern u1="&#xc3;" u2="&#x2122;" k="221" />
<hkern u1="&#xc3;" u2="&#x203a;" k="-35" />
<hkern u1="&#xc3;" u2="&#xbb;" k="-35" />
<hkern u1="&#xc3;" u2="&#xae;" k="41" />
<hkern u1="&#xc3;" u2="&#xa9;" k="41" />
<hkern u1="&#xc3;" u2="v" k="86" />
<hkern u1="&#xc3;" u2="q" k="61" />
<hkern u1="&#xc3;" u2="p" k="20" />
<hkern u1="&#xc3;" u2="m" k="20" />
<hkern u1="&#xc3;" u2="k" k="20" />
<hkern u1="&#xc3;" u2="h" k="20" />
<hkern u1="&#xc3;" u2="b" k="61" />
<hkern u1="&#xc3;" u2="\" k="139" />
<hkern u1="&#xc3;" u2="X" k="-35" />
<hkern u1="&#xc3;" u2="V" k="94" />
<hkern u1="&#xc3;" u2="Q" k="53" />
<hkern u1="&#xc3;" u2="J" k="-55" />
<hkern u1="&#xc3;" u2="E" k="82" />
<hkern u1="&#xc3;" u2="&#x3f;" k="47" />
<hkern u1="&#xc3;" u2="&#x2a;" k="145" />
<hkern u1="&#xc4;" u2="&#x2122;" k="221" />
<hkern u1="&#xc4;" u2="&#x203a;" k="-35" />
<hkern u1="&#xc4;" u2="&#xbb;" k="-35" />
<hkern u1="&#xc4;" u2="&#xae;" k="41" />
<hkern u1="&#xc4;" u2="&#xa9;" k="41" />
<hkern u1="&#xc4;" u2="v" k="86" />
<hkern u1="&#xc4;" u2="q" k="61" />
<hkern u1="&#xc4;" u2="p" k="20" />
<hkern u1="&#xc4;" u2="m" k="20" />
<hkern u1="&#xc4;" u2="k" k="20" />
<hkern u1="&#xc4;" u2="h" k="20" />
<hkern u1="&#xc4;" u2="b" k="61" />
<hkern u1="&#xc4;" u2="\" k="139" />
<hkern u1="&#xc4;" u2="X" k="-35" />
<hkern u1="&#xc4;" u2="V" k="94" />
<hkern u1="&#xc4;" u2="Q" k="53" />
<hkern u1="&#xc4;" u2="J" k="-55" />
<hkern u1="&#xc4;" u2="E" k="82" />
<hkern u1="&#xc4;" u2="&#x3f;" k="47" />
<hkern u1="&#xc4;" u2="&#x2a;" k="145" />
<hkern u1="&#xc5;" u2="&#x2122;" k="221" />
<hkern u1="&#xc5;" u2="&#x203a;" k="-35" />
<hkern u1="&#xc5;" u2="&#xbb;" k="-35" />
<hkern u1="&#xc5;" u2="&#xae;" k="41" />
<hkern u1="&#xc5;" u2="&#xa9;" k="41" />
<hkern u1="&#xc5;" u2="v" k="86" />
<hkern u1="&#xc5;" u2="q" k="61" />
<hkern u1="&#xc5;" u2="p" k="20" />
<hkern u1="&#xc5;" u2="m" k="20" />
<hkern u1="&#xc5;" u2="k" k="20" />
<hkern u1="&#xc5;" u2="h" k="20" />
<hkern u1="&#xc5;" u2="b" k="61" />
<hkern u1="&#xc5;" u2="\" k="139" />
<hkern u1="&#xc5;" u2="X" k="-35" />
<hkern u1="&#xc5;" u2="V" k="94" />
<hkern u1="&#xc5;" u2="Q" k="53" />
<hkern u1="&#xc5;" u2="J" k="-55" />
<hkern u1="&#xc5;" u2="E" k="82" />
<hkern u1="&#xc5;" u2="&#x3f;" k="47" />
<hkern u1="&#xc5;" u2="&#x2a;" k="145" />
<hkern u1="&#xc6;" u2="V" k="-14" />
<hkern u1="&#xc6;" u2="Q" k="16" />
<hkern u1="&#xc6;" u2="J" k="-37" />
<hkern u1="&#xc6;" u2="B" k="6" />
<hkern u1="&#xc7;" u2="x" k="-25" />
<hkern u1="&#xc7;" u2="q" k="37" />
<hkern u1="&#xc7;" u2="p" k="20" />
<hkern u1="&#xc7;" u2="m" k="20" />
<hkern u1="&#xc7;" u2="X" k="-37" />
<hkern u1="&#xc7;" u2="V" k="-35" />
<hkern u1="&#xc7;" u2="J" k="-68" />
<hkern u1="&#xc7;" u2="&#x29;" k="-76" />
<hkern u1="&#xc8;" u2="V" k="-14" />
<hkern u1="&#xc8;" u2="Q" k="16" />
<hkern u1="&#xc8;" u2="J" k="-37" />
<hkern u1="&#xc8;" u2="B" k="6" />
<hkern u1="&#xc9;" u2="V" k="-14" />
<hkern u1="&#xc9;" u2="Q" k="16" />
<hkern u1="&#xc9;" u2="J" k="-37" />
<hkern u1="&#xc9;" u2="B" k="6" />
<hkern u1="&#xca;" u2="V" k="-14" />
<hkern u1="&#xca;" u2="Q" k="16" />
<hkern u1="&#xca;" u2="J" k="-37" />
<hkern u1="&#xca;" u2="B" k="6" />
<hkern u1="&#xcb;" u2="V" k="-14" />
<hkern u1="&#xcb;" u2="Q" k="16" />
<hkern u1="&#xcb;" u2="J" k="-37" />
<hkern u1="&#xcb;" u2="B" k="6" />
<hkern u1="&#xd0;" u2="X" k="41" />
<hkern u1="&#xd2;" u2="&#x2122;" k="57" />
<hkern u1="&#xd2;" u2="X" k="37" />
<hkern u1="&#xd2;" u2="V" k="27" />
<hkern u1="&#xd3;" u2="&#x2122;" k="57" />
<hkern u1="&#xd3;" u2="X" k="37" />
<hkern u1="&#xd3;" u2="V" k="27" />
<hkern u1="&#xd4;" u2="&#x2122;" k="57" />
<hkern u1="&#xd4;" u2="X" k="37" />
<hkern u1="&#xd4;" u2="V" k="27" />
<hkern u1="&#xd5;" u2="&#x2122;" k="57" />
<hkern u1="&#xd5;" u2="X" k="37" />
<hkern u1="&#xd5;" u2="V" k="27" />
<hkern u1="&#xd6;" u2="&#x2122;" k="57" />
<hkern u1="&#xd6;" u2="X" k="37" />
<hkern u1="&#xd6;" u2="V" k="27" />
<hkern u1="&#xd8;" u2="&#x2122;" k="57" />
<hkern u1="&#xd8;" u2="X" k="37" />
<hkern u1="&#xd8;" u2="V" k="27" />
<hkern u1="&#xd9;" u2="v" k="-45" />
<hkern u1="&#xda;" u2="v" k="-45" />
<hkern u1="&#xdb;" u2="v" k="-45" />
<hkern u1="&#xdc;" u2="v" k="-45" />
<hkern u1="&#xdd;" u2="&#x2122;" k="-41" />
<hkern u1="&#xdd;" u2="&#xbb;" k="16" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-70" />
<hkern u1="&#xdd;" u2="x" k="16" />
<hkern u1="&#xdd;" u2="v" k="-66" />
<hkern u1="&#xdd;" u2="q" k="68" />
<hkern u1="&#xdd;" u2="p" k="74" />
<hkern u1="&#xdd;" u2="h" k="12" />
<hkern u1="&#xdd;" u2="b" k="27" />
<hkern u1="&#xdd;" u2="]" k="-57" />
<hkern u1="&#xdd;" u2="X" k="-41" />
<hkern u1="&#xdd;" u2="V" k="-66" />
<hkern u1="&#xdd;" u2="M" k="27" />
<hkern u1="&#xdd;" u2="L" k="-66" />
<hkern u1="&#xdd;" u2="J" k="88" />
<hkern u1="&#xdd;" u2="E" k="16" />
<hkern u1="&#xdd;" u2="&#x40;" k="6" />
<hkern u1="&#xdd;" u2="&#x3f;" k="-39" />
<hkern u1="&#xdd;" u2="&#x2f;" k="156" />
<hkern u1="&#xdd;" u2="&#x2a;" k="-39" />
<hkern u1="&#xdd;" u2="&#x29;" k="-47" />
<hkern u1="&#xde;" u2="T" k="135" />
<hkern u1="&#xdf;" u2="&#xff;" k="12" />
<hkern u1="&#xdf;" u2="&#xfd;" k="12" />
<hkern u1="&#xdf;" u2="y" k="12" />
<hkern u1="&#xdf;" u2="x" k="16" />
<hkern u1="&#xdf;" u2="v" k="12" />
<hkern u1="&#xe0;" u2="&#x2122;" k="119" />
<hkern u1="&#xe0;" u2="v" k="6" />
<hkern u1="&#xe0;" u2="\" k="57" />
<hkern u1="&#xe0;" u2="&#x2a;" k="47" />
<hkern u1="&#xe1;" u2="&#x2122;" k="119" />
<hkern u1="&#xe1;" u2="v" k="6" />
<hkern u1="&#xe1;" u2="\" k="57" />
<hkern u1="&#xe1;" u2="&#x2a;" k="47" />
<hkern u1="&#xe2;" u2="&#x2122;" k="119" />
<hkern u1="&#xe2;" u2="v" k="6" />
<hkern u1="&#xe2;" u2="\" k="57" />
<hkern u1="&#xe2;" u2="&#x2a;" k="47" />
<hkern u1="&#xe3;" u2="&#x2122;" k="119" />
<hkern u1="&#xe3;" u2="v" k="6" />
<hkern u1="&#xe3;" u2="\" k="57" />
<hkern u1="&#xe3;" u2="&#x2a;" k="47" />
<hkern u1="&#xe4;" u2="&#x2122;" k="119" />
<hkern u1="&#xe4;" u2="v" k="6" />
<hkern u1="&#xe4;" u2="\" k="57" />
<hkern u1="&#xe4;" u2="&#x2a;" k="47" />
<hkern u1="&#xe5;" u2="&#x2122;" k="119" />
<hkern u1="&#xe5;" u2="v" k="6" />
<hkern u1="&#xe5;" u2="\" k="57" />
<hkern u1="&#xe5;" u2="&#x2a;" k="47" />
<hkern u1="&#xe7;" u2="&#x2122;" k="16" />
<hkern u1="&#xe7;" u2="x" k="-33" />
<hkern u1="&#xe7;" u2="v" k="-41" />
<hkern u1="&#xe7;" u2="\" k="16" />
<hkern u1="&#xe8;" u2="&#x2122;" k="57" />
<hkern u1="&#xe8;" u2="]" k="-25" />
<hkern u1="&#xe8;" u2="\" k="78" />
<hkern u1="&#xe9;" u2="&#x2122;" k="57" />
<hkern u1="&#xe9;" u2="]" k="-25" />
<hkern u1="&#xe9;" u2="\" k="78" />
<hkern u1="&#xea;" u2="&#x2122;" k="57" />
<hkern u1="&#xea;" u2="]" k="-25" />
<hkern u1="&#xea;" u2="\" k="78" />
<hkern u1="&#xeb;" u2="&#x2122;" k="57" />
<hkern u1="&#xeb;" u2="]" k="-25" />
<hkern u1="&#xeb;" u2="\" k="78" />
<hkern u1="&#xec;" u2="&#x2f;" k="16" />
<hkern u1="&#xec;" u2="&#x2a;" k="-25" />
<hkern u1="&#xed;" u2="&#x2f;" k="16" />
<hkern u1="&#xed;" u2="&#x2a;" k="-25" />
<hkern u1="&#xee;" u2="&#x2f;" k="16" />
<hkern u1="&#xee;" u2="&#x2a;" k="-25" />
<hkern u1="&#xef;" u2="&#x2f;" k="16" />
<hkern u1="&#xef;" u2="&#x2a;" k="-25" />
<hkern u1="&#xf1;" u2="&#x2122;" k="109" />
<hkern u1="&#xf1;" u2="v" k="6" />
<hkern u1="&#xf1;" u2="]" k="-25" />
<hkern u1="&#xf1;" u2="\" k="66" />
<hkern u1="&#xf1;" u2="&#x2a;" k="125" />
<hkern u1="&#xf2;" u2="&#x2122;" k="109" />
<hkern u1="&#xf2;" u2="x" k="2" />
<hkern u1="&#xf2;" u2="v" k="6" />
<hkern u1="&#xf2;" u2="\" k="86" />
<hkern u1="&#xf2;" u2="&#x2a;" k="68" />
<hkern u1="&#xf3;" u2="&#x2122;" k="109" />
<hkern u1="&#xf3;" u2="x" k="2" />
<hkern u1="&#xf3;" u2="v" k="6" />
<hkern u1="&#xf3;" u2="\" k="86" />
<hkern u1="&#xf3;" u2="&#x2a;" k="68" />
<hkern u1="&#xf4;" u2="&#x2122;" k="109" />
<hkern u1="&#xf4;" u2="x" k="2" />
<hkern u1="&#xf4;" u2="v" k="6" />
<hkern u1="&#xf4;" u2="\" k="86" />
<hkern u1="&#xf4;" u2="&#x2a;" k="68" />
<hkern u1="&#xf5;" u2="&#x2122;" k="109" />
<hkern u1="&#xf5;" u2="x" k="2" />
<hkern u1="&#xf5;" u2="v" k="6" />
<hkern u1="&#xf5;" u2="\" k="86" />
<hkern u1="&#xf5;" u2="&#x2a;" k="68" />
<hkern u1="&#xf6;" u2="&#x2122;" k="109" />
<hkern u1="&#xf6;" u2="x" k="2" />
<hkern u1="&#xf6;" u2="v" k="6" />
<hkern u1="&#xf6;" u2="\" k="86" />
<hkern u1="&#xf6;" u2="&#x2a;" k="68" />
<hkern u1="&#xf8;" u2="&#x2122;" k="109" />
<hkern u1="&#xf8;" u2="x" k="2" />
<hkern u1="&#xf8;" u2="v" k="6" />
<hkern u1="&#xf8;" u2="\" k="86" />
<hkern u1="&#xf8;" u2="&#x2a;" k="68" />
<hkern u1="&#xf9;" u2="&#x2122;" k="33" />
<hkern u1="&#xf9;" u2="\" k="16" />
<hkern u1="&#xf9;" u2="&#x2a;" k="47" />
<hkern u1="&#xfa;" u2="&#x2122;" k="33" />
<hkern u1="&#xfa;" u2="\" k="16" />
<hkern u1="&#xfa;" u2="&#x2a;" k="47" />
<hkern u1="&#xfb;" u2="&#x2122;" k="33" />
<hkern u1="&#xfb;" u2="\" k="16" />
<hkern u1="&#xfb;" u2="&#x2a;" k="47" />
<hkern u1="&#xfc;" u2="&#x2122;" k="33" />
<hkern u1="&#xfc;" u2="\" k="16" />
<hkern u1="&#xfc;" u2="&#x2a;" k="47" />
<hkern u1="&#xfd;" u2="&#x2122;" k="27" />
<hkern u1="&#xfd;" u2="x" k="-41" />
<hkern u1="&#xfd;" u2="v" k="-45" />
<hkern u1="&#xfd;" u2="&#x2c;" k="135" />
<hkern u1="&#xfe;" u2="&#x2122;" k="109" />
<hkern u1="&#xff;" u2="&#x2122;" k="27" />
<hkern u1="&#xff;" u2="x" k="-41" />
<hkern u1="&#xff;" u2="v" k="-45" />
<hkern u1="&#xff;" u2="&#x2c;" k="135" />
<hkern u1="&#x152;" u2="V" k="-14" />
<hkern u1="&#x152;" u2="Q" k="16" />
<hkern u1="&#x152;" u2="J" k="-37" />
<hkern u1="&#x152;" u2="B" k="6" />
<hkern u1="&#x178;" u2="&#x2122;" k="-41" />
<hkern u1="&#x178;" u2="&#xbb;" k="16" />
<hkern u1="&#x178;" u2="&#x7d;" k="-70" />
<hkern u1="&#x178;" u2="x" k="16" />
<hkern u1="&#x178;" u2="v" k="-66" />
<hkern u1="&#x178;" u2="q" k="68" />
<hkern u1="&#x178;" u2="p" k="74" />
<hkern u1="&#x178;" u2="h" k="12" />
<hkern u1="&#x178;" u2="b" k="27" />
<hkern u1="&#x178;" u2="]" k="-57" />
<hkern u1="&#x178;" u2="X" k="-41" />
<hkern u1="&#x178;" u2="V" k="-66" />
<hkern u1="&#x178;" u2="M" k="27" />
<hkern u1="&#x178;" u2="L" k="-66" />
<hkern u1="&#x178;" u2="J" k="88" />
<hkern u1="&#x178;" u2="E" k="16" />
<hkern u1="&#x178;" u2="&#x40;" k="6" />
<hkern u1="&#x178;" u2="&#x3f;" k="-39" />
<hkern u1="&#x178;" u2="&#x2f;" k="156" />
<hkern u1="&#x178;" u2="&#x2a;" k="-39" />
<hkern u1="&#x178;" u2="&#x29;" k="-47" />
<hkern u1="&#x2018;" u2="v" k="-39" />
<hkern u1="&#x2018;" u2="V" k="-31" />
<hkern u1="&#x2018;" u2="M" k="37" />
<hkern u1="&#x2018;" u2="J" k="135" />
<hkern u1="&#x201a;" u2="v" k="109" />
<hkern u1="&#x201a;" u2="V" k="78" />
<hkern u1="&#x201a;" u2="J" k="-49" />
<hkern u1="&#x201c;" u2="v" k="-39" />
<hkern u1="&#x201c;" u2="V" k="-31" />
<hkern u1="&#x201c;" u2="M" k="37" />
<hkern u1="&#x201c;" u2="J" k="135" />
<hkern u1="&#x201e;" u2="v" k="109" />
<hkern u1="&#x201e;" u2="V" k="78" />
<hkern u1="&#x201e;" u2="J" k="-49" />
<hkern u1="&#x2026;" u2="v" k="109" />
<hkern u1="&#x2026;" u2="V" k="78" />
<hkern u1="&#x2026;" u2="J" k="-49" />
<hkern u1="&#x2039;" u2="&#xc5;" k="-35" />
<hkern u1="&#x2039;" u2="&#xc4;" k="-35" />
<hkern u1="&#x2039;" u2="&#xc3;" k="-35" />
<hkern u1="&#x2039;" u2="&#xc2;" k="-35" />
<hkern u1="&#x2039;" u2="&#xc1;" k="-35" />
<hkern u1="&#x2039;" u2="&#xc0;" k="-35" />
<hkern u1="&#x2039;" u2="A" k="-35" />
<hkern g1="uniFB01" u2="&#x2f;" k="16" />
<hkern g1="uniFB01" u2="&#x2a;" k="-25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="86" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="86" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="53" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="143" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="27" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="74" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="147" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="AE" 	k="-25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="f,uniFB01,uniFB02" 	k="47" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="l" 	k="53" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="82" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,ccedilla" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="d" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="g" 	k="61" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,Ccedilla" 	k="53" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="156" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="j" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="n,ntilde" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="r" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="G" 	k="53" />
<hkern g1="C,Ccedilla" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="y,yacute,ydieresis" 	k="6" />
<hkern g1="C,Ccedilla" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="6" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="-45" />
<hkern g1="C,Ccedilla" 	g2="W" 	k="-55" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="-45" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-55" />
<hkern g1="C,Ccedilla" 	g2="c,ccedilla" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="d" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="37" />
<hkern g1="C,Ccedilla" 	g2="n,ntilde" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="r" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="-35" />
<hkern g1="C,Ccedilla" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-35" />
<hkern g1="D,Eth" 	g2="T" 	k="61" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="37" />
<hkern g1="D,Eth" 	g2="AE" 	k="72" />
<hkern g1="D,Eth" 	g2="quoteright,quotedblright" 	k="49" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="T" 	k="-25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="W" 	k="-14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Y,Yacute,Ydieresis" 	k="-25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,Ccedilla" 	k="16" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Z" 	k="-25" />
<hkern g1="G" 	g2="T" 	k="55" />
<hkern g1="K" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="K" 	g2="w" 	k="27" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="12" />
<hkern g1="K" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="27" />
<hkern g1="K" 	g2="T" 	k="-66" />
<hkern g1="K" 	g2="W" 	k="-35" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="-55" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-41" />
<hkern g1="K" 	g2="AE" 	k="-39" />
<hkern g1="K" 	g2="quoteright,quotedblright" 	k="-59" />
<hkern g1="K" 	g2="Z" 	k="-66" />
<hkern g1="K" 	g2="s" 	k="-25" />
<hkern g1="K" 	g2="z" 	k="-29" />
<hkern g1="K" 	g2="S" 	k="-37" />
<hkern g1="L" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="16" />
<hkern g1="L" 	g2="T" 	k="119" />
<hkern g1="L" 	g2="W" 	k="57" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="94" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-55" />
<hkern g1="L" 	g2="AE" 	k="-49" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="184" />
<hkern g1="L" 	g2="j" 	k="-55" />
<hkern g1="L" 	g2="Z" 	k="-55" />
<hkern g1="L" 	g2="z" 	k="-41" />
<hkern g1="L" 	g2="S" 	k="-41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="61" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="W" 	k="8" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="33" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="33" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="57" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="27" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="t" 	k="-14" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="12" />
<hkern g1="R" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="12" />
<hkern g1="R" 	g2="w" 	k="-25" />
<hkern g1="R" 	g2="y,yacute,ydieresis" 	k="-33" />
<hkern g1="R" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="12" />
<hkern g1="R" 	g2="T" 	k="6" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="6" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-31" />
<hkern g1="R" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="20" />
<hkern g1="R" 	g2="Z" 	k="-51" />
<hkern g1="R" 	g2="z" 	k="-25" />
<hkern g1="R" 	g2="S" 	k="-14" />
<hkern g1="S" 	g2="w" 	k="2" />
<hkern g1="S" 	g2="y,yacute,ydieresis" 	k="4" />
<hkern g1="S" 	g2="T" 	k="82" />
<hkern g1="S" 	g2="W" 	k="10" />
<hkern g1="S" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="20" />
<hkern g1="S" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="16" />
<hkern g1="S" 	g2="c,ccedilla" 	k="6" />
<hkern g1="T" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="143" />
<hkern g1="T" 	g2="w" 	k="68" />
<hkern g1="T" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="61" />
<hkern g1="T" 	g2="T" 	k="-82" />
<hkern g1="T" 	g2="W" 	k="-82" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-61" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="143" />
<hkern g1="T" 	g2="AE" 	k="197" />
<hkern g1="T" 	g2="f,uniFB01,uniFB02" 	k="37" />
<hkern g1="T" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="20" />
<hkern g1="T" 	g2="l" 	k="33" />
<hkern g1="T" 	g2="t" 	k="37" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="129" />
<hkern g1="T" 	g2="c,ccedilla" 	k="131" />
<hkern g1="T" 	g2="d" 	k="131" />
<hkern g1="T" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="131" />
<hkern g1="T" 	g2="g" 	k="131" />
<hkern g1="T" 	g2="C,Ccedilla" 	k="61" />
<hkern g1="T" 	g2="quoteright,quotedblright" 	k="-88" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="150" />
<hkern g1="T" 	g2="j" 	k="20" />
<hkern g1="T" 	g2="n,ntilde" 	k="129" />
<hkern g1="T" 	g2="r" 	k="129" />
<hkern g1="T" 	g2="G" 	k="61" />
<hkern g1="T" 	g2="Z" 	k="-45" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="55" />
<hkern g1="T" 	g2="s" 	k="123" />
<hkern g1="T" 	g2="z" 	k="98" />
<hkern g1="T" 	g2="S" 	k="41" />
<hkern g1="T" 	g2="colon,semicolon" 	k="49" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="w" 	k="-45" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="y,yacute,ydieresis" 	k="-45" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="W" 	k="-12" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="27" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="t" 	k="-14" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="31" />
<hkern g1="W" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="W" 	g2="w" 	k="-76" />
<hkern g1="W" 	g2="y,yacute,ydieresis" 	k="-86" />
<hkern g1="W" 	g2="T" 	k="-82" />
<hkern g1="W" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="-31" />
<hkern g1="W" 	g2="W" 	k="-66" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-66" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="W" 	g2="AE" 	k="111" />
<hkern g1="W" 	g2="f,uniFB01,uniFB02" 	k="-41" />
<hkern g1="W" 	g2="t" 	k="-55" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-80" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="37" />
<hkern g1="W" 	g2="n,ntilde" 	k="8" />
<hkern g1="W" 	g2="Z" 	k="-57" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="74" />
<hkern g1="W" 	g2="s" 	k="18" />
<hkern g1="W" 	g2="S" 	k="-49" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="w" 	k="-76" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="y,yacute,ydieresis" 	k="-70" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-61" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-66" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-66" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="121" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="186" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,uniFB01,uniFB02" 	k="6" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="t" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="25" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,ccedilla" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="68" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-57" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="n,ntilde" 	k="74" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Z" 	k="-35" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="170" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="70" />
<hkern g1="Z" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="37" />
<hkern g1="Z" 	g2="w" 	k="16" />
<hkern g1="Z" 	g2="y,yacute,ydieresis" 	k="8" />
<hkern g1="Z" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="Z" 	g2="T" 	k="-37" />
<hkern g1="Z" 	g2="W" 	k="-35" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="-35" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="37" />
<hkern g1="Z" 	g2="quoteright,quotedblright" 	k="-39" />
<hkern g1="Z" 	g2="s" 	k="16" />
<hkern g1="Z" 	g2="z" 	k="-14" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="2" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="4" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quoteright,quotedblright" 	k="59" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-41" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="-41" />
<hkern g1="c,ccedilla" 	g2="f,uniFB01,uniFB02" 	k="-14" />
<hkern g1="c,ccedilla" 	g2="t" 	k="-29" />
<hkern g1="c,ccedilla" 	g2="quoteright,quotedblright" 	k="-29" />
<hkern g1="c,ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="-14" />
<hkern g1="e,egrave,eacute,ecircumflex,edieresis" 	g2="quoteright,quotedblright" 	k="49" />
<hkern g1="g" 	g2="y,yacute,ydieresis" 	k="-35" />
<hkern g1="g" 	g2="quoteright,quotedblright" 	k="29" />
<hkern g1="g" 	g2="j" 	k="-106" />
<hkern g1="h" 	g2="quoteright,quotedblright" 	k="88" />
<hkern g1="j" 	g2="j" 	k="-74" />
<hkern g1="k" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="33" />
<hkern g1="k" 	g2="w" 	k="-20" />
<hkern g1="k" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="29" />
<hkern g1="k" 	g2="t" 	k="-35" />
<hkern g1="k" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="25" />
<hkern g1="k" 	g2="c,ccedilla" 	k="33" />
<hkern g1="k" 	g2="d" 	k="33" />
<hkern g1="k" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="33" />
<hkern g1="k" 	g2="g" 	k="33" />
<hkern g1="l" 	g2="quoteright,quotedblright" 	k="47" />
<hkern g1="l" 	g2="s" 	k="-14" />
<hkern g1="l" 	g2="z" 	k="-25" />
<hkern g1="l" 	g2="colon,semicolon" 	k="-6" />
<hkern g1="n,ntilde" 	g2="w" 	k="6" />
<hkern g1="n,ntilde" 	g2="quoteright,quotedblright" 	k="70" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="f,uniFB01,uniFB02" 	k="27" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="t" 	k="16" />
<hkern g1="o,ograve,oacute,ocircumflex,otilde,odieresis,oslash" 	g2="quoteright,quotedblright" 	k="68" />
<hkern g1="r" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="r" 	g2="w" 	k="-45" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-45" />
<hkern g1="r" 	g2="f,uniFB01,uniFB02" 	k="-49" />
<hkern g1="r" 	g2="t" 	k="-35" />
<hkern g1="r" 	g2="c,ccedilla" 	k="16" />
<hkern g1="r" 	g2="d" 	k="16" />
<hkern g1="r" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="16" />
<hkern g1="r" 	g2="g" 	k="16" />
<hkern g1="r" 	g2="quoteright,quotedblright" 	k="-47" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="29" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="160" />
<hkern g1="r" 	g2="z" 	k="-14" />
<hkern g1="t" 	g2="w" 	k="-25" />
<hkern g1="t" 	g2="y,yacute,ydieresis" 	k="-25" />
<hkern g1="t" 	g2="t" 	k="-14" />
<hkern g1="t" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-49" />
<hkern g1="t" 	g2="colon,semicolon" 	k="-10" />
<hkern g1="w" 	g2="w" 	k="-45" />
<hkern g1="w" 	g2="y,yacute,ydieresis" 	k="-45" />
<hkern g1="w" 	g2="f,uniFB01,uniFB02" 	k="-41" />
<hkern g1="w" 	g2="t" 	k="-45" />
<hkern g1="w" 	g2="quoteright,quotedblright" 	k="-39" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="4" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="78" />
<hkern g1="w" 	g2="colon,semicolon" 	k="12" />
<hkern g1="y,yacute,ydieresis" 	g2="w" 	k="-45" />
<hkern g1="y,yacute,ydieresis" 	g2="y,yacute,ydieresis" 	k="-45" />
<hkern g1="y,yacute,ydieresis" 	g2="f,uniFB01,uniFB02" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="t" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteright,quotedblright" 	k="-39" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="66" />
<hkern g1="z" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="z" 	g2="t" 	k="-29" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w" 	k="78" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="y,yacute,ydieresis" 	k="115" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="37" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="170" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="78" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W" 	k="115" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="170" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="-39" />
<hkern g1="quoteleft,quotedblleft" 	g2="y,yacute,ydieresis" 	k="-39" />
<hkern g1="quoteleft,quotedblleft" 	g2="T" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="135" />
<hkern g1="quoteleft,quotedblleft" 	g2="t" 	k="-31" />
<hkern g1="quoteleft,quotedblleft" 	g2="c,ccedilla" 	k="49" />
<hkern g1="quoteleft,quotedblleft" 	g2="d" 	k="29" />
<hkern g1="quoteleft,quotedblleft" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="39" />
<hkern g1="quoteleft,quotedblleft" 	g2="g" 	k="39" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="57" />
<hkern g1="J" 	g2="w" 	k="-14" />
<hkern g1="J" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="J" 	g2="AE" 	k="29" />
<hkern g1="J" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="57" />
</font>
</defs></svg> 