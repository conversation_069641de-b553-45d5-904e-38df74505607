<ng-container>
  <h1><i class="icon-print_checklist"></i> {{ 'UBZ.SITE_CONTENT.11101110100' | translate }}</h1>
  <h4>{{ 'UBZ.SITE_CONTENT.11101110011' | translate }}</h4>
</ng-container>
<div class="col-sm-3 col-md-3 form-group" style="margin-top:1%">
  <label>{{ 'UBZ.SITE_CONTENT.11101101110' | translate }}</label>
  <input style="text-transform: uppercase" [(ngModel)]="reportType" (ngModelChange)="evaluateIfIsPossibleToSearch()" type="text" class="form-control numeric" placeholder="tipo rapporto" name="type"  required />
</div>
<div class="col-sm-3 col-md-3" style="margin-top:1%">
  <label>{{ 'UBZ.SITE_CONTENT.11101101111' | translate }}</label>
  <input style="text-transform: uppercase" [(ngModel)]="reportNumber" (ngModelChange)="evaluateIfIsPossibleToSearch()" type="text" class="form-control numeric" placeholder="numero rapporto" name="number"  required />
</div>
<label></label>
<button style="margin-top:1%" class="col-md-3" [disabled]="isNotPossibleToSearch" (click)="search()" type="submit" class="btn btn-primary">{{ 'UBZ.SITE_CONTENT.10010111' | translate }}</button>

