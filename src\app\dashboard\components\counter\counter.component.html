<div role="tabpanel" class="tab-pane active Carousel__TabPanel">
  <div *ngIf="inputParam.counter && inputParam.counter.length > 0" class="row">
  <ngx-slick class="carousel" [config]="slideConfig">
    <div ngxSlickItem class="col" *ngFor="let elem of inputParam.counter; let i = index">
      <div class="ml-auto mr-auto" style="position: relative;  font-size: 50px;  width: 120px;
        height: 120px; border-radius: 2000px; overflow: hidden;" (click)="clickFunction(i , elem.id)" [class]="getCounterBackgroundClass(i)">
        <div class="num" style="position: absolute; width: 100%; text-align: center !important; top: 20%;">{{elem.num}}</div>
        <round-progress [current]="1"
          [max]="1"
          [color]="getCounterColors(i)"
          [radius]="60"
          [stroke]="10"
          [rounded]="true"
          [responsive]="false"
          [duration]="800"
          [animation]="'easeInCirc'">
        </round-progress>
        <span>XXX</span>
      </div>
      <p [class]="getCounterTextClass(i)">{{elem.label | translate}}</p>
    </div>
  </ngx-slick>
  </div>
</div>
