import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';

import { Observable } from 'rxjs/Observable';

@Injectable()
export class CustomerService {
  // Array contenente gli ndg associati alla cointestazione
  coiNdgList: any[] = new Array();

  constructor(private http: Http) {}

  searchNdgAndCreateSimulation(
    isProspect: boolean,
    ndg: string,
    familyAsset: string
  ): Observable<any> {
    const url = '/UBZ-ESA-RS/service/simulation/v1/simulations';
    return this.http
      .post(url, { ndg: ndg, isProspect: isProspect, familyAsset: familyAsset })
      .map((resp: Response) => resp.json());
  }

  saveCustomerInfo(
    simulationId: string,
    model: Object,
    page: string,
    wizardCode: string
  ): Observable<any> {
    simulationId = encodeURIComponent(simulationId);
    let url: string;
    if (wizardCode === 'WSIM') {
      url = `/UBZ-ESA-RS/service/simulation/v1/customers/${simulationId}`;
    } else {
      url = `/UBZ-ESA-RS/service/reqPer/v1/customers/${simulationId}`;
    }
    return this.http
      .put(url, { page: page, apfmap: model })
      .map((resp: Response) => resp.text());
  }

  // Crea la nuova richiesta di perizia
  startAppraisalRequest(positionId: string, ndg: string): Observable<any> {
    const url = '/UBZ-ESA-RS/service/reqPer/v1/reqAppraisals/fromSimulation';
    return this.http
      .post(url, { positionId: positionId, ndg: ndg })
      .map((resp: Response) => resp.json());
  }

  newAppraisalRequest(
    familyAsset: string,
    ndg: string,
    requestType: string,
    accessPoint: string
  ): Observable<any> {
    const url = '/UBZ-ESA-RS/service/reqPer/v2/reqAppraisals';
    const requestObj = {
      familyAsset: familyAsset,
      ndg: ndg,
      origination: requestType,
      accessPoint: accessPoint
    };
    return this.http.post(url, requestObj).map((resp: Response) => resp.json());
  }

  getNdgList(): Observable<any> {
    const url = '/UBZ-ESA-RS/service/reqPer/v1/reqFracNdg';
    return this.http.get(url).map((resp: Response) => resp.json());
  }

  getCoiNdgs(ndg: string): Observable<any> {
    const url = `/UBZ-ESA-RS/service/individual/v1/individuals/jointAccount/${ndg}`;    
    return this.http.get(url).map((resp: Response) => resp.json()); 
  }

  // Crea la nuova richiesta individual TGP
  // ObjectParams è l'oggetto contenente i queryParams dell'url di invocazione
  startTgpAppraisalRequest(objectParams: Object): Observable<any> {
    const url = '/UBZ-ESA-RS/service/individual/v1/individuals/reqAppAppraisals';
    return this.http
      .post(url, objectParams)
      .map((resp: Response) => resp.json());
  }

  // Recupera idRichiesta relativo al tgpId ricevuto tramite link esternoo
  // tgpId è la stringa contenente l'id richiesta tgp dell'url di invocazione
  translateIdTgp(tgpId: string) {
    const url = '/UBZ-ESA-RS/service/individual/v1/individuals/position';
    return this.http
      .post(url, {
        requestId: null,
        appraisalId: null,
        empId: null,
        tgpId: tgpId
      })
      .map((resp: Response) => resp.json());      
  }
}
