<div class="row step-navigation">
  <div class="col-sm-12 btn-set">
    <ng-container *ngIf="!(addExpertFirm || filtersOpen)">
      <button *appAuthKey="'UBZ_REGISTRY.EXPERT_FIRM_OPEN_FILTER'" type="button"
        class="btn btn-empty waves-effect waves-secondary pull-right" (click)="openFilters()">
        <i class="icon-filter"></i> {{'UBZ.SITE_CONTENT.1100100011' | translate }}
      </button>
      <button *appAuthKey="'UBZ_REGISTRY.EXPERT_FIRM_ADD_NEW'" type="button"
        class="btn btn-empty waves-effect waves-secondary pull-right" (click)="goToNewExpertFirm()">
        <i class="icon-add"></i> {{'UBZ.SITE_CONTENT.1100100100' | translate }}
      </button>
    </ng-container>
    <ng-container *ngIf="addExpertFirm">
      <button *appAuthKey="'UBZ_REGISTRY.EXPERT_FIRM_ABORT'" type="button"
        class="btn btn-empty waves-effect waves-secondary pull-right" (click)="addExpertFirm = false">
        <i class="fa fa-times" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.100000' | translate }}
      </button>
    </ng-container>
    <ng-container *ngIf="filtersOpen">
      <button *appAuthKey="'UBZ_REGISTRY.EXPERT_FIRM_CLOSE_FILTERS'" type="button"
        class="btn btn-empty waves-effect waves-secondary pull-right" (click)="closeFilters()">
        <i class="fa fa-times" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.1100100101' | translate }}
      </button>
    </ng-container>
  </div>
</div>

<section class="row" *ngIf="filtersOpen">
  <!-- Filtri -->
  <div class="col-md-2 col-sm-12 form-group">
    <label>{{'UBZ.SITE_CONTENT.1001110' | translate }}</label>
    <input type="text" name="ndg" class="form-control" [(ngModel)]="searchFilter.ndg">
  </div>
  <div class="col-md-3 col-sm-12 form-group">
    <label>{{'UBZ.SITE_CONTENT.101111110' | translate }}</label>
    <input type="text" name="heading" class="form-control" [(ngModel)]="searchFilter.heading" ngClass="">
  </div>
  <div class="col-md-4 col-sm-12 form-group">
    <label></label>
    <div class="btn-set">
      <button *appAuthKey="'UBZ_REGISTRY.EXPERT_FIRM_FILTER'" class="btn btn-primary waves-effect pull-right"
        type="button" (click)="filter()">{{'UBZ.SITE_CONTENT.1100100110' | translate }}</button>
      <ng-container *appAuthKey="'UBZ_REGISTRY.EXPERT_FIRM_RESET_FILTER'">
        <button *ngIf="searchFilter && (searchFilter.ndg || searchFilter.heading)"
          class="btn btn-secondary waves-effect pull-right" type="button"
          (click)="cleanSearchFilter()">{{'UBZ.SITE_CONTENT.1010010101' | translate }}</button>
      </ng-container>
    </div>
  </div>
</section>

<section class="row" *ngIf="!addExpertFirm">
  <!-- Tabella -->
  <div class="col-sm-12">
    <table class="table table-hover" *ngIf="societiesNumber > 0; else noRecordFound">
      <thead>
        <tr>
          <th scope="col">{{'UBZ.SITE_CONTENT.1001110' | translate }}</th>
          <th scope="col">{{'UBZ.SITE_CONTENT.101111110' | translate }}</th>
          <th scope="col">{{'UBZ.SITE_CONTENT.1100100111' | translate }}</th>
          <th scope="col">{{'UBZ.SITE_CONTENT.10000001' | translate }}</th>
          <th scope="col"></th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let soc of societies">
          <td data-label="NDG">{{soc.ndg}}</td>
          <td data-label="Nome">{{ soc.heading }}</td>
          <td data-label="Sede">{{ soc.address }}</td>
          <td data-label="Stato">
            <span class="state" [ngClass]="getStatus(soc)"></span>
          </td>
          <td data-label>
            <a *appAuthKey="'UBZ_REGISTRY.EXPERT_FIRM_GO_TO_DETAIL'" role="button" (click)="goToExpertFirm(soc.idAnag)">
              <i class="icon-angle-double-right"></i>
            </a>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <ng-template #noRecordFound>
    <div class="Search__NoResults">
      <div class="Search__NoResults__Icon">
        <i class="icon-placeholder_note"></i>
      </div>
      <div class="Search__NoResults__Text">
        <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1001011001' | translate }}</h3>
        <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011010' | translate }}</p>
      </div>
    </div>
  </ng-template>
  <div *ngIf="societies && societiesNumber > 10">
    <div class="col-sm-6">
      <div class="results">
        <span>{{'UBZ.SITE_CONTENT.1111111' | translate }}</span>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="listSize" (ngModelChange)="pageSizeChanged()">
            <option value="10" selected>10 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{societiesNumber}}</option>
            <option value="20" *ngIf="societiesNumber > 20">20 {{'UBZ.SITE_CONTENT.10000000' | translate }}
              {{societiesNumber}}</option>
            <option value="30" *ngIf="societiesNumber > 30">30 {{'UBZ.SITE_CONTENT.10000000' | translate }}
              {{societiesNumber}}</option>
            <option value="40" *ngIf="societiesNumber > 40">40 {{'UBZ.SITE_CONTENT.10000000' | translate }}
              {{societiesNumber}}</option>
            <option value="50" *ngIf="societiesNumber > 50">50 {{'UBZ.SITE_CONTENT.10000000' | translate }}
              {{societiesNumber}}</option>
            <option value="{{societiesNumber}}">{{societiesNumber}} {{'UBZ.SITE_CONTENT.10000000' | translate }}
              {{societiesNumber}}</option>
          </select>
        </div>
      </div>
    </div>
    <div class="col-sm-6" class="pull-right">
      <pagination [boundaryLinks]="true" [directionLinks]="false" [totalItems]="societiesNumber"
        [itemsPerPage]="listSize" [maxSize]="10" (pageChanged)="changePage($event)" class="pagination"
        previousText="&lsaquo;" nextText="&rsaquo;" firstText="&laquo;" lastText="&raquo;"></pagination>
    </div>
  </div>
</section>


<section *ngIf="addExpertFirm">
  <!-- Aggiunta nuova società peritale  -->
  <form #f="ngForm" (ngSubmit)="saveNewExpert()">
    <div class="row">
      <div class="col-md-12">
        <h3>{{'UBZ.SITE_CONTENT.1100000011' | translate }}</h3>
      </div>
    </div>
    <div class="row">
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1100000100' | translate }}*</label>
        <input type="text" name="heading" [(ngModel)]="newExpertFirm.heading" required class="form-control">
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.101000' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" name="province" (change)="calculateCities($event)"
            [(ngModel)]="newExpertFirm.province">
            <option value="" hidden>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let prov of (provinces | domainMapToDomainArray)" value="{{ prov.domCode }}">{{
              prov.translationCod | translate }}</option>
          </select>
        </div>
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.101001' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="newExpertFirm.city" name="city">
            <option value="" selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let city of cities" value="{{ city.domCode }}">{{ city.translationCod | translate }}
            </option>
          </select>
        </div>
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.101010' | translate }}*</label>
        <input type="text" name="postalCode" [(ngModel)]="newExpertFirm.postalCode" class="form-control">
      </div>
    </div>
    <div class="row">
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.101011' | translate }}*</label>
        <input type="text" name="address" [(ngModel)]="newExpertFirm.address" required class="form-control">
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.101100' | translate }}*</label>
        <input type="text" name="num" required class="form-control" appOnlyNumbers [(ngModel)]="newExpertFirm.num">
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.10011110000' | translate }}*</label>
        <input type="text" name="iban" [(ngModel)]="newExpertFirm.iban" class="form-control" required
          (keyup)="checkLenghtIban($event.target.value)" maxlength="27">
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1011100011' | translate }}</label>
        <input type="text" name="userId" class="form-control" [(ngModel)]="newExpertFirm.userId">
      </div>
    </div>
    <div class="row">
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1100000110' | translate }}*</label>
        <input type="text" name="email" [(ngModel)]="newExpertFirm.email" required class="form-control">
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.11110000' | translate }}*</label>
        <input type="text" name="phoneNum" [(ngModel)]="newExpertFirm.phoneNum" required class="form-control"
          appOnlyNumber maxlength="15">
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1001110' | translate }}*</label>
        <input type="text" name="ndg" [(ngModel)]="newExpertFirm.ndg" required class="form-control">
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <h3>{{'UBZ.SITE_CONTENT.1100001001' | translate }}</h3>
      </div>
    </div>
    <div class="row">
      <div class="col-md-3 col-sm-12 form-group">
        <label for="startAbilitation">{{'UBZ.SITE_CONTENT.1100001010' | translate }}*</label>
        <app-calendario [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate" [name]="'startAbilitation'"
          [title]="'UBZ.SITE_CONTENT.1010100001' | translate" [(ngModel)]="newExpertFirm.startAbilitation"
          [required]="true">
        </app-calendario>
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label for="endAbilitation">{{'UBZ.SITE_CONTENT.1011111011' | translate }}*</label>
        <app-calendario [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate" [name]="'endAbilitation'"
          [title]="'UBZ.SITE_CONTENT.1010100001' | translate" [(ngModel)]="newExpertFirm.endAbilitation"
          [required]="true">
        </app-calendario>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <h3>{{'UBZ.SITE_CONTENT.11000001' | translate }}</h3>
        <div class="input-group">
          <input type="text" class="form-control" readonly [value]="fileName" required>
          <label class="input-group-btn">
            <span class="btn btn-primary waves-effect">
              {{'UBZ.SITE_CONTENT.11000011' | translate }}&hellip;
              <input type="file" #fileToUpload id="sfoglia" style="display: none;" multiple (change)="setFile()"
                required>
            </span>
          </label>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12 btn-set">
        <button *appAuthKey="'UBZ_REGISTRY.EXPERT_FIRM_SAVE_NEW'" class="btn btn-primary waves-effect pull-right"
          [disabled]="f.invalid || !contractChosen || isValidIban" type="submit">{{'UBZ.SITE_CONTENT.1100101000' |
          translate }}</button>
      </div>
    </div>
  </form>
</section>