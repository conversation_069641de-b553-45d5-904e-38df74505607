<form #f="ngForm" novalidate (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
          {{'UBZ.SITE_CONTENT.111010110' | translate }}
          <span class="state" [ngClass]="{green: f.valid, red: f.invalid}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <table class="uc-table">
          <thead>
            <tr>
              <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.111010110' | translate }}</th>
              <th scope="col" class="col-sm-2">€</th>
            </tr>
          </thead>
          <tbody>
            <ng-container *ngFor="let investmentType of (investiments | domainMapToDomainArray)">
              <tr *ngIf="model && model[investmentType.domCode]">
                <td>{{ investiments[investmentType.domCode].translationCod | translate }}</td>
                <td *ngIf="investmentType.domCode !== 'TOT'">
                  <app-importo [name]="investmentType.domCode" [required]="false" [(ngModel)]="model[investmentType.domCode].amount" (ngModelChange)="calculateTotal()">
                  </app-importo>
                  <td *ngIf="investmentType.domCode === 'TOT'">{{model[investmentType.domCode].amount | number:'1.2-2'}}</td>
              </tr>
            </ng-container>
          </tbody>
        </table>
        <div class="panel-box">
          <div class="row">
            <div class="col-sm-12">
              <label>
                <span>
                  <i class="icon-search note-tooltip" [tooltip]="investmentSummaryString" triggers="click"></i>
                </span>
                {{'UBZ.SITE_CONTENT.1001100111' | translate }}
              </label>
              <textarea name="investments" class="form-control" [(ngModel)]="investmentSummaryString"></textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
  </accordion-group>
</form>
