<!-- FIXME - TOGLIERE CODICE COMMENTANTO SE IMPORTO FUNZIONA -->
<!-- Suddivise parti differenti tra ndg corporate ed individuals tramite utilizzo dell'appAuthKey -->
<form #f="ngForm" novalidate (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i
            [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>{{'UBZ.SITE_CONTENT.111101010'
          | translate }}
          <span class="state" [ngClass]="{'green' : (sectionIsValid), 'red' : !(sectionIsValid)}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <fieldset [disabled]="haveDisabledFields">
          <table class="uc-table" id="table-data">
            <thead>
              <tr>
                <!-- Campi intestazione mostrati tramite funzioni in base a tipo di NDG (COR O IND) -->
                <th scope="col" style="width:15%">{{'UBZ.SITE_CONTENT.111101011' | translate }}*</th>
                <th scope="col" *appAuthKey="'UBZ_CONSISTENCY_INDIVIDUAL_UNITMEASURE'">{{'UBZ.SITE_CONTENT.111001011' | translate}}</th>
                <th scope="col" *appAuthKey="'UBZ_CONSISTENCY_CORPORATE_SURFACE'">{{'UBZ.SITE_CONTENT.100100000' | translate }}*</th>
                <th scope="col" *appAuthKey="'UBZ_CONSISTENCY_INDIVIDUAL_QUANTITY'">{{'UBZ.SITE_CONTENT.10010001100' |translate}}</th>
                <th scope="col">{{'UBZ.SITE_CONTENT.111101100' | translate }}*</th>
                <th scope="col" *appAuthKey="'UBZ_CONSISTENCY_CORPORATE_COMMSURFACE'">{{'UBZ.SITE_CONTENT.101001000' |translate }}*</th>
                <th scope="col" *appAuthKey="'UBZ_CONSISTENCY_INDIVIDUAL_COMMSURFACE'">{{'UBZ.SITE_CONTENT.10010001101'| translate}}</th>
                <th scope="col" style="width:10%">+ / - *</th>
                <th scope="col" style="width:7%">{{'UBZ.SITE_CONTENT.111101101' | translate }}*</th>
                <th scope="col" style="width:8.5%"> {{ macro_process === 'ITL' ? ('UBZ.SITE_CONTENT.100000000000' | translate) : ('UBZ.SITE_CONTENT.100000000000' | translate) + '*' }}</th>
                <th scope="col" style="width:10%">{{'UBZ.SITE_CONTENT.10010010' | translate }}*</th>
                <th scope="col" *appAuthKey="'UBZ_CONSISTENCY_CORPORATE_COMMVALUE'">{{'UBZ.SITE_CONTENT.1010111110' | translate }}*</th>
                <th scope="col" *appAuthKey="'UBZ_CONSISTENCY_INDIVIDUAL_COMMVALUE'">{{'UBZ.SITE_CONTENT.10010001110' | translate}}*</th>
                <th scope="col">{{'UBZ.SITE_CONTENT.111101111' | translate }}*</th>
                <th scope="col" *appAuthKey="'UBZ_CONSISTENCY_CORPORATE_CAUTIONARYVALUE'">{{'UBZ.SITE_CONTENT.111110000' | translate }}*</th>
                <th scope="col" *appAuthKey="'UBZ_CONSISTENCY_INDIVIDUAL_CAUTIONARYVALUE'">{{'UBZ.SITE_CONTENT.10010001111' | translate}}*</th>
                <th scope="col">{{'UBZ.SITE_CONTENT.101010001' | translate }}*</th>
                <th scope="col" style="width:4%"></th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let row of pageContent.structseval; let i = index">
                <td data-label="Codice">
                  <div class="custom-select">
                    <select class="form-control" [(ngModel)]="row.structureType" name="structureType-{{i}}" required>
                      <option value="" hidden>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                      <option *ngFor="let elem of (structureTypeDom | domainMapToDomainArray)" value="{{elem.domCode}}">
                        {{elem.translationCod | translate}}</option>
                    </select>
                  </div>
                </td>
                <!-- Campo mostrato tramite funzione in base a tipo di NDG (COR O IND) -->
                <td data-label="Unita' di misura" *appAuthKey="'UBZ_CONSISTENCY_INDIVIDUAL_UNITMEASURE'">
                  <select class="form-control" [(ngModel)]="row.measurType" name="measurType-{{i}}" required>
                    <option value="" hidden>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                    <option *ngFor="let elem of (unitMeasurTypeDom | domainMapToDomainArray)" value="{{elem.domCode}}">
                      {{elem.translationCod | translate}}</option>
                  </select>
                </td>
                <td data-label="Superficie (mq)">
                  <app-importo [name]="'grossSurface-' + i" [required]="true" [(ngModel)]="row.grossSurface"
                    (ngModelChange)="multiplyForCommSurface(row)">
                  </app-importo>
                </td>
                <td data-label="Coeff. commerciale">
                  <input appForcePattern regexPattern="^[0]{1}((\.)[0-9]{0,2})?$|^[1]$" type="text" class="form-control"
                    [(ngModel)]="row.commCoefficient" name="commCoefficient-{{i}}" required
                    (ngModelChange)="multiplyForCommSurface(row)">
                </td>
                <td data-label="Sup. commerciale (mq)">
                  <p>{{calculateCommSurf(i) | number:'1.2-2'}}</p>
                </td>
                <td data-label="no-label">
                  <div class="custom-select">
                    <select class="form-control" [(ngModel)]="row.floorDirectType" name="floorDirectType-{{i}}"
                      required>
                      <option [ngValue]="undefined">{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                      <option *ngFor="let row of (floorDirectTypeDom | domainMapToDomainArray)" value="{{row.domCode}}">
                        {{row.translationCod | translate}}</option>
                    </select>
                  </div>
                </td>
                <td data-label="Piano">
                  <input appOnlyNumbers type="text" class="form-control" [(ngModel)]="row.floor" name="floor-{{i}}"
                    maxlength="3" required>
                </td>
                <td data-label="Piano Principale">
                  <div class="custom-select">
                    <select class="form-control" [(ngModel)]="row.floorFlag" name="floorFlag-{{i}}" required>
                      <option [ngValue]="undefined">{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                      <option value="Y">{{'UBZ.SITE_CONTENT.100011011' | translate }}</option>
                      <option value="N">{{'UBZ.SITE_CONTENT.100011100' | translate }}</option>
                    </select>
                  </div>
                </td>
                <td data-label="Tipo">
                  <div class="custom-select">
                    <select class="form-control" [(ngModel)]="row.floorType" name="floorType-{{i}}" required>
                      <option [ngValue]="undefined">{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                      <option *ngFor="let row of (floorTypeDom | domainMapToDomainArray)" value="{{row.domCode}}">
                        {{row.translationCod | translate}}</option>
                    </select>
                  </div>
                </td>
                <td data-label="Val. comm. € / mq">
                  <app-importo [name]="'mqValue-' + i" [required]="true" [(ngModel)]="row.mqValue" [maxlength]="15"
                    (ngModelChange)="multiplyForBookValue(row)">
                  </app-importo>
                </td>
                <td data-label="Val. commerciale(€)">
                  <p>{{calculateVarComm(i) | number:'1.2-2'}}</p>
                </td>
                <td data-label="Val. cauzionale € / mq">
                  <app-importo [name]="'mqPledgedValue-' + i" [required]="true" [(ngModel)]="row.mqPledgedValue"
                    [maxlength]="15" (ngModelChange)="multiplyForPledgedValue(row)">
                  </app-importo>
                </td>
                <td data-label="Val. cauzionale (€)">
                  <p>{{calculateValCauzionale(i) | number:'1.2-2'}}</p>
                </td>
                <td data-label="">
                  <ng-container *appAuthKey="'UBZ_CONSISTENCY_DELETE'">
                    <button type="button" class="btn btn-empty waves-effect waves-secondary" (click)="delete(i)">
                      <i class="fa fa-trash-o" aria-hidden="true"></i>
                    </button>
                  </ng-container>
                </td>
              </tr>
            </tbody>
            <tfoot>
              <tr style="background-color: #f2f2f2;">
                <td><strong>{{'UBZ.SITE_CONTENT.101010010' | translate }}</strong></td>
                <td></td>
                <td></td>
                <td><strong>{{(pageContent && pageContent.totCommSurface ) ? (pageContent.totCommSurface | number:'1.2-2') : 0}}</strong></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td><strong>{{(pageContent && pageContent.totBookValue ) ? (pageContent.totBookValue | number:'1.2-2') : 0}}</strong></td>
                <td></td>
                <td><strong>{{(pageContent && pageContent.totPledgedValue ) ? (pageContent.totPledgedValue | number:'1.2-2') : 0}}</strong></td>
                <td>
                </td>
              </tr>
            </tfoot>
          </table>
        </fieldset>
        <div class="panel-box">
          <div class="row">
            <div class="col-sm-12 form-group">
              <fieldset [disabled]="haveDisabledFields">
                <ng-container *appAuthKey="'UBZ_CONSISTENCY_ADD'">
                  <button type="button" class="btn btn-empty" id="add-table-row" (click)="add()">
                    <i class="fa fa-plus"></i> {{'UBZ.SITE_CONTENT.111100001' | translate }}
                  </button>
                </ng-container>
              </fieldset>
            </div>
            <div class="col-sm-3 form-group">
              <fieldset [disabled]="haveDisabledFields">
                <label>
                  {{'UBZ.SITE_CONTENT.111110001' | translate }}*
                  <i *ngIf="differences['insuranceValue']" class="icon-info blue-tooltip pull-right" aria-hidden="true"
                    data-placement="right" data-toggle="tooltip"
                    title="{{'UBZ.SITE_CONTENT.1001101011' | translate }}"></i>
                </label>
                <app-importo [name]="'insuranceValue'" [required]="true" [maxlength]="19"
                  [(ngModel)]="pageContent.insuranceValue">
                </app-importo>
                <ng-container *appAuthKey="'UBZ_CONSISTENCY_DIFFERENCES.MODAL'">
                  <button *ngIf="differences['insuranceValue']" type="button"
                    class="btn btn-action waves-effect waves-secondary"
                    [ngClass]="{active: differences['insuranceValue'].assigned}"
                    (click)="openDifferencesModal('insuranceValue', 'text', 'UBZ.SITE_CONTENT.111110001')">{{pageContent.insuranceValue}}</button>
                </ng-container>
              </fieldset>
            </div>
            <div class="col-sm-3 form-group" *appAuthKey="'UBZ_APPRAISAL.NOT_EPC'">
              <fieldset [disabled]="haveDisabledFieldsSpecial">
                <label>
                  {{'UBZ.SITE_CONTENT.111110010' | translate }}{{isMarketValueRequired ? '*' : ''}}
                  <i *ngIf="differences['marketValue']" class="icon-info blue-tooltip pull-right" aria-hidden="true"
                    data-placement="right" data-toggle="tooltip"
                    title="{{'UBZ.SITE_CONTENT.1001101011' | translate }}"></i>
                </label>
                <app-importo [name]="'marketValue'" [required]="isMarketValueRequired && !haveDisabledFieldsSpecial"
                  [maxlength]="19" [(ngModel)]="pageContent.marketValue">
                </app-importo>
                <ng-container *appAuthKey="'UBZ_CONSISTENCY_DIFFERENCES.MODAL'">
                  <button *ngIf="differences['marketValue']" type="button"
                    class="btn btn-action waves-effect waves-secondary"
                    [ngClass]="{active: differences['marketValue'].assigned}"
                    (click)="openDifferencesModal('marketValue', 'text', 'UBZ.SITE_CONTENT.111110010')">{{pageContent.marketValue}}</button>
                </ng-container>
              </fieldset>
            </div>
            <div class="col-sm-3 form-group" *ngIf="haveActualValue">
              <fieldset [disabled]="haveDisabledFieldsSpecial">
                <label>
                  {{'UBZ.SITE_CONTENT.111110011' | translate }}*
                  <i *ngIf="differences['actualValue']" class="icon-info blue-tooltip pull-right" aria-hidden="true"
                    data-placement="right" data-toggle="tooltip"
                    title="{{'UBZ.SITE_CONTENT.1001101011' | translate }}"></i>
                </label>
                <app-importo [name]="'actualValue'" [required]="true" [maxlength]="19"
                  [(ngModel)]="pageContent.actualValue">
                </app-importo>
                <ng-container *appAuthKey="'UBZ_CONSISTENCY_DIFFERENCES.MODAL'">
                  <button *ngIf="differences['actualValue']" type="button"
                    class="btn btn-action waves-effect waves-secondary"
                    [ngClass]="{active: differences['actualValue'].assigned}"
                    (click)="openDifferencesModal('actualValue', 'text', 'UBZ.SITE_CONTENT.111110011')">{{pageContent.actualValue}}</button>
                </ng-container>
              </fieldset>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12 form-group">
              <label>
                <span><i class="icon-search note-tooltip" [tooltip]="pageContent.addNote" triggers="click"></i></span>
                {{'UBZ.SITE_CONTENT.11000101' | translate }}
              </label>
              <textarea name="addNotes" [(ngModel)]="pageContent.addNote" class="form-control" maxlength="4000"
                [disabled]="haveDisabledFieldsSpecial"></textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
  </accordion-group>
</form>

<fieldset [disabled]="haveDisabledFields">
  <app-differences-modal [domainCode]="selectedDomainCode" [fieldName]="selectedFieldName"
    [toTranslate]="selectedShownFieldName" [type]="selectedDifferenceType" [differences]="selectedDifference"
    [isOpen]="differencesModalIsOpen" (modalClosed)="closeDifferencesModal()"
    (valueChosen)="selectNewValue($event)"></app-differences-modal>
</fieldset>