<!-- <fieldset [disabled] = "formDisabled"> -->
  <form #f="ngForm" novalidate (click)="_accordionAPFService.handleOpenProperty(group, f)">
    <accordion-group #group class="panel">
      <div accordion-heading class="acc-note-headline" role="tab">
        <h4 class="panel-title">
          <a role="button">
            <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>{{'UBZ.SITE_CONTENT.1111110011' | translate }}
            <span class="state" [ngClass]="{'green' : (pageIsValid), 'red' : !(pageIsValid)}"></span>
          </a>
        </h4>
      </div>
      <div class="panel-collapse collapse in" role="tabpanel">
        <div class="panel-body">
          <div class="panel-box">
            <table class="table">
              <tbody>
                <tr *ngFor="let prop of pageContent; let i = index">
                  <td *ngIf="prop.customer && (!prop.customer.ndgType || prop.customer.ndgType === 'PF')">
                    {{prop.customer ? prop.customer.name : ''}} {{prop.customer ? prop.customer.surname : ''}} <br/>
                    <span>{{prop.customer ? prop.customer.taxNum : ''}} - {{prop.customer ? prop.ownershipPerc : ''}}%</span>
                  </td>
                  <td *ngIf="prop.customer && prop.customer.ndgType !== 'PF'">
                    {{prop.customer ? prop.customer.heading : ''}} <br/>
                    <span>{{prop.customer ? prop.customer.vatNum : ''}} - {{prop.customer ? prop.ownershipPerc : ''}}%</span>
                  </td>
                  <td>
                    <ng-container *appAuthKey="'UBZ_PROPERTY_DELETE'">
                      <button type="button" class="btn btn-empty waves-effect waves-secondary" (click)="delete(i)">
                        <i class="fa fa-trash-o" aria-hidden="true"></i>
                      </button>
                    </ng-container>
                    <ng-container *appAuthKey="'UBZ_PROPERTY_MODIFY'">
                      <button type="button" class="btn btn-empty waves-effect waves-secondary" (click)="openModal('modify', prop , i)">
                        <i class="fa fa-pencil-square-o" aria-hidden="true"></i>
                      </button>
                    </ng-container>
                  </td>
                </tr>
              </tbody>
            </table>
            <div class="row" *ngFor="let el of propertyAmountsStructure">
              <div class="col-sm-6">
                <label>{{rightTypeDomain && rightTypeDomain[el.rightType] ? (rightTypeDomain[el.rightType].translationCod | translate) : ''}}</label>
              </div>
              <div class="col-sm-6 text-right">
                <span class="badge circular default">
                  {{ el.sum | number:'1.0-2'}}
                </span>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12">
                <ng-container *appAuthKey="'UBZ_PROPERTY_ADD'">
                  <button type="button" class="btn btn-empty" (click)="openModal('add')">
                    <i class="fa fa-plus"></i>{{'UBZ.SITE_CONTENT.1111101011' | translate }}
                  </button>
                </ng-container>
              </div>
            </div>
          </div>
        </div>
      </div>
    </accordion-group>
  </form>

  <app-property-modal
    *ngIf="selectedProperty && modalIsOpen"
    [modalType] = "modalType"   
    [isOpen] = "modalIsOpen"
    [property]="selectedProperty" 
    [propertyArray]="pageContent"
    [propertyIndex]="selectedPropertyIndex"           
    [actualAmount]="propertyAmountsStructure"	
    [domainResp]="domainResp"
    (modalClose) = "modalIsOpen = false"
    (modalSubmit)="submitModal($event)">
  </app-property-modal>
  <!-- CUSTOM MODAL PER GESTIRE LA DELETE DELL'USER -->
  <app-custom-modal 
    [modalType] = "'delete'"
    [isOpen] = "deleteModalIsOpen"
    [largeModalFlag] = "false"
    [headerTitle] = "'UBZ.SITE_CONTENT.1111110000'"
    [positionId]="''" 
    [idCode]="''"   
    [apfString] = "''"
    [messagesArray] = "['UBZ.SITE_CONTENT.1111110001']"
    [buttonTitle] = "['UBZ.SITE_CONTENT.11110', 'UBZ.SITE_CONTENT.100000']"
    [disabledFlag] = "false"  
    (modalSubmit) = "submitDeleteModal($event)"
    (modalClose)="closeDeleteModal()">
  </app-custom-modal>
<!-- </fieldset> -->