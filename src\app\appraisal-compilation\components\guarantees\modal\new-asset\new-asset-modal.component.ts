import {
  Component,
  OnInit,
  OnChanges,
  SimpleChanges,
  Input,
  Output,
  EventEmitter,
  Inject
} from '@angular/core';
import { DomainService } from '../../../../../shared/domain/domain.service';
import { PositionService } from '../../../../../shared/position/position.service';
import { AppraisalCompilationService } from '../../../../service/appraisal-compilation.service';
import { AssetService } from '../../../../../simulation/services/asset/asset.service';
import { APP_CONSTANTS, IAppConstants } from '../../../../../app.constants';

@Component({
  selector: 'app-new-asset-modal',
  templateUrl: './new-asset-modal.component.html',
  styleUrls: ['./new-asset-modal.component.css']
})
export class NewAssetModalComponent implements OnInit, OnChanges {
  @Input() isOpen: boolean;
  @Input() positionId: string;
  @Input() requestId: string; // Usata per ricavare le garanzie;
  @Input() appraisalScope: string;
  @Output() modalClose = new EventEmitter();
  categories: any;
  selectedCategory = '';
  selectedGuarantee = '';
  guaranteesList: any[] = [];
  collateral: any = null;

  constructor(
    private domainService: DomainService,
    private appraisalCompilationService: AppraisalCompilationService,
    private _assetService: AssetService,
    private positionService: PositionService,
    @Inject(APP_CONSTANTS) private _constants: IAppConstants
  ) {}

  ngOnInit() {
    this.domainService
      .newGetDomain('UBZ_DOM_CATEGORY_TYPE', 'IMM')
      .subscribe(x => {
        this.categories = x;
      });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['isOpen'] && changes['isOpen'].currentValue) {
      if (this.requestId) {
        this.positionService
          .getCollateralData(this.requestId)
          .subscribe(res => {
            this.guaranteesList = res;
            if (res.length === 1) {
              this.selectedGuarantee = res[0].progCollateral;
            }
          });
      }
    }
    if (changes['appraisalScope'] && changes['appraisalScope'].currentValue) {
      this.selectedCategory = changes['appraisalScope'].currentValue;
    }
  }

  closeModal() {
    this.isOpen = false;
    this.modalClose.emit();
    this.selectedGuarantee = '';
  }

  addAsset() {
    const guarantee = this.getSelectedGuaranteeObject(this.selectedGuarantee);

    if (this.selectedGuarantee !== '') {
      this.collateral = {
        progCollateral: this.selectedGuarantee,
        collateralTecForm: guarantee.collateralTecForm,
        garantType: guarantee.garantType
      };
    } else {
      this.collateral = null;
    }
    this.appraisalCompilationService
      .addAsset(this.positionId, this.selectedCategory, this.collateral)
      .subscribe(x => {
        this.closeModal();
      });
  }

  private getSelectedGuaranteeObject(guaranteeId: string): any {
    for (const g of this.guaranteesList) {
      if (g.progCollateral === this.selectedGuarantee) {
        return g;
      }
    }
  }
}
