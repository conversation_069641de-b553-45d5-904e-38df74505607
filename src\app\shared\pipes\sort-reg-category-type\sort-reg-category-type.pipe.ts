import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'sortRegCategoryType'
})
export class SortRegCategoryTypePipe implements PipeTransform {

  transform(value: any, args?: any): any {
    let tmpIndex;
    let tmpElement = {};
    for (const key in value) {
      if (value.hasOwnProperty(key)) {
        tmpIndex = key;
        for (const key2 in value) {
          if (Number(key) < Number(key2)) {
            if (
              this.firstIsLower(
                value[tmpIndex].domCode,
                value[key2].domCode
              ) === false
            ) {
              tmpIndex = key2;
            }
          }
        }
        tmpElement = value[key];
        value[key] = value[tmpIndex];
        value[tmpIndex] = tmpElement;
      }
    }
    return value;
  }

  private firstIsLower(first: string, second: string): boolean {
    const firstLecter = first.substring(0, 1);
    const secondLecter = second.substring(0, 1);
    const firstNumber = Number(first.substring(1));
    const secondNumber = Number(second.substring(1));
    if ( firstLecter < secondLecter || (firstLecter === secondLecter && (firstNumber < secondNumber)) ) {
      return true;
    }else {
      return false;
    }
  }

}
