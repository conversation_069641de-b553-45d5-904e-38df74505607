import { Component, OnInit, Input, HostListener, Inject } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ChecklistService } from '../../../shared/checklist/service/checklist.service';
import { Location } from '@angular/common';
import { DOCUMENT } from '@angular/platform-browser';

@Component({
  selector: 'app-document',
  templateUrl: './document.component.html',
  styleUrls: ['./document.component.css']
})
export class DocumentComponent implements OnInit {
  @Input() positionId: string;
  @Input() requestId: string;
  @Input() isRequestId: boolean;
  pdfSrc: any;
  fitToPage = true;
  prog: any;
  renderText = true;

  constructor(
    private checklistService: ChecklistService,
    private activatedRoute: ActivatedRoute,
    private location: Location,
    @Inject(DOCUMENT) private document: any
  ) { }

  ngOnInit() {
    this.activatedRoute.params.switchMap(params => {
      this.prog = params.prog;
      return this.checklistService.getVersions(this.prog);
    })
      .switchMap(x => {
        let max = x[0];
        x.forEach(el => {
          if (el.uploadDate > max.uploadDate) {
            max = el;
          }
        });
        return this.checklistService.displayDocument(this.isRequestId ? null : this.positionId, this.isRequestId ? this.positionId : this.requestId,
          max.prog, max.versionId);
      })
      .subscribe(res => {
        const content = res.header.get('content-disposition');
        this.pdfSrc = new Uint8Array(res.document);
      });
  }

  goBack() {
    this.location.back();
  }

  @HostListener("window:scroll", [])
  onWindowScroll() {
    const button = this.document.getElementById("scroll-button");
    button.style.bottom = "20px";
    button.style.right = "20px";
  }
}

