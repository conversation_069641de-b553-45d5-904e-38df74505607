<div class="row row-eq-height" *ngIf="data">
  <div class="col-sm-4" *ngIf="data.financialValuationEval">
    <div class="box">
      <h4>{{'UBZ.SITE_CONTENT.10000011011' | translate }}</h4>
      <div class="row" *appAuthKey="'UBZ_APPRAISAL.NOT_EPC'">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000011100' | translate }}</label></div>
        <div class="col-sm-6 text-right">{{ data.financialValuationEval.marketValue | currency:'EUR':true:'1.2-2'}}
        </div>
      </div>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000011101' | translate }}</label></div>
        <div class="col-sm-6 text-right">{{data.financialValuationEval.insuranceValue | currency:'EUR':true:'1.2-2'}}
        </div>
      </div>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.1001111010' | translate }}</label></div>
        <div class="col-sm-6 text-right">{{data.financialValuationEval.totBookValue | currency:'EUR':true:'1.2-2'}}
        </div>
      </div>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.1001111001' | translate }}</label></div>
        <div class="col-sm-6 text-right">{{data.financialValuationEval.totPledgedValue | currency:'EUR':true:'1.2-2'}}
        </div>
      </div>

      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.100000100010'| translate }} </label></div>
        <div class="col-sm-6 text-right">{{data.financialValuationEval.propertyValue | currency:'EUR':true:'1.2-2'}}
        </div>


      </div>
      <!-- FIXME - togliere ?

        <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.1001110111' | translate }}</label></div>
        <div class="col-sm-6 text-right">{{data.financialValuationEval.totPrudentialValue | currency:'EUR':true:'1.2-2'}}</div>
      </div> -->
      <!-- <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000011110' | translate }}</label></div>
        <div class="col-sm-6 text-right">{{data.financialValuationEval.actualValue | currency:'EUR':true:'1.2-2'}}</div>
      </div> -->
      <!-- <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000011111' | translate }}</label></div>
        <div class="col-sm-6 text-right">{{data.financialValuationEval.ctuValue | currency:'EUR':true:'1.2-2'}}</div>
      </div> -->
      <!-- <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000100000' | translate }}</label></div>
        <div class="col-sm-6 text-right">{{data.financialValuationEval.originalValue | currency:'EUR':true:'1.2-2'}}</div>
      </div> -->
    </div>
  </div>
  <div class="col-sm-4" *ngIf="data.reComplex">
    <div class="box">
      <h4>{{'UBZ.SITE_CONTENT.1100000011' | translate }}</h4>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000100001' | translate }}</label></div>
        <div class="col-sm-6">{{ serviceDom[data.reComplex.publicServices] ? (
          serviceDom[data.reComplex.publicServices].translationCod | translate ) : ''}}</div>
      </div>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000100010' | translate }}</label></div>
        <div class="col-sm-6">{{ serviceDom[data.reComplex.publicTransports] ?
          (serviceDom[data.reComplex.publicTransports].translationCod | translate) : '' }}</div>
      </div>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000100011' | translate }}</label></div>
        <div class="col-sm-6">{{data.reComplex.areaDesc}}</div>
      </div>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000100101' | translate }}</label></div>
        <div class="col-sm-6">{{ buildDom[data.reComplex.buildFeatures] ?
          (buildDom[data.reComplex.buildFeatures].translationCod | translate) : ''}}</div>
      </div>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000100110' | translate }}</label></div>
        <div class="col-sm-6">{{data.reComplex.urbanConvenctNotes}}</div>
      </div>
      <!-- Sezione trasparenza -->
      <div class="row" *ngIf="!freeAppraisalCheck && this.billingAmount">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10011011101' | translate}}</label></div>
        <div class="col-sm-6">{{this.billingAmount | currency:'EUR':true:'1.2-2'}}</div>
      </div>
      <div class="row" *ngIf="!freeAppraisalCheck && this.billingDate">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10011011110' | translate}}</label></div>
        <div class="col-sm-6">{{this.billingDate | date: 'dd/MM/yyyy'}}</div>
      </div>
    </div>
  </div>
  <div class="col-sm-4"
    *ngIf="data.planningProgress && data.finVal && data.finVal.resourceItem && data.finVal.resourceItem[0].resourceItemType === 'IMM'">
    <!-- solo se è sal -->
    <div class="box">
      <h4>{{'UBZ.SITE_CONTENT.10000100111' | translate }}</h4>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000101000' | translate }}</label></div>
        <div class="col-sm-6">{{ data.planningProgress.executedActivityDesc }}</div>
      </div>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000101001' | translate }}</label></div>
        <div class="col-sm-6">{{ planningDom[data.planningProgress.planningProcess] ?
          (planningDom[data.planningProgress.planningProcess].translationCod | translate) : '' }}</div>
      </div>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000101010' | translate }}</label></div>
        <div class="col-sm-6">{{ data.planningProgress.planningEndDate | date: 'dd/MM/yyyy'}}</div>
      </div>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000101011' | translate }}</label></div>
        <div class="col-sm-6">{{ data.planningProgress.surveyDate | date: 'dd/MM/yyyy'}}</div>
      </div>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000101100' | translate }}</label></div>
        <div class="col-sm-6">{{ registryDom[data.planningProgress.objectsReRegCorr] ?
          (registryDom[data.planningProgress.objectsReRegCorr].translationCod | translate) : '' }}</div>
      </div>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000101101' | translate }}</label></div>
        <div class="col-sm-6">{{ data.planningProgress.physicalSalPerc }}</div>
      </div>
    </div>
  </div>
  <div class="col-sm-4"
    *ngIf="data.planningProgress && data.finVal && data.finVal.resourceItem && data.finVal.resourceItem[0].resourceItemType === 'MOB'">
    <div class="box">
      <h4>{{'UBZ.SITE_CONTENT.10000100111' | translate }}</h4>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.100001001' | translate }}</label></div>
        <div class="col-sm-6">{{ data.planningProgress.executedActivityDesc }}</div>
      </div>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000101110' | translate }}</label></div>
        <div class="col-sm-6">{{ registryDom[data.planningProgress.objectsReRegCorr] ?
          (registryDom[data.planningProgress.objectsReRegCorr].translationCod | translate) : '' }}</div>
      </div>
      <div class="row">
        <div class="col-sm-6"><label>{{'UBZ.SITE_CONTENT.10000101101' | translate }}</label></div>
        <div class="col-sm-6">{{ data.planningProgress.physicalSalPerc }}</div>
      </div>
    </div>
  </div>
</div>
<div class="row row-eq-height" *ngIf="data">
  <div class="col-sm-4">
    <div class="box">
      <h4>{{'UBZ.SITE_CONTENT.11111111101'| translate }}</h4>
      <div class="row" *ngFor="let riskType of allRischio">
        <div class="col-sm-6"><label>{{riskType.translationCod | translate}}</label></div>
        <div class="col-sm-6 text-right">{{getRiskLevel(riskType.domCode)| translate}}</div>
      </div>
    </div>
  </div>
</div>