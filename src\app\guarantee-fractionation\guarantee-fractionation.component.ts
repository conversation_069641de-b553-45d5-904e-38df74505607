import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { GuaranteeFractionationService } from './services/guarantee-fractionation.service';

@Component({
  selector: 'app-guarantee-fractionation',
  templateUrl: './guarantee-fractionation.component.html',
  styleUrls: ['guarantee-fractionation.component.css']
})
export class GuaranteeFractionationComponent implements OnInit {
  constructor(
    private activatedRoute: ActivatedRoute,
    public guaranteeFractionationService: GuaranteeFractionationService
  ) {}

  ngOnInit() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.guaranteeFractionationService.ndg = params['ndg'];
      this.guaranteeFractionationService.familyAsset = params['familyAsset'];
      this.guaranteeFractionationService.initializeStaticHeaderArray();
      // Recupera le informazioni del ndg
      this.guaranteeFractionationService
        .getNdgDetails(this.guaranteeFractionationService.ndg)
        .subscribe(response => {
          if (response && response.heading) {
            this.guaranteeFractionationService.staticHeaderArray.find(
              obj => obj['label'] === 'Heading'
            )['value'] = response.heading;
          }
        });
    });
  }
}
