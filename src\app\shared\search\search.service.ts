import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';
import { Observable } from 'rxjs/Observable';

@Injectable()
export class SearchService {
  private baseUrl = '/UBZ-ESA-RS/service/search/v1/positions';
  private baseUrlV2 = '/UBZ-ESA-RS/service/search/v2/positions';

  constructor(private http: Http) {}

  getSimulationsCounts(
    inChargeUser: boolean,
    inChargeBranch: boolean
  ): Observable<any> {
    const url = `${this.baseUrlV2}/simulation/counts`;
    return this.http
      .post(url, { inChargeUser, inChargeBranch })
      .map((resp: Response) => resp.json());
  }

  getSimulationsData(
    excel: boolean,
    page: number,
    pageSize: number,
    orderBy: string,
    asc: boolean,
    filter: any
  ): Observable<any> {
    const url = `${this.baseUrl}/simulation/data`;
    return this.http
      .post(url, { excel, page, pageSize, orderBy, asc, filter })
      .map((resp: Response) => resp.json());
  }

  getRequestsCounts(
    inChargeUser: boolean,
    inChargeBranch: boolean
  ): Observable<any> {
    const url = `${this.baseUrlV2}/appraisal/counts`;
    return this.http
      .post(url, { inChargeUser, inChargeBranch })
      .map((resp: Response) => resp.json());
  }

  getRequestsData(
    excel: boolean,
    page: number,
    pageSize: number,
    orderBy: string,
    asc: boolean,
    filter: any
  ): Observable<any> {
    const url = `${this.baseUrl}/appraisal/data`;
    return this.http
      .post(url, { excel, page, pageSize, orderBy, asc, filter })
      .map((resp: Response) => resp.json());
  }

  getTaskCounts(
    inChargeUser: boolean,
    inChargeBranch: boolean,
    statusSal?: string,
    phase?: string
  ): Observable<any> {
    const url = `${this.baseUrl}/task/counts`;
    return this.http
      .post(url, { inChargeUser, inChargeBranch, statusSal, phase })
      .map((resp: Response) => resp.json());
  }

  getSalParam(): Observable<any> {
    const url = '/UBZ-ESA-RS/service/configuration/v1/slaStatusManag';
    return this.http.get(url).map((resp: Response) => resp.json());
  }

  getTaskData(
    excel: boolean,
    page: number,
    pageSize: number,
    orderBy: string,
    asc: boolean,
    filter: any
  ): Observable<any> {
    const url = `${this.baseUrl}/task/data`;
    return this.http
      .post(url, { excel, page, pageSize, orderBy, asc, filter })
      .map((resp: Response) => resp.json());
  }

  getRequestsRESCounts(
    inChargeUser: boolean,
    inChargeBranch: boolean
  ): Observable<any> {
    const url = `${this.baseUrl}/appraisal/realestatesurv/counts`;
    return this.http
      .post(url, { inChargeUser, inChargeBranch })
      .map((resp: Response) => resp.json());
  }

  getRequestsRESData(
    excel: boolean,
    page: number,
    pageSize: number,
    orderBy: string,
    asc: boolean,
    filter: any
  ): Observable<any> {
    const url = `${this.baseUrl}/appraisal/realestatesurv/data`;
    return this.http
      .post(url, { excel, page, pageSize, orderBy, asc, filter })
      .map((resp: Response) => resp.json());
  }

  getSampleCheckCounts(): Observable<any> {
    const url = `${this.baseUrl}/sampleCheck/counts`;
    return this.http.get(url).map((resp: Response) => resp.json());
  }

  getSampleCheckData(
    excel: boolean,
    page: number,
    pageSize: number,
    orderBy: string,
    asc: boolean
  ): Observable<any> {
    const url = `${this.baseUrl}/sampleCheck/data`;
    return this.http
      .post(url, { excel, page, pageSize, orderBy, asc })
      .map((resp: Response) => resp.json());
  }
}
