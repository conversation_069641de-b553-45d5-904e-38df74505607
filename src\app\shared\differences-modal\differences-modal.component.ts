import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  OnChanges,
  SimpleChanges
} from '@angular/core';
import { DomainService } from '../../shared/domain/domain.service';
@Component({
  selector: 'app-differences-modal',
  templateUrl: './differences-modal.component.html',
  styleUrls: ['./differences-modal.component.css'],
  providers: [DomainService]
})
export class DifferencesModalComponent implements OnInit, OnChanges {
  @Input() isOpen = false;
  @Input() differences: any = {};
  @Input() type: string;
  @Input() fieldName: string;
  @Input() toTranslate: string;
  @Input() domainCode: string;
  @Output() modalClosed: EventEmitter<any> = new EventEmitter();
  @Output() valueChosen: EventEmitter<any> = new EventEmitter();

  selectedValue: any;
  private domains: any[] = [];
  private _subscription;

  constructor(private _domainService: DomainService) {}

  ngOnInit() {}

  ngOnChanges(changes: SimpleChanges) {
    if (
      ((changes &&
        changes['domainCode'] &&
        changes['domainCode'].isFirstChange) ||
        (changes &&
          changes['domainCode'] &&
          changes['domainCode'].previousValue &&
          changes['domainCode'].currentValue !==
            changes['domainCode'].previousValue)) &&
      this.domainCode
    ) {
      this._subscription = this._domainService
        .newGetDomain(this.domainCode)
        .subscribe(res => {
          this.domains = res;
        });
    }
  }

  closeModal() {
    this.isOpen = false;
    this.selectedValue = '';
    this.modalClosed.emit();
  }

  confirmValue() {
    this.isOpen = false;
    const OBJ = this.createResponse();
    this.selectedValue = '';
    this.valueChosen.emit(OBJ);
  }

  private createResponse(): any {
    const obj = {};
    obj['fieldName'] = this.fieldName;
    obj['selectedValue'] = this.selectedValue;
    return JSON.parse(JSON.stringify(obj));
  }
}
