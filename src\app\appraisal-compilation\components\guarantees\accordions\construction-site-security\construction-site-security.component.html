<!-- fixme - togliere codice commentato e rendi dinamica la tabella -->
<form #f="ngForm" novalidate (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>{{'UBZ.SITE_CONTENT.111111011' | translate }}
          <span class="state" [ngClass]="{'green' : pageIsValid, 'red' : !pageIsValid}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <div class="panel-box">
      	   <div class="row">
      		     <div class="col-sm-3">
                 <label>{{'UBZ.SITE_CONTENT.111111100' | translate }}<span *ngIf="requiredFields">*</span></label>
      		       <div class="radio-buttons">
      			          <div class="custom-radio">
      			               <input type="radio" name="n1" id="1" class="radio" value='Y' [(ngModel)]="pageContent.dlgsSubjectFlag" [required]="requiredFields">
      			               <label for="1">{{'UBZ.SITE_CONTENT.100011011' | translate }}</label>
      			          </div>
      			          <div class="custom-radio">
      			               <input type="radio" name="n1" id="2" class="radio" value='N' [(ngModel)]="pageContent.dlgsSubjectFlag" [required]="requiredFields">
      			               <label for="2">{{'UBZ.SITE_CONTENT.100011100' | translate }}</label>
      			          </div>
      		       </div>
      		     </div>
      		     <div class="col-sm-3">
      		         <label>{{'UBZ.SITE_CONTENT.111111101' | translate }}<span *ngIf="requiredFields">*</span></label>
      		         <input type="text" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.1000010000' | translate }}" [(ngModel)]="pageContent.editedSecurPlan" name="editedSecurPlan" [required]="requiredFields"/>
      		     </div>
      	   </div>
      	</div>
      	<table class="uc-table">
      	  <thead>
      		<tr>
      		  <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.10010110' | translate }}</th>
      		  <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.111111110' | translate }}</th>
      		  <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.111111111' | translate }}</th>
      		  <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.1000000000' | translate }}</th>
      		</tr>
      	  </thead>
      	  <tbody *ngIf="pageContent && pageContent.securityData">
      		<tr>
      		  <td data-label="Descrizione">{{'UBZ.SITE_CONTENT.1000000001' | translate }}</td>
      		  <td data-label="Numero"><input type="text" class="form-control" [(ngModel)]="pageContent.securityData['DIL'].securDataNum" (ngModelChange)="checkDependency()" name="securDataNumDIL" maxlength="20"/></td>
						<td data-label="Data (gg/mm/aaaa)">
							<app-calendario
								[(ngModel)]="pageContent.securityData['DIL'].securDataDate"
								[name]="'securDataDateDIL'"
								[placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
								[required]="false"
								(ngModelChange)="checkDependency()"
								[minDate]="startDate"
								[maxDate]="todayDate">
							</app-calendario>
						</td>
      		  <td data-label="Protocollo"><input type="text" class="form-control" [(ngModel)]="pageContent.securityData['DIL'].protocol" (ngModelChange)="checkDependency()" name="protocolDIL"/></td>
      		</tr>
      		<tr>
      		  <td data-label="Descrizione">{{'UBZ.SITE_CONTENT.1000000010' | translate }}</td>
      		  <td data-label="Numero"><input type="text" class="form-control" [(ngModel)]="pageContent.securityData['ASL'].securDataNum" (ngModelChange)="checkDependency()" name="securDataNumASL" maxlength="20"/></td>
						<td data-label="Data (gg/mm/aaaa)">
							<app-calendario
								[(ngModel)]="pageContent.securityData['ASL'].securDataDate"
								[name]="'securDataDateASL'"
								[placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
								[required]="false"
								(ngModelChange)="checkDependency()"
								[minDate]="startDate"
								[maxDate]="todayDate">
							</app-calendario>
						</td>
      		  <td data-label="Protocollo"><input type="text" class="form-control" [(ngModel)]="pageContent.securityData['ASL'].protocol" (ngModelChange)="checkDependency()" name="protocolASL"/></td>
      		</tr>
      		<tr>
      		  <td data-label="Descrizione">{{'UBZ.SITE_CONTENT.1000000011' | translate }}</td>
      		  <td data-label="Numero"><input type="text" class="form-control" [(ngModel)]="pageContent.securityData['IPL'].securDataNum" (ngModelChange)="checkDependency()" name="securDataNumIPL" maxlength="20"/></td>
						<td data-label="Data (gg/mm/aaaa)">
							<app-calendario
								[(ngModel)]="pageContent.securityData['IPL'].securDataDate"
								[name]="'securDataDateIPL'"
								[placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
								[required]="false"
								(ngModelChange)="checkDependency()"
								[minDate]="startDate"
								[maxDate]="todayDate">
							</app-calendario>
						</td>
      		  <td data-label="Protocollo"><input type="text" class="form-control" [(ngModel)]="pageContent.securityData['IPL'].protocol" (ngModelChange)="checkDependency()" name="protocolIPL"/></td>
      		</tr>
      		<tr>
      		  <td data-label="Descrizione">{{'UBZ.SITE_CONTENT.1000000100' | translate }}</td>
      		  <td data-label="Numero"><input type="text" class="form-control" [(ngModel)]="pageContent.securityData['DPS'].securDataNum" (ngModelChange)="checkDependency()" name="securDataNumDPS" maxlength="20"/></td>
						<td data-label="Data (gg/mm/aaaa)">
							<app-calendario
								[(ngModel)]="pageContent.securityData['DPS'].securDataDate"
								[name]="'securDataDateDPS'"
								[placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
								[required]="false"
								(ngModelChange)="checkDependency()"
								[minDate]="startDate"
								[maxDate]="todayDate">
							</app-calendario>
						</td>
      		  <td data-label="Protocollo"><input type="text" class="form-control" [(ngModel)]="pageContent.securityData['DPS'].protocol" (ngModelChange)="checkDependency()" name="protocolDPS"/></td>
      		</tr>
      	  </tbody>
      	</table>
      	<div class="panel-box">
      	  <div class="row">
      		<div class="col-sm-6 form-group">
      		  <label>{{'UBZ.SITE_CONTENT.1000000101' | translate }}<span *ngIf="requiredFields">*</span></label>
      		  <div class="radio-buttons">
      			<div class="custom-radio">
      			  <input type="radio" name="n2" id="5" class="radio" value='Y' [(ngModel)]="pageContent.firePreventionFlag" [required]="requiredFields">
      			  <label for="5">{{'UBZ.SITE_CONTENT.100011011' | translate }}</label>
      			</div>
      			<div class="custom-radio">
      			  <input type="radio" name="n2" id="6" class="radio" value='N' [(ngModel)]="pageContent.firePreventionFlag" [required]="requiredFields">
      			  <label for="6">{{'UBZ.SITE_CONTENT.100011100' | translate }}</label>
      			</div>
      		  </div>
      		</div>
      	  </div>
      	  <div class="row" *ngIf="pageContent && pageContent.securityData && pageContent.securityData['PVF']">
						<div class="col-sm-12">
							<h4>{{'UBZ.SITE_CONTENT.1000000110' | translate }}</h4>
						</div>
						<div class="col-sm-3 form-group" >
							<label>{{'UBZ.SITE_CONTENT.1000000000' | translate }}</label>
							<input type="text" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.1000010000' | translate }}" [(ngModel)]="pageContent.securityData['PVF'].protocol" name="protocolPVF"/>
						</div>
						<div class="col-sm-3 form-group" >
							<label>{{'UBZ.SITE_CONTENT.1000000111' | translate }}</label>
							<input type="text" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.1000010000' | translate }}" [(ngModel)]="pageContent.securityData['PVF'].procedure" name="procedurePVF"/>
						</div>
						<div class="col-sm-3 form-group">
							<label>{{'UBZ.SITE_CONTENT.10111' | translate }}</label>
							<app-calendario
								[placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate"
								[(ngModel)]="pageContent.securityData['PVF'].securDataDate"
								[name]="'securDataDatePVF'">
							</app-calendario>
						</div>
						<div class="col-sm-3 form-group">
							<label>{{'UBZ.SITE_CONTENT.1010000000' | translate }}</label>
							<input type="text" class="form-control" placeholder="{{'UBZ.SITE_CONTENT.1000010000' | translate }}" [(ngModel)]="pageContent.securityData['PVF'].opinion" name="optionPVF"/>
						</div>
      	  </div>
      	  <h4>{{'UBZ.SITE_CONTENT.1000001110' | translate }}</h4>
      	  <table class="table">
      		<tbody>
      		  <tr *ngFor="let securityStaffElement of pageContent.securityStaff; let i = index">
							<td>
								{{ (personRoleDomain && personRoleDomain[securityStaffElement.personRole]) ? (personRoleDomain[securityStaffElement.personRole].translationCod | translate) : '' }}, {{securityStaffElement.firstName}} {{securityStaffElement.lastName}}<br/>
									<span>{{securityStaffElement.email}}  -  {{securityStaffElement.phoneNumber}}</span>
							</td>
							<td>
								<ng-container *appAuthKey="'UBZ_CONSTRUCTION.SITE.SECURITY_DELETE'">
									<button type="button" class="btn btn-empty waves-effect waves-secondary" (click)="delete(i)">
										<i class="fa fa-trash-o" aria-hidden="true"></i>
									</button>
								</ng-container>
								<ng-container *appAuthKey="'UBZ_CONSTRUCTION.SITE.SECURITY_MODIFY'">
									<button type="button" class="btn btn-empty waves-effect waves-secondary" (click)="openModal('modify', securityStaffElement , i)">
										<i class="fa fa-pencil-square-o" aria-hidden="true"></i>
									</button>
								</ng-container>
							</td>
      		  </tr>
      		</tbody>
      	  </table>
      	  <div class="row">
      		<div class="col-sm-12">
						<ng-container *appAuthKey="'UBZ_CONSTRUCTION.SITE.SECURITY_ADD'">
							<button type="button" class="btn btn-empty" (click)="openModal('add')">
								<i class="fa fa-plus"></i> {{'UBZ.SITE_CONTENT.111110100' | translate }}
							</button>
						</ng-container>
      		</div>
      	  </div>
      	</div>
      </div>
    </div>
  </accordion-group>
</form>

<app-construction-site-security-modal
	*ngIf="selectedProperty && modalIsOpen"
	[modalType] = "modalType"
	[isOpen] = "modalIsOpen"
	[property]="selectedProperty"
	[propertyIndex]="selectedPropertyIndex"
	(modalClose) = "modalIsOpen = false"
	(modalSubmit)="submitModal($event)">
</app-construction-site-security-modal>
<!-- CUSTOM MODAL gestisce la cancellazione rilevamento prezzi medi -->
<app-custom-modal
	[modalType] = "'delete'"
	[isOpen] = "deleteModalIsOpen"
	[largeModalFlag] = "false"
	[headerTitle] = "'UBZ.SITE_CONTENT.111110111'"
	[positionId]="''"
	[idCode]="''"
	[apfString] = "''"
	[messagesArray] = "['UBZ.SITE_CONTENT.111111000']"
	[buttonTitle] = "['UBZ.SITE_CONTENT.11110', 'UBZ.SITE_CONTENT.100000']"
	[disabledFlag] = "false"
	(modalSubmit) = "submitDeleteModal()"
	(modalClose) = "closeDeleteModal()">
</app-custom-modal>
