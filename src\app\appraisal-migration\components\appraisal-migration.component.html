<app-position-header [positionId]="appraisalId" [wizardCode]="wizardCode" [lockingUser]="taskLockingUser">
</app-position-header>

<div class="col-sm-12 section-headline">
  <h1><i class="icon-dashboard"></i>{{'UBZ.SITE_CONTENT.101101000' | translate }}</h1>
  <h2>{{'UBZ.SITE_CONTENT.10001011011' | translate }}</h2>
  <section id="breadcrumbs" class="breadcrumbs">
    <div class="row">
      <div class="col-sm-12">
        <ul>
          <li><a role="button" (click)="navigateBack()">{{'UBZ.SITE_CONTENT.101101000' | translate }}</a></li>
          <li>{{'UBZ.SITE_CONTENT.10001011011' | translate }}</li>
        </ul>
      </div>
    </div>
  </section>
</div>
<app-migration-tab-manager *ngIf="appraisal && appraisal.finVal" [assets]="appraisal.finVal.resourceItem" [activeCategories]="activeCategories"
  [selectedAsset]="selectedAsset" [isAppraisalConvalidated]="appraisal.finVal.isConvalidated"
  (onSelectAsset)="changeTab($event)">
</app-migration-tab-manager>
<div class="tab-content">
  <br />
  <app-appraisal-migration-info *ngIf="!selectedAsset && appraisal" [data]="appraisal" [appraisalId]="appraisalId">
  </app-appraisal-migration-info>
  <app-appraisal-migration-asset-info *ngIf="selectedAsset" [assetId]="selectedAsset.resourceItemId"
    [resourceItemType]="resourceItemType" [resourceItemCategory]="resourceItemCategory">
  </app-appraisal-migration-asset-info>
</div>