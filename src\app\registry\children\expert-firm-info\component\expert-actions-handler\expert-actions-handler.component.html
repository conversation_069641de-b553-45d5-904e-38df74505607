<div class="row step-navigation background-transparent">
  <div class="col-sm-12 btn-set">
    <ng-container *ngIf="subjectType !== 'SOC'">
      <button *appAuthKey="'UBZ_REGISTRY.EXPERT_ACTIONS_SUSPENSION'" type="button" class="btn btn-empty waves-effect waves-secondary pull-right" (click)="openModal(SOSPENSION_CODE)">
        <i class="fa icon-add"></i> {{'UBZ.SITE_CONTENT.1011111101' | translate }}
      </button>
      <button *appAuthKey="'UBZ_REGISTRY.EXPERT_ACTIONS_REVOKE'" class="btn btn-empty waves-effect waves-secondary pull-right" type="button" (click)="openModal(REVOKE_CODE)">
        <i aria-hidden="true" class="fa fa-times"></i> {{'UBZ.SITE_CONTENT.1001010110' | translate }}
      </button>
      <button *appAuthKey="'UBZ_REGISTRY.EXPERT_ACTIONS_ACTIVATION'" type="button" class="btn btn-empty waves-effect waves-secondary pull-right" (click)="openModal(ACTIVATION_CODE)">
        <i class="fa icon-add"></i> {{'UBZ.SITE_CONTENT.1011111110' | translate }}
      </button>
    </ng-container>
    <button type="button" class="btn btn-empty waves-effect waves-secondary pull-right" (click)="downloadSupplyPressed()">
      <i class="fa fa-file-pdf-o"></i> {{'UBZ.SITE_CONTENT.1011111111' | translate }}
    </button>
  </div>
</div>

<div class="modal fade" bsModal #addNoteModal="bs-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" [config]="{show: 'true'}" *ngIf="isOpen" (onHidden)="closeModal()">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <form #f="ngForm" (ngSubmit)="saveNote()" novalidate>
        <div class="modal-header">
          <h2 *ngIf="localNoteType === SOSPENSION_CODE">{{'UBZ.SITE_CONTENT.1010110110' | translate }}</h2>
          <h2 *ngIf="localNoteType === REVOKE_CODE">{{'UBZ.SITE_CONTENT.11111101' | translate }}</h2>
          <button type="reset" class="close pull-right" aria-label="Close" (click)="closeModal()">
            <i class="icon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-sm-12 form-group">
              <label>{{'UBZ.SITE_CONTENT.10001000' | translate }}*</label>
              <input type="text" class="form-control required" data-placement="bottom"  name="noteTitle" [(ngModel)]="action.noteTitle"
                required placeholder="{{'UBZ.SITE_CONTENT.10001010' | translate }}" maxlength="50"/>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12 form-group">
              <label>{{'UBZ.SITE_CONTENT.10001001' | translate }}*</label>
              <textarea rows="4" class="form-control required" data-placement="bottom" name="noteText" required [(ngModel)]="action.noteText"
                placeholder="{{'UBZ.SITE_CONTENT.10001011' | translate }}..." required></textarea>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button *appAuthKey="'UBZ_REGISTRY.EXPERT_ACTIONS_SAVE_NOTE'" class="btn btn-primary waves-effect">{{'UBZ.SITE_CONTENT.100001' | translate }}</button>
        </div>
      </form>
    </div>
  </div>
</div>
