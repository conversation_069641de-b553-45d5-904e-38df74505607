import { Injectable } from '@angular/core';
import { Http, Response, ResponseContentType } from '@angular/http';

@Injectable()
export class ReportService {
  constructor(private http: Http) {}

  public getReport(
    reportCode: string,
    page: number,
    pageSize: number,
    orderBy: string,
    asc: boolean,
    excel: boolean,
    filterMap?: any
  ) {
    reportCode = encodeURIComponent(reportCode);
    reportCode = 'TEST_APPRAISALS'; // mock
    const url = `/UBZ-ESA-RS/service/report/v1/report/${reportCode}`;
    // const input = {
    //   page: page,
    //   pageSize: pageSize,
    //   orderBy: orderBy,
    //   asc: asc,
    // excel: excel,
    //   filter: {
    //     filterMap
    //   }
    // };
    // MOCK
    const input = {
      page: page,
      pageSize: pageSize,
      orderBy: orderBy,
      asc: asc,
      excel: excel,
      filter: {}
    };
    if (excel === true) {
      return this.http
        .post(url, input, { responseType: ResponseContentType.Blob })
        .map((resp: Response) => resp.blob());
    } else {
      return this.http.post(url, input).map((resp: Response) => resp.json());
    }
  }
}
