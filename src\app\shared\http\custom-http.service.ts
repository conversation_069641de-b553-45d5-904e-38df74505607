import { Injectable, Inject, Injector } from '@angular/core';
import {
 Http,
  XHRBackend,
  RequestOptions,
  RequestOptionsArgs
} from '@angular/http';
import { Observable } from 'rxjs/Observable';
import { ErrorObservable } from 'rxjs/observable/ErrorObservable';
import 'rxjs/add/operator/catch';
import 'rxjs/add/operator/finally';
import 'rxjs/add/observable/throw';
import { MessageService } from '../messages/services/message.service';
import { APP_CONSTANTS, IAppConstants } from '../../app.constants';
import { BehaviorSubject } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';

@Injectable()
export class CustomHttpService extends Http {
  completedReqests = 0;
  totalRequests = 0;
  _statusChanged$: BehaviorSubject<boolean> = new BehaviorSubject(false);

  constructor(
    backend: XHRBackend,
    defaultOptions: RequestOptions,
    private msgService: MessageService,
    private injector: Injector,
    @Inject(APP_CONSTANTS) private constants: IAppConstants
  ) {
    super(backend, defaultOptions);
  }

  get(url: string, options?: RequestOptionsArgs): Observable<any> {
    this.showSpinner();
    return super
      .get(url, options)
      .timeout(this.constants.CONNECTION_TIMEOUT)
      .finally(() => { this.hideSpinner(); })
      .catch(resp => {
        if (resp.name && resp.name === 'TimeoutError') {
          this.msgService.showError(
            "LA CONNESSIONE E' STATA INTERROTTA POICHE' FERMA DA TROPPO TEMPO",
            'TIMEOUT CONNESSIONE'
          );
          return Observable.throw(resp);
        }
        if (this.catchProfileError(resp)) {
          return Observable.throw(resp);
        }
        return this.catchHttpError(resp.json());
      });
  }

  post(url: string, body: any, options?: RequestOptionsArgs): Observable<any> {
    this.showSpinner();
    return super
      .post(url, body, options)
      .timeout(this.constants.CONNECTION_TIMEOUT)
      .finally(() => { this.hideSpinner(); })
      .catch(resp => {
        if (resp.name && resp.name === 'TimeoutError') {
          this.msgService.showError(
            "LA CONNESSIONE E' STATA INTERROTTA POICHE' FERMA DA TROPPO TEMPO",
            'TIMEOUT CONNESSIONE'
          );
          return Observable.throw(resp);
        }
        if (this.catchProfileError(resp)) {
          return Observable.throw(resp);
        }
        return this.catchHttpError(resp.json());
      });
  }

  put(url: string, body: any, options?: RequestOptionsArgs): Observable<any> {
    this.showSpinner();
    return super
      .put(url, body, options)
      .timeout(this.constants.CONNECTION_TIMEOUT)
      .finally(() => { this.hideSpinner(); })
      .catch(resp => {
        if (resp.name && resp.name === 'TimeoutError') {
          this.msgService.showError(
            "LA CONNESSIONE E' STATA INTERROTTA POICHE' FERMA DA TROPPO TEMPO",
            'TIMEOUT CONNESSIONE'
          );
          return Observable.throw(resp);
        }
        if (this.catchProfileError(resp)) {
          return Observable.throw(resp);
        }
        return this.catchHttpError(resp.json());
      });
  }

  delete(url: string, options?: RequestOptionsArgs): Observable<any> {
    this.showSpinner();

    return super
      .delete(url, options)
      .timeout(this.constants.CONNECTION_TIMEOUT)
      .finally(() => { this.hideSpinner(); })
      .catch(resp => {
        if (resp.name && resp.name === 'TimeoutError') {
          this.msgService.showError(
            "LA CONNESSIONE E' STATA INTERROTTA POICHE' FERMA DA TROPPO TEMPO",
            'TIMEOUT CONNESSIONE'
          );
          return Observable.throw(resp);
        }
        if (this.catchProfileError(resp)) {
          return Observable.throw(resp);
        }
        return this.catchHttpError(resp.json());
      });
  }

  patch(url: string, body: any, options?: RequestOptionsArgs): Observable<any> {
    this.showSpinner();
    return super
      .patch(url, body, options)
      .timeout(this.constants.CONNECTION_TIMEOUT)
      .finally(() => { this.hideSpinner(); })
      .catch(resp => {
        if (resp.name && resp.name === 'TimeoutError') {
          this.msgService.showError(
            "LA CONNESSIONE E' STATA INTERROTTA POICHE' FERMA DA TROPPO TEMPO",
            'TIMEOUT CONNESSIONE'
          );
          return Observable.throw(resp);
        }
        if (this.catchProfileError(resp)) {
          return Observable.throw(resp);
        }
        return this.catchHttpError(resp.json());
      });
  }

  head(url: string, options?: RequestOptionsArgs): Observable<any> {
    this.showSpinner();
    return super
      .head(url, options)
      .timeout(this.constants.CONNECTION_TIMEOUT)
      .finally(() => { this.hideSpinner(); })
      .catch(resp => {
        if (resp.name && resp.name === 'TimeoutError') {
          this.msgService.showError(
            "LA CONNESSIONE E' STATA INTERROTTA POICHE' FERMA DA TROPPO TEMPO",
            'TIMEOUT CONNESSIONE'
          );
          return Observable.throw(resp);
        }
        if (this.catchProfileError(resp)) {
          return Observable.throw(resp);
        }
        return this.catchHttpError(resp.json());
      });
  }

  options(url: string, options?: RequestOptionsArgs): Observable<any> {
    this.showSpinner();

    return super
      .options(url, options)
      .timeout(this.constants.CONNECTION_TIMEOUT)
      .finally(() => { this.hideSpinner(); })
      .catch(resp => {
        if (resp.name && resp.name === 'TimeoutError') {
          this.msgService.showError(
            "LA CONNESSIONE E' STATA INTERROTTA POICHE' FERMA DA TROPPO TEMPO",
            'TIMEOUT CONNESSIONE'
          );
          return Observable.throw(resp);
        }
        if (this.catchProfileError(resp)) {
          return Observable.throw(resp);
        }
        return this.catchHttpError(resp.json());
      });
  }

  private catchHttpError(error: any): ErrorObservable {
    this.showSpinner();
    console.log('catcho errore')
    this.getErrorMessage(error)
      .finally(() => { this.hideSpinner(); })
      .subscribe(
        data =>
          this.showMsgToast(
            error.errorCode,
            error.conversationId,
            data ? data[0] : null
          ),
        err => {
          if (error.businessError === 'W') {
            this.showMsgToast(error.errorCode, error.conversationId, {
              message: err.statusText,
              type: 'W'
            });
          } else {
            this.showMsgToast(error.errorCode, error.conversationId, null);
          }
        }
      );
    return Observable.throw(error);
  }

  private getErrorMessage(data: any): Observable<any> {
    const channel = 'BRN';
    const request: any = {};
    console.log('data');
    console.log(data);

    if (data.businessError) {
      request.errorType = data.businessError;
    }
    if (data.applicationCode) {
      request.applicationCode = data.applicationCode;
    }
    if (data.errorCode) {
      request.errorCode = data.errorCode;
    }
    if (channel) {
      request.channel = channel;
    }

    if (data.externalSystem) {
      request.externalSystem = data.externalSystem;
    }
    if (data.originalErrorCode) {
      request.originalMsgCode = data.originalErrorCode;
    }

    const url = '/UBZ-ESA-RS/service/umfService/message';
    return super
      .post(url, [request], { withCredentials: true })
      .map(resp => resp.json());
  }

  getStatusChangeSubj(): Observable<boolean> {
    return this._statusChanged$.asObservable();
  }

  setStatusChangeSubj(value: boolean): void {
    this._statusChanged$.next(value);
  }

  showSpinner() {
    this.setStatusChangeSubj(true);
    this.totalRequests++;
  }

  hideSpinner() {
    this.completedReqests++;
    if (this.completedReqests >= this.totalRequests) {
      this.setStatusChangeSubj(false);
      this.totalRequests = 0;
      this.completedReqests = 0;
    }
  }

  private showMsgToast(
    errorCode: string,
    conversationId: string,
    errorMsg: any
  ) {
    if (!errorMsg) {
      errorMsg = {
        message: 'Descrizione errore non trovata',
        type: 'E'
      };
    }

    if (!errorCode) {
      errorCode = '';
    }

    let htmlMessage = conversationId
      ? `<p>ConversationId: ${conversationId}</p>`
      : '';
    htmlMessage = `${htmlMessage}${errorMsg.message
      ? errorMsg.message
      : 'Descrizione errore non trovata'}`;
    if (errorMsg.showDetails && errorMsg.errorDetail) {
      htmlMessage = `${htmlMessage}<p>${errorMsg.errorDetail}</p>`;
    }
    htmlMessage = `${htmlMessage}<p>`;
    if (errorMsg.externalLink) {
      htmlMessage = `${htmlMessage}<span>Per supporto contatta</span>`;
    }
    if (errorMsg.externalLink) {
      htmlMessage = `${htmlMessage}
        <a href="${errorMsg.externalLink}" onclick="window.open(this.href,'Unicontact','width=600px,height=700px');event.stopPropagation();return false">
          Unicontact
        </a>`;
    }
    htmlMessage = `${htmlMessage}</p>`;

    if (errorMsg.type === 'I') {
      this.msgService.showInfo(htmlMessage, errorCode);
    } else if (errorMsg.type === 'W') {
      this.msgService.showWarning(htmlMessage, errorCode);
    } else {
      this.msgService.showError(htmlMessage, errorCode);
    }
  }

  catchProfileError(resp: any): boolean {
    if (resp._body && resp._body.match('E1039') !== null) {
      const translateService = this.injector.get(TranslateService);
      this.msgService.showError(
        translateService.instant("PRZ.NOAUTHORIZE.PROFILE.MSG"),
        translateService.instant("PRZ.NOAUTHORIZE.PROFILE.TITLE")
      );
      return true;
    }
    return false;
  }
}
