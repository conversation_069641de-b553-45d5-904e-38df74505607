import { Component, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';

import { GuaranteeTransferService } from '../guarantee-transfer/services/guarantee-transfer.service';
import { GuaranteeInfoService } from '../appraisal-request/services/guarantee-info/guarantee-info.service';
import { GuaranteeFractionationService } from '../guarantee-fractionation/services/guarantee-fractionation.service';
import { of } from 'rxjs/observable/of';

@Component({
  selector: 'app-guarantee-asset-list',
  templateUrl: './guarantee-asset-list.component.html',
  styleUrls: ['./guarantee-asset-list.component.css'],
  providers: [GuaranteeTransferService, GuaranteeInfoService]
})
export class GuaranteeAssetListComponent implements OnInit {
  @ViewChild('modal') modal;

  ndg: string;
  guaranteeFrom: string;
  assetType: string;
  guaranteeTo: string;

  assets: any[] = [];
  guaranteeInfo: any = {};
  selectedAssetsId: boolean[] = [];
  selectedAssets: Object[] = []; // Contiene gli asset selezionati
  poolData: Object[] = []; // Contiene i pool data degli asset selezionati
  allSelected = false;
  modalAsset: any;
  modalIsOpen: boolean;
  isFrazionamento: boolean = false;
  guaranteeCollatTecFormFrom: string;
  guaranteeDescriptionFrom: string;
  guaranteeDescriptionTo: string;

  previousModalIsOpen: boolean;
  draftButtonCallback = this.saveDraft.bind(this);
  isDisabled = true;

  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private guaranteeTransferService: GuaranteeTransferService,
    private guaranteeInfoService: GuaranteeInfoService,
    public guaranteeFractionationService: GuaranteeFractionationService
  ) {}

  ngOnInit() {
    // Se siamo in richiesta di frazionamento
    if (
      this.activatedRoute.data['value'] &&
      this.activatedRoute.data['value'].name === 'guaranteeFractionationWizard'
    ) {
      this.setFractionationVariables();
    }
  }

  /**
   * @name setFractionationVariables
   * @description Imposta le variabili per il processo di frazionemento
   */
  setFractionationVariables() {
    setTimeout(() => (this.guaranteeFractionationService.wizardStep = 2), 10);
    this.isFrazionamento = true;
    this.activatedRoute.parent.params.subscribe(params => {
      this.ndg = params['ndg'];
      this.assetType = params['familyAsset'];
      this.guaranteeFractionationService.ndg = params['ndg'];
      this.guaranteeFractionationService.familyAsset = params['familyAsset'];
      this.guaranteeFractionationService.setCurrentStep('UBZ-FRG-SLA');
      this.guaranteeFractionationService.getFraWizardData().subscribe(() => {
        this.isDisabled = this.guaranteeFractionationService.isCurrentStepLocked();
        this.guaranteeInfoService
          .getGuaranteeListFrg(params['ndg'], params['familyAsset'])
          .subscribe(guaranteeList => {
            if (guaranteeList) {
              this.guaranteeTransferService
                .getAssociatedGuaranteesFrg(
                  this.ndg,
                  this.assetType,
                  guaranteeList
                )
                .subscribe(result => {
                  if (result) {
                    this.assets = result;
                    this.checkSelectedAsset();
                  }
                });
            }
          });
      });
    });
  }

  /**
   * @name checkSelectedAsset
   * @description Se gli asset sono già stati selezionati precedentemente scorre l'array
   * di questi per valorizzarli a video e al termine invoca il servizio che ne setta l'index
   */
  checkSelectedAsset() {
    this.selectedAssets = [];
    this.assets.forEach(asset => {
      if (typeof asset['isSelected'] === 'undefined') asset['isSelected'] = 'N';
    });
  }

  changeSelection(asset: any) {
    asset.isSelected = asset.isSelected === 'Y' ? 'N' : 'Y';
  }

  /**
   * @name selectAll
   * @description Metodo legato al toggle del check seleziona tutti, seleziona tutti gli asset presenti
   */
  selectAll() {
    this.allSelected = !this.allSelected;
    this.assets.forEach((element, index) => {
      element['isSelected'] = this.allSelected ? 'Y' : 'N';
    });
  }

  /**
   * @name saveIsEnable
   * @description Se almeno un asset è stato selezionato abilita il pulsante prosegui
   */
  saveIsEnable(): boolean {
    if (this.isDisabled) {
      return false;
    }
    for (const el of this.assets) {
      if (el['isSelected'] === 'Y') {
        return true;
      }
    }
    return false;
  }

  public openAssetModal(asset: any) {
    this.modalIsOpen = true;
    this.modalAsset = asset;
  }

  public closeModal() {
    this.modal.hide();
    this.modalIsOpen = false;
    this.modalAsset = null;
  }

  saveDraft() {
    return this.saveData().switchMap((response: any) => {
      return of(true);
    });
  }

  draftSaved() {
    this.router.navigateByUrl('index');
  }

  save() {
    this.saveData().subscribe(() => {
      this.guaranteeFractionationService.wizardSetNextStep().subscribe(() => {
        this.guaranteeFractionationService.goToNextStep();
      });
    });
  }

  saveData() {
    const data = {
      ndg: this.ndg,
      type: this.assetType,
      assets: []
    };
    this.assets.forEach(asset => {
      if (asset['isSelected'] === 'Y') {
        let newAsset = {
          assetId: asset.assetId,
          newProgCollateral: asset.progCollateral ? asset.progCollateral : ''
        };
        data.assets.push(newAsset);
      }
    });
    return this.guaranteeFractionationService
      .frazSaveAssetInfo(data)
      .switchMap(response => {
        return of(true);
      });
  }

  modify() {
    this.guaranteeFractionationService.wizardInvalidStep().subscribe(() => {      
      this.guaranteeFractionationService.getFraWizardData().subscribe(() => {   
        setTimeout(() => this.isDisabled = this.guaranteeFractionationService.isCurrentStepLocked(), 10);
      });
    });
  }

  // Esegue redirect verso stato precedente
  // Stato individuato a seconda che si sia nel wizard di richiesta o di frazionamento
  previous() {      
    this.guaranteeFractionationService.wizardInvalidPreviousStep().subscribe(() => {      
      this.guaranteeFractionationService.goToPrevStep();
    });    
  }

  openPreviousModal() {
    this.previousModalIsOpen = true;
  }

  closePreviousModal() {
    this.previousModalIsOpen = false;
  }

  confirmUndo() {
    this.previousModalIsOpen = false;
    this.previous();
  }
}
