<aside class="row controls">
  <div class="col-sm-12 text-right">
    <ng-container *ngIf="isChangeAppointmentAllowed">
      <app-task-access-rights [positionId]="positionId" [taskId]="taskId" [taskCod]="taskCod"
        [isCte]="isMacroProcessCte" (taskLocked)="taskLocked()" (taskUnlocked)="taskUnlocked()"
        (taskLockedByOtherUser)="tasklockedByOtherUser($event)">
      </app-task-access-rights>
    </ng-container>
  </div>
</aside>

<section class="section-alignment section-header">
  <div class="row">
    <div class="col-sm-12">
      <h4 class="section-heading">{{'UBZ.SITE_CONTENT.1000111010' | translate }}</h4>
      <div *ngIf="!(appraisalDetail?.customerContactDate || appraisalDetail?.expectedSurveyDate)"
        class="Search__NoResults">
        <div class="Search__NoResults__Icon">
          <i class="icon-incarico"></i>
        </div>
        <div class="Search__NoResults__Text">
          <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011011' | translate }}</p>
        </div>
      </div>
    </div>
  </div>
</section>

<section *ngIf="appraisalDetail?.customerContactDate || appraisalDetail?.expectedSurveyDate"
  class="section-alignment">
  <div class="row">
    <div class="col-sm-3 form-group mt-3">
      <label class="field-header">{{ 'UBZ.SITE_CONTENT.1000111011' | translate }}</label>
      <span>{{ appraisalDetail.customerContactDate | date:'dd/MM/yyyy' }},
        {{ 'UBZ.SITE_CONTENT.11000' | translate }} {{ appraisalDetail.customerContactDate | customTime }}</span>
      <div *ngIf="appraisalDetail?.customerContactDateInsert" class="additional-info">
        <span>{{'UBZ.SITE_CONTENT.1000111001' | translate }}
          {{ appraisalDetail.customerContactDateInsert | date:'dd/MM/yyyy' }}</span>
        <span>{{'UBZ.SITE_CONTENT.1000111000' | translate }}
          {{ appraisalDetail.customerContactDateInsert | customTime }}</span>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-sm-3 form-group">
      <label class="field-header">{{ 'UBZ.SITE_CONTENT.1000111100' | translate }}</label>
      <span>{{ appraisalDetail.expectedSurveyDate| date:'dd/MM/yyyy' }},
        {{ 'UBZ.SITE_CONTENT.11000' | translate }} {{ appraisalDetail.expectedSurveyDate | customTime }}</span>
      <div *ngIf="appraisalDetail?.expectedSurveyDateInsert" class="additional-info">
        <span>{{'UBZ.SITE_CONTENT.1000111001' | translate }}
          {{ appraisalDetail.expectedSurveyDateInsert | date:'dd/MM/yyyy' }}</span>
        <span>{{'UBZ.SITE_CONTENT.1000111000' | translate }}
          {{ appraisalDetail.expectedSurveyDateInsert | customTime }}</span>
      </div>
    </div>
  </div>
</section>