import { Component, OnInit, Output, EventEmitter, Input } from '@angular/core';
import { AmeoriService } from '../../service/ameori.service';

@Component({
  selector: 'app-ameori-research',
  templateUrl: './ameori-research.component.html',
  styleUrls: ['./ameori-research.component.css']
})
export class AmeoriResearchComponent implements OnInit {
  reportType: string = '';
  reportNumber: number = null;
  page: number = 1;
  @Input()
  pageSize: number = 10;
  isNotPossibleToSearch: boolean = true;
  @Output() getSearchResult = new EventEmitter<any>();
  constructor(private ameoriService: AmeoriService) { }

  ngOnInit() { }

  evaluateIfIsPossibleToSearch() {
    this.isNotPossibleToSearch =
      this.reportType === '' ||
      this.reportNumber === 0 ||
      this.reportNumber.toString() === '';
  }

  search() {
    let ameoriList: any;
    this.reportType = this.reportType.toUpperCase();
    this.ameoriService
      .searchList(
        this.reportType,
        this.reportNumber,
        this.page,
        this.pageSize
      )
      .subscribe(res => {
        let result = {
          ameoriList: res,
          reportNumber: this.reportNumber,
          reportType: this.reportType
        };
        this.getSearchResult.emit(result);
      });
  }
}
