import { Component, OnInit, Input } from '@angular/core';

@Component({
  selector: 'app-customer-general-data',
  templateUrl: './customer-general-data.component.html',
  styleUrls: ['./customer-general-data.component.css']
})
export class CustomerGeneralDataComponent implements OnInit {
  @Input() ndg: string;
  @Input() heading: string;
  @Input() homeAddress: string;
  @Input() homeCity: string;
  @Input() homeZipCode: string;
  @Input() phone: string;
  @Input() region: string;
  @Input() translatedNdgType: string;

  constructor() {}

  ngOnInit() {}
}
