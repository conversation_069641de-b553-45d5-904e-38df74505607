import { Directive, Input } from '@angular/core';
import { NG_VALIDATORS, Validator, AbstractControl } from '@angular/forms';

@Directive({
  selector: '[appValidator]',
  providers: [{provide: NG_VALIDATORS, useExisting: ValidatorDirective, multi: true}]
})
export class ValidatorDirective implements Validator {

  @Input('appValidator') appValidator: string;
  @Input('appValidatorType') appValidatorType: string;
  @Input('appValidatorValues') appValidatorValues;

  constructor() { }

  validate ( control: AbstractControl ) {
    if ( this.appValidatorType === 'Number' && this.appValidator && !this.appValidator.includes('Number') ) {
      this.formatNumberEvaluation();
    }
    return this.evalExpression() ? null : control;
  }

  evalExpression(): boolean {
    try {
      if ( !this.appValidator || this.appValidator === '' ) {
        return true;
      } else if (this.appValidator.indexOf('$') === -1) {
        return eval(this.appValidator);
      } else {
        this.appValidator = this.appValidator.replace('$', 'this.appValidatorValues.');
        return this.evalExpression();
      }
    } catch (e) {
      console.error( 'EXCEPTION ON APFORM COMPONENT AT EVAL EXPRESSION:' + this.appValidator );
    }
  }

  private formatNumberEvaluation( ) {
    const firstDollarIndex = this.appValidator.indexOf( '$');
    const secondDollarIndex = this.appValidator.lastIndexOf( '$');
    const firstSpaceIndex = this.appValidator.indexOf(' ');
    const firstWord = this.appValidator.substring(firstDollarIndex , firstSpaceIndex);
    if ( firstDollarIndex !== secondDollarIndex) {
      const secondWord = this.appValidator.substring( secondDollarIndex );
      this.appValidator = this.appValidator.replace( secondWord , ('Number(' + secondWord + ')'));
    }
    this.appValidator = this.appValidator.replace( firstWord , ('Number(' + firstWord + ')') );
  }

}
