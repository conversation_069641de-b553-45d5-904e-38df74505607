import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'domainMapToDomainArray'
})
export class DomainMapToDomainArrayPipe implements PipeTransform {
  transform(value: any, args?: any): any {
    const keys = [];
    for (const key in value) {
      if (value.hasOwnProperty(key)) {
        keys.push({
          domCode: value[key].domCode,
          translationCod: value[key].translationCod
        });
      }
    }
    return keys;
  }
}
