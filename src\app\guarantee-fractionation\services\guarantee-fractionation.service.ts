import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';
import { Guarantee } from '../../appraisal-request/model/guarantee';
import { Router } from '@angular/router';
import { MessageService } from '../../shared/messages/services/message.service';
import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { of } from 'rxjs/observable/of';
import { UserDataService } from '../../shared/user-data/user-data.service';

@Injectable()
export class GuaranteeFractionationService {
  wizardStep: number = 1;
  ndg: string = '';
  familyAsset: string = '';
  heading: string = '';
  filteredGuarantees: Guarantee[] = new Array();
  assetsList: Object[] = new Array();
  poolData: Object[] = new Array();
  guaranteeInfo: any = {};
  summaryObject: Object[] = new Array();
  // Oggetto passato a positionHeader per mostrare le selezioni durante il wizard di frazionamento
  staticHeaderArray: Object[];
  lockingUser: string = '';
  wizard: any[];
  currentStep = '';
  activeStep = '';
  wizardId: number = null;
  frazioId: number = null;
  frazLoaded = false;
  wizardStepActive: number = 0;
  guaranteesStored: any[] = [];
  assetsStored: any[] = [];

  constructor(
    private http: Http,
    private router: Router,
    private userDataService: UserDataService,
    private messageService: MessageService,
    private translateService: TranslateService
  ) { }
  /**
   * @name reset
   * @description Resetta tutte le variabili del service
   */
  reset() {
    this.wizardStep = 1;
    this.ndg = '';
    this.familyAsset = '';
    this.heading = '';
    this.filteredGuarantees = new Array();
    this.assetsList = new Array();
    this.poolData = new Array();
    this.guaranteeInfo = {};
    this.summaryObject = new Array();
  }
  /**
   * @name initializeStaticHeaderArray
   * @description Inizializza staticHeaderArray
   */
  initializeStaticHeaderArray() {
    this.staticHeaderArray = [
      {
        label: 'NDG',
        value: this.ndg
      },
      {
        label: 'Heading',
        value: this.heading
      },
      {
        label: 'type',
        value: 'FRA'
      }
    ];
  }
  /**
   * @description Restituisce ciò che viene passato in input + lista di progressive garanzia da cui si farà la combo box
   * @param ndg ndg identificativo del cliente
   * @param assetsList lita di asset selezionati nello step di AssetChoiceComponent
   **/
  getNewProg(ndg: string, familyType: string) {
    return this.http
      .post(
        `/UBZ-ESA-RS/service/collateralFrg/v1/getNewProg/${ndg}/${familyType}`,
        {}
      )
      .map((resp: Response) => resp.json());
  }
  /**
   * @description Ottiene il sommario della procedura di frazionamento
   * @param ndg ndg identificativo del cliente
   * @param familyType tipo di asset
   **/
  getFrazSummary(ndg: string, familyType: string) {
    // const summary = {
    //   "collateralSummary" : [
    //  {
    //    "jointCodOld" : "jointCodOld",
    //    "jointCodNew" : "jointCodNew",
    //    "objectCodOld;" : "objectCodOld;",
    //    "objectCodNew" : "objectCodNew",
    //    "appraisalIdOld" : "appraisalIdOld",
    //    "appraisalIdNew" : "appraisalIdNew",
    //    // garanzie padre
    //    "collatTecForm" : "collatTecForm",
    //    "collatDesc" : "collatDesc",
    //    "collatAmount" : "collatAmount",
    //    "poolType" : "poolType",
    //    "poolPerc" : 10,
    //    "resItemId" : 34
    //   },
    //   {
    //     "jointCodOld" : "jointCodOld",
    //     "jointCodNew" : "jointCodNew",
    //     "objectCodOld;" : "objectCodOld;",
    //     "objectCodNew" : "objectCodNew",
    //     "appraisalIdOld" : "appraisalIdOld",
    //     "appraisalIdNew" : "appraisalIdNew",
    //     // garanzie padre
    //     "collatTecForm" : "collatTecForm",
    //     "collatDesc" : "collatDesc",
    //     "collatAmount" : "collatAmount",
    //     "poolType" : "poolType",
    //     "poolPerc" : 20,
    //     "resItemId" : 44
    //    }
    //   ]
    // };
    // return of(summary);
    return this.http
      .post(
        `/UBZ-ESA-RS/service/collateralFrg/v1/summary/${ndg}/${familyType}`,
        {}
      )
      .map((resp: Response) => resp.json());
  }
  /**
   * @description Salva l'associazione asset-garanzia
   * @param ndg ndg identificativo del cliente
   * @param assetsList lita di asset selezionati nello step di AssetChoiceComponent
   **/
  saveAssetsAssociationOLD(ndg: string, familyType: string) {
    return this.http
      .post(
        `/UBZ-ESA-RS/service/collateralFrg/v1/summary/${ndg}/${familyType}`,
        this.assetsList
      )
      .map((resp: Response) => resp.json());
  }
  /**
   * @name cancelFractionation
   * @description Metodo invocato su annulla richiesta, forza uscita wizard di frazionamento eseguendo redirect sulla home
   */
  cancelFractionation() {
    return this.router.navigate(['/']);
  }
  /**
   * @name getNdgDetails
   * @description Recupera le informazioni associate al ndg
   * @param {string} ndg ndg identificativo del quale si vogliono recuperare le info
   */
  getNdgDetails(ndg: string) {
    const url = `/UBZ-ESA-RS/service/anagrafe/v1/customers/getCustomerFromId/${ndg}`;
    return this.http.get(url).map((resp: Response) => resp.json());
  }
  // positionUnlockedFrg() {
  //   this.messageService.showInfo(
  //     this.translateService.instant(
  //       'UB1.TASKACCESSRIGHTS.DIRECTIVE.ATTIVITARILASCIATA'
  //     ),
  //     this.translateService.instant('UB1.TITLE.INFO')
  //   );
  //   this.lockingUser = '';
  // }
  lockFrgNdg(ndg: string, familyType: string) {
    return this.http
      .post(
        `/UBZ-ESA-RS/service/collateralFrg/v1/assign/${ndg}/${familyType}`,
        {}
      )
      .map((resp: Response) => resp.text());
  }

  positionLockedFrg(){
    return this.lockFrgNdg(this.ndg, this.familyAsset).subscribe(() => {
      this.messageService.showInfo(
        this.translateService.instant(
          'UB1.TASKACCESSRIGHTS.DIRECTIVE.ATTIVITA'
        ),
        this.translateService.instant('UB1.TITLE.INFO')
      );
      this.lockingUser = this.userDataService.getAll().username;
   
    });
  }

  setCurrentStep(step: string) {
    this.currentStep = step;
  }
  
  setActiveStep(step: string) {
    this.activeStep = step;
    this.setNumericStepActive();
  }

  isActiveStep() {
    return this.currentStep === this.activeStep && this.activeStep !== '';
  }

  getFraWizardDataRest(ndg: string, familyType: string) {
    const url = `/UBZ-ESA-RS/service/collateralFrg/v1/wizard/${ndg}/${familyType}`;
    return this.http.get(url).map((resp: Response) => resp.json());
  }

  getFraWizardData(): Observable<any> {
    return this.getFraWizardDataRest(this.ndg, this.familyAsset).switchMap(
      response => {
        this.setFraWizard(response);
        this.getActiveWizardStep();
        return of(response);
      }
    );
  }

  setFraWizard(fraWizardWrapper: any) {
    this.wizard = fraWizardWrapper.wizard;
    this.ndg = fraWizardWrapper.ndg;
    this.familyAsset = fraWizardWrapper.type;
    this.wizardId = fraWizardWrapper.wizardId;
    this.frazioId = fraWizardWrapper.frazioId;
    this.lockingUser = fraWizardWrapper.inChargeUser;
    this.frazLoaded = true;
  }

  getActiveWizardStep() {
    this.activeStep = '';
    // Primo step con 3 è quello in corso
    for (let element of this.wizard) {
      if (element.outcomeCod && element.outcomeCod.toString() === '3') {
        this.activeStep = element.step;
        this.setNumericStepActive();
        return this.activeStep;
      }
    }
    for (let element of this.wizard) {
      // Primo step con 1 è il primo non eseguito da eseguire
      if (element.outcomeCod && element.outcomeCod.toString() === '1') {
        this.activeStep = element.step;
        this.setNumericStepActive();
        return this.activeStep;
      }
    }
  }

  setNumericStepActive() {
    this.wizardStepActive = 0;
    if (this.activeStep) {
      switch (this.activeStep) {
        case 'UBZ-FRG-ING':
          // Info garanzie
          this.wizardStepActive = 1;
          break;
        case 'UBZ-FRG-SLA':
          // Scelta asset
          this.wizardStepActive = 2;
          break;
        case 'UBZ-FRG-GDA':
          // Garanzie di arrivo
          this.wizardStepActive = 3;
          break;
        case 'UBZ-FRG-SUM':
          // Riepilogo
          this.wizardStepActive = 4;
          break;
      }
    }
  }

  goToCurrentStep() {
    if (this.currentStep) {
      let targetUrl = '';
      switch (this.currentStep) {
        case 'UBZ-FRG-ING':
          // Info garanzie
          targetUrl = `guaranteeFractionation/${this.familyAsset}/${this.ndg}/guarantee-info`;
          break;
        case 'UBZ-FRG-SLA':
          // Scelta asset
          targetUrl = `guaranteeFractionation/${this.familyAsset}/${this.ndg}/asset-choice`;
          break;
        case 'UBZ-FRG-GDA':
          // Garanzie di arrivo
          targetUrl = `guaranteeFractionation/${this.familyAsset}/${this.ndg}/guarantee-on-arrival`;
          break;
        case 'UBZ-FRG-SUM':
          // Riepilogo
          targetUrl = `guaranteeFractionation/${this.familyAsset}/${this.ndg}/summary`;
          break;
      }
      if (targetUrl) {
        this.router.navigate([targetUrl]);
      }
    }
  }

  goToNextStep() {
    // Da selezione ndg -> Info garanzie
    let targetUrl = `guaranteeFractionation/${this.familyAsset}/${this.ndg}/guarantee-info`;
    if (this.currentStep) {
      switch (this.currentStep) {
        case 'UBZ-FRG-ING':
          // Info garanzie -> Scelta asset
          targetUrl = `guaranteeFractionation/${this.familyAsset}/${this.ndg}/asset-choice`;
          break;
        case 'UBZ-FRG-SLA':
          // Scelta asset -> Garanzie di arrivo
          targetUrl = `guaranteeFractionation/${this.familyAsset}/${this.ndg}/guarantee-on-arrival`;
          break;
        case 'UBZ-FRG-GDA':
          // Garanzie di arrivo -> Riepilogo
          targetUrl = `guaranteeFractionation/${this.familyAsset}/${this.ndg}/summary`;
          break;
      }
    }
    if (targetUrl) {
      this.router.navigate([targetUrl]);
    }
  }

  getPreviousStep() {
    let prevStep = '';
    if (this.currentStep) {
      switch (this.currentStep) {
        case 'UBZ-FRG-SLA':
          // Scelta asset
          prevStep = 'UBZ-FRG-ING';
          break;
        case 'UBZ-FRG-GDA':
          // Garanzie di arrivo
          prevStep = 'UBZ-FRG-SLA';
          break;
        case 'UBZ-FRG-SUM':
          // Riepilogo
          prevStep = 'UBZ-FRG-GDA';
          break;
      }
    }
    return prevStep;
  }

  goToPrevStep() {
    if (this.currentStep) {
      let targetUrl = '';
      switch (this.currentStep) {
        case 'UBZ-FRG-SLA':
          // Scelta asset
          targetUrl = `guaranteeFractionation/${this.familyAsset}/${this.ndg}/guarantee-info`;
          break;
        case 'UBZ-FRG-GDA':
          // Garanzie di arrivo
          targetUrl = `guaranteeFractionation/${this.familyAsset}/${this.ndg}/asset-choice`;
          break;
        case 'UBZ-FRG-SUM':
          // Riepilogo
          targetUrl = `guaranteeFractionation/${this.familyAsset}/${this.ndg}/guarantee-on-arrival`;
          break;
      }
      if (targetUrl) {
        this.router.navigate([targetUrl]);
      }
    }
  }

  isCurrentStepLocked() {
    let isStepLocked = true;
    if (this.wizard && this.currentStep) {
      for (let element of this.wizard) {
        if (element.outcomeCod && element.outcomeCod.toString() === '3') {
          if (this.currentStep === element.step) {
            isStepLocked = false;
          }
        }
      }
    }
    return isStepLocked;
  }

  wizardSetNextStepRest(data: any) {
    const url = `/UBZ-ESA-RS/service/collateralFrg/v1/wizard/${data.ndg}/${data.type}/next`;
    return this.http.post(url, data).map((resp: Response) => resp.text());
  }

  wizardSetNextStep(): Observable<any> {
    let data = {
      ndg: this.ndg,
      type: this.familyAsset,
      wizardId: this.wizardId,
      frazioId: this.frazioId,
      step: this.currentStep
    };
    return this.wizardSetNextStepRest(data).switchMap(response => {
      if (response === 'OK') {
        return of(response);
      }
      return of(response);
    });
  }

  wizardInvalidStepRest(data: any) {
    const url = `/UBZ-ESA-RS/service/collateralFrg/v1/wizard/${data.ndg}/${data.type}/invalid`;
    return this.http.post(url, data).map((resp: Response) => resp.text());
  }

  wizardInvalidStep(): Observable<any> {
    let data = {
      ndg: this.ndg,
      type: this.familyAsset,
      wizardId: this.wizardId,
      frazioId: this.frazioId,
      step: this.currentStep
    };
    return this.wizardInvalidStepRest(data).switchMap(response => {
      if (response === 'OK') {
        return of(response);
      }
      return of(response);
    });
  }
  
  wizardInvalidPreviousStep(): Observable<any> {
    let data = {
      ndg: this.ndg,
      type: this.familyAsset,
      wizardId: this.wizardId,
      frazioId: this.frazioId,
      step: this.getPreviousStep()
    };
    return this.wizardInvalidStepRest(data).switchMap(response => {
      if (response === 'OK') {
        return of(response);
      }
      return of(response);
    });
  }

  frazSaveCollateralInfoRest(data: any) {
    const url = `/UBZ-ESA-RS/service/collateralFrg/v1/collateralInfo`;
    return this.http.post(url, data).map((resp: Response) => resp.text());
  }

  frazSaveCollateralInfo(data: any): Observable<any> {
    return this.frazSaveCollateralInfoRest(data).switchMap(response => {
      return of(response);
    });
  }

  frazSaveAssetInfoRest(data: any) {
    const url = `/UBZ-ESA-RS/service/collateralFrg/v1/assetSelection`;
    return this.http.post(url, data).map((resp: Response) => resp.text());
  }
  
  frazSaveAssetInfo(data: any): Observable<any> {
    return this.frazSaveAssetInfoRest(data).switchMap(response => {
      return of(response);
    });
  }
}
