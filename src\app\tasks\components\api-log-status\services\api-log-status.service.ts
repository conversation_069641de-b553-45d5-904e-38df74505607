import { Injectable } from '@angular/core';
import { Http } from '@angular/http';
import { Observable } from 'rxjs/Observable';
import { ApiLogStatusFilterModel } from '../models/api-log-status.filter.model';

@Injectable()
export class ApiLogStatusService {
  private urlAntergateAppraisalEndpoint = `/UBZ-ESA-RS/service/anacredit/v1/position/`;

  constructor(private _http: Http) {}

  public filter(filter: ApiLogStatusFilterModel): Observable<any> {
    return this._http
      .post(
        `/UBZ-ESA-RS/service/appraisal/v1/appraisals/${
          filter.appraisalId
        }/statusHistory`,
        filter
      )
      .map(res => res.json());
    //FIXME: set service when available
    // return this.getAntergateAppraisalList(1,10);
  }

  /**
   * @function
   * @name getApiAppraisalList
   * @description Recupera il log dello stato perizia API
   * @param appraisalId
   * @param page
   * @param numberOfResults
   */
  public getApiAppraisalList(
    appraisalId: string,
    page: number,
    numberOfResults: number
  ): Observable<any> {
    const obj = {
      appraisalId: appraisalId,
      page: page,
      pageSize: numberOfResults
    };
    return this._http
      .post(
        `/UBZ-ESA-RS/service/appraisal/v1/appraisals/${appraisalId}/statusHistory`,
        obj
      )
      .map(res => res.json());
    //FIXME: set service when available
    // return Observable.of(this.getMockedList());
  }

  public getAnacreditRequestData(requestId: string): Observable<any> {
    const url = this.urlAntergateAppraisalEndpoint + requestId;
    return this._http.get(url).map(res => res.json());
    // return Observable.of(this.getMockedReqData(requestId));
  }

  public saveAnacreditRequestData(
    requestId: string,
    requestData: any
  ): Observable<boolean> {
    const url = this.urlAntergateAppraisalEndpoint + requestId;
    return this._http.post(url, requestData).map(res => res.json());
  }

  //FIXME: remove when services are available
  public getMockedReqData(requestId: string) {
    return {
      isDraft: true,
      collaterals: [
        {
          prog: 123,
          collatDesc: 'Ipoteca di grado superiore al primo',
          antergateValue: 100000,
          assets: [
            {
              resItemId: 123,
              familyAsset: 'A',
              categoryAsset: 'B',
              antergateSplittedValue: 1200
            },
            {
              resItemId: 2123,
              familyAsset: 'C',
              categoryAsset: 'D',
              antergateSplittedValue: 2300
            }
          ]
        },
        {
          prog: 234,
          collatDesc: 'Ipoteca di grado superiore al primo',
          antergateValue: 100001,
          assets: [
            {
              resItemId: 3123,
              familyAsset: 'A1',
              categoryAsset: 'B1',
              antergateSplittedValue: 1200
            },
            {
              resItemId: 42123,
              familyAsset: 'C2',
              categoryAsset: 'D2',
              antergateSplittedValue: 2300
            }
          ]
        }
      ]
    };
  }

  //FIXME: remove when services are available
  getMockedList(): any {
    return {
      details: [
        {
          appraisalId: '4003602',
          appStatusCod: '01',
          appPhaseCod: '01',
          appSubStatusCod: '001',
          expertName: 'tizio',
          note: 'nota1',
          insertDate: '2018-02-22'
        },
        {
          appraisalId: '4851',
          appStatusCod: '02',
          appPhaseCod: '02',
          appSubStatusCod: '002',
          expertName: 'caio',
          note: 'nota2',
          insertDate: '2015-06-22'
        },
        {
          appraisalId: '8874',
          appStatusCod: '03',
          appPhaseCod: '03',
          appSubStatusCod: '003',
          expertName: 'gianni',
          note: 'nota3',
          insertDate: '2018-12-02'
        },
        {
          appraisalId: '482',
          appStatusCod: '04',
          appPhaseCod: '04',
          appSubStatusCod: '004',
          expertName: 'pippo',
          note: 'nota4',
          insertDate: '2017-04-12'
        },
        {
          appraisalId: '181',
          appStatusCod: '05',
          appPhaseCod: '05',
          appSubStatusCod: '005',
          expertName: 'franca',
          note: 'nota5',
          insertDate: '2018-09-01'
        },
        {
          appraisalId: '231',
          appStatusCod: '06',
          appPhaseCod: '06',
          appSubStatusCod: '006',
          expertName: 'gina',
          note: 'nota6',
          insertDate: '2017-07-22'
        },
        {
          appraisalId: '852',
          appStatusCod: '07',
          appPhaseCod: '07',
          appSubStatusCod: '007',
          expertName: 'pino',
          note: 'nota7',
          insertDate: '2017-01-06'
        },
        {
          appraisalId: '171',
          appStatusCod: '08',
          appPhaseCod: '08',
          appSubStatusCod: '008',
          expertName: 'lara',
          note: 'nota8',
          insertDate: '2017-08-22'
        },
        {
          appraisalId: '996',
          appStatusCod: '09',
          appPhaseCod: '09',
          appSubStatusCod: '009',
          expertName: 'tito',
          note: 'nota9',
          insertDate: '2017-11-05'
        },
        {
          appraisalId: '28',
          appStatusCod: '10',
          appPhaseCod: '10',
          appSubStatusCod: '010',
          expertName: 'ajeje',
          note: 'nota10',
          insertDate: '2018-05-14'
        },
        {
          appraisalId: '1881',
          appStatusCod: '11',
          appPhaseCod: '11',
          appSubStatusCod: '011',
          expertName: 'lalla',
          note: 'nota11',
          insertDate: '2018-06-22'
        }
      ]
    };
  }
}
