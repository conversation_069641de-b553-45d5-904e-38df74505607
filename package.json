{"name": "ubz", "version": "0.0.0", "license": "MIT", "scripts": {"ng": "ng", "start": "ng serve", "start-local": "ng serve --proxy-config proxy.conf.local.json --base-href /", "start-local2": "ng serve --port 4201 --proxy-config proxy.conf.local.json --base-href /", "build": "ng build", "test": "karma start", "lint": "ng lint", "e2e": "ng e2e", "full-prod": "npm i && npm list && npm run half-prod", "half-prod": "npm run build-prod && npm run test && node maven.js", "full-prod-verbose": "npm i --verbose && npm run build-prod --verbose && node maven.js", "build-test": "ng build --output-path=../webapp/UBZ/", "build-prod": "node --max_old_space_size=2048 ./node_modules/@angular/cli/bin/ng build --output-path=../webapp/UBZ/ --prod --env=prod --aot", "build-prod-j": "node --max_old_space_size=2048 ./node_modules/@angular/cli/bin/ng build --prod --env=prod --aot", "sonar-scanner": "sonar-scanner"}, "private": true, "dependencies": {"@angular/animations": "^4.2.4", "@angular/common": "^4.2.4", "@angular/compiler": "^4.2.4", "@angular/core": "^4.2.4", "@angular/forms": "^4.2.4", "@angular/http": "^4.2.4", "@angular/platform-browser": "^4.2.4", "@angular/platform-browser-dynamic": "^4.2.4", "@angular/router": "^4.2.4", "@ngui/datetime-picker": "^0.16.2", "@ngx-translate/core": "^6.0.0", "@ngx-translate/http-loader": "^0.0.3", "@types/file-saver": "0.0.0", "@types/node": "^6.0.78", "angular-svg-round-progressbar": "1.1.0", "angular2-text-mask": "^8.0.5", "bootstrap": "^3.3.7", "classlist.js": "^1.1.20150312", "core-js": "^2.4.1", "file-saver": "^1.3.3", "font-awesome": "^4.7.0", "jquery": "^3.6.0", "maven-deploy": "^1.5.0", "ng2-currency-mask": "^4.3.4", "ng2-dragula": "^1.5.0", "ng2-pdf-viewer": "^3.0.8", "ng2-toastr": "^4.1.2", "ngx-bootstrap": "^1.7.1", "ngx-cookie": "^1.0.0", "ngx-slick": "^0.1.1", "node-sass": "^4.13.1", "recursive-copy": "^2.0.10", "rxjs": "^5.4.2", "slick-carousel": "^1.6.0", "text-mask-addons": "^3.7.2", "web-animations-js": "^2.3.2", "zone.js": "^0.8.12"}, "devDependencies": {"@angular/cli": "^1.1.3", "@angular/compiler-cli": "^4.2.4", "@types/jasmine": "2.5.38", "codelyzer": "~2.0.0", "jasmine-core": "~2.5.2", "jasmine-spec-reporter": "~3.2.0", "karma": "~1.4.1", "karma-chrome-launcher": "~2.0.0", "karma-cli": "~1.0.1", "karma-coverage-istanbul-reporter": "^0.2.0", "karma-jasmine": "~1.1.0", "karma-jasmine-html-reporter": "^0.2.2", "karma-phantomjs-launcher": "^1.0.4", "protractor": "^5.1.2", "sonarqube-scanner": "^2.6.0", "ts-node": "~2.0.0", "tslint": "~4.4.2", "typescript": "^2.4.0"}}