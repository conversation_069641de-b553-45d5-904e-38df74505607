<fieldset [disabled]="!_landingService.positionLocked">
<fieldset [disabled]="_landingService.isLockedTask[currentTask] || haveDisabledFields">
  <accordion class="panel-group" id="accordion">
    <app-accordion-application-form 
      [idCode]="positionId" 
      page="AZ_AGR_COMPLESSO" 
      [positionId]="positionId" 
      (change)="getSaveEnableState()"
      (calendarChange)="getSaveEnableState()"      
      [formDisabled] = "_landingService.isLockedTask[currentTask] || haveDisabledFields">
    </app-accordion-application-form>
    <app-gross-production [model]="model" (change)="getSaveEnableState()"></app-gross-production>
    <app-salary-calculation [model]="model" (change)="getSaveEnableState()"></app-salary-calculation>
    <app-appraisal-template-validation *ngIf="_positionService.isInternalSecondOpinion" [positionId]="positionId" templateName="AZ_AGR_COMPLESSO" (change)="getSaveEnableState()" [haveDisabledFields]="haveDisabledFields"></app-appraisal-template-validation>  
  </accordion>
</fieldset>

<app-navigation-footer showSaveDraft="true" showPrevious="true" [saveIsEnable]="saveIsEnable" [showCancelButton]="false"
  (saveButtonClick)="saveData()" (previousButtonClick)="goToPreviousTask()" (closeDraftButtonClick)="goToGenericTask()"
  [saveDraftCallback]="saveDraftCallback" [activeTaskCode]="currentTask" confirmButtonString="{{'UBZ.SITE_CONTENT.110001110' | translate }}"
></app-navigation-footer>
</fieldset>
