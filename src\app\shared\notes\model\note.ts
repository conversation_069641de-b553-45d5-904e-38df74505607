export class Note {
  positionId: string;
  noteType: string;
  noteTitle: string;
  noteDesc: string;
  creationDate: number;
  userId: string;
  noteScope: string;
  noteMode: string;
  positionType: string;
  reasonCode = '';

  constructor(positionId?: string, positionType?: string, noteMode?: string) {
    this.noteType = '';
    if (positionId) {
      this.positionId = positionId;
    }
    this.positionType = positionType;
    this.noteMode = noteMode ? noteMode : 'M';
  }
}
