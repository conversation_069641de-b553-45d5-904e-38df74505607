import { Injectable } from '@angular/core';
import { Http } from '@angular/http';
import { Observable } from 'rxjs/Observable';

import { Domain } from '../../shared/domain/domain';
import { DomainService } from '../../shared/domain/domain.service';
import { AppraisalResponse } from '../model/appraisal-response';

@Injectable()
export class WizardDetailService {
  private scopeTypes: Domain[] = new Array<Domain>();
  private appraisalTypes: Domain[] = new Array<Domain>();

  constructor(private http: Http, private domainService: DomainService) {}

  // Usato per prelevare le informazioni riguardanti la richiesta a partire dall'ID
  public getRequestData(
    positionId: string,
    wizardCode: string
  ): Observable<AppraisalResponse> {
    positionId = encodeURIComponent(positionId);
    const urlMap = {
      WRPE: '/UBZ-ESA-RS/service/reqPer/v1/reqAppraisal/' + positionId,
      WSIM: '/UBZ-ESA-RS/service/simulation/v1/simulationInfo/' + positionId
    };
    return this.http.get(urlMap[wizardCode]).map(res => res.json());
  }

  // Usato per prendere le informazioni dell'utente
  public getCustomerData(customerId: number): Observable<any> {
    const url = '/UBZ-ESA-RS/service/anagrafe/v1/customers/' + customerId;
    return this.http.get(url).map(res => res.json());
  }

  // Usato per prendere le informazioni degli oggetti di perizia
  public getAppraisalObjectsInfo(
    positionId: string,
    wizardCode: string
  ): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const urlMap = {
      WRPE:
        '/UBZ-ESA-RS/service/reqPer/v1/reqAppraisalObjectsInfo/' + positionId,
      WSIM:
        '/UBZ-ESA-RS/service/simulation/v1/simulationObjectsInfo/' + positionId
    };
    return this.http.get(urlMap[wizardCode]).map(res => res.json());
  }

  public getAssignmentType(positionId: string, positionType: string) {
      positionId = encodeURIComponent(positionId);
      const url = `/UBZ-ESA-RS/service/individual/v1/individuals/typeEmpAppraisal/${positionId}/${positionType}`;
      return this.http.get(url).map(res => res.json());
  }

  public getAppraisalHistory(positionId: string): Observable<any> {
    positionId = encodeURIComponent(positionId);
    const url = '/UBZ-ESA-RS/service/reqPer/v1/reqAppAppraisals/' + positionId;
    return this.http.get(url).map(res => res.json());
  }

  public getAppraisalTypes(): Observable<any> {
    return this.domainService.newGetDomain('UBZ_DOM_APPRAISAL_TYPE');
  }

  public getMacroprocesses(): Observable<any> {
    return this.domainService.newGetDomain('UBZ_DOM_MACRO_PROCESS');
  }

  public getScopeTypes(): Observable<any> {
    return this.domainService.newGetDomain('UBZ_DOM_SCOPE_TYPE');
  }

  public getFamilyTypes(): Observable<any> {
    return this.domainService.newGetDomain('UBZ_DOM_RESITEM_TYPE');
  }

  public getNDGTypes(): Observable<any> {
    return this.domainService.newGetDomain('UBZ_DOM_NDG_TYPE');
  }

  public getOpinionTypes(): Observable<any> {
    return this.domainService.newGetDomain('UBZ_DOM_OPINION_TYPE');
  }

  public getUpdateTypes(): Observable<any> {
    return this.domainService.newGetDomain('UBZ_DOM_AGG_APPRAISAL_TYPE');
  }

  public getCategoryTypes(expired = false): Observable<any> {
    return this.domainService.newGetDomain(
      'UBZ_DOM_CATEGORY_TYPE',
      '-',
      expired
    );
  }

  public getStatus(): Observable<any> {
    return this.domainService.getStatusName();
  }

  public getAppraisalOwners(): Observable<any> {
    return this.domainService.newGetDomain('UBZ_DOM_APPRAISAL_OWNER');
  }

  public getActivityHistory(positionId: string) {
    const url = '/assets/data/fake-activity-history.json'; // + positionId;
    return this.http.get(url).map(res => res.json());
  }
}
