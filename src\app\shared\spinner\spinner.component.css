.Spinner__Background {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, .6);
  z-index: 10000;
  display: flex;
  justify-content: center;
  align-items: center;
  visibility: hidden;
}

.Spinner__Background--visible {
  visibility: visible;
}

.Spinner {
  width: 64px;
  height: 64px;
  display: inline-block;
  box-sizing: border-box;
  position: relative;
}

.cp-round:before {
  border-radius: 50%;
  content: " ";
  width: 64px;
  height: 64px;
  display: inline-block;
  box-sizing: border-box;
  border-top: solid 6px #BFEBF3;
  border-right: solid 6px #BFEBF3;
  border-bottom: solid 6px #BFEBF3;
  border-left: solid 6px #BFEBF3;
  position: absolute;
  top: 0;
  left: 0;
}

.cp-round:after {
  border-radius: 50%;
  content: " ";
  width: 64px;
  height: 64px;
  display: inline-block;
  box-sizing: border-box;
  border-top: solid 6px #00AFD0;
  border-right: solid 6px transparent;
  border-bottom: solid 6px transparent;
  border-left: solid 6px transparent;
  position: absolute;
  top: 0;
  left: 0;
  animation: cp-round-animate 1s ease-in-out infinite;
}

@keyframes cp-round-animate {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
