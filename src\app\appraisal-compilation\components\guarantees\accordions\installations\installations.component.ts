import { Component, OnInit, Input } from '@angular/core';
import { DomainService } from '../../../../../shared/domain/domain.service';
import { AppraisalCompilationService } from '../../../../service/appraisal-compilation.service';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { LandingService } from '../../../../../simulation';
import { ApplicationFormComponent } from '../../../../../shared/application-form/components/application-form.component';

// Componente utiilzzato nel wizard di compilazione perizia (step Unità a Garanzia)
// Effettuati controlli per distinguere fra NDG CORPORATE/INDIVIDUAL e modificare opportunamente il componente
// per seguire il processo e le indicazioni rispettive
@Component({
  selector: 'app-installations',
  templateUrl: './installations.component.html',
  styleUrls: ['./installations.component.css']
})

export class InstallationsComponent implements OnInit {
  @Input() pageContent: any[];
  @Input() positionId: string;
  @Input() assetId: string;

  pageIsValid = true; // accordion facoltativo, sarà sempre valido
  selectedPageElement = null;
  systemDomain: any = {};

  // Variabili di gestione customModal
  modalType: string;
  customModalIsOpen = false;
  largeModalFlag: boolean;
  headerTitle: string;
  apfString: string;
  messagesArray: string[];
  buttonTitle: string[];
  disabledFlag: boolean;

  constructor(
    private domainService: DomainService,
    private appraisalCompilationService: AppraisalCompilationService,
    public _accordionAPFService: AccordionAPFService,
    private landingService: LandingService
  ) { }

  ngOnInit() {
    // Stringa settata a IND_ per modificare dinamicamente gli URL delle chiamate ai domini nel caso di NDG INDIVIDUAL
    let individualDomainString = '';
    if (this.landingService.posSegment === 'IND') {
      individualDomainString = 'IND_';
    }
    this.domainService.newGetDomain('UBZ_DOM_' + individualDomainString + 'SYSTEM_TYPE').subscribe(x => {
      this.systemDomain = x;
    });
  }

  // Imposta le variabili da passare alla modal e ne forza l'apertura
  // modalType indica il tipo di modal che si sta aprendo (add, delete, modify)
  // SelectedElement contiene l'elemento selezionato nel caso di modify e delete
  openCustomModal(modalType: string, selectedElement?: any) {
    if (selectedElement) {
      this.selectedPageElement = selectedElement;
    }
    this.modalType = modalType;
    switch (this.modalType) {
      case 'add':
        this.largeModalFlag = true;
        this.headerTitle = 'UBZ.SITE_CONTENT.1000010010';
        this.apfString = 'IMPIANTI';
        this.messagesArray = [];
        this.buttonTitle = ['UBZ.SITE_CONTENT.1000010010'];
        this.disabledFlag = true;
        break;
      case 'modify':
        this.largeModalFlag = true;
        this.headerTitle = 'UBZ.SITE_CONTENT.1000010101';
        this.apfString = 'IMPIANTI';
        this.messagesArray = [];
        this.buttonTitle = ['UBZ.SITE_CONTENT.1000010101'];
        this.disabledFlag = true;
        break;
      case 'delete':
        this.largeModalFlag = false;
        this.headerTitle = 'UBZ.SITE_CONTENT.1000010011';
        this.apfString = '';
        this.messagesArray = ['UBZ.SITE_CONTENT.1000010100'];
        this.buttonTitle = ['UBZ.SITE_CONTENT.11110', 'UBZ.SITE_CONTENT.100000'];
        this.disabledFlag = false;
        break;
    }
    this.customModalIsOpen = true;
  }

  // Intercetta l'evento submit della customModal ed invoca il metodo appropriato a seconda del modalType.
  // L'objectParams in input contiene l'apForm del componente e il modalType (add, modify, delete)
  handleSubmitCustomModal(objectParams: Object) {
    switch (objectParams['modalType']) {
      case 'add':
        this.submitAddModal(objectParams['apForm']);
        break;
      case 'modify':
        this.submitModifyModal(objectParams['apForm']);
        break;
      case 'delete':
        this.submitDeleteModal(objectParams['apForm']);
        break;
    }
  }

  // Esegue metodo di aggiunta per customModal con modalType === 'add'
  submitAddModal(apForm: ApplicationFormComponent) {
    this.appraisalCompilationService.saveNewStaticAccordion(
      'systemObject',
      this.assetId,
      apForm
    ).subscribe(res => {
      this.closeCustomModal();
    });
  }

  // Esegue metodo di aggiunta per customModal con modalType === 'modify'
  submitModifyModal(apForm: ApplicationFormComponent) {
    this.appraisalCompilationService.saveExistingStaticAccordion(
      'systemObject',
      this.selectedPageElement.systemId,
      apForm
    ).subscribe(res => {
      this.closeCustomModal();
    });
  }

  // Esegue metodo di aggiunta per customModal con modalType === 'delete'
  submitDeleteModal(apForm: ApplicationFormComponent) {
    this.appraisalCompilationService.deleteStaticAccordion(
      'systemObject',
      this.selectedPageElement.systemId,
    ).subscribe(res => {
      this.closeCustomModal();
    });
  }

  closeCustomModal() {
    this.refreshPagecontent();
    this.selectedPageElement = null;
    this.customModalIsOpen = false;
  }

  refreshPagecontent() {
    this.appraisalCompilationService
      .getSingleStaticComponentOfPropertyGuarantee(this.assetId, 'systemObject')
      .subscribe(x => {
        this.pageContent = x['systemObject'];
      });
  }
}
