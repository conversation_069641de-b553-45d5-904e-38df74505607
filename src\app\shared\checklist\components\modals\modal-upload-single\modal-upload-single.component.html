<div *ngIf="isOpen" class="modal fade" id="aggiungi-documento" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
  bsModal #stateBox="bs-modal" (onHidden)="closeModal(false)" [config]="{show: 'true'}">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <form #pageForm>
        <div class="modal-header">
          <h2>{{'UBZ.SITE_CONTENT.11010011' | translate }}</h2>
          <button type="button" class="close pull-right" aria-label="Close" (click)="closeModal(false)">
            <i class="icon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-sm-12">
              <h4>{{'UBZ.SITE_CONTENT.11000110' | translate }}</h4>
            </div>
            <div *ngIf="section" class="col-sm-3 form-group">
              <label>
                {{'UBZ.SITE_CONTENT.11010000' | translate }} {{ (section.entityType) ? (section.entityType.translationCod | translate) :
                ''}}
              </label>
              {{ (section.resItemType) ? (section.resItemType.translationCod | translate) : '' }} {{ (section.resItemCategory) ? ('-' +
              (section.resItemCategory.translationCod | translate)) : '' }}
            </div>
            <div class="col-sm-3 form-group">
              <label>{{'UBZ.SITE_CONTENT.11001000' | translate }}</label>
              {{ (document.lastUpload.userId) ? document.lastUpload.userId : '---'}}
            </div>
            <div class="col-sm-3 form-group">
              <label>{{'UBZ.SITE_CONTENT.1111110' | translate }}</label>
              {{ ( document.lastUpload.uploadDate ) ? ((document.lastUpload.uploadDate | date: 'dd-MM-y') + ' ' + (document.lastUpload.uploadDate
              | customTime)) : '---'}}
            </div>
            <div class="col-sm-3 form-group">
              <label>{{'UBZ.SITE_CONTENT.10000001' | translate }}</label>
              <span [class]="isValidClass[document.acquired]"></span>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12">
              <h4>{{'UBZ.SITE_CONTENT.11000001' | translate }}</h4>
            </div>
            <div class="col-sm-8 form-group">
              <label>{{'UBZ.SITE_CONTENT.1110100' | translate }}</label>
              <input type="text" class="form-control" [(ngModel)]="documentDesc" name="documentDesc" placeholder="{{'UBZ.SITE_CONTENT.110100100' | translate }}..."
                maxlentgh="50" />
            </div>
          </div>
          <div class="row" id="fase-inserimento" *ngIf="posticipaCaricamentoPressed">
            <div class="col-sm-8">
              <label>{{'UBZ.SITE_CONTENT.11010101' | translate }}</label>
              <div class="custom-select">
                <select class="form-control" [(ngModel)]="selectedPhase" name="selectedPhase">
                  <option value="" selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let el of (domains | statusMapToStatusArray)" value="{{el.domCode}}">{{el.translationCod | translate}}</option>
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12">
              <h4>{{'UBZ.SITE_CONTENT.11000001' | translate }}</h4>
              <div class="input-group">
                <input type="text" class="form-control" readonly [value]="fileName">
                <label class="input-group-btn">
                  <span class="btn btn-primary waves-effect">
                    {{'UBZ.SITE_CONTENT.11000011' | translate }}&hellip;
                    <input type="file" #fileToUpload id="sfoglia" style="display: none;" multiple (change)="setFile()" required [disabled]="posticipaCaricamentoPressed === true">
                  </span>
                </label>
              </div>
            </div>
            <div class="col-sm-12">
              <label>{{'UBZ.SITE_CONTENT.11001011' | translate }}</label>
              <textarea class="form-control" placeholder="{{'UBZ.SITE_CONTENT.10001001' | translate }}..." name="note" [(ngModel)]="note"
                maxLength="300"></textarea>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button id="upload-filedoc" type="submit" class="btn btn-primary waves-effect" data-dismiss="modal" [disabled]="!buttonIsEnabled()"
            (click)="clickOnUploadButton()">
            {{'UBZ.SITE_CONTENT.110101000' | translate }}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
