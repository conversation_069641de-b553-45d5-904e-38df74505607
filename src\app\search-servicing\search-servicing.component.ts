import { Component, Inject, OnInit } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { Observable, Subscription } from 'rxjs';
import { SearchPageService } from '../search-page/service/search-page.service';
import { DomainService } from '../shared/domain';
import { PositionService } from '../shared/position/position.service';

@Component({
  selector: 'app-search-servicing',
  templateUrl: './search-servicing.component.html',
  styleUrls: ['./search-servicing.component.css']
})
export class SearchServicingComponent implements OnInit {

  searchType = '';
  statusPhase = {};
  categoryTypeDomain = {};
  resItemTypeDomain = {};
  macroProcessDomain = {};
  appraisalTypeDomain = {};
  scopeTypeDomain = {};
  domainList = [];
  pageSize = 10;
  pageNum = 1;
  servicingForm;
  subscription: Subscription;
  collateralsToView: any[] = [];
  collateralModalIsOpen = false;
  showResults: boolean = false;
  constructor(
    public searchPageService: SearchPageService,
    private positionService: PositionService,
    public domainService: DomainService,
  ) { }

  ngOnInit() {
    this.searchPageService.advancedSearch['count'] = null;
    this.servicingForm = new FormGroup({
      search: new FormGroup({
        ndg: new FormControl(''),
        warranty: new FormControl(''),
      })
    });
  }

  ndg(): any {
    return this.servicingForm.get('search.ndg');
  }

  warranty(): any {
    return this.servicingForm.get('search.warranty');
  }

  searchForServicing() {
    this.showResults = true;
    this.searchPageService.advancedSearch["searchResults"] = [];
    this.searchPageService.advancedSearch["count"] = 0;
    const input = {
      ndg: this.ndg().value,
      progCollateral: this.warranty().value
      
    }
    this.subscription = this.searchPageService
      .doNewSearchServicing(1, 10, input, this.searchType)
      .subscribe(result => {
        this.searchPageService.advancedSearch['searchResults'] = result.positions;
        this.searchPageService.advancedSearch['count'] = result.count;
        Observable.forkJoin(
          this.domainService.getStatusName(),
          this.domainService.newGetDomain('UBZ_DOM_CATEGORY_TYPE','-',true),
          this.domainService.newGetDomain('UBZ_DOM_RESITEM_TYPE'),
          this.domainService.newGetDomain('UBZ_DOM_MACRO_PROCESS'),
          this.domainService.newGetDomain('UBZ_DOM_APPRAISAL_TYPE'),
          this.domainService.newGetDomain('UBZ_DOM_SCOPE_TYPE')
        ).subscribe(res => {
          
          this.statusPhase = res[0];
          this.categoryTypeDomain = res[1];
          this.resItemTypeDomain = res[2];
          this.macroProcessDomain = res[3];
          this.appraisalTypeDomain = res[4];
          this.scopeTypeDomain = res[5];
        });
          this.pageNum = 1;
          this.pageSize = 10;
          this.showResults = false;

      },
      (error) => {
        console.log(error);
        this.showResults = false;
      });
  }

  changePageSize() {
    this.pageNum = 1;
    this.searchPageService
      .doNewSearchServicing(this.pageNum, Number(this.pageSize))
      .subscribe(x => {
        this.searchPageService.advancedSearch['searchResults'] = x.positions;
      });
  }

  changePage(event) {
    this.pageNum = event.page;
    this.searchPageService
      .doNewSearchServicing(this.pageNum, Number(this.pageSize))
      .subscribe(x => {
        this.searchPageService.advancedSearch['searchResults'] = x.positions;
      });
  }

  openCollateralModal(row) {
    if (this.searchType !== 'PE') {
      this.positionService.getCollateralData(row.id).subscribe(x => {
        row.collaterals = x;
        this.collateralsToView = row.collaterals;
        this.collateralModalIsOpen = true;
      });
    } else {
      this.positionService.getCollateralAssetList(row.appraisalId).subscribe(resp => {
        row.collaterals = resp;
        this.collateralsToView = row.collaterals;
        this.collateralModalIsOpen = true;
      });
    }
  }

  closeCollateralModal() {
    this.collateralModalIsOpen = false;
    this.collateralsToView = [];
  }

  getCollateralIds(row) {
    let retStr: string;
    if (row.collaterals) {
      if (this.searchType !== 'PE') {
        retStr = this.parseForSim(row);
      } else {
        retStr = this.parseForPer(row);
      }
    } else {
      retStr = '';
    }
    return retStr;
  }

  private parseForPer(row): string {
    let retStr = '';
    let charCounter = 0;
    for (const col of row.collaterals) {
      const elLength = col.jointCod.length;
      if (elLength + charCounter <= 7) {
        if (charCounter !== 0) {
          retStr += ',';
        }
        retStr += col.jointCod;
        charCounter += elLength;
      } else if (elLength <= 7) {
        const subIndex = 6 - charCounter;
        retStr += ',' + col.jointCod.substring(0, subIndex) + '...';
        break;
      }
    }
    return retStr;
  }

  goToDetailsPage(appraisalId) {
    this.searchPageService.goToDetailsPage(appraisalId, '/search-servicing');
  }

  private parseForSim(row): string {
    let retStr = '';
    let charCounter = 0;
    for (const col of row.collaterals) {
      const elLength = col.progCollateral.length;
      if (elLength + charCounter <= 7) {
        if (charCounter !== 0) {
          retStr += ',';
        }
        retStr += col.progCollateral;
        charCounter += elLength;
      } else if (elLength <= 7) {
        const subIndex = 6 - charCounter;
        retStr += ',' + col.progCollateral.substring(0, subIndex) + '...';
        break;
      }
    }
    return retStr;
  }
}
