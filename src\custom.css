/* Added by Andrea - Counter carousel style */
.Carousel__TabPanel {
  padding: 0 20px;
}
.Carousel__PrevArrow {
  position: absolute;
  top: 50%;
  left: -20px;
  display: block;
  font-size: 40px;
  width: 20px;
  height: 60px;
  padding: 0;
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  cursor: pointer;
  border: none;
  outline: none;
  background: transparent;
}
.Carousel__NextArrow {
  position: absolute;
  top: 50%;
  right: -20px;
  display: block;
  font-size: 40px;
  width: 20px;
  height: 60px;
  padding: 0;
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  cursor: pointer;
  border: none;
  outline: none;
  background: transparent;
}

/* Toastr notifications style */
#toast-container {
  top: 95px;
}

.toast-close-button {
  text-shadow: none;
  top: -0.2em;
  right: 0.2em;
  position: absolute;
  font-size: 27px !important;
}

#toast-container > div.toast-custom {
  padding: 0;
}

.toast-message a {
  color: #216eb2;
}

.CustomToast {
  display: flex;
}

.CustomToast__IconContainer {
  color: white;
  background-color: #009dbd;
  font-size: 24px;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.CustomToast__TextContainer {
  flex: 1;
  padding: 10px 15px;
  color: #00738a;
  background-color: #f2f8fa;
}

.CustomToast__Title {
  font-size: 16px;
  font-weight: bold;
}

.CustomToast__Message {
  font-size: 14px;
}

.CustomToast--error .CustomToast__IconContainer {
  background-color: #ea5c4d;
}

.CustomToast--success .CustomToast__IconContainer {
  background-color: #00a197;
}

.CustomToast--warning .CustomToast__IconContainer {
  background-color: #f2ab11;
}

.CustomToast--error .CustomToast__TextContainer {
  background-color: #faf2f0;
  color: #9e2215;
}

.CustomToast--success .CustomToast__TextContainer {
  background-color: #edf7f7;
  color: #00615a;
}

.CustomToast--warning .CustomToast__TextContainer {
  background-color: #fff8e5;
  color: #94741e;
}

#toast-container > div {
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.3);
  border-radius: 6px;
}

fieldset[disabled] input,
fieldset[disabled] button,
fieldset[disabled] select,
fieldset[disabled] i,
fieldset[disabled] a,
fieldset[disabled] textarea {
  pointer-events: none;
}
fieldset[disabled] i.note-tooltip {
  pointer-events: auto;
  cursor: pointer;
  display: inherit;
}
.note-tooltip {
  display: none
}

fieldset[disabled] a[role=tab] {
  pointer-events: auto;
}

.uc-table tbody tr:nth-child(even) {
  background-color: #f3f3f3 !important;
}

.uc-table tfoot {
  background-color: #e9e9e9 !important;
}

/*Tabs nella sezione "immobili a garanzia"*/
.interactive-tabs .nav-controls .nav-tabs .MyTabs__Tab--hidden {
  display: none !important;
}

.lavorazione-counter{
  background-color: #fff;
}
.lavorazione-counter .num{
  color: #338acc;
}
.lavorazione-counter:hover {
  background-color: #dfebf6;
  cursor: pointer;
}
.lavorazione-counter:hover .num {
  color: #338acc !important;
}
.lavorazione-counter.active{
  background-color: #82b9e5;
}
.lavorazione-counter.active .num{
  color: #fff;
}
.lavorazione-counter.active:hover {
  background-color: #dfebf6;
  cursor: pointer;
}
.lavorazione-counter.active:hover .num {
  color: #338acc;
}
/**/
.sospese-counter{
  background-color: #fff;
}
.sospese-counter .num{
  color: #1bacc2;
}
.sospese-counter:hover {
  background-color: #d9f0f3;
  cursor: pointer;
}
.sospese-counter:hover .num {
  color: #00acc4 !important;
}
.sospese-counter.active{
  background-color: #71ccda;
}
.sospese-counter.active .num{
  color: #fff;
}
.sospese-counter.active:hover {
  background-color: #d9f0f3;
  cursor: pointer;
}
.sospese-counter.active:hover .num {
  color: #1bacc2;
}
/**/
.second-opinion-counter{
  background-color: #fff;
}
.second-opinion-counter .num{
  color: #7757a4;
}
.second-opinion-counter:hover {
  background-color: #e8e3ef;
  cursor: pointer;
}
.second-opinion-counter:hover .num {
  color: #7755a6;
}
.second-opinion-counter.active{
  background-color: #ac98c8;
}
.second-opinion-counter.active .num{
  color: #fff;
}
.second-opinion-counter.active:hover {
  background-color: #e8e3ef;
  cursor: pointer;
}
.second-opinion-counter.active:hover .num {
  color: #7757a4;
}
/**/
.third-opinion-counter{
  background-color: #fff;
}
.third-opinion-counter .num{
  color: #3db049;
}
.third-opinion-counter:hover {
  background-color: #e0f1e1;
  cursor: pointer;
}
.third-opinion-counter:hover .num {
  color: #3db049;
}
.third-opinion-counter.active{
  background-color: #87cf8f;
}
.third-opinion-counter.active .num{
  color: #fff;
}
.third-opinion-counter.active:hover {
  background-color: #e0f1e1;
  cursor: pointer;
}
.third-opinion-counter.active:hover .num {
  color: #3db049;
}
/**/
.concluse-counter{
  background-color: #fff;
}
.concluse-counter .num{
  color: #0f796b;
}
.concluse-counter:hover {
  background-color: #d9e8e6;
  cursor: pointer;
}
.concluse-counter:hover .num {
  color: #00796b;
}
.concluse-counter.active{
  background-color: #6cada5;
}
.concluse-counter.active .num{
  color: #fff;
}
.concluse-counter.active:hover {
  background-color: #d9e8e6;
  cursor: pointer;
}
.concluse-counter.active:hover .num {
  color: #0f796b;
}
/**/
.pratiche-counter{
  background-color: #fff;
}
.pratiche-counter .num{
  color: #bfcb30;
}
.pratiche-counter:hover {
  background-color: #f3f5dd;
  cursor: pointer;
}
.pratiche-counter:hover .num {
  color: #bfcd14;
}
.pratiche-counter.active{
  background-color: #d7e07c;
}
.pratiche-counter.active .num{
  color: #fff;
}
.pratiche-counter.active:hover {
  background-color: #f3f5dd;
  cursor: pointer;
}
.pratiche-counter.active:hover .num {
  color: #bfcb30;
}
/**/
.annullata-counter{
  background-color: #fff;
}
.annullata-counter .num{
  color: #f58523;
}
.annullata-counter:hover {
  background-color: #fbeadc;
  cursor: pointer;
}
.annullata-counter:hover .num {
  color: #f58523;
}
.annullata-counter.active{
  background-color: #f8b57a;
}
.annullata-counter.active .num{
  color: #fff;
}
.annullata-counter.active:hover {
  background-color: #fbeadc;
  cursor: pointer;
}
.annullata-counter.active:hover .num {
  color: #f58523;
}
/**/
.attiva-counter{
  background-color: #fff;
}
.attiva-counter .num{
  color: #dd1860;
}
.attiva-counter:hover {
  background-color: #f7dae5;
  cursor: pointer;
}
.attiva-counter:hover .num {
  color: #dd1860;
}
.attiva-counter.active{
  background-color: #e9739e;
}
.attiva-counter.active .num{
  color: #fff;
}
.attiva-counter.active:hover {
  background-color: #f7dae5;
  cursor: pointer;
}
.attiva-counter.active:hover .num {
  color: #dd1860;
}
/**/
.richiesta-counter{
  background-color: #fff;
}
.richiesta-counter .num{
  color: #a33694;
}
.richiesta-counter:hover {
  background-color: #efdeed;
  cursor: pointer;
}
.richiesta-counter:hover .num {
  color: #a33694;
}
.richiesta-counter.active{
  background-color: #c785be;
}
.richiesta-counter.active .num{
  color: #fff;
}
.richiesta-counter.active:hover {
  background-color: #efdeed;
  cursor: pointer;
}
.richiesta-counter.active:hover .num {
  color: #a33694;
}
/**/
.richiesta-perizia-counter{
  background-color: #fff;
}
.richiesta-perizia-counter .num{
  color: #faaa18;
}
.richiesta-perizia-counter:hover {
  background-color: #fcf0da;
  cursor: pointer;
}
.richiesta-perizia-counter:hover .num {
  color: #faaa18;
}
.richiesta-perizia-counter.active{
  background-color: #fbcb73;
}
.richiesta-perizia-counter.active .num{
  color: #fff;
}
.richiesta-perizia-counter.active:hover {
  background-color: #fcf0da;
  cursor: pointer;
}
.richiesta-perizia-counter.active:hover .num {
  color: #faaa18;
}
/**/
.terminata-counter{
  background-color: #fff;
}
.terminata-counter .num{
  color: #004c3d;
}
.terminata-counter:hover {
  background-color: #d6e2e0;
  cursor: pointer;
}
.terminata-counter:hover .num {
  color: #004c3d;
}
.terminata-counter.active{
  background-color: #65948b;
}
.terminata-counter.active .num{
  color: #fff;
}
.terminata-counter.active:hover {
  background-color: #d6e2e0;
  cursor: pointer;
}
.terminata-counter.active:hover .num {
  color: #004c3d;
}
/**/
.riaperta-counter{
  background-color: #fff;
}
.riaperta-counter .num{
  color: #9e9f36;
}
.riaperta-counter:hover {
  background-color: #eeeede;
  cursor: pointer;
}
.riaperta-counter:hover .num {
  color: #9e9f36;
}
.riaperta-counter.active{
  background-color: #c4c485;
}
.riaperta-counter.active .num{
  color: #fff;
}
.riaperta-counter.active:hover {
  background-color: #eeeede;
  cursor: pointer;
}
.riaperta-counter.active:hover .num {
  color: #9e9f36;
}
/**/
.first-opinion-counter{
  background-color: #fff;
}
.first-opinion-counter .num{
  color: #e01e25;
}
.first-opinion-counter:hover {
  background-color: #f8dbdc;
  cursor: pointer;
}
.first-opinion-counter:hover .num {
  color: #e01e25 ;
}
.first-opinion-counter.active{
  background-color: #eb777b;
}
.first-opinion-counter.active .num{
  color: #fff;
}
.first-opinion-counter.active:hover {
  background-color: #f8dbdc;
  cursor: pointer;
}
.first-opinion-counter.active:hover .num {
  color: #e01e25;
}
.only-side-padding {
  padding-top: 0px !important;
  padding-bottom: 0px !important;
}

accordion-group.panel {
  float: left;
  width: 100%;
}

.expiredCategory {
  color: #ff0000;
  font-family: 'unicreditmedium';
  line-height: initial;
}

.MyTabs__Dropdown {
  position: relative;
  float: left;
}

.MyTabs__Dropdown__Menu ul {
  list-style: none;
  padding: 0;
}

.MyTabs__Dropdown__Menu .state {
  width: 12px;
  height: 12px;
  float: right;
  margin-top: 4px;
}

.MyTabs__Dropdown__Menu__Item--active {
  background-color: #eaf0f2;
}

.dropdown-item {
  padding: 10px;
  font-size: 14px;
}

.dropdown-item:hover {
  cursor: pointer;
  background-color: #f7f9fa;
}

.dropdown-item a {
  color: #333;
}

.dropdown-item:hover a {
  text-decoration: none;
}

.MyTabs__Tab {
  width: 160px;
}

.MyTabs__Dropdown__Toggle {
  border-left: 1px solid #ddd;
}

.dropdown-menu-right {
  overflow-y: scroll;
  max-height: 250px;
}

.MyTabs__Tab--hidden {
  visibility: hidden;
}


.Search__NoResults {
  display: flex;
  align-items: center;
  margin-top: 30px;
}

.Search__NoResults__Icon {
  padding: 0 20px;
  color: #bbb;
  font-size: 30px;
}

.Search__NoResults__Text {
  display: flex;
  flex-direction: column;
}

.Search__NoResults__Title {
  font-size: 24px;
  color: #999;
  text-transform: none;
  padding: 0;
  margin: 0 0 5px 0;
}

.Search__NoResults__Subtitle {
  font-size: 18px;
  color: #bbb;
  margin: 0;
}

.Search__ResultsTable {
  margin-top: 20px;
}

.Search__Tab__ResultCount {
  font-family: 'unicreditbold';
  font-weight: 700;
  font-size: 12px;
  color: #535453;
}

.uc-datatabs .nav-tabs > li.active .Search__Tab__ResultCount {
  color: #388bca;
}

/**
 * Utils
 */

.d-inline {
  display: inline;
}

.cursor-pointer {
  cursor: pointer;
}

textarea:focus {
  z-index: 1;
  position: relative;
}