<form id="stepOne" method="post">
  <div class="row">
    <div class="col-sm-12">
      <div class="success-confirmation">
        <!-- Visualizzo il messaggio di conferma a seconda del tipo di wizard -->
        <ng-container *ngIf="wizardCode === 'WSIM' && dataLoaded">
          <h3><i class="icon-check"></i>
             {{'UBZ.SITE_CONTENT.1000001' | translate }}</h3>
          <p>{{'UBZ.SITE_CONTENT.1000010' | translate }}.</p>
          <p>{{'UBZ.SITE_CONTENT.1000011' | translate }}. </p>
          <p></p>
          <p>{{'UBZ.SITE_CONTENT.1000100' | translate }}.</p>
          <ng-container *appAuthKey="'UBZ_CONFIRM_DETAIL'">
            <button type="button" class="btn btn-tertiary waves-effect" (click)="goToSimulationDetail()" >{{'UBZ.SITE_CONTENT.1000101' | translate }}</button>
          </ng-container>
          <ng-container *appAuthKey="'UBZ_CONFIRM_REQUEST'">
            <button type="button" class="btn btn-primary waves-effect" (click)="startAppraisalRequest()" >{{'UBZ.SITE_CONTENT.1000110' | translate }}</button>
          </ng-container>
          <p>
            <span>{{'UBZ.SITE_CONTENT.1000111' | translate }}</span>
            <span><u>{{'UBZ.SITE_CONTENT.11' | translate }}</u></span>
          </p>
        </ng-container>
        <ng-container *ngIf="wizardCode === 'WRPE' && dataLoaded">
          <h3><i class="icon-check"></i>
            {{'UBZ.SITE_CONTENT.110001100' | translate }}</h3>
          <p>{{'UBZ.SITE_CONTENT.11100110' | translate }}.</p>
          <ng-container *appAuthKey="'UBZ_CONFIRM_APPRAISAL'">
          <!-- Se originationProcess === 'EMP' si nasconde pulsante avvia perizia perchè l'utente non può compiere azioni -->
          <!-- FIXME - PULSANTE MOSTRATO ANCHE PER EMP PER TESTARE IN LOCALE, DA TOGLIERE -->
            <!-- <button
              *ngIf = "landingService.originationProcess !== 'EMP'" 
              type="button" class="btn btn-primary waves-effect" 
              (click)="goToNewAppraisal()" 
              [disabled]="!landingService.positionLocked">
              {{'UBZ.SITE_CONTENT.1001011000' | translate }}
            </button> -->
            <button
              type="button" class="btn btn-primary waves-effect" 
              (click)="goToNewAppraisal()" 
              [disabled]="!landingService.positionLocked">
              {{'UBZ.SITE_CONTENT.1001011000' | translate }}
            </button>
          </ng-container>
        </ng-container>
      </div>
    </div>
  </div>
</form>

<div *ngIf="isModalOpen" class="modal fade" bsModal #modalBox="bs-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" (onHidden)="hideModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">        
        <h2>{{'UBZ.SITE_CONTENT.10101010111' | translate }}</h2>
        <button type="reset" class="close pull-right" aria-label="Close" (click)="hideModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="row pratica-states pb-3">
          <div class="col-sm-12">
            {{ restrictionAssetMessage | translate }}
          </div>         
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary waves-effect" (click)="goToNewAppraisalConfirmed()">
          <span>{{'UBZ.SITE_CONTENT.1001011000' | translate }}</span>
        </button>
      </div>
    </div>
  </div>
</div>

