import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { AmeoriService } from '../../service/ameori.service';
import { MessageService } from '../../../shared/messages/services/message.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-ameori-update',
  templateUrl: './ameori-update.component.html',
  styleUrls: ['./ameori-update.component.css']
})
export class AmeoriUpdateComponent implements OnInit {
  @Input()
  ameori: any;
  newAmount: number = null;
  isEnableToConfirm: boolean = false;
  @Output()
  onConfirm = new EventEmitter<boolean>();
  @Output()
  modalClose = new EventEmitter<boolean>();
  isSameValue: boolean;
  constructor(
    private messageService: MessageService,
    private translateService: TranslateService,
    private ameoriService: AmeoriService
  ) {}

  ngOnInit() {}

  evaluateIsEnableToConfirm() {
    this.isSameValue = this.newAmount === this.ameori.appValue;
    this.isEnableToConfirm = this.newAmount > 0 && this.newAmount != null;
  }

  confirm() {
    if (this.isSameValue) {
      this.messageService.showInfo(
        this.translateService.instant('UBZ.SITE_CONTENT.11101111011'),
        this.translateService.instant('UBZ.SITE_CONTENT.11101111010')
      );
    } else {
      this.ameoriService.update(this.ameori, this.newAmount).subscribe(res => {
        if (res === 'OK') {
          this.messageService.showSuccess(
            this.translateService.instant('UBZ.SITE_CONTENT.11101111101'),
            this.translateService.instant('UBZ.SITE_CONTENT.11101111010')
          );
          this.onConfirm.emit();
        } else {
          this.messageService.showError(
            this.translateService.instant('UBZ.SITE_CONTENT.11101111110'),
            this.translateService.instant('UBZ.SITE_CONTENT.11101111010')
          );
        }
      });
    }
  }

  hide() {
    this.modalClose.emit(true);
  }
}
