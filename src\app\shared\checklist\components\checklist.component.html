<div class="row step-navigation">
  <div class="col-sm-6">
    <div class="custom-checkbox">
      <input type="checkbox" id="selectall" class="checkbox" (click)="toggleAllAssetSelected()">
      <label for="selectall">{{'UBZ.SITE_CONTENT.111010' | translate }}</label>
    </div>
    <ng-container *appAuthKey="'UBZ_CHECKLIST_PRINT'">
      <!-- <button *ngIf="renderIconPrint" id="print" type="button" class="btn btn-empty"><i class="icon-printer"></i></button> -->
    </ng-container>
    <ng-container *appAuthKey="'UBZ_CHECKLIST_UPLOAD.SELECTED'">
      <button *ngIf="renderIconUpload" id="upload" type="button" class="btn btn-empty"
        (click)="openModalFileAssociati()">
        <i class="icon-upload"></i>
      </button>
    </ng-container>
  </div>
  <div class="col-sm-6 btn-set">
    <ng-container *appAuthKey="'UBZ_CHECKLIST_DOCUMENT'">
      <button [disabled]="(bankCod==='00100' && macroProcess !=='MLR')" type="button" class="btn btn-empty pull-right"
        (click)="openAggiungiDocumento(true)">
        <i class="icon-add"></i> {{'UBZ.SITE_CONTENT.********' | translate }}
      </button>
    </ng-container>
    <ng-container *appAuthKey="'UBZ_CHECKLIST_PRINT.CHECKLIST'">
      <button type="button" class="btn btn-empty pull-right" (click)="printChecklist()"><i
          class="icon-print_checklist"></i>
        {{'UBZ.SITE_CONTENT.********' | translate }}
      </button>
    </ng-container>
    <button type="button" class="btn btn-empty pull-right" (click)="printMandato()"><i class="icon-detail_page"></i>
      {{'UBZ.SITE_CONTENT.**********' | translate }}
    </button>
  </div>
</div>

<div class="row">
  <div class="col-sm-12">
    <h3>{{'UBZ.SITE_CONTENT.**********' | translate }}</h3>
    <div id="alert-option-first" class="alert alert-warning" role="alert" style="display: none">
      {{'UBZ.SITE_CONTENT.********' | translate }}.
    </div>
    <div id="alert-option-second" class="alert alert-warning" role="alert" style="display: none">
      {{'UBZ.SITE_CONTENT.11011001' | translate }}. <a id="cancel-action">{{'UBZ.SITE_CONTENT.100000' | translate }}</a>
    </div>
  </div>
</div>

<div class="row">

  <accordion class="panel-group" id="accordion">
    <accordion-group *ngFor="let accord of accordions; let i = index" #group class="panel"
      [isOpen]="accordionsStatusOpen[i]">
      <div accordion-heading class="acc-note-headline" role="tab">
        <h4 class="panel-title">
          <a role="button">
            <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>

            <ng-container *ngIf="entityTypeDom[accord.entityType] && accord.entityType === 'ASS'">
              {{ entityTypeDom[accord.entityType].translationCod | translate }} {{
              accord.resItemCategory }} {{accord.offerId}}
            </ng-container>
            <ng-container *ngIf="entityTypeDom[accord.entityType] && accord.entityType === 'PER'">
              {{ entityTypeDom[accord.entityType].translationCod | translate }}
            </ng-container>
            <ng-container *ngIf="entityTypeDom[accord.entityType] && accord.entityType === 'RIC'">
              {{ entityTypeDom[accord.entityType].translationCod | translate }}
            </ng-container>

            <span [class]="getAccordionStatusClass(accord)"></span>
          </a>
        </h4>
      </div>
      <div class="panel-collapse collapse in" role="tabpanel">
        <div class="panel-body">
          <div class="row">
            <div class="col-sm-12">
              <table class="uc-table">
                <thead>
                  <tr>
                    <th scope="col" class="col-sm-1 checkbox-col"></th>
                    <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10010110' | translate }}</th>
                    <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.11011010' | translate }}</th>
                    <th scope="col" class="col-sm-2">Nome del file</th>
                    <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.10000001' | translate }}</th>
                    <th scope="col" class="col-sm-2 hidden-xs hidden-sm">
                      {{'UBZ.SITE_CONTENT.1111110' | translate }}
                    </th>
                    <th scope="col" class="col-sm-2 hidden-xs hidden-sm">{{'UBZ.SITE_CONTENT.10110' | translate }}</th>
                    <th scope="col" class="col-sm-3">{{'UBZ.SITE_CONTENT.111110' | translate }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let row of accord.groups; let i2 = index" class="" data-index="1">
                    <td attr.data-label="">
                      <div class="custom-checkbox">
                        <input id="{{row.prog}}" name="checkbox-row" type="checkbox" class="checkbox"
                          [checked]="checkboxStatus[row.prog]" (change)="setCheckboxStatus(row.prog)">
                        <label for="{{row.prog}}"></label>
                      </div>
                    </td>
                    <td *ngIf="row.categoryDesc && row.groupCod !== '999'"
                      attr.data-label="{{'UBZ.SITE_CONTENT.10010110' | translate }}">{{ row.categoryDesc }}</td>
                    <td *ngIf="row.categoryDesc && row.groupCod === '999'"
                      attr.data-label="{{'UBZ.SITE_CONTENT.10010110' | translate }}">{{ row.lastUpload.customDocDesc ?
                      row.lastUpload.customDocDesc : row.categoryDesc }}</td>
                    <td *ngIf="!row.categoryDesc" attr.data-label="{{'UBZ.SITE_CONTENT.10010110' | translate }}"
                      style="color:#999">{{'UBZ.SITE_CONTENT.********00' | translate }}</td>
                    <td attr.data-label="{{'UBZ.SITE_CONTENT.11011010' | translate }}">{{
                      getStatusFormatted(row.mandatoryFor) | translate }}</td>
                    <td style="word-wrap:break-word;">{{row.lastUpload.documentName}}</td>
                    <td attr.data-label="{{'UBZ.SITE_CONTENT.10000001' | translate }}">
                      <div class="status-wrap">
                        <span [class]="getRowClass( row.acquired , row.flagMandatory)"></span>
                        <span class="info-message" style="display: none">
                          <i class="icon-info blue-tooltip" aria-hidden="true" data-placement="right"
                            data-toggle="tooltip" title="{{'UBZ.SITE_CONTENT.********01' | translate }}"></i>
                        </span>
                        <span class="info-message-del" style="display: none">
                          <i class="icon-info blue-tooltip" aria-hidden="true" data-placement="right"
                            data-toggle="tooltip"
                            title="{{'UBZ.SITE_CONTENT.********10' | translate }}. {{'UBZ.SITE_CONTENT.********11' | translate }}."></i>
                        </span>
                      </div>
                    </td>
                    <td class="data hidden-xs hidden-sm" attr.data-label="{{'UBZ.SITE_CONTENT.1111110' | translate }}">
                      <span *ngIf="row.lastUpload.uploadDate">{{ row.lastUpload.uploadDate | date: 'dd-MM-y' }}
                        {{row.lastUpload.uploadDate | customTime}}</span>
                      <span *ngIf="!row.lastUpload.uploadDate" style="color:#999">---</span>
                    </td>
                    <td class="utente hidden-xs hidden-sm" attr.data-label="{{'UBZ.SITE_CONTENT.10110' | translate }}">
                      <span *ngIf="row.lastUpload.userId">{{row.lastUpload.userId}}</span>
                      <span *ngIf="!row.lastUpload.userId" style="color:#999">---</span>
                    </td>
                    <td attr.data-label="{{'UBZ.SITE_CONTENT.111110' | translate }}">
                      <ng-container *appAuthKey="'UBZ_CHECKLIST_INVALID'">
                        <button [disabled]="(row.acquired === 'N') || (bankCod==='00100')" data-toggle="modal"
                          data-target="#invalidate" type="button" class="btn btn-empty invalidate-btn"
                          (click)="openModalInvalid(row , accord); $event.stopPropagation()" data-toggle="tooltip"
                          title="{{'UBZ.SITE_CONTENT.*********' | translate }}">
                          <i class="icon-invalidate"></i>
                        </button>
                      </ng-container>
                      <ng-container *appAuthKey="'UBZ_CHECKLIST_HISTORY'">
                        <button [disabled]="row.acquired === 'N'" data-toggle="modal" data-target="#clock" type="button"
                          class="btn btn-empty clock-btn"
                          (click)="openModalHistory(row , accord); $event.stopPropagation()" data-toggle="tooltip"
                          title="{{'UBZ.SITE_CONTENT.*********' | translate }}">
                          <i class="icon-clock" disabled></i>
                        </button>
                      </ng-container>
                      <ng-container *appAuthKey="'UBZ_CHECKLIST_NOTE'">
                        <button (click)="openModalNote(row , accord); $event.stopPropagation()" type="button"
                          class="btn btn-empty" data-toggle="tooltip"
                          title="{{'UBZ.SITE_CONTENT.********' | translate }}">
                          <i class="icon-note"></i>
                        </button>
                      </ng-container>
                      <ng-container *appAuthKey="'UBZ_CHECKLIST_UPLAOD'">
                        <button [disabled]="(bankCod==='00100')"
                          (click)="openModalUploadFileSingle(row , accord); $event.stopPropagation()" type="button"
                          class="btn btn-empty upload-single-btn hidden-xs hidden-sm" data-toggle="tooltip"
                          title="{{'UBZ.SITE_CONTENT.********' | translate }}">
                          <i class="icon-upload"></i>
                        </button>
                      </ng-container>
                      <button [disabled]="row.acquired === 'N'"
                        (click)="downloadLastUpload(row , accord); $event.stopPropagation()" type="button"
                        class="btn btn-empty upload-single-btn hidden-xs hidden-sm" data-toggle="tooltip"
                        title="Download">
                        <i class="icon-download"></i>
                      </button>
                      <!-- FIXME - <div class="dropdown table-btn-dropdown hidden-md hidden-lg">
                        <button class="btn btn-empty dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                          <i class="icon-upload"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-right text-right" aria-labelledby="dropdownMenu6">
                          <button type="button" class="btn btn-empty">
                            <i class="fa fa-camera"></i> {{'UBZ.SITE_CONTENT.11011101' | translate }}
                          </button>
                          <button type="button" class="btn btn-empty">
                            <i class="fa fa-picture-o"></i> {{'UBZ.SITE_CONTENT.11011110' | translate }}
                          </button>
                          <button type="button" class="btn btn-empty">
                            <i class="fa fa-file-text-o"></i> {{'UBZ.SITE_CONTENT.11011111' | translate }}
                          </button>
                        </ul>
                      </div> -->
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </accordion-group>
  </accordion>
</div>

<!--MODAL BOX PER L'AGGIUNTA DI UN NUOVO DOCUMENTO-->
<app-modal-nuovo-documento *ngIf="(aggiungiDocumentoIsOpen === true) && inputModalObject"
  [isOpen]="aggiungiDocumentoIsOpen" [accordions]="inputModalObject" [positionId]="positionId" [requestId]="requestId"
  (modalClose)="closeAggiungiDocumento($event)" [isUpload]="aggiungiDocumentoWithUpload"></app-modal-nuovo-documento>
<!-- FINE MODAL AGGIUNTA DOCUMENTI -->

<!--MODAL BOX PER L'UPLOAD DI FILE ASSOCIATI-->
<app-modal-file-associati *ngIf="(uploadFileAssociatiIsOpen === true)" [isOpen]="uploadFileAssociatiIsOpen"
  [positionId]="positionId" [requestId]="requestId" [documentToUploadMap]="checkboxStatus"
  [progToDocCodMap]="progToDocCodMap" (modalClose)="closeModalFileAssociati($event)"></app-modal-file-associati>
<!-- FINE MODAL UPLOAD DI FILE ASSOCIATI -->

<!--MODAL BOX PER UPLOAD FILE SINGLE-->
<app-modal-upload-single *ngIf="(uploadFileSingleIsOpen === true) && inputModalObject" [isOpen]="uploadFileSingleIsOpen"
  [section]="inputModalObject2" [positionId]="positionId" [requestId]="requestId" [document]="inputModalObject"
  (modalClose)="closeModalUploadFileSingle($event)"></app-modal-upload-single>
<!-- FINE MODAL UPLOAD FILE SINGLE -->

<!--MODAL BOX PER NOTE-->
<app-modal-note *ngIf="(modalNoteIsOpen === true) && inputModalObject" [isOpen]="modalNoteIsOpen"
  [document]="inputModalObject" [section]="inputModalObject2" (modalClose)="closeModalNote($event)"></app-modal-note>
<!-- FINE MODAL NOTE -->

<!--  MODAL BOX PER LO STORICO DELLA CHECKLIST-->
<app-modal-history *ngIf="(modalHistoryIsOpen === true) && inputModalObject" [isOpen]="modalHistoryIsOpen"
  [document]="inputModalObject" [section]="inputModalObject2" [positionId]="positionId" [requestId]="requestId"
  [isRequestId]="isRequestId" (modalClose)="closeModalHistory($event)"></app-modal-history>
<!--  MODAL BOX PER LO STORICO DELLA CHECKLIST-->

<!--  MODAL BOX PER L'ANNULLAMENTO DELLA CHECKLIST-->
<app-modal-invalid *ngIf="(modalInvalidIsOpen === true) && inputModalObject" [isOpen]="modalInvalidIsOpen"
  [document]="inputModalObject" [section]="inputModalObject2" (modalClose)="closeModalInvalid($event)">
</app-modal-invalid>
<!--  MODAL BOX PER L'ANNULLAMENTO DELLA CHECKLIST-->