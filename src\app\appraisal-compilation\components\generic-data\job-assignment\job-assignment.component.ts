import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  AfterViewChecked
} from '@angular/core';
import { SendAppraisalComponent } from '../../../../tasks/components/send-appraisal/send-appraisal.component';
import { NgForm } from '@angular/forms';
import { AccordionAPFService } from '../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';

@Component({
  selector: 'app-job-assignment',
  templateUrl: './job-assignment.component.html',
  styleUrls: ['./job-assignment.component.css']
})
export class JobAssignmentComponent implements OnInit, AfterViewChecked {
  @Input()
  positionId: string;
  @Input()
  socId;
  @Output()
  chosenExpert: EventEmitter<any> = new EventEmitter<any>();
  @Output()
  experts: EventEmitter<any> = new EventEmitter<any>();
  @Output()
  formStatusChange: EventEmitter<boolean> = new EventEmitter<boolean>();

  @ViewChild(SendAppraisalComponent)
  sendAppraisalComponent: SendAppraisalComponent;
  private subscribed: boolean;
  public validForm = false;

  constructor(public _accordionAPFService: AccordionAPFService) {}

  ngOnInit() {}

  ngAfterViewChecked() {
    if (!this.subscribed) {
      if (this.sendAppraisalComponent && this.sendAppraisalComponent.form) {
        const form: NgForm = this.sendAppraisalComponent.form;
        this.subscribed = true;
        form.statusChanges.subscribe(() => {
          setTimeout(() => {
            this.validForm = form.valid;
            // Se il campo 'Importo fattura' della sezione trasparenza è valorizzato
            // deve avere valore strettamente maggiore di zero
            if (
              form &&
              form.controls 
            &&
              form.controls['billing-amount'] &&
              form.controls['billing-amount'].value === 0
             &&
              form.controls['billing-amountsecound'] &&
              form.controls['billing-amountsecound'].value === 0
              ) {
              this.validForm = false;
            }
            this.formStatusChange.emit(this.validForm);
          }, 10);
        });
      }
    }
  }

  emit(event: any) {
    this.chosenExpert.emit(event);
  }

  newEmit(event: any) {
    this.experts.emit(event);
  }
}
