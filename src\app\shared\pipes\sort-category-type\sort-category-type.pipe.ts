import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'sortCategoryType'
})
export class SortCategoryTypePipe implements PipeTransform {
  transform(value: any, args?: any): any[] {
    let tmpIndex;
    let tmpElement = {};
    for (const key in value) {
      if (value.hasOwnProperty(key)) {
        tmpIndex = key;
        for (const key2 in value) {
          if (Number(key) < Number(key2)) {
            if (
              this.firstIsLower(
                value[tmpIndex].domCode,
                value[key2].domCode
              ) === false
            ) {
              tmpIndex = key2;
            }
          }
        }
        tmpElement = value[key];
        value[key] = value[tmpIndex];
        value[tmpIndex] = tmpElement;
      }
    }
    return value;
  }

  private firstIsLower(first: string, second: string): boolean {
    const slashIndexFirst = first.indexOf('/');
    const slashIndexSecond = second.indexOf('/');
    let firstStartWith = '';
    let secondStartWith = '';
    let firstFinishWith = -1;
    let secondFinishWith = -1;
    if (slashIndexFirst === -1) {
      firstStartWith = first;
    } else {
      firstStartWith = first.substring(0, slashIndexFirst);
      firstFinishWith = Number(first.substring(slashIndexFirst + 1));
    }
    if (slashIndexSecond === -1) {
      secondStartWith = second;
    } else {
      secondStartWith = second.substring(0, slashIndexSecond);
      secondFinishWith = Number(second.substring(slashIndexSecond + 1));
    }
    if (
      firstStartWith < secondStartWith ||
      (firstStartWith === secondStartWith && firstFinishWith < secondFinishWith)
    ) {
      return true;
    } else {
      return false;
    }
  }
}
