import { SortDomainByLengthPipe } from "./sort-domain-by-length.pipe";

describe('SortDomainByLengthPipe', () => {
    
    it('should test SortDomainByLengthPipe', () => {
        let arrayObject = [{"domCode": "A1"}, {"domCode": "A2"}, {"domCode": "A3"}, {"domCode": "A4"}]
        let arrayObjectExpect = [{"domCode": "A4"}, {"domCode": "A3"}, {"domCode": "A2"}, {"domCode": "A1"}]
        const pipe = new SortDomainByLengthPipe();
        arrayObject = pipe.transform(arrayObject);
        const listArray = arrayObject.map(item => item.domCode);
        const listArrayExpect = arrayObjectExpect.map(item => item.domCode);
        
       expect(listArray).toEqual(listArrayExpect)
    });

    it('should test SortDomainByLengthPipe length one', () => {
        let arrayObjectOneLength = [{"domCode": "b"}, {"domCode": "c"}, {"domCode": "d"}, {"domCode": "e"}]
        let arrayObjectOneLengthExpect = [{"domCode": "b"}, {"domCode": "c"}, {"domCode": "d"}, {"domCode": "e"}]
        const pipe = new SortDomainByLengthPipe();
        arrayObjectOneLength = pipe.transform(arrayObjectOneLength);
        const listArray = arrayObjectOneLength.map(item => item.domCode);
        const listArrayExpect = arrayObjectOneLengthExpect.map(item => item.domCode);

        expect(listArray).toEqual(listArrayExpect)
    });
});

