import { AssetRequestObject } from './asset-request-object';

export class AssetSearch {
  page: number;
  pageSize: number;
  orderBy: string;
  deletedAsset: boolean;
  asc: boolean;
  filter: AssetRequestObject;

  constructor() {
    this.page = 1;
    this.pageSize = 10;
    this.orderBy = 'redItemId';
    this.asc = true;
    this.deletedAsset = false;
    this.filter = new AssetRequestObject();
  }
}
