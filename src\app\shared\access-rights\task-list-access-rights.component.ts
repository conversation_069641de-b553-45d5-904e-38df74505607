import { Component, OnInit, Input, Inject } from '@angular/core';
import { Router } from '@angular/router';

import { Task } from './model/task';
import { AccessRightsService } from './services/access-rights.service';
import { APP_CONSTANTS, IAppConstants } from '../../app.constants';

@Component({
  selector: 'app-task-list-access-rights',
  templateUrl: './task-list-access-rights.component.html',
  styleUrls: ['./task-list-access-rights.component.css']
})
export class TaskListAccessRightsComponent implements OnInit {
  @Input() positionId: string;

  taskList: Task[];

  constructor(
    private accessRightsService: AccessRightsService,
    private router: Router,
    @Inject(APP_CONSTANTS) public constants: IAppConstants
  ) {}

  ngOnInit() {
    this.accessRightsService
      .getOpenTaskForUser(`${this.constants.processCode}${this.positionId}`)
      .subscribe((taskList: Task[]) => (this.taskList = taskList));
  }

  goToTask(taskId: string, taskCod: string) {
    this.router.navigateByUrl(
      `task-landing/${this.positionId}/${taskId}/${taskCod}`
    );
  }
}
