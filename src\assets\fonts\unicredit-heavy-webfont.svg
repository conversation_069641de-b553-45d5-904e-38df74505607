<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="unicreditheavy" horiz-adv-x="1187" >
<font-face units-per-em="2048" ascent="1536" descent="-512" />
<missing-glyph horiz-adv-x="444" />
<glyph unicode="&#xfb01;" horiz-adv-x="1271" d="M4 799v100q0 39 9.5 51.5t43.5 22.5l109 26v97q0 86 35 147.5t88 95.5q53 33 134 49.5t202 16.5h8q61 0 129.5 -7t122.5 -18q29 -6 38 -19t9 -32q0 -4 -1 -24.5t-5 -49.5l-8 -63q-4 -29 -14.5 -39t-45.5 -8q-33 2 -87 4t-97 2h-4q-96 0 -133 -18q-43 -20 -43 -84v-45h600 q41 0 55 -15.5t14 -44.5v-885q0 -29 -14 -44t-55 -15h-189q-41 0 -55 15.5t-14 43.5v689h-342v-689q0 -29 -14.5 -44t-55.5 -15h-188q-41 0 -55.5 15.5t-14.5 43.5v689h-111q-31 0 -41 9t-10 42z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1374" d="M4 799v100q0 39 9.5 51.5t43.5 22.5l109 26v88q0 92 28.5 151.5t94.5 100.5q47 29 123.5 47.5t205.5 18.5h15q66 0 143.5 -12.5t141.5 -28.5l192 22q41 4 55.5 -13t14.5 -46v-1022q0 -23 8 -31t31 -8h65q31 0 45 -14t14 -55v-127q0 -41 -14 -55.5t-45 -14.5h-156 q-154 0 -215 59q-35 33 -48 81t-13 126v854q-35 10 -81 18.5t-87 8.5h-10q-102 0 -141 -21.5t-39 -87.5v-34h170q29 0 41 -10.5t12 -49.5v-137q0 -39 -12.5 -49t-40.5 -10h-170v-689q0 -29 -14.5 -44t-55.5 -15h-188q-41 0 -55.5 15.5t-14.5 43.5v689h-111q-31 0 -41 9 t-10 42z" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xd;" horiz-adv-x="444" />
<glyph unicode=" "  horiz-adv-x="444" />
<glyph unicode="&#x09;" horiz-adv-x="444" />
<glyph unicode="&#xa0;" horiz-adv-x="444" />
<glyph unicode="!" horiz-adv-x="526" d="M82 141v17q0 94 51 129t123 35h6q35 0 66.5 -8.5t55.5 -27t38 -50t14 -78.5v-17q0 -45 -14 -75.5t-38 -49t-55.5 -26.5t-66.5 -8h-6q-35 0 -66.5 8t-55 26.5t-38 49t-14.5 75.5zM86 1282q-4 51 13.5 64.5t72.5 13.5h184q55 0 72.5 -13.5t13.5 -64.5l-36 -805 q-2 -43 -20.5 -54t-66.5 -11h-114q-47 0 -62.5 12t-17.5 49z" />
<glyph unicode="&#x22;" horiz-adv-x="1112" d="M115 1446q-2 20 10 31.5t31 11.5h268q45 0 41 -43l-31 -535q0 -37 -35 -37h-217q-16 0 -26.5 9.5t-10.5 27.5zM647 1446q-2 20 10 31.5t31 11.5h268q45 0 41 -43l-31 -535q0 -37 -35 -37h-217q-16 0 -26.5 9.5t-10.5 27.5z" />
<glyph unicode="#" horiz-adv-x="1349" d="M-4 350q0 14 1 22.5t9 43.5l21 90q8 31 18 41t45 10h162l43 176h-160q-25 0 -34 9.5t-9 25.5q0 14 1 22.5t9 43.5l21 90q8 31 18.5 41t44.5 10h164l70 293q10 47 33.5 55t70.5 8h113q47 0 67.5 -12t8.5 -62l-68 -282h174l70 293q10 45 31.5 54t72.5 9h117q47 0 65.5 -12 t6.5 -62l-68 -282h197q43 0 43 -31q0 -16 -5.5 -46t-13.5 -64l-14 -60q-6 -23 -16.5 -32t-44.5 -9h-203l-41 -176h196q43 0 43 -29q0 -18 -4 -42.5t-10 -51.5l-16 -72q-10 -47 -64 -47h-200l-60 -252q-10 -43 -34.5 -53t-69.5 -10h-115q-47 0 -67.5 12.5t-8.5 61.5l60 241 h-177l-59 -252q-10 -41 -32.5 -52t-71.5 -11h-115q-47 0 -65.5 12.5t-6.5 61.5l58 241h-158q-25 0 -34 9.5t-9 25.5zM547 557h176l39 176h-174z" />
<glyph unicode="$" d="M117 915v66q0 178 111.5 265t346.5 95v160q0 29 27 29h62q29 0 28 -29v-160q80 -4 166 -19t139 -34q35 -12 35 -43q0 -18 -12 -74l-27 -120q-6 -25 -22.5 -33t-55.5 2q-49 12 -111.5 21.5t-111.5 13.5v-244l68 -12q94 -16 156.5 -41t99.5 -66t52 -104.5t15 -157.5v-51 q0 -195 -105.5 -292t-342.5 -105v-138q0 -29 -29 -28h-61q-27 0 -27 28v140q-80 6 -173 21t-155 36q-41 12 -52 29.5t-7 50.5l27 158q6 35 23.5 44t49.5 -1q70 -20 145 -36t142 -20v271l-90 16q-90 14 -149.5 45t-95.5 77t-51 106.5t-15 133.5zM451 936q0 -49 26.5 -70.5 t97.5 -36.5v224q-66 -6 -95 -27.5t-29 -67.5v-22zM635 264q59 4 87 25.5t28 79.5v32q0 53 -28 78t-87 39v-254z" />
<glyph unicode="%" horiz-adv-x="1902" d="M94 936v61q0 82 18.5 147.5t73.5 119.5q39 37 97.5 58t144.5 21q78 0 136.5 -20t99.5 -53q59 -55 79.5 -133t20.5 -160v-21q0 -82 -20.5 -159.5t-79.5 -132.5q-41 -33 -99.5 -53.5t-136.5 -20.5q-86 0 -144.5 21.5t-97.5 58.5q-55 53 -73.5 118.5t-18.5 147.5zM371 911 q0 -68 15 -87t40 -19h4q25 0 40 19.5t15 86.5v111q0 68 -15 87t-40 19h-4q-25 0 -40 -19t-15 -87v-111zM408.5 1q-13.5 19 7.5 54l755 1245q16 29 35 36t58 7h149q47 0 60.5 -19t-7.5 -54l-755 -1245q-16 -29 -35 -36t-58 -7h-149q-47 0 -60.5 19zM1139 328v61 q0 82 18.5 147.5t73.5 118.5q39 37 97 58.5t145 21.5q78 0 136 -20.5t99 -52.5q59 -55 79.5 -133t20.5 -160v-21q0 -82 -20.5 -159.5t-79.5 -133.5q-41 -33 -99.5 -53t-135.5 -20q-86 0 -144.5 21.5t-97.5 57.5q-55 53 -73.5 119t-18.5 148zM1415 303q0 -68 15.5 -87 t39.5 -19h5q25 0 40 19.5t15 86.5v111q0 68 -15.5 87t-39.5 19h-5q-25 0 -40 -19.5t-15 -86.5v-111z" />
<glyph unicode="&#x26;" horiz-adv-x="1296" d="M66 344v18q0 127 65.5 209t187.5 123q-84 70 -129 148t-45 164v12q0 74 30 133t86 102t136 66.5t178 23.5h19q199 0 307.5 -89t108.5 -236v-10q0 -125 -72 -218.5t-195 -152.5l197 -209q16 66 16 154v22q0 39 43 39h160q20 0 30.5 -8t10.5 -35v-35q0 -104 -14.5 -190 t-52.5 -150l104 -110q39 -39 0 -68l-137 -110q-35 -27 -64 2l-112 116q-66 -35 -160 -54t-189 -19h-16q-262 0 -377.5 96t-115.5 266zM399 379q0 -57 45 -101.5t146 -44.5h6q35 0 75 6.5t64 14.5l-258 274q-35 -23 -56.5 -58.5t-21.5 -86.5v-4zM463 989q0 -39 26.5 -78 t85.5 -98l17 -16q43 29 78 77t35 103v8q0 47 -30 79t-85 32h-6q-55 0 -88 -27t-33 -76v-4z" />
<glyph unicode="'" horiz-adv-x="579" d="M115 1446q-2 20 10 31.5t31 11.5h268q45 0 41 -43l-31 -535q0 -37 -35 -37h-217q-16 0 -26.5 9.5t-10.5 27.5z" />
<glyph unicode="(" horiz-adv-x="710" d="M53 567q0 94 20.5 196.5t60.5 204t99.5 198t137.5 180.5q20 23 33.5 31.5t29.5 8.5q23 0 53 -16l105 -53q35 -16 44 -36.5t-20 -55.5q-55 -74 -97 -156t-69.5 -167t-42 -169t-14.5 -162q0 -80 10.5 -158.5t34 -158.5t63.5 -161t99 -165q23 -33 19.5 -51t-29.5 -33 l-127 -71q-23 -12 -41 -13q-27 0 -55 33q-147 166 -230.5 367.5t-83.5 406.5z" />
<glyph unicode=")" horiz-adv-x="710" d="M74.5 1280q9.5 20 44.5 37l104 53q31 16 53 16q16 0 30 -8.5t34 -31.5q78 -84 137.5 -180.5t99 -198t60 -203.5t20.5 -197q0 -205 -82.5 -406.5t-230.5 -367.5q-29 -33 -55 -33q-20 0 -41 13l-127 71q-27 14 -30 33t20 51q119 168 162.5 326t43.5 317q0 78 -14 162 t-42 169t-69.5 167t-97.5 156q-29 35 -19.5 55z" />
<glyph unicode="*" horiz-adv-x="864" d="M74.5 1196q-4.5 10 1.5 31l45 135q6 20 22.5 25.5t34.5 -5.5l168 -92l-33 217q-4 37 37 37h164q41 0 37 -37l-33 -217l168 92q18 10 34.5 5t22.5 -25l45 -135q6 -20 1 -30.5t-29 -18.5l-199 -50l154 -129q23 -18 20.5 -38.5t-20.5 -34.5l-125 -86q-41 -25 -62 12l-96 166 l-96 -166q-20 -37 -62 -12l-124 86q-18 14 -20.5 33.5t20.5 39.5l153 129l-199 50q-25 8 -29.5 18z" />
<glyph unicode="+" d="M100 461v127q0 43 15.5 62.5t56.5 19.5h264v272q0 49 20.5 61.5t63.5 12.5h148q43 0 63.5 -12.5t20.5 -61.5v-272h264q41 0 56 -19.5t15 -62.5v-127q0 -41 -15 -57.5t-56 -16.5h-264v-272q0 -49 -20.5 -61.5t-63.5 -12.5h-148q-43 0 -63.5 12.5t-20.5 61.5v272h-264 q-41 0 -56.5 16.5t-15.5 57.5z" />
<glyph unicode="," horiz-adv-x="507" d="M20.5 -288.5q-0.5 16.5 12.5 32.5q23 29 42 58.5t40 60.5q16 23 26.5 44t10.5 40q0 16 -17 26l-20 15q-29 23 -40.5 50.5t-11.5 78.5v57q0 78 39 121t131 43h29q94 0 133 -45t39 -137v-27q0 -43 -13 -101.5t-40 -124t-69 -135t-97 -133.5q-18 -23 -41 -22q-12 0 -33 10 l-102 62q-18 10 -18.5 26.5z" />
<glyph unicode="-" horiz-adv-x="745" d="M66 451v153q0 37 17 51.5t64 14.5h451q47 0 64.5 -14.5t17.5 -51.5v-153q0 -37 -15.5 -51.5t-66.5 -14.5h-451q-51 0 -66 14.5t-15 51.5z" />
<glyph unicode="." horiz-adv-x="497" d="M74 152v20q0 94 43 130t121 36h22q78 0 121 -36t43 -130v-20q0 -94 -43 -132t-121 -38h-22q-78 0 -121 37.5t-43 132.5z" />
<glyph unicode="/" horiz-adv-x="913" d="M59.5 -27.5q-8.5 19.5 3.5 54.5l449 1306q14 41 29.5 56.5t62.5 15.5h199q43 0 51 -19.5t-4 -54.5l-449 -1306q-14 -41 -29.5 -56.5t-62.5 -15.5h-198q-43 0 -51.5 19.5z" />
<glyph unicode="0" d="M68 573v179q0 135 26.5 244.5t88 186.5t162.5 118.5t249 41.5q150 0 250 -41.5t161.5 -118.5t88 -186.5t26.5 -244.5v-179q0 -135 -26.5 -244.5t-88 -186t-162 -118.5t-249.5 -42q-147 0 -248.5 42t-163 118.5t-88 186t-26.5 244.5zM418 608q0 -94 5 -159.5t23.5 -107.5 t52 -62.5t91.5 -20.5h8q57 0 91 19.5t52.5 61.5t23.5 108.5t5 160.5v109q0 94 -5 160.5t-23.5 108.5t-52.5 61.5t-91 19.5h-8q-57 0 -91 -20.5t-52.5 -62.5t-23.5 -107.5t-5 -159.5v-109z" />
<glyph unicode="1" d="M139 1055q-31 59 29 88l315 155q31 16 56.5 21.5t66.5 5.5h146q41 0 57 -13.5t16 -60.5v-987h185q41 0 56 -16.5t15 -57.5v-120q0 -41 -15 -55.5t-56 -14.5h-748q-41 0 -56.5 14.5t-15.5 55.5v120q0 41 15.5 57.5t56.5 16.5h217v721h-10l-176 -78q-29 -12 -50.5 -12 t-35.5 27z" />
<glyph unicode="2" d="M123 61v132q0 100 17.5 175.5t55 134t98 103.5t146.5 84l88 39q53 25 89 45.5t57.5 43t30 49t8.5 61.5v16q0 119 -146 119h-8q-59 0 -135 -14.5t-148 -38.5q-51 -16 -63 30l-37 148q-8 29 -2 47t35 29q86 35 196.5 57t223.5 22h6q199 0 311.5 -86t112.5 -272v-24 q0 -82 -17.5 -143.5t-51.5 -111t-86 -87t-120 -70.5l-106 -51q-74 -35 -119 -60.5t-69.5 -49t-34 -47.5t-9.5 -54v-23h537q47 0 58.5 -13t11.5 -54v-136q0 -33 -13.5 -47t-56.5 -14h-790q-41 0 -55.5 13.5t-14.5 47.5z" />
<glyph unicode="3" d="M102 100q0 10 4.5 27.5t10.5 38.5l28 96q10 33 24.5 40t47.5 -3q82 -27 162 -40t147 -13h6q100 0 146.5 31.5t46.5 113.5v25q0 78 -43 109.5t-154 31.5h-118q-41 0 -52.5 14.5t-11.5 51.5v129q0 37 11.5 51t52.5 14h61q131 0 176 39q37 33 37 100v15q0 57 -36 81.5 t-97 24.5h-14q-55 0 -124 -15t-159 -46q-31 -10 -47.5 0t-22.5 35l-30 127l-4.5 18t-2.5 21q0 16 9.5 28t33.5 23q37 16 85.5 29.5t100.5 23.5t106.5 16t101.5 6h18q207 0 314.5 -80.5t107.5 -254.5v-13q0 -98 -39 -178t-129 -106v-6q106 -27 157.5 -97.5t51.5 -195.5v-31 q0 -199 -127 -299t-381 -100h-10q-53 0 -108.5 5t-110 14t-101.5 20.5t-80 23.5q-45 14 -45 55z" />
<glyph unicode="4" d="M47 350v49q0 31 2 52.5t6 40t13.5 36t25.5 41.5l428 691q27 41 49.5 53t65.5 12h248q53 0 68.5 -16.5t15.5 -69.5v-698h106q39 0 48.5 -10.5t9.5 -45.5v-147q0 -35 -9.5 -45t-48.5 -10h-106v-215q0 -47 -15.5 -57.5t-58.5 -10.5h-180q-45 0 -61.5 10t-16.5 58v215h-522 q-41 0 -54.5 13t-13.5 54zM350 541h287v460h-2z" />
<glyph unicode="5" d="M102 102q0 10 3.5 23.5t7.5 32.5l28 104q12 47 33 54.5t49 -3.5q72 -27 157 -45t157 -18h20q90 0 143 37q25 16 37.5 47t12.5 88v14q0 45 -9.5 77t-33.5 54q-39 35 -117 35h-12q-49 0 -115 -13.5t-125 -35.5q-10 -4 -20.5 -7t-20.5 -3t-23.5 6t-23.5 12l-66 43 q-16 10 -23 21.5t-7 31.5v19.5t4 62.5l26 525q2 35 17.5 48t52.5 13h668q39 0 52 -10t13 -49v-158q0 -33 -14.5 -44t-54.5 -11h-414q-29 0 -29 -27l-6 -190q41 14 95 22t108 8h20q197 0 289 -90q53 -51 76.5 -128t23.5 -197v-29q0 -125 -34.5 -207t-96.5 -133 q-61 -51 -159.5 -75.5t-221.5 -24.5h-20q-45 0 -97.5 6t-106.5 15t-106.5 22.5t-95.5 29.5q-41 14 -41 47z" />
<glyph unicode="6" d="M98 600v53q0 367 154 535q70 78 170 116.5t223 38.5h14q82 0 171.5 -15t142.5 -38q43 -16 43 -49q0 -10 -2 -21.5t-6 -23.5l-33 -121q-10 -35 -25.5 -41t-50.5 4q-47 16 -100.5 25.5t-108.5 9.5h-14q-104 0 -167 -61.5t-71 -196.5q47 23 111.5 37t134.5 14h23 q195 0 293 -91t98 -308v-37q0 -223 -123 -335.5t-342 -112.5h-23q-152 0 -251 42t-157.5 121.5t-81 194.5t-22.5 260zM436 510q0 -82 10.5 -133t32 -81t54 -40t79.5 -10h6q82 0 117 38t35 117v43q0 80 -34 114t-115 34h-15q-49 0 -92 -8.5t-78 -22.5v-51z" />
<glyph unicode="7" d="M135 1122v133q0 41 11.5 55.5t54.5 14.5h815q41 0 57.5 -10t16.5 -47v-47q0 -25 -3.5 -46.5t-11.5 -46t-20.5 -55.5t-32.5 -74l-449 -944q-16 -33 -35.5 -44t-70.5 -11h-207q-47 0 -55 20.5t6 47.5l504 985h-514q-43 0 -54.5 14t-11.5 55z" />
<glyph unicode="8" d="M80 346v16q0 100 57.5 184.5t149.5 117.5q-92 57 -131 130.5t-39 172.5v18q0 164 127 261t352 97h22q213 0 331 -84.5t118 -238.5v-19q0 -90 -44 -167.5t-132 -122.5q111 -43 165 -127t54 -183v-14q0 -209 -130 -307t-362 -98h-20q-141 0 -239.5 26.5t-161 75.5 t-90 115.5t-27.5 146.5zM420 387q0 -76 51 -111.5t131 -35.5h8q86 0 124 28.5t38 83.5v10q0 57 -49 90t-141 68l-90 33q-72 -55 -72 -147v-19zM446 979q0 -51 34 -90t120 -72l62 -22q33 27 55 71.5t22 83.5v15q0 66 -42 98.5t-101 32.5h-10q-61 0 -100.5 -33t-39.5 -78v-6z " />
<glyph unicode="9" d="M90 887v28q0 195 117 311.5t352 116.5h29q141 0 237.5 -35.5t154.5 -109.5t84 -189.5t26 -275.5v-49q0 -225 -45.5 -363.5t-122 -214t-177 -100t-208.5 -24.5h-2q-90 0 -185.5 18t-154.5 41q-35 12 -35 47q0 12 6 37l33 131q8 29 23.5 37t45.5 -2q51 -16 122 -30.5 t136 -14.5h6q115 0 161 62.5t52 180.5q-49 -20 -108 -30t-113 -10h-22q-221 0 -316.5 110.5t-95.5 327.5zM418 893q0 -92 37 -130t110 -38h17q96 0 168 37v29q0 86 -8.5 142t-27 88t-49 44t-75.5 12h-19q-72 0 -112.5 -39t-40.5 -127v-18z" />
<glyph unicode=":" horiz-adv-x="497" d="M74 152v20q0 94 43 130t121 36h22q78 0 121 -36t43 -130v-20q0 -94 -43 -132t-121 -38h-22q-78 0 -121 37.5t-43 132.5zM74 932v20q0 94 43 130t121 36h22q78 0 121 -36t43 -130v-20q0 -94 -43 -132t-121 -38h-22q-78 0 -121 37.5t-43 132.5z" />
<glyph unicode=";" horiz-adv-x="507" d="M20.5 -288.5q-0.5 16.5 12.5 32.5q23 29 42 58.5t40 60.5q16 23 26.5 44t10.5 40q0 16 -17 26l-20 15q-29 23 -40.5 50.5t-11.5 78.5v57q0 78 39 121t131 43h29q94 0 133 -45t39 -137v-27q0 -43 -13 -101.5t-40 -124t-69 -135t-97 -133.5q-18 -23 -41 -22q-12 0 -33 10 l-102 62q-18 10 -18.5 26.5zM82 932v20q0 94 43 130t121 36h22q78 0 121 -36t43 -130v-20q0 -94 -43 -132t-121 -38h-22q-78 0 -121 37.5t-43 132.5z" />
<glyph unicode="&#x3c;" d="M106 494v77q0 37 10.5 52.5t39.5 31.5l747 404q25 12 41 12q29 0 49 -39l47 -84q25 -45 32 -60.5t7 -25.5q0 -33 -51 -59l-512 -271l512 -270q51 -27 51 -55q0 -12 -7 -29.5t-38 -71.5l-41 -73q-20 -39 -49 -39q-20 0 -41 12l-747 404q-29 16 -39.5 31.5t-10.5 52.5z" />
<glyph unicode="=" d="M100 209v127q0 43 15.5 62.5t56.5 19.5h844q41 0 56 -19.5t15 -62.5v-127q0 -41 -15 -57.5t-56 -16.5h-844q-41 0 -56.5 16.5t-15.5 57.5zM100 713v127q0 43 15.5 62.5t56.5 19.5h844q41 0 56 -19.5t15 -62.5v-127q0 -41 -15 -57.5t-56 -16.5h-844q-41 0 -56.5 16.5 t-15.5 57.5z" />
<glyph unicode="&#x3e;" d="M106 207q0 29 52 55l512 270l-512 271q-51 27 -52 59q0 10 7.5 25.5t31.5 60.5l48 84q20 39 49 39q16 0 41 -12l747 -404q29 -16 39 -31.5t10 -52.5v-77q0 -37 -10 -52.5t-39 -31.5l-747 -404q-23 -12 -41 -12q-29 0 -49 39l-41 73q-31 53 -38.5 71t-7.5 30z" />
<glyph unicode="?" horiz-adv-x="956" d="M49 1219q0 33 45 53q31 14 74 27.5t90 22.5t96 15t95 6h14q205 0 310.5 -80.5t105.5 -244.5v-14q0 -115 -40 -191t-145 -139l-61 -37q-78 -47 -107.5 -81t-29.5 -93v-29q0 -37 -47 -37h-162q-47 0 -47 45v47q0 59 5 100.5t20.5 72t45 56t76.5 54.5l51 31q70 41 90.5 73.5 t20.5 81.5v7q0 113 -135 112h-4q-55 0 -122 -16.5t-126 -38.5q-25 -10 -40.5 -3t-25.5 36l-39 121q-4 10 -6 21t-2 22zM195 141v17q0 94 51 129t123 35h6q35 0 66.5 -8.5t55 -27t38 -50t14.5 -78.5v-17q0 -45 -14.5 -75.5t-38 -49t-55 -26.5t-66.5 -8h-6q-35 0 -67 8 t-55.5 26.5t-37.5 49t-14 75.5z" />
<glyph unicode="@" horiz-adv-x="1671" d="M90 586q0 193 63.5 344t174 253.5t262 157t330.5 54.5q141 0 263 -40t213 -119t142 -194.5t51 -265.5q0 -121 -30.5 -213t-84 -156.5t-128 -97.5t-160.5 -33q-92 0 -156.5 28t-93.5 75h-6q-27 -33 -74 -52.5t-106 -19.5h-5q-123 0 -176 57.5t-53 161.5q0 49 7 115t18 119 q25 139 86 199.5t172 60.5q68 0 111.5 -20.5t72.5 -59.5l14 31q6 14 14.5 20t30.5 6h113q23 0 33 -7t6 -29l-45 -345q-4 -35 -6 -56t-2 -38q0 -35 13 -57.5t54 -22.5q66 0 106 79t40 227q0 217 -121 327.5t-332 110.5q-121 0 -219 -39t-169 -114t-109.5 -184.5t-38.5 -248.5 q0 -219 123.5 -338t381.5 -119q111 0 214.5 12.5t181.5 30.5q29 6 38 -1t13 -23l20 -101q4 -27 -2 -37t-32 -20q-86 -29 -208 -47t-245 -18q-373 0 -566.5 171t-193.5 476zM758 551q0 -31 11 -51.5t46 -20.5t53.5 21.5t28.5 87.5l16 121q4 35 6.5 50t2.5 27q0 53 -58 54 q-35 0 -54 -21.5t-30 -73.5q-8 -41 -15 -98t-7 -96z" />
<glyph unicode="A" horiz-adv-x="1251" d="M27 45q0 10 6 32.5t22 80.5q86 289 165 554t165 521q16 51 48 71.5t97 20.5h187q66 0 97.5 -20.5t47.5 -71.5q86 -256 167 -521.5t167 -553.5q16 -57 22.5 -80t6.5 -33q0 -27 -17.5 -36t-52.5 -9h-182q-31 0 -50.5 4t-30.5 11.5t-16.5 20.5t-11.5 32l-47 170h-387 l-47 -170q-6 -18 -11 -31.5t-16.5 -22t-31 -11.5t-50.5 -3h-178q-35 0 -52 9t-17 36zM489 504h269q-12 49 -27.5 106.5t-32 119t-33 122.5t-30.5 115h-23q-16 -53 -32.5 -114.5t-32 -123t-30.5 -119t-28 -106.5z" />
<glyph unicode="B" horiz-adv-x="1200" d="M131 104v1143q0 45 15.5 61.5t56.5 20.5q68 8 147.5 11t163.5 3h21q152 0 253 -22.5t162 -71.5q53 -43 80 -101t27 -138v-19q0 -100 -40 -168.5t-108 -103.5v-6q98 -25 157.5 -94.5t59.5 -192.5v-27q0 -178 -94 -272q-123 -127 -409 -127h-58q-115 0 -212 3t-140 11 q-47 8 -64.5 28.5t-17.5 61.5zM475 313q0 -33 31 -34q16 -2 35.5 -2.5t40.5 -0.5h28q53 0 83 8.5t48 24.5q20 16 27.5 43t7.5 62v16q0 72 -37 100q-20 16 -55 23.5t-90 7.5h-119v-248zM475 805h92q78 0 111 26q33 27 33 97v24q0 59 -25 84q-29 31 -104 31h-19h-38.5 t-30.5 -2q-18 -4 -19 -23v-237z" />
<glyph unicode="C" horiz-adv-x="1148" d="M94 537v251q0 150 28 250.5t91 165.5q66 72 170 105.5t256 33.5h14q100 0 210 -23.5t192 -66.5q23 -12 29 -28.5t-5 -44.5l-67 -172q-12 -31 -35 -31q-18 0 -39 10q-66 33 -132.5 51.5t-137.5 18.5h-9q-131 0 -176 -74q-23 -39 -31 -97.5t-8 -146.5v-145 q0 -94 11.5 -157.5t46.5 -104.5q29 -31 75 -47.5t117 -16.5h8q55 0 128 17.5t126 40.5q8 4 15.5 6t17.5 2q12 0 21.5 -9.5t15.5 -33.5l35 -148q6 -25 6 -39q0 -18 -12.5 -30.5t-48.5 -26.5q-72 -29 -165 -47t-184 -18h-20q-297 0 -426 137q-63 66 -90 167t-27 251z" />
<glyph unicode="D" horiz-adv-x="1304" d="M131 88v1118q0 61 19.5 82t72.5 27q61 6 142 10t180 4h28q180 0 302 -33.5t200 -109.5q72 -72 108 -183.5t36 -273.5v-119q0 -172 -35 -280.5t-111 -179.5q-80 -78 -210 -114t-328 -36h-43q-78 0 -160 2t-123 6q-78 8 -78 80zM475 330q0 -27 8.5 -35t24.5 -10 q20 -2 48 -2h44h10q121 0 181 59q77 77 77 280v5v84q0 209 -69 280q-33 35 -80 46.5t-111 11.5h-10q-18 0 -45 -1.5t-43 -3.5q-35 -2 -35 -34v-680z" />
<glyph unicode="E" horiz-adv-x="1079" d="M106 610v125q0 66 2.5 155t10.5 175q6 80 26.5 130t57.5 79t94 40t139 11h490q49 0 58 -11t9 -52v-154q0 -37 -9 -49t-58 -12h-434q-29 0 -39.5 -9.5t-12.5 -31.5q-2 -31 -3 -90.5t-3 -112.5h432q39 0 52.5 -11.5t13.5 -48.5v-143q0 -33 -13.5 -44t-52.5 -11h-436 q0 -63 3 -123t9 -107q4 -23 14.5 -29.5t30.5 -6.5h437q47 0 58 -11.5t11 -52.5v-152q0 -37 -11 -50t-58 -13h-494q-76 0 -131 9t-93 34t-58.5 66t-26.5 106q-10 94 -12.5 193.5t-2.5 201.5z" />
<glyph unicode="F" horiz-adv-x="1050" d="M127 66v1019q0 37 2 64t8 47.5t17.5 37.5t29.5 38q49 53 187 53h561q47 0 58 -15.5t11 -49.5v-148q0 -39 -14 -52t-55 -13h-430q-31 0 -31 -31v-223h408q41 0 52 -11.5t11 -52.5v-133q0 -37 -11 -49t-52 -12h-408v-469q0 -43 -14.5 -54.5t-61.5 -11.5h-192 q-47 0 -61.5 11.5t-14.5 54.5z" />
<glyph unicode="G" horiz-adv-x="1273" d="M94 545v235q0 152 33 256.5t98 171.5q68 70 172.5 102.5t264.5 32.5h18q57 0 118.5 -8t119 -21.5t107.5 -30.5t87 -38q20 -10 27.5 -20.5t7.5 -22.5q0 -10 -3 -23.5t-14 -37.5l-53 -140q-13 -34 -38 -34q-12 0 -27 8q-78 41 -154 61.5t-164 20.5h-10q-139 0 -192 -76 q-27 -39 -37.5 -96.5t-10.5 -149.5v-155q0 -104 12.5 -165t43.5 -96q25 -29 64.5 -40t103.5 -11h12q29 0 56.5 2t43.5 6q23 4 31 15.5t8 36.5v188h-106q-37 0 -49.5 10.5t-12.5 44.5v138q0 39 12.5 51t47.5 12h389q37 0 50 -16.5t13 -50.5v-551q0 -39 -13 -60.5t-48 -36.5 q-43 -18 -96.5 -31.5t-108.5 -23.5t-109.5 -15t-101.5 -5h-27q-166 0 -272 28.5t-172 93.5q-70 68 -95.5 174.5t-25.5 266.5z" />
<glyph unicode="H" horiz-adv-x="1345" d="M131 63v1199q0 43 12.5 53t59.5 10h200q47 0 59.5 -10t12.5 -53v-457h395v457q0 43 12.5 53t59.5 10h201q47 0 59 -10t12 -53v-1199q0 -43 -12 -53t-59 -10h-199q-47 0 -60.5 10t-13.5 53v463h-395v-463q0 -43 -12 -53t-60 -10h-202q-47 0 -58.5 10t-11.5 53z" />
<glyph unicode="I" horiz-adv-x="614" d="M135 63v1199q0 43 12.5 53t59.5 10h201q47 0 59 -10t12 -53v-1199q0 -43 -12 -53t-59 -10h-201q-47 0 -59.5 10t-12.5 53z" />
<glyph unicode="J" horiz-adv-x="768" d="M29 70v139q0 47 15 60.5t56 13.5h99q29 0 52 2t38 16q14 14 17 43t3 102v816q0 43 13.5 53t60.5 10h201q47 0 58 -10t11 -53v-826q0 -80 -4 -140t-14 -104.5t-30.5 -76t-53.5 -54.5q-59 -41 -138 -51t-184 -10h-133q-37 0 -52 14.5t-15 55.5z" />
<glyph unicode="K" horiz-adv-x="1218" d="M131 63v1199q0 43 12.5 53t59.5 10h200q47 0 59.5 -10t12.5 -53v-447h10q14 0 20.5 7t16.5 24l279 428q18 29 39.5 40t62.5 11h225q69 0 69 -35q0 -17 -17 -43l-373 -516q-12 -16 -19.5 -25.5t-7.5 -19.5t8.5 -20.5t18.5 -26.5l383 -576q11 -18 11 -32q0 -31 -66 -31 h-217q-53 0 -75 9t-38 38l-277 459q-12 23 -20 29t-21 6h-12v-478q0 -43 -12 -53t-60 -10h-200q-47 0 -59.5 10t-12.5 53z" />
<glyph unicode="L" horiz-adv-x="972" d="M127 221v1041q0 43 12.5 53t59.5 10h202q47 0 58.5 -10t11.5 -53v-936q0 -23 10.5 -35t38.5 -12h328q49 0 60.5 -12.5t11.5 -53.5v-147q0 -39 -11.5 -52.5t-60.5 -13.5h-496q-127 0 -176 50t-49 171z" />
<glyph unicode="M" horiz-adv-x="1660" d="M113 70q0 137 5 284.5t11 297t13.5 294t15.5 273.5q4 66 25.5 86t76.5 20h197q72 0 104.5 -20.5t48.5 -77.5q33 -113 59.5 -209t51 -183t48.5 -168t48 -163h14q27 82 51.5 164t51.5 169t54.5 183t60.5 207q16 57 48.5 77.5t104.5 20.5h199q55 0 76.5 -20.5t25.5 -85.5 q8 -129 15.5 -273.5t13.5 -294t11 -297t5 -284.5q0 -41 -15 -55.5t-67 -14.5h-168q-51 0 -65 13.5t-14 56.5v169t-1 189t-3.5 199.5t-4.5 199.5h-8q-57 -182 -107.5 -336.5t-90.5 -277.5q-16 -47 -41 -59.5t-74 -12.5h-125q-49 0 -73.5 12.5t-41.5 59.5q-39 113 -91 274.5 t-101 339.5h-8q-2 -98 -4.5 -199.5t-3.5 -199.5t-1 -189.5v-168.5q0 -43 -14 -56.5t-66 -13.5h-165q-51 0 -66.5 14.5t-15.5 55.5z" />
<glyph unicode="N" horiz-adv-x="1329" d="M131 70v1187q0 41 16.5 54.5t55.5 13.5h90q49 0 77.5 -11t53.5 -46l450 -672h5v659q0 43 17 56.5t58 13.5h172q41 0 56.5 -13.5t15.5 -56.5v-1187q0 -41 -16.5 -54.5t-55.5 -13.5h-90q-49 0 -77.5 11.5t-53.5 45.5l-450 633h-4v-620q0 -43 -15.5 -56.5t-56.5 -13.5h-172 q-41 0 -58.5 13.5t-17.5 56.5z" />
<glyph unicode="O" horiz-adv-x="1302" d="M94 584v157q0 178 38 304t134 202q59 47 151.5 71.5t225.5 24.5h16q133 0 225.5 -24.5t151.5 -71.5q96 -76 134 -201.5t38 -304.5v-157q0 -178 -37.5 -304t-134.5 -202q-59 -47 -151 -71.5t-226 -24.5h-16q-133 0 -225 24.5t-152 71.5q-96 76 -134 202t-38 304zM444 625 q0 -115 10.5 -193t47.5 -115q25 -25 56.5 -37t78.5 -12h29q47 0 78.5 12.5t56.5 36.5q37 37 47 115t10 193v75q0 115 -10 193t-47 115q-25 25 -56.5 37t-78.5 12h-29q-47 0 -79 -12.5t-56 -36.5q-37 -37 -47.5 -115t-10.5 -193v-75z" />
<glyph unicode="P" horiz-adv-x="1161" d="M131 63v1178q0 51 18.5 69.5t71.5 22.5q66 6 153 8t193 2h25q154 0 261.5 -27.5t170.5 -92.5q47 -47 69.5 -118t22.5 -179v-70q0 -229 -129 -324q-72 -53 -173 -75.5t-241 -22.5h-98v-371q0 -37 -18.5 -50t-53.5 -13h-200q-37 0 -54.5 13.5t-17.5 49.5zM475 715h107 q59 0 95 9t56 26q37 31 37 120v52q0 76 -35 108q-37 37 -127 37h-26q-23 0 -47.5 -1t-42.5 -3q-16 -2 -17 -25v-323z" />
<glyph unicode="Q" horiz-adv-x="1302" d="M94 584v157q0 178 38 304t134 202q59 47 150.5 71.5t224.5 24.5h21q133 0 224 -24.5t150 -71.5q96 -76 134 -201.5t38 -304.5v-157q0 -178 -37.5 -304t-134.5 -202q-72 -55 -188 -80q57 -53 124.5 -104.5t121.5 -88.5q35 -25 34 -47q0 -10 -6 -18t-22 -25l-125 -112 q-23 -20 -47 -21q-25 0 -49 19q-45 33 -92.5 78t-90.5 97t-82 106.5t-67 103.5q-88 8 -162 30.5t-121 61.5q-96 76 -133 202t-37 304zM444 625q0 -115 10.5 -193t47.5 -115q25 -25 56.5 -37t78.5 -12h29q47 0 78.5 12.5t56.5 36.5q37 37 47 115t10 193v75q0 115 -10 193 t-47 115q-25 25 -56.5 37t-78.5 12h-29q-47 0 -79 -12.5t-56 -36.5q-37 -37 -47.5 -115t-10.5 -193v-75z" />
<glyph unicode="R" horiz-adv-x="1210" d="M131 63v1184q0 43 16.5 60.5t53.5 21.5q57 6 145 10t195 4h43q160 0 266 -27.5t172 -90.5q47 -47 72.5 -114t25.5 -165v-43q0 -104 -20.5 -176t-67.5 -125q-59 -66 -155 -96l260 -438q16 -29 6 -48.5t-56 -19.5h-200q-53 0 -73.5 11.5t-37.5 45.5l-205 394q-6 12 -13 17 t-23 5h-60v-410q0 -43 -12 -53t-60 -10h-202q-47 0 -58.5 10t-11.5 53zM475 745h133q53 0 83 7.5t48 25.5q35 35 35 121v27q0 37 -7 61.5t-24 40.5q-35 37 -139 37h-39h-30.5t-26.5 -2q-16 -2 -24.5 -9t-8.5 -28v-281z" />
<glyph unicode="S" horiz-adv-x="1110" d="M86 913v58q0 172 92 262q59 57 156.5 83.5t251.5 26.5h14q92 0 189.5 -15t158.5 -38q33 -12 41 -31.5t-2 -60.5l-35 -149q-9 -40 -45 -40q-13 0 -30 5q-70 23 -135.5 33t-135.5 10h-8q-102 0 -140 -23.5t-38 -79.5v-18q0 -49 27.5 -69.5t105.5 -37.5l156 -30 q88 -18 150.5 -43t100 -66t55 -103.5t17.5 -156.5v-49q0 -203 -112.5 -301t-372.5 -98h-25q-43 0 -94 4t-102.5 12t-97.5 18.5t-81 22.5q-41 14 -51 33.5t-6 48.5l27 166q6 47 42 47q13 0 29 -6q88 -31 175.5 -47.5t152.5 -16.5h10q96 0 134 26.5t38 90.5v20q0 63 -35.5 86 t-125.5 39l-140 27q-90 16 -149.5 47t-95 76t-51 104.5t-15.5 132.5z" />
<glyph unicode="T" horiz-adv-x="1071" d="M29 1108v154q0 41 12 52t53 11h883q41 0 53 -11t12 -52v-154q0 -37 -12 -49t-53 -12h-268v-981q0 -39 -14.5 -52.5t-61.5 -13.5h-195q-47 0 -61.5 13.5t-14.5 52.5v981h-268q-41 0 -53 12t-12 49z" />
<glyph unicode="U" horiz-adv-x="1290" d="M111 463v799q0 43 11 53t60 10h201q49 0 61.5 -10t12.5 -53v-789q0 -61 9 -101t32 -63q41 -41 143 -41h10q102 0 144 41q23 23 32 63t9 101v789q0 43 11 53t60 10h201q49 0 60.5 -10t11.5 -53v-799q0 -135 -28 -219t-97 -150q-124 -112 -393 -112h-5h-24 q-143 0 -240.5 28.5t-159.5 83.5q-70 66 -96 156t-26 213z" />
<glyph unicode="V" horiz-adv-x="1183" d="M20 1272q-6 31 7.5 42t60.5 11h197q45 0 59 -8t25 -49q23 -111 50 -232t57 -238.5t58.5 -224t53.5 -188.5h10q25 82 53.5 188.5t58 224t57.5 238.5t50 232q10 41 24.5 49t59.5 8h195q47 0 60 -11t7 -42q-29 -141 -68.5 -302t-85.5 -321t-96.5 -309.5t-99.5 -267.5 q-14 -35 -38.5 -53.5t-82.5 -18.5h-200q-57 0 -82 18.5t-39 53.5q-49 119 -99.5 268t-96.5 309t-87 320.5t-68 302.5z" />
<glyph unicode="W" horiz-adv-x="1703" d="M18 1272q-4 29 9.5 41t54.5 12h203q37 0 56 -9t24 -44q41 -276 74.5 -495.5t62.5 -407.5h8q20 102 44 212.5t46.5 218t43 208t38.5 184.5q6 29 25.5 41t60.5 12h168q41 0 60.5 -12t25.5 -41q18 -84 38.5 -184.5t43 -208t45 -218t45.5 -212.5h8q29 188 61.5 407t75.5 496 q4 35 23.5 44t56.5 9h203q41 0 54.5 -12.5t9.5 -40.5q-53 -301 -119 -612.5t-137 -596.5q-10 -35 -33 -49t-80 -14h-184q-59 0 -83 16.5t-34 51.5q-37 141 -79 316t-79 359h-12q-37 -184 -79 -359t-79 -316q-10 -35 -33.5 -51.5t-83.5 -16.5h-184q-57 0 -79.5 14.5 t-33.5 48.5q-72 285 -138 596.5t-118 612.5z" />
<glyph unicode="X" horiz-adv-x="1214" d="M38 23.5q-5 23.5 11 52.5l277 547q8 14 13 27.5t5 23.5q0 16 -14 45l-281 551q-12 23 0 39t53 16h207q43 0 63.5 -10t30.5 -35l193 -455h12l203 455q10 25 29.5 35t64.5 10h207q45 0 55.5 -15.5t-2.5 -39.5l-280 -551q-14 -29 -15 -45q0 -10 5.5 -22.5t13.5 -28.5 l276 -547q16 -29 11 -52.5t-52 -23.5h-204q-47 0 -70 11.5t-39 49.5l-203 453h-12l-193 -453q-16 -37 -38.5 -49t-69.5 -12h-205q-47 0 -52 23.5z" />
<glyph unicode="Y" horiz-adv-x="1093" d="M23 1257q-4 17 -4 29q0 39 42 39h215q45 0 61.5 -5t27.5 -44q16 -66 38.5 -142.5t47 -154.5t49 -151.5t47.5 -133.5h10q23 59 47.5 133t48 152t45 154.5t37.5 142.5q10 39 26.5 44t61.5 5h209q43 0 43 -39q0 -13 -4 -29q-31 -109 -72 -224t-88 -229t-96 -219.5 t-94 -191.5v-330q0 -37 -13.5 -50t-52.5 -13h-215q-39 0 -52 13.5t-13 49.5v328q-45 86 -94.5 191.5t-97.5 220.5t-89 230.5t-71 223.5z" />
<glyph unicode="Z" horiz-adv-x="1077" d="M53 70v61q0 35 6.5 54.5t26.5 52.5l487 809h-423q-41 0 -56.5 13t-15.5 50v150q0 41 15.5 53t56.5 12h794q37 0 51.5 -11t14.5 -48v-70q0 -33 -8.5 -56.5t-32.5 -64.5l-500 -796h475q41 0 56.5 -13.5t15.5 -52.5v-147q0 -41 -15.5 -53.5t-56.5 -12.5h-813 q-47 0 -62.5 14.5t-15.5 55.5z" />
<glyph unicode="[" horiz-adv-x="741" d="M133 -121v1452q0 35 13.5 45t48.5 10h413q37 0 49.5 -10t12.5 -45v-166q0 -31 -11.5 -44t-48.5 -13h-157v-1006h157q37 0 48.5 -13t11.5 -44v-166q0 -35 -12.5 -45t-49.5 -10h-413q-35 0 -48.5 10t-13.5 45z" />
<glyph unicode="\" horiz-adv-x="913" d="M59 1385.5q-8 -19.5 4 -54.5l449 -1306q14 -41 29.5 -56.5t62.5 -15.5h198q43 0 51.5 19.5t-3.5 54.5l-449 1306q-14 41 -29.5 56.5t-62.5 15.5h-199q-43 0 -51 -19.5z" />
<glyph unicode="]" horiz-adv-x="741" d="M72 45q0 31 11 44t48 13h158v1006h-158q-37 0 -48 13.5t-11 43.5v166q0 35 12 45t49 10h414q35 0 48 -10t13 -45v-1452q0 -35 -13 -45t-48 -10h-414q-37 0 -49 10t-12 45v166z" />
<glyph unicode="^" d="M119 674q0 12 8 31.5t41 64.5l266 367q14 18 24.5 29.5t22 17.5t24.5 8t34 2h110q20 0 33.5 -2t25 -8t21.5 -17.5t25 -29.5l266 -367q33 -45 41 -64.5t8 -31.5q0 -25 -33 -47l-112 -76q-27 -16 -43 -16q-25 0 -45 28l-234 299h-16l-234 -299q-10 -12 -20 -20t-25 -8 q-16 0 -43 16l-112 76q-33 23 -33 47z" />
<glyph unicode="_" horiz-adv-x="1157" d="M66 -143q0 37 17 51t64 14h863q47 0 64.5 -14t17.5 -51v-121q0 -37 -15.5 -51.5t-66.5 -14.5h-863q-51 0 -66 14.5t-15 51.5v121z" />
<glyph unicode="`" horiz-adv-x="1288" d="M158 1378q0 18 12 35l98 127q18 27 41 27q18 0 39 -19l322 -274q18 -12 18 -33q0 -18 -22 -47l-62 -72q-16 -16 -24.5 -22t-24.5 -6q-20 0 -41 14l-324 221q-16 12 -24 22.5t-8 26.5z" />
<glyph unicode="a" horiz-adv-x="1040" d="M61 297v22q0 172 98.5 238t305.5 66h14q41 0 81 -2.5t63 -6.5v43q0 31 -6.5 52.5t-28.5 38.5q-18 12 -45 17t-78 5q-53 0 -115.5 -9t-122.5 -26q-39 -10 -52 1.5t-17 37.5l-17 109q-2 10 -3 22.5t-1 18.5q0 20 9.5 31.5t39.5 21.5q66 18 156 31.5t195 13.5 q123 0 204.5 -24.5t130.5 -75.5q78 -80 78 -256v-609q0 -31 -14 -44t-55 -13h-97q-35 0 -50 9t-25 38l-13 37h-4q-31 -47 -100.5 -74.5t-167.5 -27.5h-31q-154 0 -243 79.5t-89 235.5zM369 309q0 -57 30.5 -75.5t83.5 -18.5h15q35 0 58.5 9t37.5 22q16 16 22.5 37.5 t6.5 62.5v57q-16 4 -46 5.5t-53 1.5h-22q-72 0 -102.5 -17.5t-30.5 -75.5v-8z" />
<glyph unicode="b" horiz-adv-x="1097" d="M104 373v954q0 29 13.5 44t54.5 15h190q41 0 55.5 -15t14.5 -44v-360h4q90 55 234 55h18q86 0 156.5 -28.5t111.5 -82.5q35 -47 52.5 -122.5t17.5 -215.5v-147q0 -115 -24.5 -203t-79.5 -139q-59 -55 -141.5 -78.5t-209.5 -23.5h-41q-143 0 -222 26.5t-122 73.5 q-33 35 -57.5 100.5t-24.5 190.5zM432 362q0 -70 29 -98q16 -16 38.5 -21t53.5 -5h18q41 0 63.5 10t37.5 31q16 27 20 66.5t4 111.5v88q0 72 -3 118t-23 68q-16 18 -39 25.5t-64 7.5h-8q-70 0 -100 -31q-16 -16 -21.5 -41.5t-5.5 -66.5v-263z" />
<glyph unicode="c" horiz-adv-x="929" d="M72 434v119q0 76 6 131t19.5 98t33 74t45.5 57q53 53 134 81t204 28h21q82 0 169 -18.5t146 -49.5q39 -20 39 -51q0 -12 -4 -26.5t-13 -34.5l-38 -99q-8 -18 -17.5 -27t-25.5 -9q-12 0 -37 10q-45 18 -90.5 29.5t-92.5 11.5h-8q-78 0 -117 -39q-31 -31 -38 -75t-7 -114 v-69q0 -70 9.5 -108t31.5 -64q39 -43 133 -43h17q41 0 90 8t88 18q8 2 18.5 4.5t20.5 2.5q31 0 37 -35l18 -105q4 -16 6 -30.5t2 -24.5q0 -20 -10 -32.5t-43 -22.5q-61 -23 -137 -35t-158 -12h-24q-221 0 -324 102q-59 59 -81.5 144t-22.5 206z" />
<glyph unicode="d" horiz-adv-x="1083" d="M72 420v164q0 127 21.5 207.5t80.5 136.5q43 41 113.5 67.5t158.5 26.5h25q59 0 107.5 -10.5t83.5 -26.5h4v342q0 29 14 44t55 15h191q41 0 54 -15t13 -44v-932q0 -121 -20.5 -188.5t-59.5 -110.5q-55 -59 -138 -86.5t-232 -27.5h-35q-131 0 -205 23.5t-125 68.5 q-57 49 -81.5 136t-24.5 210zM401 446q0 -78 5.5 -117.5t25.5 -62.5q24 -28 89 -28h5h23q59 0 88 26q29 29 29 98v261q0 47 -6.5 69.5t-18.5 38.5q-27 33 -96 33h-21q-66 0 -94 -35q-20 -23 -24.5 -64.5t-4.5 -127.5v-91z" />
<glyph unicode="e" horiz-adv-x="1021" d="M72 438v115q0 154 34.5 242t88.5 135q104 92 321 92h29q111 0 189.5 -27.5t119.5 -74.5q53 -59 75.5 -146.5t22.5 -220.5v-107q0 -33 -14 -45t-45 -12h-492v-16q0 -72 41 -99.5t150 -27.5h22q49 0 114 8t103 18q8 2 18.5 4.5t20.5 2.5q31 0 37 -35l17 -103q4 -16 6 -30.5 t2 -24.5q0 -20 -10.5 -32.5t-42.5 -22.5q-61 -23 -147.5 -36t-162.5 -13h-39q-123 0 -213 25.5t-143 78.5q-61 61 -81.5 144t-20.5 208zM401 602h226q25 0 24 27v33q0 61 -20 94q-25 37 -94 37h-19q-59 0 -86 -29q-31 -35 -31 -111v-51z" />
<glyph unicode="f" horiz-adv-x="733" d="M4 799v100q0 39 9.5 51.5t43.5 22.5l109 26v88q0 86 18.5 145.5t63.5 98.5q88 74 270 74h21q59 0 117.5 -6t95.5 -17q35 -10 42 -26.5t3 -46.5l-13 -129q-4 -29 -14 -37t-45 -4q-29 4 -60.5 6t-54.5 2h-12q-37 0 -55.5 -4t-28.5 -15q-20 -20 -20 -71v-53h170 q29 0 41 -10.5t12 -49.5v-137q0 -39 -12.5 -49t-40.5 -10h-170v-689q0 -29 -14.5 -44t-55.5 -15h-188q-41 0 -55.5 15.5t-14.5 43.5v689h-111q-31 0 -41 9t-10 42z" />
<glyph unicode="g" horiz-adv-x="1069" d="M68 489v138q0 207 106.5 301t321.5 94h28q20 109 102.5 166t225.5 57h100q29 0 40.5 -10t11.5 -43v-113q0 -31 -13.5 -40t-40.5 -9h-96q-37 0 -57.5 -7t-36.5 -24q121 -33 176 -123t55 -249v-138q0 -113 -27.5 -184.5t-93.5 -114.5q78 -41 125 -108.5t47 -147.5v-14 q0 -141 -111.5 -222t-369.5 -81h-39q-49 0 -103 6t-106.5 15.5t-102.5 22.5t-89 30q-44 16 -44 56q0 10 3 22l37 131q8 29 23.5 40t49.5 1q39 -12 83 -23.5t87 -20t82 -12.5t70 -4h29q96 0 137 28t41 77v6q0 59 -62 106q-18 -2 -44.5 -3t-55.5 -1h-55q-215 0 -324.5 94.5 t-109.5 300.5zM391 518q0 -61 8.5 -91t26.5 -48q29 -29 96 -29h15q35 0 59.5 8.5t36.5 20.5q18 18 26.5 48t8.5 91v80q0 61 -8.5 91t-26.5 48q-12 12 -37 20.5t-59 8.5h-15q-68 0 -96 -29q-18 -18 -26.5 -48t-8.5 -91v-80z" />
<glyph unicode="h" horiz-adv-x="1087" d="M104 59v1268q0 29 14.5 44t55.5 15h188q41 0 55.5 -15t14.5 -44v-360h4q90 55 219 55h7q180 0 260 -84q41 -43 58 -104.5t17 -165.5v-609q0 -29 -14 -44t-55 -15h-189q-41 0 -55 15.5t-14 43.5v580q0 70 -23 94q-25 27 -90 27h-12q-66 0 -90 -27q-23 -25 -23 -94v-580 q0 -29 -14.5 -44t-55.5 -15h-188q-41 0 -55.5 15.5t-14.5 43.5z" />
<glyph unicode="i" horiz-adv-x="544" d="M94 1251v11q0 82 38 126t128 44h25q90 0 128 -44.5t38 -125.5v-11q0 -82 -38 -126t-128 -44h-25q-90 0 -128 44t-38 126zM109 59q0 -29 14 -44t55 -15h189q41 0 55 15.5t14 43.5v885q0 29 -14 44.5t-55 15.5h-189q-41 0 -55 -15.5t-14 -44.5v-885z" />
<glyph unicode="j" horiz-adv-x="544" d="M-109 -156v-143q0 -39 13.5 -52.5t44.5 -13.5h119q104 0 173.5 20.5t114.5 72.5q45 51 62.5 121.5t17.5 187.5v907q0 29 -14 44.5t-55 15.5h-189q-41 0 -55 -15.5t-14 -44.5v-885q0 -49 -4.5 -78.5t-20.5 -48.5q-16 -16 -32.5 -22t-51.5 -6h-51q-31 0 -44.5 -12.5 t-13.5 -47.5zM94 1251v11q0 82 38 126t128 44h25q90 0 128 -44.5t38 -125.5v-11q0 -82 -38 -126t-128 -44h-25q-90 0 -128 44t-38 126z" />
<glyph unicode="k" horiz-adv-x="1011" d="M104 59v1268q0 29 14.5 44t55.5 15h188q41 0 55.5 -15t14.5 -44v-668h14l195 297q16 29 33.5 38.5t52.5 9.5h238q35 0 34 -25q0 -12 -16 -39l-235 -352q-27 -41 -27 -62q0 -14 6 -26.5t21 -34.5l251 -397q6 -10 9.5 -17.5t3.5 -17.5q0 -33 -43 -33h-228q-43 0 -62.5 10 t-37.5 43l-195 344h-14v-338q0 -29 -14.5 -44t-55.5 -15h-188q-41 0 -55.5 15.5t-14.5 43.5z" />
<glyph unicode="l" horiz-adv-x="626" d="M104 266v1061q0 29 14.5 44t55.5 15h188q41 0 55.5 -15t14.5 -44v-1022q0 -23 8.5 -31t30.5 -8h66q31 0 45 -14t14 -55v-127q0 -41 -14.5 -55.5t-44.5 -14.5h-156q-154 0 -215 59q-35 33 -48.5 81t-13.5 126z" />
<glyph unicode="m" horiz-adv-x="1636" d="M104 59v887q0 31 14.5 44.5t55.5 13.5h96q35 0 50.5 -9.5t25.5 -38.5l12 -38h7q98 104 280 104h8q90 0 153.5 -20.5t117.5 -73.5q49 47 120.5 70.5t157.5 23.5h8q180 0 260 -84q41 -43 58.5 -104.5t17.5 -165.5v-609q0 -29 -14 -44t-55 -15h-189q-41 0 -55 15.5t-14 43.5 v580q0 70 -23 94q-25 27 -90 27h-4q-66 0 -90 -27q-23 -25 -23 -94v-580q0 -29 -14 -44t-55 -15h-189q-41 0 -55 15.5t-14 43.5v580q0 70 -23 94q-25 27 -90 27h-4q-66 0 -90 -27q-23 -25 -23 -94v-580q0 -29 -14.5 -44t-55.5 -15h-188q-41 0 -55.5 15.5t-14.5 43.5z" />
<glyph unicode="n" horiz-adv-x="1087" d="M104 59v887q0 31 14.5 44.5t55.5 13.5h96q35 0 50.5 -9.5t25.5 -38.5l12 -38h7q98 104 280 104h17q180 0 260 -84q41 -43 58 -104.5t17 -165.5v-609q0 -29 -14 -44t-55 -15h-189q-41 0 -55 15.5t-14 43.5v580q0 70 -23 94q-25 27 -90 27h-12q-66 0 -90 -27 q-23 -25 -23 -94v-580q0 -29 -14.5 -44t-55.5 -15h-188q-41 0 -55.5 15.5t-14.5 43.5z" />
<glyph unicode="o" horiz-adv-x="1056" d="M72 436v131q0 78 8 137.5t24.5 103.5t40 77t56.5 57q45 35 125 57.5t190 22.5h25q111 0 190.5 -22.5t124.5 -57.5q33 -25 56.5 -57.5t40 -76.5t24.5 -102.5t8 -136.5v-133q0 -78 -8 -137t-24.5 -103t-40 -77t-56.5 -58q-45 -35 -125 -57t-190 -22h-25q-111 0 -190.5 22.5 t-124.5 56.5q-33 25 -56.5 58t-40 77t-24.5 103t-8 137zM401 422q0 -68 7.5 -100.5t29.5 -55.5q28 -28 82 -28h4h8q57 0 86 28q23 23 30 55.5t7 100.5v160q0 68 -7 100.5t-30 54.5q-29 29 -86 29h-8q-57 0 -86 -29q-23 -23 -30 -55.5t-7 -99.5v-160z" />
<glyph unicode="p" horiz-adv-x="1097" d="M104 -291v1229q0 37 16.5 51.5t55.5 14.5h86q39 0 53.5 -8.5t22.5 -34.5l14 -46h4q59 59 130 83t176 24h18q96 0 165.5 -29.5t106.5 -77.5q37 -47 55.5 -120.5t18.5 -214.5v-154q0 -123 -18.5 -206t-75.5 -138t-126 -77.5t-161 -22.5h-22q-53 0 -104.5 11t-86.5 32v-316 q0 -45 -16.5 -58t-69.5 -13h-156q-51 0 -68.5 13t-17.5 58zM432 362q0 -70 29 -98q28 -26 88 -26h4h18q41 0 63.5 10t37.5 31q16 27 20 66.5t4 111.5v88q0 72 -3 114.5t-21 67.5q-14 18 -38 27.5t-65 9.5h-14q-37 0 -58.5 -7t-37.5 -24q-16 -16 -21.5 -38.5t-5.5 -69.5v-263 z" />
<glyph unicode="q" horiz-adv-x="1099" d="M72 420v172q0 63 5 113.5t16 90.5t29.5 72.5t47.5 61.5q43 45 115.5 68.5t160.5 23.5h35q88 0 157 -24.5t110 -73.5h4l12 41q8 23 21.5 31t43.5 8h101q66 0 65 -64v-1231q0 -18 1 -31.5t-2 -22.5t-14 -13t-34 -4h-192q-51 0 -68.5 13t-17.5 58v326q-35 -25 -87.5 -39 t-109.5 -14h-43q-82 0 -145.5 23.5t-106.5 64.5q-53 51 -78.5 139t-25.5 211zM403 440q0 -76 5.5 -111.5t23.5 -58.5q26 -32 92 -32h4h23q53 0 88 30q29 29 29 99v256q0 47 -5.5 70.5t-17.5 39.5q-25 31 -92 31h-27q-35 0 -56 -7t-38 -26q-20 -23 -24.5 -65.5t-4.5 -112.5 v-113z" />
<glyph unicode="r" horiz-adv-x="755" d="M104 59v887q0 31 14.5 44.5t55.5 13.5h98q35 0 50.5 -9.5t23.5 -38.5l16 -63h7q37 59 90 85t139 26h82q33 0 46 -11.5t13 -46.5v-149q0 -35 -12 -46.5t-43 -11.5h-61q-113 0 -156 -43q-20 -20 -27.5 -55t-7.5 -86v-496q0 -29 -14.5 -44t-55.5 -15h-188q-41 0 -55.5 15.5 t-14.5 43.5z" />
<glyph unicode="s" horiz-adv-x="927" d="M63 61.5q-6 14.5 0 47.5l29 135q7 44 43 44q12 0 27 -5q51 -16 121.5 -31.5t124.5 -15.5h6q70 0 95.5 14t25.5 47v6q0 27 -21.5 40t-99.5 30l-90 20q-131 29 -188.5 90.5t-57.5 190.5v26q0 152 80 230q94 92 311 92h20q35 0 76 -3t82 -8t78 -11.5t63 -14.5 q27 -8 33 -19.5t2 -33.5l-22 -168q-5 -30 -35 -30q-9 0 -21 3q-53 12 -108 20.5t-109 8.5h-2q-68 0 -94 -12.5t-26 -42.5v-6q0 -29 24.5 -42.5t91.5 -27.5l96 -21q66 -14 112 -35.5t76 -54t43 -80t13 -110.5v-31q0 -160 -97 -240.5t-304 -80.5h-21q-43 0 -89 4t-92 11 t-88 17.5t-73 22.5q-29 10 -35 24.5z" />
<glyph unicode="t" horiz-adv-x="796" d="M4 799v104q0 39 10.5 50.5t42.5 19.5l109 26v201q0 29 14.5 44.5t55.5 15.5h188q41 0 55.5 -15.5t14.5 -44.5v-196h170q29 0 41 -10.5t12 -49.5v-137q0 -39 -12.5 -49t-40.5 -10h-170v-388q0 -27 3 -43t13 -28q10 -10 27.5 -15.5t52.5 -5.5h90q41 0 53 -15t12 -60v-117 q0 -45 -12 -60.5t-53 -15.5h-164q-123 0 -187.5 17.5t-103.5 58.5q-33 35 -46 92t-13 156v424h-111q-29 0 -40 11t-11 40z" />
<glyph unicode="u" horiz-adv-x="1060" d="M90 338v606q0 29 14.5 44.5t55.5 15.5h188q41 0 55.5 -15.5t14.5 -44.5v-577q0 -70 22 -95q24 -26 85 -26h5q66 0 91 26q22 24 22 89v6v577q0 29 14.5 44.5t55.5 15.5h188q41 0 55.5 -15.5t14.5 -44.5v-606q0 -98 -22.5 -162.5t-69.5 -107.5q-55 -49 -140.5 -67.5 t-208.5 -18.5t-207.5 18t-140.5 68q-47 43 -69.5 107.5t-22.5 162.5z" />
<glyph unicode="v" horiz-adv-x="1011" d="M16 944q-2 10 -2 18q0 41 60 42h190q35 0 48.5 -8.5t21.5 -39.5q20 -80 44.5 -167t48.5 -168.5t45.5 -152.5t37.5 -116h8q14 47 35.5 117t43 150.5t43.5 165.5t40 163q8 35 20.5 45.5t47.5 10.5h194q56 -1 56 -40q0 -9 -3 -20q-29 -102 -65.5 -226t-77.5 -247t-82 -236.5 t-76 -193.5q-10 -20 -24.5 -30.5t-46.5 -10.5h-224q-31 0 -48 9t-27 34q-35 80 -78 190.5t-85 233.5t-81 247t-64 230z" />
<glyph unicode="w" horiz-adv-x="1431" d="M16 942q-8 37 6.5 49.5t47.5 12.5h200q35 0 48.5 -12.5t17.5 -45.5q10 -68 24.5 -157t30 -176t27.5 -159.5t18 -105.5h8q10 49 23.5 119t28 145.5t28 149.5t21.5 131q6 43 22.5 52t61.5 9h176q43 0 59.5 -10t22.5 -51q8 -55 23.5 -129t30.5 -149.5t29.5 -146.5t24.5 -120 h6q8 51 21.5 126t27 158t25.5 166t23 148q4 35 15 46.5t44 11.5h209q37 0 46 -14.5t3 -47.5q-23 -109 -53.5 -247t-61 -267t-58.5 -233.5t-44 -145.5q-12 -33 -27.5 -41t-48.5 -8h-225q-41 0 -56.5 12.5t-21.5 34.5q-14 55 -29.5 126t-28.5 143.5t-24.5 141t-17.5 117.5h-8 q-6 -49 -18.5 -117.5t-26 -141t-28.5 -143.5t-30 -126q-6 -25 -22.5 -36t-50.5 -11h-222q-35 0 -51 10t-26 35q-16 43 -44 147.5t-60 234.5t-63.5 268.5t-52.5 246.5z" />
<glyph unicode="x" horiz-adv-x="1017" d="M14 19.5q-8 19.5 9 48.5l208 401q8 16 10.5 23.5t2.5 17.5t-2 17.5t-11 21.5l-196 387q-16 29 -8 48.5t43 19.5h229q41 0 55.5 -8.5t28.5 -43.5l111 -282q4 -8 14 -8h2q10 0 14 8l111 282q14 35 28.5 43.5t55.5 8.5h229q35 0 43 -19.5t-8 -48.5l-197 -387 q-8 -16 -10 -23.5t-2 -17.5t2 -17.5t10 -21.5l209 -401q16 -29 8 -48.5t-42 -19.5h-230q-41 0 -54 8t-30 43l-123 289q-4 8 -14 8h-2q-10 0 -14 -8l-123 -289q-16 -35 -29.5 -43t-54.5 -8h-230q-35 0 -43 19.5z" />
<glyph unicode="y" horiz-adv-x="987" d="M14 944q-2 8 -2 15q0 44 64 45h186q35 0 50.5 -9.5t21.5 -40.5q14 -84 33.5 -174t40 -179t42 -174t44.5 -159h10q25 70 49.5 160t44.5 184.5t37.5 182.5t28.5 153q4 33 19 44.5t46 11.5h189q35 0 47 -13.5t8 -48.5q-20 -137 -60.5 -298t-96.5 -331t-130 -344t-162 -336 q-12 -25 -25.5 -34t-31.5 -9q-23 0 -59 17l-105 45q-53 25 -53 51q0 14 10 35q16 33 33.5 67.5t35 68.5t31 62.5t19.5 47.5q-41 16 -73 44.5t-60 94.5q-29 66 -62.5 170t-66.5 221t-59.5 231.5t-43.5 198.5z" />
<glyph unicode="z" horiz-adv-x="903" d="M49 59v47q0 47 8.5 76t36.5 70l344 491h-325q-29 0 -41.5 10.5t-12.5 49.5v141q0 39 12.5 49.5t41.5 10.5h692q29 0 44 -12.5t15 -47.5v-39q0 -45 -8 -73.5t-37 -71.5l-352 -500h350q29 0 41 -10t12 -49v-142q0 -39 -12 -49t-41 -10h-704q-33 0 -48.5 12.5t-15.5 46.5z " />
<glyph unicode="{" horiz-adv-x="827" d="M74 524v164q0 53 49 60q117 14 117 102v240q0 166 68.5 231t209.5 65h180q41 0 54.5 -12t13.5 -47v-164q0 -55 -55 -55h-97q-27 0 -40 -10.5t-13 -44.5v-209q0 -86 -32.5 -149.5t-110.5 -84.5v-8q80 -25 111.5 -86t31.5 -147v-209q0 -35 13.5 -45.5t39.5 -10.5h97 q55 0 55 -55v-164q0 -35 -13.5 -47t-54.5 -12h-180q-141 0 -209.5 65.5t-68.5 231.5v239q0 47 -29 71t-88 32q-49 6 -49 59z" />
<glyph unicode="|" horiz-adv-x="579" d="M129 41v1276q0 31 2 48t11.5 26.5t26.5 11.5t46 2h145q57 0 74 -14.5t17 -73.5v-1276q0 -59 -16.5 -73.5t-74.5 -14.5h-145q-29 0 -46 2t-26.5 11t-11.5 26.5t-2 48.5z" />
<glyph unicode="}" horiz-adv-x="827" d="M61 49q0 55 56 55h96q27 0 40 10.5t13 45.5v209q0 86 32 147.5t112 85.5v8q-78 20 -111 84t-33 150v209q0 35 -13 45t-40 10h-96q-55 0 -56 55v164q0 35 13.5 47t54.5 12h180q141 0 210 -65.5t69 -230.5v-240q0 -88 117 -102q49 -6 49 -60v-164q0 -53 -49 -59 q-59 -8 -88 -31.5t-29 -71.5v-239q0 -166 -68.5 -231.5t-210.5 -65.5h-180q-41 0 -54.5 12t-13.5 47v164z" />
<glyph unicode="~" d="M18 535q0 23 11.5 43t23.5 40q76 129 166 187.5t189 58.5q55 0 105 -18.5t97 -46.5l66 -41q47 -29 75.5 -41t55.5 -12q57 0 113 106q16 37 43 37q14 0 38 -12l136 -76q33 -18 32 -41q0 -20 -9 -42t-25 -52q-66 -125 -154 -181.5t-186 -56.5q-100 0 -189 53l-74 45 q-37 23 -58 34t-50 11q-35 0 -66.5 -26.5t-70.5 -85.5q-18 -31 -51 -31q-27 0 -54 16l-131 80q-33 18 -33 52z" />
<glyph unicode="&#xa1;" horiz-adv-x="526" d="M82 845q0 -94 51 -129t123 -35h6q35 0 66.5 8.5t55.5 27t38 50t14 78.5v17q0 45 -14 75.5t-38 49t-55.5 26.5t-66.5 8h-6q-35 0 -66.5 -8t-55 -26.5t-38 -49t-14.5 -75.5v-17zM86 -279q-4 -51 13.5 -64.5t72.5 -13.5h184q55 0 72.5 13.5t13.5 64.5l-36 805 q-2 43 -20.5 54t-66.5 11h-114q-47 0 -62.5 -12t-17.5 -49z" />
<glyph unicode="&#xa2;" d="M180 434v119q0 76 6.5 131t19.5 98t32.5 74t46.5 57q53 53 134 81t204 28h10v137q0 29 26 29h62q29 0 29 -29v-145q57 -8 112.5 -23.5t95.5 -38.5q39 -20 39 -51q0 -12 -4 -26.5t-12 -34.5l-39 -97q-10 -20 -18.5 -28t-24.5 -8q-12 0 -37 10q-55 23 -112 35v-504 q33 4 66.5 10t62.5 14l18 4.5t21 2.5q31 0 36 -35l19 -105q4 -16 6 -30.5t2 -24.5q0 -20 -10 -32.5t-43 -22.5q-84 -29 -178 -41v-144q0 -29 -29 -28h-62q-27 0 -26 28v138h-25q-221 0 -323 102q-59 59 -82 144t-23 206zM510 461q0 -70 9 -107.5t32 -64.5q29 -33 82 -41v508 q-51 -10 -78 -37q-31 -31 -38 -75t-7 -114v-69z" />
<glyph unicode="&#xa3;" d="M115 51v123q0 23 7 34t23 21q86 51 119 116t33 157v37h-107q-33 0 -43 12t-10 43v88q0 39 10.5 52.5t47.5 17.5l102 14v162q0 205 98.5 310t327.5 105h12q80 0 161 -13t146 -36q39 -14 39 -51q0 -12 -2 -25.5t-6 -25.5l-31 -123q-6 -23 -23.5 -31t-57.5 2 q-41 10 -89.5 17.5t-85.5 7.5h-8q-82 0 -109.5 -41t-27.5 -119v-123h301q29 0 39 -10t10 -41v-145q0 -29 -10 -38t-39 -9h-303v-33q0 -82 -26.5 -146.5t-67.5 -97.5h493q35 0 47.5 -11t12.5 -46v-148q0 -31 -11.5 -44t-44.5 -13h-872q-31 0 -43 11.5t-12 39.5z" />
<glyph unicode="&#xa4;" horiz-adv-x="1179" d="M56 277q-1 16 24 45l115 110q-33 78 -33 176q0 47 8 92t27 86l-119 111q-23 23 -21 43.5t25 44.5l133 137q39 41 82 0l123 -116q39 12 80 19t86 7q90 0 172 -28l125 118q45 43 82 0l133 -137q23 -25 23.5 -41t-23.5 -45l-121 -117q18 -41 25.5 -84t7.5 -90t-6.5 -91 t-22.5 -83l117 -112q25 -29 23.5 -45.5t-23.5 -38.5l-133 -140q-37 -43 -82 0l-121 117q-41 -18 -85 -25.5t-91 -7.5q-45 0 -88 8.5t-80 22.5l-121 -115q-45 -43 -82 0l-135 140q-23 23 -24 39zM442 608q0 -66 42 -106.5t102 -40.5q59 0 101 41t42 106q0 66 -42 107t-101 41 t-101.5 -41t-42.5 -107z" />
<glyph unicode="&#xa5;" d="M72 1257q-18 68 39 68h213q45 0 61 -5t27 -44q16 -66 37.5 -142.5t45 -156.5t47 -157t46.5 -140h10q23 63 47.5 140t48 157t45 156.5t37.5 142.5q10 39 26.5 44t61.5 5h213q57 0 39 -68q-41 -145 -99.5 -297.5t-123.5 -293.5h104q29 0 41.5 -10.5t12.5 -43.5v-98 q0 -33 -12.5 -43t-41.5 -10h-231v-82h231q29 0 41.5 -10.5t12.5 -42.5v-99q0 -33 -12.5 -43t-41.5 -10h-231v-111q0 -37 -13.5 -50t-52.5 -13h-215q-39 0 -52 13.5t-13 49.5v111h-238q-29 0 -41 10.5t-12 42.5v99q0 33 12.5 43t40.5 10h238v82h-238q-29 0 -41 10t-12 43v98 q0 33 12.5 43.5t40.5 10.5h109q-66 141 -122 293.5t-97 297.5z" />
<glyph unicode="&#xa6;" horiz-adv-x="579" d="M129 41v448q0 31 2 48.5t11.5 26.5t26.5 11.5t46 2.5h145q57 0 74 -14.5t17 -74.5v-448q0 -59 -16.5 -73.5t-74.5 -14.5h-145q-29 0 -46 2t-26.5 11t-11.5 26.5t-2 48.5zM129 868v449q0 31 2 48t11.5 26.5t26.5 11.5t46 2h145q57 0 74 -14.5t17 -73.5v-449 q0 -59 -16.5 -73.5t-74.5 -14.5h-145q-29 0 -46 2t-26.5 11.5t-11.5 27t-2 47.5z" />
<glyph unicode="&#xa7;" horiz-adv-x="1024" d="M106 610v17q0 61 34 108t85 70q-117 66 -116 233v17q0 78 39.5 138t107.5 97q98 53 266 53h15q80 0 155.5 -12t134.5 -33q29 -10 29 -28q0 -6 -4 -28.5t-10 -55.5l-19 -101q-6 -39 -35 -38q-10 0 -28 6q-41 14 -98.5 23t-100.5 9h-8q-53 0 -80 -8t-27 -33v-8 q0 -16 26 -26.5t85 -20.5l80 -16q80 -16 133 -37t84 -47.5t43 -61t12 -84.5v-12q0 -66 -31.5 -115t-91.5 -75q63 -35 97.5 -93.5t34.5 -136.5v-20q0 -72 -29 -129.5t-80 -98.5q-53 -41 -137 -61t-180 -20h-17q-35 0 -76 4t-83 12t-82.5 19.5t-71.5 25.5q-25 10 -37 21.5 t-12 33.5q0 14 6 43l26 113q8 33 24.5 42t53.5 -3q53 -18 122 -33.5t126 -15.5h10q66 0 84.5 11t18.5 34v4q0 23 -24.5 35t-90.5 26l-80 17q-156 33 -219.5 92t-63.5 147zM381 694q0 -29 22.5 -45t98.5 -33l80 -18q12 10 18 24.5t6 28.5v6q0 14 -3 23.5t-14 18t-35 15.5 t-62 17l-89 21q-23 -18 -22 -52v-6z" />
<glyph unicode="&#xa8;" horiz-adv-x="808" d="M74 1274v16q0 59 38 96t95 37h12q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-12q-57 0 -95 36.5t-38 96.5zM457 1274v16q0 59 37.5 96t95.5 37h12q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-12q-57 0 -95 36.5t-38 96.5z" />
<glyph unicode="&#xa9;" horiz-adv-x="1687" d="M82 713q0 160 52 295t150.5 234t240 155.5t319.5 56.5t319.5 -56.5t239.5 -155.5t150.5 -234.5t52.5 -294.5q0 -160 -52.5 -295t-150.5 -234.5t-239.5 -156t-319.5 -56.5t-319.5 56.5t-240 156t-150.5 234.5t-52 295zM338 709q0 -111 30.5 -205t93 -163t158 -108.5 t224.5 -39.5t224 39.5t157.5 108.5t93.5 163t31 205q0 113 -31 209t-93.5 166.5t-157.5 109.5t-224 39t-224.5 -39t-158 -109.5t-93 -167t-30.5 -208.5zM549 629v157q0 166 69.5 238t233.5 72q66 0 123 -13.5t98 -35.5q20 -10 23.5 -24.5t-4.5 -39.5l-25 -76 q-10 -27 -23.5 -28.5t-35.5 8.5q-35 16 -62.5 23.5t-56.5 7.5q-66 0 -86.5 -26t-20.5 -97v-170q0 -57 20.5 -86t88.5 -29q27 0 55.5 6t69.5 19q25 8 36 2.5t17 -27.5l25 -80q8 -27 -1.5 -39t-43.5 -24q-41 -16 -90.5 -25.5t-96.5 -9.5q-166 0 -239.5 72.5t-73.5 224.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="710" d="M57 782v9q0 121 68 161.5t190 40.5h11q51 0 100 -6v27q0 31 -20.5 44t-77.5 13h-11q-37 0 -76.5 -7t-76.5 -17q-18 -6 -27.5 1t-13.5 23l-10 49q-4 25 -6.5 36t-2.5 22q0 27 29 34q111 35 236 35h14q59 0 107.5 -10t81 -35.5t50 -68.5t17.5 -111v-395q0 -39 -37 -39h-88 q-23 0 -31 4t-12 20l-8 31h-4q-16 -29 -60.5 -49.5t-103.5 -20.5h-27q-61 0 -102 18.5t-65.5 48.5t-34 66.5t-9.5 75.5zM254 780q0 -29 16.5 -43t57.5 -14h8q90 0 90 82v41q-18 2 -38.5 3t-39.5 1h-8q-47 0 -66.5 -14.5t-19.5 -49.5v-6z" />
<glyph unicode="&#xab;" horiz-adv-x="1486" d="M57 467v25q0 31 8.5 50t47.5 50l403 321q31 25 52.5 24t45.5 -30l97 -106q29 -33 26.5 -55.5t-39.5 -53.5l-249 -213l249 -213q41 -35 39 -57.5t-26 -50.5l-97 -107q-27 -29 -47 -30t-51 24l-403 322q-35 29 -45.5 47t-10.5 53zM741 467v25q0 31 8.5 50t47.5 50l403 321 q31 25 52.5 24t45.5 -30l97 -106q29 -33 26.5 -55.5t-39.5 -53.5l-249 -213l249 -213q41 -35 39 -57.5t-26 -50.5l-97 -107q-27 -29 -47 -30t-51 24l-403 322q-35 29 -45.5 47t-10.5 53z" />
<glyph unicode="&#xac;" d="M100 461v127q0 43 15.5 62.5t56.5 19.5h836q41 0 60 -16.5t19 -59.5v-459q0 -41 -16 -55t-57 -14h-127q-43 0 -62.5 14t-19.5 55v252h-633q-41 0 -56.5 16.5t-15.5 57.5z" />
<glyph unicode="&#xad;" horiz-adv-x="745" d="M66 451v153q0 37 17 51.5t64 14.5h451q47 0 64.5 -14.5t17.5 -51.5v-153q0 -37 -15.5 -51.5t-66.5 -14.5h-451q-51 0 -66 14.5t-15 51.5z" />
<glyph unicode="&#xae;" horiz-adv-x="1687" d="M82 713q0 160 52 295t150.5 234t240 155.5t319.5 56.5t319.5 -56.5t239.5 -155.5t150.5 -234.5t52.5 -294.5q0 -160 -52.5 -295t-150.5 -234.5t-239.5 -156t-319.5 -56.5t-319.5 56.5t-240 156t-150.5 234.5t-52 295zM338 709q0 -111 30.5 -205t93 -163t158 -108.5 t224.5 -39.5t224 39.5t157.5 108.5t93.5 163t31 205q0 113 -31 209t-93.5 166.5t-157.5 109.5t-224 39t-224.5 -39t-158 -109.5t-93 -167t-30.5 -208.5zM578 401v611q0 31 15 49t52 22q35 4 83 5.5t99 1.5q160 0 237 -60.5t77 -177.5v-16q0 -156 -144 -213l148 -224 q16 -23 5 -39t-50 -16h-113q-29 0 -43 8t-26 31l-121 213h-4v-195q0 -29 -14.5 -43t-51.5 -14h-84q-37 0 -51 14.5t-14 42.5zM797 756h34q53 0 74 20q18 18 19 68v2q0 72 -80 72h-24.5t-22.5 -3v-159z" />
<glyph unicode="&#xaf;" horiz-adv-x="811" d="M129 1219v100q0 51 53 51h447q53 0 53 -51v-100q0 -49 -53 -50h-447q-53 0 -53 50z" />
<glyph unicode="&#xb0;" horiz-adv-x="753" d="M68 1229q0 68 22.5 126t62.5 100t97 65.5t127 23.5t127 -23.5t97 -65.5t62.5 -100.5t22.5 -125.5q0 -68 -22.5 -125t-62.5 -98t-97 -63.5t-127 -22.5t-127 22.5t-97 63.5t-62.5 98t-22.5 125zM283 1229q0 -53 26.5 -79t67.5 -26t67.5 26t26.5 79t-26.5 79.5t-67.5 26.5 t-67.5 -26.5t-26.5 -79.5z" />
<glyph unicode="&#xb1;" d="M100 74v127q0 43 15.5 62.5t56.5 19.5h844q41 0 56 -19.5t15 -62.5v-127q0 -41 -15 -57.5t-56 -16.5h-844q-41 0 -56.5 16.5t-15.5 57.5zM100 813v127q0 43 15.5 62.5t56.5 19.5h264v272q0 49 20.5 61.5t63.5 12.5h148q43 0 63.5 -12.5t20.5 -61.5v-272h264 q41 0 56 -19.5t15 -62.5v-127q0 -41 -15 -57.5t-56 -16.5h-264v-272q0 -49 -20.5 -61.5t-63.5 -12.5h-148q-43 0 -63.5 12.5t-20.5 61.5v272h-264q-41 0 -56.5 16.5t-15.5 57.5z" />
<glyph unicode="&#xb2;" horiz-adv-x="925" d="M170 918v90q0 111 37 177t139 115l64 31q59 29 78.5 46.5t19.5 37.5v8q0 14 -13.5 25.5t-39.5 11.5h-15q-37 0 -84 -9.5t-88 -25.5q-16 -4 -20 -4q-20 0 -23 19l-24 110q-4 25 -7.5 38.5t-3.5 17.5q0 20 25 30q53 20 126 35t140 15h15q121 0 199.5 -56.5t78.5 -175.5v-12 q0 -49 -9 -86t-28.5 -64.5t-50.5 -51t-76 -46.5l-67 -33q-53 -27 -75 -39t-22 -32v-15h265q33 0 43 -12t10 -47v-111q0 -33 -10.5 -45t-42.5 -12h-471q-41 0 -55.5 14.5t-14.5 55.5z" />
<glyph unicode="&#xb3;" horiz-adv-x="925" d="M147 932q0 8 1.5 19.5t9.5 41.5l16 68q10 41 25.5 49t42.5 -2q29 -10 80 -20.5t88 -10.5h10q45 0 63.5 12.5t18.5 40.5v11q0 23 -16.5 34t-65.5 11h-47q-25 0 -36 8t-11 35v80q0 27 11 35t34 8h39q59 0 74.5 16t15.5 37v6q0 43 -72 43h-8q-31 0 -77 -8t-87 -21 q-12 -4 -18 -4q-20 0 -27 29l-21 100q-4 23 -7 35t-3 21q0 23 23 28q55 23 131 37.5t143 14.5h12q276 0 277 -226v-12q0 -66 -21.5 -105.5t-72.5 -66.5v-6q100 -35 100 -150v-12q0 -127 -81 -192.5t-236 -65.5h-19q-59 0 -126.5 11t-116.5 28q-25 8 -34 17t-12 26z" />
<glyph unicode="&#xb4;" horiz-adv-x="858" d="M162 1241q0 20 18 33l322 274q20 18 39 19q23 0 41 -27l98 -127q12 -16 12 -35q0 -16 -8 -26.5t-25 -22.5l-323 -221q-23 -14 -41 -14q-16 0 -24.5 6t-24.5 22l-62 72q-23 29 -22 47z" />
<glyph unicode="&#xb5;" horiz-adv-x="1179" d="M104 -301v1245q0 29 14.5 44.5t55.5 15.5h188q41 0 55.5 -15.5t14.5 -44.5v-571q0 -68 20.5 -93.5t79.5 -25.5h13q59 0 85.5 27.5t26.5 91.5v571q0 29 14.5 44.5t55.5 15.5h188q41 0 55.5 -15.5t14.5 -44.5v-622q0 -68 55 -68h7q12 0 26 2t25 6q41 12 43 -31l8 -104 q2 -29 3 -42t1 -19q0 -16 -6 -24.5t-25 -18.5q-29 -16 -80 -28.5t-110 -12.5h-10q-74 0 -124 17t-81 58h-6q-23 -31 -66 -53t-102 -22h-13q-55 0 -98 16v-299q0 -29 -14.5 -44t-55.5 -15h-188q-41 0 -55.5 15t-14.5 44z" />
<glyph unicode="&#xb6;" horiz-adv-x="1372" d="M47 950v19q0 82 27.5 151.5t84 119.5t142.5 79t201 29h776q27 0 37 -9.5t10 -38.5v-174q0 -47 -47 -47h-117v-1237q0 -39 -16.5 -49t-50.5 -10h-193q-35 0 -51 10t-16 49v1237h-123v-1237q0 -39 -16.5 -49t-51.5 -10h-192q-35 0 -51.5 10t-16.5 49v725 q-166 29 -251 130.5t-85 252.5z" />
<glyph unicode="&#xb7;" horiz-adv-x="497" d="M74 586v20q0 94 43 130t121 36h22q78 0 121 -36t43 -130v-20q0 -94 -43 -132t-121 -38h-22q-78 0 -121 38t-43 132z" />
<glyph unicode="&#xb8;" horiz-adv-x="917" d="M201 -432q0 6 1 16t3 21l8 51q6 31 27 31q6 0 18 -4q23 -8 60.5 -14.5t76.5 -6.5h6q59 0 60 41v4q0 18 -14.5 30.5t-45.5 12.5h-6q-16 0 -34.5 -2t-36.5 -6q-29 -6 -43 12l-23 29q-16 23 -16 31q0 12 8 39l37 131q6 27 32 26h119q27 0 27 -16q0 -4 -2 -15.5t-6 -21.5 l-17 -66q31 6 72 7h4q72 0 120 -43t48 -136v-6q0 -111 -61.5 -161t-178.5 -50h-10q-51 0 -106.5 8.5t-89.5 18.5q-20 8 -28.5 15t-8.5 24z" />
<glyph unicode="&#xb9;" horiz-adv-x="925" d="M137 1483q0 23 31 41l186 114q33 20 53.5 26.5t63.5 6.5h107q31 0 44 -12t13 -45v-524h96q33 0 46.5 -11.5t13.5 -46.5v-127q0 -33 -10.5 -45t-47.5 -12h-487q-37 0 -47.5 12t-10.5 45v127q0 35 13.5 46.5t46.5 11.5h125v282h-8l-80 -47q-25 -12 -41 -12q-23 0 -41 30 l-51 101q-14 23 -15 39z" />
<glyph unicode="&#xba;" horiz-adv-x="737" d="M70 872v76q0 160 69.5 229.5t222.5 69.5h13q154 0 223.5 -69.5t69.5 -229.5v-76q0 -160 -70 -229.5t-223 -69.5h-13q-154 0 -223 70t-69 229zM285 852q0 -55 20.5 -76.5t59.5 -21.5h8q39 0 59.5 21.5t20.5 76.5v121q0 55 -20.5 74.5t-59.5 19.5h-8q-39 0 -59.5 -19.5 t-20.5 -74.5v-121z" />
<glyph unicode="&#xbb;" horiz-adv-x="1486" d="M65.5 745.5q2.5 -22.5 39.5 -53.5l249 -213l-249 -213q-41 -35 -39 -57.5t26 -50.5l97 -107q27 -29 47 -30t51 24l403 322q35 29 45.5 47t10.5 53v25q0 31 -8.5 50t-47.5 50l-403 321q-31 25 -52.5 24t-45.5 -30l-97 -106q-29 -33 -26.5 -55.5zM749.5 745.5 q2.5 -22.5 39.5 -53.5l249 -213l-249 -213q-41 -35 -39 -57.5t26 -50.5l97 -107q27 -29 47 -30t51 24l403 322q35 29 45.5 47t10.5 53v25q0 31 -8.5 50t-47.5 50l-403 321q-31 25 -52.5 24t-45.5 -30l-97 -106q-29 -33 -26.5 -55.5z" />
<glyph unicode="&#xbc;" horiz-adv-x="2123" d="M131 1135q0 23 31 41l186 114q33 20 53.5 26.5t63.5 6.5h106q31 0 44.5 -12.5t13.5 -44.5v-525h96q33 0 46 -11t13 -46v-127q0 -33 -10 -45t-47 -12h-487q-37 0 -47.5 12t-10.5 45v127q0 35 13.5 46t46.5 11h125v283h-9l-79 -47q-25 -12 -41 -12q-23 0 -41 30l-52 101 q-14 23 -14 39zM578.5 8.5q-11.5 18.5 7.5 46.5l717 1235q16 29 36.5 41t67.5 12h154q33 0 44 -18t-8 -47l-716 -1235q-16 -29 -37 -41t-68 -12h-153q-33 0 -44.5 18.5zM1235 229v64q0 16 7 33.5t32 56.5l250 385q18 31 33.5 43t56.5 12h176q45 0 57.5 -11t12.5 -52v-383h57 q23 0 33 -7.5t10 -37.5v-107q0 -29 -10.5 -39t-32.5 -10h-57v-127q0 -27 -10.5 -38t-43.5 -11h-149q-53 0 -53 47v129h-318q-29 0 -40 10.5t-11 42.5zM1475 377h129v196h-9z" />
<glyph unicode="&#xbd;" horiz-adv-x="2123" d="M133 1137q0 23 31 41l186 114q33 20 53.5 26.5t63.5 6.5h107q31 0 44 -12t13 -45v-524h96q33 0 46.5 -11.5t13.5 -46.5v-127q0 -33 -10.5 -45t-47.5 -12h-487q-37 0 -47.5 12t-10.5 45v127q0 35 13.5 46.5t46.5 11.5h125v282h-8l-80 -47q-25 -12 -41 -12q-23 0 -41 30 l-51 101q-14 23 -15 39zM578.5 8.5q-11.5 18.5 7.5 46.5l716 1235q16 29 37 41t68 12h154q33 0 44 -18t-8 -47l-716 -1235q-16 -29 -37 -41t-68 -12h-153q-33 0 -44.5 18.5zM1389 76v90q0 111 37 177t139 115l64 31q59 29 78.5 46.5t19.5 37.5v8q0 14 -13.5 25.5t-39.5 11.5 h-15q-37 0 -84 -9.5t-88 -25.5q-16 -4 -20 -4q-20 0 -23 19l-24 110q-4 25 -7.5 38.5t-3.5 17.5q0 20 25 30q53 20 126 35t140 15h15q121 0 199.5 -56.5t78.5 -175.5v-12q0 -49 -9 -86t-28.5 -64.5t-50.5 -51t-76 -46.5l-67 -33q-53 -27 -75 -39t-22 -32v-15h265 q33 0 43 -12t10 -47v-111q0 -33 -10.5 -45t-42.5 -12h-471q-41 0 -55.5 14.5t-14.5 55.5z" />
<glyph unicode="&#xbe;" horiz-adv-x="2123" d="M207 582q0 8 1 19t9 42l16 68q10 41 25.5 49t42.5 -2q29 -10 80 -20.5t88 -10.5h10q45 0 63.5 12.5t18.5 40.5v11q0 23 -16.5 34t-65.5 11h-47q-25 0 -36 8t-11 35v79q0 27 11.5 35t33.5 8h39q59 0 74.5 16.5t15.5 37.5v6q0 43 -72 43h-8q-31 0 -77 -8.5t-87 -20.5 q-12 -4 -18 -4q-20 0 -27 29l-20 100q-4 23 -7 35t-3 20q0 23 22 29q55 23 131 37t144 14h12q276 0 276 -225v-12q0 -66 -21.5 -106t-72.5 -66v-6q100 -35 100 -150v-12q0 -127 -80.5 -192.5t-236.5 -65.5h-18q-59 0 -127 11t-117 28q-25 8 -34 17t-11 26zM584.5 8.5 q-11.5 18.5 7.5 46.5l717 1235q16 29 36.5 41t67.5 12h154q33 0 44 -18t-7 -47l-717 -1235q-16 -29 -37 -41t-68 -12h-153q-33 0 -44.5 18.5zM1235 229v64q0 16 7 33.5t32 56.5l250 385q18 31 33.5 43t56.5 12h176q45 0 57.5 -11t12.5 -52v-383h57q23 0 33 -7.5t10 -37.5 v-107q0 -29 -10.5 -39t-32.5 -10h-57v-127q0 -27 -10.5 -38t-43.5 -11h-149q-53 0 -53 47v129h-318q-29 0 -40 10.5t-11 42.5zM1475 377h129v196h-9z" />
<glyph unicode="&#xbf;" horiz-adv-x="956" d="M77 -1q0 115 40 191t145 139l61 37q78 47 107.5 81t29.5 93v29q0 37 47 37h162q47 0 47 -45v-47q0 -59 -5 -100.5t-20.5 -72t-45 -56t-76.5 -54.5l-51 -31q-70 -41 -90.5 -73.5t-20.5 -81.5v-7q0 -113 135 -112h4q55 0 122 16.5t126 38.5q25 10 40.5 3t25.5 -36l39 -121 q4 -10 6 -21t2 -22q0 -33 -45 -53q-31 -14 -74 -27.5t-90 -22.5t-96 -15t-95 -6h-14q-205 0 -310.5 80.5t-105.5 244.5v14zM407 845v17q0 45 14.5 75.5t38 49t55 26.5t66.5 8h6q35 0 67 -8t55.5 -26.5t37.5 -49t14 -75.5v-17q0 -94 -51 -129t-123 -35h-6q-35 0 -66.5 8.5 t-55 27t-38 50t-14.5 78.5z" />
<glyph unicode="&#xc0;" horiz-adv-x="1255" d="M31 45q0 10 6 32.5t22 80.5q86 289 165 554t165 521q16 51 48 71.5t97 20.5h187q66 0 97.5 -20.5t47.5 -71.5q86 -256 167 -521.5t167 -553.5q16 -57 22.5 -80t6.5 -33q0 -27 -17.5 -36t-52.5 -9h-182q-31 0 -50.5 4t-30.5 11.5t-16.5 20.5t-11.5 32l-47 170h-387 l-47 -170q-6 -18 -11 -31.5t-16.5 -22t-31 -11.5t-50.5 -3h-178q-35 0 -52 9t-17 36zM316 1645q0 20 14 43l78 127q6 10 15 20t24 10q16 0 39 -14l350 -236q23 -14 22 -36q0 -18 -16 -43l-53 -78q-14 -18 -23.5 -26.5t-21.5 -8.5q-23 0 -43 10l-346 182q-39 20 -39 50z M493 504h269q-12 49 -27.5 106.5t-32 119t-33 122.5t-30.5 115h-23q-16 -53 -32.5 -114.5t-32 -123t-30.5 -119t-28 -106.5z" />
<glyph unicode="&#xc1;" horiz-adv-x="1255" d="M31 45q0 10 6 32.5t22 80.5q86 289 165 554t165 521q16 51 48 71.5t97 20.5h187q66 0 97.5 -20.5t47.5 -71.5q86 -256 167 -521.5t167 -553.5q16 -57 22.5 -80t6.5 -33q0 -27 -17.5 -36t-52.5 -9h-182q-31 0 -50.5 4t-30.5 11.5t-16.5 20.5t-11.5 32l-47 170h-387 l-47 -170q-6 -18 -11 -31.5t-16.5 -22t-31 -11.5t-50.5 -3h-178q-35 0 -52 9t-17 36zM398 1559q0 23 22 36l350 236q23 14 39 14q14 0 23.5 -10t15.5 -20l78 -127q14 -23 14 -43q0 -29 -38 -50l-347 -182q-20 -10 -43 -10q-12 0 -21 8t-24 27l-53 78q-16 25 -16 43zM493 504 h269q-12 49 -27.5 106.5t-32 119t-33 122.5t-30.5 115h-23q-16 -53 -32.5 -114.5t-32 -123t-30.5 -119t-28 -106.5z" />
<glyph unicode="&#xc2;" horiz-adv-x="1255" d="M31 45q0 10 6 32.5t22 80.5q86 289 165 554t165 521q16 51 48 71.5t97 20.5h187q66 0 97.5 -20.5t47.5 -71.5q86 -256 167 -521.5t167 -553.5q16 -57 22.5 -80t6.5 -33q0 -27 -17.5 -36t-52.5 -9h-182q-31 0 -50.5 4t-30.5 11.5t-16.5 20.5t-11.5 32l-47 170h-387 l-47 -170q-6 -18 -11 -31.5t-16.5 -22t-31 -11.5t-50.5 -3h-178q-35 0 -52 9t-17 36zM311 1526q0 18 17 35l180 208q16 16 28.5 21.5t28.5 5.5h125q16 0 28.5 -5t29.5 -22l180 -208q16 -16 16 -35q0 -12 -14 -27l-74 -61q-23 -18 -43 -19q-25 0 -45 23l-135 145h-10 l-135 -145q-20 -23 -45 -23q-23 0 -43 19l-74 61q-14 14 -15 27zM493 504h269q-12 49 -27.5 106.5t-32 119t-33 122.5t-30.5 115h-23q-16 -53 -32.5 -114.5t-32 -123t-30.5 -119t-28 -106.5z" />
<glyph unicode="&#xc3;" horiz-adv-x="1255" d="M31 45q0 10 6 32.5t22 80.5q86 289 165 554t165 521q16 51 48 71.5t97 20.5h187q66 0 97.5 -20.5t47.5 -71.5q86 -256 167 -521.5t167 -553.5q16 -57 22.5 -80t6.5 -33q0 -27 -17.5 -36t-52.5 -9h-182q-31 0 -50.5 4t-30.5 11.5t-16.5 20.5t-11.5 32l-47 170h-387 l-47 -170q-6 -18 -11 -31.5t-16.5 -22t-31 -11.5t-50.5 -3h-178q-35 0 -52 9t-17 36zM270 1513q0 12 6 29q41 98 102.5 146.5t126.5 48.5q41 0 71 -11.5t58 -29.5l39 -25q47 -29 72 -29t45.5 15.5t42.5 50.5q10 16 27 16q10 0 26 -10l78 -53q18 -16 19 -29q0 -12 -8.5 -27.5 t-16.5 -25.5q-94 -141 -219 -141q-61 0 -115 32l-41 27q-41 27 -69 27q-18 0 -38 -13.5t-48 -58.5q-12 -18 -29 -18q-10 0 -25 6l-88 49q-16 10 -16 24zM493 504h269q-12 49 -27.5 106.5t-32 119t-33 122.5t-30.5 115h-23q-16 -53 -32.5 -114.5t-32 -123t-30.5 -119 t-28 -106.5z" />
<glyph unicode="&#xc4;" horiz-adv-x="1255" d="M31 45q0 10 6 32.5t22 80.5q86 289 165 554t165 521q16 51 48 71.5t97 20.5h187q66 0 97.5 -20.5t47.5 -71.5q86 -256 167 -521.5t167 -553.5q16 -57 22.5 -80t6.5 -33q0 -27 -17.5 -36t-52.5 -9h-182q-31 0 -50.5 4t-30.5 11.5t-16.5 20.5t-11.5 32l-47 170h-387 l-47 -170q-6 -18 -11 -31.5t-16.5 -22t-31 -11.5t-50.5 -3h-178q-35 0 -52 9t-17 36zM297 1612v16q0 59 38 96t95 37h13q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-13q-57 0 -95 36.5t-38 96.5zM493 504h269q-12 49 -27.5 106.5t-32 119t-33 122.5t-30.5 115h-23 q-16 -53 -32.5 -114.5t-32 -123t-30.5 -119t-28 -106.5zM680 1612v16q0 59 38 96t95 37h13q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-13q-57 0 -95 36.5t-38 96.5z" />
<glyph unicode="&#xc5;" horiz-adv-x="1255" d="M31 45q0 10 6 32.5t22 80.5q86 289 165 554t165 521q16 51 48 71.5t97 20.5h187q66 0 97.5 -20.5t47.5 -71.5q86 -256 167 -521.5t167 -553.5q16 -57 22.5 -80t6.5 -33q0 -27 -17.5 -36t-52.5 -9h-182q-31 0 -50.5 4t-30.5 11.5t-16.5 20.5t-11.5 32l-47 170h-387 l-47 -170q-6 -18 -11 -31.5t-16.5 -22t-31 -11.5t-50.5 -3h-178q-35 0 -52 9t-17 36zM418 1599q0 45 16.5 84t45 67t66.5 43t81 15q90 0 149.5 -58t59.5 -151q0 -92 -59.5 -150t-149.5 -58q-43 0 -81 15t-66.5 43t-45 66.5t-16.5 83.5zM493 504h269q-12 49 -27.5 106.5 t-32 119t-33 122.5t-30.5 115h-23q-16 -53 -32.5 -114.5t-32 -123t-30.5 -119t-28 -106.5zM567 1599q0 -29 18.5 -44t41.5 -15t41 15.5t18 43.5q0 29 -18.5 44.5t-40.5 15.5q-23 0 -41.5 -15.5t-18.5 -44.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1773" d="M24.5 13.5q-18.5 13.5 2.5 54.5q76 156 155.5 317.5t157.5 317t149.5 299t133.5 264.5q29 59 104 59h903q49 0 58.5 -11t9.5 -52v-154q0 -37 -9.5 -49t-58.5 -12h-485v-244h426q39 0 52 -11.5t13 -48.5v-143q0 -33 -13 -44t-52 -11h-430v-223q0 -23 9 -33t36 -10h442 q47 0 58.5 -11.5t11.5 -52.5v-152q0 -37 -11.5 -50t-58.5 -13h-493q-152 0 -232 43t-80 172v53h-354l-102 -213q-14 -29 -37 -42t-76 -13h-170q-41 0 -59.5 13.5zM584 539h239v508h-20z" />
<glyph unicode="&#xc7;" horiz-adv-x="1148" d="M94 537v251q0 150 28 250.5t91 165.5q66 72 170 105.5t256 33.5h14q100 0 210 -23.5t192 -66.5q23 -12 29 -28.5t-5 -44.5l-67 -172q-12 -31 -35 -31q-18 0 -39 10q-66 33 -132.5 51.5t-137.5 18.5h-9q-131 0 -176 -74q-23 -39 -31 -97.5t-8 -146.5v-145 q0 -94 11.5 -157.5t46.5 -104.5q29 -31 75 -47.5t117 -16.5h8q55 0 128 17.5t126 40.5q8 4 15.5 6t17.5 2q12 0 21.5 -9.5t15.5 -33.5l35 -148q6 -25 6 -39q0 -18 -12.5 -30.5t-48.5 -26.5q-72 -29 -163 -46t-181 -19l-23 -91q31 6 72 7h4q72 0 120 -43t48 -136v-6 q0 -117 -61.5 -164t-178.5 -47h-10q-51 0 -106.5 8.5t-90.5 18.5q-20 8 -28.5 15t-8.5 24q0 6 1 16t3 21l9 51q6 31 26 31q6 0 19 -4q23 -8 60.5 -14.5t76.5 -6.5h6q59 0 59 41v4q0 18 -14 30.5t-45 12.5h-6q-16 0 -34.5 -2t-37.5 -6q-29 -6 -43 12l-22 29q-16 23 -17 31 q0 12 9 39l36 131v8q-180 25 -274 127q-63 66 -90 167t-27 251z" />
<glyph unicode="&#xc8;" horiz-adv-x="1079" d="M106 610v125q0 66 2.5 155t10.5 175q6 80 26.5 130t57.5 79t94 40t139 11h490q49 0 58 -11t9 -52v-154q0 -37 -9 -49t-58 -12h-434q-29 0 -39.5 -9.5t-12.5 -31.5q-2 -31 -3 -90.5t-3 -112.5h432q39 0 52.5 -11.5t13.5 -48.5v-143q0 -33 -13.5 -44t-52.5 -11h-436 q0 -63 3 -123t9 -107q4 -23 14.5 -29.5t30.5 -6.5h437q47 0 58 -11.5t11 -52.5v-152q0 -37 -11 -50t-58 -13h-494q-76 0 -131 9t-93 34t-58.5 66t-26.5 106q-10 94 -12.5 193.5t-2.5 201.5zM303 1645q0 20 14 43l78 127q6 10 15 20t24 10q16 0 39 -14l350 -236 q23 -14 22 -36q0 -18 -16 -43l-53 -78q-14 -18 -23.5 -26.5t-21.5 -8.5q-23 0 -43 10l-346 182q-39 20 -39 50z" />
<glyph unicode="&#xc9;" horiz-adv-x="1079" d="M106 610v125q0 66 2.5 155t10.5 175q6 80 26.5 130t57.5 79t94 40t139 11h490q49 0 58 -11t9 -52v-154q0 -37 -9 -49t-58 -12h-434q-29 0 -39.5 -9.5t-12.5 -31.5q-2 -31 -3 -90.5t-3 -112.5h432q39 0 52.5 -11.5t13.5 -48.5v-143q0 -33 -13.5 -44t-52.5 -11h-436 q0 -63 3 -123t9 -107q4 -23 14.5 -29.5t30.5 -6.5h437q47 0 58 -11.5t11 -52.5v-152q0 -37 -11 -50t-58 -13h-494q-76 0 -131 9t-93 34t-58.5 66t-26.5 106q-10 94 -12.5 193.5t-2.5 201.5zM363 1559q0 23 22 36l350 236q23 14 39 14q14 0 23.5 -10t15.5 -20l78 -127 q14 -23 14 -43q0 -29 -38 -50l-347 -182q-20 -10 -43 -10q-12 0 -21 8t-24 27l-53 78q-16 25 -16 43z" />
<glyph unicode="&#xca;" horiz-adv-x="1079" d="M106 610v125q0 66 2.5 155t10.5 175q6 80 26.5 130t57.5 79t94 40t139 11h490q49 0 58 -11t9 -52v-154q0 -37 -9 -49t-58 -12h-434q-29 0 -39.5 -9.5t-12.5 -31.5q-2 -31 -3 -90.5t-3 -112.5h432q39 0 52.5 -11.5t13.5 -48.5v-143q0 -33 -13.5 -44t-52.5 -11h-436 q0 -63 3 -123t9 -107q4 -23 14.5 -29.5t30.5 -6.5h437q47 0 58 -11.5t11 -52.5v-152q0 -37 -11 -50t-58 -13h-494q-76 0 -131 9t-93 34t-58.5 66t-26.5 106q-10 94 -12.5 193.5t-2.5 201.5zM272 1526q0 18 17 35l180 208q16 16 28.5 21.5t28.5 5.5h125q16 0 28.5 -5 t29.5 -22l180 -208q16 -16 16 -35q0 -12 -14 -27l-74 -61q-23 -18 -43 -19q-25 0 -45 23l-135 145h-10l-135 -145q-20 -23 -45 -23q-23 0 -43 19l-74 61q-14 14 -15 27z" />
<glyph unicode="&#xcb;" horiz-adv-x="1079" d="M106 610v125q0 66 2.5 155t10.5 175q6 80 26.5 130t57.5 79t94 40t139 11h490q49 0 58 -11t9 -52v-154q0 -37 -9 -49t-58 -12h-434q-29 0 -39.5 -9.5t-12.5 -31.5q-2 -31 -3 -90.5t-3 -112.5h432q39 0 52.5 -11.5t13.5 -48.5v-143q0 -33 -13.5 -44t-52.5 -11h-436 q0 -63 3 -123t9 -107q4 -23 14.5 -29.5t30.5 -6.5h437q47 0 58 -11.5t11 -52.5v-152q0 -37 -11 -50t-58 -13h-494q-76 0 -131 9t-93 34t-58.5 66t-26.5 106q-10 94 -12.5 193.5t-2.5 201.5zM260 1612v16q0 59 38 96t95 37h13q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96 t-95 -37h-13q-57 0 -95 36.5t-38 96.5zM643 1612v16q0 59 38 96t95 37h13q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-13q-57 0 -95 36.5t-38 96.5z" />
<glyph unicode="&#xcc;" horiz-adv-x="614" d="M-12 1645q0 20 14 43l78 127q6 10 15 20t24 10q16 0 39 -14l350 -236q23 -14 22 -36q0 -18 -16 -43l-53 -78q-14 -18 -23.5 -26.5t-21.5 -8.5q-23 0 -43 10l-346 182q-39 20 -39 50zM135 63v1199q0 43 12.5 53t59.5 10h201q47 0 59 -10t12 -53v-1199q0 -43 -12 -53 t-59 -10h-201q-47 0 -59.5 10t-12.5 53z" />
<glyph unicode="&#xcd;" horiz-adv-x="614" d="M90 1559q0 23 22 36l350 236q23 14 39 14q14 0 23.5 -10t15.5 -20l78 -127q14 -23 14 -43q0 -29 -38 -50l-347 -182q-20 -10 -43 -10q-12 0 -21 8t-24 27l-53 78q-16 25 -16 43zM135 63v1199q0 43 12.5 53t59.5 10h201q47 0 59 -10t12 -53v-1199q0 -43 -12 -53t-59 -10 h-201q-47 0 -59.5 10t-12.5 53z" />
<glyph unicode="&#xce;" horiz-adv-x="614" d="M-9 1526q0 18 17 35l180 208q16 16 28.5 21.5t28.5 5.5h125q16 0 28.5 -5t29.5 -22l180 -208q16 -16 16 -35q0 -12 -14 -27l-74 -61q-23 -18 -43 -19q-25 0 -45 23l-135 145h-10l-135 -145q-20 -23 -45 -23q-23 0 -43 19l-74 61q-14 14 -15 27zM135 63v1199q0 43 12.5 53 t59.5 10h201q47 0 59 -10t12 -53v-1199q0 -43 -12 -53t-59 -10h-201q-47 0 -59.5 10t-12.5 53z" />
<glyph unicode="&#xcf;" horiz-adv-x="614" d="M-19 1612v16q0 59 38 96t95 37h13q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-13q-57 0 -95 36.5t-38 96.5zM135 63v1199q0 43 12.5 53t59.5 10h201q47 0 59 -10t12 -53v-1199q0 -43 -12 -53t-59 -10h-201q-47 0 -59.5 10t-12.5 53zM364 1612v16q0 59 38 96 t95 37h13q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-13q-57 0 -95 36.5t-38 96.5z" />
<glyph unicode="&#xd0;" horiz-adv-x="1343" d="M8 606v111q0 29 11.5 43t39.5 14h111v430q0 63 18.5 84t71.5 27q61 6 142 10t180 4h32q164 0 285 -30.5t201 -99.5t118.5 -180.5t38.5 -270.5v-156q0 -311 -167.5 -451.5t-528.5 -140.5h-31q-78 0 -159.5 2t-122.5 6q-78 8 -78 80v461h-111q-29 0 -40 12t-11 45zM514 330 q0 -27 8 -35t25 -10q20 -2 48 -2h44h16q123 0 187.5 80.5t64.5 254.5v101q0 94 -16.5 157.5t-49 102.5t-80.5 54.5t-110 15.5h-14q-18 0 -45 -1t-43 -4q-35 -2 -35 -34v-236h139q29 0 40.5 -14.5t11.5 -42.5v-111q0 -33 -11.5 -45t-40.5 -12h-139v-219z" />
<glyph unicode="&#xd1;" horiz-adv-x="1329" d="M131 70v1187q0 41 16.5 54.5t55.5 13.5h90q49 0 77.5 -11t53.5 -46l450 -672h5v659q0 43 17 56.5t58 13.5h172q41 0 56.5 -13.5t15.5 -56.5v-1187q0 -41 -16.5 -54.5t-55.5 -13.5h-90q-49 0 -77.5 11.5t-53.5 45.5l-450 633h-4v-620q0 -43 -15.5 -56.5t-56.5 -13.5h-172 q-41 0 -58.5 13.5t-17.5 56.5zM311 1513q0 12 6 29q41 98 102.5 146.5t126.5 48.5q41 0 71 -11.5t58 -29.5l39 -25q47 -29 72 -29t45.5 15.5t42.5 50.5q10 16 27 16q10 0 26 -10l78 -53q18 -16 19 -29q0 -12 -8.5 -27.5t-16.5 -25.5q-94 -141 -219 -141q-61 0 -115 32 l-41 27q-41 27 -69 27q-18 0 -38 -13.5t-48 -58.5q-12 -18 -29 -18q-10 0 -25 6l-88 49q-16 10 -16 24z" />
<glyph unicode="&#xd2;" horiz-adv-x="1302" d="M94 584v157q0 178 38 304t134 202q59 47 151.5 71.5t225.5 24.5h16q133 0 225.5 -24.5t151.5 -71.5q96 -76 134 -201.5t38 -304.5v-157q0 -178 -37.5 -304t-134.5 -202q-59 -47 -151 -71.5t-226 -24.5h-16q-133 0 -225 24.5t-152 71.5q-96 76 -134 202t-38 304zM357 1645 q0 20 14 43l78 127q6 10 15 20t24 10q16 0 39 -14l350 -236q23 -14 22 -36q0 -18 -16 -43l-53 -78q-14 -18 -23.5 -26.5t-21.5 -8.5q-23 0 -43 10l-346 182q-39 20 -39 50zM444 625q0 -115 10.5 -193t47.5 -115q25 -25 56.5 -37t78.5 -12h29q47 0 78.5 12.5t56.5 36.5 q37 37 47 115t10 193v75q0 115 -10 193t-47 115q-25 25 -56.5 37t-78.5 12h-29q-47 0 -79 -12.5t-56 -36.5q-37 -37 -47.5 -115t-10.5 -193v-75z" />
<glyph unicode="&#xd3;" horiz-adv-x="1302" d="M94 584v157q0 178 38 304t134 202q59 47 151.5 71.5t225.5 24.5h16q133 0 225.5 -24.5t151.5 -71.5q96 -76 134 -201.5t38 -304.5v-157q0 -178 -37.5 -304t-134.5 -202q-59 -47 -151 -71.5t-226 -24.5h-16q-133 0 -225 24.5t-152 71.5q-96 76 -134 202t-38 304zM414 1559 q0 23 22 36l350 236q23 14 39 14q14 0 23.5 -10t15.5 -20l78 -127q14 -23 14 -43q0 -29 -38 -50l-347 -182q-20 -10 -43 -10q-12 0 -21 8t-24 27l-53 78q-16 25 -16 43zM444 625q0 -115 10.5 -193t47.5 -115q25 -25 56.5 -37t78.5 -12h29q47 0 78.5 12.5t56.5 36.5 q37 37 47 115t10 193v75q0 115 -10 193t-47 115q-25 25 -56.5 37t-78.5 12h-29q-47 0 -79 -12.5t-56 -36.5q-37 -37 -47.5 -115t-10.5 -193v-75z" />
<glyph unicode="&#xd4;" horiz-adv-x="1302" d="M94 584v157q0 178 38 304t134 202q59 47 151.5 71.5t225.5 24.5h16q133 0 225.5 -24.5t151.5 -71.5q96 -76 134 -201.5t38 -304.5v-157q0 -178 -37.5 -304t-134.5 -202q-59 -47 -151 -71.5t-226 -24.5h-16q-133 0 -225 24.5t-152 71.5q-96 76 -134 202t-38 304zM329 1526 q0 18 17 35l180 208q16 16 28.5 21.5t28.5 5.5h125q16 0 28.5 -5t29.5 -22l180 -208q16 -16 16 -35q0 -12 -14 -27l-74 -61q-23 -18 -43 -19q-25 0 -45 23l-135 145h-10l-135 -145q-20 -23 -45 -23q-23 0 -43 19l-74 61q-14 14 -15 27zM444 625q0 -115 10.5 -193t47.5 -115 q25 -25 56.5 -37t78.5 -12h29q47 0 78.5 12.5t56.5 36.5q37 37 47 115t10 193v75q0 115 -10 193t-47 115q-25 25 -56.5 37t-78.5 12h-29q-47 0 -79 -12.5t-56 -36.5q-37 -37 -47.5 -115t-10.5 -193v-75z" />
<glyph unicode="&#xd5;" horiz-adv-x="1302" d="M94 584v157q0 178 38 304t134 202q59 47 151.5 71.5t225.5 24.5h16q133 0 225.5 -24.5t151.5 -71.5q96 -76 134 -201.5t38 -304.5v-157q0 -178 -37.5 -304t-134.5 -202q-59 -47 -151 -71.5t-226 -24.5h-16q-133 0 -225 24.5t-152 71.5q-96 76 -134 202t-38 304zM289 1513 q0 12 6 29q41 98 102.5 146.5t126.5 48.5q41 0 71 -11.5t58 -29.5l39 -25q47 -29 72 -29t45.5 15.5t42.5 50.5q10 16 27 16q10 0 26 -10l78 -53q18 -16 19 -29q0 -12 -8.5 -27.5t-16.5 -25.5q-94 -141 -219 -141q-61 0 -115 32l-41 27q-41 27 -69 27q-18 0 -38 -13.5 t-48 -58.5q-12 -18 -29 -18q-10 0 -25 6l-88 49q-16 10 -16 24zM444 625q0 -115 10.5 -193t47.5 -115q25 -25 56.5 -37t78.5 -12h29q47 0 78.5 12.5t56.5 36.5q37 37 47 115t10 193v75q0 115 -10 193t-47 115q-25 25 -56.5 37t-78.5 12h-29q-47 0 -79 -12.5t-56 -36.5 q-37 -37 -47.5 -115t-10.5 -193v-75z" />
<glyph unicode="&#xd6;" horiz-adv-x="1302" d="M94 584v157q0 178 38 304t134 202q59 47 151.5 71.5t225.5 24.5h16q133 0 225.5 -24.5t151.5 -71.5q96 -76 134 -201.5t38 -304.5v-157q0 -178 -37.5 -304t-134.5 -202q-59 -47 -151 -71.5t-226 -24.5h-16q-133 0 -225 24.5t-152 71.5q-96 76 -134 202t-38 304zM311 1612 v16q0 59 38 96t95 37h13q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-13q-57 0 -95 36.5t-38 96.5zM444 625q0 -115 10.5 -193t47.5 -115q25 -25 56.5 -37t78.5 -12h29q47 0 78.5 12.5t56.5 36.5q37 37 47 115t10 193v75q0 115 -10 193t-47 115q-25 25 -56.5 37 t-78.5 12h-29q-47 0 -79 -12.5t-56 -36.5q-37 -37 -47.5 -115t-10.5 -193v-75zM694 1612v16q0 59 38 96t95 37h13q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-13q-57 0 -95 36.5t-38 96.5z" />
<glyph unicode="&#xd7;" d="M160.5 767.5q5.5 23.5 36.5 53.5l104 105q31 31 54.5 36t58.5 -30l192 -193l187 187q29 31 53 27.5t55 -33.5l90 -91q29 -29 30 -51t-30 -51l-186 -186l192 -193q35 -35 30 -58.5t-36 -53.5l-104 -105q-31 -31 -54.5 -36t-58.5 30l-192 192l-187 -186q-29 -31 -51 -30 t-51 30l-90 90q-31 31 -34 55.5t28 53.5l186 186l-193 193q-35 35 -29.5 58.5z" />
<glyph unicode="&#xd8;" horiz-adv-x="1302" d="M94 584v157q0 178 38 304t134 202q59 47 150.5 71.5t224.5 24.5h21q150 0 253 -34l78 141q12 23 22.5 31t22.5 8q18 0 47 -14l43 -23q29 -16 38.5 -26.5t9.5 -20.5q0 -14 -8.5 -31.5t-22.5 -42.5l-68 -123q72 -78 101.5 -194.5t29.5 -272.5v-157q0 -178 -37.5 -304 t-134.5 -202q-59 -47 -150 -71.5t-224 -24.5h-21q-135 0 -227 24l-64 -112q-23 -43 -47 -44q-10 0 -20.5 4.5t-26.5 12.5l-59 29q-20 10 -26.5 19t-6.5 22q0 12 5 24t11 25l62 112q-82 78 -115 199t-33 287zM444 542q0 -38 5 -71l313 565q-35 20 -96 21h-29q-47 0 -79 -12.5 t-56 -36.5q-37 -37 -47.5 -115t-10.5 -193v-75v-83zM563 279q16 -6 33.5 -8.5t40.5 -2.5h29q47 0 78.5 12.5t56.5 36.5q37 37 47 115t10 193v75v55.5t-2 51.5z" />
<glyph unicode="&#xd9;" horiz-adv-x="1290" d="M111 463v799q0 43 11 53t60 10h201q49 0 61.5 -10t12.5 -53v-789q0 -61 9 -101t32 -63q41 -41 143 -41h10q102 0 144 41q23 23 32 63t9 101v789q0 43 11 53t60 10h201q49 0 60.5 -10t11.5 -53v-799q0 -135 -28 -219t-97 -150q-125 -113 -398 -112h-24q-143 0 -240.5 28.5 t-159.5 83.5q-70 66 -96 156t-26 213zM373 1645q0 20 14 43l78 127q6 10 15 20t24 10q16 0 39 -14l350 -236q23 -14 22 -36q0 -18 -16 -43l-53 -78q-14 -18 -23.5 -26.5t-21.5 -8.5q-23 0 -43 10l-346 182q-39 20 -39 50z" />
<glyph unicode="&#xda;" horiz-adv-x="1290" d="M111 463v799q0 43 11 53t60 10h201q49 0 61.5 -10t12.5 -53v-789q0 -61 9 -101t32 -63q41 -41 143 -41h10q102 0 144 41q23 23 32 63t9 101v789q0 43 11 53t60 10h201q49 0 60.5 -10t11.5 -53v-799q0 -135 -28 -219t-97 -150q-125 -113 -398 -112h-24q-143 0 -240.5 28.5 t-159.5 83.5q-70 66 -96 156t-26 213zM373 1559q0 23 22 36l350 236q23 14 39 14q14 0 23.5 -10t15.5 -20l78 -127q14 -23 14 -43q0 -29 -38 -50l-347 -182q-20 -10 -43 -10q-12 0 -21 8t-24 27l-53 78q-16 25 -16 43z" />
<glyph unicode="&#xdb;" horiz-adv-x="1290" d="M111 463v799q0 43 11 53t60 10h201q49 0 61.5 -10t12.5 -53v-789q0 -61 9 -101t32 -63q41 -41 143 -41h10q102 0 144 41q23 23 32 63t9 101v789q0 43 11 53t60 10h201q49 0 60.5 -10t11.5 -53v-799q0 -135 -28 -219t-97 -150q-125 -113 -398 -112h-24q-143 0 -240.5 28.5 t-159.5 83.5q-70 66 -96 156t-26 213zM327 1526q0 18 17 35l180 208q16 16 28.5 21.5t28.5 5.5h125q16 0 28.5 -5t29.5 -22l180 -208q16 -16 16 -35q0 -12 -14 -27l-74 -61q-23 -18 -43 -19q-25 0 -45 23l-135 145h-10l-135 -145q-20 -23 -45 -23q-23 0 -43 19l-74 61 q-14 14 -15 27z" />
<glyph unicode="&#xdc;" horiz-adv-x="1290" d="M111 463v799q0 43 11 53t60 10h201q49 0 61.5 -10t12.5 -53v-789q0 -61 9 -101t32 -63q41 -41 143 -41h10q102 0 144 41q23 23 32 63t9 101v789q0 43 11 53t60 10h201q49 0 60.5 -10t11.5 -53v-799q0 -135 -28 -219t-97 -150q-125 -113 -398 -112h-24q-143 0 -240.5 28.5 t-159.5 83.5q-70 66 -96 156t-26 213zM313 1612v16q0 59 38 96t95 37h13q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-13q-57 0 -95 36.5t-38 96.5zM696 1612v16q0 59 38 96t95 37h13q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-13q-57 0 -95 36.5 t-38 96.5z" />
<glyph unicode="&#xdd;" horiz-adv-x="1093" d="M23 1257q-18 68 38 68h215q45 0 61.5 -5t27.5 -44q16 -66 38.5 -142.5t47 -154.5t49 -151.5t47.5 -133.5h10q23 59 47.5 133t48 152t45 154.5t37.5 142.5q10 39 26.5 44t61.5 5h209q57 0 39 -68q-31 -109 -72 -224t-88 -229t-96 -219.5t-94 -191.5v-330q0 -37 -13.5 -50 t-52.5 -13h-215q-39 0 -52 13.5t-13 49.5v328q-45 86 -94.5 191.5t-97.5 220.5t-89 230.5t-71 223.5zM338 1559q0 23 22 36l350 236q23 14 39 14q14 0 23.5 -10t15.5 -20l78 -127q14 -23 14 -43q0 -29 -38 -50l-347 -182q-20 -10 -43 -10q-12 0 -21 8t-24 27l-53 78 q-16 25 -16 43z" />
<glyph unicode="&#xde;" horiz-adv-x="1148" d="M131 63v1217q0 37 17.5 50t54.5 13h200q35 0 53.5 -13t18.5 -50v-147h121q129 0 224 -18.5t157.5 -64.5t94.5 -123t32 -194v-106q0 -117 -37 -195t-106.5 -124t-167 -65.5t-220.5 -19.5h-98v-160q0 -37 -18.5 -50t-53.5 -13h-200q-37 0 -54.5 13.5t-17.5 49.5zM475 514 h92q104 0 145.5 30.5t41.5 108.5v68q0 72 -36 101.5t-114 29.5h-22q-18 0 -51 -1t-56 -3v-334z" />
<glyph unicode="&#xdf;" horiz-adv-x="1202" d="M104 -109v1065q0 125 29 210t89.5 138.5t152.5 77t219 23.5h18q119 0 205 -25.5t143.5 -72.5t85 -113t27.5 -143v-15q0 -125 -36 -195.5t-97 -101.5v-6q113 -33 153 -115.5t40 -193.5v-37q0 -111 -27 -187.5t-76 -125.5t-120.5 -70.5t-161.5 -21.5h-23q-47 0 -100.5 4 t-87.5 10q-35 6 -35 27q0 12 1 27.5t3 37.5l10 121q2 35 31 35q8 0 28 -4q14 -2 37 -4.5t45 -2.5h7q74 0 105.5 38t31.5 132v29q0 98 -48 132t-135 34h-51q-45 0 -45 43v156q0 43 45 43h37q70 0 104.5 30.5t34.5 118.5v19q0 66 -34.5 100.5t-110.5 34.5h-12q-72 0 -113 -39 t-41 -148v-1065q0 -41 -14.5 -52t-55.5 -11h-188q-41 0 -55.5 11t-14.5 52z" />
<glyph unicode="&#xe0;" horiz-adv-x="1040" d="M61 297v22q0 172 98.5 238t305.5 66h14q41 0 81 -2.5t63 -6.5v43q0 31 -6.5 52.5t-28.5 38.5q-18 12 -45 17t-78 5q-53 0 -115.5 -9t-122.5 -26q-39 -10 -52 1.5t-17 37.5l-17 109q-2 10 -3 22.5t-1 18.5q0 20 9.5 31.5t39.5 21.5q66 18 156 31.5t195 13.5 q123 0 204.5 -24.5t130.5 -75.5q78 -80 78 -256v-609q0 -31 -14 -44t-55 -13h-97q-35 0 -50 9t-25 38l-13 37h-4q-31 -47 -100.5 -74.5t-167.5 -27.5h-31q-154 0 -243 79.5t-89 235.5zM240 1378q0 18 12 35l98 127q18 27 41 27q18 0 39 -19l322 -274q18 -12 18 -33 q0 -18 -22 -47l-62 -72q-16 -16 -24.5 -22t-24.5 -6q-20 0 -41 14l-324 221q-16 12 -24 22.5t-8 26.5zM369 309q0 -57 30.5 -75.5t83.5 -18.5h15q35 0 58.5 9t37.5 22q16 16 22.5 37.5t6.5 62.5v57q-16 4 -46 5.5t-53 1.5h-22q-72 0 -102.5 -17.5t-30.5 -75.5v-8z" />
<glyph unicode="&#xe1;" horiz-adv-x="1040" d="M61 297v22q0 172 98.5 238t305.5 66h14q41 0 81 -2.5t63 -6.5v43q0 31 -6.5 52.5t-28.5 38.5q-18 12 -45 17t-78 5q-53 0 -115.5 -9t-122.5 -26q-39 -10 -52 1.5t-17 37.5l-17 109q-2 10 -3 22.5t-1 18.5q0 20 9.5 31.5t39.5 21.5q66 18 156 31.5t195 13.5 q123 0 204.5 -24.5t130.5 -75.5q78 -80 78 -256v-609q0 -31 -14 -44t-55 -13h-97q-35 0 -50 9t-25 38l-13 37h-4q-31 -47 -100.5 -74.5t-167.5 -27.5h-31q-154 0 -243 79.5t-89 235.5zM324 1241q0 20 18 33l322 274q20 18 39 19q23 0 41 -27l98 -127q12 -16 12 -35 q0 -16 -8 -26.5t-25 -22.5l-323 -221q-23 -14 -41 -14q-16 0 -24.5 6t-24.5 22l-62 72q-23 29 -22 47zM369 309q0 -57 30.5 -75.5t83.5 -18.5h15q35 0 58.5 9t37.5 22q16 16 22.5 37.5t6.5 62.5v57q-16 4 -46 5.5t-53 1.5h-22q-72 0 -102.5 -17.5t-30.5 -75.5v-8z" />
<glyph unicode="&#xe2;" horiz-adv-x="1040" d="M61 297v22q0 172 98.5 238t305.5 66h14q41 0 81 -2.5t63 -6.5v43q0 31 -6.5 52.5t-28.5 38.5q-18 12 -45 17t-78 5q-53 0 -115.5 -9t-122.5 -26q-39 -10 -52 1.5t-17 37.5l-17 109q-2 10 -3 22.5t-1 18.5q0 20 9.5 31.5t39.5 21.5q66 18 156 31.5t195 13.5 q123 0 204.5 -24.5t130.5 -75.5q78 -80 78 -256v-609q0 -31 -14 -44t-55 -13h-97q-35 0 -50 9t-25 38l-13 37h-4q-31 -47 -100.5 -74.5t-167.5 -27.5h-31q-154 0 -243 79.5t-89 235.5zM221 1188q0 18 17 35l180 209q16 16 28.5 21t28.5 5h125q16 0 28.5 -5t29.5 -21 l180 -209q16 -16 16 -35q0 -12 -14 -27l-74 -61q-23 -18 -43 -19q-25 0 -45 23l-135 145h-10l-135 -145q-20 -23 -45 -23q-23 0 -43 19l-74 61q-14 14 -15 27zM369 309q0 -57 30.5 -75.5t83.5 -18.5h15q35 0 58.5 9t37.5 22q16 16 22.5 37.5t6.5 62.5v57q-16 4 -46 5.5 t-53 1.5h-22q-72 0 -102.5 -17.5t-30.5 -75.5v-8z" />
<glyph unicode="&#xe3;" horiz-adv-x="1040" d="M61 297v22q0 172 98.5 238t305.5 66h14q41 0 81 -2.5t63 -6.5v43q0 31 -6.5 52.5t-28.5 38.5q-18 12 -45 17t-78 5q-53 0 -115.5 -9t-122.5 -26q-39 -10 -52 1.5t-17 37.5l-17 109q-2 10 -3 22.5t-1 18.5q0 20 9.5 31.5t39.5 21.5q66 18 156 31.5t195 13.5 q123 0 204.5 -24.5t130.5 -75.5q78 -80 78 -256v-609q0 -31 -14 -44t-55 -13h-97q-35 0 -50 9t-25 38l-13 37h-4q-31 -47 -100.5 -74.5t-167.5 -27.5h-31q-154 0 -243 79.5t-89 235.5zM197 1184q0 12 6 28q41 98 102.5 146.5t127.5 48.5q41 0 70.5 -11.5t58.5 -29.5l39 -25 q47 -29 71 -28q25 0 45.5 15t42.5 50q10 16 27 17q10 0 27 -11l77 -53q18 -16 19 -28q0 -12 -8.5 -27.5t-16.5 -26.5q-94 -141 -219 -141q-61 0 -115 33l-41 26q-41 27 -69 27q-18 0 -38 -13.5t-48 -58.5q-12 -18 -29 -18q-10 0 -24 6l-89 49q-16 10 -16 25zM369 309 q0 -57 30.5 -75.5t83.5 -18.5h15q35 0 58.5 9t37.5 22q16 16 22.5 37.5t6.5 62.5v57q-16 4 -46 5.5t-53 1.5h-22q-72 0 -102.5 -17.5t-30.5 -75.5v-8z" />
<glyph unicode="&#xe4;" horiz-adv-x="1040" d="M61 297v22q0 172 98.5 238t305.5 66h14q41 0 81 -2.5t63 -6.5v43q0 31 -6.5 52.5t-28.5 38.5q-18 12 -45 17t-78 5q-53 0 -115.5 -9t-122.5 -26q-39 -10 -52 1.5t-17 37.5l-17 109q-2 10 -3 22.5t-1 18.5q0 20 9.5 31.5t39.5 21.5q66 18 156 31.5t195 13.5 q123 0 204.5 -24.5t130.5 -75.5q78 -80 78 -256v-609q0 -31 -14 -44t-55 -13h-97q-35 0 -50 9t-25 38l-13 37h-4q-31 -47 -100.5 -74.5t-167.5 -27.5h-31q-154 0 -243 79.5t-89 235.5zM224 1274v16q0 59 38 96t95 37h12q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37 h-12q-57 0 -95 36.5t-38 96.5zM369 309q0 -57 30.5 -75.5t83.5 -18.5h15q35 0 58.5 9t37.5 22q16 16 22.5 37.5t6.5 62.5v57q-16 4 -46 5.5t-53 1.5h-22q-72 0 -102.5 -17.5t-30.5 -75.5v-8zM607 1274v16q0 59 37.5 96t95.5 37h12q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96 t-95 -37h-12q-57 0 -95 36.5t-38 96.5z" />
<glyph unicode="&#xe5;" horiz-adv-x="1040" d="M61 297v22q0 172 98.5 238t305.5 66h14q41 0 81 -2.5t63 -6.5v43q0 31 -6.5 52.5t-28.5 38.5q-18 12 -45 17t-78 5q-53 0 -115.5 -9t-122.5 -26q-39 -10 -52 1.5t-17 37.5l-17 109q-2 10 -3 22.5t-1 18.5q0 20 9.5 31.5t39.5 21.5q66 18 156 31.5t195 13.5 q123 0 204.5 -24.5t130.5 -75.5q78 -80 78 -256v-609q0 -31 -14 -44t-55 -13h-97q-35 0 -50 9t-25 38l-13 37h-4q-31 -47 -100.5 -74.5t-167.5 -27.5h-31q-154 0 -243 79.5t-89 235.5zM320 1286q0 45 16.5 84t45 66.5t66.5 43t81 15.5q90 0 149.5 -58.5t59.5 -150.5 t-59.5 -150.5t-149.5 -58.5q-43 0 -81 15.5t-66.5 43t-45 66.5t-16.5 84zM369 309q0 -57 30.5 -75.5t83.5 -18.5h15q35 0 58.5 9t37.5 22q16 16 22.5 37.5t6.5 62.5v57q-16 4 -46 5.5t-53 1.5h-22q-72 0 -102.5 -17.5t-30.5 -75.5v-8zM469 1286q0 -29 18.5 -44t41.5 -15 t41 15t18 44t-18.5 44.5t-40.5 15.5q-23 0 -41.5 -15.5t-18.5 -44.5z" />
<glyph unicode="&#xe6;" horiz-adv-x="1572" d="M61 297v22q0 172 98.5 238t305.5 66h14q41 0 81 -2.5t63 -6.5v43q0 31 -6.5 52.5t-28.5 38.5q-18 12 -45 17t-78 5q-53 0 -115.5 -9t-122.5 -26q-39 -10 -52 1.5t-17 37.5l-17 109q-2 12 -3 23.5t-1 17.5q0 20 9.5 31.5t39.5 21.5q66 18 156 31.5t195 13.5 q90 0 160.5 -25.5t99.5 -72.5q39 45 111.5 71.5t158.5 26.5h29q111 0 189.5 -27.5t119.5 -74.5q53 -59 75.5 -146.5t22.5 -220.5v-107q0 -33 -14 -45t-45 -12h-492v-16q0 -72 41 -99.5t150 -27.5h22q49 0 113.5 8t103.5 18q8 2 18.5 4.5t20.5 2.5q31 0 37 -35l17 -103 q4 -16 6 -30.5t2 -24.5q0 -20 -10.5 -32.5t-42.5 -22.5q-61 -23 -147.5 -36t-162.5 -13h-39q-123 0 -204.5 29.5t-131.5 82.5h-4q-47 -55 -114.5 -83.5t-190.5 -28.5h-30q-344 0 -345 315zM369 309q0 -57 30.5 -75.5t83.5 -18.5h15q35 0 58.5 9t37.5 22q16 16 22.5 37.5 t6.5 62.5v57q-16 4 -46 5.5t-53 1.5h-22q-72 0 -102.5 -17.5t-30.5 -75.5v-8zM952 602h226q25 0 24 27v33q0 61 -20 94q-25 37 -95 37h-18q-59 0 -86 -29q-31 -35 -31 -111v-51z" />
<glyph unicode="&#xe7;" horiz-adv-x="929" d="M72 434v119q0 76 6 131t19.5 98t33 74t45.5 57q53 53 134 81t204 28h21q82 0 169 -18.5t146 -49.5q39 -20 39 -51q0 -12 -4 -26.5t-13 -34.5l-38 -99q-14 -37 -43 -36q-12 0 -37 10q-39 16 -86 28.5t-88 12.5h-17q-78 0 -117 -39q-31 -31 -38 -75t-7 -114v-69 q0 -70 9.5 -108t31.5 -64q39 -43 133 -43h17q41 0 90 8t88 18q8 2 18.5 4.5t20.5 2.5q31 0 37 -35l18 -105q4 -16 6 -30.5t2 -24.5q0 -20 -10 -32.5t-43 -22.5q-55 -20 -116.5 -31.5t-137.5 -15.5l-6 -25l-16 -66q31 6 71 7h4q72 0 120 -43t48 -136v-6q0 -111 -61 -161 t-178 -50h-10q-51 0 -106.5 8.5t-90.5 18.5q-20 8 -28.5 15t-8.5 24q0 6 1 16t3 21l8 51q6 31 27 31q6 0 18 -4q23 -8 61 -14.5t77 -6.5h6q59 0 59 41v4q0 18 -14 30.5t-45 12.5h-6q-16 0 -35 -2t-37 -6q-29 -6 -43 12l-23 29q-16 23 -16 31q0 12 8 39l37 131v6 q-141 18 -213 94q-59 59 -81.5 144t-22.5 206z" />
<glyph unicode="&#xe8;" horiz-adv-x="1021" d="M72 438v115q0 154 34.5 242t88.5 135q104 92 321 92h29q111 0 189.5 -27.5t119.5 -74.5q53 -59 75.5 -146.5t22.5 -220.5v-107q0 -33 -14 -45t-45 -12h-492v-16q0 -72 41 -99.5t150 -27.5h22q49 0 114 8t103 18q8 2 18.5 4.5t20.5 2.5q31 0 37 -35l17 -103q4 -16 6 -30.5 t2 -24.5q0 -20 -10.5 -32.5t-42.5 -22.5q-61 -23 -147.5 -36t-162.5 -13h-39q-123 0 -213 25.5t-143 78.5q-61 61 -81.5 144t-20.5 208zM228 1378q0 18 12 35l98 127q18 27 41 27q18 0 39 -19l322 -274q18 -12 18 -33q0 -18 -22 -47l-62 -72q-16 -16 -24.5 -22t-24.5 -6 q-20 0 -41 14l-324 221q-16 12 -24 22.5t-8 26.5zM401 602h226q25 0 24 27v33q0 61 -20 94q-25 37 -94 37h-19q-59 0 -86 -29q-31 -35 -31 -111v-51z" />
<glyph unicode="&#xe9;" horiz-adv-x="1021" d="M72 438v115q0 154 34.5 242t88.5 135q104 92 321 92h29q111 0 189.5 -27.5t119.5 -74.5q53 -59 75.5 -146.5t22.5 -220.5v-107q0 -33 -14 -45t-45 -12h-492v-16q0 -72 41 -99.5t150 -27.5h22q49 0 114 8t103 18q8 2 18.5 4.5t20.5 2.5q31 0 37 -35l17 -103q4 -16 6 -30.5 t2 -24.5q0 -20 -10.5 -32.5t-42.5 -22.5q-61 -23 -147.5 -36t-162.5 -13h-39q-123 0 -213 25.5t-143 78.5q-61 61 -81.5 144t-20.5 208zM334 1241q0 20 18 33l322 274q20 18 39 19q23 0 41 -27l98 -127q12 -16 12 -35q0 -16 -8 -26.5t-25 -22.5l-323 -221q-23 -14 -41 -14 q-16 0 -24.5 6t-24.5 22l-62 72q-23 29 -22 47zM401 602h226q25 0 24 27v33q0 61 -20 94q-25 37 -94 37h-19q-59 0 -86 -29q-31 -35 -31 -111v-51z" />
<glyph unicode="&#xea;" horiz-adv-x="1021" d="M72 438v115q0 154 34.5 242t88.5 135q104 92 321 92h29q111 0 189.5 -27.5t119.5 -74.5q53 -59 75.5 -146.5t22.5 -220.5v-107q0 -33 -14 -45t-45 -12h-492v-16q0 -72 41 -99.5t150 -27.5h22q49 0 114 8t103 18q8 2 18.5 4.5t20.5 2.5q31 0 37 -35l17 -103q4 -16 6 -30.5 t2 -24.5q0 -20 -10.5 -32.5t-42.5 -22.5q-61 -23 -147.5 -36t-162.5 -13h-39q-123 0 -213 25.5t-143 78.5q-61 61 -81.5 144t-20.5 208zM211 1188q0 18 17 35l180 209q16 16 28.5 21t28.5 5h125q16 0 28.5 -5t29.5 -21l180 -209q16 -16 16 -35q0 -12 -14 -27l-74 -61 q-23 -18 -43 -19q-25 0 -45 23l-135 145h-10l-135 -145q-20 -23 -45 -23q-23 0 -43 19l-74 61q-14 14 -15 27zM401 602h226q25 0 24 27v33q0 61 -20 94q-25 37 -94 37h-19q-59 0 -86 -29q-31 -35 -31 -111v-51z" />
<glyph unicode="&#xeb;" horiz-adv-x="1021" d="M72 438v115q0 154 34.5 242t88.5 135q104 92 321 92h29q111 0 189.5 -27.5t119.5 -74.5q53 -59 75.5 -146.5t22.5 -220.5v-107q0 -33 -14 -45t-45 -12h-492v-16q0 -72 41 -99.5t150 -27.5h22q49 0 114 8t103 18q8 2 18.5 4.5t20.5 2.5q31 0 37 -35l17 -103q4 -16 6 -30.5 t2 -24.5q0 -20 -10.5 -32.5t-42.5 -22.5q-61 -23 -147.5 -36t-162.5 -13h-39q-123 0 -213 25.5t-143 78.5q-61 61 -81.5 144t-20.5 208zM193 1274v16q0 59 38 96t95 37h12q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-12q-57 0 -95 36.5t-38 96.5zM401 602h226 q25 0 24 27v33q0 61 -20 94q-25 37 -94 37h-19q-59 0 -86 -29q-31 -35 -31 -111v-51zM576 1274v16q0 59 37.5 96t95.5 37h12q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-12q-57 0 -95 36.5t-38 96.5z" />
<glyph unicode="&#xec;" horiz-adv-x="544" d="M-37 1378q0 18 12 35l98 127q18 27 41 27q18 0 39 -19l322 -274q18 -12 18 -33q0 -18 -22 -47l-62 -72q-16 -16 -24.5 -22t-24.5 -6q-20 0 -41 14l-324 221q-16 12 -24 22.5t-8 26.5zM109 59v885q0 29 14 44.5t55 15.5h189q41 0 55 -15.5t14 -44.5v-885q0 -29 -14 -44 t-55 -15h-189q-41 0 -55 15.5t-14 43.5z" />
<glyph unicode="&#xed;" horiz-adv-x="544" d="M90 1241q0 20 18 33l322 274q20 18 39 19q23 0 41 -27l98 -127q12 -16 12 -35q0 -16 -8 -26.5t-25 -22.5l-323 -221q-23 -14 -41 -14q-16 0 -24.5 6t-24.5 22l-62 72q-23 29 -22 47zM109 59v885q0 29 14 44.5t55 15.5h189q41 0 55 -15.5t14 -44.5v-885q0 -29 -14 -44 t-55 -15h-189q-41 0 -55 15.5t-14 43.5z" />
<glyph unicode="&#xee;" horiz-adv-x="544" d="M-41 1188q0 18 17 35l180 209q16 16 28.5 21t28.5 5h125q16 0 28.5 -5t29.5 -21l180 -209q16 -16 16 -35q0 -12 -14 -27l-74 -61q-23 -18 -43 -19q-25 0 -45 23l-135 145h-10l-135 -145q-20 -23 -45 -23q-23 0 -43 19l-74 61q-14 14 -15 27zM109 59v885q0 29 14 44.5 t55 15.5h189q41 0 55 -15.5t14 -44.5v-885q0 -29 -14 -44t-55 -15h-189q-41 0 -55 15.5t-14 43.5z" />
<glyph unicode="&#xef;" horiz-adv-x="544" d="M-53 1274v16q0 59 38 96t95 37h12q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-12q-57 0 -95 36.5t-38 96.5zM109 59v885q0 29 14 44.5t55 15.5h189q41 0 55 -15.5t14 -44.5v-885q0 -29 -14 -44t-55 -15h-189q-41 0 -55 15.5t-14 43.5zM330 1274v16q0 59 37.5 96 t95.5 37h12q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-12q-57 0 -95 36.5t-38 96.5z" />
<glyph unicode="&#xf0;" horiz-adv-x="1083" d="M72 420v104q0 127 21.5 208t80.5 136q43 41 113.5 68t158.5 27h25q53 0 104.5 -11.5t86.5 -27.5h4q-12 59 -44 109t-92 97l-57 -83q-14 -18 -27.5 -18.5t-35.5 15.5l-70 46q-33 25 -10 55l53 76l-107 51q-25 8 -24 33q0 14 14 34l64 105q12 18 28 18t35 -8 q31 -12 60.5 -24.5t58.5 -26.5l63 90q12 16 25.5 18t36.5 -12l59 -39q25 -16 31 -29.5t-10 -35.5l-53 -76q80 -49 142 -113.5t103 -153.5t62.5 -205t21.5 -265v-95v-92q0 -121 -20.5 -188.5t-59.5 -110.5q-55 -59 -138 -86.5t-232 -27.5h-35q-131 0 -205 23.5t-125 68.5 q-57 49 -81.5 136t-24.5 210zM401 446q0 -78 5.5 -117.5t25.5 -62.5q25 -29 94 -28h23q59 0 88 26q29 29 29 98v201q0 47 -6.5 69.5t-18.5 39.5q-27 33 -96 33h-21q-66 0 -94 -35q-20 -23 -24.5 -65t-4.5 -128v-31z" />
<glyph unicode="&#xf1;" horiz-adv-x="1087" d="M104 59v887q0 31 14.5 44.5t55.5 13.5h96q35 0 50.5 -9.5t25.5 -38.5l12 -38h7q98 104 280 104h17q180 0 260 -84q41 -43 58 -104.5t17 -165.5v-609q0 -29 -14 -44t-55 -15h-189q-41 0 -55 15.5t-14 43.5v580q0 70 -23 94q-25 27 -90 27h-12q-66 0 -90 -27 q-23 -25 -23 -94v-580q0 -29 -14.5 -44t-55.5 -15h-188q-41 0 -55.5 15.5t-14.5 43.5zM213 1184q0 12 6 28q41 98 102.5 146.5t127.5 48.5q41 0 70.5 -11.5t58.5 -29.5l39 -25q47 -29 71 -28q25 0 45.5 15t42.5 50q10 16 27 17q10 0 27 -11l77 -53q18 -16 19 -28 q0 -12 -8.5 -27.5t-16.5 -26.5q-94 -141 -219 -141q-61 0 -115 33l-41 26q-41 27 -69 27q-18 0 -38 -13.5t-48 -58.5q-12 -18 -29 -18q-10 0 -24 6l-89 49q-16 10 -16 25z" />
<glyph unicode="&#xf2;" horiz-adv-x="1056" d="M72 436v131q0 78 8 137.5t24.5 103.5t40 77t56.5 57q45 35 125 57.5t190 22.5h25q111 0 190.5 -22.5t124.5 -57.5q33 -25 56.5 -57.5t40 -76.5t24.5 -102.5t8 -136.5v-133q0 -78 -8 -137t-24.5 -103t-40 -77t-56.5 -58q-45 -35 -125 -57t-190 -22h-25q-111 0 -190.5 22.5 t-124.5 56.5q-33 25 -56.5 58t-40 77t-24.5 103t-8 137zM246 1378q0 18 12 35l98 127q18 27 41 27q18 0 39 -19l322 -274q18 -12 18 -33q0 -18 -22 -47l-62 -72q-16 -16 -24.5 -22t-24.5 -6q-20 0 -41 14l-324 221q-16 12 -24 22.5t-8 26.5zM401 422q0 -68 7.5 -100.5 t29.5 -55.5q29 -29 86 -28h8q57 0 86 28q23 23 30 55.5t7 100.5v160q0 68 -7 100.5t-30 54.5q-29 29 -86 29h-8q-57 0 -86 -29q-23 -23 -30 -55.5t-7 -99.5v-160z" />
<glyph unicode="&#xf3;" horiz-adv-x="1056" d="M72 436v131q0 78 8 137.5t24.5 103.5t40 77t56.5 57q45 35 125 57.5t190 22.5h25q111 0 190.5 -22.5t124.5 -57.5q33 -25 56.5 -57.5t40 -76.5t24.5 -102.5t8 -136.5v-133q0 -78 -8 -137t-24.5 -103t-40 -77t-56.5 -58q-45 -35 -125 -57t-190 -22h-25q-111 0 -190.5 22.5 t-124.5 56.5q-33 25 -56.5 58t-40 77t-24.5 103t-8 137zM342 1241q0 20 18 33l322 274q20 18 39 19q23 0 41 -27l98 -127q12 -16 12 -35q0 -16 -8 -26.5t-25 -22.5l-323 -221q-23 -14 -41 -14q-16 0 -24.5 6t-24.5 22l-62 72q-23 29 -22 47zM401 422q0 -68 7.5 -100.5 t29.5 -55.5q29 -29 86 -28h8q57 0 86 28q23 23 30 55.5t7 100.5v160q0 68 -7 100.5t-30 54.5q-29 29 -86 29h-8q-57 0 -86 -29q-23 -23 -30 -55.5t-7 -99.5v-160z" />
<glyph unicode="&#xf4;" horiz-adv-x="1056" d="M72 436v131q0 78 8 137.5t24.5 103.5t40 77t56.5 57q45 35 125 57.5t190 22.5h25q111 0 190.5 -22.5t124.5 -57.5q33 -25 56.5 -57.5t40 -76.5t24.5 -102.5t8 -136.5v-133q0 -78 -8 -137t-24.5 -103t-40 -77t-56.5 -58q-45 -35 -125 -57t-190 -22h-25q-111 0 -190.5 22.5 t-124.5 56.5q-33 25 -56.5 58t-40 77t-24.5 103t-8 137zM219 1188q0 18 17 35l180 209q16 16 28.5 21t28.5 5h125q16 0 28.5 -5t29.5 -21l180 -209q16 -16 16 -35q0 -12 -14 -27l-74 -61q-23 -18 -43 -19q-25 0 -45 23l-135 145h-10l-135 -145q-20 -23 -45 -23q-23 0 -43 19 l-74 61q-14 14 -15 27zM401 422q0 -68 7.5 -100.5t29.5 -55.5q29 -29 86 -28h8q57 0 86 28q23 23 30 55.5t7 100.5v160q0 68 -7 100.5t-30 54.5q-29 29 -86 29h-8q-57 0 -86 -29q-23 -23 -30 -55.5t-7 -99.5v-160z" />
<glyph unicode="&#xf5;" horiz-adv-x="1056" d="M72 436v131q0 78 8 137.5t24.5 103.5t40 77t56.5 57q45 35 125 57.5t190 22.5h25q111 0 190.5 -22.5t124.5 -57.5q33 -25 56.5 -57.5t40 -76.5t24.5 -102.5t8 -136.5v-133q0 -78 -8 -137t-24.5 -103t-40 -77t-56.5 -58q-45 -35 -125 -57t-190 -22h-25q-111 0 -190.5 22.5 t-124.5 56.5q-33 25 -56.5 58t-40 77t-24.5 103t-8 137zM186 1184q0 12 6 28q41 98 102.5 146.5t127.5 48.5q41 0 70.5 -11.5t58.5 -29.5l39 -25q47 -29 71 -28q25 0 45.5 15t42.5 50q10 16 27 17q10 0 27 -11l77 -53q18 -16 19 -28q0 -12 -8.5 -27.5t-16.5 -26.5 q-94 -141 -219 -141q-61 0 -115 33l-41 26q-41 27 -69 27q-18 0 -38 -13.5t-48 -58.5q-12 -18 -29 -18q-10 0 -24 6l-89 49q-16 10 -16 25zM401 422q0 -68 7.5 -100.5t29.5 -55.5q29 -29 86 -28h8q57 0 86 28q23 23 30 55.5t7 100.5v160q0 68 -7 100.5t-30 54.5 q-29 29 -86 29h-8q-57 0 -86 -29q-23 -23 -30 -55.5t-7 -99.5v-160z" />
<glyph unicode="&#xf6;" horiz-adv-x="1056" d="M72 436v131q0 78 8 137.5t24.5 103.5t40 77t56.5 57q45 35 125 57.5t190 22.5h25q111 0 190.5 -22.5t124.5 -57.5q33 -25 56.5 -57.5t40 -76.5t24.5 -102.5t8 -136.5v-133q0 -78 -8 -137t-24.5 -103t-40 -77t-56.5 -58q-45 -35 -125 -57t-190 -22h-25q-111 0 -190.5 22.5 t-124.5 56.5q-33 25 -56.5 58t-40 77t-24.5 103t-8 137zM201 1274v16q0 59 38 96t95 37h12q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-12q-57 0 -95 36.5t-38 96.5zM401 422q0 -68 7.5 -100.5t29.5 -55.5q29 -29 86 -28h8q57 0 86 28q23 23 30 55.5t7 100.5v160 q0 68 -7 100.5t-30 54.5q-29 29 -86 29h-8q-57 0 -86 -29q-23 -23 -30 -55.5t-7 -99.5v-160zM584 1274v16q0 59 37.5 96t95.5 37h12q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-12q-57 0 -95 36.5t-38 96.5z" />
<glyph unicode="&#xf7;" d="M100 461v127q0 43 15.5 62.5t56.5 19.5h844q41 0 56 -19.5t15 -62.5v-127q0 -41 -15 -57.5t-56 -16.5h-844q-41 0 -56.5 16.5t-15.5 57.5zM422 129v14q0 92 46 126t112 34h24q72 0 114 -33.5t42 -126.5v-14q0 -84 -42 -124t-114 -40h-24q-94 0 -126 40t-32 124zM422 915 v15q0 92 46 126t112 34h24q72 0 114 -34t42 -126v-15q0 -84 -42 -123.5t-114 -39.5h-24q-94 0 -126 39.5t-32 123.5z" />
<glyph unicode="&#xf8;" horiz-adv-x="1056" d="M72 436v131q0 78 8 137.5t24.5 103.5t40 77t56.5 57q45 35 125 57.5t190 22.5h25q70 0 127 -9t104 -26l70 105q14 23 21 28.5t16 5.5q6 0 13 -2t21 -12l33 -22q16 -10 22.5 -18.5t6.5 -14.5q0 -10 -6 -20.5t-17 -24.5l-65 -99q49 -49 73.5 -127.5t24.5 -216.5v-133 q0 -78 -8 -137t-24.5 -103t-40 -77t-56.5 -58q-45 -35 -125 -57t-190 -22h-25q-66 0 -121 8t-98 22l-61 -92q-14 -23 -21.5 -29t-15.5 -6q-6 0 -13.5 2t-21.5 13l-33 22q-16 10 -22.5 18.5t-6.5 14.5q0 10 6.5 20.5t16.5 24.5l53 80q-53 51 -79.5 132t-26.5 224zM401 420 l211 321q-29 25 -80 25h-8q-57 0 -86 -29q-23 -23 -30 -55.5t-7 -99.5v-160v-2zM455 252q23 -14 69 -14h8q57 -1 86 28q23 23 30 55.5t7 100.5v137z" />
<glyph unicode="&#xf9;" horiz-adv-x="1060" d="M90 338v606q0 29 14.5 44.5t55.5 15.5h188q41 0 55.5 -15.5t14.5 -44.5v-577q0 -70 22 -95q25 -27 90 -26q66 0 91 26q23 25 22 95v577q0 29 14.5 44.5t55.5 15.5h188q41 0 55.5 -15.5t14.5 -44.5v-606q0 -98 -22.5 -162.5t-69.5 -107.5q-55 -49 -140.5 -67.5 t-208.5 -18.5t-207.5 18t-140.5 68q-47 43 -69.5 107.5t-22.5 162.5zM244 1378q0 18 12 35l98 127q18 27 41 27q18 0 39 -19l322 -274q18 -12 18 -33q0 -18 -22 -47l-62 -72q-16 -16 -24.5 -22t-24.5 -6q-20 0 -41 14l-324 221q-16 12 -24 22.5t-8 26.5z" />
<glyph unicode="&#xfa;" horiz-adv-x="1060" d="M90 338v606q0 29 14.5 44.5t55.5 15.5h188q41 0 55.5 -15.5t14.5 -44.5v-577q0 -70 22 -95q25 -27 90 -26q66 0 91 26q23 25 22 95v577q0 29 14.5 44.5t55.5 15.5h188q41 0 55.5 -15.5t14.5 -44.5v-606q0 -98 -22.5 -162.5t-69.5 -107.5q-55 -49 -140.5 -67.5 t-208.5 -18.5t-207.5 18t-140.5 68q-47 43 -69.5 107.5t-22.5 162.5zM342 1241q0 20 18 33l322 274q20 18 39 19q23 0 41 -27l98 -127q12 -16 12 -35q0 -16 -8 -26.5t-25 -22.5l-323 -221q-23 -14 -41 -14q-16 0 -24.5 6t-24.5 22l-62 72q-23 29 -22 47z" />
<glyph unicode="&#xfb;" horiz-adv-x="1060" d="M90 338v606q0 29 14.5 44.5t55.5 15.5h188q41 0 55.5 -15.5t14.5 -44.5v-577q0 -70 22 -95q25 -27 90 -26q66 0 91 26q23 25 22 95v577q0 29 14.5 44.5t55.5 15.5h188q41 0 55.5 -15.5t14.5 -44.5v-606q0 -98 -22.5 -162.5t-69.5 -107.5q-55 -49 -140.5 -67.5 t-208.5 -18.5t-207.5 18t-140.5 68q-47 43 -69.5 107.5t-22.5 162.5zM215 1188q0 18 17 35l180 209q16 16 28.5 21t28.5 5h125q16 0 28.5 -5t29.5 -21l180 -209q16 -16 16 -35q0 -12 -14 -27l-74 -61q-23 -18 -43 -19q-25 0 -45 23l-135 145h-10l-135 -145q-20 -23 -45 -23 q-23 0 -43 19l-74 61q-14 14 -15 27z" />
<glyph unicode="&#xfc;" horiz-adv-x="1060" d="M90 338v606q0 29 14.5 44.5t55.5 15.5h188q41 0 55.5 -15.5t14.5 -44.5v-577q0 -70 22 -95q25 -27 90 -26q66 0 91 26q23 25 22 95v577q0 29 14.5 44.5t55.5 15.5h188q41 0 55.5 -15.5t14.5 -44.5v-606q0 -98 -22.5 -162.5t-69.5 -107.5q-55 -49 -140.5 -67.5 t-208.5 -18.5t-207.5 18t-140.5 68q-47 43 -69.5 107.5t-22.5 162.5zM203 1274v16q0 59 38 96t95 37h12q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-12q-57 0 -95 36.5t-38 96.5zM586 1274v16q0 59 37.5 96t95.5 37h12q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96 t-95 -37h-12q-57 0 -95 36.5t-38 96.5z" />
<glyph unicode="&#xfd;" horiz-adv-x="987" d="M14 944q-12 59 62 60h186q35 0 50.5 -9.5t21.5 -40.5q14 -84 33.5 -174t40 -179t42 -174t44.5 -159h10q25 70 49.5 160t44.5 184.5t37.5 182.5t28.5 153q4 33 19 44.5t46 11.5h189q35 0 47 -13.5t8 -48.5q-20 -137 -60.5 -298t-96.5 -331t-130 -344t-162 -336 q-12 -25 -25.5 -34t-31.5 -9q-23 0 -59 17l-105 45q-53 25 -53 51q0 14 10 35q16 33 33.5 67.5t35 68.5t31 62.5t19.5 47.5q-41 16 -73 44.5t-60 94.5q-29 66 -62.5 170t-66.5 221t-59.5 231.5t-43.5 198.5zM283 1241q0 20 18 33l322 274q20 18 39 19q23 0 41 -27l98 -127 q12 -16 12 -35q0 -16 -8 -26.5t-25 -22.5l-323 -221q-23 -14 -41 -14q-16 0 -24.5 6t-24.5 22l-62 72q-23 29 -22 47z" />
<glyph unicode="&#xfe;" horiz-adv-x="1097" d="M104 -291v1618q0 29 14.5 44t55.5 15h188q41 0 55.5 -15t14.5 -44v-358q41 27 95.5 40t134.5 13h18q96 0 165.5 -29.5t106.5 -77.5q37 -47 55.5 -120.5t18.5 -214.5v-154q0 -123 -18.5 -206t-75.5 -138t-126 -77.5t-161 -22.5h-22q-53 0 -104.5 11t-86.5 32v-316 q0 -45 -16.5 -58t-69.5 -13h-156q-51 0 -68.5 13t-17.5 58zM432 362q0 -70 29 -98q29 -27 92 -26h18q41 0 63.5 10t37.5 31q16 27 20 66.5t4 111.5v88q0 72 -3 114.5t-21 67.5q-14 18 -38 27.5t-65 9.5h-14q-37 0 -58.5 -7t-37.5 -24q-16 -16 -21.5 -38.5t-5.5 -69.5v-263z " />
<glyph unicode="&#xff;" horiz-adv-x="987" d="M14 944q-12 59 62 60h186q35 0 50.5 -9.5t21.5 -40.5q14 -84 33.5 -174t40 -179t42 -174t44.5 -159h10q25 70 49.5 160t44.5 184.5t37.5 182.5t28.5 153q4 33 19 44.5t46 11.5h189q35 0 47 -13.5t8 -48.5q-20 -137 -60.5 -298t-96.5 -331t-130 -344t-162 -336 q-12 -25 -25.5 -34t-31.5 -9q-23 0 -59 17l-105 45q-53 25 -53 51q0 14 10 35q16 33 33.5 67.5t35 68.5t31 62.5t19.5 47.5q-41 16 -73 44.5t-60 94.5q-29 66 -62.5 170t-66.5 221t-59.5 231.5t-43.5 198.5zM168 1274v16q0 59 38 96t95 37h12q57 0 95 -36.5t38 -96.5v-16 q0 -59 -38 -96t-95 -37h-12q-57 0 -95 36.5t-38 96.5zM551 1274v16q0 59 37.5 96t95.5 37h12q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-12q-57 0 -95 36.5t-38 96.5z" />
<glyph unicode="&#x152;" horiz-adv-x="1830" d="M94 584v157q0 178 37 306.5t135 199.5q66 49 148 72.5t192 23.5h21q90 0 168.5 -26.5t138.5 -75.5q35 53 98.5 68.5t155.5 15.5h489q49 0 58.5 -11t9.5 -52v-154q0 -37 -9.5 -49t-58.5 -12h-434q-29 0 -39 -9.5t-12 -31.5q-2 -31 -4 -90.5t-2 -112.5h432q39 0 52 -11.5 t13 -48.5v-143q0 -33 -13 -44t-52 -11h-436q0 -63 3 -123t9 -107q2 -16 13.5 -26t31.5 -10h436q47 0 58.5 -11.5t11.5 -52.5v-152q0 -37 -11.5 -50t-58.5 -13h-493q-98 0 -162 19.5t-100 66.5q-51 -53 -131 -78.5t-162 -25.5h-21q-117 0 -198.5 24.5t-141.5 71.5 q-96 76 -134 202t-38 304zM444 625q0 -115 10.5 -193t47.5 -115q25 -25 56.5 -37t86.5 -12h21q53 0 108 17.5t90 44.5q-6 137 -6 280v125q0 53 1 121t5 137q-39 29 -92 46.5t-115 17.5h-20q-47 0 -79 -12.5t-56 -36.5q-37 -37 -47.5 -115t-10.5 -193v-75z" />
<glyph unicode="&#x153;" horiz-adv-x="1605" d="M72 436v131q0 78 8 137.5t24.5 103.5t40 77t56.5 57q45 35 119.5 57.5t185.5 22.5h24q188 0 293 -98q55 49 131 73.5t156 24.5h29q111 0 184.5 -27.5t114.5 -74.5q53 -59 75.5 -146.5t22.5 -220.5v-107q0 -33 -14.5 -45t-44.5 -12h-492v-16q0 -72 41 -99.5t150 -27.5h22 q49 0 113.5 8t103.5 18q8 2 18.5 4.5t20.5 2.5q31 0 37 -35l16 -103q4 -16 6.5 -30.5t2.5 -24.5q0 -20 -10.5 -32.5t-43.5 -22.5q-61 -23 -142 -36t-157 -13h-30q-111 0 -191 29.5t-121 70.5q-43 -43 -120.5 -71.5t-170.5 -28.5h-24q-111 0 -185.5 22.5t-119.5 56.5 q-33 25 -56.5 58t-40 77t-24.5 103t-8 137zM401 422q0 -68 7.5 -100.5t29.5 -55.5q29 -29 86 -28h8q57 0 86 28q23 23 29 51.5t8 83.5v37v144q0 68 -7 100.5t-30 54.5q-29 29 -86 29h-8q-57 0 -86 -29q-23 -23 -30 -55.5t-7 -99.5v-160zM985 602h225q25 0 25 27v33 q0 61 -21 94q-25 37 -94 37h-18q-59 0 -86 -29q-31 -35 -31 -111v-51z" />
<glyph unicode="&#x178;" horiz-adv-x="1093" d="M23 1257q-18 68 38 68h215q45 0 61.5 -5t27.5 -44q16 -66 38.5 -142.5t47 -154.5t49 -151.5t47.5 -133.5h10q23 59 47.5 133t48 152t45 154.5t37.5 142.5q10 39 26.5 44t61.5 5h209q57 0 39 -68q-31 -109 -72 -224t-88 -229t-96 -219.5t-94 -191.5v-330q0 -37 -13.5 -50 t-52.5 -13h-215q-39 0 -52 13.5t-13 49.5v328q-45 86 -94.5 191.5t-97.5 220.5t-89 230.5t-71 223.5zM217 1612v16q0 59 38 96t95 37h13q57 0 95 -36.5t38 -96.5v-16q0 -59 -38 -96t-95 -37h-13q-57 0 -95 36.5t-38 96.5zM600 1612v16q0 59 38 96t95 37h13q57 0 95 -36.5 t38 -96.5v-16q0 -59 -38 -96t-95 -37h-13q-57 0 -95 36.5t-38 96.5z" />
<glyph unicode="&#x2c6;" horiz-adv-x="755" d="M61 1188q0 18 17 35l180 209q16 16 28.5 21t28.5 5h125q16 0 28.5 -5t29.5 -21l180 -209q16 -16 16 -35q0 -12 -14 -27l-74 -61q-23 -18 -43 -19q-25 0 -45 23l-135 145h-10l-135 -145q-20 -23 -45 -23q-23 0 -43 19l-74 61q-14 14 -15 27z" />
<glyph unicode="&#x2dc;" horiz-adv-x="714" d="M4 1184q0 12 6 28q41 98 102.5 146.5t127.5 48.5q41 0 70.5 -11.5t58.5 -29.5l39 -25q47 -29 71 -28q25 0 45.5 15t42.5 50q10 16 27 17q10 0 27 -11l77 -53q18 -16 19 -28q0 -12 -8.5 -27.5t-16.5 -26.5q-94 -141 -219 -141q-61 0 -115 33l-41 26q-41 27 -69 27 q-18 0 -38 -13.5t-48 -58.5q-12 -18 -29 -18q-10 0 -24 6l-89 49q-16 10 -16 25z" />
<glyph unicode="&#x2000;" horiz-adv-x="922" />
<glyph unicode="&#x2001;" horiz-adv-x="1845" />
<glyph unicode="&#x2002;" horiz-adv-x="922" />
<glyph unicode="&#x2003;" horiz-adv-x="1845" />
<glyph unicode="&#x2004;" horiz-adv-x="615" />
<glyph unicode="&#x2005;" horiz-adv-x="461" />
<glyph unicode="&#x2006;" horiz-adv-x="307" />
<glyph unicode="&#x2007;" horiz-adv-x="307" />
<glyph unicode="&#x2008;" horiz-adv-x="230" />
<glyph unicode="&#x2009;" horiz-adv-x="369" />
<glyph unicode="&#x200a;" horiz-adv-x="102" />
<glyph unicode="&#x2010;" horiz-adv-x="745" d="M66 451v153q0 37 17 51.5t64 14.5h451q47 0 64.5 -14.5t17.5 -51.5v-153q0 -37 -15.5 -51.5t-66.5 -14.5h-451q-51 0 -66 14.5t-15 51.5z" />
<glyph unicode="&#x2011;" horiz-adv-x="745" d="M66 451v153q0 37 17 51.5t64 14.5h451q47 0 64.5 -14.5t17.5 -51.5v-153q0 -37 -15.5 -51.5t-66.5 -14.5h-451q-51 0 -66 14.5t-15 51.5z" />
<glyph unicode="&#x2012;" horiz-adv-x="745" d="M66 451v153q0 37 17 51.5t64 14.5h451q47 0 64.5 -14.5t17.5 -51.5v-153q0 -37 -15.5 -51.5t-66.5 -14.5h-451q-51 0 -66 14.5t-15 51.5z" />
<glyph unicode="&#x2013;" horiz-adv-x="1024" d="M0 451v153q0 37 17.5 51.5t64.5 14.5h860q47 0 64.5 -14.5t17.5 -51.5v-153q0 -37 -15.5 -51.5t-66.5 -14.5h-860q-51 0 -66.5 14.5t-15.5 51.5z" />
<glyph unicode="&#x2014;" horiz-adv-x="2048" d="M0 451v153q0 37 17.5 51.5t64.5 14.5h1896q39 0 54.5 -12.5t15.5 -53.5v-153q0 -41 -15.5 -53.5t-54.5 -12.5h-1896q-51 0 -66.5 14.5t-15.5 51.5z" />
<glyph unicode="&#x2018;" horiz-adv-x="563" d="M82 981v27q0 43 13 101.5t40 124t69 135t97 133.5q18 23 41 22q12 0 33 -10l102 -62q18 -10 18.5 -26.5t-12.5 -32.5q-23 -29 -42 -58.5t-40 -60.5q-16 -23 -26.5 -44t-10.5 -40q0 -16 17 -26l20 -15q29 -23 40.5 -50.5t11.5 -78.5v-57q0 -78 -39 -121t-131 -43h-29 q-94 0 -133 45t-39 137z" />
<glyph unicode="&#x2019;" horiz-adv-x="563" d="M67.5 897.5q-0.5 16.5 12.5 32.5q23 29 42 58.5t40 60.5q16 23 26.5 44t10.5 40q0 16 -17 26l-20 15q-29 23 -40.5 50.5t-11.5 78.5v57q0 78 39 121t131 43h29q94 0 133 -45t39 -137v-27q0 -43 -13 -101.5t-40 -124t-69 -135t-97 -133.5q-18 -23 -41 -22q-12 0 -33 10 l-102 62q-18 10 -18.5 26.5z" />
<glyph unicode="&#x201a;" horiz-adv-x="563" d="M67.5 -288.5q-0.5 16.5 12.5 32.5q23 29 42 58.5t40 60.5q16 23 26.5 44t10.5 40q0 16 -17 26l-20 15q-29 23 -40.5 50.5t-11.5 78.5v57q0 78 39 121t131 43h29q94 0 133 -45t39 -137v-27q0 -43 -13 -101.5t-40 -124t-69 -135t-97 -133.5q-18 -23 -41 -22q-12 0 -33 10 l-102 62q-18 10 -18.5 26.5z" />
<glyph unicode="&#x201c;" horiz-adv-x="1073" d="M82 981v27q0 43 13 101.5t40 124t69 135t97 133.5q18 23 41 22q12 0 33 -10l102 -62q18 -10 18.5 -26.5t-12.5 -32.5q-23 -29 -42 -58.5t-40 -60.5q-16 -23 -26.5 -44t-10.5 -40q0 -16 17 -26l20 -15q29 -23 40.5 -50.5t11.5 -78.5v-57q0 -78 -39 -121t-131 -43h-29 q-94 0 -133 45t-39 137zM592 981v27q0 43 13 101.5t40 124t69 135t97 133.5q18 23 41 22q12 0 33 -10l102 -62q18 -10 18.5 -26.5t-12.5 -32.5q-23 -29 -42 -58.5t-40 -60.5q-16 -23 -26.5 -44t-10.5 -40q0 -16 17 -26l20 -15q29 -23 40.5 -50.5t11.5 -78.5v-57 q0 -78 -39 -121t-131 -43h-29q-94 0 -133 45t-39 137z" />
<glyph unicode="&#x201d;" horiz-adv-x="1073" d="M67.5 897.5q-0.5 16.5 12.5 32.5q23 29 42 58.5t40 60.5q16 23 26.5 44t10.5 40q0 16 -17 26l-20 15q-29 23 -40.5 50.5t-11.5 78.5v57q0 78 39 121t131 43h29q94 0 133 -45t39 -137v-27q0 -43 -13 -101.5t-40 -124t-69 -135t-97 -133.5q-18 -23 -41 -22q-12 0 -33 10 l-102 62q-18 10 -18.5 26.5zM577.5 897.5q-0.5 16.5 12.5 32.5q23 29 42 58.5t40 60.5q16 23 26.5 44t10.5 40q0 16 -17 26l-20 15q-29 23 -40.5 50.5t-11.5 78.5v57q0 78 39 121t131 43h29q94 0 133 -45t39 -137v-27q0 -43 -13 -101.5t-40 -124t-69 -135t-97 -133.5 q-18 -23 -41 -22q-12 0 -33 10l-102 62q-18 10 -18.5 26.5z" />
<glyph unicode="&#x201e;" horiz-adv-x="1073" d="M67.5 -288.5q-0.5 16.5 12.5 32.5q23 29 42 58.5t40 60.5q16 23 26.5 44t10.5 40q0 16 -17 26l-20 15q-29 23 -40.5 50.5t-11.5 78.5v57q0 78 39 121t131 43h29q94 0 133 -45t39 -137v-27q0 -43 -13 -101.5t-40 -124t-69 -135t-97 -133.5q-18 -23 -41 -22q-12 0 -33 10 l-102 62q-18 10 -18.5 26.5zM577.5 -288.5q-0.5 16.5 12.5 32.5q23 29 42 58.5t40 60.5q16 23 26.5 44t10.5 40q0 16 -17 26l-20 15q-29 23 -40.5 50.5t-11.5 78.5v57q0 78 39 121t131 43h29q94 0 133 -45t39 -137v-27q0 -43 -13 -101.5t-40 -124t-69 -135t-97 -133.5 q-18 -23 -41 -22q-12 0 -33 10l-102 62q-18 10 -18.5 26.5z" />
<glyph unicode="&#x2022;" horiz-adv-x="811" d="M115 539q0 61 22.5 114.5t61.5 93t91 62t113 22.5t114.5 -22.5t93.5 -62t62.5 -93t22.5 -114.5t-22.5 -113.5t-62.5 -91.5t-93 -61.5t-115 -22.5q-61 0 -113 22.5t-91 61.5t-61.5 91.5t-22.5 113.5z" />
<glyph unicode="&#x2026;" horiz-adv-x="1492" d="M74 152v20q0 94 43 130t121 36h22q78 0 121 -36t43 -130v-20q0 -94 -43 -132t-121 -38h-22q-78 0 -121 37.5t-43 132.5zM571 152v20q0 94 43 130t121 36h23q78 0 121 -36t43 -130v-20q0 -94 -43 -132t-121 -38h-23q-78 0 -121 37.5t-43 132.5zM1069 152v20q0 94 43 130 t121 36h22q78 0 121 -36t43 -130v-20q0 -94 -43 -132t-121 -38h-22q-78 0 -121 37.5t-43 132.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="369" />
<glyph unicode="&#x2039;" horiz-adv-x="802" d="M57 467v25q0 31 8.5 50t47.5 50l403 321q31 25 52.5 24t45.5 -30l97 -106q29 -33 26.5 -55.5t-39.5 -53.5l-249 -213l249 -213q41 -35 39 -57.5t-26 -50.5l-97 -107q-27 -29 -47 -30t-51 24l-403 322q-35 29 -45.5 47t-10.5 53z" />
<glyph unicode="&#x203a;" horiz-adv-x="802" d="M65 209q-2 22 39 57l250 213l-250 213q-37 31 -39 53.5t27 55.5l96 106q25 29 46.5 30t52.5 -24l403 -321q39 -31 47 -50.5t8 -49.5v-25q0 -35 -10 -53.5t-45 -46.5l-403 -322q-31 -25 -51.5 -23.5t-47.5 29.5l-96 107q-25 29 -27 51z" />
<glyph unicode="&#x205f;" horiz-adv-x="461" />
<glyph unicode="&#x20ac;" d="M49 446v99q0 33 10.5 43t38.5 10h125v107h-125q-29 0 -39 10t-10 43v98q0 33 10.5 43t38.5 10h140q35 211 167 322.5t345 111.5h16q92 0 185 -24.5t161 -58.5q35 -18 35 -43q0 -20 -14 -52l-56 -129q-20 -41 -65 -26q-57 20 -116.5 33.5t-127.5 13.5h-6q-84 0 -124 -39 t-50 -109h317q29 0 39 -10t10 -43v-98q0 -33 -10 -43t-39 -10h-332v-107h332q29 0 39 -10t10 -43v-99q0 -33 -10 -43t-39 -10h-303q12 -53 62.5 -89t148.5 -36h6q47 0 104.5 9.5t104.5 21.5q37 10 52.5 1t21.5 -38l16 -78q10 -53 13.5 -69.5t3.5 -24.5q0 -37 -41 -53 q-25 -10 -61 -20.5t-75.5 -17.5t-81.5 -12t-77 -5h-29q-233 0 -367.5 100t-162.5 311h-142q-29 0 -39 10.5t-10 42.5z" />
<glyph unicode="&#x2122;" horiz-adv-x="1542" d="M49 1196v86q0 25 9.5 34t33.5 9h451q25 0 34 -9t9 -34v-86q0 -25 -9.5 -34t-37.5 -9h-117v-426q0 -43 -57 -43h-99q-31 0 -43 10.5t-12 32.5v426h-115q-29 0 -38 9t-9 34zM633 727q4 154 12 294t19 249q4 35 16 45t43 10h199q41 0 54 -11t17 -38q10 -59 24.5 -136 t31.5 -151q16 74 29.5 151t25.5 136q4 27 16.5 38t53.5 11h204q35 0 47.5 -10t16.5 -45q10 -109 16 -249t10 -294q0 -43 -53 -43h-100q-55 0 -55 43q0 104 -3.5 201.5t-7.5 195.5q-8 -43 -18 -89t-20.5 -90t-19.5 -85t-17 -72q-6 -23 -17.5 -31.5t-50.5 -8.5h-117 q-35 0 -47 9t-18 31q-16 66 -39 162.5t-39 182.5q-4 -100 -7 -199.5t-3 -206.5q0 -43 -56 -43h-94q-53 0 -53 43z" />
<glyph unicode="&#x25fc;" horiz-adv-x="1003" d="M0 0v1004h1004v-1004h-1004z" />
<hkern u1="&#x26;" u2="&#x178;" k="61" />
<hkern u1="&#x26;" u2="&#xdd;" k="61" />
<hkern u1="&#x26;" u2="Z" k="-31" />
<hkern u1="&#x26;" u2="Y" k="61" />
<hkern u1="&#x26;" u2="T" k="113" />
<hkern u1="&#x26;" u2="V" k="41" />
<hkern u1="&#x26;" u2="J" k="-61" />
<hkern u1="&#x28;" u2="&#x178;" k="-20" />
<hkern u1="&#x28;" u2="&#xdd;" k="-20" />
<hkern u1="&#x28;" u2="j" k="-244" />
<hkern u1="&#x28;" u2="g" k="-20" />
<hkern u1="&#x28;" u2="Y" k="-20" />
<hkern u1="&#x28;" u2="W" k="-20" />
<hkern u1="&#x28;" u2="T" k="-41" />
<hkern u1="&#x28;" u2="X" k="-20" />
<hkern u1="&#x28;" u2="V" k="-41" />
<hkern u1="&#x2a;" u2="&#x178;" k="-41" />
<hkern u1="&#x2a;" u2="&#x153;" k="72" />
<hkern u1="&#x2a;" u2="&#xf8;" k="72" />
<hkern u1="&#x2a;" u2="&#xf6;" k="72" />
<hkern u1="&#x2a;" u2="&#xf5;" k="72" />
<hkern u1="&#x2a;" u2="&#xf4;" k="72" />
<hkern u1="&#x2a;" u2="&#xf3;" k="72" />
<hkern u1="&#x2a;" u2="&#xf2;" k="72" />
<hkern u1="&#x2a;" u2="&#xf0;" k="72" />
<hkern u1="&#x2a;" u2="&#xef;" k="-12" />
<hkern u1="&#x2a;" u2="&#xee;" k="-12" />
<hkern u1="&#x2a;" u2="&#xed;" k="-12" />
<hkern u1="&#x2a;" u2="&#xec;" k="-12" />
<hkern u1="&#x2a;" u2="&#xeb;" k="72" />
<hkern u1="&#x2a;" u2="&#xea;" k="72" />
<hkern u1="&#x2a;" u2="&#xe9;" k="72" />
<hkern u1="&#x2a;" u2="&#xe8;" k="72" />
<hkern u1="&#x2a;" u2="&#xe7;" k="72" />
<hkern u1="&#x2a;" u2="&#xdd;" k="-41" />
<hkern u1="&#x2a;" u2="&#xc5;" k="143" />
<hkern u1="&#x2a;" u2="&#xc4;" k="143" />
<hkern u1="&#x2a;" u2="&#xc3;" k="143" />
<hkern u1="&#x2a;" u2="&#xc2;" k="143" />
<hkern u1="&#x2a;" u2="&#xc1;" k="143" />
<hkern u1="&#x2a;" u2="&#xc0;" k="143" />
<hkern u1="&#x2a;" u2="o" k="72" />
<hkern u1="&#x2a;" u2="i" k="-12" />
<hkern u1="&#x2a;" u2="e" k="72" />
<hkern u1="&#x2a;" u2="d" k="72" />
<hkern u1="&#x2a;" u2="c" k="72" />
<hkern u1="&#x2a;" u2="Y" k="-41" />
<hkern u1="&#x2a;" u2="W" k="-45" />
<hkern u1="&#x2a;" u2="T" k="-41" />
<hkern u1="&#x2a;" u2="A" k="143" />
<hkern u1="&#x2a;" u2="q" k="72" />
<hkern u1="&#x2a;" u2="V" k="-49" />
<hkern u1="&#x2c;" u2="v" k="113" />
<hkern u1="&#x2c;" u2="V" k="164" />
<hkern u1="&#x2c;" u2="J" k="-29" />
<hkern u1="&#x2e;" u2="v" k="113" />
<hkern u1="&#x2e;" u2="V" k="164" />
<hkern u1="&#x2e;" u2="J" k="-29" />
<hkern u1="&#x2f;" g2="uniFB02" k="31" />
<hkern u1="&#x2f;" g2="uniFB01" k="31" />
<hkern u1="&#x2f;" u2="&#x178;" k="-41" />
<hkern u1="&#x2f;" u2="&#x153;" k="123" />
<hkern u1="&#x2f;" u2="&#xff;" k="41" />
<hkern u1="&#x2f;" u2="&#xfd;" k="41" />
<hkern u1="&#x2f;" u2="&#xfc;" k="102" />
<hkern u1="&#x2f;" u2="&#xfb;" k="102" />
<hkern u1="&#x2f;" u2="&#xfa;" k="102" />
<hkern u1="&#x2f;" u2="&#xf9;" k="102" />
<hkern u1="&#x2f;" u2="&#xf8;" k="123" />
<hkern u1="&#x2f;" u2="&#xf6;" k="123" />
<hkern u1="&#x2f;" u2="&#xf5;" k="123" />
<hkern u1="&#x2f;" u2="&#xf4;" k="123" />
<hkern u1="&#x2f;" u2="&#xf3;" k="123" />
<hkern u1="&#x2f;" u2="&#xf2;" k="123" />
<hkern u1="&#x2f;" u2="&#xf1;" k="123" />
<hkern u1="&#x2f;" u2="&#xf0;" k="123" />
<hkern u1="&#x2f;" u2="&#xeb;" k="123" />
<hkern u1="&#x2f;" u2="&#xea;" k="123" />
<hkern u1="&#x2f;" u2="&#xe9;" k="123" />
<hkern u1="&#x2f;" u2="&#xe8;" k="123" />
<hkern u1="&#x2f;" u2="&#xe7;" k="123" />
<hkern u1="&#x2f;" u2="&#xe6;" k="123" />
<hkern u1="&#x2f;" u2="&#xe5;" k="123" />
<hkern u1="&#x2f;" u2="&#xe4;" k="123" />
<hkern u1="&#x2f;" u2="&#xe3;" k="123" />
<hkern u1="&#x2f;" u2="&#xe2;" k="123" />
<hkern u1="&#x2f;" u2="&#xe1;" k="123" />
<hkern u1="&#x2f;" u2="&#xe0;" k="123" />
<hkern u1="&#x2f;" u2="&#xdd;" k="-41" />
<hkern u1="&#x2f;" u2="z" k="61" />
<hkern u1="&#x2f;" u2="y" k="41" />
<hkern u1="&#x2f;" u2="w" k="41" />
<hkern u1="&#x2f;" u2="u" k="102" />
<hkern u1="&#x2f;" u2="t" k="41" />
<hkern u1="&#x2f;" u2="s" k="123" />
<hkern u1="&#x2f;" u2="r" k="123" />
<hkern u1="&#x2f;" u2="o" k="123" />
<hkern u1="&#x2f;" u2="n" k="123" />
<hkern u1="&#x2f;" u2="g" k="133" />
<hkern u1="&#x2f;" u2="f" k="31" />
<hkern u1="&#x2f;" u2="e" k="123" />
<hkern u1="&#x2f;" u2="d" k="123" />
<hkern u1="&#x2f;" u2="c" k="123" />
<hkern u1="&#x2f;" u2="a" k="123" />
<hkern u1="&#x2f;" u2="Y" k="-41" />
<hkern u1="&#x2f;" u2="W" k="-49" />
<hkern u1="&#x2f;" u2="T" k="-41" />
<hkern u1="&#x2f;" u2="x" k="82" />
<hkern u1="&#x2f;" u2="v" k="41" />
<hkern u1="&#x2f;" u2="q" k="123" />
<hkern u1="&#x2f;" u2="p" k="123" />
<hkern u1="&#x2f;" u2="m" k="123" />
<hkern u1="&#x2f;" u2="V" k="-51" />
<hkern u1="&#x3a;" u2="x" k="-20" />
<hkern u1="&#x3a;" u2="v" k="-20" />
<hkern u1="&#x3b;" u2="x" k="-20" />
<hkern u1="&#x3b;" u2="v" k="-20" />
<hkern u1="A" u2="&#x2122;" k="123" />
<hkern u1="A" u2="&#x203a;" k="-31" />
<hkern u1="A" u2="&#xbb;" k="-31" />
<hkern u1="A" u2="&#x7d;" k="-20" />
<hkern u1="A" u2="v" k="53" />
<hkern u1="A" u2="q" k="8" />
<hkern u1="A" u2="b" k="20" />
<hkern u1="A" u2="]" k="-20" />
<hkern u1="A" u2="\" k="143" />
<hkern u1="A" u2="V" k="78" />
<hkern u1="A" u2="Q" k="20" />
<hkern u1="A" u2="J" k="-51" />
<hkern u1="A" u2="E" k="8" />
<hkern u1="A" u2="&#x3f;" k="51" />
<hkern u1="A" u2="&#x2a;" k="143" />
<hkern u1="B" u2="T" k="61" />
<hkern u1="B" u2="&#x2122;" k="41" />
<hkern u1="B" u2="&#x29;" k="31" />
<hkern u1="C" u2="&#xef;" k="-123" />
<hkern u1="C" u2="&#xee;" k="-102" />
<hkern u1="C" u2="&#xec;" k="-20" />
<hkern u1="C" u2="q" k="41" />
<hkern u1="C" u2="X" k="-20" />
<hkern u1="C" u2="V" k="-31" />
<hkern u1="C" u2="Q" k="29" />
<hkern u1="C" u2="J" k="-31" />
<hkern u1="C" u2="&#x3f;" k="-12" />
<hkern u1="C" u2="&#x29;" k="-41" />
<hkern u1="D" u2="&#x2122;" k="41" />
<hkern u1="D" u2="v" k="-10" />
<hkern u1="D" u2="X" k="45" />
<hkern u1="E" u2="V" k="-14" />
<hkern u1="E" u2="Q" k="10" />
<hkern u1="E" u2="J" k="-20" />
<hkern u1="E" u2="&#x3f;" k="-20" />
<hkern u1="F" u2="&#x2026;" k="252" />
<hkern u1="F" u2="&#x201e;" k="252" />
<hkern u1="F" u2="&#x201a;" k="252" />
<hkern u1="F" u2="&#x178;" k="-31" />
<hkern u1="F" u2="&#x153;" k="41" />
<hkern u1="F" u2="&#xf8;" k="41" />
<hkern u1="F" u2="&#xf6;" k="41" />
<hkern u1="F" u2="&#xf5;" k="41" />
<hkern u1="F" u2="&#xf4;" k="41" />
<hkern u1="F" u2="&#xf3;" k="41" />
<hkern u1="F" u2="&#xf2;" k="41" />
<hkern u1="F" u2="&#xf1;" k="20" />
<hkern u1="F" u2="&#xf0;" k="41" />
<hkern u1="F" u2="&#xeb;" k="41" />
<hkern u1="F" u2="&#xea;" k="41" />
<hkern u1="F" u2="&#xe9;" k="41" />
<hkern u1="F" u2="&#xe8;" k="41" />
<hkern u1="F" u2="&#xe6;" k="49" />
<hkern u1="F" u2="&#xe5;" k="49" />
<hkern u1="F" u2="&#xe4;" k="49" />
<hkern u1="F" u2="&#xe3;" k="49" />
<hkern u1="F" u2="&#xe2;" k="49" />
<hkern u1="F" u2="&#xe1;" k="49" />
<hkern u1="F" u2="&#xe0;" k="49" />
<hkern u1="F" u2="&#xdd;" k="-31" />
<hkern u1="F" u2="&#xc6;" k="184" />
<hkern u1="F" u2="&#xc5;" k="86" />
<hkern u1="F" u2="&#xc4;" k="86" />
<hkern u1="F" u2="&#xc3;" k="86" />
<hkern u1="F" u2="&#xc2;" k="86" />
<hkern u1="F" u2="&#xc1;" k="86" />
<hkern u1="F" u2="&#xc0;" k="86" />
<hkern u1="F" u2="r" k="20" />
<hkern u1="F" u2="o" k="41" />
<hkern u1="F" u2="n" k="20" />
<hkern u1="F" u2="g" k="41" />
<hkern u1="F" u2="e" k="41" />
<hkern u1="F" u2="d" k="41" />
<hkern u1="F" u2="a" k="49" />
<hkern u1="F" u2="Y" k="-31" />
<hkern u1="F" u2="W" k="-31" />
<hkern u1="F" u2="T" k="-20" />
<hkern u1="F" u2="A" k="86" />
<hkern u1="F" u2="&#x2e;" k="252" />
<hkern u1="F" u2="&#x2c;" k="252" />
<hkern u1="F" u2="&#xef;" k="-102" />
<hkern u1="F" u2="&#xee;" k="-82" />
<hkern u1="F" u2="&#xec;" k="-61" />
<hkern u1="F" u2="&#x7d;" k="-29" />
<hkern u1="F" u2="q" k="20" />
<hkern u1="F" u2="p" k="20" />
<hkern u1="F" u2="m" k="20" />
<hkern u1="F" u2="]" k="-29" />
<hkern u1="F" u2="V" k="-31" />
<hkern u1="F" u2="J" k="78" />
<hkern u1="F" u2="&#x3f;" k="-29" />
<hkern u1="F" u2="&#x2f;" k="111" />
<hkern u1="F" u2="&#x29;" k="-29" />
<hkern u1="G" u2="&#xef;" k="-41" />
<hkern u1="G" u2="J" k="-20" />
<hkern u1="I" u2="&#x3f;" k="-41" />
<hkern u1="K" u2="&#x7d;" k="-41" />
<hkern u1="K" u2="x" k="-61" />
<hkern u1="K" u2="v" k="61" />
<hkern u1="K" u2="q" k="45" />
<hkern u1="K" u2="X" k="-20" />
<hkern u1="K" u2="V" k="-25" />
<hkern u1="K" u2="J" k="-53" />
<hkern u1="K" u2="&#x2f;" k="-49" />
<hkern u1="L" u2="&#x2122;" k="217" />
<hkern u1="L" u2="&#xae;" k="41" />
<hkern u1="L" u2="x" k="-20" />
<hkern u1="L" u2="v" k="66" />
<hkern u1="L" u2="\" k="154" />
<hkern u1="L" u2="X" k="-20" />
<hkern u1="L" u2="V" k="78" />
<hkern u1="L" u2="J" k="-20" />
<hkern u1="L" u2="&#x3f;" k="61" />
<hkern u1="L" u2="&#x2f;" k="-41" />
<hkern u1="L" u2="&#x2a;" k="225" />
<hkern u1="L" u2="&#x26;" k="-10" />
<hkern u1="M" u2="&#x178;" k="20" />
<hkern u1="M" u2="&#xdd;" k="20" />
<hkern u1="M" u2="Y" k="20" />
<hkern u1="M" u2="T" k="10" />
<hkern u1="O" u2="X" k="20" />
<hkern u1="O" u2="V" k="20" />
<hkern u1="P" g2="uniFB02" k="-45" />
<hkern u1="P" g2="uniFB01" k="-45" />
<hkern u1="P" u2="&#x2026;" k="225" />
<hkern u1="P" u2="&#x201e;" k="225" />
<hkern u1="P" u2="&#x201a;" k="225" />
<hkern u1="P" u2="&#x153;" k="29" />
<hkern u1="P" u2="&#xff;" k="-37" />
<hkern u1="P" u2="&#xfd;" k="-37" />
<hkern u1="P" u2="&#xf8;" k="29" />
<hkern u1="P" u2="&#xf6;" k="29" />
<hkern u1="P" u2="&#xf5;" k="29" />
<hkern u1="P" u2="&#xf4;" k="29" />
<hkern u1="P" u2="&#xf3;" k="29" />
<hkern u1="P" u2="&#xf2;" k="29" />
<hkern u1="P" u2="&#xf0;" k="29" />
<hkern u1="P" u2="&#xeb;" k="29" />
<hkern u1="P" u2="&#xea;" k="29" />
<hkern u1="P" u2="&#xe9;" k="29" />
<hkern u1="P" u2="&#xe8;" k="29" />
<hkern u1="P" u2="&#xe7;" k="29" />
<hkern u1="P" u2="&#xe6;" k="45" />
<hkern u1="P" u2="&#xe5;" k="45" />
<hkern u1="P" u2="&#xe4;" k="45" />
<hkern u1="P" u2="&#xe3;" k="45" />
<hkern u1="P" u2="&#xe2;" k="45" />
<hkern u1="P" u2="&#xe1;" k="45" />
<hkern u1="P" u2="&#xe0;" k="45" />
<hkern u1="P" u2="&#xc6;" k="176" />
<hkern u1="P" u2="&#xc5;" k="92" />
<hkern u1="P" u2="&#xc4;" k="92" />
<hkern u1="P" u2="&#xc3;" k="92" />
<hkern u1="P" u2="&#xc2;" k="92" />
<hkern u1="P" u2="&#xc1;" k="92" />
<hkern u1="P" u2="&#xc0;" k="92" />
<hkern u1="P" u2="y" k="-37" />
<hkern u1="P" u2="w" k="-37" />
<hkern u1="P" u2="t" k="-45" />
<hkern u1="P" u2="s" k="25" />
<hkern u1="P" u2="o" k="29" />
<hkern u1="P" u2="g" k="25" />
<hkern u1="P" u2="f" k="-45" />
<hkern u1="P" u2="e" k="29" />
<hkern u1="P" u2="c" k="29" />
<hkern u1="P" u2="a" k="45" />
<hkern u1="P" u2="A" k="92" />
<hkern u1="P" u2="&#x2e;" k="225" />
<hkern u1="P" u2="&#x2c;" k="225" />
<hkern u1="P" u2="&#xef;" k="-102" />
<hkern u1="P" u2="&#xee;" k="-102" />
<hkern u1="P" u2="v" k="-37" />
<hkern u1="P" u2="q" k="20" />
<hkern u1="P" u2="X" k="20" />
<hkern u1="P" u2="J" k="92" />
<hkern u1="P" u2="&#x2f;" k="113" />
<hkern u1="P" u2="&#x29;" k="-10" />
<hkern u1="P" u2="&#x26;" k="41" />
<hkern u1="Q" u2="&#x178;" k="20" />
<hkern u1="Q" u2="&#xdd;" k="20" />
<hkern u1="Q" u2="&#xc6;" k="53" />
<hkern u1="Q" u2="&#xc5;" k="20" />
<hkern u1="Q" u2="&#xc4;" k="20" />
<hkern u1="Q" u2="&#xc3;" k="20" />
<hkern u1="Q" u2="&#xc2;" k="20" />
<hkern u1="Q" u2="&#xc1;" k="20" />
<hkern u1="Q" u2="&#xc0;" k="20" />
<hkern u1="Q" u2="j" k="-33" />
<hkern u1="Q" u2="Y" k="20" />
<hkern u1="Q" u2="T" k="20" />
<hkern u1="Q" u2="A" k="20" />
<hkern u1="Q" u2="X" k="20" />
<hkern u1="Q" u2="V" k="20" />
<hkern u1="R" u2="x" k="-20" />
<hkern u1="R" u2="q" k="25" />
<hkern u1="R" u2="\" k="61" />
<hkern u1="R" u2="X" k="-25" />
<hkern u1="R" u2="J" k="-33" />
<hkern u1="S" u2="J" k="-37" />
<hkern u1="T" u2="&#xfc;" k="82" />
<hkern u1="T" u2="&#xfb;" k="82" />
<hkern u1="T" u2="&#xf6;" k="82" />
<hkern u1="T" u2="&#xf5;" k="102" />
<hkern u1="T" u2="&#xf4;" k="102" />
<hkern u1="T" u2="&#xef;" k="-143" />
<hkern u1="T" u2="&#xee;" k="-123" />
<hkern u1="T" u2="&#xec;" k="-102" />
<hkern u1="T" u2="&#xbb;" k="41" />
<hkern u1="T" u2="&#x7d;" k="-41" />
<hkern u1="T" u2="x" k="82" />
<hkern u1="T" u2="v" k="102" />
<hkern u1="T" u2="q" k="143" />
<hkern u1="T" u2="p" k="102" />
<hkern u1="T" u2="m" k="102" />
<hkern u1="T" u2="h" k="20" />
<hkern u1="T" u2="b" k="20" />
<hkern u1="T" u2="]" k="-41" />
<hkern u1="T" u2="\" k="-49" />
<hkern u1="T" u2="V" k="-33" />
<hkern u1="T" u2="Q" k="20" />
<hkern u1="T" u2="M" k="10" />
<hkern u1="T" u2="J" k="61" />
<hkern u1="T" u2="F" k="20" />
<hkern u1="T" u2="E" k="12" />
<hkern u1="T" u2="&#x40;" k="61" />
<hkern u1="T" u2="&#x3f;" k="-41" />
<hkern u1="T" u2="&#x2f;" k="106" />
<hkern u1="T" u2="&#x2a;" k="-41" />
<hkern u1="T" u2="&#x29;" k="-41" />
<hkern u1="V" g2="uniFB02" k="-20" />
<hkern u1="V" g2="uniFB01" k="-20" />
<hkern u1="V" u2="&#x2026;" k="164" />
<hkern u1="V" u2="&#x201e;" k="164" />
<hkern u1="V" u2="&#x201d;" k="-41" />
<hkern u1="V" u2="&#x201a;" k="164" />
<hkern u1="V" u2="&#x2019;" k="-41" />
<hkern u1="V" u2="&#x178;" k="-20" />
<hkern u1="V" u2="&#x153;" k="61" />
<hkern u1="V" u2="&#x152;" k="20" />
<hkern u1="V" u2="&#xfc;" k="16" />
<hkern u1="V" u2="&#xfb;" k="16" />
<hkern u1="V" u2="&#xfa;" k="16" />
<hkern u1="V" u2="&#xf9;" k="16" />
<hkern u1="V" u2="&#xf8;" k="61" />
<hkern u1="V" u2="&#xf6;" k="61" />
<hkern u1="V" u2="&#xf5;" k="61" />
<hkern u1="V" u2="&#xf4;" k="61" />
<hkern u1="V" u2="&#xf3;" k="61" />
<hkern u1="V" u2="&#xf2;" k="61" />
<hkern u1="V" u2="&#xf1;" k="41" />
<hkern u1="V" u2="&#xf0;" k="61" />
<hkern u1="V" u2="&#xeb;" k="61" />
<hkern u1="V" u2="&#xea;" k="61" />
<hkern u1="V" u2="&#xe9;" k="61" />
<hkern u1="V" u2="&#xe8;" k="61" />
<hkern u1="V" u2="&#xe7;" k="61" />
<hkern u1="V" u2="&#xe6;" k="61" />
<hkern u1="V" u2="&#xe5;" k="61" />
<hkern u1="V" u2="&#xe4;" k="61" />
<hkern u1="V" u2="&#xe3;" k="61" />
<hkern u1="V" u2="&#xe2;" k="61" />
<hkern u1="V" u2="&#xe1;" k="61" />
<hkern u1="V" u2="&#xe0;" k="61" />
<hkern u1="V" u2="&#xdd;" k="-20" />
<hkern u1="V" u2="&#xd8;" k="20" />
<hkern u1="V" u2="&#xd6;" k="20" />
<hkern u1="V" u2="&#xd5;" k="20" />
<hkern u1="V" u2="&#xd4;" k="20" />
<hkern u1="V" u2="&#xd3;" k="20" />
<hkern u1="V" u2="&#xd2;" k="20" />
<hkern u1="V" u2="&#xc6;" k="131" />
<hkern u1="V" u2="&#xc5;" k="78" />
<hkern u1="V" u2="&#xc4;" k="78" />
<hkern u1="V" u2="&#xc3;" k="78" />
<hkern u1="V" u2="&#xc2;" k="78" />
<hkern u1="V" u2="&#xc1;" k="78" />
<hkern u1="V" u2="&#xc0;" k="78" />
<hkern u1="V" u2="z" k="20" />
<hkern u1="V" u2="u" k="16" />
<hkern u1="V" u2="t" k="-20" />
<hkern u1="V" u2="s" k="51" />
<hkern u1="V" u2="r" k="41" />
<hkern u1="V" u2="o" k="61" />
<hkern u1="V" u2="n" k="41" />
<hkern u1="V" u2="g" k="61" />
<hkern u1="V" u2="f" k="-20" />
<hkern u1="V" u2="e" k="61" />
<hkern u1="V" u2="d" k="61" />
<hkern u1="V" u2="c" k="61" />
<hkern u1="V" u2="a" k="61" />
<hkern u1="V" u2="Y" k="-20" />
<hkern u1="V" u2="W" k="-20" />
<hkern u1="V" u2="T" k="-33" />
<hkern u1="V" u2="O" k="20" />
<hkern u1="V" u2="A" k="78" />
<hkern u1="V" u2="&#x2e;" k="164" />
<hkern u1="V" u2="&#x2c;" k="164" />
<hkern u1="V" u2="&#xef;" k="-164" />
<hkern u1="V" u2="&#xee;" k="-123" />
<hkern u1="V" u2="&#xec;" k="-102" />
<hkern u1="V" u2="&#xbb;" k="20" />
<hkern u1="V" u2="&#x7d;" k="-41" />
<hkern u1="V" u2="x" k="20" />
<hkern u1="V" u2="q" k="61" />
<hkern u1="V" u2="p" k="41" />
<hkern u1="V" u2="m" k="41" />
<hkern u1="V" u2="b" k="16" />
<hkern u1="V" u2="]" k="-41" />
<hkern u1="V" u2="\" k="-53" />
<hkern u1="V" u2="V" k="-20" />
<hkern u1="V" u2="Q" k="31" />
<hkern u1="V" u2="J" k="82" />
<hkern u1="V" u2="F" k="20" />
<hkern u1="V" u2="&#x40;" k="20" />
<hkern u1="V" u2="&#x3f;" k="-41" />
<hkern u1="V" u2="&#x2f;" k="82" />
<hkern u1="V" u2="&#x2a;" k="-49" />
<hkern u1="V" u2="&#x29;" k="-41" />
<hkern u1="V" u2="&#x26;" k="31" />
<hkern u1="W" u2="&#xef;" k="-143" />
<hkern u1="W" u2="&#xee;" k="-123" />
<hkern u1="W" u2="&#xec;" k="-102" />
<hkern u1="W" u2="&#x7d;" k="-41" />
<hkern u1="W" u2="q" k="49" />
<hkern u1="W" u2="p" k="20" />
<hkern u1="W" u2="m" k="20" />
<hkern u1="W" u2="]" k="-41" />
<hkern u1="W" u2="\" k="-41" />
<hkern u1="W" u2="V" k="-20" />
<hkern u1="W" u2="J" k="55" />
<hkern u1="W" u2="&#x40;" k="10" />
<hkern u1="W" u2="&#x3f;" k="-45" />
<hkern u1="W" u2="&#x2f;" k="41" />
<hkern u1="W" u2="&#x2a;" k="-45" />
<hkern u1="W" u2="&#x29;" k="-20" />
<hkern u1="W" u2="&#x26;" k="29" />
<hkern u1="X" g2="uniFB02" k="6" />
<hkern u1="X" g2="uniFB01" k="6" />
<hkern u1="X" u2="&#x153;" k="31" />
<hkern u1="X" u2="&#x152;" k="20" />
<hkern u1="X" u2="&#xff;" k="45" />
<hkern u1="X" u2="&#xfd;" k="45" />
<hkern u1="X" u2="&#xf8;" k="31" />
<hkern u1="X" u2="&#xf6;" k="31" />
<hkern u1="X" u2="&#xf5;" k="31" />
<hkern u1="X" u2="&#xf4;" k="31" />
<hkern u1="X" u2="&#xf3;" k="31" />
<hkern u1="X" u2="&#xf2;" k="31" />
<hkern u1="X" u2="&#xf1;" k="10" />
<hkern u1="X" u2="&#xf0;" k="31" />
<hkern u1="X" u2="&#xeb;" k="31" />
<hkern u1="X" u2="&#xea;" k="31" />
<hkern u1="X" u2="&#xe9;" k="31" />
<hkern u1="X" u2="&#xe8;" k="31" />
<hkern u1="X" u2="&#xe7;" k="31" />
<hkern u1="X" u2="&#xd8;" k="20" />
<hkern u1="X" u2="&#xd6;" k="20" />
<hkern u1="X" u2="&#xd5;" k="20" />
<hkern u1="X" u2="&#xd4;" k="20" />
<hkern u1="X" u2="&#xd3;" k="20" />
<hkern u1="X" u2="&#xd2;" k="20" />
<hkern u1="X" u2="&#xc7;" k="20" />
<hkern u1="X" u2="z" k="-41" />
<hkern u1="X" u2="y" k="45" />
<hkern u1="X" u2="w" k="31" />
<hkern u1="X" u2="t" k="33" />
<hkern u1="X" u2="o" k="31" />
<hkern u1="X" u2="n" k="10" />
<hkern u1="X" u2="g" k="57" />
<hkern u1="X" u2="f" k="6" />
<hkern u1="X" u2="e" k="31" />
<hkern u1="X" u2="d" k="31" />
<hkern u1="X" u2="c" k="31" />
<hkern u1="X" u2="O" k="20" />
<hkern u1="X" u2="G" k="20" />
<hkern u1="X" u2="C" k="20" />
<hkern u1="X" u2="&#xef;" k="-123" />
<hkern u1="X" u2="&#xee;" k="-102" />
<hkern u1="X" u2="&#xec;" k="-82" />
<hkern u1="X" u2="&#xbb;" k="20" />
<hkern u1="X" u2="v" k="39" />
<hkern u1="X" u2="q" k="31" />
<hkern u1="X" u2="Q" k="20" />
<hkern u1="X" u2="J" k="-51" />
<hkern u1="X" u2="&#x3f;" k="-20" />
<hkern u1="X" u2="&#x29;" k="-20" />
<hkern u1="Y" u2="&#xef;" k="-143" />
<hkern u1="Y" u2="&#xee;" k="-123" />
<hkern u1="Y" u2="&#xec;" k="-102" />
<hkern u1="Y" u2="&#xbb;" k="61" />
<hkern u1="Y" u2="&#x7d;" k="-41" />
<hkern u1="Y" u2="x" k="41" />
<hkern u1="Y" u2="q" k="102" />
<hkern u1="Y" u2="p" k="82" />
<hkern u1="Y" u2="m" k="82" />
<hkern u1="Y" u2="h" k="20" />
<hkern u1="Y" u2="b" k="20" />
<hkern u1="Y" u2="]" k="-29" />
<hkern u1="Y" u2="\" k="-57" />
<hkern u1="Y" u2="V" k="-20" />
<hkern u1="Y" u2="Q" k="20" />
<hkern u1="Y" u2="M" k="20" />
<hkern u1="Y" u2="J" k="92" />
<hkern u1="Y" u2="E" k="20" />
<hkern u1="Y" u2="&#x40;" k="61" />
<hkern u1="Y" u2="&#x3f;" k="-29" />
<hkern u1="Y" u2="&#x2f;" k="119" />
<hkern u1="Y" u2="&#x2a;" k="-41" />
<hkern u1="Y" u2="&#x29;" k="-20" />
<hkern u1="Y" u2="&#x26;" k="72" />
<hkern u1="Z" u2="&#x7d;" k="-20" />
<hkern u1="Z" u2="v" k="25" />
<hkern u1="Z" u2="q" k="20" />
<hkern u1="Z" u2="]" k="-20" />
<hkern u1="Z" u2="V" k="-20" />
<hkern u1="Z" u2="J" k="-41" />
<hkern u1="Z" u2="&#x3f;" k="-16" />
<hkern u1="Z" u2="&#x26;" k="20" />
<hkern u1="[" u2="&#x178;" k="-29" />
<hkern u1="[" u2="&#xdd;" k="-29" />
<hkern u1="[" u2="&#xc5;" k="-20" />
<hkern u1="[" u2="&#xc4;" k="-20" />
<hkern u1="[" u2="&#xc3;" k="-20" />
<hkern u1="[" u2="&#xc2;" k="-20" />
<hkern u1="[" u2="&#xc1;" k="-20" />
<hkern u1="[" u2="&#xc0;" k="-20" />
<hkern u1="[" u2="z" k="-31" />
<hkern u1="[" u2="j" k="-166" />
<hkern u1="[" u2="g" k="20" />
<hkern u1="[" u2="Z" k="-20" />
<hkern u1="[" u2="Y" k="-29" />
<hkern u1="[" u2="W" k="-41" />
<hkern u1="[" u2="T" k="-41" />
<hkern u1="[" u2="A" k="-20" />
<hkern u1="[" u2="x" k="-20" />
<hkern u1="[" u2="V" k="-41" />
<hkern u1="[" u2="J" k="-86" />
<hkern u1="\" u2="&#x178;" k="86" />
<hkern u1="\" u2="&#xff;" k="45" />
<hkern u1="\" u2="&#xfd;" k="45" />
<hkern u1="\" u2="&#xdd;" k="86" />
<hkern u1="\" u2="&#xc5;" k="-41" />
<hkern u1="\" u2="&#xc4;" k="-41" />
<hkern u1="\" u2="&#xc3;" k="-41" />
<hkern u1="\" u2="&#xc2;" k="-41" />
<hkern u1="\" u2="&#xc1;" k="-41" />
<hkern u1="\" u2="&#xc0;" k="-41" />
<hkern u1="\" u2="y" k="45" />
<hkern u1="\" u2="Y" k="86" />
<hkern u1="\" u2="W" k="61" />
<hkern u1="\" u2="T" k="123" />
<hkern u1="\" u2="A" k="-41" />
<hkern u1="\" u2="v" k="82" />
<hkern u1="\" u2="V" k="109" />
<hkern u1="a" u2="&#x2122;" k="61" />
<hkern u1="a" u2="v" k="10" />
<hkern u1="a" u2="\" k="123" />
<hkern u1="a" u2="&#x2a;" k="51" />
<hkern u1="b" u2="&#x2122;" k="61" />
<hkern u1="b" u2="v" k="6" />
<hkern u1="b" u2="\" k="123" />
<hkern u1="b" u2="&#x2a;" k="72" />
<hkern u1="c" u2="x" k="-25" />
<hkern u1="c" u2="v" k="-41" />
<hkern u1="c" u2="\" k="41" />
<hkern u1="d" u2="&#xec;" k="-41" />
<hkern u1="e" u2="&#x2122;" k="61" />
<hkern u1="e" u2="v" k="6" />
<hkern u1="e" u2="\" k="111" />
<hkern u1="e" u2="&#x2a;" k="72" />
<hkern u1="f" g2="uniFB02" k="-33" />
<hkern u1="f" g2="uniFB01" k="-33" />
<hkern u1="f" u2="&#x2026;" k="102" />
<hkern u1="f" u2="&#x201e;" k="102" />
<hkern u1="f" u2="&#x201d;" k="-102" />
<hkern u1="f" u2="&#x201a;" k="102" />
<hkern u1="f" u2="&#x2019;" k="-102" />
<hkern u1="f" u2="&#xff;" k="-33" />
<hkern u1="f" u2="&#xfd;" k="-33" />
<hkern u1="f" u2="&#xed;" k="-45" />
<hkern u1="f" u2="y" k="-33" />
<hkern u1="f" u2="w" k="-37" />
<hkern u1="f" u2="t" k="-33" />
<hkern u1="f" u2="l" k="-61" />
<hkern u1="f" u2="j" k="-49" />
<hkern u1="f" u2="i" k="-45" />
<hkern u1="f" u2="f" k="-33" />
<hkern u1="f" u2="&#x3b;" k="-41" />
<hkern u1="f" u2="&#x3a;" k="-41" />
<hkern u1="f" u2="&#x2e;" k="102" />
<hkern u1="f" u2="&#x2c;" k="102" />
<hkern u1="f" u2="&#x27;" k="-123" />
<hkern u1="f" u2="&#x22;" k="-123" />
<hkern u1="f" u2="&#x2122;" k="-143" />
<hkern u1="f" u2="&#xef;" k="-209" />
<hkern u1="f" u2="&#xee;" k="-168" />
<hkern u1="f" u2="&#xec;" k="-168" />
<hkern u1="f" u2="&#xbb;" k="-41" />
<hkern u1="f" u2="&#x7d;" k="-102" />
<hkern u1="f" u2="v" k="-33" />
<hkern u1="f" u2="k" k="-61" />
<hkern u1="f" u2="h" k="-61" />
<hkern u1="f" u2="b" k="-61" />
<hkern u1="f" u2="]" k="-102" />
<hkern u1="f" u2="\" k="-123" />
<hkern u1="f" u2="&#x3f;" k="-115" />
<hkern u1="f" u2="&#x2a;" k="-123" />
<hkern u1="f" u2="&#x29;" k="-102" />
<hkern u1="f" u2="&#x21;" k="-82" />
<hkern u1="g" u2="&#x7d;" k="-41" />
<hkern u1="g" u2="]" k="-41" />
<hkern u1="g" u2="\" k="33" />
<hkern u1="g" u2="&#x29;" k="-41" />
<hkern u1="h" u2="\" k="61" />
<hkern u1="i" u2="&#xef;" k="-41" />
<hkern u1="i" u2="&#xee;" k="-41" />
<hkern u1="i" u2="&#x2f;" k="20" />
<hkern u1="i" u2="&#x2a;" k="-12" />
<hkern u1="j" u2="&#xef;" k="-41" />
<hkern u1="j" u2="&#xee;" k="-41" />
<hkern u1="j" u2="\" k="25" />
<hkern u1="k" u2="v" k="-16" />
<hkern u1="k" u2="q" k="16" />
<hkern u1="k" u2="]" k="-20" />
<hkern u1="l" u2="v" k="31" />
<hkern u1="l" u2="\" k="41" />
<hkern u1="l" u2="&#x2a;" k="41" />
<hkern u1="m" u2="\" k="61" />
<hkern u1="n" u2="&#x2122;" k="61" />
<hkern u1="n" u2="v" k="10" />
<hkern u1="n" u2="\" k="61" />
<hkern u1="n" u2="&#x2a;" k="82" />
<hkern u1="o" u2="&#x2122;" k="61" />
<hkern u1="o" u2="x" k="6" />
<hkern u1="o" u2="v" k="6" />
<hkern u1="o" u2="\" k="123" />
<hkern u1="o" u2="&#x2a;" k="72" />
<hkern u1="p" u2="&#x2122;" k="61" />
<hkern u1="p" u2="v" k="6" />
<hkern u1="p" u2="\" k="123" />
<hkern u1="p" u2="&#x2a;" k="72" />
<hkern u1="q" u2="j" k="-111" />
<hkern u1="q" u2="&#x2122;" k="61" />
<hkern u1="q" u2="\" k="61" />
<hkern u1="q" u2="&#x2a;" k="31" />
<hkern u1="r" u2="x" k="-20" />
<hkern u1="r" u2="v" k="-33" />
<hkern u1="r" u2="q" k="8" />
<hkern u1="r" u2="]" k="20" />
<hkern u1="r" u2="\" k="41" />
<hkern u1="s" u2="&#x2122;" k="61" />
<hkern u1="s" u2="\" k="82" />
<hkern u1="s" u2="&#x2a;" k="31" />
<hkern u1="t" u2="\" k="41" />
<hkern u1="t" u2="&#x2f;" k="-70" />
<hkern u1="u" u2="&#x2122;" k="41" />
<hkern u1="u" u2="\" k="61" />
<hkern u1="v" g2="uniFB02" k="-53" />
<hkern u1="v" g2="uniFB01" k="-53" />
<hkern u1="v" u2="&#x2026;" k="113" />
<hkern u1="v" u2="&#x201e;" k="113" />
<hkern u1="v" u2="&#x201d;" k="-41" />
<hkern u1="v" u2="&#x201a;" k="113" />
<hkern u1="v" u2="&#x2019;" k="-41" />
<hkern u1="v" u2="&#x153;" k="6" />
<hkern u1="v" u2="&#xff;" k="-27" />
<hkern u1="v" u2="&#xfd;" k="-27" />
<hkern u1="v" u2="&#xf8;" k="6" />
<hkern u1="v" u2="&#xf6;" k="6" />
<hkern u1="v" u2="&#xf5;" k="6" />
<hkern u1="v" u2="&#xf4;" k="6" />
<hkern u1="v" u2="&#xf3;" k="6" />
<hkern u1="v" u2="&#xf2;" k="6" />
<hkern u1="v" u2="&#xf0;" k="6" />
<hkern u1="v" u2="&#xeb;" k="6" />
<hkern u1="v" u2="&#xea;" k="6" />
<hkern u1="v" u2="&#xe9;" k="6" />
<hkern u1="v" u2="&#xe8;" k="6" />
<hkern u1="v" u2="&#xe7;" k="6" />
<hkern u1="v" u2="&#xe6;" k="16" />
<hkern u1="v" u2="&#xe5;" k="16" />
<hkern u1="v" u2="&#xe4;" k="16" />
<hkern u1="v" u2="&#xe3;" k="16" />
<hkern u1="v" u2="&#xe2;" k="16" />
<hkern u1="v" u2="&#xe1;" k="16" />
<hkern u1="v" u2="&#xe0;" k="16" />
<hkern u1="v" u2="y" k="-27" />
<hkern u1="v" u2="w" k="-16" />
<hkern u1="v" u2="t" k="-53" />
<hkern u1="v" u2="o" k="6" />
<hkern u1="v" u2="f" k="-53" />
<hkern u1="v" u2="e" k="6" />
<hkern u1="v" u2="d" k="6" />
<hkern u1="v" u2="c" k="6" />
<hkern u1="v" u2="a" k="16" />
<hkern u1="v" u2="&#x3b;" k="-20" />
<hkern u1="v" u2="&#x3a;" k="-20" />
<hkern u1="v" u2="&#x2e;" k="113" />
<hkern u1="v" u2="&#x2c;" k="113" />
<hkern u1="v" u2="v" k="-16" />
<hkern u1="v" u2="q" k="6" />
<hkern u1="v" u2="\" k="41" />
<hkern u1="w" u2="v" k="-20" />
<hkern u1="w" u2="\" k="41" />
<hkern u1="x" u2="&#x201d;" k="-41" />
<hkern u1="x" u2="&#x2019;" k="-41" />
<hkern u1="x" u2="&#x153;" k="6" />
<hkern u1="x" u2="&#xff;" k="-8" />
<hkern u1="x" u2="&#xfd;" k="-8" />
<hkern u1="x" u2="&#xf8;" k="6" />
<hkern u1="x" u2="&#xf6;" k="6" />
<hkern u1="x" u2="&#xf5;" k="6" />
<hkern u1="x" u2="&#xf4;" k="6" />
<hkern u1="x" u2="&#xf3;" k="6" />
<hkern u1="x" u2="&#xf2;" k="6" />
<hkern u1="x" u2="&#xf0;" k="6" />
<hkern u1="x" u2="&#xeb;" k="6" />
<hkern u1="x" u2="&#xea;" k="6" />
<hkern u1="x" u2="&#xe9;" k="6" />
<hkern u1="x" u2="&#xe8;" k="6" />
<hkern u1="x" u2="&#xe7;" k="6" />
<hkern u1="x" u2="z" k="-8" />
<hkern u1="x" u2="y" k="-8" />
<hkern u1="x" u2="o" k="6" />
<hkern u1="x" u2="g" k="20" />
<hkern u1="x" u2="e" k="6" />
<hkern u1="x" u2="d" k="6" />
<hkern u1="x" u2="c" k="6" />
<hkern u1="x" u2="&#x3b;" k="-20" />
<hkern u1="x" u2="&#x3a;" k="-20" />
<hkern u1="x" u2="x" k="-25" />
<hkern u1="x" u2="]" k="-20" />
<hkern u1="x" u2="\" k="41" />
<hkern u1="y" u2="v" k="-20" />
<hkern u1="y" u2="\" k="41" />
<hkern u1="z" u2="&#xbb;" k="-61" />
<hkern u1="z" u2="x" k="-8" />
<hkern u1="z" u2="v" k="-20" />
<hkern u1="z" u2="]" k="-31" />
<hkern u1="z" u2="\" k="61" />
<hkern u1="&#x7b;" u2="&#x178;" k="-41" />
<hkern u1="&#x7b;" u2="&#xdd;" k="-41" />
<hkern u1="&#x7b;" u2="&#xc5;" k="-20" />
<hkern u1="&#x7b;" u2="&#xc4;" k="-20" />
<hkern u1="&#x7b;" u2="&#xc3;" k="-20" />
<hkern u1="&#x7b;" u2="&#xc2;" k="-20" />
<hkern u1="&#x7b;" u2="&#xc1;" k="-20" />
<hkern u1="&#x7b;" u2="&#xc0;" k="-20" />
<hkern u1="&#x7b;" u2="j" k="-207" />
<hkern u1="&#x7b;" u2="g" k="-20" />
<hkern u1="&#x7b;" u2="Z" k="-20" />
<hkern u1="&#x7b;" u2="Y" k="-41" />
<hkern u1="&#x7b;" u2="W" k="-41" />
<hkern u1="&#x7b;" u2="T" k="-41" />
<hkern u1="&#x7b;" u2="A" k="-20" />
<hkern u1="&#x7b;" u2="V" k="-41" />
<hkern u1="&#x7b;" u2="J" k="-74" />
<hkern u1="&#xa1;" u2="j" k="-123" />
<hkern u1="&#xab;" g2="uniFB02" k="-61" />
<hkern u1="&#xab;" g2="uniFB01" k="-61" />
<hkern u1="&#xab;" u2="&#x178;" k="61" />
<hkern u1="&#xab;" u2="&#xdd;" k="61" />
<hkern u1="&#xab;" u2="&#xc5;" k="-31" />
<hkern u1="&#xab;" u2="&#xc4;" k="-31" />
<hkern u1="&#xab;" u2="&#xc3;" k="-31" />
<hkern u1="&#xab;" u2="&#xc2;" k="-31" />
<hkern u1="&#xab;" u2="&#xc1;" k="-31" />
<hkern u1="&#xab;" u2="&#xc0;" k="-31" />
<hkern u1="&#xab;" u2="z" k="-61" />
<hkern u1="&#xab;" u2="f" k="-61" />
<hkern u1="&#xab;" u2="Y" k="61" />
<hkern u1="&#xab;" u2="T" k="41" />
<hkern u1="&#xab;" u2="A" k="-31" />
<hkern u1="&#xab;" u2="X" k="20" />
<hkern u1="&#xab;" u2="V" k="20" />
<hkern u1="&#xbf;" u2="j" k="-205" />
<hkern u1="&#xbf;" u2="T" k="109" />
<hkern u1="&#xc0;" u2="&#x2122;" k="123" />
<hkern u1="&#xc0;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc0;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc0;" u2="&#x7d;" k="-20" />
<hkern u1="&#xc0;" u2="v" k="53" />
<hkern u1="&#xc0;" u2="q" k="8" />
<hkern u1="&#xc0;" u2="b" k="20" />
<hkern u1="&#xc0;" u2="]" k="-20" />
<hkern u1="&#xc0;" u2="\" k="143" />
<hkern u1="&#xc0;" u2="V" k="78" />
<hkern u1="&#xc0;" u2="Q" k="20" />
<hkern u1="&#xc0;" u2="J" k="-51" />
<hkern u1="&#xc0;" u2="E" k="8" />
<hkern u1="&#xc0;" u2="&#x3f;" k="51" />
<hkern u1="&#xc0;" u2="&#x2a;" k="143" />
<hkern u1="&#xc1;" u2="&#x2122;" k="123" />
<hkern u1="&#xc1;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc1;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc1;" u2="&#x7d;" k="-20" />
<hkern u1="&#xc1;" u2="v" k="53" />
<hkern u1="&#xc1;" u2="q" k="8" />
<hkern u1="&#xc1;" u2="b" k="20" />
<hkern u1="&#xc1;" u2="]" k="-20" />
<hkern u1="&#xc1;" u2="\" k="143" />
<hkern u1="&#xc1;" u2="V" k="78" />
<hkern u1="&#xc1;" u2="Q" k="20" />
<hkern u1="&#xc1;" u2="J" k="-51" />
<hkern u1="&#xc1;" u2="E" k="8" />
<hkern u1="&#xc1;" u2="&#x3f;" k="51" />
<hkern u1="&#xc1;" u2="&#x2a;" k="143" />
<hkern u1="&#xc2;" u2="&#x2122;" k="123" />
<hkern u1="&#xc2;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc2;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc2;" u2="&#x7d;" k="-20" />
<hkern u1="&#xc2;" u2="v" k="53" />
<hkern u1="&#xc2;" u2="q" k="8" />
<hkern u1="&#xc2;" u2="b" k="20" />
<hkern u1="&#xc2;" u2="]" k="-20" />
<hkern u1="&#xc2;" u2="\" k="143" />
<hkern u1="&#xc2;" u2="V" k="78" />
<hkern u1="&#xc2;" u2="Q" k="20" />
<hkern u1="&#xc2;" u2="J" k="-51" />
<hkern u1="&#xc2;" u2="E" k="8" />
<hkern u1="&#xc2;" u2="&#x3f;" k="51" />
<hkern u1="&#xc2;" u2="&#x2a;" k="143" />
<hkern u1="&#xc3;" u2="&#x2122;" k="123" />
<hkern u1="&#xc3;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc3;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc3;" u2="&#x7d;" k="-20" />
<hkern u1="&#xc3;" u2="v" k="53" />
<hkern u1="&#xc3;" u2="q" k="8" />
<hkern u1="&#xc3;" u2="b" k="20" />
<hkern u1="&#xc3;" u2="]" k="-20" />
<hkern u1="&#xc3;" u2="\" k="143" />
<hkern u1="&#xc3;" u2="V" k="78" />
<hkern u1="&#xc3;" u2="Q" k="20" />
<hkern u1="&#xc3;" u2="J" k="-51" />
<hkern u1="&#xc3;" u2="E" k="8" />
<hkern u1="&#xc3;" u2="&#x3f;" k="51" />
<hkern u1="&#xc3;" u2="&#x2a;" k="143" />
<hkern u1="&#xc4;" u2="&#x2122;" k="123" />
<hkern u1="&#xc4;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc4;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc4;" u2="&#x7d;" k="-20" />
<hkern u1="&#xc4;" u2="v" k="53" />
<hkern u1="&#xc4;" u2="q" k="8" />
<hkern u1="&#xc4;" u2="b" k="20" />
<hkern u1="&#xc4;" u2="]" k="-20" />
<hkern u1="&#xc4;" u2="\" k="143" />
<hkern u1="&#xc4;" u2="V" k="78" />
<hkern u1="&#xc4;" u2="Q" k="20" />
<hkern u1="&#xc4;" u2="J" k="-51" />
<hkern u1="&#xc4;" u2="E" k="8" />
<hkern u1="&#xc4;" u2="&#x3f;" k="51" />
<hkern u1="&#xc4;" u2="&#x2a;" k="143" />
<hkern u1="&#xc5;" u2="&#x2122;" k="123" />
<hkern u1="&#xc5;" u2="&#x203a;" k="-31" />
<hkern u1="&#xc5;" u2="&#xbb;" k="-31" />
<hkern u1="&#xc5;" u2="&#x7d;" k="-20" />
<hkern u1="&#xc5;" u2="v" k="53" />
<hkern u1="&#xc5;" u2="q" k="8" />
<hkern u1="&#xc5;" u2="b" k="20" />
<hkern u1="&#xc5;" u2="]" k="-20" />
<hkern u1="&#xc5;" u2="\" k="143" />
<hkern u1="&#xc5;" u2="V" k="78" />
<hkern u1="&#xc5;" u2="Q" k="20" />
<hkern u1="&#xc5;" u2="J" k="-51" />
<hkern u1="&#xc5;" u2="E" k="8" />
<hkern u1="&#xc5;" u2="&#x3f;" k="51" />
<hkern u1="&#xc5;" u2="&#x2a;" k="143" />
<hkern u1="&#xc6;" u2="V" k="-14" />
<hkern u1="&#xc6;" u2="Q" k="10" />
<hkern u1="&#xc6;" u2="J" k="-20" />
<hkern u1="&#xc6;" u2="&#x3f;" k="-20" />
<hkern u1="&#xc7;" u2="&#xef;" k="-123" />
<hkern u1="&#xc7;" u2="&#xee;" k="-102" />
<hkern u1="&#xc7;" u2="&#xec;" k="-20" />
<hkern u1="&#xc7;" u2="q" k="41" />
<hkern u1="&#xc7;" u2="X" k="-20" />
<hkern u1="&#xc7;" u2="V" k="-31" />
<hkern u1="&#xc7;" u2="Q" k="29" />
<hkern u1="&#xc7;" u2="J" k="-31" />
<hkern u1="&#xc7;" u2="&#x3f;" k="-12" />
<hkern u1="&#xc7;" u2="&#x29;" k="-41" />
<hkern u1="&#xc8;" u2="V" k="-14" />
<hkern u1="&#xc8;" u2="Q" k="10" />
<hkern u1="&#xc8;" u2="J" k="-20" />
<hkern u1="&#xc8;" u2="&#x3f;" k="-20" />
<hkern u1="&#xc9;" u2="V" k="-14" />
<hkern u1="&#xc9;" u2="Q" k="10" />
<hkern u1="&#xc9;" u2="J" k="-20" />
<hkern u1="&#xc9;" u2="&#x3f;" k="-20" />
<hkern u1="&#xca;" u2="V" k="-14" />
<hkern u1="&#xca;" u2="Q" k="10" />
<hkern u1="&#xca;" u2="J" k="-20" />
<hkern u1="&#xca;" u2="&#x3f;" k="-20" />
<hkern u1="&#xcb;" u2="V" k="-14" />
<hkern u1="&#xcb;" u2="Q" k="10" />
<hkern u1="&#xcb;" u2="J" k="-20" />
<hkern u1="&#xcb;" u2="&#x3f;" k="-20" />
<hkern u1="&#xd0;" u2="&#x2122;" k="41" />
<hkern u1="&#xd0;" u2="v" k="-10" />
<hkern u1="&#xd0;" u2="X" k="45" />
<hkern u1="&#xd2;" u2="X" k="20" />
<hkern u1="&#xd2;" u2="V" k="20" />
<hkern u1="&#xd3;" u2="X" k="20" />
<hkern u1="&#xd3;" u2="V" k="20" />
<hkern u1="&#xd4;" u2="X" k="20" />
<hkern u1="&#xd4;" u2="V" k="20" />
<hkern u1="&#xd5;" u2="X" k="20" />
<hkern u1="&#xd5;" u2="V" k="20" />
<hkern u1="&#xd6;" u2="X" k="20" />
<hkern u1="&#xd6;" u2="V" k="20" />
<hkern u1="&#xd8;" u2="X" k="20" />
<hkern u1="&#xd8;" u2="V" k="20" />
<hkern u1="&#xdd;" u2="&#xef;" k="-143" />
<hkern u1="&#xdd;" u2="&#xee;" k="-123" />
<hkern u1="&#xdd;" u2="&#xec;" k="-102" />
<hkern u1="&#xdd;" u2="&#xbb;" k="61" />
<hkern u1="&#xdd;" u2="&#x7d;" k="-41" />
<hkern u1="&#xdd;" u2="x" k="41" />
<hkern u1="&#xdd;" u2="q" k="102" />
<hkern u1="&#xdd;" u2="p" k="82" />
<hkern u1="&#xdd;" u2="m" k="82" />
<hkern u1="&#xdd;" u2="h" k="20" />
<hkern u1="&#xdd;" u2="b" k="20" />
<hkern u1="&#xdd;" u2="]" k="-29" />
<hkern u1="&#xdd;" u2="\" k="-57" />
<hkern u1="&#xdd;" u2="V" k="-20" />
<hkern u1="&#xdd;" u2="Q" k="20" />
<hkern u1="&#xdd;" u2="M" k="20" />
<hkern u1="&#xdd;" u2="J" k="92" />
<hkern u1="&#xdd;" u2="E" k="20" />
<hkern u1="&#xdd;" u2="&#x40;" k="61" />
<hkern u1="&#xdd;" u2="&#x3f;" k="-29" />
<hkern u1="&#xdd;" u2="&#x2f;" k="119" />
<hkern u1="&#xdd;" u2="&#x2a;" k="-41" />
<hkern u1="&#xdd;" u2="&#x29;" k="-20" />
<hkern u1="&#xdd;" u2="&#x26;" k="72" />
<hkern u1="&#xde;" g2="uniFB02" k="-82" />
<hkern u1="&#xde;" g2="uniFB01" k="-82" />
<hkern u1="&#xde;" u2="&#xff;" k="-61" />
<hkern u1="&#xde;" u2="&#xfd;" k="-61" />
<hkern u1="&#xde;" u2="z" k="-20" />
<hkern u1="&#xde;" u2="y" k="-61" />
<hkern u1="&#xde;" u2="w" k="-61" />
<hkern u1="&#xde;" u2="t" k="-82" />
<hkern u1="&#xde;" u2="f" k="-82" />
<hkern u1="&#xde;" u2="x" k="-41" />
<hkern u1="&#xde;" u2="v" k="-61" />
<hkern u1="&#xdf;" u2="&#xff;" k="20" />
<hkern u1="&#xdf;" u2="&#xfd;" k="20" />
<hkern u1="&#xdf;" u2="y" k="20" />
<hkern u1="&#xdf;" u2="v" k="20" />
<hkern u1="&#xe0;" u2="&#x2122;" k="61" />
<hkern u1="&#xe0;" u2="v" k="10" />
<hkern u1="&#xe0;" u2="\" k="123" />
<hkern u1="&#xe0;" u2="&#x2a;" k="51" />
<hkern u1="&#xe1;" u2="&#x2122;" k="61" />
<hkern u1="&#xe1;" u2="v" k="10" />
<hkern u1="&#xe1;" u2="\" k="123" />
<hkern u1="&#xe1;" u2="&#x2a;" k="51" />
<hkern u1="&#xe2;" u2="&#x2122;" k="61" />
<hkern u1="&#xe2;" u2="v" k="10" />
<hkern u1="&#xe2;" u2="\" k="123" />
<hkern u1="&#xe2;" u2="&#x2a;" k="51" />
<hkern u1="&#xe3;" u2="&#x2122;" k="61" />
<hkern u1="&#xe3;" u2="v" k="10" />
<hkern u1="&#xe3;" u2="\" k="123" />
<hkern u1="&#xe3;" u2="&#x2a;" k="51" />
<hkern u1="&#xe4;" u2="&#x2122;" k="61" />
<hkern u1="&#xe4;" u2="v" k="10" />
<hkern u1="&#xe4;" u2="\" k="123" />
<hkern u1="&#xe4;" u2="&#x2a;" k="51" />
<hkern u1="&#xe5;" u2="&#x2122;" k="61" />
<hkern u1="&#xe5;" u2="v" k="10" />
<hkern u1="&#xe5;" u2="\" k="123" />
<hkern u1="&#xe5;" u2="&#x2a;" k="51" />
<hkern u1="&#xe6;" u2="&#x2122;" k="61" />
<hkern u1="&#xe6;" u2="v" k="6" />
<hkern u1="&#xe6;" u2="\" k="111" />
<hkern u1="&#xe6;" u2="&#x2a;" k="72" />
<hkern u1="&#xe7;" u2="x" k="-25" />
<hkern u1="&#xe7;" u2="v" k="-41" />
<hkern u1="&#xe7;" u2="\" k="41" />
<hkern u1="&#xe8;" u2="&#x2122;" k="61" />
<hkern u1="&#xe8;" u2="v" k="6" />
<hkern u1="&#xe8;" u2="\" k="111" />
<hkern u1="&#xe8;" u2="&#x2a;" k="72" />
<hkern u1="&#xe9;" u2="&#x2122;" k="61" />
<hkern u1="&#xe9;" u2="v" k="6" />
<hkern u1="&#xe9;" u2="\" k="111" />
<hkern u1="&#xe9;" u2="&#x2a;" k="72" />
<hkern u1="&#xea;" u2="&#x2122;" k="61" />
<hkern u1="&#xea;" u2="v" k="6" />
<hkern u1="&#xea;" u2="\" k="111" />
<hkern u1="&#xea;" u2="&#x2a;" k="72" />
<hkern u1="&#xeb;" u2="&#x2122;" k="61" />
<hkern u1="&#xeb;" u2="v" k="6" />
<hkern u1="&#xeb;" u2="\" k="111" />
<hkern u1="&#xeb;" u2="&#x2a;" k="72" />
<hkern u1="&#xec;" u2="&#xef;" k="-41" />
<hkern u1="&#xec;" u2="&#xee;" k="-41" />
<hkern u1="&#xec;" u2="&#x2f;" k="20" />
<hkern u1="&#xec;" u2="&#x2a;" k="-12" />
<hkern u1="&#xed;" u2="&#xed;" k="-41" />
<hkern u1="&#xed;" u2="&#xec;" k="-41" />
<hkern u1="&#xed;" u2="l" k="-41" />
<hkern u1="&#xed;" u2="j" k="-41" />
<hkern u1="&#xed;" u2="i" k="-41" />
<hkern u1="&#xed;" u2="&#xef;" k="-41" />
<hkern u1="&#xed;" u2="&#xee;" k="-41" />
<hkern u1="&#xed;" u2="k" k="-41" />
<hkern u1="&#xed;" u2="h" k="-41" />
<hkern u1="&#xed;" u2="b" k="-41" />
<hkern u1="&#xed;" u2="&#x2f;" k="20" />
<hkern u1="&#xed;" u2="&#x2a;" k="-12" />
<hkern u1="&#xee;" u2="l" k="-41" />
<hkern u1="&#xee;" u2="&#xef;" k="-41" />
<hkern u1="&#xee;" u2="&#xee;" k="-41" />
<hkern u1="&#xee;" u2="k" k="-41" />
<hkern u1="&#xee;" u2="h" k="-41" />
<hkern u1="&#xee;" u2="b" k="-41" />
<hkern u1="&#xee;" u2="&#x2f;" k="20" />
<hkern u1="&#xee;" u2="&#x2a;" k="-12" />
<hkern u1="&#xef;" u2="&#xed;" k="-61" />
<hkern u1="&#xef;" u2="&#xec;" k="-61" />
<hkern u1="&#xef;" u2="l" k="-61" />
<hkern u1="&#xef;" u2="j" k="-61" />
<hkern u1="&#xef;" u2="i" k="-61" />
<hkern u1="&#xef;" u2="&#xef;" k="-41" />
<hkern u1="&#xef;" u2="&#xee;" k="-41" />
<hkern u1="&#xef;" u2="k" k="-61" />
<hkern u1="&#xef;" u2="h" k="-61" />
<hkern u1="&#xef;" u2="b" k="-61" />
<hkern u1="&#xef;" u2="&#x2f;" k="20" />
<hkern u1="&#xef;" u2="&#x2a;" k="-12" />
<hkern u1="&#xf1;" u2="&#x2122;" k="61" />
<hkern u1="&#xf1;" u2="v" k="10" />
<hkern u1="&#xf1;" u2="\" k="61" />
<hkern u1="&#xf1;" u2="&#x2a;" k="82" />
<hkern u1="&#xf2;" u2="&#x2122;" k="61" />
<hkern u1="&#xf2;" u2="x" k="6" />
<hkern u1="&#xf2;" u2="v" k="6" />
<hkern u1="&#xf2;" u2="\" k="123" />
<hkern u1="&#xf2;" u2="&#x2a;" k="72" />
<hkern u1="&#xf3;" u2="&#x2122;" k="61" />
<hkern u1="&#xf3;" u2="x" k="6" />
<hkern u1="&#xf3;" u2="v" k="6" />
<hkern u1="&#xf3;" u2="\" k="123" />
<hkern u1="&#xf3;" u2="&#x2a;" k="72" />
<hkern u1="&#xf4;" u2="&#x2122;" k="61" />
<hkern u1="&#xf4;" u2="x" k="6" />
<hkern u1="&#xf4;" u2="v" k="6" />
<hkern u1="&#xf4;" u2="\" k="123" />
<hkern u1="&#xf4;" u2="&#x2a;" k="72" />
<hkern u1="&#xf5;" u2="&#x2122;" k="61" />
<hkern u1="&#xf5;" u2="x" k="6" />
<hkern u1="&#xf5;" u2="v" k="6" />
<hkern u1="&#xf5;" u2="\" k="123" />
<hkern u1="&#xf5;" u2="&#x2a;" k="72" />
<hkern u1="&#xf6;" u2="&#x2122;" k="61" />
<hkern u1="&#xf6;" u2="x" k="6" />
<hkern u1="&#xf6;" u2="v" k="6" />
<hkern u1="&#xf6;" u2="\" k="123" />
<hkern u1="&#xf6;" u2="&#x2a;" k="72" />
<hkern u1="&#xf8;" u2="&#x2122;" k="61" />
<hkern u1="&#xf8;" u2="x" k="6" />
<hkern u1="&#xf8;" u2="v" k="6" />
<hkern u1="&#xf8;" u2="\" k="123" />
<hkern u1="&#xf8;" u2="&#x2a;" k="72" />
<hkern u1="&#xf9;" u2="&#x2122;" k="41" />
<hkern u1="&#xf9;" u2="\" k="61" />
<hkern u1="&#xfa;" u2="&#x2122;" k="41" />
<hkern u1="&#xfa;" u2="\" k="61" />
<hkern u1="&#xfb;" u2="&#x2122;" k="41" />
<hkern u1="&#xfb;" u2="\" k="61" />
<hkern u1="&#xfc;" u2="&#x2122;" k="41" />
<hkern u1="&#xfc;" u2="\" k="61" />
<hkern u1="&#xfd;" u2="v" k="-20" />
<hkern u1="&#xfd;" u2="\" k="41" />
<hkern u1="&#xff;" u2="v" k="-20" />
<hkern u1="&#xff;" u2="\" k="41" />
<hkern u1="&#x152;" u2="V" k="-14" />
<hkern u1="&#x152;" u2="Q" k="10" />
<hkern u1="&#x152;" u2="J" k="-20" />
<hkern u1="&#x152;" u2="&#x3f;" k="-20" />
<hkern u1="&#x153;" u2="&#x2122;" k="61" />
<hkern u1="&#x153;" u2="v" k="6" />
<hkern u1="&#x153;" u2="\" k="111" />
<hkern u1="&#x153;" u2="&#x2a;" k="72" />
<hkern u1="&#x178;" u2="&#xef;" k="-143" />
<hkern u1="&#x178;" u2="&#xee;" k="-123" />
<hkern u1="&#x178;" u2="&#xec;" k="-102" />
<hkern u1="&#x178;" u2="&#xbb;" k="61" />
<hkern u1="&#x178;" u2="&#x7d;" k="-41" />
<hkern u1="&#x178;" u2="x" k="41" />
<hkern u1="&#x178;" u2="q" k="102" />
<hkern u1="&#x178;" u2="p" k="82" />
<hkern u1="&#x178;" u2="m" k="82" />
<hkern u1="&#x178;" u2="h" k="20" />
<hkern u1="&#x178;" u2="b" k="20" />
<hkern u1="&#x178;" u2="]" k="-29" />
<hkern u1="&#x178;" u2="\" k="-57" />
<hkern u1="&#x178;" u2="V" k="-20" />
<hkern u1="&#x178;" u2="Q" k="20" />
<hkern u1="&#x178;" u2="M" k="20" />
<hkern u1="&#x178;" u2="J" k="92" />
<hkern u1="&#x178;" u2="E" k="20" />
<hkern u1="&#x178;" u2="&#x40;" k="61" />
<hkern u1="&#x178;" u2="&#x3f;" k="-29" />
<hkern u1="&#x178;" u2="&#x2f;" k="119" />
<hkern u1="&#x178;" u2="&#x2a;" k="-41" />
<hkern u1="&#x178;" u2="&#x29;" k="-20" />
<hkern u1="&#x178;" u2="&#x26;" k="72" />
<hkern u1="&#x2018;" u2="x" k="-20" />
<hkern u1="&#x2018;" u2="v" k="-20" />
<hkern u1="&#x2018;" u2="X" k="-20" />
<hkern u1="&#x2018;" u2="V" k="-41" />
<hkern u1="&#x201a;" u2="v" k="113" />
<hkern u1="&#x201a;" u2="V" k="164" />
<hkern u1="&#x201a;" u2="J" k="-29" />
<hkern u1="&#x201c;" u2="x" k="-20" />
<hkern u1="&#x201c;" u2="v" k="-20" />
<hkern u1="&#x201c;" u2="X" k="-20" />
<hkern u1="&#x201c;" u2="V" k="-41" />
<hkern u1="&#x201d;" u2="s" k="102" />
<hkern u1="&#x201e;" u2="v" k="113" />
<hkern u1="&#x201e;" u2="V" k="164" />
<hkern u1="&#x201e;" u2="J" k="-29" />
<hkern u1="&#x2026;" u2="v" k="113" />
<hkern u1="&#x2026;" u2="V" k="164" />
<hkern u1="&#x2026;" u2="J" k="-29" />
<hkern u1="&#x2039;" u2="&#xc5;" k="-31" />
<hkern u1="&#x2039;" u2="&#xc4;" k="-31" />
<hkern u1="&#x2039;" u2="&#xc3;" k="-31" />
<hkern u1="&#x2039;" u2="&#xc2;" k="-31" />
<hkern u1="&#x2039;" u2="&#xc1;" k="-31" />
<hkern u1="&#x2039;" u2="&#xc0;" k="-31" />
<hkern u1="&#x2039;" u2="A" k="-31" />
<hkern g1="uniFB01" u2="&#xef;" k="-41" />
<hkern g1="uniFB01" u2="&#xee;" k="-41" />
<hkern g1="uniFB01" u2="&#x2f;" k="20" />
<hkern g1="uniFB01" u2="&#x2a;" k="-12" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="g" 	k="25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="w" 	k="25" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="y,yacute,ydieresis" 	k="49" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="T" 	k="102" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="W" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="Y,Yacute,Ydieresis" 	k="90" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="AE" 	k="-41" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="c,ccedilla" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="d" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="8" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="t" 	k="37" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="G" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="123" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,Ccedilla" 	k="20" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="C,Ccedilla" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="g" 	k="45" />
<hkern g1="C,Ccedilla" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="T" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="W" 	k="-51" />
<hkern g1="C,Ccedilla" 	g2="Y,Yacute,Ydieresis" 	k="-33" />
<hkern g1="C,Ccedilla" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-20" />
<hkern g1="C,Ccedilla" 	g2="c,ccedilla" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="d" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="41" />
<hkern g1="C,Ccedilla" 	g2="G" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="C,Ccedilla" 	k="29" />
<hkern g1="C,Ccedilla" 	g2="Z" 	k="-31" />
<hkern g1="C,Ccedilla" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="-31" />
<hkern g1="C,Ccedilla" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="20" />
<hkern g1="D,Eth" 	g2="w" 	k="-10" />
<hkern g1="D,Eth" 	g2="y,yacute,ydieresis" 	k="-10" />
<hkern g1="D,Eth" 	g2="T" 	k="49" />
<hkern g1="D,Eth" 	g2="Y,Yacute,Ydieresis" 	k="53" />
<hkern g1="D,Eth" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="29" />
<hkern g1="D,Eth" 	g2="AE" 	k="82" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="T" 	k="8" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="W" 	k="-14" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Y,Yacute,Ydieresis" 	k="-10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-25" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="G" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="C,Ccedilla" 	k="10" />
<hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE" 	g2="Z" 	k="-29" />
<hkern g1="K" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="45" />
<hkern g1="K" 	g2="g" 	k="45" />
<hkern g1="K" 	g2="w" 	k="41" />
<hkern g1="K" 	g2="y,yacute,ydieresis" 	k="41" />
<hkern g1="K" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="49" />
<hkern g1="K" 	g2="T" 	k="-33" />
<hkern g1="K" 	g2="W" 	k="-25" />
<hkern g1="K" 	g2="Y,Yacute,Ydieresis" 	k="-33" />
<hkern g1="K" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-33" />
<hkern g1="K" 	g2="c,ccedilla" 	k="45" />
<hkern g1="K" 	g2="d" 	k="45" />
<hkern g1="K" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="45" />
<hkern g1="K" 	g2="S" 	k="-16" />
<hkern g1="K" 	g2="z" 	k="-41" />
<hkern g1="L" 	g2="g" 	k="41" />
<hkern g1="L" 	g2="w" 	k="20" />
<hkern g1="L" 	g2="y,yacute,ydieresis" 	k="61" />
<hkern g1="L" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="25" />
<hkern g1="L" 	g2="T" 	k="123" />
<hkern g1="L" 	g2="W" 	k="78" />
<hkern g1="L" 	g2="Y,Yacute,Ydieresis" 	k="135" />
<hkern g1="L" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-29" />
<hkern g1="L" 	g2="AE" 	k="-25" />
<hkern g1="L" 	g2="t" 	k="41" />
<hkern g1="L" 	g2="quoteright,quotedblright" 	k="164" />
<hkern g1="L" 	g2="f,uniFB01,uniFB02" 	k="41" />
<hkern g1="L" 	g2="Z" 	k="-25" />
<hkern g1="L" 	g2="z" 	k="-20" />
<hkern g1="L" 	g2="j" 	k="-61" />
<hkern g1="L" 	g2="s" 	k="-14" />
<hkern g1="L" 	g2="quotedbl,quotesingle" 	k="184" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="T" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="Y,Yacute,Ydieresis" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="AE" 	k="41" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="t" 	k="-16" />
<hkern g1="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="f,uniFB01,uniFB02" 	k="-16" />
<hkern g1="R" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="25" />
<hkern g1="R" 	g2="g" 	k="33" />
<hkern g1="R" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="12" />
<hkern g1="R" 	g2="T" 	k="8" />
<hkern g1="R" 	g2="Y,Yacute,Ydieresis" 	k="12" />
<hkern g1="R" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="-12" />
<hkern g1="R" 	g2="c,ccedilla" 	k="25" />
<hkern g1="R" 	g2="d" 	k="25" />
<hkern g1="R" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="25" />
<hkern g1="R" 	g2="Z" 	k="-23" />
<hkern g1="R" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="25" />
<hkern g1="R" 	g2="s" 	k="12" />
<hkern g1="S" 	g2="T" 	k="20" />
<hkern g1="S" 	g2="AE" 	k="12" />
<hkern g1="T" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="143" />
<hkern g1="T" 	g2="g" 	k="143" />
<hkern g1="T" 	g2="w" 	k="82" />
<hkern g1="T" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="T" 	g2="T" 	k="-12" />
<hkern g1="T" 	g2="W" 	k="-33" />
<hkern g1="T" 	g2="Y,Yacute,Ydieresis" 	k="-31" />
<hkern g1="T" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="102" />
<hkern g1="T" 	g2="AE" 	k="143" />
<hkern g1="T" 	g2="c,ccedilla" 	k="143" />
<hkern g1="T" 	g2="d" 	k="143" />
<hkern g1="T" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="143" />
<hkern g1="T" 	g2="t" 	k="41" />
<hkern g1="T" 	g2="G" 	k="20" />
<hkern g1="T" 	g2="C,Ccedilla" 	k="20" />
<hkern g1="T" 	g2="f,uniFB01,uniFB02" 	k="49" />
<hkern g1="T" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="143" />
<hkern g1="T" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="143" />
<hkern g1="T" 	g2="S" 	k="20" />
<hkern g1="T" 	g2="z" 	k="102" />
<hkern g1="T" 	g2="s" 	k="123" />
<hkern g1="T" 	g2="l" 	k="20" />
<hkern g1="T" 	g2="n,ntilde" 	k="102" />
<hkern g1="T" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="123" />
<hkern g1="T" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="T" 	g2="r" 	k="102" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="AE" 	k="20" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="t" 	k="-10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="f,uniFB01,uniFB02" 	k="-10" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="41" />
<hkern g1="W" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="49" />
<hkern g1="W" 	g2="g" 	k="49" />
<hkern g1="W" 	g2="T" 	k="-33" />
<hkern g1="W" 	g2="W" 	k="-20" />
<hkern g1="W" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="W" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="20" />
<hkern g1="W" 	g2="AE" 	k="94" />
<hkern g1="W" 	g2="c,ccedilla" 	k="49" />
<hkern g1="W" 	g2="d" 	k="49" />
<hkern g1="W" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="49" />
<hkern g1="W" 	g2="t" 	k="-33" />
<hkern g1="W" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="W" 	g2="f,uniFB01,uniFB02" 	k="-35" />
<hkern g1="W" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="W" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="49" />
<hkern g1="W" 	g2="s" 	k="23" />
<hkern g1="W" 	g2="n,ntilde" 	k="20" />
<hkern g1="W" 	g2="r" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="g" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="T" 	k="-31" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="W" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="90" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="AE" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="c,ccedilla" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="d" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="f,uniFB01,uniFB02" 	k="20" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="102" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="z" 	k="57" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="s" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="n,ntilde" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="82" />
<hkern g1="Y,Yacute,Ydieresis" 	g2="colon,semicolon" 	k="20" />
<hkern g1="Z" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="20" />
<hkern g1="Z" 	g2="g" 	k="20" />
<hkern g1="Z" 	g2="w" 	k="25" />
<hkern g1="Z" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="20" />
<hkern g1="Z" 	g2="W" 	k="-20" />
<hkern g1="Z" 	g2="Y,Yacute,Ydieresis" 	k="-20" />
<hkern g1="Z" 	g2="c,ccedilla" 	k="20" />
<hkern g1="Z" 	g2="d" 	k="20" />
<hkern g1="Z" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="20" />
<hkern g1="Z" 	g2="quoteright,quotedblright" 	k="-20" />
<hkern g1="Z" 	g2="z" 	k="-10" />
<hkern g1="Z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="20" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="w" 	k="10" />
<hkern g1="c,ccedilla" 	g2="w" 	k="-37" />
<hkern g1="c,ccedilla" 	g2="y,yacute,ydieresis" 	k="-37" />
<hkern g1="c,ccedilla" 	g2="t" 	k="-45" />
<hkern g1="c,ccedilla" 	g2="f,uniFB01,uniFB02" 	k="-45" />
<hkern g1="g" 	g2="j" 	k="-180" />
<hkern g1="g" 	g2="i,igrave,iacute,icircumflex,idieresis" 	k="-4" />
<hkern g1="j" 	g2="j" 	k="-72" />
<hkern g1="k" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="k" 	g2="g" 	k="20" />
<hkern g1="k" 	g2="w" 	k="-16" />
<hkern g1="k" 	g2="y,yacute,ydieresis" 	k="-29" />
<hkern g1="k" 	g2="c,ccedilla" 	k="16" />
<hkern g1="k" 	g2="d" 	k="16" />
<hkern g1="k" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="16" />
<hkern g1="k" 	g2="t" 	k="-33" />
<hkern g1="k" 	g2="f,uniFB01,uniFB02" 	k="-33" />
<hkern g1="k" 	g2="z" 	k="-16" />
<hkern g1="k" 	g2="s" 	k="-16" />
<hkern g1="k" 	g2="colon,semicolon" 	k="-41" />
<hkern g1="l" 	g2="g" 	k="41" />
<hkern g1="l" 	g2="w" 	k="18" />
<hkern g1="l" 	g2="quoteright,quotedblright" 	k="61" />
<hkern g1="n,ntilde" 	g2="w" 	k="10" />
<hkern g1="r" 	g2="o,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="8" />
<hkern g1="r" 	g2="g" 	k="8" />
<hkern g1="r" 	g2="w" 	k="-37" />
<hkern g1="r" 	g2="y,yacute,ydieresis" 	k="-33" />
<hkern g1="r" 	g2="c,ccedilla" 	k="8" />
<hkern g1="r" 	g2="d" 	k="8" />
<hkern g1="r" 	g2="e,egrave,eacute,ecircumflex,edieresis" 	k="8" />
<hkern g1="r" 	g2="t" 	k="-29" />
<hkern g1="r" 	g2="f,uniFB01,uniFB02" 	k="-29" />
<hkern g1="r" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="164" />
<hkern g1="r" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="33" />
<hkern g1="r" 	g2="colon,semicolon" 	k="-29" />
<hkern g1="s" 	g2="g" 	k="12" />
<hkern g1="t" 	g2="g" 	k="20" />
<hkern g1="t" 	g2="s" 	k="-6" />
<hkern g1="t" 	g2="r" 	k="12" />
<hkern g1="w" 	g2="w" 	k="-20" />
<hkern g1="w" 	g2="y,yacute,ydieresis" 	k="-29" />
<hkern g1="w" 	g2="t" 	k="-41" />
<hkern g1="w" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="w" 	g2="f,uniFB01,uniFB02" 	k="-41" />
<hkern g1="w" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="82" />
<hkern g1="w" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="16" />
<hkern g1="w" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="y,yacute,ydieresis" 	g2="w" 	k="-29" />
<hkern g1="y,yacute,ydieresis" 	g2="y,yacute,ydieresis" 	k="-25" />
<hkern g1="y,yacute,ydieresis" 	g2="t" 	k="-49" />
<hkern g1="y,yacute,ydieresis" 	g2="quoteright,quotedblright" 	k="-41" />
<hkern g1="y,yacute,ydieresis" 	g2="f,uniFB01,uniFB02" 	k="-49" />
<hkern g1="y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase,ellipsis" 	k="70" />
<hkern g1="y,yacute,ydieresis" 	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae" 	k="25" />
<hkern g1="y,yacute,ydieresis" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="z" 	g2="quoteright,quotedblright" 	k="-49" />
<hkern g1="z" 	g2="f,uniFB01,uniFB02" 	k="-20" />
<hkern g1="z" 	g2="colon,semicolon" 	k="-20" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="w" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="y,yacute,ydieresis" 	k="119" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="O,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="T" 	k="143" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="41" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="W" 	k="82" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="Y,Yacute,Ydieresis" 	k="164" />
<hkern g1="comma,period,quotesinglbase,quotedblbase,ellipsis" 	g2="j" 	k="-98" />
<hkern g1="colon,semicolon" 	g2="w" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="T" 	k="-20" />
<hkern g1="colon,semicolon" 	g2="j" 	k="-61" />
<hkern g1="quoteleft,quotedblleft" 	g2="w" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="y,yacute,ydieresis" 	k="-20" />
<hkern g1="quoteleft,quotedblleft" 	g2="W" 	k="-41" />
<hkern g1="quoteleft,quotedblleft" 	g2="Y,Yacute,Ydieresis" 	k="-41" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="123" />
<hkern g1="quoteleft,quotedblleft" 	g2="AE" 	k="238" />
<hkern g1="quoteleft,quotedblleft" 	g2="Z" 	k="-20" />
<hkern g1="J" 	g2="AE" 	k="20" />
</font>
</defs></svg> 