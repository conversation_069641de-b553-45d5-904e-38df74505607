import { Injectable, EventEmitter } from '@angular/core';
import { Http, Response } from '@angular/http';
import { PlatformLocation } from '@angular/common';
import { Router } from '@angular/router';

import { MenuElement } from '../model/menu-element';

import { Observable } from 'rxjs/Observable';

@Injectable()
export class MenuService {
  menu: MenuElement[] = [];
  menuStatus: number[];
  previousMenuStatus: number[] = null;
  sideMenuPropriety = 'toggled';
  footerProperty = '';
  indexOpenedVoice: number = -1;
  iconMap: string[] = [];
  isSearchBoxOpen = false;
  isAdvancedSearchOpen = false;
  isSideSearchOpen = false;
  public eventIsSearchBoxOpen = new EventEmitter<boolean>();
  public eventSideMenuOpen = new EventEmitter<boolean>();
  plotsCounter = 0;
  private forcedHeadUrl: string;

  constructor(
    private http: Http,
    private platformLocation: PlatformLocation,
    private router: Router
  ) {
    this.createMenu();
    this.http
      .get(
        `${platformLocation.getBaseHrefFromDOM()}assets/data/icons-mapping.json`
      )
      .subscribe(value => (this.iconMap = value.json()[0]));
  }

  private createMenu() {
    this.http
      .get(
        '/UBZ-ESA-RS/service/menuService/v1/menu/ubzMenu'
        // `${this.platformLocation.getBaseHrefFromDOM()}assets/data/fake-menuWithAnagrafica.json`
      )
      .subscribe(value => {
        this.menu = value.json();
        if (this.forcedHeadUrl) {
          this.menuStatus = [this.getHeadUrlIndex(this.forcedHeadUrl), 0, -1];
        } else {
          this.menuStatus = [0, 0, -1];
        }
        this.previousMenuStatus = [0, 0, -1];
      });
  }

  changeSideMenuPropriety(index: number = null) {
    if (
      this.sideMenuPropriety === 'toggled' &&
      (index == null ||
        this.menu[this.menuStatus[0]].childs[index].childs.length > 0) &&
      this.menuStatus[0] !== this.getHeadUrlIndex('localSearch')
    ) {
      this.sideMenuPropriety = '';
      this.footerProperty = 'opened';
    } else {
      this.sideMenuPropriety = 'toggled';
      this.footerProperty = '';
    }
  }

  getIconClass(elemName: string) {
    return this.iconMap[elemName];
  }

  public setMenuStatus(
    headPosition: number,
    sidePosition: number,
    thirdPosition?: number
  ) {
    if (!thirdPosition) {
      thirdPosition = -1;
    }
    this.menuStatus = [headPosition, sidePosition, thirdPosition];
  }

  public changeHeadMenuStatus(headIndex: number) {
    if (
      this.getHeadUrlIndex('localSearch') !== -1 &&
      headIndex === this.getHeadUrlIndex('localSearch')
    ) {
      if (this.isSearchBoxOpen) {
        this.previousMenuStatus = Object.assign({}, this.menuStatus);
        this.menuStatus[0] = headIndex;
        this.menuStatus[1] = 0;
        this.menuStatus[2] = -1;
      } else {
        this.menuStatus = this.previousMenuStatus;
      }
    } else {
      this.menuStatus[0] = headIndex;
      this.menuStatus[1] = 0;
      this.menuStatus[2] = -1;
    }
  }

  public setInitHeadStatusByUrl(url: string) {
    this.forcedHeadUrl = url;
  }

  private getHeadUrlIndex(url: string): number {
    let index = 0;
    for (const el of this.menu) {
      if (el.action === url) {
        return index;
      }
      index++;
    }
    return -1;
  }

  public changeHeadPage(url: string, index: number) {
    if (url === 'localSearch') {
      this.isSearchBoxOpen = !this.isSearchBoxOpen;
      this.isAdvancedSearchOpen = false;
      this.eventIsSearchBoxOpen.emit(this.isSearchBoxOpen);
      this.changeHeadMenuStatus(index);
    } else {
      this.router.navigate([url]);
      this.changeHeadMenuStatus(index);
      this.isSearchBoxOpen = false;
    }
  }

  public refreshPlotsCounter() {
    this.http
      .get('/UAM-ESA-RS/service/lotto/v1/lottos/count')
      .map((resp: Response) => resp.json())
      .subscribe(x => {
        this.plotsCounter = x.num;
      });
  }
}
