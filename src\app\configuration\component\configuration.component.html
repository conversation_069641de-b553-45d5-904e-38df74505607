<div class="row">
  <div class="col-sm-12 section-headline">
    <h1><i class="icon-richiesta_perizia"></i>{{'UBZ.SITE_CONTENT.1011011101' | translate }}</h1>
    <section id="breadcrumbs" class="breadcrumbs">
      <div class="row">
        <div class="col-sm-12">
          <ul>
            <li><a role="button" (click)="goToDashboard()">{{'UBZ.SITE_CONTENT.11' | translate }}</a></li>
            <li>{{'UBZ.SITE_CONTENT.1011011110' | translate }}</li>
          </ul>
        </div>
      </div>
    </section>
  </div>
</div>


<app-sample-checks *ngIf="configurationType === _constants.ConfigurationType.SAMPLE_CHECKS"
  [configurations]="configurations"></app-sample-checks>
<app-expert-assignment-configuration *ngIf="configurationType === _constants.ConfigurationType.EXPERT_ASSIGNMENT"
  [configurations]="configurations"></app-expert-assignment-configuration>
<app-appraisal-configuration *ngIf="configurationType === _constants.ConfigurationType.APPRAISAL"
  [configurations]="configurations" [length]="length"></app-appraisal-configuration>
<app-individual-assignment
  *ngIf="configurationType === _constants.ConfigurationType.INDIVIDUAL_ASSIGNMENT && activeList"
  [configurationInfo]="configurationInfo" [activeList]="activeList" [inactiveList]="inactiveList"
  (refreshPage)="refreshPageAddedDeleted($event)" (submitForm)="saveOnIndividualAssignment($event)">
</app-individual-assignment>

<!-- Modal per la conferma del salvataggio -->
<div *ngIf="confirmModalOpen" class="modal fade" bsModal #modalBox="bs-modal" tabindex="-1" role="dialog"
  aria-labelledby="myModalLabel" (onHidden)="hide()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <form #f="ngForm" novalidate>
        <div class="modal-header">
          <h2>{{'UBZ.SITE_CONTENT.1011011111' | translate }}</h2>
          <button type="reset" class="close pull-right" aria-label="Close" (click)="hide()">
            <i class="icon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="row ">
            <div class="col-md-12">
              <p>{{'UBZ.SITE_CONTENT.1011100000' | translate }}.</p>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-primary waves-effect" type="button" (click)="hide()">{{'UBZ.SITE_CONTENT.1100010' |
            translate }}</button>
        </div>
      </form>
    </div>
  </div>
</div>

<app-navigation-footer [showSaveDraft]="false" [showPrevious]="false" [showCancelButton]="false"
  [configurationInfo]="_configurationService.configurationInfoStore" [saveIsEnable]="_configurationService.saveIsEnable"
  [undoIsEnable]="_configurationService.undoIsEnabled" confirmButtonString="Salva"
  (saveButtonClick)="saveConfigurationRules()" (undoButtonClick)="undoModifications()"
  (restoreButtonClick)="restoreModifications()">
</app-navigation-footer>