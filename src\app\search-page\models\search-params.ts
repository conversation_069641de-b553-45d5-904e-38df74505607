export class SearchParams {
  page: number;
  pageSize: number;
  orderBy: string;
  asc: boolean;
  filter: any;
  excel: boolean;

  constructor(
    page: number,
    pageSize: number,
    orderBy: string,
    asc: boolean,
    filter: any,
    excel?: boolean
  ) {
    this.page = page;
    this.pageSize = pageSize;
    this.orderBy = orderBy;
    this.asc = asc;
    this.filter = filter;
    if (excel) {
      this.excel = excel;
    } else {
      this.excel = false;
    }
  }
}
