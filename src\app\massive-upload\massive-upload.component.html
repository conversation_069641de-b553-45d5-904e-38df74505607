<div class="row">
  <div class="col-sm-12 section-headline">
    <h1>{{'UBZ.SITE_CONTENT.10000001110' | translate }}</h1>
  </div>
  <div class='btn-toolbar pull-right'>
    <button type="button" id="fixedbutton" class="btn btn-empty clock-btn"
      (click)="modalIsOpen = true">{{'UBZ.SITE_CONTENT.10000001111' | translate }}</button>
  </div><br />
  <section class="dashboard">
    <div class="col-sm-12">
      <table class="uc-table">
        <thead class="thead-default">
          <tr>
            <th scope="col">{{'UBZ.SITE_CONTENT.10000010000' | translate }}</th>
            <th scope="col">{{'UBZ.SITE_CONTENT.10110' | translate }}</th>
            <th scope="col">{{'UBZ.SITE_CONTENT.10111' | translate }}</th>
            <th scope="col" style="width: 20%">{{'UBZ.SITE_CONTENT.10010110' | translate }}</th>
            <th scope="col" style="width: 24%">{{'UBZ.SITE_CONTENT.10010010' | translate }}</th>
            <th scope="col" style="width: 7%">{{'UBZ.SITE_CONTENT.1101000010' | translate }}</th>
            <th scope="col" style="width: 12%">{{'UBZ.SITE_CONTENT.10000010001' | translate }}</th>
            <th scope="col" style="width: 5%">{{'UBZ.SITE_CONTENT.10000010010' | translate }}</th>
            <th scope="col" style="width: 5%"></th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of data; let i = index">
            <td attr.data-label="Progressive">{{item.progTec}}</td>
            <td attr.data-label="Utente">{{item.userName}}</td>
            <td attr.data-label="Data">
              <div *ngIf="item.acquiredDate" style="font-size: 15px">
                {{item.acquiredDate | date: 'dd-MM-y'}} {{item.acquiredDate | customTime}}
              </div>
            </td>
            <td attr.data-label="Descrizione">{{item.description}}</td>
            <td attr.data-label="Tipo">
              {{(item.fileType && domains[item.fileType]) ? (domains[item.fileType].translationCod | translate) : ''}}
            </td>
            <!-- <td attr.data-label="Tipo" style="text-align: center">{{item.fileType}}</td> -->
            <td attr.data-label="Esito">
              <span class="state" [ngClass]="{'green' : item.result, 'red' : !item.result}"></span>
            </td>
            <td *ngIf="!isCurrentlyLoading(item)" attr.data-label="Acquisito">
              <button *ngIf="item.acquired === 'N' && item.result === true" type="button" class="btn btn-empty"
                data-toggle="tooltip" (click)="carica(i)">
                {{'UBZ.SITE_CONTENT.10000010011' | translate }}
              </button>
              <span *ngIf="item.acquired !== 'N' && item.result === true" class="state"
                [ngClass]="{'green' : item.acquired ==='Y', 'red' : item.acquired ==='E', 'yellow' : item.acquired ==='W'}"></span>
            </td>
            <td *ngIf="isCurrentlyLoading(item)" attr.data-label="Acquisito">
              {{'UBZ.SITE_CONTENT.11111011100' | translate }}
            </td>
            <td scope="col" class="col-sm-1">
              <div class="text-center">
                <button *ngIf="showLogModal(item)" type="button" id="fixedbutton" class="btn btn-empty"
                  (click)="creaItemLog(item)">
                  <i class="icon-note"></i>
                </button>
              </div>
            </td>
            <!-- Freccia per entrare nel dettaglio visibile solo per oggetti perizia (MASSLOAP3) e con esito === true and acquisito Y,E,W -->
            <td><a *ngIf="goToUploadedAppraisalPage(item)" [routerLink]="['/upload-list', item.progTec]"><i
                  class="icon-angle-double-right"></i></a>
              <a *ngIf="goToUploadedAssetPage(item)" [routerLink]="['/upload-asset-list', item.progTec]"><i
                  class="icon-angle-double-right"></i></a>
            </td>
          </tr>
        </tbody>
      </table>
      <br>
      <div class="row" *ngIf="listSize > 10">
        <div class="col-sm-6">
          <div class="results">
            <span>{{'UBZ.SITE_CONTENT.1111111' | translate }}</span>
            <div class="custom-select">
              <select class="form-control" [(ngModel)]="pageSize" (change)="changePageSize()">
                <option value="10" selected>10 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}</option>
                <option *ngIf="listSize > 20" value="20">20 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}
                </option>
                <option *ngIf="listSize > 30" value="30">30 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}
                </option>
                <option *ngIf="listSize > 40" value="40">40 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}
                </option>
                <option *ngIf="listSize > 50" value="50">50 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}
                </option>
                <option *ngIf="listSize <= 50" [ngValue]="listSize">{{listSize}}
                  {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}</option>
                  <option value="{{listSize}}">{{listSize}} {{'UBZ.SITE_CONTENT.10000000' | translate }} {{listSize}}</option>
              </select>
            </div>
          </div>
        </div>
        <div class="col-sm-6 rightArrow" >
          <nav aria-label="Page navigation" class="pull-right">
            <pagination [boundaryLinks]="true" [directionLinks]="false" [totalItems]="listSize" [ngModel]="page"
              [itemsPerPage]="pageSize" (pageChanged)="changePage($event)" class="pagination" previousText="&lsaquo;"
              nextText="&rsaquo;" firstText="&laquo;" lastText="&raquo;" [maxSize]="10"></pagination>
          </nav>
        </div>
      </div>
    </div>
  </section>
</div>

<div *ngIf="openLogModal" class="modal fade" bsModal tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
  (onHidden)="closeLogModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.10000010100' | translate }}</h2>
        <button type="button" class="close pull-right" aria-label="Close" (click)="closeLogModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="row" *ngFor="let it of getErrorList(); let ind = index"
          style="padding-right: 20px;padding-left: 20px;">
          <div class="col-sm-6 form-group">
            <label> {{it.key}} </label>
          </div>
          <div class="col-sm-6 form-group">
            <label> {{it.value}} </label>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


<div *ngIf="modalIsOpen" class="modal fade" bsModal tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
  (onHidden)="closeUploadModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.11010011' | translate }}</h2>
        <button type="button" class="close pull-right" aria-label="Close" (click)="closeUploadModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="row">
          <div class="form-group">
            <div class="col-sm-12">
              <div class="custom-select">
                <select class="form-control" [(ngModel)]="selectedPhase" name="selectedPhase" required>
                  <option value="" hidden>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let elem of (domains | domainMapToDomainArray)" value="{{elem.domCode}}">
                    {{elem.translationCod | translate}}</option>
                </select>
              </div><br />
            </div>
            <div class="col-sm-12">
              <label>{{'UBZ.SITE_CONTENT.1110100' | translate }}</label>
              <textarea class="form-control" placeholder="Inserisci qui la descrizione del documento da aggiungere"
                name="descrizione" [(ngModel)]="descrizione">
            </textarea>
              <br />
            </div>
          </div>
          <div class="col-sm-12">
            <h4>{{'UBZ.SITE_CONTENT.11000001' | translate }}</h4>
            <div class="input-group">
              <input type="text" class="form-control" readonly [value]="fileName">
              <label class="input-group-btn">
                <span class="btn btn-primary waves-effect">
                  {{'UBZ.SITE_CONTENT.11000011' | translate }}&hellip; <input type="file" #fileToUpload id="sfoglia"
                    style="display: none;" multiple (change)="setFile()" required>
                </span>
              </label>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button id="upload-filedoc" type="submit" class="btn btn-primary waves-effect" data-dismiss="modal"
            [disabled]="fileName === '' " (click)="clickOnUploadButton()">
            {{'UBZ.SITE_CONTENT.110101000' | translate }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>