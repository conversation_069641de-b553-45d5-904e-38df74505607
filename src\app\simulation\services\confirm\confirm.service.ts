import { Injectable } from '@angular/core';
import { Http, Response } from '@angular/http';

import { Observable } from 'rxjs/Observable';

@Injectable()
export class ConfirmService {
  constructor(private http: Http) {}

  public startAppraisalRequest(positionId: string): Observable<any> {
    const url = '/UBZ-ESA-RS/service/reqPer/v1/reqAppraisals/fromSimulation';
    return this.http
      .post(url, { positionId: positionId, ndg: '' })
      .map((resp: Response) => resp.json());
  }

  public startBPMProcessFromAppraisal(positionId: string) {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/bpmProcess/v1/BpmProcess/${positionId}`;
    return this.http.post(url, {}).map((resp: Response) => resp.json());
  }

  public checkRestrictionAssets(positionId: string) {
    positionId = encodeURIComponent(positionId);
    const url = `/UBZ-ESA-RS/service/reqPer/v1/reqAppraisals/${positionId}/wizardType`;
    return this.http.get(url).map((resp: Response) => resp.json());
  }
  
}
