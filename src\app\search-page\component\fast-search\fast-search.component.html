<div class="row">
  <div class="col-sm-12 form-group section-headline">
    <h1 *ngIf="searchPageService.fastSearch['searchText']">{{'UBZ.SITE_CONTENT.10111111' | translate }} per</h1>
    <span *ngIf="searchPageService.fastSearch['searchText']" class="badge search-tag">{{searchPageService.fastSearch['searchText']}}</span>
    <div class="uc-datatabs">
      <!-- Nav tabs -->
      <ul class="nav nav-tabs" role="tablist">
        <li *ngIf="visibleVoiceMenu.PE" role="presentation" [class]="activeVoiceMenu['PE']"><a aria-controls="perizie" role="tab" data-toggle="tab" (click)="setActiveVoiceMenu('PE')">{{'UBZ.SITE_CONTENT.110' | translate }} <span class="Search__Tab__ResultCount">({{(searchPageService && searchPageService.fastSearch && searchPageService.fastSearch['searchResults']) ? searchPageService.fastSearch['searchResults'].appraisals.length : 0}})</span></a></li>
        <li *ngIf="visibleVoiceMenu.RI" role="presentation" [class]="activeVoiceMenu['RI']"><a aria-controls="perizie" role="tab" data-toggle="tab" (click)="setActiveVoiceMenu('RI')">{{'UBZ.SITE_CONTENT.1100100' | translate }} <span class="Search__Tab__ResultCount">({{(searchPageService && searchPageService.fastSearch && searchPageService.fastSearch['searchResults']) ? searchPageService.fastSearch['searchResults'].requests.length : 0}})</span></a></li>
     <!--   <li *ngIf="visibleVoiceMenu.AS" role="presentation" [class]="activeVoiceMenu['AS']"><a aria-controls="asset" role="tab" data-toggle="tab" (click)="setActiveVoiceMenu('AS')">{{'UBZ.SITE_CONTENT.10001111' | translate }} <span class="Search__Tab__ResultCount">({{(searchPageService && searchPageService.fastSearch && searchPageService.fastSearch['searchResults']) ? searchPageService.fastSearch['searchResults'].assets.length : 0}})</span></a></li> -->
      </ul>

      <!-- Tab panes -->
      <div class="tab-content">
        <div *ngIf="activeVoiceMenu['RI'] === 'active'" role="tabpanel" class="tab-pane active" id="perizie">
          <div
            *ngIf="searchPageService.fastSearch['searchResults'].requests.length === 0"
            class="Search__NoResults"
          >
            <div class="Search__NoResults__Icon">
              <i class="icon-search"></i>
            </div>
            <div class="Search__NoResults__Text">
              <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1001011001' | translate }}</h3>
              <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011010' | translate }}</p>
            </div>
          </div>

          <ng-container *ngIf="searchPageService.fastSearch['searchResults'].requests.length > 0">
            <table class="Search__ResultsTable table table-hover">
              <thead>
                <tr>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10100101' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.1001110' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.1101101' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10010010' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10000001' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10011000100' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.1011001' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.1110001' | translate }}</th>
                  <th scope="col"></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let row of searchPageService.fastSearch['searchResults'].requests">
                  <td data-label="A">{{row.id}}</td>
                  <td data-label="B">{{row.ndg}}</td>
                  <td data-label="C">{{row.heading}}</td>
                  <td data-label="D">{{ (appraisalTypes && appraisalTypes[row.type]) ? (appraisalTypes[row.type].translationCod | translate) : ('' | translate)}}</td>
                  <td data-label="E">{{ (statusPhase && statusPhase[row.phase + row.status]) ? (statusPhase[row.phase + row.status].translationStatusCod | translate) : ('' | translate)}}</td>
                  <td data-label="G">{{(macroProcessDomain && macroProcessDomain[row.macroprocess]) ? (macroProcessDomain[row.macroprocess].translationCod | translate) : ''}}</td>
                  <td data-label="H" *ngIf="appraisalTypeDomain && appraisalTypeDomain[row.appraisalType]">
                    {{appraisalTypeDomain[row.appraisalType].translationCod | translate}}
                  </td>
                  <td data-label="H" *ngIf="appraisalTypeDomain && !appraisalTypeDomain[row.appraisalType]">
                    {{row.appraisalType}}
                  </td>
                  <td data-label="I">{{(scopeTypeDomain && scopeTypeDomain[row.finOperation]) ? (scopeTypeDomain[row.finOperation].translationCod | translate) : ''}}</td>
                  <ng-container *appAuthKey="'UBZ_FAST.SEARCH_GO.REQUEST'">
                    <td data-label=""><a role="button" (click)="goToAppraisalRequest(row.id) ; $event.stopPropagation()"><i class="icon-angle-double-right"></i></a></td>
                  </ng-container>
                </tr>
              </tbody>
            </table>
          </ng-container>
        </div>

        <div *ngIf="activeVoiceMenu['PE'] === 'active'" role="tabpanel" class="tab-pane active" id="perizie">
          <div
            *ngIf="searchPageService.fastSearch['searchResults'].appraisals.length === 0"
            class="Search__NoResults"
          >
            <div class="Search__NoResults__Icon">
              <i class="icon-search"></i>
            </div>
            <div class="Search__NoResults__Text">
              <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1001011001' | translate }}</h3>
              <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011010' | translate }}</p>
            </div>
          </div>

          <ng-container *ngIf="searchPageService.fastSearch['searchResults'].appraisals.length > 0">
            <table class="Search__ResultsTable table table-hover">
              <thead>
                <tr>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10010001' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10100101' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.1001110' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.1101101' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10010010' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10000001' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10011001110' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.10011000100' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.1011001' | translate }}</th>
                  <th scope="col">{{'UBZ.SITE_CONTENT.1110001' | translate }}</th>                
                  <th scope="col"></th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let row of searchPageService.fastSearch['searchResults'].appraisals">
                  <td data-label="A">{{row.appraisalId}}</td>
                  <td data-label="A">{{row.id}}</td>
                  <td data-label="B">{{row.ndg}}</td>
                  <td data-label="C">{{row.heading}}</td>
                  <td data-label="D">{{ (appraisalTypes && appraisalTypes[row.type]) ? (appraisalTypes[row.type].translationCod | translate) : ('' | translate)}}</td>
                  <td data-label="E">{{ (statusPhase && statusPhase[row.phase + row.status]) ? (statusPhase[row.phase + row.status].translationStatusCod | translate) : ('' | translate)}}</td>
                  <td data-label="F">{{row.insertDateApp ? ((row.insertDateApp | date: 'dd-MM-y')  + ' ' + (row.insertDateApp | customTime)) : ''}}</td>
                  <td data-label="G">{{(macroProcessDomain && macroProcessDomain[row.macroprocess]) ? (macroProcessDomain[row.macroprocess].translationCod | translate) : ''}}</td>
                  <td data-label="H" *ngIf="appraisalTypeDomain && appraisalTypeDomain[row.appraisalType]">
                    {{appraisalTypeDomain[row.appraisalType].translationCod | translate}}
                  </td>
                  <td data-label="H" *ngIf="appraisalTypeDomain && !appraisalTypeDomain[row.appraisalType]">
                    {{row.appraisalType}}
                  </td>
                  <td data-label="I">{{(scopeTypeDomain && scopeTypeDomain[row.finOperation]) ? (scopeTypeDomain[row.finOperation].translationCod | translate) : ''}}</td>                  
                  <ng-container *appAuthKey="'UBZ_FAST.SEARCH_GO.DETAILS'">
                    <td data-label=""><a role="button" (click)="goToDetailsPage(row.appraisalId) ; $event.stopPropagation()"><i class="icon-angle-double-right"></i></a></td>
                  </ng-container>
                </tr>
              </tbody>
            </table>
          </ng-container>
        </div>

       

        </div>

            </div>
            </div>
</div>
