import { Component, OnInit, Input } from '@angular/core';
import { Observable } from 'rxjs/Observable';
import { DomainService } from '../../../shared/domain/domain.service';
import { GenericTaskService } from '../../../tasks/services/generic-task/generic-task.service';
import { ConfigurationService } from '../../service/configuration.service';
import { ExpertAssignmentConfigurationRule } from '../../configuration.models';
@Component({
  selector: 'app-expert-assignment-configuration',
  templateUrl: './expert-assignment-configuration.component.html',
  styleUrls: ['./expert-assignment-configuration.component.css']
})
export class ExpertAssignmentConfigurationComponent implements OnInit {
  @Input() configurations: any[] = [];
  public macroProcessTypes: any[];
  public posSegments: any[];
  public appraisalTypes: any[];
  public resItemTypes: any[];
  public categoryTypes: any[];
  public expiredCategoryTypes: any[];
  public expertTypes: any[];
  public appraisalOwners: any[];
  public expertList: any[];
  public editAction = false;
  public selectedRule: ExpertAssignmentConfigurationRule = new ExpertAssignmentConfigurationRule();
  private selectedRuleIndex = -1;
  public ruleToExpand: ExpertAssignmentConfigurationRule = new ExpertAssignmentConfigurationRule();

  constructor(
    private _domainService: DomainService,
    private _genericTaskService: GenericTaskService,
    private _configurationService: ConfigurationService
  ) {}

  ngOnInit() {
    Observable.forkJoin(
      this._domainService.newGetDomain('UBZ_DOM_MACRO_PROCESS'),
      this._domainService.newGetDomain('UBZ_DOM_POS_SEGMENT'),
      this._domainService.newGetDomain('UBZ_DOM_APPRAISAL_TYPE'),
      this._domainService.newGetDomain('UBZ_DOM_RESITEM_TYPE'),
      this._domainService.newGetDomain('UBZ_DOM_CATEGORY_TYPE'),
      this._domainService.newGetDomain('UBZ_DOM_EXPERT_TYPE'),
      this._domainService.newGetDomain('UBZ_DOM_APPRAISAL_OWNER'),
      this._genericTaskService.getExpertList('0', 'SOC', 0, 0),
      this._domainService.newGetDomain('UBZ_DOM_CATEGORY_TYPE','-',true)
      
    ).subscribe(res => {
      this.macroProcessTypes = res[0];
      this.posSegments = res[1];
      this.appraisalTypes = res[2];
      this.resItemTypes = res[3];
      this.categoryTypes = res[4];
      this.expertTypes = res[5];
      this.appraisalOwners = res[6];
      this.expertList = res[7].listExpSocPer;
      this.expiredCategoryTypes = res[8];
    });
  }

  goToNewRule() {
    this.selectedRule = new ExpertAssignmentConfigurationRule();
    this._configurationService.setSaveButtonState(false);
    this.editAction = true;
  }

  modifySelectedRule(item) {
    this.selectedRule = JSON.parse(JSON.stringify(item));
    this.selectedRule.bornDate = new Date(this.selectedRule.bornDate);
    this.selectedRule.deadDate = new Date(this.selectedRule.deadDate);
    this.selectedRuleIndex = this.configurations.indexOf(item);
    this._configurationService.setSaveButtonState(false);
    this.editAction = true;
  }

  public calculateNewItemCategories(): void {
    this._domainService
      .newGetDomain('UBZ_DOM_CATEGORY_TYPE', this.selectedRule.resItemType)
      .subscribe(res => {
        this.categoryTypes = res;
      });
  }

  public confirmEditAction(): void {
    const newRule = JSON.parse(JSON.stringify(this.selectedRule));
    if (this.selectedRuleIndex !== -1) {
      this.configurations[this.selectedRuleIndex] = newRule;
    } else {
      this.configurations.push(newRule);
    }
    this.closeEditAction();
  }

  public abortEditAction(): void {
    this.closeEditAction();
  }

  private closeEditAction(): void {
    this.selectedRuleIndex = -1;
    this.editAction = false;
    this._configurationService.setSaveButtonState(true);
  }

  public removeRule(item: ExpertAssignmentConfigurationRule) {
    item.activeFlag = false;
  }

  public getAssignedExpertHeading(socId: number): string {
    for (const expert of this.expertList) {
      if (expert.idSocPer === socId) {
        return expert.heading;
      }
    }
  }

  public selectRuleToExpand(item: ExpertAssignmentConfigurationRule): void {
    if (this.ruleToExpand.ruleId === item.ruleId) {
      this.ruleToExpand = new ExpertAssignmentConfigurationRule();
    } else {
      this.ruleToExpand = item;
    }
  }
}
