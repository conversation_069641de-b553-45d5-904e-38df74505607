import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>n,
  <PERSON><PERSON><PERSON>ist,
  ViewChild,
  <PERSON><PERSON><PERSON><PERSON><PERSON>he<PERSON>,
  ChangeDetectorRef,
} from '@angular/core';
import { Subscription } from 'rxjs/Subscription';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { TranslateService } from '@ngx-translate/core';

// Models
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';

// Components
import { AccordionApplicationFormComponent } from '../../../shared/application-form/components/accordion-application-form/accordion-application-form.component';

// Services
import { AppraisalCompilationService } from '../../service/appraisal-compilation.service';
import { MenuService } from '../../../shared/menu/services/menu.service';
import { LandingService } from '../../../simulation/services/landing/landing.service';
import { PositionService } from '../../../shared/position/position.service';
import { GenericTaskService } from '../../../tasks/services/generic-task/generic-task.service';
import { MessageService } from '../../../shared/messages/services/message.service';
import { EvaluationService } from '../evaluations/service/evaluation.service';
import { DomainService } from '../../../shared/domain';
import { Subject } from 'rxjs';
import { SharedService } from '../../../shared/services/shared.service';

@Component({
  selector: 'app-guarantees',
  templateUrl: './guarantees.component.html',
  styleUrls: ['./guarantees.component.css'],
  providers: [
    AppraisalCompilationService,
    GenericTaskService,
    EvaluationService,
  ]
})
export class GuaranteesComponent
  implements OnInit, OnDestroy, AfterViewChecked
{
  @ViewChildren(AccordionApplicationFormComponent)
  appForms: QueryList<AccordionApplicationFormComponent>;
  @ViewChild('property')
  property;
  @ViewChild('installation')
  installation;
  @ViewChild('origin')
  origin;
  @ViewChild('buildingMeasures')
  buildingMeasures;
  @ViewChild('buildinfAmnesty')
  buildinfAmnesty;
  @ViewChild('constructionSiteSecurity')
  constructionSiteSecurity;
  @ViewChild('consistency')
  consistency;
  @ViewChild('renovationManufactured')
  renovationManufactured;
  @ViewChild('templateValidation')
  templateValidation;

  positionId: string;
  wizardCode: string;
  currentTask: string;
  isEditable = true;
  newAssetModalIsOpen = false;
  deleteAssetModalIsOpen = false;

  assets: any[] = [];
  selectedAsset: any = {};

  endDate: string;
  inspectionDate: string;
  staticPageContent;
  today: Date;

  bpmTaskId: string;
  bpmTaskCod: string;
  agrarianLoan: boolean;
  private _subscription;
  private _thirdOpinionSubscription;
  destroyed$: Subject<void> = new Subject<void>();
  thirdOpinion: boolean;
  opinionDifferences: any[] = [];
  thirdOpinionDifferences: any = {};
  isAppraisalConvalidated: boolean;
  isShipping: boolean;
  isAeremobile: boolean;
  isAValidPage = false;
  // Flag per disabilitare accordion statitici
  // (per SAL, Fine lavori, Frazionamento o perizie migrate):
  haveDisabledFields = false;
  // Flag per forzare la disibilitazione del accordion dinamici (apf)
  // (per Fine lavori, Frazionamento o perizie migrate):
  forceApfDisabled: boolean = false;
  // Disabilita alcuni campi negli accordion statici:
  // "CONSISTENZA" (valore immediato utilizzo, valore allo stato attuale e note)
  // e "COSTI DI REALIZZAZIONE/RISTRUTTURAZIONE FABBRICATO" (campi % a destra)
  haveDisabledFieldsSpecial = false;
  // Disabilita i campi rimanenti nell'accordion statico:
  // e "COSTI DI REALIZZAZIONE/RISTRUTTURAZIONE FABBRICATO" (tutti tranne: campi % a destra)
  haveDisabledFieldsRenovationManifactured = false;
  // Impedisce creazione, copia e cancellazione asset
  canAddDeleteAsset = true;
  appraisalType: string;
  mobileScope: string;
  requestId: string;
  appraisalInfo: any;
  isInterno: boolean;
  isEsterno: boolean;
  isTemplateUpdate: boolean;
  minAdmissibleDate: Date = new Date(1900, 1, 1);
  draftButtonCallback = this.saveDraft.bind(this);
  isMarketValueRequired = false;
  private previousSelectedAssetStatus: string;
  private subscribedFormsIndex = 1;
  private existingForms: any[] = [];
  private subscribedForms: Object = {};
  private formSubscriptions: Subscription[] = [];
  activeCategories: any = [];
  activeDomain: any = [];
  mapApf: any = new Map();
  fieldObject: any;
  domainsFromChildsObj: Object = {};
  domainsConsistencyObj = {};
  domainsBuildingMeasuresObj = {};
  domainList = [
    'UBZ_DOM_CATEGORY_TYPE',
    'UBZ_DOM_MOTIVATIONS_DISABLE',
    'UBZ_DOM_FLOOR_DIRECT_TYPE',
    'UBZ_DOM_FLOOR_TYPE',
    'UBZ_DOM_IND_MEASUREMENT',
    'UBZ_DOM_SANCTION_TYPE',
    'UBZ_DOM_PROJECT_CORR',
    'UBZ_DOM_PROVISION_STATUS',
    'UBZ_DOM_PERSON_ROLE',
    'UBZ_DOM_ORIGIN_TYPE',
    'UBZ_DOM_RIGHT_TYPE'
  ];

  constructor(
    public menuService: MenuService,
    public domainService: DomainService,
    public _activatedRoute: ActivatedRoute,
    public _landingService: LandingService,
    private _router: Router,
    private _appraisalCompilationService: AppraisalCompilationService,
    public _positionService: PositionService,
    private _genericTaskService: GenericTaskService,
    @Inject(APP_CONSTANTS) public constants: IAppConstants,
    private _messageService: MessageService,
    private _evaluationService: EvaluationService,
    private _translateService: TranslateService,
    private sharedService: SharedService,
    protected _cdRef: ChangeDetectorRef

  ) { }

  ngOnInit() {
    this.sharedService.getCheckboxChangeSubj().takeUntil(this.destroyed$).subscribe(data => {
      if (this.appForms) {
        for (const apf of this.appForms.toArray()) {
          this.createMapAndUpdate(apf);
        }
      }
    });

    this._subscription = this._activatedRoute.parent.params
      .switchMap((params: Params) => {
        this.positionId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        this.bpmTaskId = params['taskId'];
        this.bpmTaskCod = params['taskCod'];
        return Observable.forkJoin(
          this._appraisalCompilationService.getAssociateAssets(this.positionId),
          this._positionService.getAppraisalInfo(this.positionId),
          this._positionService.consistencyIsMarketValueMandatory(
            this.positionId
          )
        );
      })
      .switchMap((forkResponse) => {
        this.setAppraisalInfo(forkResponse[1]);
        this.isMarketValueRequired = forkResponse[2] ? forkResponse[2] : false;

        if (forkResponse[0].length !== 0) {
          this.assets = forkResponse[0];
          this.selectedAsset = this.assets[0];
          this.hasDisabledFields();
          if (this.thirdOpinion) {
            this.getDifferenceBetweenOpinion();
          }
          return this.getStatics(this.selectedAsset.idObject);
        } else {
          return Observable.empty();
        }
      })
      .takeUntil(this.destroyed$).subscribe((staticsResponse) => {
        if (!staticsResponse.partialSaveData) {
          this.staticPageContent = staticsResponse;
        } else {
          const draftStuff = JSON.parse(staticsResponse.partialSaveData);
          if (
            staticsResponse['objevaluation'] &&
            staticsResponse['objevaluation'].evaluationId &&
            staticsResponse['objevaluation'].objectCod
          ) {
            draftStuff['objevaluation'].evaluationId =
              staticsResponse['objevaluation'].evaluationId;
            draftStuff['objevaluation'].objectCod =
              staticsResponse['objevaluation'].objectCod;
          }
          this.staticPageContent = draftStuff;
          this.restoreSavedListSection(staticsResponse);
        }
        if (
          !(this.isShipping || this.isAeremobile) &&
          this.staticPageContent &&
          !this.staticPageContent.ownershipdetails
        ) {
          this.staticPageContent.ownershipdetails = [];
        }
        this.processTemplateUpdateActualValue();
        this.dataFormat();
        setTimeout(() => {
          this.pageIsValid();
        }, 0);
      });
    this.today = new Date();
    
    this.getDomainList(this.domainList);

  } 
  
  getDomainList(domainList) {
    this.domainService.getDomainList(domainList).subscribe(data => {
      domainList.forEach((domainName: string) => {
        if(domainName === 'UBZ_DOM_CATEGORY_TYPE') {
          this.activeDomain = 
            this.domainService.transform(data['UBZ_DOM_CATEGORY_TYPE']).forEach((cat) => {
              this.activeCategories.push(cat.domCode);
            });
        }
        else if(domainName === 'UBZ_DOM_FLOOR_DIRECT_TYPE' || domainName === 'UBZ_DOM_FLOOR_TYPE' || domainName === 'UBZ_DOM_IND_MEASUREMENT') {
          this.domainsConsistencyObj[domainName] = data[domainName];
        }
        else if(domainName === 'UBZ_DOM_PROJECT_CORR' || domainName === 'UBZ_DOM_PROVISION_STATUS') {
          this.domainsBuildingMeasuresObj[domainName] = data[domainName];
        }
        else {
          this.domainsFromChildsObj[domainName] = data[domainName];
        }

        });
        
    });
  }

  /**
   * @name setAppraisalInfo
   * @description Metodo invocato sul recupero dell'oggetto perizia, imposta le variabili ad esso connesse
   * @param appraisalInfo Oggetto perizia recuperto dal wizard container
   */
  setAppraisalInfo(appraisalInfo) {
    this.appraisalInfo = appraisalInfo;
    this._landingService.posSegment = appraisalInfo.appraisal.posSegment;
    this.checkProvenienza();
    this.agrarianLoan =
      appraisalInfo.appraisal.loanScope === 'MUT' ? true : false;
    this.appraisalType = appraisalInfo.appraisal.appraisalType;
    this.requestId = appraisalInfo.requestId;
    this.isAppraisalConvalidated = appraisalInfo.isConvalidated;

    // Check if is template update (template di aggiornamento perizia)
    this.isTemplateUpdate =
      appraisalInfo.templateType && appraisalInfo.templateType === 'AGL';
    if (appraisalInfo.appraisal.resItemType === 'MOB') {
      if (
        this._appraisalCompilationService.getMobileType(
          appraisalInfo.appraisalObject
        ) === this.constants.appraisalTypes.AEREOMOBILE
      ) {
        this.isShipping = false;
        this.isAeremobile = true;
        this.mobileScope = this.constants.appraisalTypes.AEREOMOBILE;
      } else {
        this.isShipping = true;
        this.isAeremobile = false;
        this.mobileScope = 'NAU';
      }
    }
    if (this.isAeremobile || this.isShipping) {
      this.currentTask = 'UBZ-PER-GAR';
    } else {
      this.currentTask = 'UBZ-PER-IGA';
    }
    this._landingService.checkIfCompletedTask(
      this.positionId,
      this.currentTask
    );
    this.thirdOpinion = appraisalInfo.opinionType === 'TO';
  }

  ngAfterViewChecked() {
    if (this.appForms) {
      for (const apf of this.appForms.toArray()) {
        for (const form of apf.listaForm.toArray()) {
          if (this.existingForms.indexOf(form) === -1) {
            this.existingForms.push(form);
            const key = this.subscribedFormsIndex.toString();
            this.subscribedFormsIndex++;
            this.subscribedForms[key] = false;
            if (!this.subscribedForms[key]) {
              this.formSubscriptions.push(
                form.statusChanges.subscribe(() => {
                  this.createMapAndUpdate(apf); 
                  setTimeout(() => {
                    this.pageIsValid();
                    this.subscribedForms[key] = true;
                  }, 0);
                })
              );
            }
          }
        }
      }
    }
    this._cdRef.detectChanges();
  }

  createMapAndUpdate(apf: AccordionApplicationFormComponent) {
      for (const form of apf.applicationForms) {
        for (const sect of form.sections) {
          for (const row of sect.rows) {
            for (const field of row.fields) {
              //create a data dictionary object
              this.mapApf.set(field.code, apf.getModelForField(form.applicationForm, field.code));
              this.fieldObject = Array.from(this.mapApf).reduce((obj, [key, value]) => (Object.assign(obj, { [key]: value }) // Be careful! Maps can have non-String keys; object literals can't.
              ), {});

            }

          }
        }
      }
  }

  private restoreSavedListSection(contentFromService: any): void {
    this.staticPageContent['principalOwner'] =
      contentFromService['principalOwner'];
    this.staticPageContent['systemObject'] = contentFromService['systemObject'];
    this.staticPageContent['originObject'] = contentFromService['originObject'];
    this.staticPageContent['buildingProv'] = contentFromService['buildingProv'];
    this.staticPageContent['buildingSanc'] = contentFromService['buildingSanc'];
  }

  private checkProvenienza(): void {
    switch (this.appraisalInfo.appraisal.surveyNecFlag) {
      case 'INTERNO':
      case 'INT':
      case 'I':
        this.isInterno = true;
        break;
      case 'ESTERNO':
      case 'EST':
      case 'E':
        this.isEsterno = true;
        break;
      default:
        this.isInterno = false;
        this.isEsterno = false;
    }
  }

  ngOnDestroy() {
    this._subscription.unsubscribe();
    if (this._thirdOpinionSubscription) {
      this._thirdOpinionSubscription.unsubscribe();
    }
    for (const sub of this.formSubscriptions) {
      if (sub && !sub.closed) {
        sub.unsubscribe();
      }
    }
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  changeSelectedAsset(asset: any) {
    this.selectedAsset = undefined;
    this.getStatics(asset.idObject).takeUntil(this.destroyed$).subscribe((y) => {
      if (!y.partialSaveData) {
        this.staticPageContent = y;
      } else {
        this.staticPageContent = JSON.parse(y.partialSaveData);
        this.restoreSavedListSection(y);
      }
      this.dataFormat();
      const assetId = asset.idObject.toString();
      if (this.thirdOpinionDifferences[assetId]) {
        this.opinionDifferences =
          this.thirdOpinionDifferences[assetId]['mapDiffOpinion'];
      } else {
        this.opinionDifferences = [];
      }
      this.selectedAsset = asset;
      this.processTemplateUpdateActualValue();
      this.pageIsValid();
      this.hasDisabledFields();
    });
  }

  getStatics(idObject: string): Observable<any> {
    if (this.isShipping || this.isAeremobile) {
      return this._appraisalCompilationService.getShippingStatics(idObject);
    }
    // Se non è shipping viene tornata la risposta nel caso di tutti gli altri servizi
    return this._appraisalCompilationService.getStaticComponentsOfPropertyGuarantee(
      idObject
    );
  }

  processTemplateUpdateActualValue() {
    if (this.isTemplateUpdate) {
      this.staticPageContent['objevaluation']['actualValue'] = null;
    }
  }

  getDifferenceBetweenOpinion() {
    this._thirdOpinionSubscription = this._genericTaskService
      .getDifferencesBetweenOpinions(this.positionId)
      .takeUntil(this.destroyed$).subscribe((res) => {
        const assetId = this.assets[0].idObject.toString();
        this.thirdOpinionDifferences = res;
        if (this.thirdOpinionDifferences[assetId]) {
          this.opinionDifferences =
            this.thirdOpinionDifferences[assetId]['mapDiffOpinion'];
        } else {
          this.opinionDifferences = [];
        }
      });
  }

  save() {
    this.saveData(this.selectedAsset.idObject)
      .switchMap(() => {
        if (this.isMarketValueRequired) {
          return this._positionService.consistencyValidateEvaluation(
            this.positionId,
            this.selectedAsset.idObject
          );
        } else {
          return Observable.of(true);
        }
      })
      .switchMap(() => {
        if (this.templateValidation) {
          return this._appraisalCompilationService.saveTemplateValidation(
            this.templateValidation.appValidation
          );
        } else {
          return Observable.of(true);
        }
      })
      .takeUntil(this.destroyed$).subscribe(
        (x) => {
          this.isEditable = false;
          this.refreshAndRemainOnAssetTab(this.selectedAsset);
        },
        (err) => {
          this.selectedAsset.conclusionStatusFlag =
            this.previousSelectedAssetStatus;
        }
      );
  }

  refreshAndRemainOnAssetTab(selectedAsset) {
    this.refreshPageAfterSalva(selectedAsset);
    this.changeSelectedAsset(selectedAsset);
  }

  // Prepara le strutture da inserire nel servizio di salvataggio
  // Attraverso la differenziazione tra (isShipping | isAeromobile) e la differenziazione tra ndg individual e corporate
  // imposta le variabili utilizzate dal servizio saveImmGaranzia per generare la url di salvataggio corretta
  private saveData(idObject: string): Observable<any> {
    this.previousSelectedAssetStatus = this.selectedAsset.conclusionStatusFlag;
    this.selectedAsset.conclusionStatusFlag = 'Y';
    const apfMap = {};
    for (const appForm of this.appForms.toArray()) {
      const appModel = appForm.getFormsWithModel();
      for (const prop in appModel) {
        if (appModel.hasOwnProperty(prop)) {
          apfMap[prop] = appModel[prop];
        }
      }
    }
    let pageString = '';
    let urlDestinationString = '';
    if (this.isShipping || this.isAeremobile) {
      pageString = 'GARANZIA';
      urlDestinationString = 'SHI';
    } else {
      this.convertNumbers();
      pageString = 'IMM_GARANZIA';
      urlDestinationString = this._landingService.posSegment;
    }
    this.processTemplateUpdateActualValue();
    return this._appraisalCompilationService.saveImmGaranzia(
      urlDestinationString,
      idObject,
      { page: pageString, apfmap: apfMap },
      this.staticPageContent
    );
  }

  private convertNumbers(): void {
    const propArray: string[] = [
      'totCommSurface',
      'totBookValue',
      'bookUnitValue',
      'pledgedUnitValue',
      'totPledgedValue',
      'prudentialUnitValue',
      'totPrudentialValue',
      'insuranceValue',
      'grossSurface',
      'mqPledgedValue',
      'mqValue',
      'marketValue',
      'actualValue',
      'commCoefficient',
    ];
    const consistencyData = this.staticPageContent['objevaluation'];
    if (consistencyData) {
      for (const prop of propArray) {
        if (consistencyData[prop]) {
          consistencyData[prop] = Number.parseFloat(consistencyData[prop]);
        }
        if (
          consistencyData.registryStructEvaluation &&
          consistencyData.registryStructEvaluation[prop]
        ) {
          consistencyData.registryStructEvaluation[prop] = Number.parseFloat(
            consistencyData.registryStructEvaluation[prop]
          );
        }
      }
      if (consistencyData.structseval) {
        for (const evalu of consistencyData.structseval) {
          for (const prop of propArray) {
            if (evalu[prop]) {
              evalu[prop] = Number.parseFloat(evalu[prop]);
            }
          }
        }
      }
    }
  }

  edit() {
    this.isEditable = true;
  }

  delete() {
    this.openDeleteAssetModal();
  }

  copy() {
    this._appraisalCompilationService
      .copyAsset(this.selectedAsset.idObject.toString(), this.positionId)
      .subscribe((newAssetId) => {
        this._appraisalCompilationService
          .getAssociateAssets(this.positionId)
          .subscribe((assets) => {
            for (const a of assets) {
              if (a.idObject === Number(newAssetId)) {
                this.refreshPage();
                this.changeSelectedAsset(a);
              }
            }
          });
      });
  }

  openNewAssetModal() {
    this.newAssetModalIsOpen = true;
  }

  closeNewAssetModal() {
    this.newAssetModalIsOpen = false;
    this.refreshPage();
  }

  openDeleteAssetModal() {
    this.deleteAssetModalIsOpen = true;
  }

  // Gestisce il submit della modale di cancellazione utente
  submitDeleteModal(data) {
    if (data.modalType === 'deleteAsset' && this.selectedAsset && this.selectedAsset.idObject) {
      this._appraisalCompilationService
        .motivateAssetDeletion(
          this.selectedAsset.idObject,
          data.reason,
          data.notes
        )
        .takeUntil(this.destroyed$).subscribe((res) => {
          if(res === true) {
            this.deleteSelectedAsset();
          }
        });
    } 
    else if (this.selectedAsset && this.selectedAsset.idObject) {
      this.deleteSelectedAsset();
    }
  }

  deleteSelectedAsset() {
    this._appraisalCompilationService
        .deleteAsset(this.selectedAsset.idObject)
        .takeUntil(this.destroyed$).subscribe((x) => {
          this.closeDeleteModal();
          this.refreshPage();
        });
  }

  // Chiude e resetta le variabili per la modale di cancellazione
  closeDeleteModal() {
    this.deleteAssetModalIsOpen = false;
  }

  private refreshPage() {
    this._appraisalCompilationService
      .getAssociateAssets(this.positionId)
      .takeUntil(this.destroyed$).subscribe((x) => {
        this.assets = x;
        this.changeSelectedAsset(this.assets[this.assets.length - 1]);
        this.endDate = null;
        this.inspectionDate = null;
      });
  }

  refreshPageAfterSalva(selectedAsset) {

    this._appraisalCompilationService
      .getAssociateAssets(this.positionId)
      .takeUntil(this.destroyed$).subscribe(x => {
        this.assets = x;
        this.changeSelectedAsset(selectedAsset);
        this.endDate = null;
        this.inspectionDate = null;
      });
  }


  goNextPage() {
    this.checkCollateralRegistry().takeUntil(this.destroyed$).subscribe((resCont) => {
      if (
        resCont['outcome'] === 'KO' &&
        resCont['type'] === 'W' &&
        resCont['message']
      ) {
        this._messageService.showWarning(
          this._translateService.instant(`${resCont['message']}`),
          this._translateService.instant('UBZ.SITE_CONTENT.10011101111')
        );
      }
      this._landingService
        .getNextTask(this.positionId, this.currentTask, this.wizardCode)
        .takeUntil(this.destroyed$).subscribe((res) => {
          this._landingService.refreshWizard();
          this._router.navigate([`../${this.constants.landingMap[res]}`], {
            relativeTo: this._activatedRoute,
          });
        });
    });
  }

  checkCollateralRegistry(): Observable<any> {
    //stop wizard if invalid data
    return this._evaluationService.checkCollateralRegistry(this.positionId);
  }

  goToPreviousTask() {
    this._landingService.goToPreviousTask(
      this.positionId,
      this.wizardCode,
      this._activatedRoute
    );
  }

  saveDraft(): Observable<any> {
    if (!this.isAValidPage) {
      this.selectedAsset.conclusionStatusFlag = 'N';
    }
    const apfMap = {};
    for (const appForm of this.appForms.toArray()) {
      if (appForm.drivers && appForm.drivers['DD_ORD_PAG']) {
        apfMap[appForm.drivers['DD_ORD_PAG']] = JSON.stringify(appForm.model);
      } else {
        apfMap[1] = JSON.stringify(appForm.model);
      }
    }
    const page: string =
      this.isShipping || this.isAeremobile ? 'GARANZIA' : 'IMM_GARANZIA';
    return this._landingService
      .saveDraft(
        this.positionId,
        JSON.stringify(this.staticPageContent),
        apfMap,
        page,
        this.selectedAsset.idObject
      )
      .switchMap(() => {
        if (this.templateValidation) {
          return this._appraisalCompilationService.saveTemplateValidation(
            this.templateValidation.appValidation
          );
        } else {
          return Observable.of(true);
        }
      });
  }

  draftSaved() {
    this._router.navigateByUrl(
      `generic-task/${this.positionId}/${this.bpmTaskId}/${this.bpmTaskCod}`
    );
  }

  private areStaticsAllValid(): boolean {
    if (!this.staticPageContent) {
      return false;
    }
    if (this.isShipping) {
      if (this.isInterno) {
        return (
          this.staticPageContent['surveyDate'] &&
          this.staticPageContent['inspectionDate']
        );
      } else if (this.isEsterno) {
        return (
          this.staticPageContent['externSurveyDate'] &&
          this.staticPageContent['inspectionDate']
        );
      } else {
        return this.staticPageContent['inspectionDate'];
      }
    }
    if (this.isInterno) {
      return (
        this.checkWithoutProvenienza() &&
        this.staticPageContent['surveyDate'] !== null
      );
    } else if (this.isEsterno) {
      return (
        this.checkWithoutProvenienza() &&
        this.staticPageContent['externSurveyDate'] !== null
      );
    } else {
      return this.checkWithoutProvenienza();
    }
  }

  private checkWithoutProvenienza(): boolean {
    return (
      (!this.property || (this.property && this.property.pageIsValid)) &&
      (!this.installation ||
        (this.installation && this.installation.pageIsValid)) &&
      (!this.origin || (this.origin && this.origin.pageIsValid)) &&
      (!this.buildingMeasures ||
        (this.buildingMeasures && this.buildingMeasures.pageIsValid)) &&
      (!this.buildinfAmnesty ||
        (this.buildinfAmnesty && this.buildinfAmnesty.pageIsValid)) &&
      (!this.constructionSiteSecurity ||
        (this.constructionSiteSecurity &&
          this.constructionSiteSecurity.pageIsValid)) &&
      (!this.consistency ||
        (this.consistency && this.consistency.sectionIsValid)) &&
      (!this.renovationManufactured ||
        (this.renovationManufactured &&
          this.renovationManufactured.pageIsValid)) &&
      (!this.templateValidation ||
        (this.templateValidation && this.templateValidation.form.valid)) &&
      (this.staticPageContent['docConclusionDate'] !== null ||
        this.isTemplateUpdate)
    );
  }

  private areDynamicsAllValid(): boolean {
    if (!this.appForms) {
      return false;
    }
    if (this.appForms) {
      for (const af of this.appForms.toArray()) {
        if (!af.isAllValid()) {
          return false;
        }
      }
    }
    return true;
  }

  pageIsValid() {
    if (this.areDynamicsAllValid() && this.areStaticsAllValid()) {
      setTimeout(() => {
        this.isAValidPage = true;
      }, 0);
    } else {
      setTimeout(() => {
        this.isAValidPage = false;
      }, 0);
    }
  }

  areAllAssetsValid(): boolean {
    for (const as of this.assets) {
      if (as.conclusionStatusFlag === 'N') {
        return false;
      }
    }
    return true;
  }

  private dataFormat() {
    if (!this.isShipping) {
      this.staticPageContent['docConclusionDate'] = this.staticPageContent[
        'docConclusionDate'
      ]
        ? new Date(this.staticPageContent['docConclusionDate'])
        : null;
    }
    this.staticPageContent['surveyDate'] = this.staticPageContent['surveyDate']
      ? new Date(this.staticPageContent['surveyDate'])
      : null;
    this.staticPageContent['externSurveyDate'] = this.staticPageContent[
      'externSurveyDate'
    ]
      ? new Date(this.staticPageContent['externSurveyDate'])
      : null;
    if (this.isShipping || this.isAeremobile) {
      this.staticPageContent['inspectionDate'] = this.staticPageContent[
        'inspectionDate'
      ]
        ? new Date(this.staticPageContent['inspectionDate'])
        : null;
    }
    const buildSec = this.staticPageContent['objbuildsec'];
    if (buildSec) {
      const securityData = buildSec.securityData;
      if (securityData) {
        if (securityData['DIL'] && securityData['DIL'].securDataDate) {
          securityData['DIL'].securDataDate = new Date(
            securityData['DIL'].securDataDate
          );
        }
        if (securityData['ASL'] && securityData['ASL'].securDataDate) {
          securityData['ASL'].securDataDate = new Date(
            securityData['ASL'].securDataDate
          );
        }
        if (securityData['IPL'] && securityData['IPL'].securDataDate) {
          securityData['IPL'].securDataDate = new Date(
            securityData['IPL'].securDataDate
          );
        }
        if (securityData['DPS'] && securityData['DPS'].securDataDate) {
          securityData['DPS'].securDataDate = new Date(
            securityData['DPS'].securDataDate
          );
        }
        if (securityData['PVF'] && securityData['PVF'].securDataDate) {
          securityData['PVF'].securDataDate = new Date(
            securityData['PVF'].securDataDate
          );
        }
      }
      buildSec.planningStartDate = buildSec.planningStartDate
        ? new Date(buildSec.planningStartDate)
        : null;
      buildSec.planningEndDate = buildSec.planningEndDate
        ? new Date(buildSec.planningEndDate)
        : null;
    }
  }

  /**
   * @function
   * @name hasDisabledFields
   * @description Setta i flag per disabilitare i campi degli accordion in base al tipo di appraisal o processo di origine
   */
  // FIXME - TOGLIERE CODICE COMMENTATO SE LOGICA METODO CORRETTA
  hasDisabledFields(): void {
    //Reset
    this.haveDisabledFields = false;
    this.haveDisabledFieldsSpecial = false;
    this.forceApfDisabled = false;
    this.haveDisabledFieldsRenovationManifactured = false;
    this.canAddDeleteAsset = true;

    //RESTRIZIONE: non posso aggiungere, copiare e cancellare asset
    if (this.appraisalType === this.constants.appraisalTypes.RESTRIZIONE) {
      this.canAddDeleteAsset = false;
    }

    // DISABILITIAMO COMPORTAMENTO VECCHIO
    // if (
    //   this.appraisalInfo.appraisal.originProcess === 'MIG' ||
    //   this.appraisalInfo.migParent
    // ) {
    //   //Pratiche Migrate o con genitore migrato:
    //   //tutti i campi sbloccati
    //   this.haveDisabledFields = false;
    //   this.forceApfDisabled = false;
    //   this.haveDisabledFieldsRenovationManifactured = false;
    //   return;
    // }
    // // Nel caso in cui appraisalType = SAL/FLA
    // if (
    //   this.appraisalType === this.constants.appraisalTypes.FINE_LAVORI ||
    //   this.appraisalType === this.constants.appraisalTypes.SAL
    // ) {
    //   if (this.appraisalInfo['opinionType'] === 'SO') {
    //     // Se siamo in second opinion (SAL o FLA - internal o external) si lasciano tutti i campi editabili
    //     this.haveDisabledFields = false;
    //     this.forceApfDisabled = false;
    //     this.haveDisabledFieldsRenovationManifactured = false;
    //   } else {
    //     //FLA e SAL NON second opinion
    //     //Blocchiamo la maggior parte dei campi statici
    //     this.haveDisabledFields = true;
    //     if (
    //       this.appraisalType === this.constants.appraisalTypes.FINE_LAVORI ||
    //       this.isShipping ||
    //       this.isAeremobile
    //     ) {
    //       //FLA e Beni Mobili: comportamento vecchio
    //       //Blocco campi non % in Costi
    //       //Gestiamo a FrontEnd la disabilitazione degli APF
    //       this.haveDisabledFieldsRenovationManifactured = true;
    //       this.forceApfDisabled = true;
    //     } else {
    //       //Nuova logica: il BackEnd si occupa degli APF disabilitati,
    //       //il FrontEnd non blocca alcuni campi in Costi
    //       this.haveDisabledFieldsRenovationManifactured = false;
    //       this.forceApfDisabled = false;
    //     }
    //   }
    // }
    // FINE COMPORTAMENTO VECCHIO DISABILITATO

    // COMPORTAMENTO VECCHIO MANTENUTO PER BENI MOBILI
    // NON DOVREBBE MAI ENTRARE IN QUESTO CICLO
    // IN QUANTO NON CI DOVREBBERO ESSERE BENI MOBILI SAL O FLA
    if (this.isShipping || this.isAeremobile) {
      //SAL Beni mobili: non posso aggiungere, copiare e cancellare asset
      if (this.appraisalType === this.constants.appraisalTypes.SAL) {
        this.canAddDeleteAsset = false;
      }

      if (
        this.appraisalInfo.appraisal.originProcess === 'MIG' ||
        this.appraisalInfo.migParent
      ) {
        //Pratiche Migrate o con genitore migrato:
        //tutti i campi sbloccati
        this.haveDisabledFields = false;
        this.forceApfDisabled = false;
        this.haveDisabledFieldsRenovationManifactured = false;
        return;
      }

      if (
        (this.appraisalType === this.constants.appraisalTypes.FINE_LAVORI ||
          this.appraisalType === this.constants.appraisalTypes.SAL) &&
        this.appraisalInfo['opinionType'] !== 'SO'
      ) {
        //FLA e SAL NON second opinion Beni Mobili
        //Blocchiamo la maggior parte dei campi statici
        this.haveDisabledFields = true;
        //Blocco campi non % in Costi
        //Gestiamo a FrontEnd la disabilitazione degli APF
        this.haveDisabledFieldsRenovationManifactured = true;
        this.forceApfDisabled = true;
      }
    }
  }

  saveIsEnabled(): boolean {
    return this.areAllAssetsValid() && !this.isEditable;
  }
}
