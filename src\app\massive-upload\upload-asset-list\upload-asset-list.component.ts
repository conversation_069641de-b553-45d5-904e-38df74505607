import { Component, OnInit, Inject, ViewChild  } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { UploadFileService } from '../service/upload-file.service';
import { DomainService } from '../../shared/domain/domain.service';
import { ModalDirective } from 'ngx-bootstrap/modal';
import { APP_CONSTANTS, IAppConstants } from '../../app.constants';
import { MessageService } from '../../shared/messages/services/message.service';
import { TranslateService } from '@ngx-translate/core';
import { DOCUMENT } from '@angular/platform-browser';

@Component({
  selector: 'app-upload-asset-list',
  templateUrl: './upload-asset-list.component.html',
  styleUrls: ['./upload-asset-list.component.css'],
  providers: [UploadFileService]
})
export class UploadAssetListComponent implements OnInit {

  @ViewChild(ModalDirective) modal: ModalDirective;
  loadId: string;
  assetList: any[];
  selectedAppraisal: any;
  listSize = 0;
  pageSize = 10;
  page = 1;
  modalSelected = '';
  showLogError: any = false;
  appTypeDomain: any = {};
  scopeTypeDomain: any = {};
  surveyTypeDomain: any = {};
  macroProcOomain: any = {};
  countryDomain: any = {};
  assetCategoryDomain: any = {};
  docCategoryDomain: any = {};
  selectedAsset: any;
  constructor(
    private translateService: TranslateService,
    private messageService: MessageService,
    private activatedRoute: ActivatedRoute,
    private uploadFileService: UploadFileService,
    private domainService: DomainService,
    @Inject(APP_CONSTANTS) public constants: IAppConstants,
    @Inject(DOCUMENT) private document: any
  ) { }

  ngOnInit() {
    this.activatedRoute.params.subscribe((params: Params) => {
      this.loadId = params['loadId'];
      this.retrieveAssetList();
    });
    // Observable.forkJoin(
    //   this.domainService.newGetDomain('UBZ_DOM_APPRAISAL_TYPE'),
    //   this.domainService.newGetDomain('UBZ_DOM_SCOPE_TYPE'),
    //   this.domainService.newGetDomain('UBZ_DOM_SURVEY_TYPE'),
    //   this.domainService.newGetDomain('UBZ_DOM_PROCESS_TYPE'),
    //   this.domainService.newGetDomain('UBZ_DOM_COUNTRY'),
    //   this.domainService.newGetDomain('UBZ_DOM_REG_CATEGORY_TYPE', '-', true),
    //   this.domainService.newGetDomain('UBZ_DOM_MALO_DOC_CATEGORY')
    // ).subscribe(res => {
    //   this.appTypeDomain = res[0];
    //   this.scopeTypeDomain = res[1];
    //   this.surveyTypeDomain = res[2];
    //   this.macroProcOomain = res[3];
    //   this.countryDomain = res[4];
    //   this.assetCategoryDomain = res[5];
    //   this.docCategoryDomain = res[6];
    // });
  }

  retrieveAssetList() {
    this.uploadFileService
      .getMaloEnergyHis(this.loadId, this.page, this.pageSize)
      .subscribe(res => {
        this.assetList = res.assets;
        this.listSize = res.nAssets;
      });
  }

  changePage(event: any) {
    this.page = event.page;
    this.retrieveAssetList();
  }

  changePageSize() {
    this.page = 1;
    this.retrieveAssetList();
  }

  handleLogModal(asset?: any) {
    this.selectedAsset = asset ? asset : null;
    this.showLogError = asset ? true : false;
  }

  goToAssetPage(idAsset: any) {
    this.document.location.href = `/UAM-EFA-PF/UAM/#/detail/false/${idAsset}`;
  }
}
