<div class="row step-navigation">
  <div class="col-sm-12 btn-set">
    <ng-container *ngIf="!(addExpert || filtersOpen)">
      <button *appAuthKey="'UBZ_REGISTRY.EXPERTS_OPEN_FILTER'" type="button" class="btn btn-empty waves-effect waves-secondary pull-right"
        (click)="openFilters()">
        <i class="icon-filter"></i> {{'UBZ.SITE_CONTENT.1100100011' | translate }}
      </button>
      <button *appAuthKey="'UBZ_REGISTRY.EXPERTS_ADD_NEW'" type="button" class="btn btn-empty waves-effect waves-secondary pull-right"
        (click)="goToNewExpert()">
        <i class="icon-add"></i> {{'UBZ.SITE_CONTENT.1011100100' | translate }}
      </button>
    </ng-container>
    <ng-container *ngIf="addExpert">
      <button *appAuthKey="'UBZ_REGISTRY.EXPERTS_ABORT'" type="button" class="btn btn-empty waves-effect waves-secondary pull-right"
        (click)="addExpert = false">
        <i class="fa fa-times" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.100000' | translate }}
      </button>
    </ng-container>
    <ng-container *ngIf="filtersOpen">
      <button *appAuthKey="'UBZ_REGISTRY.EXPERTS_CLOSE_FILTERS'" type="button" class="btn btn-empty waves-effect waves-secondary pull-right"
        (click)="closeFilters()">
        <i class="fa fa-times" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.1100100101' | translate }}
      </button>
    </ng-container>
  </div>
</div>

<section class="row" *ngIf="filtersOpen">
  <!-- Filtri -->
  <div class="col-md-2 col-sm-12 form-group">
    <label>{{'UBZ.SITE_CONTENT.1001110' | translate }}</label>
    <input type="text" name="ndg" class="form-control" [(ngModel)]="searchFilter.ndg">
  </div>
  <div class="col-md-2 col-sm-12 form-group">
    <label>{{'UBZ.SITE_CONTENT.101111110' | translate }}</label>
    <input type="text" name="firstName" class="form-control" [(ngModel)]="searchFilter.firstName">
  </div>
  <div class="col-md-2 col-sm-12 form-group">
    <label>{{'UBZ.SITE_CONTENT.101111111' | translate }}</label>
    <input type="text" name="lastName" class="form-control" [(ngModel)]="searchFilter.lastName">
  </div>
  <div class="col-md-4 col-md-offset-2 col-sm-12 form-group">
    <label></label>
    <div class="btn-set">
      <button *appAuthKey="'UBZ_REGISTRY.EXPERTS_FILTER'" class="btn btn-primary waves-effect pull-right" type="button" (click)="filter()">{{'UBZ.SITE_CONTENT.1100100110' | translate }}</button>
      <ng-container *appAuthKey="'UBZ_REGISTRY.EXPERTS_RESET_FILTER'">
        <button *ngIf="(searchFilter.ndg || searchFilter.firstName || searchFilter.lastName)" class="btn btn-secondary waves-effect pull-right"
          type="button" (click)="cleanSearchFilter()">{{'UBZ.SITE_CONTENT.1010010101' | translate }}</button>
      </ng-container>
    </div>
  </div>
</section>

<section class="row" *ngIf="!addExpert">
  <!-- Tabella -->
  <div class="col-sm-12">
    <table class="table table-hover" *ngIf="expertsNumber > 0; else noRecordFound">
      <thead>
        <tr>
          <th scope="col">{{'UBZ.SITE_CONTENT.1001110' | translate }}</th>
          <th scope="col">{{'UBZ.SITE_CONTENT.101111110' | translate }}</th>
          <th scope="col">{{'UBZ.SITE_CONTENT.101111111' | translate }}</th>
          <th scope="col">{{'UBZ.SITE_CONTENT.1100100111' | translate }}</th>
          <th scope="col">{{'UBZ.SITE_CONTENT.10000001' | translate }}</th>
          <th scope="col"></th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let expert of experts">
          <td data-label="NDG">{{expert.ndg}}</td>
          <td data-label="Nome">{{expert.firstName}}</td>
          <td data-label="Cognome">{{ expert.lastname }}</td>
          <td data-label="Sede">{{ expert.address }}</td>
          <td data-label="Stato">{{ expert.status }}</td>
          <td data-label>
            <a *appAuthKey="'UBZ_REGISTRY.EXPERTS_GO_TO_DETAIL'" role="button" (click)="goToExpertDetail(expert.idAnag)">
              <i class="icon-angle-double-right"></i>
            </a>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <ng-template #noRecordFound>
    <div class="Search__NoResults">
      <div class="Search__NoResults__Icon">
        <i class="icon-placeholder_note"></i>
      </div>
      <div class="Search__NoResults__Text">
        <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1001011001' | translate }}</h3>
        <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011010' | translate }}</p>
      </div>
    </div>
  </ng-template>
  <ng-container *ngIf="expertsNumber > 10">
    <div class="col-sm-6">
      <div class="results">
        <span>{{'UBZ.SITE_CONTENT.1111111' | translate }}</span>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="pageSize" (ngModelChange)="pageSizeChanged()">
            <option value="{{10}}" selected>10 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{expertsNumber}}</option>
            <option value="{{20}}" *ngIf="expertsNumber > 20">20 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{expertsNumber}}</option>
            <option value="{{30}}" *ngIf="expertsNumber > 30">30 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{expertsNumber}}</option>
            <option value="{{40}}" *ngIf="expertsNumber > 40">40 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{expertsNumber}}</option>
            <option value="{{50}}" *ngIf="expertsNumber > 50">50 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{expertsNumber}}</option>
            <option value="{{expertsNumber}}">{{expertsNumber}} {{'UBZ.SITE_CONTENT.10000000' | translate }} {{expertsNumber}}</option>
          </select>
        </div>
      </div>
    </div>
    <div class="col-sm-6" class="pull-right">
      <pagination [boundaryLinks]="true" [directionLinks]="false" [totalItems]="expertsNumber" [itemsPerPage]="pageSize" [maxSize]="10"
        (pageChanged)="changePage($event)" class="pagination" previousText="&lsaquo;" nextText="&rsaquo;" firstText="&laquo;"
        lastText="&raquo;"></pagination>
    </div>
  </ng-container>
</section>

<section *ngIf="addExpert">
  <!-- Aggiunta nuova società peritale  -->
  <form #f="ngForm" (ngSubmit)="saveNewExpert()">
    <div class="row">
      <div class="col-md-12">
        <h3>{{'UBZ.SITE_CONTENT.1100000011' | translate }}</h3>
      </div>
    </div>
    <div class="row">
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.101111110' | translate }}*</label>
        <input type="text" name="heading" [(ngModel)]="newExpert.firstName" required class="form-control">
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.101111111' | translate }}*</label>
        <input type="text" name="heading" [(ngModel)]="newExpert.lastname" required class="form-control">
      </div>
      <ng-container *ngIf="chosenSection === ACTIVABLE_SECTIONS.BEN">
        <div class="col-md-3 col-sm-12 form-group">
          <label>{{'UBZ.SITE_CONTENT.1011100011' | translate }}*</label>
          <input type="text" name="userId" [(ngModel)]="newExpert.userId" required class="form-control">
        </div>
      </ng-container>
      <ng-container *ngIf="chosenSection === ACTIVABLE_SECTIONS.INT">
        <div class="col-md-3 col-sm-12 form-group">
          <label>{{'UBZ.SITE_CONTENT.1011100011' | translate }}</label>
          <input type="text" name="userId" [(ngModel)]="newExpert.userId" class="form-control">
        </div>
      </ng-container>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.101000' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" name="province" (change)="calculateCities($event)" [(ngModel)]="newExpert.province">
            <option value="" hidden>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let prov of (provinces | domainMapToDomainArray)" value="{{ prov.domCode }}">{{ prov.translationCod | translate }}</option>
          </select>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.101001' | translate }}*</label>
        <div class="custom-select">
          <select class="form-control" [(ngModel)]="newExpert.city" name="city">
            <option value="" selected>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
            <option *ngFor="let city of cities" value="{{ city.domCode }}">{{ city.translationCod | translate }}</option>
          </select>
        </div>
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.101010' | translate }}*</label>
        <input type="text" name="heading" [(ngModel)]="newExpert.postalCode" required class="form-control">
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.101011' | translate }}*</label>
        <input type="text" name="heading" [(ngModel)]="newExpert.address" required class="form-control">
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.101100' | translate }}*</label>
        <input type="text" name="num" [(ngModel)]="newExpert.num" required class="form-control" appOnlyNumbers>
      </div>
    </div>
    <div class="row">
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.11110000' | translate }}*</label>
        <input type="text" name="phoneNum" [(ngModel)]="newExpert.phoneNum" required class="form-control" appOnlyNumber maxlength="15">
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1100000111' | translate }}*</label>

        <input type="text" name="phoneNum" [(ngModel)]="newExpert.mobileNumber" required class="form-control" appOnlyNumber maxlength="15">
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1100000110' | translate }}*</label>
        <input type="text" name="email" [(ngModel)]="newExpert.email" required class="form-control">
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label>{{'UBZ.SITE_CONTENT.1001110' | translate }}*</label>
        <input type="text" name="ndg" [(ngModel)]="newExpert.ndg" required class="form-control" appOnlyNumbers>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <h3>{{'UBZ.SITE_CONTENT.1100001001' | translate }}</h3>
      </div>
    </div>
    <div class="row">
      <div class="col-md-3 col-sm-12 form-group">
        <label for="startAbilitation">{{'UBZ.SITE_CONTENT.1100001010' | translate }}*</label>
        <app-calendario [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate" [name]="'startAbilitation'" [title]="'UBZ.SITE_CONTENT.1010100001' | translate"
          [(ngModel)]="newExpert.startAbilitation" [required]="true">
        </app-calendario>
      </div>
      <div class="col-md-3 col-sm-12 form-group">
        <label for="endAbilitation">{{'UBZ.SITE_CONTENT.1011111011' | translate }}*</label>
        <app-calendario [name]="'endAbilitation'" [title]="'UBZ.SITE_CONTENT.1010100001' | translate" [(ngModel)]="newExpert.endAbilitation"
          [required]="true" [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate">
        </app-calendario>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <h3>{{'UBZ.SITE_CONTENT.11000001' | translate }}</h3>
        <div class="input-group">
          <input type="text" class="form-control" readonly [value]="fileName" required>
          <label class="input-group-btn">
            <span class="btn btn-primary waves-effect">
              {{'UBZ.SITE_CONTENT.11000011' | translate }}&hellip;
              <input type="file" #fileToUpload id="sfoglia" style="display: none;" multiple (change)="setFile()" required>
            </span>
          </label>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12 btn-set">
        <button *appAuthKey="'UBZ_REGISTRY.EXPERTS_SAVE_NEW'" class="btn btn-primary waves-effect pull-right" [disabled]="f.invalid || !contractChosen"
          type="submit">{{'UBZ.SITE_CONTENT.1100101001' | translate }}</button>
      </div>
    </div>
  </form>
</section>
