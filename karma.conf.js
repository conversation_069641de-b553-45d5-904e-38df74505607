module.exports = function(config) {
  config.set({
    basePath: '',
    frameworks: ['jasmine', '@angular/cli'],
    plugins: [
      require('karma-jasmine'),
      require('karma-phantomjs-launcher'),
      require('karma-coverage-istanbul-reporter'),
      require('@angular/cli/plugins/karma')
    ],
    angularCli: {
      environment: 'dev',
      codeCoverage: true
    },
    reporters: ['coverage-istanbul'],
    specReporter: {
      maxLogLines: 5,
      suppressErrorSummary: false,
      suppressFailed: false,
      suppressPassed: false,
      suppressSkipped: false,
      showSpecTiming: true,
      failFast: false
    },
    coverageReporter: {
      include: [
        'src/**/*.ts',
        '!src/typings.d.ts',
        '!src/app/**/*.spec.ts',
        '!src/environments/environment*.ts'
      ]
    },
    coverageIstanbulReporter: {
      reports: ['html', 'lcovonly'],
      dir: process.env['coverageFolder'] || 'coverage',
      combineBrowserReports: true,
      fixWebpackSourcePaths: true,
      skipFilesWithNoCoverage: false,
      'report-config': {
        html: {
          subdir: 'html'
        }
      },
      verbose: true
    },
    port: 9876,
    colors: true,
    logLevel: config.LOG_DEBUG,
    autoWatch: true,
    browsers: ['PhantomJS'],
    singleRun: true,
    browserNoActivityTimeout: 600000,
    failOnEmptyTestSuite: false,
    captureTimeout: 60000,
    browserDisconnectTimeout: 60000,
    browserDisconnectTolerance: 3
  });
};

