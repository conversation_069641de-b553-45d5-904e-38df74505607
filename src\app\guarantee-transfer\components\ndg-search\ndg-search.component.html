<!-- FIXME - CENTRALIZZARE LABELS -->
<div class="col-sm-12">
  <h1><i class="icon-switch"></i> {{'UBZ.SITE_CONTENT.1111111111' | translate }}</h1>
  <h2>{{'UBZ.SITE_CONTENT.1001011' | translate }}</h2>
</div>
<div class="row">
  <form name="start" #form="ngForm">
    <div class="col-sm-12">
      <div class="col-md-3 form-group">
        <div class="custom-checkbox">
          <input id="differentNdg" type="checkbox" [(ngModel)]="differentNdg" name="differentNdg" class="checkbox" (click)="toggleDifferentNdg()">
          <label for="differentNdg">NDG DIFFERENTI</label>
        </div>
      </div>
    </div>
    <div class="col-sm-12">
      <div class="col-md-3 form-group">
        <!-- <label>{{'UBZ.SITE_CONTENT.1001110' | translate }}</label> -->
        <label>NDG DI PARTENZA</label>
        <input type="text" class="form-control numeric" placeholder="{{'UBZ.SITE_CONTENT.110001101' | translate }}" name="ndgOrigin" [(ngModel)]="ndgOrigin"
          appOnlyNumbers maxLength="16" required />
      </div>
    </div>
    <div class="col-sm-12" *ngIf="differentNdg">
      <div class="col-md-3 form-group">
        <!-- <label>{{'UBZ.SITE_CONTENT.1001110' | translate }}</label> -->
        <label>NDG DI ARRIVO</label>
        <input type="text" class="form-control numeric" placeholder="{{'UBZ.SITE_CONTENT.110001101' | translate }}" name="ndgDestination" [(ngModel)]="ndgDestination"
          appOnlyNumbers maxLength="16" required />
      </div>
    </div>
    <div class="col-sm-12">
      <div class="col-sm-2 col-md-2">
        <button type="submit" class="btn btn-primary" [disabled]="ndgOrigin === ''" (click) = "startGuaranteeTransfer()" [disabled]="form.invalid">{{'UBZ.SITE_CONTENT.10000001001' | translate }}</button>
      </div>
    </div>
  </form>
</div>
