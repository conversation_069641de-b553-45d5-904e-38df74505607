<div class="row">
  <table *ngIf="guaranteeList && guaranteeList.length > 0" class="uc-table">
    <thead>
      <tr>
        <th></th>
        <th>{{'UBZ.SITE_CONTENT.10011101' | translate }}</th>
        <th>{{'UBZ.SITE_CONTENT.10010101' | translate }}</th>
        <th colspan="2">{{'UBZ.SITE_CONTENT.10010110' | translate }}</th>
        <th>{{'UBZ.SITE_CONTENT.10000001100' | translate }}</th>
        <th>{{'UBZ.SITE_CONTENT.1111110111' | translate }}</th>
        <th>{{'UBZ.SITE_CONTENT.1111111000' | translate }}</th>
        <th>{{'UBZ.SITE_CONTENT.10011111' | translate }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let row of guaranteeList; let i = index">
        <td>
          <div class="custom-radio">
            <input type="radio" name="checkbox" id="{{i}}" class="radio" value="{{row.jointCod}}" [(ngModel)]="guaranteeIdChecked">
            <label for="{{i}}"></label>
          </div>
        </td>
        <td>{{row.jointCod}}</td>
        <td>{{row.collatTecForm}}</td>
        <td colspan="2">{{row.description}}</td>
        <td style="text-align: right">{{row.collatAmount | currency:'EUR':true:'1.2-2' }}</td>
        <td>{{row.percType}}</td>
        <td>{{row.poolPerc}}</td>
        <td>{{row.propAssociate}}</td>
      </tr>
    </tbody>
  </table>
  <div class="Search__NoResults" *ngIf="!guaranteeList || guaranteeList.length === 0">
    <div class="Search__NoResults__Icon">
      <i class="icon-search"></i>
    </div>
    <div class="Search__NoResults__Text">
      <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.10000001101' | translate }}</h3>
    </div>
  </div>
</div>

<!-- CENTRALIZZARE FOOTER NEL COMPONENTE PADRE CONTENITORE -->
<app-navigation-footer 
  (cancelButtonClick)="guaranteeTransferService.cancelRequest()"  
  [showPrevious]="true" 
  [saveIsEnable]="guaranteeIdChecked" 
  (saveButtonClick)="save()" 
  (previousButtonClick)="openModal()">
</app-navigation-footer>

<div *ngIf="previousModalIsOpen" class="modal fade" id="nuovo-asset" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" bsModal #modal="bs-modal" (onHidden)="closeModal()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{'UBZ.SITE_CONTENT.1100010' | translate }}</h2>
        <button type="button" role="button" class="close pull-right" aria-label="Close" (click)="closeModal()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <p>{{'UBZ.SITE_CONTENT.10000000100' | translate }}.</p>
        <p>{{'UBZ.SITE_CONTENT.1111110110' | translate }}?</p>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" (click)="confirmUndo()">
          {{'UBZ.SITE_CONTENT.110101000' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>
