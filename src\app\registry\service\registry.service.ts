import { Injectable } from '@angular/core';
import { Http, Response, ResponseContentType } from '@angular/http';
import { Observable } from 'rxjs/Observable';
import { IFilter } from '../model/registry.filters.models';
import {
  JudicialRecord,
  CompetencePronvince,
  Certificate
} from '../model/registry.models';

@Injectable()
export class RegistryService {
  private readonly baseUrl = '/UBZ-ESA-RS/service/expert/v1';
  private readonly expSocPerBaseUrl = `${this.baseUrl}/expPerSocieties`;
  private readonly expertBaseUrl = `${this.baseUrl}/experts`;

  public lastExpertFirm: string;
  public fromExpertFirm: boolean;

  constructor(private _http: Http) {}

  public getExpertFirmList(page: number, pageSize: number): Observable<any> {
    const url = `${this.baseUrl}/expPerSocieties/${page}/${pageSize}`;
    return this._http.get(url).map(res => res.json());
  }

  public addNewExpertFirm(toSave: any, file: any): Observable<any> {
    const url = `${this.baseUrl}/expPerSocieties/createNewFirm`;
    const form: FormData = new FormData();
    form.append('infos', JSON.stringify(toSave));
    form.append('contract', file, file.name);
    return this._http.post(url, form).map(res => res.json());
  }

  public getExpertFirmDetail(
    idAnag: string,
    mock: boolean,
    subjectType: string
  ): Observable<any> {
    const urlMap = {
      SOC: `${this.baseUrl}/expPerSocieties/${idAnag}`,
      PBN: `${this.expertBaseUrl}/${idAnag}`,
      PER: `${this.expertBaseUrl}/${idAnag}`
    };
    return this._http.get(urlMap[subjectType]).map(res => res.json());
  }

  // INIZIO SEZIONE SOCIETÀ PERITALI
  public getGenericData(idAnag: string, subjectType: string): Observable<any> {
    const urlMap = {
      SOC: `${this.expSocPerBaseUrl}/generic-data/${idAnag}`,
      PBN: `${this.expertBaseUrl}/generic-data/${idAnag}`,
      PER: `${this.expertBaseUrl}/generic-data/${idAnag}`
    };
    return this._http.get(urlMap[subjectType]).map(res => res.json());
  }
  public saveGenericData(
    idAnag: string,
    toSave: any,
    subjectType: string
  ): Observable<any> {
    const urlMap = {
      SOC: `${this.expSocPerBaseUrl}/generic-data/${idAnag}`,
      PER: `${this.expertBaseUrl}/generic-data/${idAnag}`,
      PBN: `${this.expertBaseUrl}/generic-data/${idAnag}`
    };
    return this._http.put(urlMap[subjectType], toSave).map(res => res.json());
  }
  public getAbilitationData(
    idAnag: string,
    subjectType: string
  ): Observable<any> {
    const urlMap = {
      SOC: `${this.expSocPerBaseUrl}/abilitation/${idAnag}`,
      PER: `${this.expertBaseUrl}/abilitation/${idAnag}`,
      PBN: `${this.expertBaseUrl}/abilitation/${idAnag}`
    };
    return this._http.get(urlMap[subjectType]).map(res => res.json());
  }
  public saveAbilitationData(
    idAnag: string,
    toSave: any,
    subjectType: string
  ): Observable<any> {
    const urlMap = {
      SOC: `${this.expSocPerBaseUrl}/abilitation/${idAnag}`,
      PER: `${this.expertBaseUrl}/abilitation/${idAnag}`,
      PBN: `${this.expertBaseUrl}/abilitation/${idAnag}`
    };
    return this._http.put(urlMap[subjectType], toSave).map(res => res.json());
  }
  public getRegisterData(idAnag: string, subjectType: string): Observable<any> {
    const urlMap = {
      SOC: `${this.expSocPerBaseUrl}/register/${idAnag}`,
      PER: `${this.expertBaseUrl}/register/${idAnag}`,
      PBN: `${this.expertBaseUrl}/register/${idAnag}`
    };
    return this._http.get(urlMap[subjectType]).map(res => res.json());
  }
  public saveRegisterData(
    idAnag: string,
    toSave: any,
    subjectType: string
  ): Observable<any> {
    const urlMap = {
      SOC: `${this.expSocPerBaseUrl}/register/${idAnag}`,
      PER: `${this.expertBaseUrl}/register/${idAnag}`,
      PBN: `${this.expertBaseUrl}/register/${idAnag}`
    };
    return this._http.put(urlMap[subjectType], toSave).map(res => res.json());
  }
  public getExpSocAssociatedExperts(idAnag: string): Observable<any> {
    const url = `${this.expSocPerBaseUrl}/associate-expert/${idAnag}`;
    return this._http.get(url).map(res => res.json());
  }
  public saveExpSocAssociatedExperts(
    toSave: any,
    idAnag: string
  ): Observable<any> {
    const url = `${this.expSocPerBaseUrl}/associate-expert/${idAnag}`;
    return this._http.put(url, toSave).map(res => res.json());
  }
  public getInsurancePolicyList(
    idAnag: string,
    subjectType: string
  ): Observable<any> {
    const urlMap = {
      SOC: `${this.expSocPerBaseUrl}/insurance/${idAnag}`,
      PER: `${this.expertBaseUrl}/insurance/${idAnag}`,
      PNB: `${this.expertBaseUrl}/insurance/${idAnag}`
    };
    return this._http.get(urlMap[subjectType]).map(res => res.json());
  }
  public saveInsurancePolicyList(
    idAnag: string,
    toSave: any,
    subjectType: string
  ): Observable<any> {
    const urlMap = {
      SOC: `${this.expSocPerBaseUrl}/insurance/${idAnag}`,
      PER: `${this.expertBaseUrl}/insurance/${idAnag}`,
      PNB: `${this.expertBaseUrl}/insurance/${idAnag}`
    };
    return this._http.put(urlMap[subjectType], toSave).map(res => res.json());
  }
  // FINE SEZIONE SOCIETÀ PERITALI

  public getExperts(
    internalExperts: boolean,
    page: number,
    numberOfResults: number
  ): Observable<any> {
    const url = `${this
      .expertBaseUrl}/${internalExperts}/${page}/${numberOfResults}`;
    return this._http.get(url).map(res => res.json());
  }

  public saveNewExpert(toSave: any, contractFile: any) {
    const url = `${this.expertBaseUrl}/createNewExpert`;
    const form: FormData = new FormData();
    form.append('infos', JSON.stringify(toSave));
    form.append('contractFile', contractFile, contractFile.name);
    return this._http.post(url, form).map(res => res.json());
  }

  public filter(subjectType: string, filter: IFilter): Observable<any> {
    const urlMap = {
      PBN: `${this.expertBaseUrl}/filter`,
      PER: `${this.expertBaseUrl}/filter`,
      SOC: `${this.expSocPerBaseUrl}/filter`
    };
    return this._http.post(urlMap[subjectType], filter).map(res => res.json());
  }

  public getIdentityDocument(anagId: string): Observable<any> {
    const url = `${this.expertBaseUrl}/identity-card/${anagId}`;
    return this._http.get(url).map(res => res.json());
  }

  public saveIdentityDocument(anagId: string, toSave: any): Observable<any> {
    const url = `${this.expertBaseUrl}/identity-card/${anagId}`;
    return this._http.put(url, toSave).map(res => res.json());
  }

  public getJudicialRecord(anagId: string): Observable<any> {
    const url = `${this.expertBaseUrl}/judicial-record/${anagId}`;
    return this._http.get(url).map(res => res.json());
  }

  public saveJudicialRecord(
    anagId: string,
    toSave: JudicialRecord
  ): Observable<any> {
    const url = `${this.expertBaseUrl}/judicial-record/${anagId}`;
    return this._http.put(url, toSave).map(res => res.json());
  }

  public getCompetenceProvinces(
    anagId: string
  ): Observable<CompetencePronvince[]> {
    const url = `${this.expertBaseUrl}/competence-provinces/${anagId}`;
    return this._http.get(url).map(res => res.json());
  }

  public saveCompetenceProvinces(
    anagId: string,
    toSave: CompetencePronvince[]
  ): Observable<any> {
    const url = `${this.expertBaseUrl}/competence-provinces/${anagId}`;
    return this._http.put(url, toSave).map(res => res.json());
  }

  public getCertifications(anagId: string): Observable<Certificate[]> {
    const url = `${this.expertBaseUrl}/certification/${anagId}`;
    return this._http.get(url).map(res => res.json());
  }

  public saveCertifications(
    anagId: string,
    toSave: Certificate[]
  ): Observable<any> {
    const url = `${this.expertBaseUrl}/certification/${anagId}`;
    return this._http.put(url, toSave).map(res => res.json());
  }

  public getDriverAssignment(idAnag): Observable<any> {
    const url = `${this.expertBaseUrl}/admitted-appraisal/${idAnag}`;
    return this._http.get(url).map(res => res.json());
  }

  public saveDriverAssignment(idAnag: string, toSave: any[]): Observable<any> {
    const url = `${this.expertBaseUrl}/admitted-appraisal/${idAnag}`;
    return this._http.put(url, toSave).map(res => res.json());
  }

  public saveExpertAction(idAnag: string, toSave: any): Observable<any> {
    const url = `${this.expertBaseUrl}/action/${idAnag}`;
    return this._http.post(url, toSave).map(res => res.json());
  }

  public getExpertAppraisals(
    anagId: string,
    appraisalType: string
  ): Observable<any> {
    const urlMap = {
      REVOCATED: `/UBZ-ESA-RS/service/appraisal/v1/appraisals/revocated/${anagId}`,
      COMPLETED: `/UBZ-ESA-RS/service/appraisal/v1/appraisals/in-charge/${anagId}/true`,
      IN_CHARGE: `/UBZ-ESA-RS/service/appraisal/v1/appraisals/in-charge/${anagId}/false`
    };

    return this._http.get(urlMap[appraisalType]).map(res => res.json());
  }

  public getLandingPage(): Observable<string> {
    const url = `${this.expertBaseUrl}/landing`;
    return this._http.get(url).map(res => res.text());
  }

  public downloadSupplyContract(downloadRequest: any): Observable<any> {
    if (downloadRequest['contractId'] === null) {
      return Observable.empty();
    }
    const url = `${this.expertBaseUrl}/download`;
    return this._http
      .put(url, downloadRequest, { responseType: ResponseContentType.Blob })
      .map((res: Response) => res.blob());
  }

  public getRegionDomainForRegistry(): Observable<any> {
    const url = '/UBZ-ESA-RS/service/conMap/v1/map/compRegion';
    return this._http.get(url).map(res => res.json());
  }
}
