import {
  Component,
  OnInit,
  Input,
  ViewChild
} from '@angular/core';
import { NgForm } from '@angular/forms';
import { Observable } from 'rxjs/Observable';

// Services
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { DomainService } from '../../../../../shared/domain/domain.service';

// Models
import { ComparableElementModel } from '../../model/comparable-element.model';
import createNumberMask from 'text-mask-addons/dist/createNumberMask';

@Component({
  selector: 'app-comparables',
  templateUrl: './comparables.component.html',
  styleUrls: ['./comparables.component.css']
})
export class ComparablesComponent implements OnInit {
  @ViewChild('f') form: NgForm;
  @Input("comparablesList") comparablesList: ComparableElementModel[];
  domainsStringArray: string[] = new Array(); // array che contiene una singola occorrenza per ogni dominio che bisogna recuperare
  domainsObject: Object = {};
  // averageCommercialDiscount: number[] = [0, 0, 0, 0];  // array contenente i valori per i campi statici "Sconto Medio Trattativa"
  //usedMarketValue: number;  // variabile contenente i valori per il campo statico "Valore di mercato utilizzato"
  isOutOfRange: boolean;
  numericMaskConfig = createNumberMask({
    prefix: "",
    thousandsSeparatorSymbol: ".",
    allowDecimal: true,
    decimalSymbol: ","
  });


  constructor(public accordionAPFService: AccordionAPFService, private domainService: DomainService) {}

  // L'evento change del form viene intercettato dal componente padre EvaluationsComponent e
  // viene scatenato il metodo del padre checkSaveEnable() per validare lo step del wizard
  ngOnInit() {
    this.getAllDomainsString();
  }

  public isValid(): boolean {
    return this.form && this.form.valid;
  }

  // Scorre l'oggetto comparableList esaminando ogni singolo comparableField
  // Se type = combo, inserisce il relativo domainCod all'interno dell'array domainsStringArray per
  // effettuare successivamente tutte le chiamate di recupero domini
  getAllDomainsString() {
    this.comparablesList.forEach(comparableElement => {
      comparableElement.comparableField.forEach(comparableField => {
        if (comparableField.type === 'Combo') {
          if (this.domainsStringArray.indexOf(comparableField.domainCod) === -1) {
            this.domainsStringArray.push(comparableField.domainCod);
          }
        }
      });
    });
    this.getAllDomainsData();
  }

  // Effettua la subscribe sull'arrray di observable contenenti tutti i domini necessari in pagina
  // e provvede alla valorizzazione del relativo oggetto per contenerli
  getAllDomainsData() {
    this.sendAllDomainRequest().subscribe(resultArray => {
      resultArray.forEach((res, index) => {
        this.domainsObject[this.domainsStringArray[index]] = res;
      });
    });
  }

  // Per ogni dominio presente in domainsStringArray effettua la chiamata al servizio di recupero valori
  // e inserisce l'observable di risposta in observablesDomain per effettuare una forkJoin
  sendAllDomainRequest(): Observable<any> {
    let observablesDomain: Observable<any>[] = new Array;
    this.domainsStringArray.forEach(domainString => {
      observablesDomain.push(this.domainService.newGetDomain(domainString));
    });
    return Observable.forkJoin(observablesDomain);
  }

  // Calcola il valore da inserire nelle celle di "valore unitario"
  unitValue(columnIndex: number): number {
    if (+this.comparablesList[5].comparableField[columnIndex].fieldValue) {
      return (+this.comparablesList[2].comparableField[columnIndex].fieldValue / +this.comparablesList[5].comparableField[columnIndex].fieldValue);
    } else {
      return 0;
    }
  }

  // Calcola il valore da inserire nelle celle di "Totale % ponderazione"
  ponderingTotalPerc(columnIndex: number): number {
    let startIndex = 4;
    let sum = 0;
    // L'ultima riga dell'array contiene il valore "Valore di mercato utilizzato €/MQ" e non deve essere conteggiata
    // La penultima riga viene sommata al termine del ciclo
    while (startIndex < this.comparablesList.length - 2) {
      sum += +this.comparablesList[startIndex].comparableField[columnIndex].percentage;
      startIndex++;
    }
    // return sum;
    return (+this.comparablesList[startIndex].comparableField[columnIndex].fieldValue + sum);
  }

  // Calcola il valore da inserire nelle celle di "Valore di mercato ponderato"
  ponderingMarketValue(columnIndex: number) {
    return this.unitValue(columnIndex) * (100 + this.ponderingTotalPerc(columnIndex)) / 100;
  }

  // Calcola il valore da inserire nelle celle di "Valore di mercato medio"
  averageMarketValue() {
    //Solo i primi due valori sono obbligatori, se gli altri non sono maggiori di 0 non divido
    let items = 2;
    if (this.ponderingMarketValue(2) > 0) items++;
    if (this.ponderingMarketValue(3) > 0) items++;
    return (this.ponderingMarketValue(0) + this.ponderingMarketValue(1) + this.ponderingMarketValue(2) + this.ponderingMarketValue(3)) / items;
  }

  // Invocato dal componente padre, restituisce i valori del componente da salvare
  getData(): ComparableElementModel[] {
    return this.comparablesList;
  }
}
