<div [formGroup]="singleSociety" class="panel panel-default">
  <div class="panel-heading text-center">
    <h3 class="panel-title">{{singleSociety.value.heading | uppercase}}</h3>
  </div>
  <div class="form-group">
    <label>{{ 'UBZ.SITE_CONTENT.11111000010' | translate }}</label>
    <input type="text" class="form-control" name="distributionPercentage" formControlName="distributionPercentage"
      appForcePattern regexPattern="^0$|^[1-9][0-9]?$|^100$" required>
  </div>
  <div class="form-group">
    <label>{{ 'UBZ.SITE_CONTENT.11111000011' | translate }}</label>
    <div class="custom-select">
      <select class="form-control" formControlName="priority" required>
        <option *ngFor="let priority of prioritiesList" [ngValue]="priority">{{priority}}</option>
      </select>
    </div>
  </div>
  <button type="button" class="btn btn-empty deleteSociety" [disabled]="prioritiesList?.length <= 1"
    (click)="deleteModal.show()">
    <i class="fa fa-trash-o"></i>
  </button>
</div>

<!-- Modal for confirmation of delete action  -->
<div class="modal fade" bsModal #deleteModal="bs-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h2>{{ 'UBZ.SITE_CONTENT.11111011001' | translate }}</h2>
        <button type="button" class="close pull-right" aria-label="Close" (click)="deleteModal.hide()">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="modal-body">
        <p> {{ 'UBZ.SITE_CONTENT.11111010111' | translate }} {{ societyHeading }}
          {{ 'UBZ.SITE_CONTENT.11111011000' | translate }}</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary waves-effect"
          (click)="submitDeleteModal(singleSociety)">{{'UBZ.SITE_CONTENT.1100010' | translate }}</button>
      </div>
    </div>
  </div>
</div>