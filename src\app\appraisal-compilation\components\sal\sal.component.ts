import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, Inject } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { Observable } from 'rxjs/Observable';

// Componenti
import { CostPrevisionTabComponent } from './cost-prevision-tab/cost-prevision-tab.component';
import { EndJobEvaluationComponent } from './end-job-evaluation/end-job-evaluation.component';
import { ShippingCostTableComponent } from './shipping-cost-table/shipping-cost-table.component';
import { AppraisalTemplateValidationComponent } from '../appraisal-template-validation/appraisal-template-validation.component';

// Servizi
import { LandingService } from '../../../simulation/services/landing/landing.service';
import { SALService } from './service/sal.service';
import { PositionService } from '../../../shared/position/position.service';
import { AppraisalCompilationService } from '../../service/appraisal-compilation.service';
import { MessageService } from '../../../shared/messages/services/message.service';

// Modelli
import { APP_CONSTANTS, IAppConstants } from '../../../app.constants';
import {
  EndJobEvaluationModel,
  SALComplessivoModel,
  CostPrevisionTableModel
} from './model/sal.models';

@Component({
  selector: 'app-sal',
  templateUrl: './sal.component.html',
  styleUrls: ['./sal.component.css'],
  providers: [SALService, AppraisalCompilationService]
})
export class SALComponent implements OnInit, OnDestroy {
  positionId: string;
  wizardCode: string;
  bpmTaskId: string;
  bpmTaskCod: string;
  currentTask = 'UBZ-PER-SAL';
  @ViewChild(CostPrevisionTabComponent)
  costPrevisionTab: CostPrevisionTabComponent;
  @ViewChild(EndJobEvaluationComponent)
  endJobEvaluationTab: EndJobEvaluationComponent;
  @ViewChild(ShippingCostTableComponent)
  shippingCostTable: ShippingCostTableComponent;
  @ViewChild(AppraisalTemplateValidationComponent)
  templateValidationComponent: AppraisalTemplateValidationComponent;
  endJobEvaluationList: EndJobEvaluationModel[] = [];
  salComplessivo: SALComplessivoModel = new SALComplessivoModel();
  costiDiRealizzazione: CostPrevisionTableModel[] = [];
  presCompMetrica: string;
  presProgEsecutiva: string;
  appraisalType: string; // IND = template industriale; EAG = edilizia agevolata; MUT = mutui agrari
  mobileAppraisalType: string; // SHI = shipping
  saveDraftCallback = this.saveDraft.bind(this);
  private _subscription;

  constructor(
    @Inject(APP_CONSTANTS) public constants: IAppConstants,
    private _activatedRoute: ActivatedRoute,
    public _landingService: LandingService,
    private _salService: SALService,
    public _positionService: PositionService,
    private _appraisalCompilationService: AppraisalCompilationService,
    private msgService: MessageService
  ) {}

  ngOnInit() {
    this._subscription = this._activatedRoute.params
      .switchMap((params: Params) => {
        this.positionId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        this.bpmTaskId = params['taskId'];
        this.bpmTaskCod = params['taskCod'];
        this._landingService.checkIfCompletedTask(
          this.positionId,
          this.currentTask
        );
        return this._positionService.getAppraisalInfo(this.positionId);
      })
      .switchMap(res => {
        this.setAppraisalInfo(res);
        switch (this.mobileAppraisalType) {
          // Il servizio da chiamare nel caso di aereomobili o shipping sono uguali
          case this.constants.appraisalTypes.SHIPPING:
          case this.constants.appraisalTypes.AEREOMOBILE:
            return this._salService.getSalData(this.positionId, 'SHI');
          default:
            return this._salService.getSalData(this.positionId, 'ALL');
        }
      })
      .subscribe(res => {
        if (res.toShowMessage) {
          this.msgService.showWarning(
            `Non tutti gli asset nel wizard 'Unità a Garanzia' hanno il flag SAL valorizzato nell'accordion 'Cronoprogramma'`,
            'SAL non flaggati'
          );
        }
        this.setSalInfo(res);
      });
  }

  /**
   * @name setAppraisalInfo
   * @description Metodo invocato sul recupero dell'oggetto perizia, imposta le variabili ad esso connesse
   * @param appraisalInfo Oggetto perizia recuperto dal wizard container
   */
  setAppraisalInfo(appraisalInfo) {
    this.appraisalType = appraisalInfo.appraisal.loanScope;
    if (appraisalInfo.appraisal.resItemType === 'MOB') {
      if (
        this._appraisalCompilationService.getMobileType(
          appraisalInfo.appraisalObject
        ) === this.constants.appraisalTypes.AEREOMOBILE
      ) {
        this.mobileAppraisalType = 'AER';
      } else {
        this.mobileAppraisalType = 'SHI';
      }
    }
  }

  ngOnDestroy() {
    this._subscription.unsubscribe();
  }

  /**
   * @name setSalInfo
   * @description Metodo invocato sul recupero dell'oggetto SAL, imposta le variabili ad esso connesse
   * @param res Oggetto SAL recuperato
   */
  setSalInfo(res: any): void {
    if (res.partialSaveData) {
      res = JSON.parse(res.partialSaveData);
    }
    if (
      // FIXME - è giusto che il controllo sui beni industriali sia stato tolto?
      // this.appraisalType === this.constants.appraisalTypes.BENI_INDUSTRIALI ||
      this.mobileAppraisalType === this.constants.appraisalTypes.SHIPPING ||
      this.mobileAppraisalType === this.constants.appraisalTypes.AEREOMOBILE
    ) {
      this.salComplessivo = res;
      this.presCompMetrica = res.metricCalculationFlag;
      this.presProgEsecutiva = res.execProjectFlag;
    } else {
      this.endJobEvaluationList = res.tabStimaFineLavori;
      this.salComplessivo = res.datiSalComplessivo;
      this.presCompMetrica = res.presCompMetrica;
      this.presProgEsecutiva = res.presProgEsecutiva;
      this.costiDiRealizzazione = res.tabAppTotCostEstimate;
      setTimeout(() => this.costPrevisionTab.totalsInit(), 30);
    }
    this.parseDate();
  }

  private parseDate(): void {
    if (this.salComplessivo.planningEndDate) {
      this.salComplessivo.planningEndDate = new Date(
        this.salComplessivo.planningEndDate
      );
    }
    if (this.salComplessivo.planningStartDate) {
      this.salComplessivo.planningStartDate = new Date(
        this.salComplessivo.planningStartDate
      );
    }
    if (this.salComplessivo.surveyDate) {
      this.salComplessivo.surveyDate = new Date(this.salComplessivo.surveyDate);
    }
  }

  isPageValid(): boolean {
    if (!this.costPrevisionTab || !this.costPrevisionTab.isValid()) {
      return false;
    }
    if (
      this.appraisalType === this.constants.appraisalTypes.EDILIZIA_AGEVOLATA
    ) {
      if (!this.endJobEvaluationTab || !this.endJobEvaluationTab.isValid()) {
        return false;
      }
    }
    if (this.shippingCostTable) {
      if (!this.shippingCostTable || !this.shippingCostTable.isValid()) {
        return false;
      }
    }
    if (
      this.templateValidationComponent &&
      !this.templateValidationComponent.form.valid
    ) {
      return false;
    }
    return true;
  }

  saveData() {
    this.savePageData().subscribe(res => {
      this._landingService.goNextPage(
        this.positionId,
        this.currentTask,
        this.wizardCode,
        this._activatedRoute
      );
    });
  }

  // Esegue salvataggio dati sal prima di avanzare step wizard
  // In base a mobileApprisalType ed appraisalType invoca il corretto servizio di salvataggio
  savePageData(): Observable<any> {
    if (
      this.mobileAppraisalType === this.constants.appraisalTypes.SHIPPING ||
      this.mobileAppraisalType === this.constants.appraisalTypes.AEREOMOBILE
    ) {
      // shipping
      return this._salService
        .saveSalShippingData(this.positionId, this.salComplessivo)
        .switchMap(() => this.saveTemplateValidation());
    }
    if (this.appraisalType === this.constants.appraisalTypes.BENI_INDUSTRIALI) {
      let postData;
      if (this.salComplessivo && this.salComplessivo['datiSalComplessivo']) {
        postData = this.salComplessivo['datiSalComplessivo'];
      } else {
        postData = this.salComplessivo;
      }
      return this._salService
        .saveSalIndustrialData(this.positionId, postData)
        .switchMap(() => this.saveTemplateValidation());
    } else {
      const toSave = this.getStaticSaveObject();
      return this._salService
        .saveSalData(this.positionId, toSave)
        .switchMap(() => this.saveTemplateValidation());
    }
  }

  saveDraft(): Observable<any> {
    let toSave = null;
    if (
      this.mobileAppraisalType === this.constants.appraisalTypes.SHIPPING ||
      this.mobileAppraisalType === this.constants.appraisalTypes.AEREOMOBILE ||
      this.appraisalType === this.constants.appraisalTypes.BENI_INDUSTRIALI
    ) {
      toSave = this.salComplessivo;
    } else {
      toSave = this.getStaticSaveObject();
    }
    return this._landingService
      .saveDraft(this.positionId, JSON.stringify(toSave), {}, 'SAL')
      .switchMap(() => this.saveTemplateValidation());
  }

  private getStaticSaveObject() {
    const toSave = {};
    if (this.endJobEvaluationTab) {
      toSave['tabStimaFineLavori'] = this.endJobEvaluationTab.getAllObjects();
    }
    toSave['presCompMetrica'] = this.presCompMetrica;
    toSave['presProgEsecutiva'] = this.presProgEsecutiva;
    toSave['datiSalComplessivo'] = this.costPrevisionTab.salComplessivo;
    // Ora mandiamo anche i totali, quindi non devono essere azzerati
    // this.costPrevisionTab.refreshCostiDiRealizzazione();
    toSave[
      'tabAppTotCostEstimate'
    ] = this.costPrevisionTab.costiDiRealizzazione;

    return toSave;
  }

  private saveTemplateValidation(): Observable<boolean> {
    if (this.templateValidationComponent) {
      return this._appraisalCompilationService.saveTemplateValidation(
        this.templateValidationComponent.appValidation
      );
    } else {
      return Observable.of(true);
    }
  }

  goToGenericTask() {
    this._landingService.goToGenericTask(
      this.positionId,
      this.bpmTaskId,
      this.bpmTaskCod
    );
  }

  goToPreviousTask() {
    this._landingService.goToPreviousTask(
      this.positionId,
      this.wizardCode,
      this._activatedRoute
    );
  }
}
