import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { NgForm } from '@angular/forms';
import { Observable } from 'rxjs/Observable';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { MerchantabilityStaticModel } from '../../model/evaluations.models';
import { DomainService } from '../../../../../shared/domain/domain.service';
import { Domain } from '../../../../../shared/domain/domain';

@Component({
  selector: 'app-merchantability',
  templateUrl: './merchantability.component.html',
  styleUrls: ['./merchantability.component.css']
})
export class MerchantabilityComponent implements OnInit {
  @Input() model: MerchantabilityStaticModel;
  @Input() investmentSummaryString: string;
  investiments: Domain[] = [];
  @ViewChild(NgForm) private form: NgForm;

  constructor(
    public _accordionAPFService: AccordionAPFService,
    private _domainService: DomainService
  ) { }

  ngOnInit() {
    return Observable.forkJoin(
      this._domainService.newGetDomain('UBZ_DOM_INVESTMENT_TYPE')
    ).subscribe(res => {
      this.investiments = res[0];
      this.calculateTotal();
    });
  }

  calculateTotal() {
    if (this.model && this.model['TOT']) {
      this.model['TOT'].amount = 0;
      if (this.model['ISP'].amount) {
        this.model['TOT'].amount += this.model['ISP'].amount;
      }
      if (this.model['MEA'].amount) {
        this.model['TOT'].amount += this.model['MEA'].amount;
      }
      if (this.model['OPE'].amount) {
        this.model['TOT'].amount += this.model['OPE'].amount;
      }
      if (this.model['OPM'].amount) {
        this.model['TOT'].amount += this.model['OPM'].amount;
      }
      if (this.model['STC'].amount) {
        this.model['TOT'].amount += this.model['STC'].amount;
      }
    }
  }

  public isValidSection(): boolean {
    return this.form && this.form.valid;
  }

  public getModel() {
    return this.model;
  }
}
