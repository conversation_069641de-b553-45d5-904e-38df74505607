<div class="row">
  <h3>{{'UBZ.SITE_CONTENT.10111010' | translate }}</h3>
  <div class="col-sm-12">
    <table class="uc-table">
      <thead>
        <tr>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1010001' | translate }}</th>
          <th scope="col" class="col-sm-2">{{'UBZ.SITE_CONTENT.1010011011' | translate }}</th>
          <th scope="col" class="col-sm-6">{{'UBZ.SITE_CONTENT.10010110' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1010011100' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1010011101' | translate }}</th>
          <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.1110101' | translate }}</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngIf="configurations">
          <ng-container *ngFor="let item of configurations">
            <tr>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1010001' | translate }}">{{ macroProcessTypes && macroProcessTypes[item.macroProcess] ? (macroProcessTypes[item.macroProcess].translationCod
                | translate) : '' }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1010011011' | translate }}">{{ item.debtValue ? (item.debtValue | currency:'EUR':true:'1.2-2') : '' }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.10010110' | translate }}">{{ item.ruleDesc ? (item.ruleDesc | replaceString:'{MIN}':item.minValue | replaceString:'{MAX}':item.maxValue)
                : '' }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1010011100' | translate }}">{{ checkTypes && checkTypes[item.checkType] ? (checkTypes[item.checkType].translationCod | translate) : ''
                }}</td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1010011101' | translate }}">
                <i class="icon-check" [ngClass]="{'icon-lightblue': item.activeFlag, 'light-grey': !item.activeFlag}"></i>
              </td>
              <td attr.data-label="{{'UBZ.SITE_CONTENT.1110101' | translate }}">
                <ng-container *appAuthKey="'UBZ_CONFIGURATION.SAMPLE_CHECKS_MODIFY'">
                  <button type="button" class="btn btn-empty waves-effect waves-secondary" (click)="openModal(item)">
                    <i class="fa fa-pencil-square-o"></i>
                  </button>
                </ng-container>
                <button type="button" class="btn btn-empty" (click)="selectRuleToExpand(item)">
                  <i class="fa" [ngClass]="{'fa-plus-square-o': !(ruleToExpand.ruleId === item.ruleId), 'fa-minus-square-o': (ruleToExpand.ruleId === item.ruleId)}"
                    aria-hidden="true"></i>
                </button>
              </td>
            </tr>
            <tr *ngIf="ruleToExpand && ruleToExpand.ruleId === item.ruleId">
              <td colspan="6" class="row">
                <div class="col-sm-12 text-left">
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.1010100000' | translate }}: </strong>
                    <i class="icon-check" [ngClass]="{'icon-lightblue': item.salFlag, 'light-grey': !item.salFlag}"></i>
                  </p>
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.1010011110' | translate }}: </strong>
                    <span>{{ item.minValue | number:'1.2-2' }}</span>
                  </p>
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.1010011111' | translate }}: </strong>
                    <span>{{ item.maxValue | number:'1.2-2' }}</span>
                  </p>
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.1010110000' | translate }}: </strong>
                    <i class="icon-check" [ngClass]="{'icon-lightblue': item.ruleOutcome, 'light-grey': !item.ruleOutcome}"></i>
                  </p>
                  <p>
                    <strong>{{'UBZ.SITE_CONTENT.1010101111' | translate }}: </strong>
                    {{ item.bornDate | date:'shortDate' }} - {{ item.deadDate | date:'shortDate' }}
                  </p>
                </div>
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </tbody>
    </table>
  </div>
</div>

<div *ngIf="isModalOpen" class="modal fade" bsModal #modalBox="bs-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
  (onHidden)="hide()" [config]="{show: 'true'}">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <form #popform="ngForm" (ngSubmit)="saveSelectedConfiguration()" novalidate>
        <div class="modal-header">
          <h2>{{'UBZ.SITE_CONTENT.1010100011' | translate }}</h2>
          <button type="reset" class="close pull-right" aria-label="Close" (click)="hide()">
            <i class="icon-close"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-12 form-group">
              <label>{{'UBZ.SITE_CONTENT.1010001' | translate }}*</label>
              <div class="custom-select">
                <select class="form-control" [(ngModel)]="selectedConfiguration.macroProcess" required name="macroProcess">
                  <option value="" hidden>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
                  <option *ngFor="let row of (macroProcessTypes | domainMapToDomainArray)" value="{{row.domCode}}">{{row.translationCod | translate}}</option>
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6 form-group">
              <label for="minValue">{{'UBZ.SITE_CONTENT.1010011110' | translate }}*</label>
              <input appOnlyNumbers type="text" name="minValue" class="form-control" required="" [(ngModel)]="selectedConfiguration.minValue">
            </div>
            <div class="col-md-6 form-group">
              <label for="maxValue">{{'UBZ.SITE_CONTENT.1010011111' | translate }}*</label>
              <input appOnlyNumbers type="text" name="maxValue" class="form-control" required="" [(ngModel)]="selectedConfiguration.maxValue">
            </div>
          </div>
          <div class="row">
            <div class="col-md-6 form-group">
              <div class="custom-checkbox">
                <input div="custom-checkbox" type="checkbox" id="salFlag" name="salFlag" [(ngModel)]="selectedConfiguration.salFlag">
                <label for="salFlag" data-toggle="tooltip" attr.title="{{'UBZ.SITE_CONTENT.1010100000' | translate }}">{{'UBZ.SITE_CONTENT.1010100000' | translate }}</label>
              </div>
            </div>
            <div class="col-md-6 form-group">
              <div class="custom-checkbox">
                <input div="custom-checkbox" type="checkbox" id="activeFlag" name="progressivo" [(ngModel)]="selectedConfiguration.activeFlag">
                <label for="activeFlag" data-toggle="tooltip" attr.title="{{'UBZ.SITE_CONTENT.1010011101' | translate }}">{{'UBZ.SITE_CONTENT.1010011101' | translate }}</label>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6 form-group">
              <label for="bornDate">{{'UBZ.SITE_CONTENT.1010100001' | translate }}*</label>
              <app-calendario [name]="'bornDate'" [(ngModel)]="selectedConfiguration.bornDate" [title]="'UBZ.SITE_CONTENT.1010100001' | translate"
                [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate" [required]="true">
              </app-calendario>
            </div>
            <div class="col-md-6 form-group">
              <label for="deadDate">{{'UBZ.SITE_CONTENT.1010100010' | translate }}*</label>
              <app-calendario [name]="'deadDate'" [(ngModel)]="selectedConfiguration.deadDate" [title]="'UBZ.SITE_CONTENT.1010100010' | translate"
                [placeholder]="'UBZ.SITE_CONTENT.1000010001' | translate" [required]="true">
              </app-calendario>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <ng-container *appAuthKey="'UBZ_CONFIGURATION.SAMPLE_CHECKS_CONFIRM'">
            <button class="btn btn-primary waves-effect" type="submit" [disabled]="popform.invalid">{{'UBZ.SITE_CONTENT.1100010' | translate }}</button>
          </ng-container>
        </div>
      </form>
    </div>
  </div>
</div>
