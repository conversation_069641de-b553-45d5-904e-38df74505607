import { Component, OnInit, EventEmitter, Output, Input } from '@angular/core';
import { ConfigurationService } from '../../service/configuration.service';
import {
  IndividualAssignmentConfig,
  SocietyConf,
} from '../../configuration.models';
import { FormArray, FormBuilder, FormGroup } from '@angular/forms';
import { MessageService } from '../../../shared/messages/services/message.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-individual-assignment',
  templateUrl: './individual-assignment.component.html',
  styleUrls: ['./individual-assignment.component.css'],
})
export class IndividualAssignmentComponent implements OnInit {
  @Input() activeList: SocietyConf[] = [];
  @Input() inactiveList: SocietyConf[] = [];
  @Input() configurationInfo: IndividualAssignmentConfig;
  @Output() refreshPage = new EventEmitter();
  @Output() submitForm = new EventEmitter();

  aggiungiModalIsOpen: boolean = false;
  inputModalObject: any;
  prioritiesList: any[];
  societiesForm: FormGroup;
  societiesArrayControls: FormArray;

  constructor(
    private configurationService: ConfigurationService,
    private messageService: MessageService,
    private translateService: TranslateService,
    private formBuilder: FormBuilder
  ) {}

  ngOnInit() {
    this.initializeForm();
    this.societiesForm.valueChanges.subscribe((changes) => {
      if (this.societiesForm.dirty) {
        this.configurationService.setUndoButtonState(true);
        this.configurationService.setSaveButtonState(true);
      }
    });
    if (this.configurationService.subscription == undefined) {
      this.configurationService.subscription =
        this.configurationService.invokeSocietiesFormSubmit.subscribe(() => {
          this.save();
        });
    }
  }

  initializeForm() {
    this.societiesForm = this.formBuilder.group({
      societies: this.formBuilder.array([]),
    });
    this.activeList.forEach((activeSoc) => {
      this.addNewSociety(activeSoc);
    });
    this.societiesArrayControls = this.societiesForm.controls['societies'] as FormArray;
    this.prioritiesList = this.getPriorities(this.activeList);
  }

  addNewSociety(activeSoc: SocietyConf) {
    (this.societiesForm.controls['societies'] as FormArray).push(
      this.newSociety(activeSoc)
    );
  }

  newSociety(activeSoc: SocietyConf): FormGroup {
    let formGroup = this.formBuilder.group({
      anagId: activeSoc.anagId,
      priority: activeSoc.priority,
      distributionPercentage: activeSoc.distributionPercentage,
      heading: activeSoc.heading,
      activationEndDate: activeSoc.activationEndDate,
      activationStartDate: activeSoc.activationStartDate,
      email: activeSoc.email,
      iban: activeSoc.iban,
      mobileNum: activeSoc.mobileNum,
      ndg: activeSoc.ndg,
      phoneNum: activeSoc.phoneNum,
      vatNum: activeSoc.vatNum,
    });
    // priority comes 0 after adding a new society, so we set default to 1
    if (activeSoc.priority == 0) {
      formGroup.controls['priority'].setValue(1, { onlySelf: true });
    }
    return formGroup;
  }

  openAggiungiModalIfCase() {
    if (this.inactiveList !== undefined && this.activeList !== undefined) {
      if (this.inactiveList.length > 0 && this.activeList.length < 10) {
        this.inputModalObject = this.inactiveList;
        this.aggiungiModalIsOpen = true;
      }
      if (this.inactiveList.length === 0) {
        this.aggiungiModalIsOpen = false;
        this.messageService.showError(
          this.translateService.instant('UBZ.SITE_CONTENT.11111010011'),
          this.translateService.instant('UBZ.SITE_CONTENT.10011010100')
        );
      }
      if (this.activeList.length === 10) {
        this.aggiungiModalIsOpen = false;
        this.messageService.showError(
          this.translateService.instant('UBZ.SITE_CONTENT.11111010100'),
          this.translateService.instant('UBZ.SITE_CONTENT.10011010100')
        );
      }
    }
  }

  closeAggiungiModal(refreshPageAddOrDelete) {
    this.inputModalObject = null;
    this.aggiungiModalIsOpen = false;
    if (refreshPageAddOrDelete === true) {
      this.refreshPage.emit(refreshPageAddOrDelete);
    }
  }

  getPriorities(activeList: SocietyConf[]) {
    let prioritiesList = [];
    for (let i = 1; i <= activeList.length; i++) {
      prioritiesList.push(i);
    }
    return prioritiesList;
  }

  save() {
    let societiesToSave = [];
    this.societiesArrayControls.controls.forEach((singleSoc) => {
      societiesToSave.push(singleSoc.value);
    });

    this.activeList = societiesToSave;
    this.submitForm.emit(societiesToSave);
  }

  refreshIfDeleted(deleted) {
    if (deleted === true) {
      this.refreshPage.emit(deleted);
    }
  }
}
