<fieldset [disabled]="!_landingService.positionLocked">
<fieldset [disabled]="_landingService.isLockedTask[currentTask] || haveDisabledFields">
<accordion>
  <app-total-costs [model]="response" (formStateChanged)="saveIsEnable()"></app-total-costs>
  <app-financial-plan [model]="response" (formStateChanged)="saveIsEnable()"></app-financial-plan>
  <!-- // FIXME - TOGLIERE SE LA CENTRALIZZAZIONE IN POSITION SERVICE FUNZIONA CORRETTAMENTE         -->
  <!-- <app-appraisal-template-validation *ngIf="isInternalSecondOpinion" (change)="saveIsEnable()" [positionId]="positionId" templateName="QTE" [haveDisabledFields]="haveDisabledFields"></app-appraisal-template-validation> -->
  <app-appraisal-template-validation *ngIf="_positionService.isInternalSecondOpinion" (change)="saveIsEnable()" [positionId]="positionId" templateName="QTE" [haveDisabledFields]="haveDisabledFields"></app-appraisal-template-validation>
</accordion>
</fieldset>

<app-navigation-footer [footerClass]="menuService.footerProperty" [showCancelButton]="false" (closeDraftButtonClick)="goToGenericTask()"
  (saveButtonClick)="saveData()" showSaveDraft="true" showPrevious="true" [saveDraftCallback]="saveDraftCallback" [saveIsEnable]="_saveIsEnable"
  (previousButtonClick)="goToPreviousTask()" [activeTaskCode]="currentTask" confirmButtonString="{{'UBZ.SITE_CONTENT.110001110' | translate }}"
></app-navigation-footer>
</fieldset>
