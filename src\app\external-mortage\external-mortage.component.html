<section id="page-content-wrapper">

  <div class="row">
    <div class="col-sm-12 section-headline">
      <h1><i class="icon-richiesta_perizia"></i> {{'UBZ.SITE_CONTENT.10011001011' | translate }} </h1>
      <h2>{{'UBZ.SITE_CONTENT.10011001100' | translate }}</h2>
    </div>
  </div>

  <div class="row step-navigation">
    <div class="col-sm-12 btn-set">
      <ng-container *ngIf="!filtersOpen">
        <button *appAuthKey="'UBZ_EXTERNAL_MORTAGE_ACCESS'" type="button" class="btn btn-empty waves-effect waves-secondary pull-right"
          (click)="openFilters()">
          <i class="icon-filter"></i> {{'UBZ.SITE_CONTENT.1100100011' | translate }}
        </button>
      </ng-container>
      <ng-container *ngIf="filtersOpen">
        <button *appAuthKey="'UBZ_EXTERNAL_MORTAGE_ACCESS'" type="button" class="btn btn-empty waves-effect waves-secondary pull-right"
          (click)="closeFilters()">
          <i class="fa fa-times" aria-hidden="true"></i> {{'UBZ.SITE_CONTENT.1100100101' | translate }}
        </button>
      </ng-container>
    </div>
  </div>

  <section class="row" *ngIf="filtersOpen">
    <!-- Filtri -->
    <div class="col-md-2 col-sm-12 form-group">
      <!-- NDG -->
      <label>{{'UBZ.SITE_CONTENT.1001110' | translate }}</label>
      <input type="text" name="ndg" class="form-control" [(ngModel)]="searchFilter.ndg">
    </div>
    <div class="col-md-2 col-sm-12 form-group">
      <!-- ID Richiesta -->
      <label>{{'UBZ.SITE_CONTENT.10100101' | translate }}</label>
      <input type="text" name="requestId" class="form-control" [(ngModel)]="searchFilter.requestId">
    </div>
    <div class="col-md-2 col-sm-12 form-group">
      <!-- Stato -->
      <label>{{'UBZ.SITE_CONTENT.10000001' | translate }}</label>
      <div class="custom-select">
        <select class="form-control" [(ngModel)]="searchFilter.isDraft" name="searchFilter.isDraft">
          <option [ngValue]="null" hidden>{{'UBZ.SITE_CONTENT.1000' | translate }}</option>
          <option [ngValue]="false">{{'UBZ.SITE_CONTENT.10011010001' | translate }}</option>
          <option [ngValue]="true">{{'UBZ.SITE_CONTENT.10011010010' | translate }}</option>
        </select>
      </div>
    </div>
    <div class="col-md-4 col-md-offset-2 col-sm-12 form-group">
      <label></label>
      <div class="btn-set">
        <button *appAuthKey="'UBZ_EXTERNAL_MORTAGE_ACCESS'" class="btn btn-primary waves-effect pull-right" type="button" (click)="startFilter()">{{'UBZ.SITE_CONTENT.1100100110' | translate }}</button>
        <ng-container *appAuthKey="'UBZ_EXTERNAL_MORTAGE_ACCESS'">
          <button *ngIf="(searchFilter.ndg || searchFilter.requestId || searchFilter.isDraft || searchFilter.isDraft === false)" class="btn btn-secondary waves-effect pull-right"
            type="button" (click)="cleanSearchFilter()">{{'UBZ.SITE_CONTENT.1010010101' | translate }}</button>
        </ng-container>
      </div>
    </div>
  </section>

  <section class="row" *appAuthKey="'UBZ_EXTERNAL_MORTAGE_ACCESS'">
    <!-- Tabella -->
    <div class="col-sm-12">
      <table class="table table-hover" *ngIf="reqAppraisalsNumber > 0; else noRecordFound">
        <thead>
          <tr>
            <!-- ID Richiesta -->
            <th scope="col">{{'UBZ.SITE_CONTENT.10100101' | translate }}</th>
            <!-- NDG -->
            <th scope="col">{{'UBZ.SITE_CONTENT.1001110' | translate }}</th>
            <!-- Totale stima esposizione Antergate -->
            <th scope="col">{{'UBZ.SITE_CONTENT.10011101011' | translate }}</th>
            <!-- Data creazione richiesta -->
            <th scope="col">{{'UBZ.SITE_CONTENT.10011010000' | translate }}</th>
            <!-- Stato -->
            <th scope="col">{{'UBZ.SITE_CONTENT.10000001' | translate }}</th>
            <th scope="col"></th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let reqAppraisal of reqAppraisals">
            <td attr.data-label="{{'UBZ.SITE_CONTENT.10100101' | translate}}">{{reqAppraisal.requestId}}</td>
            <td attr.data-label="{{'UBZ.SITE_CONTENT.1001110' | translate}}">{{reqAppraisal.ndg}}</td>
            <td attr.data-label="{{'UBZ.SITE_CONTENT.10011101011' | translate}}">{{ reqAppraisal.antergateTotalValue | currency:'EUR':true:'1.2-2' }}</td>
            <td attr.data-label="{{'UBZ.SITE_CONTENT.10011010000' | translate}}">{{ reqAppraisal.creationDate | date: 'dd/MM/y'}}</td>
            <td attr.data-label="{{'UBZ.SITE_CONTENT.10000001' | translate}}"><span class="state" [ngClass]="{'green' : (reqAppraisal.isDraft === false), 'red' : (reqAppraisal.isDraft === true)}"></span></td>
            <td attr.data-label>
              <a *appAuthKey="'UBZ_EXTERNAL_MORTAGE_ACCESS'" role="button" (click)="goToReqAppraisalsDetail(reqAppraisal.requestId)">
                <i class="icon-angle-double-right"></i>
              </a>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <ng-template #noRecordFound>
      <div class="Search__NoResults">
        <div class="Search__NoResults__Icon">
          <i class="icon-placeholder_note"></i>
        </div>
        <div class="Search__NoResults__Text">
          <h3 class="Search__NoResults__Title">{{'UBZ.SITE_CONTENT.1001011001' | translate }}</h3>
          <p class="Search__NoResults__Subtitle">{{'UBZ.SITE_CONTENT.1001011010' | translate }}</p>
        </div>
      </div>
    </ng-template>
    <ng-container *ngIf="reqAppraisalsNumber > 10">
      <div class="col-sm-6">
        <div class="results">
          <span>{{'UBZ.SITE_CONTENT.1111111' | translate }}</span>
          <div class="custom-select">
            <select class="form-control" [(ngModel)]="pageSize" (ngModelChange)="pageSizeChanged()">
              <option [ngValue]="10" selected>10 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{reqAppraisalsNumber}}</option>
              <option [ngValue]="20" *ngIf="reqAppraisalsNumber > 20">20 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{reqAppraisalsNumber}}</option>
              <option [ngValue]="30" *ngIf="reqAppraisalsNumber > 30">30 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{reqAppraisalsNumber}}</option>
              <option [ngValue]="40" *ngIf="reqAppraisalsNumber > 40">40 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{reqAppraisalsNumber}}</option>
              <option [ngValue]="50" *ngIf="reqAppraisalsNumber > 50">50 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{reqAppraisalsNumber}}</option>
              <option value="{{reqAppraisalsNumber}}">{{reqAppraisalsNumber}} {{'UBZ.SITE_CONTENT.10000000' | translate }} {{reqAppraisalsNumber}}</option>
            </select>
          </div>
        </div>
      </div>
      <div class="col-sm-6" class="pull-right">
        <pagination #paginator [boundaryLinks]="true" [directionLinks]="false" [totalItems]="reqAppraisalsNumber" [itemsPerPage]="pageSize" [maxSize]="10"
          (pageChanged)="changePage($event)" class="pagination" previousText="&lsaquo;" nextText="&rsaquo;" firstText="&laquo;"
          lastText="&raquo;"></pagination>
      </div>
    </ng-container>
  </section>

</section>
