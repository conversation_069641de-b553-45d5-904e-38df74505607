import { Component, OnInit, Input } from '@angular/core';
import { OverallJobRowModel } from '../model/sal.models';
import { AccordionAPFService } from '../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';

@Component({
  selector: 'app-overall-job-statistics',
  templateUrl: './overall-job-statistics.component.html',
  styleUrls: ['./overall-job-statistics.component.css']
})
export class OverallJobStatisticsComponent implements OnInit {
  @Input() tableRows: OverallJobRowModel[] = [];

  constructor(public _accordionAPFService: AccordionAPFService) {}

  ngOnInit() {}

  getDistributionPerc(index: number): number {
    if (this.tableRows[index]) {
      return this.tableRows[index].distributionPerc;
    }
    return 0;
  }
  getDistributionAmount(index: number): number {
    if (this.tableRows[index]) {
      return this.tableRows[index].distributionAmount;
    }
    return 0;
  }
  getUpdatePerc(index: number): number {
    if (this.tableRows[index]) {
      return this.tableRows[index].updatePerc;
    }
    return 0;
  }

  getTotalPercentage(): number {
    let sum = 0;
    for (const row of this.tableRows) {
      sum += row.distributionPerc;
    }
    return sum;
  }
  getTotalValue(): number {
    let sum = 0;
    for (const row of this.tableRows) {
      sum += row.distributionAmount;
    }
    return sum;
  }
}
