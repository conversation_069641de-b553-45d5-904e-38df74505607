import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';

import { LandingService } from '../../../simulation/services/landing/landing.service';
import { PositionService } from '../../../shared/position/position.service';

@Component({
  selector: 'app-appraisal-fill-confirm',
  templateUrl: './appraisal-fill-confirm.component.html',
  styleUrls: ['./appraisal-fill-confirm.component.css']
})
export class AppraisalFillConfirmComponent implements OnInit {
  positionId: string;
  wizardCode: string;
  bpmTaskId: string;
  bpmTaskCod: string;
  currentTask: string;
  isApiAppraisal: boolean = false;
  validatedApiAppraisal: boolean = false;
  private appraisalInfo: any;

  constructor(
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    public _landingService: LandingService,
    private _positionService: PositionService
  ) {}

  ngOnInit() {
    this._activatedRoute.params
      .switchMap((params: Params) => {
        this.positionId = params['positionId'];
        this.wizardCode = params['wizardCode'];
        this.bpmTaskId = params['taskId'];
        this.bpmTaskCod = params['taskCod'];
        this.currentTask = 'UBZ-PER-CON';
        return this._positionService.getAppraisalInfo(this.positionId, false);
      })
      .subscribe(res => {
        this.appraisalInfo = res;
        if (
          this.appraisalInfo.appraisal.lastFromApiFlag &&
          this.appraisalInfo.appraisal.lastFromApiFlag === 'Y'
        ) {
          this.isApiAppraisal = true;
          if (this.appraisalInfo.appraisal.statusCod === 'PER-COV') {
            this.validatedApiAppraisal = true;
          }
        }
      });
  }

  confirmPress() {
    this._positionService
      .consistencyValidateAppraisalEvaluation(this.positionId)
      .subscribe(() => {
        this._landingService
          .getNextTask(this.positionId, this.currentTask, this.wizardCode)
          .subscribe(() => {
            this._router.navigate([
              `generic-task/${this.positionId}/${this.bpmTaskId}/${
                this.bpmTaskCod
              }`
            ]);
          });
      });
  }
}
