import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { BehaviorSubject } from 'rxjs/BehaviorSubject';

@Injectable()
export class SharedService {
  _checkboxChanged$: BehaviorSubject<boolean> = new BehaviorSubject(false);

  stableHash(obj: object): string {
    let currentValue = '';
    if (obj === null || obj === undefined) {
      return '';
    }
    const sortable: any = Object.keys(obj).map((k) => [k, obj[k]]).sort(([a], [b]) => a.localeCompare(b));

    for (const [k, v] of sortable) {
      if (typeof v === 'object') {
        currentValue += `_.${k}=${this.stableHash(v)}`;
      } else {
        currentValue += `_${k}=${v}`;
      }
    }

    return currentValue;
  }

  getCheckboxChangeSubj(): Observable<boolean> {
    return this._checkboxChanged$.asObservable();
  }

  setCheckboxChangeSubj(value: boolean): void {
    this._checkboxChanged$.next(value);
  }
}
