<div class="row">
  <div class="col-sm-12">
    <div class="success-confirmation">
      <h3>
        <i class="icon-check"></i> {{'UBZ.SITE_CONTENT.10000000110' | translate }}</h3>
      <p>{{'UBZ.SITE_CONTENT.10000000111' | translate }} {{'UBZ.SITE_CONTENT.10000001000' | translate }}
        {{guaranteeTransferService.guaranteeFrom}}
        {{'UBZ.SITE_CONTENT.10000000001' | translate }} {{guaranteeTransferService.guaranteeTo}}.
      </p>
    </div>
  </div>
  <div class="col-sm-12">
    <h3>{{ 'UBZ.SITE_CONTENT.10010111001' | translate }}</h3>
  </div>
  <div class="col-sm-12">
    <accordion class="panel-group" id="accordion" *ngFor="let appraisal of guaranteeTransferService.appraisalList">
      <accordion-group #group class="panel">
        <div accordion-heading class="acc-note-headline" role="tab">
          <h4 class="panel-title">
            <a role="button">
              <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
              <span>{{ 'UBZ.SITE_CONTENT.10010001' | translate }}: {{appraisal.appraisalId}}</span>
            </a>
          </h4>
        </div>
        <div class="panel-collapse collapse in" role="tabpanel">
          <div class="panel-body">
            <div class="row">
              <div class="col-sm-12">
                <table class="uc-table">
                  <thead>
                    <tr>
                      <th scope="col" class="col-sm-2" style="text-align : center">
                        {{ 'UBZ.SITE_CONTENT.10010100' | translate }}</th>
                      <th scope="col" class="col-sm-2" style="text-align : center">
                        {{ 'UBZ.SITE_CONTENT.10001110011' | translate }}</th>
                      <th scope="col" class="col-sm-2" style="text-align : center">
                        {{ 'UBZ.SITE_CONTENT.100100' | translate }}</th>
                      <th scope="col" class="col-sm-2" style="text-align : center">
                        {{ 'UBZ.SITE_CONTENT.111100' | translate }}</th>
                      <th scope="col" class="col-sm-2" style="text-align : center">
                        {{ 'UBZ.SITE_CONTENT.10010111010' | translate }}</th>
                      <th scope="col" class="col-sm-2" style="text-align : center">
                        {{ 'UBZ.SITE_CONTENT.101000' | translate }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    <ng-container *ngFor="let guarantee of appraisal.collaterals">
                        <tr *ngFor="let asset of guarantee.assets">
                          <td style="text-align : center">
                            <span
                              *ngIf="guarantee.jointCod === guaranteeTransferService.guaranteeFrom">{{guaranteeTransferService.guaranteeTo}}</span>
                            <span
                              *ngIf="guarantee.jointCod !== guaranteeTransferService.guaranteeFrom">{{guarantee.jointCod}}</span>
                          </td>
                          <td style="text-align : center">
                            <span *ngIf="guarantee.jointCod === guaranteeTransferService.guaranteeFrom">
                              {{guaranteeTransferService.collatTecTo}}</span>
                            <span *ngIf="guarantee.jointCod !== guaranteeTransferService.guaranteeFrom">
                              {{guarantee.collatTecForm}}</span>
                          </td>
                          <td style="text-align : center">{{asset.assetId}}</td>
                          <td style="text-align : center">{{asset.assetDescription}}</td>
                          <td style="text-align : center">{{asset.assetType}}</td>
                          <td style="text-align : center">{{asset.province}}</td>
                        </tr>
                    </ng-container>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </accordion-group>
    </accordion>
  </div>
</div>