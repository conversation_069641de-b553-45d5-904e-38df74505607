import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  ViewChildren,
  QueryList,
  AfterContentInit
} from '@angular/core';
import { NgForm } from '@angular/forms';
import { AccordionAPFService } from '../../../../../shared/application-form/services/accordion-apf-service/accordion-apf.service';
import { FinancialItem, QteResponse } from '../../model/qte.models';

@Component({
  selector: 'app-financial-plan',
  templateUrl: './financial-plan.component.html',
  styleUrls: ['./financial-plan.component.css']
})
export class FinancialPlanComponent implements OnInit, AfterContentInit {
  @Input() model: QteResponse;
  @Output() formStateChanged: EventEmitter<void> = new EventEmitter<void>();
  @ViewChildren(NgForm) private forms = new QueryList<NgForm>();
  today = new Date();
  modalOpen = false;
  modalTypes = {
    expense: 'EXP',
    coverage: 'COV'
  };
  modalType: string;
  createdItem: FinancialItem = new FinancialItem();

  constructor(public _accordionAPFService: AccordionAPFService) {}

  ngOnInit() {}
  ngAfterContentInit() {
    setTimeout(() => {
      this.forms.toArray()[0].form.statusChanges.subscribe(() => {
        this.formStateChanged.emit();
      });
    }, 0);
  }

  public calculateTotalCost(): number {
    let total = 0;
    for (const item of this.model.expenses) {
      total += item.amount;
    }
    return total;
  }

  public calculateTotalCoverage(): number {
    let total = 0;
    for (const item of this.model.coverages) {
      total += item.amount;
    }
    return total;
  }

  hideModal() {
    this.createdItem.description = '';
    this.createdItem.amount = 0;
    this.modalOpen = false;
  }

  openModal(modalType: string) {
    this.modalType = modalType;
    this.modalOpen = true;
  }

  itemCreated() {
    const item: FinancialItem = JSON.parse(JSON.stringify(this.createdItem));
    if (this.modalType === this.modalTypes['expense']) {
      this.model.expenses.push(item);
    } else {
      this.model.coverages.push(item);
    }
    this.hideModal();
  }

  public isSectionValid(): boolean {
    if (this.forms.toArray() && this.forms.toArray()[0]) {
      return this.forms.toArray()[0].valid;
    }
  }
}
