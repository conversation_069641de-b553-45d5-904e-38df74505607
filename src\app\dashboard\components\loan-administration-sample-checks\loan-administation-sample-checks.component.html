<div class="row">
  <div class="col-sm-12 section-headline">
    <h1><i class="icon-dashboard"></i> {{'UBZ.SITE_CONTENT.10111010' | translate }}</h1>
    <h2>{{'UBZ.SITE_CONTENT.1111111001' | translate }}</h2>
  </div>
</div>
<br/>
<table class="table table-hover">
  <thead>
    <tr>
      <th scope="col">{{'UBZ.SITE_CONTENT.10010001' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.1111111010' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.1111111011' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.101110101' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.1111111100' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.10111' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.1111111101' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.1110100000' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.101000' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.1101110110' | translate }}</th>
      <th scope="col">{{'UBZ.SITE_CONTENT.1011001' | translate }}</th>
      <th scope="col"></th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let position of positionListResults?.positions">
      <td data-label="appraisalId">{{position.appraisalId}}</td>
      <td data-label="society">{{position.society}}</td>
      <td data-label="wellKnowExpert">{{position.wellKnowExpert}}</td>
      <td data-label="expert">{{position.expert}}</td>
      <td data-label="auditor">{{position.auditor}}</td>
      <td data-label="appDate">{{position.appDate | date: 'dd-MM-yyyy'}}</td>
      <td data-label="customerName">{{position.customerName}}</td>
      <td data-label="city">{{position.city}}</td>
      <td data-label="province">{{position.province}}</td>
      <td data-label="region">{{position.region}}</td>
      <td data-label="operationScope">{{position.operationScope | translate}}</td>
      <td data-label><a role="button" (click)="goToEvaluationCompilation( position.appraisalId )"><i class="icon-angle-double-right"></i></a></td>
    </tr>
  </tbody>
</table>

<div class="row">
  <div *ngIf="positionListResults?.count > 10" class="col-sm-6">
    <div class="results">
      <span>{{'UBZ.SITE_CONTENT.1111111' | translate }}</span>
      <div class="custom-select">
        <select class="form-control" [(ngModel)]="pageSize" (change)="changePageSize()">
          <option value="10" selected>10 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{positionListResults.count}}</option>
          <option *ngIf="positionListResults?.count > 20" value="20">20 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{positionListResults.count}}</option>
          <option *ngIf="positionListResults?.count > 30" value="30">30 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{positionListResults.count}}</option>
          <option *ngIf="positionListResults?.count > 40" value="40">40 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{positionListResults.count}}</option>
          <option *ngIf="positionListResults?.count > 50" value="50">50 {{'UBZ.SITE_CONTENT.10000000' | translate }} {{positionListResults.count}}</option>
          <option *ngIf="positionListResults?.count <= 50" [ngValue]="positionListResults.count">{{positionListResults.count}} {{'UBZ.SITE_CONTENT.10000000' | translate }} {{positionListResults.count}}</option>
        </select>
      </div>
    </div>
  </div>
  <div class="col-sm-6" class="pull-right">
    <pagination [boundaryLinks]="true" [directionLinks]="false" [totalItems]="positionListResults?.count" [(ngModel)]="page" [itemsPerPage]="pageSize"
      (pageChanged)="changePage($event)" class="pagination" previousText="&lsaquo;" nextText="&rsaquo;" firstText="&laquo;" lastText="&raquo;"></pagination>
  </div>
</div>
