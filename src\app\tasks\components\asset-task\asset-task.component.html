<div class="col-sm-12 text-right">
  <app-task-access-rights [positionId]="positionId" [taskId]="taskId" [taskCod]="taskCod" [excludeList]="['UBZ_PRZ_OK']" (taskLocked)="taskLocked()" (taskUnlocked)="taskUnlocked()" (taskLockedByOtherUser)="tasklockedByOtherUser($event)"></app-task-access-rights>
</div>
<div class="col-sm-12">
  <fieldset style="margin-top: 0.5%;">
    <accordion class="panel-group" id="accordion">
      <fieldset class="validation-sect">
        <app-asset-no-collateral (saveEvent)="saveChanges($event)" [semaforo]="semaforo[0]" [collateralList]="collateralList" [assetsWithNoCollateral]="assetsWithNoCollateral"></app-asset-no-collateral>
      </fieldset>
      <fieldset class="validation-sect">
        <app-asset-with-collateral [semaforo]="semaforo[1]" [title]="'UBZ.SITE_CONTENT.11101101010' | translate" [assetList] ="assetsWithInactiveCollateral"></app-asset-with-collateral>
      </fieldset>
      <fieldset class="validation-sect">
        <app-asset-with-collateral [semaforo]="semaforo[2]" [title]="'UBZ.SITE_CONTENT.11101101001'| translate" [assetList]="assetsWithCollateral"></app-asset-with-collateral>
      </fieldset>
    </accordion>
  </fieldset>
</div>
<div class="col-md-12 btn-set">
  <button type="button" class="btn btn-primary waves-effect waves-secondary pull-right" (click)="valida()"
    [disabled]="!valideIsEnable" style="margin-top:2%">{{'UBZ.SITE_CONTENT.101111011' | translate }}</button>
</div>