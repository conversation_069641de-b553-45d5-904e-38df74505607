import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-collateral-modal',
  templateUrl: './collateral-modal.component.html',
  styleUrls: ['./collateral-modal.component.css']
})
export class CollateralModalComponent implements OnInit {
  @Input() isOpen: boolean;
  @Input() collaterals: any[];
  @Input() searchType: string;
  @Output() modalClose = new EventEmitter();

  constructor() { }

  ngOnInit() { }

  closeModal() {
    this.modalClose.emit();
    this.isOpen = false;
  }
}
