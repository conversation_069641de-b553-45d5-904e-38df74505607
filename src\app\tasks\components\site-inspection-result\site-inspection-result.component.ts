import { Component, OnInit, Input, Inject } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { GenericTaskService } from '../../services/generic-task/generic-task.service';
import { AccessRightsService } from '../../../shared/access-rights/services/access-rights.service';
import { LockUnlockTask } from '../../../shared/access-rights/model/lock-unlock-task';
import { MessageService } from '../../../shared/messages/services/message.service';
import { PositionService } from '../../../shared/position/position.service';
import { IAppConstants, APP_CONSTANTS } from '../../../app.constants';
import { UserDataService } from '../../../shared/user-data/user-data.service';
import { UserData } from '../../../shared/user-data/user-data';

@Component({
  selector: 'app-site-inspection-result',
  templateUrl: './site-inspection-result.component.html',
  styleUrls: ['./site-inspection-result.component.css']
})

export class SiteInspectionResultComponent implements OnInit {
  date: any;
  hour: string;
  minute: string;
  isPositiveResult: boolean;
  note: string;
  @Input() positionId: string;
  taskCod: string;
  taskId: string;
  taskOutcomes = { true: 'UBZ_PRZ_OK', false: 'UBZ_MODIFY_APPOINTMENT' };
  isTaskLocked: boolean;
  today: Date = new Date();
  minDateSelectable: Date = new Date();
  public readonly hours: string[] = [
    '08',
    '09',
    '10',
    '11',
    '12',
    '13',
    '14',
    '15',
    '16',
    '17',
    '18',
    '19',
    '20'
  ];
  public readonly minutes: string[] = ['00', '15', '30', '45'];

  constructor(
    private genericTaskService: GenericTaskService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private accessRightsService: AccessRightsService,
    private userDataService: UserDataService,
    private messageService: MessageService,
    private translateService: TranslateService,
    private _positionService: PositionService,
    @Inject(APP_CONSTANTS) private constants: IAppConstants)
     { }

  ngOnInit() {
    this.hour = '';
    this.minute = '';
    this.activatedRoute.params
      .switchMap((params: Params) => {
        this.taskId = params['taskId'];
        this.taskCod = params['taskCod'];
        return this._positionService.getAppraisalAssignment(this.positionId);
      })
      .subscribe(res => {
        this.minDateSelectable = new Date(
          res.customerContactDate - this.constants.MILLISECONDS_IN_A_DAY
        );
      });
  }
  
  submitForm(){
    this.date.setHours(this.hour);
    this.date.setMinutes(this.minute);
    const input = {
      surveyDate: this.date,
      surveyOutcome: this.isPositiveResult,
      surveyNote: this.note
    };
    this.genericTaskService
      .saveSiteInspectionResult(this.positionId, input)
      .switchMap(() => {
        const lockUnlockObj = new LockUnlockTask(this.taskId, this.taskCod);
        lockUnlockObj.eventCode = this.taskOutcomes[`${this.isPositiveResult}`];
        return this.accessRightsService.closeTask(lockUnlockObj);
      })
      .subscribe(() => {
        this.messageService.showSuccess(
          this.translateService.instant('UBZ.SITE_CONTENT.1001100000'),
          this.translateService.instant('UBZ.SITE_CONTENT.1001100001')
        );
        this.router.navigate(['dashboard/LAA']);
      });
  }

  taskLocked() {
    this.isTaskLocked = true;
   this.userDataService.getAll().subscribe((res:UserData)=>{
    this.genericTaskService.taskLockingUser=res.username
   })
  }

  tasklockedByOtherUser(user: string) {
    this.genericTaskService.taskLockingUser = user;
    this.isTaskLocked = false;
  }
  
  taskUnlocked() {
    this.genericTaskService.taskLockingUser = undefined;
    this.isTaskLocked = false;
  }
}
