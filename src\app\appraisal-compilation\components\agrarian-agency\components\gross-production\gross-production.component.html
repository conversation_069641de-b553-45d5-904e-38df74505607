<form #f="ngForm" (click)="_accordionAPFService.handleOpenProperty(group, f)">
  <accordion-group #group class="panel">
    <div accordion-heading class="acc-note-headline" role="tab">
      <h4 class="panel-title">
        <a role="button">
          <i [ngClass]="{'icon-arrow_up': group?.isOpen, 'icon-arrow_down' : !group?.isOpen}"></i>
          {{'UBZ.SITE_CONTENT.111001000' | translate }}
          <span class="state" [ngClass]="{'red': f.invalid, 'green': f.valid}"></span>
        </a>
      </h4>
    </div>
    <div class="panel-collapse collapse in" role="tabpanel">
      <div class="panel-body">
        <table class="uc-table">
          <thead>
            <tr>
              <th scope="col" class="text-center" colspan="7">{{'UBZ.SITE_CONTENT.111001001' | translate }}</th>
            </tr>
            <tr>
              <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.111001010' | translate }}</th>
              <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.111001011' | translate }}</th>
              <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.111001100' | translate }}</th>
              <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.111001101' | translate }}</th>
              <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.111001110' | translate }}</th>
              <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.111001111' | translate }}</th>
              <th scope="col" class="col-sm-1">{{'UBZ.SITE_CONTENT.111010000' | translate }}</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let model of model['appFarmNoIncomePlv']; let index = index;">
              <td>
                <input required type="text" name="prodotto-{{index}}" [(ngModel)]="model.productType" class="form-control">
              </td>
              <td>{{model.measurement}}</td>
              <td>
                <app-importo [name]="'prodUnitaria-' + index" [required]="true" [(ngModel)]="model.unitProduction">
                </app-importo>
              </td>
              <td>
                <app-importo [name]="'superficie-' + index" [required]="true" [(ngModel)]="model.haSurface">
                </app-importo>
              </td>
              <td>
                <app-importo [name]="'produzione-' + index" [required]="true" [(ngModel)]="model.totProduction">
                </app-importo>
              </td>
              <td>
                <app-importo [name]="'prezzoUnitario-' + index" [required]="true" [(ngModel)]="model.unitPrice">
                </app-importo>
              </td>
              <td>
                <app-importo [name]="'prezzoTotale-' + index" [required]="true" [(ngModel)]="model.totPrice">
                </app-importo>
              </td>
            </tr>
            <tr *ngFor="let item of (model.appFarmIncomePlv | objectMapToObjectArray | slice:0:1); let index = index;">
              <td>{{ (expenseType && expenseType[item.incomeType]) ? (expenseType[item.incomeType].translationCod | translate)
                : '' }}</td>
              <td>
                <app-importo [name]="'measurement-mapped-' + index" [required]="true" [(ngModel)]="model.appFarmIncomePlv[item.incomeType].measurement">
                </app-importo>
              </td>
              <td>
                <app-importo [name]="'unitProduction-mapped-' + index" [required]="true" [(ngModel)]="model.appFarmIncomePlv[item.incomeType].unitProduction">
                </app-importo>
              </td>
              <td>
                <app-importo [name]="'haSurface-mapped-' + index" [required]="true" [(ngModel)]="model.appFarmIncomePlv[item.incomeType].haSurface">
                </app-importo>
              </td>
              <td>
                <app-importo [name]="'totProduction-mapped-' + index" [required]="true" [(ngModel)]="model.appFarmIncomePlv[item.incomeType].totProduction">
                </app-importo>
              </td>
              <td>
                <app-importo [name]="'unitPrice-mapped-' + index" [required]="true" [(ngModel)]="model.appFarmIncomePlv[item.incomeType].unitPrice">
                </app-importo>
              </td>
              <td>
                <app-importo [name]="'totPrice-mapped-' + index" [required]="true" [(ngModel)]="model.appFarmIncomePlv[item.incomeType].totPrice">
                </app-importo>
              </td>
            </tr>
            <tr>
              <td>
                <strong>{{'UBZ.SITE_CONTENT.1010111111' | translate }}:</strong>
              </td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
              <td></td>
            </tr>
            <tr *ngFor="let item of (model.appFarmIncomePlv | objectMapToObjectArray | slice:1); let index = index;">
              <td>{{ (expenseType && expenseType[item.incomeType]) ? (expenseType[item.incomeType].translationCod | translate)
                : '' }}</td>
              <td>
                <app-importo [name]="'measurement-mapped-' + (index + 1)" [required]="true" [(ngModel)]="model.appFarmIncomePlv[item.incomeType].measurement">
                </app-importo>
              </td>
              <td>
                <app-importo [name]="'unitProduction-mapped-' + (index + 1)" [required]="true" [(ngModel)]="model.appFarmIncomePlv[item.incomeType].unitProduction">
                </app-importo>
              </td>
              <td>
                <app-importo [name]="'haSurface-mapped-' + (index + 1)" [required]="true" [(ngModel)]="model.appFarmIncomePlv[item.incomeType].haSurface">
                </app-importo>
              </td>
              <td>
                <app-importo [name]="'totProduction-mapped-' + (index + 1)" [required]="true" [(ngModel)]="model.appFarmIncomePlv[item.incomeType].totProduction">
                </app-importo>
              </td>
              <td>
                <app-importo [name]="'unitPrice-mapped-' + (index + 1)" [required]="true" [(ngModel)]="model.appFarmIncomePlv[item.incomeType].unitPrice">
                </app-importo>
              </td>
              <td>
                <app-importo [name]="'totPrice-mapped-' + (index + 1)" [required]="true" [(ngModel)]="model.appFarmIncomePlv[item.incomeType].totPrice">
                </app-importo>
              </td>
            </tr>
            <tr>
              <td colspan="6">
                <strong>{{'UBZ.SITE_CONTENT.100000111' | translate }}</strong>
              </td>
              <td>
                <strong>{{ getTotal() | currency:'EUR':true:'1.2-2' }}</strong>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </accordion-group>
</form>
